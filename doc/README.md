# 新框架开发文档 #


**目录 (Table of Contents)**

[TOCM]

[TOC]

# 1. 整体描述
## 1.1 文档概述
本文档主要分为两部分，一部份基于springboot的应用开发框架使用说明；另一部份基于springcloud的微服务架构使用说明。框架搭建总体上遵循SpringBoot的设计思想，约定大于配置，同时结合公司业务与产品的做了模块化处理，最大程度的保证框架的高内聚低耦合。            
## 1.2 框架主要引用第三方包说明
Spring:5.1.5.RELEASE  
Springboot:2.1.3.RELEASE  
Springcloud: 2.1.3.RELEASE

# 2. 快速开始
## 2.1 配置环境
### 2.1.1 Git拉取样例（wantwant-op-template），使用idea导入
### 2.1.2 maven settings.xml配置  
/doc/maven/settings_want.xml复制到.m2下
### 2.1.3 idea配置maven settings.xml，  
应用名、包名约定、类名、方法名等，见附录1
# 3. 约定规则
## 3.1 一级模块命名
| 名称  |  内容   |  命名  |  code  |
| :------------ | :------------ |:---------------:|:---------------:|  
| 商品模块 |  产品、规格、商品  | -       |  - |
| 订单模块 |  订单、支付、结算、拆单  |-        | - |
| 库存模块 |  出库单、库存管理  |-        | -|
| 营销模块 |  x  |-        | - |    
| 内容模块 | x   |-        |  - |
| 会员模块 |  x  |-          |  - |
| 报表模块 |  x  |-        |  - |
| 系统模块 |  x  |-        |  - |
## 3.2 二级模块命名
二方包：一级模块-二级模块-api
具体实现：一级模块-二级模块-service
RestAPI：一级模块-二级模块-webapi
## 3.3 二级模块命名
规范：一级模块-二级模块，如订单管理，order-core
## 3.4 开发项目Api命名
规范：一级模块-二级模块-api，如订单管理Api，order-core-webapi  
## 3.5 微服务命名
规范：与项目名相同，常量系统定义
## 3.6 url定义
规范：对应业务名称 /（find**|insert|save|create|update|modify|delete|undo**|do**|enabled|disabled|count）
## 3.7 包名定义
规范：com.wantwant.一级模块.*
## 3.8 类名定义
### 3.8.1 Controller定义
规范：业务子模块+Controller
### 3.8.2 Service定义
规范：接口I+业务模块+Service
规范：业务子模块+ServiceImpl
### 3.8.3 Mapper定义
规范：表名（驼峰）+Mapper，首字母大写
### 3.8.4 Model定义
规范：表名（驼峰），首字母大写
### 3.8.5 Model属性字段定义
规范：表字段（驼峰）
### 3.8.6 Api的定义
规范：业务子模块+Api
## 3.11 注释  
规范：看阿里代码规范  
## 3.12 Code Style  
规范：code目录下code/README.md
## 3.13 二方包命名  
命名：一级模块名-【功能模块名】
pom: groupId: com.wantwant.一级模块名
    artifactId: 一级模块名-【功能模块名】
## 5.2 swagger的使用  
1. 使用注解，参见demo;  
2. 生成文档url: http://localhost:***/doc.html  
3. 与yapi对接url: http://localhost:***/v2/api-docs  
4. 接口排序，参照demo，同时需在接口文档界面设置：文档管理->个性化设置->勾选：启用SwaggerBootstrapUi提供的增强功能 ->保存，刷新页面
## 6.1 启动说明
1. 默认使用dev配置文件启动
2. 需要使用其它配置时修改application.yml相关配置（不建议）
3. 或者在启动项添加-Dspring.profiles.active=XXX



