<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
	<!--
		servers | This is a list of authentication profiles, keyed by the server-id used within the system. | Authentication profiles can be used whenever maven must make a connection to a remote server. |
	-->
	<localRepository>/Users/<USER>/.m2/repository</localRepository>
  <servers>
    <server>
      <username>want_service</username>
      <password>want_service</password>
      <id>central</id>
    </server>
    <server>
      <username>want_service</username>
      <password>want_service</password>
      <id>snapshots</id>
    </server>
  </servers>
  <profiles>
    <profile>
      <repositories>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>central</id>
          <name>wantwant-central</name>
          <url>http://monitor.hotkidceo.com/artifactory/wantwant-central</url>
        </repository>
        <repository>
          <snapshots />
          <id>snapshots</id>
          <name>wantwant-central</name>
          <url>http://monitor.hotkidceo.com/artifactory/wantwant-central</url>
        </repository>

    		<repository>
            <id>getui-nexus</id>
            <url>http://mvn.gt.igexin.com/nexus/content/repositories/releases/</url>
        </repository>

      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>central</id>
          <name>wantwant-central</name>
          <url>http://monitor.hotkidceo.com/artifactory/wantwant-central</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots />
          <id>snapshots</id>
          <name>wantwant-central</name>
          <url>http://monitor.hotkidceo.com/artifactory/wantwant-central</url>
        </pluginRepository>
      </pluginRepositories>
      <id>artifactory</id>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>artifactory</activeProfile>
  </activeProfiles>
</settings>
