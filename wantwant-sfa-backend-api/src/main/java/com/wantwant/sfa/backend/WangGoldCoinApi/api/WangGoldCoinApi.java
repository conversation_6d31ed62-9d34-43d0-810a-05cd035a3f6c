package com.wantwant.sfa.backend.WangGoldCoinApi.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.WangGoldCoinApi.request.WangGoldCoinDetailRequest;
import com.wantwant.sfa.backend.WangGoldCoinApi.request.WangGoldCoinHeaderRequest;
import com.wantwant.sfa.backend.WangGoldCoinApi.vo.WangGoldCoinDetaillVo;
import com.wantwant.sfa.backend.WangGoldCoinApi.vo.WangGoldCoinHeaderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@Api(value = "WangGoldCoinApi", tags = "旺金币相关接口")
public interface WangGoldCoinApi {

  @ApiOperation(value = "旺金币表头列表", notes = "造旺币表头列表", httpMethod = "POST")
  @PostMapping("/wangGoldCoin/header/list")
  Response<Page<WangGoldCoinHeaderVo>> getWangGoldCoinHeaderList(
      @RequestBody @Valid WangGoldCoinHeaderRequest request);

  @ApiOperation(value = "旺金币明细列表", notes = "造旺币明细列表", httpMethod = "POST")
  @PostMapping("/wangGoldCoin/detail/list")
  Response<WangGoldCoinDetaillVo> getWangGoldCoinDetailList(
      @RequestBody @Valid WangGoldCoinDetailRequest request);

  @ApiOperation(value = "旺金币表头删除", notes = "造旺币表头删除", httpMethod = "POST")
  @PostMapping("/wangGoldCoin/detail/delete")
  Response<Integer> getWangGoldCoinDetailDelete(
      @RequestBody @Valid WangGoldCoinHeaderRequest request);
}
