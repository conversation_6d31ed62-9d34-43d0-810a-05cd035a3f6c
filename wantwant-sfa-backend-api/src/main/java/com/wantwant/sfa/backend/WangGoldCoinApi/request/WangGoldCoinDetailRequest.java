package com.wantwant.sfa.backend.WangGoldCoinApi.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(value = "旺金币明细传参")
@Data
public class WangGoldCoinDetailRequest extends PageParam {

  @ApiModelProperty(value = "登录工号")
  private String employeeId;

  @NotNull(message = "旺金币申请ID不能为空")
  @ApiModelProperty(value = "旺金币申请ID")
  private Integer batchId;

  @ApiModelProperty(value = "是否审核 0否;1是")
  private Integer isAudit;

  @ApiModelProperty(value = "是否分页 0否;1是")
  private Integer isPaging;

  @ApiModelProperty(value = "页面类型(1.企划2.财务3.主管)")
  private Integer pageType;

  @ApiModelProperty(value = "操作类型(1.业务 2.组织)")
  private Integer operationType;
}
