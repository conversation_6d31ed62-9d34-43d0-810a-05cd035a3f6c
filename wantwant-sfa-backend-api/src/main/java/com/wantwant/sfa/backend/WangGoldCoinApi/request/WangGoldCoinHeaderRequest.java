package com.wantwant.sfa.backend.WangGoldCoinApi.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "旺金币主表传参")
@Data
public class WangGoldCoinHeaderRequest extends PageParam {

    @ApiModelProperty(value = "登录工号")
    private String employeeId;

    @ApiModelProperty(value = "月份开始时间")
    private String monthStart;

    @ApiModelProperty(value = "月份结束时间")
    private String monthEnd;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "费用类型")
    private String applyType;

    @ApiModelProperty(value = "处理结果:1.待提交 2.财务审核中 3.财务驳回 4.主管审核中 5.主管驳回 6.已生效")
    private Integer processResult;

    @ApiModelProperty(value = "费用部门")
    private String deptName;

    @ApiModelProperty(value = "旺金币申请ID")
    private Integer batchId;

    @ApiModelProperty(value = "登录人类型(不用传)")
    private Integer employeeIdType;

    @ApiModelProperty(value = "页面类型(1.企划2.财务3.主管)")
    private Integer pageType;

    @ApiModelProperty(value = "导入类型(1.业务 2.组织)")
    private Integer importType;


    private Integer businessGroup;

    @ApiModelProperty(value = "0:通用旺金币 1:组别币 2:sku币 null 代表全部")
    private Integer coinsType;
}
