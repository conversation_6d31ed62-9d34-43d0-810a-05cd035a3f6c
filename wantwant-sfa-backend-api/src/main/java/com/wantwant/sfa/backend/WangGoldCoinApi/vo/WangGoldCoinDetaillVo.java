package com.wantwant.sfa.backend.WangGoldCoinApi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "旺金币明细返回参数")
@Data
public class WangGoldCoinDetaillVo {

  @ApiModelProperty(value = "旺金币信息列表")
  private List<WangGoldCoinVo> wangGoldCoinVo;

  @ApiModelProperty(value = "旺金币审核流程列表")
  private List<WangGoldCoinOperationVo> wangGoldCoinOperationVo;

  @ApiModelProperty(value = "数量")
  private Integer count;
}
