package com.wantwant.sfa.backend.WangGoldCoinApi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value = "旺金币表头返回参数")
@Data
public class WangGoldCoinHeaderVo {

  @ApiModelProperty(value = "旺金币申请ID")
  private Integer batchId;

  @ApiModelProperty(value = "归属开始月份")
  private String startMonth;

  @ApiModelProperty(value = "归属结束月份")
  private String endMonth;

  @ApiModelProperty(value = "标题")
  private String title;

  @ApiModelProperty(value = "边际（1.上 2.下）")
  private Integer boundary;

  @ApiModelProperty(value = "导入月份")
  private String importMonth;

  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
  @ApiModelProperty(value = "操作时间")
  private LocalDateTime operatingTime;

  @ApiModelProperty(value = "一级分类名称")
  private String mainCategoryName;

  @ApiModelProperty(value = "二级分类名称")
  private String secondaryCategoryName;

  private String totalAmount;

  @ApiModelProperty("费用类型")
  private String expensesType;

  @ApiModelProperty("费用承担部门")
  private String deptName;

  @ApiModelProperty(value = "操作类型(1.业务 2.组织)")
  private String operationType;


  @ApiModelProperty(value = "操作人员")
  private String operatingName;

  @ApiModelProperty(value = "状态")
  private String state;

  @ApiModelProperty(value = "0:通用旺金币 1:组别币 2:sku币")
  private int coinsType;

}
