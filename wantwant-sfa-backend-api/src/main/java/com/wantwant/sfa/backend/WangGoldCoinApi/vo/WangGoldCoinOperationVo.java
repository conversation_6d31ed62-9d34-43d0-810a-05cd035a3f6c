package com.wantwant.sfa.backend.WangGoldCoinApi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value = "旺金币审核流程返回参数")
@Data
public class WangGoldCoinOperationVo {

  @ApiModelProperty(value = "审核人类型(1.企划2.财务3.主管)")
  private Integer auditType;

  @ApiModelProperty(value = "审核状态(1.待提交2.待审核3.通过(完成)4.驳回)")
  private Integer state;

  @ApiModelProperty(value = "操作者姓名")
  private String empName;

  private LocalDateTime createTime;

  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
  @ApiModelProperty(value = "时间")
  private LocalDateTime time;

  @ApiModelProperty(value = "原因")
  private String remark;
}
