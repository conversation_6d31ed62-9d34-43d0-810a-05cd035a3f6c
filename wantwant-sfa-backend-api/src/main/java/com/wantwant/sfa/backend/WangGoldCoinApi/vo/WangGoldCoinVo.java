package com.wantwant.sfa.backend.WangGoldCoinApi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "旺金币明细信息返回参数")
@Data
public class WangGoldCoinVo {

    @ApiModelProperty(value = "费用归属月份")
    private String month;

    @ApiModelProperty(value = "费用导入月份")
    private String importMonth;

    @ApiModelProperty(value = "大区")
    private String areaName;

    @ApiModelProperty(value = "一级分类名称")
    private String mainCategoryName;

    @ApiModelProperty(value = "二级分类名称")
    private String secondaryCategoryName;

    @ApiModelProperty(value = "分公司(组织名称)")
    private String companyName;

    @ApiModelProperty(value = "费用承担部门")
    private String deptName;

    @ApiModelProperty(value = "岗位")
    private String positionId;

    @ApiModelProperty(value = "边际（1.上 2.下）")
    private Integer boundary;

    @ApiModelProperty(value = "姓名")
    private String customerName;

    @ApiModelProperty(value = "手机号")
    private String mobileNumber;

    @ApiModelProperty(value = "费用类型")
    private String expensesType;

    @ApiModelProperty(value = "费用")
    private BigDecimal amount;

    @ApiModelProperty(value = "费用率")
    private String expensesRate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "明细id")
    private Integer detailId;

    @ApiModelProperty(value = "状态: 0.未发放 1.已发放 2.发放失败")
    private Integer status;

    @ApiModelProperty(value = "归属开始月份")
    private String startMonth;

    @ApiModelProperty(value = "归属结束月份")
    private String endMonth;

    @ApiModelProperty(value = "0:通用旺金币 1:组别币 2:sku币 3.年节币")
    private Integer coinsType;

    @ApiModelProperty(value = "币种子类")
    private String coinsTypeSubName;

    @ApiModelProperty(value = "省区")
    private String provinceName;

    @ApiModelProperty(value = "大区/虚拟大区")
    private String vareaName;

    @ApiModelProperty(value = "营业所")
    private String departmentName;

    @ApiModelProperty(value = "spu费用支出")
    private String spuExpenditure;

}
