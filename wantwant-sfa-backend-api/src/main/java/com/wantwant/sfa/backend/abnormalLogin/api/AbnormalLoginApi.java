package com.wantwant.sfa.backend.abnormalLogin.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginAuditRequest;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginCommitRequest;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginRequest;
import com.wantwant.sfa.backend.abnormalLogin.vo.AbnormalLoginListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 异常登陆API。
 * @Auther: zhangpengpeng
 * @Date: 2024/10/22
 */
@Api(value = "AbnormalLoginApi", tags = "异常登录API")
@RequestMapping("/abnormal/login")
public interface AbnormalLoginApi {

    @ApiOperation(value = "异常登录提交", notes = "异常登录提交", httpMethod = "POST")
    @PostMapping("/commit")
    Response abnormalLoginCommit(@RequestBody @Validated AbnormalLoginCommitRequest request);

    @ApiOperation(value = "异常登录列表", notes = "异常登录列表", httpMethod = "POST")
    @PostMapping("/page")
    Response<IPage<AbnormalLoginListVo>> abnormalLoginPage(@RequestBody @Validated AbnormalLoginRequest request);

    @ApiOperation(value = "异常登录明细列表", notes = "异常登录明细列表", httpMethod = "POST")
    @PostMapping("/list")
    Response<List<AbnormalLoginListVo>> abnormalLoginList(@RequestBody @Validated AbnormalLoginRequest request);

    @ApiOperation(value = "异常登录更多列表", notes = "异常登录更多列表", httpMethod = "POST")
    @PostMapping("/more")
    Response<IPage<AbnormalLoginListVo>> abnormalLoginMore(@RequestBody @Validated AbnormalLoginRequest request);

    @ApiOperation(value = "异常登录列表-下载", notes = "异常登录列表-下载", httpMethod = "POST")
    @PostMapping(value = "/download")
    void abnormalLoginDownload(@RequestBody @Validated AbnormalLoginRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "异常登录稽核", notes = "异常登录稽核", httpMethod = "POST")
    @PostMapping("/audit")
    Response abnormalLoginAudit(@RequestBody @Validated AbnormalLoginAuditRequest request);

}
