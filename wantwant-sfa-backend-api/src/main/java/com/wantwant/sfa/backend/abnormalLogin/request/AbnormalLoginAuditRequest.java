package com.wantwant.sfa.backend.abnormalLogin.request;

import com.wantwant.sfa.backend.common.validation.AllowValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "异常登录稽核")
public class AbnormalLoginAuditRequest implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;

    @ApiModelProperty(value = "稽核结果")
    @AllowValue(value = "1,2", message = "稽核结果传入不正确")
    private Integer auditResult;

    @ApiModelProperty(value = "稽核异常原因")
    @AllowValue(value = "1,2,3,4,5,6", message = "稽核异常原因传入不正确")
    private Integer auditReason;

    @ApiModelProperty("稽核备注")
    @Length(max = 200, message = "稽核备注不能超过200个字符")
    private String auditComment;

    @ApiModelProperty(value = "是否申请线下稽核")
    private Boolean isApplyOffline;

    @ApiModelProperty(value = "复核结果")
    @AllowValue(value = "1,2", message = "复核结果传入不正确")
    private Integer leoAuditResult;

    @ApiModelProperty(value = "复核异常原因")
    @AllowValue(value = "1,2,3,4,5,6", message = "复核异常原因传入不正确")
    private Integer leoAuditReason;

    @ApiModelProperty("复核备注")
    @Length(max = 200, message = "复核备注不能超过200个字符")
    private String leoAuditComment;

    @ApiModelProperty(value = "是否确认线下稽核")
    private Boolean isConfirmOffline;

}
