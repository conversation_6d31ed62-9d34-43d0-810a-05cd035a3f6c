package com.wantwant.sfa.backend.abnormalLogin.request;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "异常登录提交")
public class AbnormalLoginCommit {

    @ApiModelProperty(value = "是否历史数据", required = true)
    @NotNull(message = "是否历史数据不能为空")
    private Boolean isHistory;

    @ApiModelProperty(value = "异常类型（login_abnormal_type）", required = true)
    @NotNull(message = "异常类型不能为空")
    private Integer abnormalType;

    @ApiModelProperty(value = "异常原因（login_abnormal_reason）", required = true)
    @NotNull(message = "异常原因不能为空")
    private Integer abnormalReason;

    @ApiModelProperty(value = "本次活体照片")
    private String liveImage;

    @ApiModelProperty(value = "身份证正面照片")
    private String idImage;

    @ApiModelProperty(value = "首次登录照片")
    private String firstPhotos;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "登录手机号")
    private String mobileNumber;

    @ApiModelProperty(value = "IP地址")
    private String loginIp;

    @ApiModelProperty(value = "设备型号")
    private String deviceModel;

    @ApiModelProperty(value = "登录时间")
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime loginTime;

    @ApiModelProperty(value = "登录位置")
    private String loginAddress;

    @ApiModelProperty(value = "办公地址")
    private String partnerOffice;

}
