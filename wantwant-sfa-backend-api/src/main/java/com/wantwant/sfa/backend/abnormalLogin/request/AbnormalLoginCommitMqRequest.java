package com.wantwant.sfa.backend.abnormalLogin.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "异常登录提交")
public class AbnormalLoginCommitMqRequest {

    @ApiModelProperty(value = "memberKey", required = true)
    @NotNull(message = "memberKey不能为空")
    private Long memberKey;

    @ApiModelProperty(value = "异常登录提交List")
    private List<AbnormalLoginCommitMq> abnormalLoginCommitList;

}
