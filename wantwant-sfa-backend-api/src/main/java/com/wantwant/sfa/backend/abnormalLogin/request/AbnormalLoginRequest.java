package com.wantwant.sfa.backend.abnormalLogin.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel(value = "异常登录req")
public class AbnormalLoginRequest extends PageParam implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    /* 排序通用字段 */
    @ApiModelProperty(value = "排序名称")
    private String sortName;
    @ApiModelProperty(value = "排序类型（desc.倒序 asc.正序）")
    private String sortType;

    /* 操作人相关字段 */
    @NotBlank(message = "操作人工号不能为空")
    @ApiModelProperty(value = "操作人工号", required = true)
    private String person;
    @ApiModelProperty(value = "业务组", hidden = true)
    private Integer personBusinessGroup;
    @ApiModelProperty(value = "组织类型", hidden = true)
    private String personOrganizationType;
    @ApiModelProperty(value = "是否导出全组")
    private Boolean downloadAllGroup = false;

    /* 查询通用字段 */
    @ApiModelProperty("组织Id")
    private String organizationId;
    @ApiModelProperty("筛选的组织id")
    private List<String> chooseOrganizationIds;

    /* 业务参数字段 */
    @ApiModelProperty("稽核状态")
    private Integer auditResult;
    @ApiModelProperty("复核状态")
    private Integer leoAuditResult;
    @ApiModelProperty("姓名或手机号，模糊匹配")
    private String employeeInfo;
    @ApiModelProperty("本人相似度:1(0-30),2(30-50),3(50-80),4(80-)")
    private Integer selfSimilarityType;
    @ApiModelProperty("相似度:1(0-30),2(30-50),3(50-80),4(80-)")
    private Integer similarityType;
    @ApiModelProperty("岗位类型：7.总部 1.总督导 12.大区总监 11.省区总监 2.区域总监 10.区域经理 3.合伙人 311.全职合伙人 312.兼职合伙人 32.企业合伙人 38.直营合伙人 36.业务BD 37.承揽BD")
    private Integer positionType;
    @ApiModelProperty(value = "是否申请线下稽核")
    private Boolean isApplyOffline;
    @ApiModelProperty(value = "是否确认线下稽核")
    private Boolean isConfirmOffline;

    @ApiModelProperty(value = "登录日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate loginTimeFrom;
    @ApiModelProperty(value = "登录日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate loginTimeTo;

    @ApiModelProperty(value = "稽核日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate auditTimeFrom;
    @ApiModelProperty(value = "稽核日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate auditTimeTo;

    @ApiModelProperty(value = "异常原因")
    private Integer abnormalReason;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("memberKeyList")
    private List<Long> memberKeyList;

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("IP地址")
    private String loginIp;

}
