package com.wantwant.sfa.backend.abnormalLogin.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.coverter.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel("异常登录列表")
public class AbnormalLoginListVo {

    @ApiModelProperty("ID")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "memberKey")
    @ExcelIgnore
    private Long memberKey;

    @ApiModelProperty(value = "登录时间")
    @ExcelProperty(value = "登录时间", converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime loginTime;

    @ApiModelProperty(value = "异常类型")
    @ExcelIgnore
    private Integer abnormalType;
    @ExcelProperty(value = "异常类型")
    private String abnormalTypeName;

    @ApiModelProperty(value = "异常原因")
    @ExcelIgnore
    private Integer abnormalReason;
    @ExcelProperty(value = "异常原因")
    private String abnormalReasonName;

    @ApiModelProperty(value = "异常程度")
    @ExcelProperty(value = "异常程度")
    private String abnormalDegreeStr;
    @ExcelIgnore
    private Integer abnormalDegree;

    @ApiModelProperty(value = "异常等级")
    @ExcelProperty(value = "异常等级")
    private String abnormalLevelStr;
    @ExcelIgnore
    private Integer abnormalLevel;

    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;
    @ApiModelProperty("业务组名称")
    @ExcelProperty(value = "产品组")
    private String businessGroupName;

    @ApiModelProperty("战区名称")
    @ExcelProperty(value = "战区")
    private String areaName;

    @ApiModelProperty("大区名称")
    @ExcelProperty(value = "大区")
    private String vareaName;

    @ApiModelProperty("省区名称")
    @ExcelProperty(value = "省区")
    private String provinceName;

    @ApiModelProperty("分公司名称")
    @ExcelProperty(value = "分公司")
    private String companyName;

    @ApiModelProperty("营业所名称")
    @ExcelProperty(value = "营业所")
    private String departmentName;

    @ApiModelProperty("组织名称-全称")
    @ExcelIgnore
    private String fullOrganizationName;

    @ApiModelProperty("姓名")
    @ExcelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty("手机号")
    @ExcelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty("岗位类型")
    @ExcelProperty(value = "岗位类型")
    private String positionTypeName;

    @ApiModelProperty(value = "实名认证手持照片")
    @ExcelIgnore
    private String holdCardImage;

    @ApiModelProperty(value = "本次活体照片")
    @ExcelProperty(value = "本次活体照片")
    private String liveImage;

    @ApiModelProperty(value = "身份证正面照片")
    @ExcelIgnore
    private String idImage;

    @ApiModelProperty(value = "本人相似度")
    @ExcelProperty(value = "本人对比相似度")
    private BigDecimal selfSimilarity;

    @ApiModelProperty(value = "首次登录照片")
    @ExcelIgnore
    private String firstPhotos;

    @ApiModelProperty(value = "首次登录照片相似度")
    @ExcelIgnore
    private BigDecimal firstSimilarity;

    @ApiModelProperty(value = "建档照片")
    @ExcelIgnore
    private String archivesImage;

    @ApiModelProperty(value = "建档照片相似度")
    @ExcelIgnore
    private BigDecimal archivesSimilarity;

    @ApiModelProperty(value = "本人最高相似度照片")
    @ExcelIgnore
    private String selfMaxSimilarityImage;

    @ApiModelProperty(value = "本人最高相似度")
    @ExcelIgnore
    private BigDecimal selfMaxSimilarity;

    @ApiModelProperty(value = "是否直接上级")
    @ExcelIgnore
    private Boolean directParentFlg;

    @ApiModelProperty(value = "上级姓名")
    @ExcelIgnore
    private String parentEmployeeName;

    @ApiModelProperty(value = "上级照片")
    @ExcelIgnore
    private String parentImage;

    @ApiModelProperty(value = "上级相似度")
    @ExcelIgnore
    private BigDecimal parentSimilarity;

    @ApiModelProperty(value = "上级照片(通关)")
    @ExcelIgnore
    private String parentCompleteImage;

    @ApiModelProperty(value = "上级相似度(通关)")
    @ExcelIgnore
    private BigDecimal parentCompleteSimilarity;

    @ApiModelProperty(value = "上级组织")
    @ExcelIgnore
    private String parentOrganizationId;

    @ApiModelProperty(value = "上级组织")
    @ExcelIgnore
    private String parentOrganizationNames;

    @ApiModelProperty(value = "上级组织类型")
    @ExcelIgnore
    private String parentOrganizationType;
    @ApiModelProperty(value = "上级组织类型名称")
    @ExcelIgnore
    private String parentOrganizationTypeName;

    @ApiModelProperty(value = "上级最高相似度照片")
    @ExcelProperty(value = "同组对比相似度（最高）活体照片")
    private String parentMaxSimilarityImage;

    @ApiModelProperty(value = "上级最高相似度")
    @ExcelProperty(value = "同组对比相似度（最高）")
    private BigDecimal parentMaxSimilarity;

    @ApiModelProperty(value = "稽核时间")
    @ExcelProperty(value = "稽核时间", converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "稽核人员工号")
    @ExcelIgnore
    private String auditEmployeeId;

    @ApiModelProperty(value = "稽核结果")
    @ExcelIgnore
    private Integer auditResult;
    @ExcelProperty(value = "稽核结果")
    private String auditResultName;

    @ApiModelProperty(value = "稽核异常原因")
    @ExcelIgnore
    private Integer auditReason;
    @ExcelProperty(value = "稽核异常原因")
    private String auditReasonName;

    @ApiModelProperty(value = "稽核备注")
    @ExcelProperty(value = "稽核备注")
    private String auditComment;

    @ApiModelProperty(value = "是否申请线下稽核")
    @ExcelIgnore
    private Boolean isApplyOffline;

    @ApiModelProperty(value = "稽核异常次数")
    @ExcelIgnore
    private Integer abnormalAuditCount;

    @ApiModelProperty(value = "复核时间")
    @ExcelProperty(value = "复核时间", converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime leoAuditTime;

    @ApiModelProperty(value = "复核人员工号")
    @ExcelIgnore
    private String leoAuditEmployeeId;

    @ApiModelProperty(value = "复核结果")
    @ExcelIgnore
    private Integer leoAuditResult;
    @ExcelProperty(value = "复核结果")
    private String leoAuditResultName;

    @ApiModelProperty(value = "复核异常原因")
    @ExcelIgnore
    private Integer leoAuditReason;
    @ExcelProperty(value = "复核异常原因")
    private String leoAuditReasonName;

    @ApiModelProperty("复核备注")
    @ExcelProperty(value = "复核备注")
    private String leoAuditComment;

    @ApiModelProperty(value = "是否确认线下稽核")
    @ExcelIgnore
    private Boolean isConfirmOffline;

    @ApiModelProperty(value = "是否申请线下稽核")
    @ExcelProperty(value = "申请线下稽核")
    private String isApplyOfflineStr;

    @ApiModelProperty(value = "是否确认线下稽核")
    @ExcelProperty(value = "确认线下稽核")
    private String isConfirmOfflineStr;

    @ApiModelProperty(value = "设备ID")
    @ExcelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "IP地址")
    @ExcelProperty(value = "IP地址")
    private String loginIp;

    @ApiModelProperty(value = "设备型号")
    @ExcelProperty(value = "设备型号")
    private String deviceModel;

    @ApiModelProperty(value = "登录位置")
    @ExcelProperty(value = "登录位置")
    private String loginAddress;

    @ApiModelProperty(value = "办公地址")
    @ExcelProperty(value = "联系地址")
    private String partnerOffice;

    @ApiModelProperty(value = "登录人角色")
    @ExcelIgnore
    private Integer mobilePositionTypeId;

    @ApiModelProperty(value = "登录人角色")
    @ExcelProperty(value = "登录人角色")
    private String mobilePositionTypeName;

    @ApiModelProperty(value = "登录人")
    @ExcelProperty(value = "登录人")
    private String mobileEmployeeName;

    @ApiModelProperty(value = "登录手机号")
    @ExcelProperty(value = "登录手机号")
    private String mobileNumber;

    @ApiModelProperty("登录人-组织名称-全称")
    @ExcelIgnore
    private String mobileFullOrganizationName;

    @ApiModelProperty(value = "稽核按钮")
    @ExcelIgnore
    private Boolean showAuditButton;

    @ApiModelProperty(value = "复核按钮")
    @ExcelIgnore
    private Boolean showLeoAuditButton;

}
