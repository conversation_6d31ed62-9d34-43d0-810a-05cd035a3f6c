package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaControlVo;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaVo;
import com.wantwant.sfa.backend.activityQuota.vo.RetrieveQuotaVo;
import com.wantwant.sfa.backend.interview.request.QuotaValidRequest;
import com.wantwant.sfa.backend.order.request.BranchQuotaInfoVo;
import com.wantwant.sfa.backend.order.request.RetriveQuotaInfoRequest;
import com.wantwant.sfa.backend.order.request.RetriveQuotaRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;


/**
 * @Description: 活动配额API。
 * @Auther: zhengxu
 * @Date: 2021/11/26/下午2:17
 */
@Api(value = "ActivityQuotaApi",tags = "活动配额API")
public interface ActivityQuotaApi {
    @ApiOperation(value = "活动额度申请", notes = "活动额度申请", httpMethod = "PUT")
    @PutMapping("activityQuota/apply")
    Response applyQuota(@Validated @RequestBody ApplyQuotaRequest request);


    @ApiOperation(value = "活动额度申请取消", notes = "活动额度申请取消", httpMethod = "POST")
    @PostMapping("activityQuota/cancel")
    Response cancel(@Validated @RequestBody CancelQuotaRequest request);


    @ApiOperation(value = "活动额度配置", notes = "活动额度配置", httpMethod = "POST")
    @PostMapping("activityQuota/config")
    Response quotaConfig(@Validated @RequestBody QuotaConfigRequest request);


    @ApiOperation(value = "审核通过", notes = "活动额度配置", httpMethod = "POST")
    @PostMapping("activityQuota/passValid")
    Response passValid(@Validated @RequestBody QuotaValidRequest request);

    @ApiOperation(value = "驳回", notes = "活动额度配置", httpMethod = "POST")
    @PostMapping("activityQuota/faliedValid")
    Response faliedValid(@Validated @RequestBody QuotaValidRequest request);


    @ApiOperation(value = "额度回收明细", notes = "额度回收明细", httpMethod = "POST")
    @PostMapping("activityQuota/retrieveQuotaInfo")
    @ApiIgnore
    Response<RetrieveQuotaVo> retrieveQuotaInfo(@Validated @RequestBody RetriveQuotaInfoRequest request);

    @ApiOperation(value = "额度回收", notes = "额度回收", httpMethod = "POST")
    @PostMapping("activityQuota/retriveQuota")
    @ApiIgnore
    Response retriveQuota(@Validated @RequestBody RetriveQuotaRequest request);

    @ApiOperation(value = "营业所额度列表", notes = "额度回收", httpMethod = "POST")
    @PostMapping("activityQuota/getBranchQuotaInfo")
    Response<BranchQuotaInfoVo> getBranchQuotaInfo(@Validated @RequestBody BranchQuotRequest request);

    @ApiOperation(value = "额度发放", notes = "额度发放", httpMethod = "POST")
    @PostMapping("activityQuota/quotaDistribute")
    Response quotaDistribute(@Validated @RequestBody QuotaDistributeRequest request);

    @ApiOperation(value = "查询额度", notes = "查询额度", httpMethod = "GET")
    @GetMapping("activityQuota/quotaSelect")
    Response<QuotaVo> selectQuota(@Param("month") String month,
                                  @Param("organizationId")String organizationId,
                                  @Param("applyType")Integer applyType,
                                  @Param("deptCode")String deptCode);

    @ApiOperation(value = "额度发放余额查询", notes = "额度发放余额查询", httpMethod = "POST")
    @PostMapping("activityQuota/surplus")
    Response<QuotaControlVo> selectDistributeQuota(@Validated @RequestBody QuotaControlRequest request);

    @ApiOperation(value = "设置额度管理权限", notes = "设置额度管理权限", httpMethod = "POST")
    @PostMapping("activityQuota/setQuotaPermission")
    Response SetQuotaPermission(@Valid @RequestBody SetQuotaPermissionRequest request);


    @ApiOperation(value = "获取默认部门CODE", notes = "获取默认部门CODE", httpMethod = "POST")
    @GetMapping("activityQuota/getDefaultDeptCode")
    Response<String> getDefaultDeptCode();



}
