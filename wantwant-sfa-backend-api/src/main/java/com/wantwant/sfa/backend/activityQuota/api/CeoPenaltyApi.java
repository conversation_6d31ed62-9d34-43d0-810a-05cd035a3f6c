package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.CeoPenaltyRequest;
import com.wantwant.sfa.backend.activityQuota.request.PersonalPenaltyRequest;
import com.wantwant.sfa.backend.activityQuota.request.RewardPenaltyDeductionRequest;
import com.wantwant.sfa.backend.activityQuota.vo.RewardPenaltyDeductionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/04/上午10:11
 */
@Api(value = "合伙人扣罚接口",tags = "合伙人扣罚接口")
@RequestMapping("/ceoPenalty")
public interface CeoPenaltyApi {

    @ApiOperation(value = "合伙人扣罚处理", notes = "合伙人扣罚处理", httpMethod = "POST")
    @PostMapping("/process")
    Response<List<String>> process(@RequestBody List<CeoPenaltyRequest> list);

    @ApiOperation(value = "组织扣罚处理", notes = "组织扣罚处理", httpMethod = "POST")
    @PostMapping("/orgProcess")
    Response<List<String>> orgProcess(@Valid @RequestBody List<OrgPenaltyRequest> list);

    @ApiOperation(value = "个人扣罚记录", notes = "个人扣罚记录", httpMethod = "POST")
    @PostMapping("/personalPenalty")
    Response<List<String>> personalPenalty(@Valid @RequestBody List<PersonalPenaltyRequest> list);


}
