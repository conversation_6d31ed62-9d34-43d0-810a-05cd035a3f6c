package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.CoinsTypeApplyApprovalRequest;
import com.wantwant.sfa.backend.activityQuota.request.CoinsTypeApplyRejectRequest;
import com.wantwant.sfa.backend.activityQuota.request.CostTypeApplyRequest;
import com.wantwant.sfa.backend.activityQuota.request.CostTypeTriggerStatusRequest;
import com.wantwant.sfa.backend.activityQuota.vo.CoinsApplyProcessVO;
import com.wantwant.sfa.backend.activityQuota.vo.CoinsTypeApplyInfoVo;
import com.wantwant.sfa.backend.activityQuota.vo.CoinsTypeApplyPermissionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.http.Path;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/17/上午10:56
 */
@Api(value = "CoinsTypeApplyApi",tags = "费用类型申请API")
public interface CoinsTypeApplyApi {

    @PostMapping("/coinsTypeApply")
    @ApiOperation(value = "费用类型申请", notes = "费用类型申请", httpMethod = "POST")
    Response apply(@Validated @RequestBody CostTypeApplyRequest costTypeApplyRequest);

    @PostMapping("/coinsTypeApply/approval")
    @ApiOperation(value = "费用类型申请审核通过", notes = "费用类型申请审核通过", httpMethod = "POST")
    Response approval(@Validated @RequestBody CoinsTypeApplyApprovalRequest coinsTypeApplyApprovalRequest);

    @PostMapping("/coinsTypeApply/reject")
    @ApiOperation(value = "费用类型申请审核驳回", notes = "费用类型申请审核驳回", httpMethod = "POST")
    Response reject(@Validated @RequestBody CoinsTypeApplyRejectRequest coinsTypeApplyRejectRequest);

    @GetMapping("/coinsTypeApply/{applyId}/{person}")
    @ApiOperation(value = "获取申请详情", notes = "获取申请详情", httpMethod = "GET")
    Response<CoinsTypeApplyInfoVo> getCoinsTypeApplyInfo(@PathVariable Long applyId, @PathVariable String person);

    @GetMapping("/coinsTypeProcess/{applyId}")
    @ApiOperation(value = "获取审核流程", notes = "获取审核流程", httpMethod = "GET")
    Response<List<CoinsApplyProcessVO>> getProcessVo(@PathVariable Long applyId);

    @GetMapping("/coinsTypePermission/{person}")
    @ApiOperation(value = "获取权限", notes = "获取权限", httpMethod = "GET")
    Response<CoinsTypeApplyPermissionVo> getPermission(@PathVariable String person);

    @PostMapping("/coinsTypeApply/triggerStatus")
    @ApiOperation(value = "修改启用状态", notes = "修改启用状态", httpMethod = "POST")
    Response triggerStatus(@Valid @RequestBody CostTypeTriggerStatusRequest costTypeTriggerStatusRequest);
}
