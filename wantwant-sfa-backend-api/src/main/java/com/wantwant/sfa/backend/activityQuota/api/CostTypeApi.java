package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.CostTypeRequest;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/06/上午10:27
 */
@Api(value = "CostTypeApi",tags = "费用类型API")
public interface CostTypeApi {

    @ApiOperation(value = "获取费用承担部门信息", notes = "获取费用承担部门信息", httpMethod = "GET")
    @GetMapping(value = "/costDepartment/{costType}")
    Response<List<CostDepartmentVo>> getCostDepartment(@PathVariable Integer costType);

    @ApiOperation(value = "费用类型关系表", notes = "费用类型关系表", httpMethod = "POST")
    @PostMapping("/costType/list")
    Response<Page<CostTypeVo>> costTypeList(@RequestBody CostTypeRequest request);

    @ApiOperation(value = "费用类型关系表导出", notes = "费用类型关系表导出", httpMethod = "POST")
    @PostMapping("/costType/export")
    void costTypeExport(@RequestBody CostTypeRequest request);

    @ApiOperation(value = "费用类型分类列表", notes = "费用类型分类列表", httpMethod = "GET")
    @GetMapping(value = {"/costType/category","/costType/category/{mainCategoryId}"})
    Response<List<CostCategoryVo>> costCategoryList(@PathVariable(required = false) Integer mainCategoryId);

    @ApiOperation(value = "根据费用类型获取配置信息", notes = "根据费用类型获取配置信息", httpMethod = "GET")
    @GetMapping(value = "/costType/config/{applyType}")
    Response<CoinsTypeVo> getCoinsConfig(@PathVariable String applyType);

    @ApiOperation(value = "获取费用大类信息", notes = "获取费用大类信息", httpMethod = "GET")
    @GetMapping(value = "/costType/classType")
    Response<List<CoinsClassTypeVo>> getClassType();



}
