package com.wantwant.sfa.backend.activityQuota.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/13/下午6:48
 */
@Data
@ApiModel("组织扣罚")
@ToString
public class OrgPenaltyRequest {
    @ApiModelProperty("扣罚组织名称")
    @NotBlank(message = "缺少扣罚组织名称")
    private String organizationName;
    @ApiModelProperty("扣罚项目名称")
    private String penaltyItem;
    @ApiModelProperty("扣罚额度")
    private BigDecimal penaltyQuota;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("产品组")
    private String businessGroupCode;
}
