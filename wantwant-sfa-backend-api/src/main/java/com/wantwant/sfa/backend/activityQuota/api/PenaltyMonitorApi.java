package com.wantwant.sfa.backend.activityQuota.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyDetailVo;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyMonitorVO;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyVo;
import com.wantwant.sfa.backend.activityQuota.vo.RewardPenaltyDeductionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/17/上午9:29
 */
@Api(value = "扣罚监控",tags = "扣罚监控API")
@RequestMapping("/penaltyMonitor")
public interface PenaltyMonitorApi {

    @ApiOperation(value = "扣罚接口列表", notes = "扣罚接口列表", httpMethod = "POST")
    @PostMapping("/select")
    Response<Page<PenaltyVo>> selectPenalty(@RequestBody PenaltyQueryRequest request);

    @ApiOperation(value = "查询扣罚", notes = "扣罚接口列表", httpMethod = "POST")
    @PostMapping("/search")
    Response<IPage<PenaltyMonitorVO>> selectPenaltyMonitor(@RequestBody @Valid PenaltyMonitorSearchRequest penaltyMonitorSearchRequest);

    @ApiOperation(value = "查询扣罚导出", notes = "查询扣罚导出", httpMethod = "POST")
    @PostMapping("/search/export")
    void exportPenaltyMonitor(@RequestBody @Valid PenaltyMonitorSearchRequest penaltyMonitorSearchRequest);

    @ApiOperation(value = "获取扣罚明细", notes = "获取扣罚明细", httpMethod = "GET")
    @GetMapping("/{penaltyId}")
    Response<List<PenaltyDetailVo>> getPenaltyDetail(@PathVariable Long penaltyId);

    @ApiOperation(value = "旺金币扣罚导出", notes = "旺金币扣罚导出", httpMethod = "POST")
    @PostMapping("/export")
    void exportPenalty(@RequestBody PenaltyQueryRequest request);

    @ApiOperation(value = "扣罚操作", notes = "扣罚操作", httpMethod = "POST")
    @PostMapping("/updateStatus")
    Response updateStatus(@RequestBody UpdateStatusRequest request);

    @ApiOperation(value = "扣罚操作批处理", notes = "扣罚操作批处理", httpMethod = "POST")
    @PostMapping("/updateStatusBatch")
    Response batchUpdateStatus(@RequestBody UpdateStatusBatchRequest request);


    @ApiOperation(value = "导入扣罚项目" ,notes = "导入扣罚项目" ,httpMethod = "PUT")
    @PutMapping("/upload")
    Response<List<String>> upload(@RequestParam(value = "file", required = true) MultipartFile file,
                                  @RequestParam(value="person",required = true)String person);

    @ApiOperation(value = "检查是否有操作权限" ,notes = "检查是否有操作权限" ,httpMethod = "GET")
    @GetMapping("/checkCanProcess/{person}")
    Response<Boolean> checkCanProcess(@PathVariable String person);


    @ApiOperation(value = "奖惩扣罚", notes = "奖惩扣罚", httpMethod = "POST")
    @PostMapping("/rewardPenaltyDeduction")
    Response rewardPenaltyDeduction(@Valid @RequestBody RewardPenaltyOperateRequest request);
}
