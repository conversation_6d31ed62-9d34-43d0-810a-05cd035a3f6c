package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyAttendanceRegular;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyCategoryVo;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyEliminateRegularVo;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyRegularVo;
import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午1:05
 */
@Api(value = "罚款规则API",tags = "罚款规则API")
@RequestMapping("/penaltyRegular")
public interface PenaltyRegularApi {

    @ApiOperation(value = "扣罚规则列表", notes = "扣罚规则列表", httpMethod = "POST")
    @PostMapping("/list")
    Response<Page<PenaltyRegularVo>> getPenaltyRegularList(@RequestBody PenaltyRegularSearchRequest request);

    @ApiOperation(value = "扣罚规则列表导出", notes = "扣罚规则列表导出", httpMethod = "POST")
    @PostMapping("/export")
    void penaltyRegularExport(@RequestBody PenaltyRegularSearchRequest request);

    @ApiOperation(value = "获取扣罚类型", notes = "获取扣罚类型", httpMethod = "GET")
    @GetMapping("/category")
    Response<List<PenaltyCategoryVo>> getPenaltyCategory();

    @ApiOperation(value = "创建扣罚规则", notes = "创建扣罚规则", httpMethod = "PUT")
    @PutMapping("/create")
    Response create(@RequestBody @Valid CPenaltyRequest request);

    @ApiOperation(value = "修改规则", notes = "修改规则", httpMethod = "POST")
    @PostMapping("/update")
    Response update(@RequestBody @Valid UPenaltyRequest request);

    @ApiOperation(value = "获取特批汰换合伙人扣款详情", notes = "获取特批汰换合伙人扣款详情", httpMethod = "GET")
    @GetMapping("/eliminateInfo")
    Response<PenaltyEliminateRegularVo> getPenaltyEliminateInfo();

    @ApiOperation(value = "获取考勤异常扣款信息", notes = "获取考勤异常扣款信息", httpMethod = "GET")
    @GetMapping("/attendanceInfo")
    Response<PenaltyAttendanceRegular> getPenaltyAttendanceInfo();

    @ApiOperation(value = "关闭/启用扣罚规则", notes = "启用扣罚规则", httpMethod = "POST")
    @PostMapping("/switch")
    Response switchRegular(@RequestBody @Valid PenaltySwitchRequest penaltyCloseRequest);

    @ApiOperation(value = "修改特批汰换合伙人扣款", notes = "修改特批汰换合伙人扣款", httpMethod = "POST")
    @PostMapping("/updatePenaltyEliminateRegular")
    Response updatePenaltyEliminateRegular(@RequestBody @Valid PenaltyEliminateRegularEditRequest penaltyEliminateRegularEditRequest);

    @ApiOperation(value = "修改考勤异常扣款", notes = "修改考勤异常扣款", httpMethod = "POST")
    @PostMapping("/updatePenaltyAttendanceRegular")
    Response updateAttendanceRegular(@RequestBody @Valid PenaltyAttendanceRegularRequest request);

    @ApiOperation(value = "获取规则信息", notes = "获取规则信息", httpMethod = "GET")
    @GetMapping("/{id}")
    Response<PenaltyRegularVo> getRegular(@PathVariable Long id);

    @ApiOperation(value = "获取所有规则", notes = "获取规则信息", httpMethod = "GET")
    @GetMapping("/getAllRegular")
    Response<List<PenaltyRegularVo>> getAllRegular();
}
