package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.CeoQuotaApplyRequest;
import com.wantwant.sfa.backend.activityQuota.request.CeoQuotaDistributeRequest;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaSurplusVo;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/04/上午11:37
 */
@Api(value = "QuotaCeoApi",tags = "旺铺造旺币接口")
public interface QuotaCeoApi {

    @ApiOperation(value = "额度申请", notes = "额度申请", httpMethod = "POST")
    @PostMapping("/ceo/quotaApply")
    Response quotaApply(@RequestBody CeoQuotaApplyRequest request);

    @ApiOperation(value = "剩余额度查询", notes = "剩余额度查询", httpMethod = "GET")
    @GetMapping("/ceo/quotaSearch/{companyName}/{businessGroupCode}")
    Response<QuotaSurplusVo> quotaSearch(@PathVariable String companyName,@PathVariable String businessGroupCode);


    @ApiOperation(value = "合伙人额度发放", notes = "合伙人额度发放", httpMethod = "POST")
    @PostMapping("/ceo/quota/distribute")
    Response distribute(@RequestBody @Validated CeoQuotaDistributeRequest ceoQuotaDistributeRequest);
}
