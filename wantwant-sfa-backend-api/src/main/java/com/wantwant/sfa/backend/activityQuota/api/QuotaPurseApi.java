package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.BusinessQuotaRequest;
import com.wantwant.sfa.backend.activityQuota.request.OrgQuotaRequest;
import com.wantwant.sfa.backend.activityQuota.request.PurseSearchRequest;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/09/上午10:16
 */
@Api(value = "QuotaPurseApi",tags = "造旺币收支")
public interface QuotaPurseApi {

    @ApiOperation(value = "大区收支", notes = "大区收支", httpMethod = "POST")
    @PostMapping("/purse/area")
    Response<Page<AreaPurseVo>> selectAreaPurse(@RequestBody PurseSearchRequest request);

    @ApiOperation(value = "大区收支导出", notes = "大区收支导出", httpMethod = "POST")
    @PostMapping("/purse/export/area")
    void areaPurseExport(@RequestBody PurseSearchRequest request);

    @ApiOperation(value = "分公司收支", notes = "分公司收支", httpMethod = "POST")
    @PostMapping("/purse/company")
    Response<Page<CompanyPurseVo>> selectCompanyPurse(@RequestBody PurseSearchRequest request);

    @ApiOperation(value = "分公司收支导出", notes = "分公司收支导出", httpMethod = "POST")
    @PostMapping("/purse/export/company")
    void companyPurseExport(@RequestBody PurseSearchRequest request);

    @ApiOperation(value = "业务收支", notes = "业务收支", httpMethod = "POST")
    @PostMapping("/purse/business")
    Response<Page<CeoPurseVo>> selectBusinessPurse(@RequestBody PurseSearchRequest request);


    @ApiOperation(value = "业务收支导出", notes = "业务收支导出", httpMethod = "POST")
    @PostMapping("/purse/export/business")
    void businessPurseExport(@RequestBody PurseSearchRequest request);

    @ApiOperation(value = "额度总览-大区", notes = "额度总览-大区", httpMethod = "POST")
    @PostMapping("/purse/area/quota")
    Response<Page<AreaQuotaVo>> selectAreaQuota(@RequestBody OrgQuotaRequest request);

    @ApiOperation(value = "额度总览-大区导出", notes = "额度总览-大区导出", httpMethod = "POST")
    @PostMapping("/purse/area/quota/export")
    void areaQuotaExport(@RequestBody OrgQuotaRequest request);

    @ApiOperation(value = "额度总览-分公司", notes = "额度总览-分公司", httpMethod = "POST")
    @PostMapping("/purse/company/quota")
    Response<Page<CompanyQuotaVo>> selectCompanyQuota(@RequestBody OrgQuotaRequest request);

    @ApiOperation(value = "额度总览-分公司导出", notes = "额度总览-分公司导出", httpMethod = "POST")
    @PostMapping("/purse/company/quota/export")
    void companyQuotaExport(@RequestBody OrgQuotaRequest request);

    @ApiOperation(value = "额度总览-业务", notes = "额度总览-业务", httpMethod = "POST")
    @PostMapping("/purse/business/quota")
    Response<Page<CeoQuotaVo>> selectBusinessQuota(@RequestBody BusinessQuotaRequest request);

    @ApiOperation(value = "额度总览-业务导出", notes = "额度总览-业务导出", httpMethod = "POST")
    @PostMapping("/purse/business/quota/export")
    void businessQuotaExport(@RequestBody BusinessQuotaRequest request);

    @ApiOperation(value = "获取额度权限", notes = "获取额度权限", httpMethod = "GET")
    @GetMapping("/purse/permission")
    Response<PursePermissionVo> getPermission(@RequestParam String organizationId,@RequestParam String person);

    @ApiOperation(value = "新额度总览-大区/分公司/业务", notes = "新额度总览-大区/分公司/业务", httpMethod = "POST")
    @PostMapping("/purse/quota")
    Response<Page<QuotaCollectionVo>> selectQuota(@RequestBody OrgQuotaRequest request);


    @ApiOperation(value = "新额度总览-大区/分公司/业务导出列表", notes = "新额度总览-大区/分公司/业务导出列表", httpMethod = "POST")
    @PostMapping("/purse/quota/export")
    void selectQuotaExport(@RequestBody OrgQuotaRequest request, HttpServletResponse response);


}
