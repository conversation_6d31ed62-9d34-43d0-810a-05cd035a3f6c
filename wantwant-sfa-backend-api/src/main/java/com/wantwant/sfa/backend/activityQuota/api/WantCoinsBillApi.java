package com.wantwant.sfa.backend.activityQuota.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.request.CeoBillDetailRequest;
import com.wantwant.sfa.backend.activityQuota.request.WantCoinsApplyTypeBillRequest;
import com.wantwant.sfa.backend.activityQuota.request.WantCoinsBillRequest;
import com.wantwant.sfa.backend.activityQuota.request.WantCoinsDetailRequest;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/上午9:38
 */
@Api(value = "WantCoinsBillApi",tags = "旺金币账单API")
public interface WantCoinsBillApi {

    @ApiOperation(value = "获取旺金币年账单", notes = "获取旺金币年账单", httpMethod = "GET")
    @GetMapping("/wantCoins/yearBill")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "organizationId",value="当前组织",required = true),
            @ApiImplicitParam(name = "deptCode",value="部门CODE",required = false)
    })
    Response<List<WantCoinsYearBillVo>> getYearBill(@RequestParam String organizationId,@RequestParam(required = false) String deptCode);


    @ApiOperation(value = "获取旺金币合伙人年账单", notes = "获取旺金币合伙人年账单", httpMethod = "GET")
    @GetMapping("/wantCoins/ceoBill")
    @ApiImplicitParam(name = "organizationId",value="organizationId",required = true)
    Response<List<WantCoinsCeoYearBillVo>> getCeoBill(@RequestParam String organizationId);


    @ApiOperation(value = "旺金币构成率", notes = "旺金币构成率", httpMethod = "GET")
    @GetMapping("/wantCoins/coinsRatio")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",value="类型(1.收入 2.支出)",required = true),
            @ApiImplicitParam(name = "year",value="年",required = true),
            @ApiImplicitParam(name = "month",value="月",required = true),
            @ApiImplicitParam(name = "organizationId",value="当前组织",required = true),
            @ApiImplicitParam(name = "deptCode",value="部门CODE",required = false)
    })
    Response<List<WantCoinsRatioVo>> getCoinsRatio(@RequestParam int type,@RequestParam String year,@RequestParam String month,@RequestParam String organizationId,@RequestParam(required = false) String deptCode);


    @ApiOperation(value = "合伙人旺金币构成率", notes = "合伙人旺金币构成率", httpMethod = "GET")
    @GetMapping("/wantCoins/ceoCoinsRatio")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",value="类型(1.收入 2.支出)",required = true),
            @ApiImplicitParam(name = "year",value="年",required = true),
            @ApiImplicitParam(name = "month",value="月",required = true),
            @ApiImplicitParam(name = "organizationId",value="当前组织",required = true),
            @ApiImplicitParam(name = "deptCode",value="部门CODE",required = false)
    })
    Response<List<WantCoinsRatioVo>> getCeoCoinsRatio(@RequestParam int type,@RequestParam String year,@RequestParam String month,@RequestParam String organizationId,@RequestParam(required = false) String deptCode);



    @ApiOperation(value = "获取收支记录明细", notes = "获取收支记录明细", httpMethod = "POST")
    @PostMapping("/wantCoins/detail")
    Response<WantCoinsDetailWrapsVO> getWantCoinsDetail(@Validated @RequestBody WantCoinsBillRequest wantCoinsBillRequest);


    @ApiOperation(value = "获取收支记录合伙人明细", notes = "获取收支记录合伙人明细", httpMethod = "POST")
    @PostMapping("/wantCoinsCeo/detail")
    Response<WantCoinsCeoDetailWrapsVo> getWantCoinsCeoDetail(@Validated @RequestBody WantCoinsDetailRequest wantCoinsDetailRequest);


    @ApiOperation(value = "费用明细导出", notes = "费用明细导出", httpMethod = "POST")
    @PostMapping("/wantCoins/detail/export")
    void exportWantCoinsDetail(@Validated @RequestBody WantCoinsBillRequest wantCoinsBillRequest);


    @ApiOperation(value = "获取旺金币费用类型收支记录明细", notes = "获取旺金币费用类型收支记录明细", httpMethod = "POST")
    @PostMapping("/wantCoins/coinsType/detail")
    Response<Page<WantCoinsCoinsTypeBillVo>> getWantCoinsCoinsTypeBill(@Validated @RequestBody WantCoinsApplyTypeBillRequest request);


    @ApiOperation(value = "获取旺金币费用类型合伙人收支记录明细", notes = "获取旺金币费用类型合伙人收支记录明细", httpMethod = "POST")
    @PostMapping("/wantCoins/coinsTypeCeo/detail")
    Response<List<WantCoinsCoinsTypeCeoBillVo>> getWantCoinsCoinsTypeCeoBill(@Validated @RequestBody WantCoinsApplyTypeBillRequest request);



    @ApiOperation(value = "合伙人费用明细导出", notes = "合伙人费用明细导出", httpMethod = "POST")
    @PostMapping("/wantCoinsCeo/detail/export")
    void exportWantCoinsCeoDetail(@Validated @RequestBody WantCoinsDetailRequest wantCoinsDetailRequest);

    @ApiOperation(value = "合伙人账单明细查询", notes = "合伙人账单明细查询", httpMethod = "POST")
    @PostMapping("/wantCoins/ceoBill/select")
    Response<Page<CeoBillDetailVo>> selectCeoBillDetail(@RequestBody CeoBillDetailRequest request);


    @ApiOperation(value = "合伙人账单明细导出", notes = "合伙人账单明细导出", httpMethod = "POST")
    @PostMapping("/wantCoins/ceoBill/export")
    void exportCeoBillDetail(@RequestBody CeoBillDetailRequest request);
}
