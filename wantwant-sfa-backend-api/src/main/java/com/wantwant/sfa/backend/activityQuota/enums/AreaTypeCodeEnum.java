package com.wantwant.sfa.backend.activityQuota.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/22/上午11:52
 */
public enum AreaTypeCodeEnum {
    // 区域类型Code area 战区 varea 大区 province 省区 company 分公司 department 营业所
    AREA("area","战区"),
    VAREA("varea","大区"),
    PROVINCE("province","省区"),
    COMPANY("company","分公司"),
    DEPARTMENT("department","营业所"),
    UNKNOWN("unknown","未知");

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    AreaTypeCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AreaTypeCodeEnum find(String code){
        AreaTypeCodeEnum[] values = AreaTypeCodeEnum.values();
        for(AreaTypeCodeEnum e : values){
            if(e.getCode().equals(code)){
                return e;
            }
        }
        return UNKNOWN;
    }
}
