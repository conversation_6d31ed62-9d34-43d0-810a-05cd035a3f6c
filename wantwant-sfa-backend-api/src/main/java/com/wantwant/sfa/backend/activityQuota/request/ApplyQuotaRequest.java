package com.wantwant.sfa.backend.activityQuota.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 活动额度申请用Request。
 * @Auther: zhengxu
 * @Date: 2021/11/26/下午2:20
 */
@ApiModel("活动额度申请用Request")
@Data
@ToString
public class ApplyQuotaRequest {
    @NotNull(message = "缺少memberKey")
    @ApiModelProperty(value = "会员memberKey", required = true)
    private Integer memberKey;

    @NotBlank(message = "缺少申请单号")
    @ApiModelProperty(value = "申请单号", required = true)
    private String code;

    @NotNull(message = "缺少活动类型")
    @ApiModelProperty(value = "活动类型", required = true)
    private Integer activityType;

    @NotNull(message = "缺少申请额度")
    @ApiModelProperty(value = "申请额度", required = true)
    private BigDecimal quota;


    @NotBlank(message = "缺少申请人手机号")
    @ApiModelProperty(value = "申请人手机号", required = true)
    private String mobile;

    @NotBlank(message = "缺少申请年月")
    @ApiModelProperty(value = "申请年月:格式为yyyy-MM", required = true)
    private String applyYearMonth;

    @NotNull(message = "缺少渠道ID")
    @ApiModelProperty(value = "渠道ID:1.旺铺 2.造旺 3.旺江山", required = true)
    private Integer businessCode;

    @ApiModelProperty(value = "费用用途")
    private String remark;


    /**
     * 金币类型 0-造旺币 1-旺金币
     */
    @ApiModelProperty("0-造旺币 1-旺金币")
    private Integer amountType = 1;

    /**
     * 金币子类型  默认0-通用币；1-产品组币 ；2-spu币
     */
    @ApiModelProperty("金币子类型  默认0-通用币；1-产品组币 ；2-spu币")
    @Min(value = 1,message = "不支持的类型")
    @Max(value = 1,message = "不支持的类型")
    @NotNull(message = "缺少金币子类型")
    private Integer amountSubType;

    /**
     * 金币子类型唯一编码
     */
    @ApiModelProperty("金币子类型唯一编码")
    @NotBlank(message = "缺少金币子类型")
    private String amountSubTypeId;


}
