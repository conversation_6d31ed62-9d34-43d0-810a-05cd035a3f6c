package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 分公司额度列表request。
 * @Auther: zhengxu
 * @Date: 2021/12/24/上午11:13
 */
@Data
@ApiModel("分公司额度列表request")
public class BranchQuotRequest {
    @ApiModelProperty("营业所名称")
    private String branchName;
    @ApiModelProperty("申请人名称")
    private String applyUserName;
    @ApiModelProperty("小标市场名称")
    private String smallMarketName;
}
