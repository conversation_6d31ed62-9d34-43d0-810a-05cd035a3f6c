package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/15/下午5:17
 */
@Data
@ApiModel("业务别查询")
@ToString
public class BusinessQuotaRequest extends OrgQuotaRequest{
    @ApiModelProperty("职位:1.造旺合伙人 2.区域总监  3.企业合伙人")
    private Integer positionType;
}
