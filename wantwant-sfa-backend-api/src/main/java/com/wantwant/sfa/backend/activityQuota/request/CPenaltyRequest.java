package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午1:09
 */
@Data
@ToString
@ApiModel("创建罚款规则request")
public class CPenaltyRequest {

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;


    @ApiModelProperty("项目名称")
    @NotBlank(message = "缺少项目名称")
    private String regularName;

    @ApiModelProperty("项目规则说明")
    @NotBlank(message = "缺少项目规则说明")
    private String regularDescription;

    @ApiModelProperty("类型名称")
    @NotBlank(message = "缺少类型名称")
    @Size(max = 20,message = "类型名称过长")
    private String categoryName;
    @ApiModelProperty("类型子名称")
    @NotBlank(message = "缺少类型子名称")
    @Size(max = 20,message = "类型子名称过长")
    private String secondaryCategoryName;

    @ApiModelProperty("扣罚金币类型:1.旺金币")
    @NotNull(message = "缺少扣罚金币类型")
    private Integer coinsType;

    @ApiModelProperty("操作方式:1.系统 2.人工")
    @NotNull(message = "缺少操作方式")
    private Integer processType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("费用类型")
    private List<Integer> applyTypeList;
}
