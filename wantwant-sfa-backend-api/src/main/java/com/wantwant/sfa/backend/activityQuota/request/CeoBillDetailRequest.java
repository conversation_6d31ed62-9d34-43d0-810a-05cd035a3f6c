package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/03/01/下午5:18
 */
@Data
@ToString
@ApiModel("合伙人账单明细request")
public class CeoBillDetailRequest extends PageParam {

    @ApiModelProperty("查询组织CODE")
    private String communityCode;
    @ApiModelProperty("大区CODE")
    private String areaCode;
    @ApiModelProperty("分公司CODE")
    private String companyCode;
    @ApiModelProperty("营业所CODE")
    private String departmentCode;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("合伙人姓名/手机号")
    private String employeeKey;
    @ApiModelProperty("开始日期")
    private String startDate;
    @ApiModelProperty("结束日期")
    private String endDate;
    @ApiModelProperty("月份")
    private String month;
    @ApiModelProperty("年份")
    private String year;
    @ApiModelProperty("费用类型")
    private Integer applyType;
    @ApiModelProperty("收入排序:1.升序 2.降序")
    private Integer incomeOrder;
    @ApiModelProperty("使用排序:1.升序 2.降序")
    private Integer usedOrder;
    @ApiModelProperty("剩余排序:1.升序 2.降序")
    private Integer surplusOrder;
    @ApiModelProperty("费用率:1.升序 2.降序")
    private Integer quotaFeeRateOrder;
}
