package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/04/上午10:07
 */
@Data
@ApiModel("合伙人扣款申请")
@ToString
public class CeoPenaltyRequest {
    @ApiModelProperty("扣罚人memberKey")
    private Long memberKey;
    @ApiModelProperty("扣罚人手机号")
    private String mobile;
    @ApiModelProperty("扣罚项目名称")
    private String penaltyItem;
    @ApiModelProperty("扣罚额度")
    private BigDecimal penaltyQuota;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("产品组")
    private String businessGroupCode;
}
