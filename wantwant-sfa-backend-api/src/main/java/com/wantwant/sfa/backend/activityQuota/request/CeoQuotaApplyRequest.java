package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/04/上午11:41
 */
@Data
@ApiModel("旺铺额度申请request")
@ToString
public class CeoQuotaApplyRequest {
    @ApiModelProperty("申请人memberKey")
    @NotNull(message = "缺少申请人memberKey")
    private Long applyMemberKey;

    @ApiModelProperty("费用所属部门CODE")
    @NotBlank(message = "缺少费用承担部门")
    private String departmentCode;

    @ApiModelProperty("接受人memberKey")
    @NotNull(message = "缺少接受人memberKey")
    private Long acceptMemberKey;

    @ApiModelProperty("发放单据唯一编号")
    @NotBlank(message = "缺少发放单据")
    private String code;


    @ApiModelProperty("申请额度")
    @Min(value = 0,message = "额度必须大于0")
    private BigDecimal quota;

    @ApiModelProperty("申请类型")
    @NotNull(message = "缺少申请类型")
    private Integer applyType;

    @ApiModelProperty("备注")
    private String remark;
}
