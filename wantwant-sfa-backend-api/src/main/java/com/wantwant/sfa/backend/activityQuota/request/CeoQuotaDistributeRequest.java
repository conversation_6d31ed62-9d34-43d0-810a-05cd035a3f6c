package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/21/上午10:56
 */
@Data
@ApiModel("旺铺额度发放request")
@ToString
public class CeoQuotaDistributeRequest {
    @ApiModelProperty("额度接受人memberKey")
    @NotNull(message = "缺少memberKey")
    private Long memberKey;

    @ApiModelProperty("分公司名称")
    @NotBlank(message = "缺少分公司名称")
    private String companyName;

    @ApiModelProperty("业务组CODE")
    @NotBlank(message = "缺少业务组CODE")
    private String businessGroupCode;

    @ApiModelProperty("额度")
    @DecimalMin(value = "0",inclusive = false)
    private BigDecimal quota;

    private String cause;

    @ApiModelProperty("售后单号")
    @NotBlank(message = "缺少售后单号")
    private String code;

    @ApiModelProperty("是否来自于旺铺")
    private boolean wpSource = true;

    @ApiModelProperty("备注")
    private String remark;
}
