package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/18/上午8:57
 */
@Data
@ToString
@ApiModel("旺金币费用类型审核通过")
public class CoinsTypeApplyApprovalRequest {

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("申请编号")
    @NotNull(message = "缺少申请编号")
    private Long applyId;

    @ApiModelProperty("费用大类-管理端")
    private Integer classType;

    @ApiModelProperty("费用大类-业务端")
    private Integer businessType;

    @ApiModelProperty("损益一级分类")
    private Integer mainCategoryId;

    @ApiModelProperty("损益二级分类名称")
    private String secondaryCategoryName;


    @ApiModelProperty("费用用途")
    private String expensePurpose;

    @ApiModelProperty("其他费用用途描述")
    private String expensePurposeOther;

    @ApiModelProperty("评论")
    private String comment;
}
