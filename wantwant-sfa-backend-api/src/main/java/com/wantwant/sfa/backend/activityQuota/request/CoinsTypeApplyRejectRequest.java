package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/18/下午12:21
 */
@Data
@ToString
@ApiModel("旺金币费用类型审核驳回")
public class CoinsTypeApplyRejectRequest {

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("申请编号")
    @NotNull(message = "缺少申请编号")
    private Long applyId;

    @ApiModelProperty("评论")
    private String comment;
}
