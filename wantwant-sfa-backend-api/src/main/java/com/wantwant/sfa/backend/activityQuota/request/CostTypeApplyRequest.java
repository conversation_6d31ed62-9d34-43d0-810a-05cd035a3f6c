package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/17/上午10:57
 */
@Data
@ToString
@ApiModel("费用类型申请request")
public class CostTypeApplyRequest {

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("费用类型名称")
    @NotBlank(message = "缺少费用类型名称")
    private String applyTypeName;

    @ApiModelProperty("申请目的")
    private String purpose;

    @ApiModelProperty("规则")
    @NotBlank(message = "缺少规则")
    private String regular;

    @ApiModelProperty("费用场景")
    @NotBlank(message = "费用场景")
    private String scene;

    @ApiModelProperty("费用部门CODE")
    @NotEmpty(message = "缺少费用部门CODE")
    private String deptCode;

    @ApiModelProperty("费用大类-管理端ID")
    private Integer classType;

    @ApiModelProperty("费用大类-业务端ID")
    private Integer businessType;

    @ApiModelProperty("是否总部承担:1.是 0.否")
    @NotNull(message = "缺少是否总部承担选项")
    private Integer zbTolerate;

    @ApiModelProperty("费用用途")
    private String expensePurpose;

    @ApiModelProperty("其他费用用途描述")
    private String expensePurposeOther;

    private String remark;
}
