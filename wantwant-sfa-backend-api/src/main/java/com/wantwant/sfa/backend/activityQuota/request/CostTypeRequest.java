package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/06/上午10:36
 */
@Data
@ApiModel("费用类型request")
@ToString
public class CostTypeRequest extends PageParam {

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("费用申请编号")
    private Integer applyId;

    @ApiModelProperty("费用类型名称")
    private String applyType;

    @ApiModelProperty("费用承担部门")
    private String departmentCode;

    @ApiModelProperty("一级分类ID")
    private Integer categoryMainId;

    @ApiModelProperty("二级分类名称")
    private String secondaryCategoryName;

    @ApiModelProperty("状态：1.财务审核中 2.数据审核中 3.主管审核中 4.财务已驳回 5.数据已驳回 6.主管已驳回 7.已生效 8.待审核")
    private Integer status;


    private boolean needPage = true;

    private List<Integer> roleIds;
}


