package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/12/上午11:20
 */
@Data
@ApiModel("修改费用类型状态")
@ToString
public class CostTypeTriggerStatusRequest {
    @ApiModelProperty("申请ID")
    @NotNull(message = "缺少申请ID")
    private Long applyId;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("部门CODE")
    @NotBlank(message = "缺少部门CODE")
    private String deptCode;

    @ApiModelProperty("状态")
    @NotNull(message = "缺少状态")
    private Integer status;
}
