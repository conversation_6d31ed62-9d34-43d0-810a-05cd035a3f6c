package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/15/下午3:50
 */
@ApiModel("组织信息查询")
@Data
@ToString
public class OrgQuotaRequest extends PageParam {
    @ApiModelProperty("查询月份:格式yyyy-MM")
    private String month;

    @ApiModelProperty("姓名查询")
    private String key;
    @ApiModelProperty(value = "旺旺大区ID")
    private List<String> areaOrganizationIds;
    @ApiModelProperty(value = "大区总监")
    private List<String> vareaOrganizationIds;
    @ApiModelProperty(value = "省区总监")
    private List<String> provinceOrganizationIds;
    @ApiModelProperty(value = "旺旺分公司ID")
    private List<String> companyOrganizationIds;
    @ApiModelProperty(value = "旺旺营业所ID")
    private List<String> departmentIds;
    @ApiModelProperty("职位:1.造旺合伙人 2.区域总监  3.企业合伙人")
    private Integer positionType;

    private Integer businessGroup;

    @ApiModelProperty("页面类型(1.大区.2.分公司.3业务)")
    private Integer type;

    private boolean needPage = true;
}
