package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.activityQuota.vo.PenaltyAttendanceRegularDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:59
 */
@Data
@ToString
@ApiModel("考勤规则修改request")
public class PenaltyAttendanceRegularRequest {

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("金币类型:1.旺金币")
    @NotNull(message = "缺少金币类型")
    private Integer coinsType;

    @ApiModelProperty("操作方式:1.系统 2.人工")
    @NotNull(message = "缺少操作方式")
    private Integer processType;

    @ApiModelProperty("币种类型ID")
    @NotNull(message = "缺少币种类型ID")
    private Integer walletTypeId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("考勤规则明细列表")
    private List<PenaltyAttendanceRegularDetailVo> list;
}
