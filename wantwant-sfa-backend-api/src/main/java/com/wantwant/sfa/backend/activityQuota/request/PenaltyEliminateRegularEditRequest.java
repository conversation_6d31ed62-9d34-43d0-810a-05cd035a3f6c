package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:40
 */
@Data
@ToString
@ApiModel("特批汰换合伙人扣款")
public class PenaltyEliminateRegularEditRequest {
    @ApiModelProperty("扣除金额")
    @Min(value = 1,message = "扣除金额必须大于0")
    private BigDecimal amount;

    @ApiModelProperty("金币类型:1.旺金币")
    @NotNull(message = "缺少金币类型")
    private Integer coinsType;

    @ApiModelProperty("币种类型ID")
    @NotNull(message = "缺少币种类型ID")
    private Integer walletTypeId;

    @ApiModelProperty("操作类型：1.系统 2.人工")
    @NotNull(message = "缺少操作类型")
    private Integer processType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;
}
