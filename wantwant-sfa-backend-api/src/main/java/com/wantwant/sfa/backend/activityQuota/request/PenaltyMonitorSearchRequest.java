package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/07/上午11:16
 */
@Data
@ApiModel("扣罚监控明细查询request")
public class PenaltyMonitorSearchRequest extends PageParam {
    @ApiModelProperty("组织code")
    private List<String> orgCodes;
    @ApiModelProperty(hidden = true)
    private String orgType;
    @ApiModelProperty("岗位类型:1.战区 12.大区 11.省区 2.分公司 10.营业所")
    private Integer positionTypeId;
    @ApiModelProperty("开始日期")
    private String startDate;
    @ApiModelProperty("结束日期")
    private String endDate;
    @ApiModelProperty("业务查询")
    private String employeeKey;
    @ApiModelProperty("是否有实际扣罚:1")
    private Integer hasActualPenalty;
    @ApiModelProperty(hidden = true)
    private boolean needPage = true;
    @ApiModelProperty(hidden = true)
    private Integer businessGroup;
}
