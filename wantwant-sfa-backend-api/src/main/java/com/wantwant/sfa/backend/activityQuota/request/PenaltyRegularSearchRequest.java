package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/05/上午10:02
 */
@Data
@ToString
@ApiModel("扣罚规则查询")
public class PenaltyRegularSearchRequest extends PageParam {

    @ApiModelProperty("扣罚项目名称")
    private String regularName;

    @ApiModelProperty(hidden = true)
    private Integer businessGroup;

    @ApiModelProperty(hidden = true)
    private boolean needPage = true;
}
