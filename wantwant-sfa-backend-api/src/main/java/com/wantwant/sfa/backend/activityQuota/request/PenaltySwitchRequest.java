package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:33
 */
@Data
@ApiModel("关闭扣罚项目")
@ToString
public class PenaltySwitchRequest {
    @ApiModelProperty("规则ID")
    @NotNull(message = "缺少规则ID")
    private Integer regularId;

    @ApiModelProperty("状态:1.启用 0.停用")
    @NotNull(message = "缺少状态")
    private Integer status;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;


}
