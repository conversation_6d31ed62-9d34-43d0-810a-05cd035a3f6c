package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/09/下午2:54
 */
@Data
@ApiModel("个人扣罚")
@ToString
public class PersonalPenaltyRequest {

    @ApiModelProperty("扣罚项目名称")
    @NotBlank(message = "缺少扣罚项目名称")
    private String penaltyItemName;

    @ApiModelProperty("扣罚人员memberKey")
    @NotNull(message = "缺少扣罚人员memberKey")
    private Long memberKey;

    @ApiModelProperty("扣罚业务组Code")
    @NotBlank(message = "缺少扣罚业务组Code")
    private String businessGroupCode;

    @ApiModelProperty("扣罚业务组Code")
    @NotNull(message = "缺少扣罚业务组Code")
    private Integer applyType;

    @ApiModelProperty("组织名称")
    @NotBlank(message = "缺少组织名称")
    private String organizationName;

    @ApiModelProperty("扣罚币种:0-通用币；1-产品组币 2-SPU币")
    @NotNull(message = "缺少扣罚币种")
    private Integer walletType;

    @ApiModelProperty("子项目ID,spu币ID")
    private String subTypeId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("额度")
    @NotNull(message = "缺少扣罚额度")
    private BigDecimal quota;
}
