package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/09/上午10:22
 */
@Data
@ApiModel("收支查询request")
@ToString
public class PurseSearchRequest extends PageParam {
    @ApiModelProperty(value = "区域类型Code area 战区 varea 大区 province 省区 company 分公司 department 营业所")
    private String areaTypeCode;
    @ApiModelProperty("查询开始时间")
    private String startTime;
    @ApiModelProperty("查询结束时间")
    private String endTime;
    @ApiModelProperty("姓名")
    private String key;
    @ApiModelProperty(value = "旺旺大区ID")
    private List<String> areaOrganizationIds;
    @ApiModelProperty(value = "大区总监")
    private List<String> vareaOrganizationIds;
    @ApiModelProperty(value = "省区总监")
    private List<String> provinceOrganizationIds;
    @ApiModelProperty(value = "旺旺分公司ID")
    private List<String> companyOrganizationIds;
    @ApiModelProperty(value = "旺旺营业所ID")
    private List<String> departmentIds;

    @ApiModelProperty("操作类型: 1.本月增加 2.本月使用 3.上月累积 4.本月回收 5.上级回收")
    private Integer type;
    @ApiModelProperty("费用类型")
    private Integer applyType;
    @ApiModelProperty("岗位类型: 1.全职业务合伙人 2.兼职业务合伙人 3.企业合伙人 4.区域总监 ")
    private Integer ceoType;

    private Integer businessGroup;

    @ApiModelProperty("实际收入")
    private String revenue;

    @ApiModelProperty(value = "费用币种 0 通用币 1 组别币 2 SPU币")
    private String walletTypeId;

    @ApiModelProperty(value = "币种子类名称")
    private String subTypeName;

    @ApiModelProperty(value = "币种子类",hidden = true)
    private String subTypeId;

    private boolean needPage;
}
