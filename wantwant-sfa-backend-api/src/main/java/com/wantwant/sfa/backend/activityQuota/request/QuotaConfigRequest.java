package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:活动额度配置用Request。
 * @Auther: zhengxu
 * @Date: 2021/12/01/上午9:27
 */
@Data
@ApiModel("活动额度配置用Request")
@ToString
public class QuotaConfigRequest {
    @ApiModelProperty("操作人")
    @NotBlank
    private String person;

    @ApiModelProperty("组织ID")
    @NotBlank
    private String organizationId;

    @ApiModelProperty("配置额度")
    private BigDecimal quota;

    @ApiModelProperty("活动类型")
    @NotNull
    private Integer activityType;

    @ApiModelProperty("申请类型")
    private Integer applyType;

    @ApiModelProperty("活动日期,格式为yyyy-MM")
    @NotBlank
    private String activityTime;

    @ApiModelProperty("增加额度")
    private BigDecimal addQuota;

    @ApiModelProperty("回收额度")
    private BigDecimal subQuota;

    @ApiModelProperty("部门CODE")
    private String deptCode;

    @ApiModelProperty("备注")
    private String remark;
}
