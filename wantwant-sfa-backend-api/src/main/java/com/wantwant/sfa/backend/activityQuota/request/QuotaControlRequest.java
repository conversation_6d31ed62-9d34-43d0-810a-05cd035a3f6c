package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/12/上午9:45
 */
@ApiModel("额度管控查询")
@Data
@ToString
public class QuotaControlRequest {

    @ApiModelProperty("分公司CODE")
    @NotBlank(message = "缺少分公司CODE")
    private String companyCode;

    @ApiModelProperty("发放业务员手机号")
    @NotBlank(message = "发放业务员手机号")
    private String employeeMobile;
}
