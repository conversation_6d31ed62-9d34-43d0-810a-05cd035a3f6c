package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/24/下午12:13
 */
@Data
@ToString
@ApiModel("额度发放请求")
public class QuotaDistributeRequest {
    @ApiModelProperty("操作人")
    @NotBlank
    private String person;

    @ApiModelProperty("活动类型（0.市场费用）")
    @NotNull
    private Integer activityType;

    @ApiModelProperty("月份")
    @NotBlank
    private String month;

    @ApiModelProperty("分公司CODE")
    @NotBlank
    private String companyCode;

    @ApiModelProperty("业务员手机号")
    @NotBlank
    private String employeeMobile;

    @ApiModelProperty("额度")
    @NotNull
    private BigDecimal quota;

    @ApiModelProperty("备注")
    @NotBlank
    private String remark;
}
