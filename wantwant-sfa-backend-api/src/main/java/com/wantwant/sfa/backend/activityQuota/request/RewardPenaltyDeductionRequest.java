package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.activityQuota.api.OrgPenaltyRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel("奖惩扣罚request")
public class RewardPenaltyDeductionRequest extends OrgPenaltyRequest {
    @ApiModelProperty("奖惩扣罚ID")
    private Long rewardPenaltyId;
}
