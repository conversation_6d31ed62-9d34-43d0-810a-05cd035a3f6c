package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("奖惩扣罚操作request")
public class RewardPenaltyOperateRequest {

    @ApiModelProperty("操作人")
    private String person;

    @ApiModelProperty("奖惩扣罚List")
    private List<RewardPenaltyDeductionRequest> rewardPenaltyDeductionRequestList;
}
