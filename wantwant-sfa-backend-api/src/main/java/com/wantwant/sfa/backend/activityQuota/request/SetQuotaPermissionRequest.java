package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/17/下午6:31
 */
@Data
@ApiModel("设置造旺币权限")
public class SetQuotaPermissionRequest {

    @ApiModelProperty("组织ID")
    @NotBlank(message = "缺少组织")
    private String organizationId;
    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;
    @ApiModelProperty("状态:1.启用 0.停用")
    @NotNull(message = "缺少状态")
    private Integer status;

}
