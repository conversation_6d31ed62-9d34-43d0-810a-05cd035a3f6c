package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:56
 */
@ApiModel("修改规则")
@Data
@ToString
public class UPenaltyRequest extends CPenaltyRequest {

    @ApiModelProperty("规则ID")
    @NotNull(message = "缺少规则ID")
    private Integer regularId;

}
