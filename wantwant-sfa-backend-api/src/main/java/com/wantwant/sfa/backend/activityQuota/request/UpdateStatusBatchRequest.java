package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/29/下午7:30
 */
@Data
@ApiModel("扣罚操作批处理request")
@ToString
public class UpdateStatusBatchRequest {

    @ApiModelProperty("ids")
    private List<Integer> ids;
    @ApiModelProperty("person")
    private String person;
    @ApiModelProperty("状态:1.进行中 2.撤销 3.完成 4.取消")
    @NotNull(message = "缺少状态")
    private Integer status;
}
