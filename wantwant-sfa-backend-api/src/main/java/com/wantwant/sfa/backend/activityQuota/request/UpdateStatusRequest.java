package com.wantwant.sfa.backend.activityQuota.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/17/上午10:31
 */
@Data
@ApiModel("扣罚操作request")
public class UpdateStatusRequest {
    @ApiModelProperty("id")
    @NotNull(message = "缺少单据ID")
    private Integer id;
    @ApiModelProperty("person")
    @NotBlank(message = "缺少操作人")
    private String person;
    @ApiModelProperty("状态:1.进行中 2.撤销 3.完成 4.取消")
    @NotNull(message = "缺少状态")
    private Integer status;
}
