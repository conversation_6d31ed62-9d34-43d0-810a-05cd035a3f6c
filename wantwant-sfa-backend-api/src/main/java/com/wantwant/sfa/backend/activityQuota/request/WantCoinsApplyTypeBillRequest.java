package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/15/上午9:34
 */
@Data
@ApiModel("旺金币费用类型账单明细查询")
@ToString
public class WantCoinsApplyTypeBillRequest extends PageParam {

    @ApiModelProperty("年")
    @NotBlank(message = "缺少年")
    private String year;

    @ApiModelProperty("月")
    @NotBlank(message = "缺少月")
    private String month;

    @ApiModelProperty("组织CODE")
    @NotBlank(message = "缺少组织CODE")
    private String organizationId;

    @ApiModelProperty("查询组织类型 1:总部 2.大区 3.分公司")
    @NotNull(message = "缺少查询组织类型")
    private Integer searchOrgType;

    @ApiModelProperty("部门CODE")
    private String deptCode;

    @ApiModelProperty("费用大类")
    private String classTypeName;

    @ApiModelProperty("费用类型")
    private Integer applyType;



    @ApiModelProperty("收入排序:1.升序 2.降序")
    private Integer incomeOrder;
    @ApiModelProperty("使用排序:1.升序 2.降序")
    private Integer usedOrder;
    @ApiModelProperty("剩余排序:1.升序 2.降序")
    private Integer surplusOrder;


    @ApiModelProperty(value = "组织类型", required = false,hidden=true)
    private String organizationType;
    @ApiModelProperty(value = "父组织", required = false,hidden=true)
    private String parentOrganizationId;
    @ApiModelProperty(value = "大区codes", required = false,hidden=true)
    private List<String> areaCodes;
    @ApiModelProperty(value = "分公司codes", required = false,hidden=true)
    private List<String> companyCodes;

}
