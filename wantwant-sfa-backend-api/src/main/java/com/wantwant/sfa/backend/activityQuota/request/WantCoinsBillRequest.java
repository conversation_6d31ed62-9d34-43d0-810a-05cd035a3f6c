package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/18/下午4:48
 */
@Data
@ApiModel("旺金币账单查询request")
@ToString
public class WantCoinsBillRequest extends PageParam {

    @ApiModelProperty("年")
    @NotBlank(message = "缺少年")
    private String year;

    @ApiModelProperty("月")
    @NotBlank(message = "缺少月")
    private String month;

    @ApiModelProperty("组织CODE")
    @NotBlank(message = "缺少组织CODE")
    private String organizationId;

    @ApiModelProperty("合伙人查询KEY")
    private String employeeKey;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("部门CODE")
    private String deptCode;

    @ApiModelProperty("支出方")
    private String expenditure;

    @ApiModelProperty("收入方")
    private String revenue;

    @ApiModelProperty("费用类型")
    private List<Integer> applyType;

    @ApiModelProperty("费用大类")
    private String classTypeName;

    @ApiModelProperty("操作类型:1.收入 2.支出")
    private Integer type;

    @ApiModelProperty("排序字段：date-时间,quota-金额,expenditure-支出方,revenue-收入方")
    private String orderType;

    @ApiModelProperty("排序:ASC升序 DESC降序")
    private String sort;
}
