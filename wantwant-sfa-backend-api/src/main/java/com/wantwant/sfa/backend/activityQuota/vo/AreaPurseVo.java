package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/09/上午10:17
 */
@Data
@ApiModel("大区收支")
public class AreaPurseVo {
    @ApiModelProperty(value = "战区")
    @Excel(name = "战区")
    private String area;
    @ApiModelProperty(value = "大区")
    @Excel(name = "大区")
    private String virtualAreaName;

    @ApiModelProperty(value = "省区")
    @Excel(name = "省区")
    private String provinceName;

    @ApiModelProperty(value = "分公司")
    @Excel(name = "分公司")
    private String company;

    @ApiModelProperty(value = "营业所")
    @Excel(name = "营业所")
    private String departmentName;

    @Excel(name = "负责人")
    @ApiModelProperty("负责人")
    private String managerName;
    @Excel(name = "操作类型")
    @ApiModelProperty("操作类型")
    private String type;
    @Excel(name = "费用类型")
    @ApiModelProperty("费用类型")
    private String applyType;
    @ApiModelProperty(value = "费用币种 0 通用币 1 组别币 2 SPU币")
    @Excel(name = "费用币种")
    private String walletTypeName;
    @ApiModelProperty(value = "币种子类")
    private String subTypeId;
    @ApiModelProperty(value = "币种子类名称")
    @Excel(name = "币种子类名称")
    private String subTypeName;
    @Excel(name = "金额")
    @ApiModelProperty("金额")
    private String quota;

    @Excel(name = "源支出方")
    @ApiModelProperty("源支出方")
    private String expenditure;
    @Excel(name = "实际收入方")
    @ApiModelProperty("实际收入方")
    private String revenue;
    @Excel(name = "操作人")
    @ApiModelProperty("操作人")
    private String processUserName;
    @Excel(name = "操作时间")
    @ApiModelProperty("操作时间")
    private String processTime;
    @Excel(name = "账户余额")
    @ApiModelProperty("账户余额")
    private String curSurplus;
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否可回收")
    private boolean canRevert;
    @ApiModelProperty("回收记录ID")
    private Long logId;
    @ApiModelProperty("memberKey")
    private Long memberKey;
    @ApiModelProperty("回收金额")
    private BigDecimal revertQuota;
}
