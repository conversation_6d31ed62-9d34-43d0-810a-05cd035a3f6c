package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: GuRongHua
 * @Date: 2022/09/18/下午2:34
 */
@Data
@ApiModel("营业所新额度总览Vo")
public class BranchQuotaCollectionVo {
    @ApiModelProperty("月份")
    @Excel(name="月份")
    private String month;
    @ApiModelProperty("大区名称")
    @Excel(name="大区名称")
    private String areaName;
    @ApiModelProperty("分公司名称")
    @Excel(name = "分公司名称")
    private String companyName;
    @ApiModelProperty("业务姓名")
    @Excel(name="业务姓名")
    private String employeeName;
    @ApiModelProperty("职位")
    @Excel(name="职位")
    private String position;
    @ApiModelProperty(value = "额度明细")
    @Excel(name="额度明细")
    private List<QuotaDetailVo> quotaDetailVo;

    @ApiModelProperty("上级回收额度")
    @Excel(name="上级回收额度")
    private BigDecimal superiorRecoveryQuota;

    @ApiModelProperty("累计总额度")
    @Excel(name="累计总额度")
    private BigDecimal totalQuota;
    @ApiModelProperty("累计已使用额度")
    @Excel(name="累计已使用额度")
    private BigDecimal totalUsedQuota;
    @ApiModelProperty("当前剩余额度")
    @Excel(name="当前剩余额度")
    private BigDecimal totalRemainQuota;
}
