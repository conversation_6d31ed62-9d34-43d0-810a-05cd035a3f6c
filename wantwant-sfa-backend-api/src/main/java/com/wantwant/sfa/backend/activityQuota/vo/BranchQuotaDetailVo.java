package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:分公司额度信息。
 * @Auther: zhengxu
 * @Date: 2021/12/24/上午11:02
 */
@Data
@ApiModel("分公司额度信息")
public class BranchQuotaDetailVo {
    @ApiModelProperty("大区名称")
    private String areaName;
    @ApiModelProperty("大区CODE")
    private String areaCode;
    @ApiModelProperty("分公司名称")
    private String companyName;
    @ApiModelProperty("分公司CODE")
    private String companyCode;
    @ApiModelProperty("营业所名称")
    private String branchName;
    @ApiModelProperty("营业所CODE")
    private String branchCode;
    @ApiModelProperty("小标市场名称")
    private String smallMarketName;
    @ApiModelProperty("申请人名称")
    private String applyUserName;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("职位")
    private String postition;
    @ApiModelProperty("在岗状态")
    private String postionType;
    @ApiModelProperty("申请额度")
    private String applyQuota;
    @ApiModelProperty("批准额度")
    private String approveQuota;
    @ApiModelProperty("回收额度")
    private String retriveQuota;
}
