package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/03/01/下午5:12
 */
@Data
@ApiModel("合伙人账单明细")
public class CeoBillDetailVo {
    @ApiModelProperty("大区")
    @Excel(name = "大区")
    private String areaName;

    @ApiModelProperty("分公司")
    @Excel(name = "分公司")
    private String companyName;

    @ApiModelProperty("营业所")
    @Excel(name = "营业所")
    private String departmentName;

    @ApiModelProperty("合伙人姓名")
    @Excel(name = "合伙人姓名")
    private String employeeName;


    @ApiModelProperty("人员状态")
    @Excel(name = "人员状态")
    private String employeeStatus;

    @ApiModelProperty("手机号")
    @Excel(name = "手机号")
    private String mobile;

    @ApiModelProperty("收入")
    @Excel(name = "收入")
    private String income;

    @ApiModelProperty("使用")
    @Excel(name = "使用")
    private String used;

    @ApiModelProperty("剩余")
    @Excel(name = "剩余")
    private String surplus;

    @ApiModelProperty("费用率")
    @Excel(name = "费用率")
    private String quotaFeeRate;
}
