package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/27/下午6:43
 */
@Data
@ApiModel("合伙人账单Vo")
public class CeoBillVo {

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("起止日期")
    private String period;

    @ApiModelProperty("旺金币费用率")
    private String quotaFeeRate;

    @ApiModelProperty("收入")
    private BigDecimal partnerQuotaIncome;

    @ApiModelProperty("使用")
    private BigDecimal partnerQuotaUse;

    @ApiModelProperty("剩余")
    private BigDecimal partnerQuotaSurplus;

}
