package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/09/上午10:29
 */
@Data
@ApiModel("业务收支")
public class CeoPurseVo {

    @Excel(name = "大区")
    @ApiModelProperty("大区")
    private String areaName;
    @Excel(name = "分公司")
    @ApiModelProperty("分公司")
    private String companyName;

    @Excel(name = "业务姓名")
    @ApiModelProperty("业务姓名")
    private String ceoName;

    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String mobile;

    @Excel(name = "职位")
    @ApiModelProperty("职位")
    private String position;

    @Excel(name = "操作类型")
    @ApiModelProperty("操作类型")
    private String type;
    @Excel(name = "费用类型")
    @ApiModelProperty("费用类型")
    private String applyType;
    @Excel(name = "金额")
    @ApiModelProperty("金额")
    private String quota;

    @Excel(name = "源支出方")
    @ApiModelProperty("源支出方")
    private String expenditure;
    @Excel(name = "实际收入方")
    @ApiModelProperty("实际收入方")
    private String revenue;

    @Excel(name = "操作人")
    @ApiModelProperty("操作人")
    private String processUserName;
    @Excel(name = "操作时间")
    @ApiModelProperty("操作时间")
    private String processTime;
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;
}
