package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/15/下午5:06
 */
@Data
@ApiModel("业务别VO")
public class CeoQuotaVo {

    @ApiModelProperty("月份")
    @Excel(name="月份")
    private String month;

    @ApiModelProperty("大区名称")
    @Excel(name="大区名称")
    private String areaName;

    @ApiModelProperty("分公司名称")
    @Excel(name="分公司名称")
    private String companyName;

    @ApiModelProperty("员工名称")
    @Excel(name="员工名称")
    private String employeeName;

    @ApiModelProperty("岗位")
    @Excel(name="岗位")
    private String position;

    @ApiModelProperty("市场费用 本月新增")
    @Excel(name="市场费用 本月新增")
    private BigDecimal marketingThisMonthNewQuota;

    @ApiModelProperty("文宣品费用 本月新增")
    @Excel(name="文宣品费用 本月新增")
    private BigDecimal publishingThisMonthNewQuota;

    @ApiModelProperty("试吃 本月新增")
    @Excel(name="试吃 本月新增")
    private BigDecimal foretasteThisMonthNewQuota;

    @ApiModelProperty("特陈奖 本月新增")
    @Excel(name="特陈奖 本月新增")
    private BigDecimal displayThisMonthNewQuota;

    @ApiModelProperty("主推奖 本月新增")
    @Excel(name="主推奖 本月新增")
    private BigDecimal popularizeThisMonthNewQuota;

    @ApiModelProperty("排名奖 本月新增")
    @Excel(name="排名奖 本月新增")
    private BigDecimal rankingThisMonthNewQuota;

    @ApiModelProperty("业务补贴 本月新增")
    @Excel(name="业务补贴 本月新增")
    private BigDecimal subsidiesThisMonthNewQuota;

    @ApiModelProperty("售后 本月新增")
    @Excel(name="售后 本月新增")
    private BigDecimal aftersaleThisMonthNewQuota;

    @ApiModelProperty("上级回收额度")
    @Excel(name="上级回收额度")
    private BigDecimal superiorRecoveryQuota;

    @ApiModelProperty("累计额度")
    @Excel(name="累计额度")
    private BigDecimal totalQuota;

    @ApiModelProperty("累计已使用额度")
    @Excel(name="累计已使用额度")
    private BigDecimal totalUsedQuota;

    @ApiModelProperty("累计剩余额度")
    @Excel(name="累计剩余额度")
    private BigDecimal totalRemainQuota;
}
