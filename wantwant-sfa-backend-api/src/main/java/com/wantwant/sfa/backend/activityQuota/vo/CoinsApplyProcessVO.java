package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/18/下午5:03
 */
@Data
@ApiModel("旺金币费用类型申请流程VO")
public class CoinsApplyProcessVO {
    @ApiModelProperty("流程名称")
    private String processName;
    @ApiModelProperty("处理结果")
    private String processResult;
    @ApiModelProperty("处理人姓名")
    private String processUserName;
    @ApiModelProperty("执行时间")
    private String executeTime;
    @ApiModelProperty("意见")
    private String comment;
}
