package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/17/下午3:51
 */
@Data
@ApiModel("旺金币类型申请明细")
public class CoinsTypeApplyInfoVo {

    @ApiModelProperty("申请人")
    private String applyUser;
    @ApiModelProperty("申请时间")
    private String applyTime;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("费用类型")
    private String applyType;
    @ApiModelProperty("费用部门")
    private String deptName;
    @ApiModelProperty("申请目的")
    private String purpose;
    @ApiModelProperty("费用场景")
    private String scene;
    @ApiModelProperty("费用规则")
    private String regular;
    @ApiModelProperty("费用大类-管理端")
    private Integer classType;
    @ApiModelProperty("费用大类-管理端名称")
    private String classTypeName;
    @ApiModelProperty("费用大类-业务端")
    private Integer businessType;
    @ApiModelProperty("当前流程类型:10.财务审核 20.数据审核 30.主管审核")
    private Integer processStep;
    @ApiModelProperty("费用大类-业务端名称")
    private String businessTypeName;
    @ApiModelProperty("损益一级分类")
    private Integer mainCategoryId;
    @ApiModelProperty("损益一级分类名称")
    private String mainCategoryName;
    @ApiModelProperty("损益二级分类")
    private String secondaryCategoryName;
    @ApiModelProperty("费用用途")
    private String expensePurpose;
    @ApiModelProperty("其他费用用途描述")
    private String expensePurposeOther;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否总部承担(1.是 0.否)")
    private Integer zbTolerate;
    @ApiModelProperty("是否可修改")
    private boolean canModify;
    @ApiModelProperty("是否可操作")
    private boolean canProcess;

}
