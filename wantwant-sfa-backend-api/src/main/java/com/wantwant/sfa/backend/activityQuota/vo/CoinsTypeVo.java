package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/18/下午3:59
 */
@Data
@ApiModel
public class CoinsTypeVo {

    @ApiModelProperty("费用类型")
    private String applyType;
    @ApiModelProperty("费用大类-管理端名称")
    private String classTypeName;
    @ApiModelProperty("费用大类-业务端名称")
    private String businessTypeName;
    @ApiModelProperty("费用大类-管理端")
    private Integer classType;
    @ApiModelProperty("费用大类-业务端")
    private Integer businessType;
    @ApiModelProperty("一级分类ID")
    private Integer mainCategoryId;
    @ApiModelProperty("一级分类名称")
    private String mainCategoryName;
    @ApiModelProperty("二级分类ID")
    private Integer secondaryCategoryId;
    @ApiModelProperty("二级分类名称")
    private String secondaryCategoryName;


}
