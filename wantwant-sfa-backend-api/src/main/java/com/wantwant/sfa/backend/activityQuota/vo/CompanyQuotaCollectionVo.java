package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: GuRongHua
 * @Date: 2022/09/18/下午2:34
 */
@Data
@ApiModel("分公司新额度总览Vo")
public class CompanyQuotaCollectionVo {
    @ApiModelProperty("月份")
    @Excel(name="月份")
    private String month;
    @ApiModelProperty("大区")
    @Excel(name="大区名称")
    private String areaName;
    @ApiModelProperty("分公司")
    @Excel(name = "分公司名称")
    private String companyName;
    @ApiModelProperty("负责人")
    @Excel(name="负责人")
    private String managerName;

    @ApiModelProperty(value = "额度明细")
    @Excel(name="额度明细")
    private List<QuotaDetailVo> quotaDetailVo;

    @ApiModelProperty("累计总额度(业务别-累计额度)")
    @Excel(name="累计总额度")
    private BigDecimal totalQuota;
    @ApiModelProperty("累计剩余额度(业务别-累计剩余额度)")
    @Excel(name="累计剩余额度")
    private BigDecimal totalRemainQuota;

}
