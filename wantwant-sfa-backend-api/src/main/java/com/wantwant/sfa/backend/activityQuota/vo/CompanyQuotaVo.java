package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/15/下午3:54
 */
@Data
@ApiModel("分公司额度总览VO")
public class CompanyQuotaVo {
    @ApiModelProperty("月份")
    @Excel(name="月份",orderNum = "1")
    private String month;
    @ApiModelProperty("大区名称")
    @Excel(name="大区名称",orderNum = "2")
    private String areaName;
    @ApiModelProperty("分公司名称")
    @Excel(name = "分公司名称",orderNum = "3")
    private String companyName;
    @ApiModelProperty("负责人")
    @Excel(name="负责人",orderNum = "4")
    private String managerName;

    @ApiModelProperty("市场费用 上月遗留额度")
    @Excel(name="市场费用 上月遗留额度",orderNum = "17")
    private BigDecimal marketingLastMonthRemainingQuota;
    @ApiModelProperty("市场费用 本月新增额度")
    @Excel(name="市场费用 本月新增额度",orderNum = "18")
    private BigDecimal marketingThisMonthNewQuota;
    @ApiModelProperty("市场费用 本月分配额度")
    @Excel(name="市场费用 本月分配额度",orderNum = "19")
    private BigDecimal marketingThisMonthDistributionQuota;
    @ApiModelProperty("市场费用 上级回收额度")
    private BigDecimal marketingThisMonthRecycleQuota;
    @ApiModelProperty("市场费用 上级回收额度")
    private BigDecimal marketingSuperiorRecoverQuota;
    @ApiModelProperty("市场费用剩余额度")
    @Excel(name="市场费用剩余额度",orderNum = "20")
    private BigDecimal marketingThisMonthRemainingQuota;

    @ApiModelProperty("文宣品 上月遗留额度")
    @Excel(name="文宣品 上月遗留额度",orderNum = "25")
    private BigDecimal publishingLastMonthRemainingQuota;
    @ApiModelProperty("文宣品 本月新增额度")
    @Excel(name="文宣品 本月新增额度",orderNum = "26")
    private BigDecimal publishingThisMonthNewQuota;
    @ApiModelProperty("文宣品 本月分配额度")
    @Excel(name="文宣品 本月分配额度",orderNum = "27")
    private BigDecimal publishingThisMonthDistributionQuota;
    @ApiModelProperty("文宣品 本月回收额度")
    private BigDecimal publishingThisMonthRecycleQuota;
    @ApiModelProperty("文宣品 上级回收额度")
    private BigDecimal publishingSuperiorRecoverQuota;
    @ApiModelProperty("文宣品 剩余额度")
    @Excel(name="文宣品 剩余额度",orderNum = "28")
    private BigDecimal publishingThisMonthRemainingQuota;

    @ApiModelProperty("特陈奖 上月遗留额度")
    @Excel(name="特陈奖 上月遗留额度",orderNum = "9")
    private BigDecimal displayLastMonthRemainingQuota;
    @ApiModelProperty("特陈奖 本月新增额度")
    @Excel(name="特陈奖 本月新增额度",orderNum = "10")
    private BigDecimal displayThisMonthNewQuota;
    @ApiModelProperty("特陈奖 本月分配额度")
    @Excel(name="特陈奖 本月分配额度",orderNum = "11")
    private BigDecimal displayThisMonthDistributionQuota;
    @ApiModelProperty("特陈奖 本月回收额度")
    private BigDecimal displayThisMonthRecycleQuota;
    @ApiModelProperty("特陈奖 上级回收额度")
    private BigDecimal displaySuperiorRecoverQuota;
    @ApiModelProperty("特陈奖 剩余额度")
    @Excel(name="特陈奖 剩余额度",orderNum = "12")
    private BigDecimal displayThisMonthRemainingQuota;

    @ApiModelProperty("试吃 上月遗留额度")
    @Excel(name="试吃 上月遗留额度",orderNum = "13")
    private BigDecimal foretasteLastMonthRemainingQuota;
    @ApiModelProperty("试吃 本月新增额度")
    @Excel(name="试吃 本月新增额度",orderNum = "14")
    private BigDecimal foretasteThisMonthNewQuota;
    @ApiModelProperty("试吃 本月分配额度")
    @Excel(name="试吃 本月分配额度",orderNum = "15")
    private BigDecimal foretasteThisMonthDistributionQuota;
    @ApiModelProperty("试吃 本月回收额度")
    private BigDecimal foretasteThisMonthRecycleQuota;
    @ApiModelProperty("试吃 上级回收额度")
    private BigDecimal foretasteSuperiorRecoverQuota;
    @ApiModelProperty("试吃 剩余额度")
    @Excel(name="试吃 剩余额度",orderNum = "16")
    private BigDecimal foretasteThisMonthRemainingQuota;

    @ApiModelProperty("主推奖 上月遗留额度")
    @Excel(name="主推奖 上月遗留额度",orderNum = "21")
    private BigDecimal popularizeLastMonthRemainingQuota;
    @ApiModelProperty("主推奖 本月新增额度")
    @Excel(name="主推奖 本月新增额度",orderNum = "22")
    private BigDecimal popularizeThisMonthNewQuota;
    @ApiModelProperty("主推奖 本月分配额度")
    @Excel(name="主推奖 本月分配额度",orderNum = "23")
    private BigDecimal popularizeThisMonthDistributionQuota;
    @ApiModelProperty("主推奖 本月回收额度")
    private BigDecimal popularizeThisMonthRecycleQuota;
    @ApiModelProperty("主推奖 上级回收额度")
    private BigDecimal popularizeSuperiorRecoverQuota;
    @ApiModelProperty("主推奖 剩余额度")
    @Excel(name="主推奖 剩余额度",orderNum = "24")
    private BigDecimal popularizeThisMonthRemainingQuota;

    @ApiModelProperty("排名奖 上月遗留额度")
    @Excel(name="排名奖 上月遗留额度",orderNum = "29")
    private BigDecimal rankingLastMonthRemainingQuota;
    @ApiModelProperty("排名奖 本月新增额度")
    @Excel(name="排名奖 本月新增额度",orderNum = "30")
    private BigDecimal rankingThisMonthNewQuota;
    @ApiModelProperty("排名奖 本月分配额度")
    @Excel(name="排名奖 本月分配额度",orderNum = "31")
    private BigDecimal rankingThisMonthDistributionQuota;
    @ApiModelProperty("排名奖 本月回收额度")
    private BigDecimal rankingThisMonthRecycleQuota;
    @ApiModelProperty("排名奖 上级回收额度")
    private BigDecimal rankingSuperiorRecoverQuota;
    @ApiModelProperty("排名奖 剩余额度")
    @Excel(name="排名奖 剩余额度",orderNum = "32")
    private BigDecimal rankingThisMonthRemainingQuota;

    @ApiModelProperty("业务补贴 上月遗留额度")
    @Excel(name="业务补贴 上月遗留额度",orderNum = "33")
    private BigDecimal subsidiesLastMonthRemainingQuota;
    @ApiModelProperty("业务补贴 本月新增额度")
    @Excel(name="业务补贴 本月新增额度",orderNum = "34")
    private BigDecimal subsidiesThisMonthNewQuota;
    @ApiModelProperty("业务补贴 本月分配额度")
    @Excel(name="业务补贴 本月分配额度",orderNum = "35")
    private BigDecimal subsidiesThisMonthDistributionQuota;
    @ApiModelProperty("业务补贴 本月回收额度")
    private BigDecimal subsidiesThisMonthRecycleQuota;
    @ApiModelProperty("业务补贴 上级回收额度")
    private BigDecimal subsidiesSuperiorRecoverQuota;
    @ApiModelProperty("业务补贴 剩余额度")
    @Excel(name="业务补贴 剩余额度",orderNum = "36")
    private BigDecimal subsidiesThisMonthRemainingQuota;


    @ApiModelProperty("售后 上月遗留额度")
    @Excel(name="售后 上月遗留额度",orderNum = "5")
    private BigDecimal aftersaleLastMonthRemainingQuota;
    @ApiModelProperty("售后 本月新增额度")
    @Excel(name="售后 本月新增额度",orderNum = "6")
    private BigDecimal aftersaleThisMonthNewQuota;
    @ApiModelProperty("售后 本月分配额度")
    @Excel(name="售后 本月分配额度",orderNum = "7")
    private BigDecimal aftersaleThisMonthDistributionQuota;
    @ApiModelProperty("售后 本月回收额度")
    private BigDecimal aftersaleThisMonthRecycleQuota;
    @ApiModelProperty("售后 上级回收额度")
    private BigDecimal aftersaleSuperiorRecoverQuota;
    @ApiModelProperty("售后 剩余额度")
    @Excel(name="售后 剩余额度",orderNum = "8")
    private BigDecimal aftersaleThisMonthRemainingQuota;

    @ApiModelProperty("累计总额度")
    @Excel(name="累计总额度",orderNum = "37")
    private BigDecimal totalQuota;
    @ApiModelProperty("累计剩余额度")
    @Excel(name="累计剩余额度",orderNum = "38")
    private BigDecimal totalRemainQuota;

}
