package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/06/上午11:57
 */
@ApiModel("费用类型分类VO")
@Data
public class CostCategoryVo {
    @ApiModelProperty("分类ID")
    private Integer categoryId;
    @ApiModelProperty("分类名称")
    private String categoryName;
}
