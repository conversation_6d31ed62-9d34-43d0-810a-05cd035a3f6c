package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/06/上午10:29
 */
@Data
@ApiModel("费用类型VO")
public class CostTypeVo {

    @ApiModelProperty("费用类型申请ID")
    @Excel(name = "编号")
    private Long applyId;

    @ApiModelProperty("费用类型")
    @Excel(name = "费用类型")
    private String applyType;

    @ApiModelProperty("费用类型ID")
    @Excel(name = "费用类型ID")
    private String applyTypeId;

    @ApiModelProperty("费用大类-管理端")
    @Excel(name = "费用大类-管理端")
    private String classType;

    @ApiModelProperty("费用大类-业务端")
    @Excel(name = "费用大类-业务端")
    private String businessType;

    @ApiModelProperty("费用承担部门")
    @Excel(name = "费用承担部门")
    private String departmentName;

    @ApiModelProperty("所属部门CODE")
    private String deptCode;

    @ApiModelProperty("损益一级分类")
    @Excel(name = "损益一级分类")
    private String categoryMain;

    @ApiModelProperty("损益二级分类")
    @Excel(name = "损益二级分类")
    private String categorySecondary;

    @ApiModelProperty("费用场景")
    @Excel(name = "费用场景")
    private String scene;

    @ApiModelProperty("费用用途")
    @Excel(name = "费用用途")
    private String expensePurpose;

    @ApiModelProperty("是否总部承担")
    @Excel(name = "是否总部承担",replace={"-_null","是_1","否_0"})
    private Integer zbTolerate;

    @ApiModelProperty("创建人")
    @Excel(name = "创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @Excel(name = "创建时间")
    private String createTime;


    @ApiModelProperty("审批状态")
    @Excel(name = "审批状态")
    private String status;

    @ApiModelProperty("开启状态:1.开启 0.停用")
    @Excel(name = "开启状态",replace={"-_null","开启_1","停用_0"})
    private Integer deptStatus;

    @ApiModelProperty("是否可操作")
    private boolean canProcess;

    @ApiModelProperty("是否可操作")
    private boolean triggerStatus;
}
