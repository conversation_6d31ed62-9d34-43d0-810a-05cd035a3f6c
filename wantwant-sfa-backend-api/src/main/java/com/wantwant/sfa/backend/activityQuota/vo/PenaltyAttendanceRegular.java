package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:29
 */
@Data
@ApiModel("考勤异常规则信息")
public class PenaltyAttendanceRegular {
    @ApiModelProperty("考勤异常规则明细")
    private List<PenaltyAttendanceRegularDetailVo> list;
    @ApiModelProperty("项目名称")
    private String regularName;
    @ApiModelProperty("扣罚金币类型")
    private Integer coinsType;
    @ApiModelProperty("扣罚类型ID")
    private Integer walletTypeId;
    @ApiModelProperty("扣罚类型")
    private String walletType;
    @ApiModelProperty("操作方式:1.系统 2.人工")
    private Integer processType;
    @ApiModelProperty("备注")
    private String remark;
}
