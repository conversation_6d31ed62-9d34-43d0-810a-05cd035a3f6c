package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:25
 */
@ApiModel("考勤异常扣款规则明细")
@Data
public class PenaltyAttendanceRegularDetailVo {
    @ApiModelProperty("规则ID")
    private Integer id;
    @ApiModelProperty("左侧限定")
    private BigDecimal limitLeft;
    @ApiModelProperty("左侧限定符号(1.小于 2.小于等于 3.等于 4.大于 5.大于等于)")
    private Integer limitLeftSignal;
    @ApiModelProperty("右侧限定")
    private BigDecimal limitRight;
    @ApiModelProperty("右侧限定符号(1.小于 2.小于等于 3.等于 4.大于 5.大于等于)")
    private Integer limitRightSignal;
    @ApiModelProperty("扣除金额")
    private BigDecimal amount;
}
