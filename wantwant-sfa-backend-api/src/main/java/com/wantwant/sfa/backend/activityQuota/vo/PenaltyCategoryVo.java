package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/05/上午8:21
 */
@Data
@ApiModel("扣罚类型VO")
public class PenaltyCategoryVo {
    @ApiModelProperty("类型ID")
    private Long categoryId;
    @ApiModelProperty(hidden = true)
    private Long parentId;
    @ApiModelProperty("类型名称")
    private String categoryName;
    @ApiModelProperty("子类")
    private List<PenaltyCategoryVo> children;
}
