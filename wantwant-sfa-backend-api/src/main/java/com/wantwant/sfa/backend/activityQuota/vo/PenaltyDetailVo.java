package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/12/22/上午9:32
 */
@Data
@ApiModel("实际扣款明细")
public class PenaltyDetailVo {

    @ApiModelProperty("费用币种")
    private String walletType;

    @ApiModelProperty("币种子类")
    private String walletSubType;

    @ApiModelProperty("扣罚时间")
    private String penaltyTime;

    @ApiModelProperty("扣罚金额")
    private BigDecimal quota;

    @ApiModelProperty("金币类型")
    private String applyType;
}
