package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午2:19
 */
@Data
@ApiModel("特批汰换扣罚规则明细")
public class PenaltyEliminateRegularVo {

    @ApiModelProperty("项目名称")
    private String regularName;
    @ApiModelProperty("金币类型:1.旺金币")
    private Integer coinsType;
    @ApiModelProperty("扣罚类型ID")
    private Integer walletTypeId;
    @ApiModelProperty("扣罚类型")
    private String walletType;
    @ApiModelProperty("扣除金额")
    private BigDecimal amount;
    @ApiModelProperty("操作方式:1.系统 2.人工")
    private Integer processType;
    @ApiModelProperty("备注")
    private String remark;
}
