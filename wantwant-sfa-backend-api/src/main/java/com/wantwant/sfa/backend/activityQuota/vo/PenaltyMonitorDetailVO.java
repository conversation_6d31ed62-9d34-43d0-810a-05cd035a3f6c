package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/07/上午10:26
 */
@Data
@ApiModel("扣罚监控明细")
public class PenaltyMonitorDetailVO {
    @ApiModelProperty(hidden = true)
    private String organizationId;
    @ApiModelProperty("大类ID")
    private Integer categoryId;
    @ApiModelProperty("大类")
    private String categoryName;
    @ApiModelProperty("子类")
    private String secondaryCategoryName;
    @ApiModelProperty("子类ID")
    private Integer secondaryCategoryId;
    @ApiModelProperty("应扣")
    private BigDecimal penaltyAmount = BigDecimal.ZERO;
    @ApiModelProperty("实扣")
    private BigDecimal actualPenaltyAmount  = BigDecimal.ZERO;
    @ApiModelProperty("挂帐")
    private BigDecimal deferPayment  = BigDecimal.ZERO;
}
