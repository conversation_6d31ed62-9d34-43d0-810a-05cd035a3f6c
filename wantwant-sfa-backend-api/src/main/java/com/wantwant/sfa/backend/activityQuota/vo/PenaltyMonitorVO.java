package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/06/下午4:51
 */
@Data
@ApiModel("扣罚监控")
public class PenaltyMonitorVO {
    @ApiModelProperty("战区")
    private String areaName;
    @ApiModelProperty("大区")
    private String vareaName;
    @ApiModelProperty("省区")
    private String provinceName;
    @ApiModelProperty("分公司")
    private String companyName;
    @ApiModelProperty("营业所")
    private String departmentName;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty(hidden = true)
    private String organizationType;
    @ApiModelProperty("成员数")
    private Integer numbers;
    @ApiModelProperty("岗位")
    private String position;
    @ApiModelProperty("头像")
    private String avatar;
    @ApiModelProperty("姓名")
    private String employeeName;
    @ApiModelProperty("employeeInfoId")
    private Integer employeeInfoId;
    @ApiModelProperty("扣罚明细")
    private List<PenaltyMonitorDetailVO> penaltyMonitorDetailVOList;
    @ApiModelProperty("memberKey")
    private Long memberKey;
    @ApiModelProperty("在职天数")
    private Integer onBoardDate;
    @ApiModelProperty("合计应扣")
    private BigDecimal totalPenaltyAmount;
    @ApiModelProperty("合计实扣")
    private BigDecimal totalActualPenaltyAmount;
    @ApiModelProperty("合计挂帐")
    private BigDecimal totalDeferPayment;




}
