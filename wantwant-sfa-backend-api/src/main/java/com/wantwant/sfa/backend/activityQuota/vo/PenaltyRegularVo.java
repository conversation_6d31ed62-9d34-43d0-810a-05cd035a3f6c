package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午1:49
 */
@Data
@ApiModel("扣罚规则Vo")
public class PenaltyRegularVo {

    @ApiModelProperty("规则ID")
    @Excel(name = "规则ID")
    private Long regularId;

    @ApiModelProperty("规则名称")
    @Excel(name = "规则名称")
    private String regularName;

    @ApiModelProperty("项目规则")
    @Excel(name = "项目规则")
    private String regularDescription;

    @ApiModelProperty("类型名称")
    @Excel(name = "类型名称")
    private String categoryName;
    @ApiModelProperty("类型子名称")
    @Excel(name = "类型子名称")
    private String secondaryCategoryName;

    @ApiModelProperty("制定人")
    @Excel(name = "制定人")
    private String createUserName;

    @ApiModelProperty("扣罚金币类型:1.旺金币")
    private Integer coinsType;

    @ApiModelProperty("币种类型")
    @Excel(name = "币种类型")
    private String walletType;

    @ApiModelProperty("币种类型ID")
    private Integer walletTypeId;

    @ApiModelProperty("操作方式:1.系统，2.人工")
    @Excel(name = "操作方式",replace = {"-_null","系统_1","人工_2"})
    private Integer processType;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remark;

    @ApiModelProperty("状态:0.停用 1.启用")
    @Excel(name = "状态",replace = {"-_null","停用_0","启用_1"})
    private Integer status;

    @ApiModelProperty("费用类型ID")
    private String applyTypeList;


    @ApiModelProperty("费用类型")
    @Excel(name = "费用类型")
    private String applyTypeNameStr;
}
