package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/17/上午9:32
 */
@Data
@ApiModel("扣罚项目")
public class PenaltyVo {
    @ApiModelProperty("产品组")
    @Excel(name = "产品组")
    private String businessGroupName;

    @ApiModelProperty("单据ID")
    @Excel(name = "单据ID")
    private Long id;


    @ApiModelProperty("扣罚时间")
    @Excel(name = "扣罚时间")
    private String penaltyTime;

    @ApiModelProperty("扣罚项目")
    @Excel(name = "扣罚项目")
    private String penaltyItem;

    @ApiModelProperty("扣罚规则")
    private String regularDescription;

    @ApiModelProperty("扣罚类型(大类)")
    @Excel(name = "扣罚类型(大类)")
    private String categoryName;

    @ApiModelProperty("扣罚类型(子类)")
    @Excel(name = "扣罚类型(子类)")
    private String secondaryCategoryName;

    @ApiModelProperty("扣罚币种")
    private String walletType;

    @ApiModelProperty("实际扣罚币种ID")
    private Integer actualWalletTypeId;
    @ApiModelProperty("实际扣罚币种类型")
    private String actualWalletType;

    @ApiModelProperty("应扣罚组织")
    @Excel(name = "应扣罚组织")
    private String penaltyOrganizationName;
    @ApiModelProperty("应扣罚人员")
    @Excel(name = "应扣罚人员")
    private String penaltyEmployeeName;
    @ApiModelProperty("应扣金币")
    @Excel(name = "应扣金币")
    private BigDecimal penaltyAmount;

    @ApiModelProperty("实际扣罚组织")
    @Excel(name = "实际扣罚组织")
    private String actualPenaltyOrganizationName;
    @ApiModelProperty("实际扣罚人员")
    @Excel(name = "实际扣罚组织")
    private String actualPenaltyEmployeeName;
    @ApiModelProperty("实际扣金币")
    @Excel(name = "实际扣金币")
    private BigDecimal actualPenaltyAmount;
    @ApiModelProperty("挂帐金额")
    @Excel(name = "挂帐金额")
    private BigDecimal deferPayment;
    @ApiModelProperty("扣款对象:1.旺金币")
    private Integer coinsType;

    @ApiModelProperty("金币类型")
    @Excel(name = "扣罚金币类型")
    private String applyType;

    @ApiModelProperty("状态:1.进行中 2.撤销 3.完成 4.取消")
    @Excel(name = "状态",replace={"-_null","进行中_1","撤销_2","完成_3","取消_4"})
    private Integer status;


    @ApiModelProperty("扣罚规则ID")
    private Integer penaltyRegularId;
    @ApiModelProperty("规则制定人")
    private String createUserName;


    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作人")
    private String processUserName;
}
