package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/17/上午10:05
 */
@Data
@ApiModel("额度权限VO")
public class PursePermissionVo {
    //区域类型Code area 战区 varea 大区 province 省区 company 分公司 department 营业所
    @ApiModelProperty("战区权限")
    private boolean areaData;
    @ApiModelProperty("大区权限")
    private boolean vareaData;
    @ApiModelProperty("省区权限")
    private boolean provinceData;
    @ApiModelProperty("分公司权限")
    private boolean companyData;
    @ApiModelProperty("营业所权限")
    private boolean departmentData;
}
