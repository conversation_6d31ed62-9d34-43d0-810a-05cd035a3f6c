package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: GuRongHua
 * @Date: 2022/09/18/下午2:34
 */
@Data
@ApiModel("新额度总览Vo")
public class QuotaCollectionVo {
    @ApiModelProperty("月份")
    @Excel(name="月份",orderNum = "1")
    private String month;
    @ApiModelProperty("大区名称")
    @Excel(name="大区名称",orderNum = "2")
    private String areaName;
    @ApiModelProperty("分公司名称")
    @Excel(name = "分公司名称",orderNum = "3")
    private String companyName;
    @ApiModelProperty("负责人")
    @Excel(name="负责人",orderNum = "4")
    private String managerName;
    @ApiModelProperty("业务姓名")
    @Excel(name="员工名称")
    private String employeeName;
    @ApiModelProperty("职位")
    @Excel(name="岗位")
    private String position;

    @ApiModelProperty(value = "额度明细")
    private List<QuotaDetailVo> quotaDetailVo;

    @ApiModelProperty("累计总额度(业务别-累计额度)")
    @Excel(name="累计总额度")
    private BigDecimal totalQuota;
    @ApiModelProperty("累计剩余额度(业务别-累计剩余额度)")
    @Excel(name="累计剩余额度")
    private BigDecimal totalRemainQuota;

    @ApiModelProperty("应扣金额")
    @Excel(name="应扣金额")
    private BigDecimal penaltyAmount;

    @ApiModelProperty("实际扣除金额")
    @Excel(name="实际扣除金额")
    private BigDecimal actualPenaltyAmount;

    @ApiModelProperty("累计挂帐")
    @Excel(name="累计挂帐")
    private BigDecimal penaltyRemainAmount;

    @ApiModelProperty("上级回收额度")
    @Excel(name="上级回收额度")
    private BigDecimal superiorRecoveryQuota;

    @ApiModelProperty("累计已使用额度")
    @Excel(name="累计已使用额度")
    private BigDecimal totalUsedQuota;

}
