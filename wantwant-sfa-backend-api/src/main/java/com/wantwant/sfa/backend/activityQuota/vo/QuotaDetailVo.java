package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/15/下午2:34
 */
@Data
@ApiModel("新额度明细Vo")
public class QuotaDetailVo {

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("上月遗留额度")
    private BigDecimal lastMonthRemainingQuota;
    @Excel(name = "本月新增额度")
    @ApiModelProperty("本月新增额度")
    private BigDecimal thisMonthNewQuota;
    @Excel(name = "本月分配额度")
    @ApiModelProperty("本月分配额度")
    private BigDecimal thisMonthDistributionQuota;
    @Excel(name = "上级回收额度")
    //@ApiModelProperty("上级回收额度")
    private BigDecimal thisMonthRecycleQuota;
    //@ApiModelProperty("上级回收额度")
    private BigDecimal superiorRecoverQuota;
    @Excel(name = "剩余额度")
    @ApiModelProperty("剩余额度")
    private BigDecimal thisMonthRemainingQuota;
}
