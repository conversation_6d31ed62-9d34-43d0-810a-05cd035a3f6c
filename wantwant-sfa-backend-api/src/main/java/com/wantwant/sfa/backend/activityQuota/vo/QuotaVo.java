package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/10/上午11:34
 */
@Data
@ApiModel("额度信息")
public class QuotaVo {
    @ApiModelProperty("总额度")
    private BigDecimal totalQuota;
    @ApiModelProperty("额度")
    private BigDecimal quota;
    @ApiModelProperty("罚款额度")
    private BigDecimal penaltyQuota;
    @ApiModelProperty("可用额度")
    private BigDecimal canUseQuota;
}
