package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 回收额度明细。
 * @Auther: zhengxu
 * @Date: 2021/12/24/上午9:24
 */
@Data
@ApiModel("回收额度明细")
public class RetrieveQuotaVo {
    @ApiModelProperty("组织信息")
    private String organizationInfo;
    @ApiModelProperty("员工信息")
    private String employeeInfo;
    @ApiModelProperty("已使用的额度")
    private String usedQuota;
    @ApiModelProperty("已回收的额度")
    private String retrievedQuota;
    @ApiModelProperty("可使用的额度")
    private String quota;

}
