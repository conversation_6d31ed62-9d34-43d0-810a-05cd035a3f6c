package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/上午9:39
 */
@Data
@ApiModel("旺金币账单")
public class WantCoinsBillVo {
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("月份")
    private String month;
    @ApiModelProperty("起止日期")
    private String period;
    @ApiModelProperty("月初旺金币费用")
    private BigDecimal monthBeginQuota;
    @ApiModelProperty("总收入旺金币")
    private BigDecimal incomeQuotaTotal;
    @ApiModelProperty("收入旺金币（非迁移）")
    private BigDecimal incomeQuota;
    @ApiModelProperty("收入旺金币（迁移）")
    private BigDecimal incomeQuotaMove;
    @ApiModelProperty("挂帐")
    private BigDecimal quotaCharge;
    @ApiModelProperty("总支出旺金币")
    private BigDecimal expenditureQuotaTotal;
    @ApiModelProperty("支出旺金币（非迁移）")
    private BigDecimal expenditureQuota;
    @ApiModelProperty("支出旺金币（迁移）")
    private BigDecimal expenditureQuotaMove;
    @ApiModelProperty("剩余额度")
    private BigDecimal quotaSurplus;
    @ApiModelProperty("扣减额度")
    private BigDecimal quotaDeducted;
    @ApiModelProperty("实际可用额度")
    private BigDecimal quotaAvailable;


}
