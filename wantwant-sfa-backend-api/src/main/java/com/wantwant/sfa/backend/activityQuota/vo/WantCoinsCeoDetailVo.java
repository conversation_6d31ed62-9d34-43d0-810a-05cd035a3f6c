package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/28/下午1:34
 */
@ApiModel("旺金币合伙人明细VO")
@Data
public class WantCoinsCeoDetailVo {

    @ApiModelProperty("日期")
    @Excel(name = "日期")
    private String date;

    @ApiModelProperty("额度")
    @Excel(name = "额度")
    private BigDecimal quota;

    @ApiModelProperty("旺金币父类名称")
    @Excel(name = "旺金币父类名称")
    private String classTypeName;

    @ApiModelProperty("费用类型")
    @Excel(name = "费用类型")
    private String applyType;


    @ApiModelProperty("合伙人名称")
    @Excel(name = "合伙人名称")
    private String employeeName;

    @ApiModelProperty("合伙人手机号")
    @Excel(name = "合伙人手机号")
    private String mobile;

}
