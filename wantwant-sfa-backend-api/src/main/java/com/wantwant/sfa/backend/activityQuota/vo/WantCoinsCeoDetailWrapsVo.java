package com.wantwant.sfa.backend.activityQuota.vo;

import com.wantwant.commons.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/28/下午1:33
 */
@Data
@ApiModel("旺金币合伙人收支记录包装类")
public class WantCoinsCeoDetailWrapsVo {
    @ApiModelProperty("总金额")
    private BigDecimal total;
    @ApiModelProperty("明细")
    private Page<WantCoinsCeoDetailVo> wantCoinsDetailVoPage;
}
