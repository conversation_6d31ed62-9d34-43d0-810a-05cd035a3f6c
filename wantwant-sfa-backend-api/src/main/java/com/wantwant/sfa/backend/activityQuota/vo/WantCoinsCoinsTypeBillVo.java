package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/15/上午9:31
 */
@Data
@ApiModel("旺金币费用类型账单明细")
public class WantCoinsCoinsTypeBillVo {
    @ApiModelProperty("大区名称")
    private String areaName;
    @ApiModelProperty("分公司名称")
    private String companyName;
    @ApiModelProperty("旺金币父类")
    private String classTypeName;
    @ApiModelProperty("费用类型")
    private String applyType;


    @ApiModelProperty("月初旺金币费用")
    private BigDecimal monthBeginQuota;
    @ApiModelProperty("下级月初旺金币费用")
    private String nextMonthBeginQuota;

    @ApiModelProperty("总收入旺金币")
    private BigDecimal incomeQuotaTotal;
    @ApiModelProperty("下级总收入旺金币")
    private String nextIncomeQuotaTotal;

    @ApiModelProperty("收入旺金币（非迁移）")
    private BigDecimal incomeQuota;
    @ApiModelProperty("收入旺金币（迁移）")
    private BigDecimal incomeQuotaMove;

    @ApiModelProperty("总支出旺金币")
    private BigDecimal expenditureQuotaTotal;
    @ApiModelProperty("下级旺金币支出")
    private String nextExpenditureQuotaTotal;

    @ApiModelProperty("支出旺金币（非迁移）")
    private BigDecimal expenditureQuota;
    @ApiModelProperty("支出旺金币（迁移）")
    private BigDecimal expenditureQuotaMove;
    @ApiModelProperty("剩余额度")
    private BigDecimal quotaSurplus;
    @ApiModelProperty("扣减额度")
    private BigDecimal quotaDeducted;
    @ApiModelProperty("下级扣减额度")
    private String nextQuotaDeducted;

    @ApiModelProperty("实际可用额度")
    private BigDecimal quotaAvailable;
    @ApiModelProperty("下级旺金币可用")
    private String nextQuotaAvailable;

    private Integer coinsType;

    private String organizationId;

    private String organizationName;
}
