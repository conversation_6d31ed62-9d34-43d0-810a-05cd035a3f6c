package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/28/下午5:49
 */
@Data
@ApiModel("旺金币合伙人按类型账单")
public class WantCoinsCoinsTypeCeoBillVo {

    @ApiModelProperty("旺金币父类")
    private String classTypeName;
    @ApiModelProperty("费用类型")
    private String applyType;
    @ApiModelProperty("总收入旺金币")
    private BigDecimal incomeQuotaTotal;
    @ApiModelProperty("总支出旺金币")
    private BigDecimal expenditureQuotaTotal;
    @ApiModelProperty("剩余额度")
    private BigDecimal quotaSurplus;
    @ApiModelProperty("旺金币费用率")
    private BigDecimal quotaFeeRate;

}
