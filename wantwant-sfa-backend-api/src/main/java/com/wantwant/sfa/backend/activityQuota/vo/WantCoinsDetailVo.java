package com.wantwant.sfa.backend.activityQuota.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/下午12:15
 */
@ApiModel("收支记录明细")
@Data
public class WantCoinsDetailVo {
    @ApiModelProperty("日期")
    @Excel(name = "时间")
    private String date;

    @ApiModelProperty("旺金币父类")
    @Excel(name = "费用大类")
    private String classTypeName;

    @ApiModelProperty("旺金币子类")
    @Excel(name = "费用类型")
    private String applyType;

    @ApiModelProperty("操作类型")
    @Excel(name = "操作类型",replace = {"收入_1","支出_2"})
    private Integer type;

    @ApiModelProperty("额度")
    @Excel(name = "金额")
    private BigDecimal quota;

    @ApiModelProperty("支出方")
    @Excel(name = "支出方")
    private String expenditure;

    @ApiModelProperty("收入方")
    @Excel(name = "收入方")
    private String revenue;

    @ApiModelProperty("是否组织迁移部分:1.是 0.否")
    @Excel(name = "是否组织迁移部分",replace = {"是_1","否_0"})
    private Integer actionType;
}
