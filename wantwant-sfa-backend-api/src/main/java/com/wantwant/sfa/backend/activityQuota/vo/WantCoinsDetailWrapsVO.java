package com.wantwant.sfa.backend.activityQuota.vo;

import com.wantwant.commons.pagination.Page;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/下午12:46
 */
@Data
@ApiModel("旺金币收支记录包装类")
public class WantCoinsDetailWrapsVO {

    private BigDecimal total;

    private Page<WantCoinsDetailVo> wantCoinsDetailVoPage;
}
