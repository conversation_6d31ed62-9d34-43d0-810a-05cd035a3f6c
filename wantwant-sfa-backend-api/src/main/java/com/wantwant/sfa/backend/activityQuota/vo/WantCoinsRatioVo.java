package com.wantwant.sfa.backend.activityQuota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/上午10:50
 */
@Data
@ApiModel("费用收支占比")
public class WantCoinsRatioVo {
    @ApiModelProperty("旺金币父类名称")
    private String quotaParentTypeName;

    @ApiModelProperty("占比")
    private BigDecimal ratio;

    @ApiModelProperty("额度")
    private BigDecimal quota;

    @ApiModelProperty("明细")
    private List<WantCoinsRatioDetailVo> details;
}
