package com.wantwant.sfa.backend.authorization.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.authorization.request.*;
import com.wantwant.sfa.backend.authorization.vo.AuthorizationProcessRecordVo;
import com.wantwant.sfa.backend.authorization.vo.AuthorizationProcessVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.validation.Valid;
import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午4:05
 */
@Api(value = "AuthorizeCustomerApi", tags = "客户授权")
public interface AuthorizeCustomerApi {

    @ApiOperation(value = "创建客户授权审核信息", notes = "创建客户授权审核信息", httpMethod = "POST")
    @PostMapping("/customer/auth")
    Response createProcess(@Validated @RequestBody CreateProcessRequest request);

    @ApiOperation(value = "客户授权书撤回", notes = "客户授权书撤回", httpMethod = "POST")
    @PostMapping("/customer/revert")
    Response revert(@RequestBody @Valid RevertRequest revertRequest);

    @ApiOperation(value = "通知审核照片", notes = "通知审核照片", httpMethod = "POST")
    @PostMapping("/customer/auth/notify")
    Response authNotify(@Validated @RequestBody NotifyRequest request);

    @ApiOperation(value = "审核通过", notes = "审核通过", httpMethod = "POST")
    @PostMapping("/customer/auth/verify")
    Response verify(@Validated @RequestBody VerifyRequest request);

    @ApiOperation(value = "审核驳回", notes = "审核驳回", httpMethod = "POST")
    @PostMapping("/customer/auth/reject")
    @Deprecated
    Response reject(@Validated @RequestBody RejectRequest request);

    @ApiOperation(value = "获取授权审核记录", notes = "获取授权审核记录", httpMethod = "GET")
    @GetMapping("/customer/auth/record/{applyId}")
    Response<List<AuthorizationProcessVo>> getRecord(@PathVariable Integer applyId);

    @ApiOperation(value = "上传合同", notes = "上传合同", httpMethod = "POST")
    @PostMapping("/customer/auth/uploadContract")
    Response uploadContract(@Validated @RequestBody ContractUploadRequest request);
}
