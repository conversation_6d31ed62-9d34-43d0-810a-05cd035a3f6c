package com.wantwant.sfa.backend.authorization.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.authorization.request.AuthorizationDetailRequest;
import com.wantwant.sfa.backend.authorization.request.AuthorizationSearchRequest;
import com.wantwant.sfa.backend.authorization.request.ManageRequest;
import com.wantwant.sfa.backend.authorization.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午5:01
 */
@Api(value = "CustomerAuthorizationApi", tags = "客户授权信息获取")
public interface CustomerAuthorizationApi {

    @ApiOperation(value = "根据客户ID获取授权信息", notes = "根据客户ID获取授权信息", httpMethod = "GET")
    @GetMapping("/customer/authorization")
    Response<AuthorizationInfoVo> getAuthorizationByCustomerId(@RequestParam String customerId);

    @ApiOperation(value = "获取客户等级")
    @GetMapping("/customer/level")
    Response<List<String>> getCustomerLevel();

    @ApiOperation(value = "根据客户ID获取初版合同", notes = "根据客户ID获取初版合同", httpMethod = "GET")
    @GetMapping("/customer/authorization/draftContracts")
    Response<String> getDraftContracts(@RequestParam String customerId);

    @ApiOperation(value = "授权审核列表", notes = "授权审核列表", httpMethod = "POST")
    @PostMapping("/authorization/list")
    Response<Page<AuthorizationVo>> authorizationList(@RequestBody AuthorizationSearchRequest request);

    @ApiOperation(value = "授权审核明细", notes = "授权审核明细", httpMethod = "POST")
    @PostMapping("/authorization")
    Response<AuthorizationDetailVo> getAuthorizationDetail(@RequestBody AuthorizationDetailRequest request);

    @ApiOperation(value = "获取审核流程记录", notes = "获取审核流程记录", httpMethod = "GET")
    @GetMapping("/authorization/record/{verifyId}")
    Response<AuthorizationVerifyRecordVo> getVerifyRecord(@PathVariable Long verifyId);

    @ApiOperation(value = "上传正式合同页面信息", notes = "上传正式合同页面信息", httpMethod = "GET")
    @GetMapping("/authorization/contractInfo/{verifyId}")
    Response<AuthorizationContractInfoVo> getAuthorizationContractInfo(@PathVariable Long verifyId);

    @ApiOperation(value = "授权管理关闭权限", notes = "授权管理关闭权限")
    @GetMapping("/authorization/closePermissions/{employeeId}")
    Response<Boolean> closePermissions(@PathVariable String employeeId);

    @ApiOperation(value = "授权管理关闭", notes = "授权管理")
    @PutMapping("/authorization/close/{verifyId}/{employeeId}")
    Response<Integer> close(@PathVariable Long verifyId,@PathVariable String employeeId);
}
