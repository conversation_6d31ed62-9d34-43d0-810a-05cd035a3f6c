package com.wantwant.sfa.backend.authorization.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/09/下午1:25
 */
@Data
@ToString
@ApiModel("授权列表查询request")
public class AuthorizationSearchRequest extends PageParam {
    @ApiModelProperty("申请ID")
    private Integer applyId;
    @ApiModelProperty("申请开始时间")
    private LocalDate applyStartDate;
    @ApiModelProperty("申请结束时间")
    private LocalDate applyEndDate;
    @ApiModelProperty("客户查询")
    private String customerKey;
    @ApiModelProperty("业务查询")
    private String businessKey;
    @ApiModelProperty(value = "旺旺大区ID")
    private List<String> areaOrganizationIds;

    @ApiModelProperty(value = "大区总监")
    private List<String> vareaOrganizationIds;

    @ApiModelProperty(value = "省区总监")
    private List<String> provinceOrganizationIds;

    @ApiModelProperty(value = "旺旺分公司ID")
    private List<String> companyOrganizationIds;

    @ApiModelProperty(value = "旺旺营业所ID")
    private List<String> departmentIds;

    @ApiModelProperty("子任务状态:10.区域经理审核 20.区域总监审核 30.省区总监审核 40.大区总监审核 50.战区督导审核 60.营运审核 70.待营运上传合同  80.不予受理  81.基础信息填写错误")
    private Integer subTaskStatus;

    @ApiModelProperty("主任务状态: 1.审核中 2.已驳回 3.已通过 4.已撤回 5.待营运上传合同")
    private Integer status;

    @ApiModelProperty("合同类型 0:合伙人合同 1:客户合同")
    private Integer contractType;
    @ApiModelProperty("操作人")
    private String person;
}
