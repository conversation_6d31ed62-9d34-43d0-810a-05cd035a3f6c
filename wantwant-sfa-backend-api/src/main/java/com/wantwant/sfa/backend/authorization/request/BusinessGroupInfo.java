package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/29/下午4:54
 */
@Data
@ApiModel("业务组信息")
@ToString
public class BusinessGroupInfo {

    @ApiModelProperty("业务组CODE")
    private String businessGroupCode;

    @ApiModelProperty("产线ID")
    private List<String> lineId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    private Integer status;

    private String msg;
}
