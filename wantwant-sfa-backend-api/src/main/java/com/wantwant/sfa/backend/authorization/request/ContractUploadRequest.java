package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/07/下午3:44
 */
@ApiModel("上传合同request")
@ToString
@Data
public class ContractUploadRequest {
    @ApiModelProperty("授权申请ID")
    @NotNull(message = "缺少授权申请ID")
    private Integer applyId;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("有效开始时间")
    private LocalDate startValidTime;

    @ApiModelProperty("有效结束时间")
    private LocalDate endValidTime;

    @ApiModelProperty("合同文件")
    private String contracts;

    @ApiModelProperty("证明文件")
    private String verifyFiles;

    @ApiModelProperty("其他协议")
    private String protocols;
}
