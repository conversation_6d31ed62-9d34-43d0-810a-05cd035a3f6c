package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午4:10
 */
@Data
@ApiModel("创建流程request")
@ToString
public class CreateProcessRequest {

    @ApiModelProperty("客户ID")
    private String customerId;

    @ApiModelProperty("授权申请ID")
    @NotNull(message = "缺少授权申请ID")
    private Integer authorizationId;

    @ApiModelProperty("合同类型 0:合伙人 1:建党客户")
    @NotNull(message = "缺少合同类型")
    private Integer contractType;

    @ApiModelProperty("合同流水号")
    private String contractNo;

    @ApiModelProperty("company_name")
    private String companyName;

    @ApiModelProperty("会员号")
    private String memberKey;

    @ApiModelProperty("税号")
    private String taxpayerNumber;

    @ApiModelProperty("纳税人资质 0:一般纳税人 1:小规模纳税人")
    private Integer taxpayerType;

    @ApiModelProperty("法人姓名")
    private String legalPersonName;

    @ApiModelProperty("法人身份证")
    private String legalPersonIdCard;

    @ApiModelProperty("公司地址")
    private String companyAddress;

    @ApiModelProperty("公司电话")
    private String companyMobile;

    @ApiModelProperty("开户银行")
    private String bankNo;

    @ApiModelProperty("开户账号")
    private String bankAccount;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("联系人电话")
    private String contactNumber;

    @ApiModelProperty("联系地址")
    private String contactAddress;

    @ApiModelProperty("电子邮箱")
    private String email;

    @ApiModelProperty("合同pdf_url")
    private String imageUrl;

    @ApiModelProperty("注册资本")
    private String registeredCapital;

    @ApiModelProperty("法人身份证照片正面")
    private String legalPersonIdCardImgFront;

    @ApiModelProperty("法人身份证照片反面")
    private String legalPersonIdCardImgBack;

    @ApiModelProperty("营业执照")
    private String businessLicense;

    @ApiModelProperty("营业执照照片")
    private String businessLicenseImg;

    @ApiModelProperty("食品经营许可证")
    private String foodBusinessLicenseImg;

    @ApiModelProperty("酒类经营许可证")
    private String liquorBusinessLicenseImg;

    @ApiModelProperty("经营品牌")
    private String businessBrand;

    @ApiModelProperty("公司车辆信息")
    private String vehicle;

    @ApiModelProperty("业务组信息")
    @NotEmpty(message = "缺少业务组信息")
    private List<BusinessGroupInfo> businessGroupInfoList;

    @ApiModelProperty("经销范围")
    @Length(max = 500, message = "经销范围不能超过500个字符")
    private String distributionScope;


    @ApiModelProperty(value = "是否替代申请售后单:默认 0: 合伙人申请  1:代申请 ")
    private Integer substituteType;

    @ApiModelProperty(value = "代申请人的memberKey")
    private String  substituteMemberKey;
}
