package com.wantwant.sfa.backend.authorization.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


@ApiModel("授权管理request")
@ToString
@Data
public class ManageRequest extends PageParam implements Serializable{

    private static final long serialVersionUID = 2315622873064654974L;

    @ApiModelProperty(value = "登录人组织id",required = true)
    private String organizationId;

    @ApiModelProperty(value = "上传开始时间(yyyy-MM-dd)")
    private String startDate;

    @ApiModelProperty(value = "上传结束时间(yyyy-MM-dd)")
    private String endDate;

    @ApiModelProperty(value = "客户查询(经销商全称/客户编号/客户姓名/手机号)")
    private String customerKey;

    @ApiModelProperty(value = "状态(0:已生效,1:已失效)")
    private Integer status;

    @ApiModelProperty(value = "大区组织ID")
    private String areaOrganizationId;

    @ApiModelProperty(value = "分公司组织ID")
    private String companyCode;

    @ApiModelProperty("业务姓名/手机号")
    private String businessKey;

}
