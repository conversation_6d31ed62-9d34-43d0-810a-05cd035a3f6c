package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午4:17
 */
@Data
@ApiModel("授权申请request")
@ToString
public class NotifyRequest {
    @ApiModelProperty("授权申请ID")
    private Integer authorizationId;

    @ApiModelProperty("有效开始时间")
    @NotNull(message = "缺少有效开始时间")
    private LocalDate startValidTime;

    @ApiModelProperty("有效结束时间")
    @NotNull(message = "缺少有效结束时间")
    private LocalDate endValidTime;

    @ApiModelProperty("合同文件")
    private String contracts;

    @ApiModelProperty("证明文件")
    private String verifyFiles;

    @ApiModelProperty("其他附件")
    private String protocols;


}
