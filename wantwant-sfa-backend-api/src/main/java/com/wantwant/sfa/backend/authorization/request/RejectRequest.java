package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/07/下午2:50
 */
@Data
@ToString
@ApiModel("客户授权驳回")
public class RejectRequest {
    @ApiModelProperty("操作人工号")
    @NotNull(message = "缺少操作人工号")
    private String person;

    @ApiModelProperty("旺铺合同流水号")
    @NotBlank(message = "缺少旺铺合同流水号")
    private String contractNo;

    @ApiModelProperty("审核ID")
    @NotNull(message = "缺少审核ID")
    private Long verifyId;

    @ApiModelProperty("驳回原因")
    private String reason;
}
