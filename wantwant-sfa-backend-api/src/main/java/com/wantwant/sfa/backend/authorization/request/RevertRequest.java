package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/09/01/下午2:02
 */
@Data
@ToString
@ApiModel("授权书撤回请求")
public class RevertRequest {

    @ApiModelProperty("授权申请ID")
    @NotNull(message = "缺少授权申请ID")
    private Integer authorizationId;
}
