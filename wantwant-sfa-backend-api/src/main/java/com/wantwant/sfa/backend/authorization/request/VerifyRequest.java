package com.wantwant.sfa.backend.authorization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/07/上午10:33
 */
@ApiModel("审核通过request")
@Data
@ToString
public class VerifyRequest {

    @ApiModelProperty("申请ID")
    @NotNull(message = "缺少申请ID")
    private Long applyId;
    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少审核人")
    private String person;
    @ApiModelProperty("处理结果：1.通过 2.驳回 5.不予受理")
    @NotNull(message = "缺少处理结果")
    private Integer result;
    @ApiModelProperty("备注")
    private String comment;
}
