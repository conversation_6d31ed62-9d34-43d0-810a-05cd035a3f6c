package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/09/下午2:43
 */
@Data
@ApiModel("上传合同页面信息")
public class AuthorizationContractInfoVo {
    @ApiModelProperty("合同类型 0:合伙人合同 1:客户合同")
    private Integer contractType;

    @ApiModelProperty("旺铺合同流水号")
    private String contractNo;

    @ApiModelProperty("客户编号")
    private String customerId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("法人姓名")
    private String legalPersonName;

    @ApiModelProperty("合同")
    private String contract;
    @ApiModelProperty("证明")
    private String verifyFile;
    @ApiModelProperty("协议")
    private String protocols;

    @ApiModelProperty("有效开始日期")
    private String validStartDate;

    @ApiModelProperty("有效结束日期")
    private String validEndDate;

    @ApiModelProperty("公司电话")
    private String companyMobile;
}
