package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/09/下午1:33
 */
@Data
@ApiModel("审核明细")
public class AuthorizationDetailVo {

    @ApiModelProperty("申请时间")
    private String applyTime;
    @ApiModelProperty("组织")
    private String orgName;
    @ApiModelProperty("合伙人名称")
    private String ceoName;
    @ApiModelProperty("合伙人手机号")
    private String mobile;
    @ApiModelProperty("合同类型 0:合伙人合同 1:客户合同")
    private Integer contractType;
    @ApiModelProperty("客户编号")
    private String customerId;
    @ApiModelProperty("经销范围")
    private String distributionScope;
    @ApiModelProperty("联系人")
    private String contact;
    @ApiModelProperty("联系人电话")
    private String contactNumber;
    @ApiModelProperty("电子邮件")
    private String email;


    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("公司地址")
    private String companyAddress;
    @ApiModelProperty("公司电话")
    private String companyMobile;
    @ApiModelProperty("注册资本")
    private String registeredCapital;
    @ApiModelProperty("法人姓名")
    private String legalPersonName;
    @ApiModelProperty("法人身份证")
    private String legalPersonIdCard;
    @ApiModelProperty("营业执照")
    private String businessLicense;
    @ApiModelProperty("纳税人识别号")
    private String taxpayerNumber;
    @ApiModelProperty("纳税人资质 0:一般纳税人 1:小规模纳税人")
    private Integer taxpayerType;
    @ApiModelProperty("开户银行")
    private String bankNo;
    @ApiModelProperty("开户账号")
    private String bankAccount;
    @ApiModelProperty("公司经营品牌")
    private String businessBrand;
    @ApiModelProperty("公司车辆信息")
    private String vehicle;
    @ApiModelProperty("身份证正面")
    private String legalPersonIdCardImgFront;
    @ApiModelProperty("身份证反面")
    private String legalPersonIdCardImgBack;
    @ApiModelProperty("食品经营许可证")
    private String foodBusinessLicenseImg;
    @ApiModelProperty("酒类经营许可证")
    private String liquorBusinessLicenseImg;

    @ApiModelProperty("旺铺合同流水号")
    private String contractNo;

    @ApiModelProperty("是否可上传合同")
    private boolean canUpload;
    @ApiModelProperty("是否可操作")
    private boolean canProcess;

    @ApiModelProperty("产线名称")
    private String lineName;

    @ApiModelProperty("审批状态名称")
    private String subStatusName;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("有效开始时间")
    private String startValidTime;

    @ApiModelProperty("有效结束时间")
    private String endValidTime;

    @ApiModelProperty("合同文件")
    private String contracts;

    @ApiModelProperty("证明文件")
    private String verifyFiles;

    @ApiModelProperty("其他协议")
    private String protocols;


}
