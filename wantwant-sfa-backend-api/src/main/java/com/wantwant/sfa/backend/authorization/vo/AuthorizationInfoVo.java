package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午4:57
 */
@ApiModel("授权信息VO")
@Data
public class AuthorizationInfoVo {
    @ApiModelProperty("授权书")
    private String certificate;
    @ApiModelProperty("合同")
    private String contract;
    @ApiModelProperty("有效开始日期")
    private String validStartTime;
    @ApiModelProperty("有效结束日期")
    private String validEndTime;
}
