package com.wantwant.sfa.backend.authorization.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 授权管理列表
 *
 * @date 5/11/22 11:21 AM
 * @version 1.0
 */
@Data
@ApiModel("授权管理列表")
public class AuthorizationManageVo implements Serializable {

    private static final long serialVersionUID = 4352013105672981464L;

    @ApiModelProperty("授权审核ID")
    private Long verifyId;

    @ApiModelProperty("大区名称")
    private String areaName;

    @ApiModelProperty("分公司名称")
    private String companyName;

    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime createTime;

    @ApiModelProperty("客户编号")
    private String customerId;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("客户手机号")
    private String contactPhone;

    @ApiModelProperty("经销商名称")
    private String distributionName;

    @ApiModelProperty("经销范围")
    private String distributionScope;

    @ApiModelProperty("经销品项")
    private String lineName;

    @ApiModelProperty("所属业务员")
    private String employeeName;

    @ApiModelProperty("业务手机号")
    private String employeeMobile;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime validStartTime;

    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime validEndTime;

    /** 
     * 上传最终合同
     */
    @ApiModelProperty("负责人")
    private String creator;


    private Integer status;
    @ApiModelProperty(value = "状态")
    private String statusStr;

    public String getStatusStr() {
        if (status == 1) {
            return "已失效";
        } else {
            return "已生效";
        }
    }

    @ApiModelProperty("操作人")
    private String updator;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime updateTime;

}
