package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/09/06/上午10:24
 */
@Data
@ApiModel("授权审核操作记录VO")
public class AuthorizationProcessRecordVo {
    @ApiModelProperty("处理步骤")
    private String processName;
    @ApiModelProperty("处理时间")
    private String processTime;
    @ApiModelProperty("操作人")
    private String processUserName;
    @ApiModelProperty("处理结果:0.未处理 1.通过 2.驳回 4.关闭 5.不予受理")
    private String processResult;
    @ApiModelProperty("备注")
    private String comment;

    private String positionId;
    @ApiModelProperty("代申请人信息")
    private String substituteUserInfo;

}
