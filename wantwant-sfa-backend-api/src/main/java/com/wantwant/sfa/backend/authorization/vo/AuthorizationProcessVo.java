package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/09/06/上午11:01
 */
@Data
@ApiModel("授权审核操作VO")
public class AuthorizationProcessVo {

    @ApiModelProperty("第几次申请")
    private int applyTimes;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("操作记录")
    private List<AuthorizationProcessRecordVo> records;
}
