package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/09/下午1:54
 */
@ApiModel("审核记录")
@Data
public class AuthorizationVerifyRecordDetailVo {
    @ApiModelProperty("处理步骤")
    private String processName;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("驳回原因")
    private String rejectReason;
    @ApiModelProperty("操作人")
    private String person;
    @ApiModelProperty("处理时间")
    private String processTime;
}
