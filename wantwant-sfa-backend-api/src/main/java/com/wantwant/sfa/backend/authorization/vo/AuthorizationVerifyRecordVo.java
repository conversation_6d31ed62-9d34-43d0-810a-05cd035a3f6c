package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/11/下午2:16
 */
@Data
@ApiModel("处理流程记录")
public class AuthorizationVerifyRecordVo {
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("申请时间")
    private String applyTime;
    @ApiModelProperty("审核记录")
    private List<AuthorizationVerifyRecordDetailVo> details;
}
