package com.wantwant.sfa.backend.authorization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/09/下午1:17
 */
@Data
@ApiModel("授权审核列表信息")
public class AuthorizationVo {
    @ApiModelProperty("授权审核ID")
    private Long applyId;

    @ApiModelProperty("申请时间")
    private String applyTime;

    @ApiModelProperty("战区名称")
    private String areaName;

    @ApiModelProperty("大区名称")
    private String vareaName;

    @ApiModelProperty("省区名称")
    private String provinceName;

    @ApiModelProperty("分公司名称")
    private String companyName;

    @ApiModelProperty("营业所名称")
    private String departmentName;

    private String memberKey;

    @ApiModelProperty("合伙人名称")
    private String ceoName;

    @ApiModelProperty("授权对象: 0.合伙人 1.建党客户")
    private Integer contractType;

    @ApiModelProperty("客户编号")
    private String customerId;

    @ApiModelProperty("客户公司名称")
    private String customerCompanyName;

    @ApiModelProperty("注册资本")
    private String registeredCapital;

    @ApiModelProperty("法人姓名")
    private String legalPersonName;

    @ApiModelProperty("法人身份证")
    private String legalPersonIdCard;

    @ApiModelProperty("营业执照")
    private String businessLicense;

    @ApiModelProperty("纳税人资质 0:一般纳税人 1:小规模纳税人")
    private Integer taxpayerType;

    @ApiModelProperty("纳税人识别号")
    private String taxpayerNumber;

    @ApiModelProperty("开户银行")
    private String bankNo;

    @ApiModelProperty("开户账号")
    private String bankAccount;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("联系人电话")
    private String contactNumber;

    @ApiModelProperty("公司地址")
    private String companyAddress;

    @ApiModelProperty("合伙人电话")
    private String mobile;

    @ApiModelProperty("公司电话")
    private String companyMobile;


    @ApiModelProperty("联系地址")
    private String contactAddress;

    @ApiModelProperty("审批状态名称")
    private String subStatusName;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("是否可处理")
    private boolean canProcess;

    @ApiModelProperty("是否可上传")
    private boolean canUpload;


}
