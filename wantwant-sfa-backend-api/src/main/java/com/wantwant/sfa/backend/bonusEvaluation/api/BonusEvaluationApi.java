package com.wantwant.sfa.backend.bonusEvaluation.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.bonusEvaluation.request.*;
import com.wantwant.sfa.backend.bonusEvaluation.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

@Api(value = "BonusEvaluationApi", tags = "奖金评定Api")
public interface BonusEvaluationApi {

  @ApiOperation(value = "获取合伙人奖金评定", notes = "获取合伙人奖金评定", httpMethod = "POST")
  @PostMapping("/bonusEvaluation/branch")
  Response<Page<BranchBonusEvaluationVo>> getBranchBonusEvaluationList(
      @RequestBody BonusEvaluationRequest request);

  @ApiOperation(value = "获取区域总监奖金评定", notes = "获取区域总监奖金评定", httpMethod = "POST")
  @PostMapping("/bonusEvaluation/company")
  Response<Page<CompanyBonusEvaluationVo>> getCompanyBonusEvaluationList(
      @RequestBody BonusEvaluationRequest request);

  @ApiOperation(value = "更新保存奖金评定", notes = "更新奖金评定", httpMethod = "POST")
  @PostMapping("/bonusEvaluation")
  Response<Integer> updateBonusEvaluationList(@RequestBody BonusEvaluationUpdateRequest request);

  @ApiOperation(value = "合伙人/总监/总督导绩效评定列表", notes = "合伙人/总监/总督导绩效评定列表", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/list")
  Response<Page<PerformanceEvaluationVo>> performanceEvaluatioList(@Validated @RequestBody PerformanceEvaluationRequest request);

  @ApiOperation(value = "绩效奖金查询列表", notes = "绩效奖金查询列表", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/Detail/list")
  Response<PerformanceEvaluationListVo> performanceEvaluatioDetailList(@Validated @RequestBody PerformanceEvaluationRequest request) throws ParseException;

  @ApiOperation(value = "绩效奖金-提成明细-sku列表", notes = "绩效奖金-提成明细-sku列表", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/skuList")
  Response<List<PerformanceSkuDetailVo>> querySkuListPerformanceDetail(@RequestBody @Validated PerformanceSkuDetailPageRequest request);

  @ApiOperation(value = "绩效奖金-提成明细", notes = "绩效奖金-提成明细", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/skuDetail")
  Response<IPage<PerformanceSkuDetailVo>> querySkuPerformanceDetail(@RequestBody @Validated PerformanceSkuDetailPageRequest request);

  @ApiOperation(value = "绩效奖金-提成明细-下载", notes = "绩效奖金-提成明细-下载", httpMethod = "POST")
  @PostMapping(value = "/performanceEvaluation/downLoadSkuDetail")
  void downLoadSkuPerformanceDetail(@RequestBody @Validated PerformanceSkuDetailPageRequest request, HttpServletRequest req, HttpServletResponse res);

  @ApiOperation(value = "更新保存绩效奖金评定", notes = "更新保存绩效奖金评定", httpMethod = "POST")
  @PostMapping("/performanceEvaluation")
  Response<Integer> updatePerformanceEvaluatio(@Validated @RequestBody PerformanceEvaluationScoreRequest request);

  @ApiOperation(value = "定时任务更新评分数据(大区，分公司)", notes = "定时任务更新评分数据", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/organization/task")
  void performanceEvaluatioOrganizationTask();

  @ApiOperation(value = "定时任务更新评分数据(区域经理)", notes = "定时任务更新评分数据", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/task")
  void performanceEvaluatioTask();

  @ApiOperation(value = "绩效奖金待办事项", notes = "绩效奖金待办事项", httpMethod = "GET")
  @GetMapping("/performanceEvaluation/todo")
  Response<PerformanceEvalustionMattersVo> performanceEvaluatioMatters(@ApiParam("工号") @RequestParam("employeeId") String employeeId,
                                                                       @ApiParam(value = "1.总督导;2.区域总监;3.合伙人;10.区域经理",required = true) @RequestParam("type") Integer type);
  @ApiOperation(value = "绩效奖金查询表头", notes = "绩效奖金查询表头", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/header")
  Response<PerformanceEvaluationDynamicVo> performanceEvaluatioHeader(@RequestBody PerformanceEvaluationHeaderRequest request);

  @ApiOperation(value = "动态绩效奖金查询表头", notes = "动态绩效奖金查询表头", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/dynamic/list")
  Response<List<Map<String,Object>>> performanceEvaluatioDynamicList(@Validated @RequestBody PerformanceEvaluationRequest request);


  @ApiOperation(value = "绩效考核规则", notes = "绩效考核规则", httpMethod = "GET")
  @GetMapping("/performanceEvaluation/assessment/rule")
  Response<List<PerformanceAssessmentRulesVo>> performanceEvaluatioAssessmentRule(
                                                                       @ApiParam("开始月份") @RequestParam("startMonth") String startMonth,
                                                                       @ApiParam("结束月份") @RequestParam("endMonth") String endMonth,
                                                                       @ApiParam(value = "1.总督导;2.区域总监;10.区域经理",required = true) @RequestParam("type") Integer type);


  @ApiOperation(value = "根据工号得到部门的目标", notes = "根据工号得到部门的目标", httpMethod = "GET")
  @GetMapping("/section/target")
  Response<Page<SectionTargetVo>> getSectionTargetByPerson( @ApiParam(value = "工号",required = true) @RequestParam("person") String person);


  @ApiOperation(value = "根据工号得到部门的目标", notes = "根据工号得到部门的目标", httpMethod = "GET")
  @GetMapping("/section/target/all")
  Response<Page<SectionTargetVo>> getSectionTargetAll( @ApiParam(value = "工号",required = true) @RequestParam("person") String person,
                                                       @ApiParam(value = "部门名称",required = false) @RequestParam("sectionName") String sectionName,
                                                       @ApiParam(value = "目标内容",required = false) @RequestParam("targetContent") String targetContent);

  @ApiOperation(value = "绩效奖金消息中间页排行", notes = "绩效奖金消息中间页排行", httpMethod = "POST")
  @PostMapping("/performanceEvaluation/notify/top")
  Response<PerformanceEvaluationTopVo> performanceEvaluatioNotifyTop(@Validated @RequestBody PerformanceEvaluationTopRequest request);



}
