package com.wantwant.sfa.backend.bonusEvaluation.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "评定字段传参")
public class BonusEvaluationDetailRequest {

  @ApiModelProperty(value = "组织ID")
  private String organiztaionId;

  @ApiModelProperty(value = "员工信息id")
  private Integer employeeInfoId;

  @ApiModelProperty(value = "实际业绩达成奖")
  private Double actualAchievementAward;

  @ApiModelProperty(value = "实际客户成交奖")
  private Double actualCustomerTransactionAward;

  @ApiModelProperty(value = "实际目标达成奖")
  private Double actualGoalAchievementAward;

  @ApiModelProperty(value = "实际人效奖")
  private Double actualHumanEffectAward;
}
