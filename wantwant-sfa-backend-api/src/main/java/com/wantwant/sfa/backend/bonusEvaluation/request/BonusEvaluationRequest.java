package com.wantwant.sfa.backend.bonusEvaluation.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "评定传参")
public class BonusEvaluationRequest extends PageParam {

  @ApiModelProperty(value = "大区组织ID")
  private String areaId;

  @ApiModelProperty(value = "分公司组织ID")
  private String companyId;

  @ApiModelProperty(value = "组织ID")
  private String organizationId;

  @NotBlank(message = "考核月份不能为空")
  @ApiModelProperty(value = "考核月份")
  private String assessmentTime;
}
