package com.wantwant.sfa.backend.bonusEvaluation.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@ApiModel(value = "评定修改传参")
public class BonusEvaluationUpdateRequest {

  @ApiModelProperty(value = "操作人工号")
  private String employeeId;

  @ApiModelProperty(value = "是否提交(0.否；1.是)")
  private Integer isSubmit;

  @ApiModelProperty(value = "组织类型(0.分公司;1.合伙人)")
  private Integer organiztaionType;

  @ApiModelProperty(value = "考核月份")
  @NotBlank(message = "考核月份不能为空")
  private String assessmentTime;

  @ApiModelProperty(value = "评定字段明细")
  private List<BonusEvaluationDetailRequest> BonusEvaluationDetailList;
}
