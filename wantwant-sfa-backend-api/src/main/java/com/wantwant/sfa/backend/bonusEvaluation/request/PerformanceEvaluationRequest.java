package com.wantwant.sfa.backend.bonusEvaluation.request;

import com.wantwant.sfa.backend.common.validation.AllowValue;
import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@ApiModel(value = "绩效评定列表传参")
@Data
public class PerformanceEvaluationRequest extends PageParam {

    @ApiModelProperty(value = "大区")
    private String area;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "岗位类型(1.战区;2.分公司;3.营业所;10.区域经理;12大区;11省区)")
    @NotNull(message = "岗位类型不能为空")
    @AllowValue(value = "1,2,10")
    private Integer positionType;

    @ApiModelProperty(value = "人员")
    private String personnel;

    @ApiModelProperty(value = "员工表id")
    private Integer employeeInfoId;

    @ApiModelProperty("操作人工号")
    @NotBlank(message = "操作人工号不能为空")
    private String person;
    @ApiModelProperty(value = "操作人组织", hidden = true)
    private List<String> personOrganizationIds;
    @ApiModelProperty(value = "操作人岗位类型", hidden = true)
    private Integer personPositionTypeId;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "开始月份")
    private String startMonth;

    @ApiModelProperty(value = "结束月份")
    private String endMonth;

    @ApiModelProperty(value = "是否明细（0.否;1.是）")
    private int isDeltail;

    @ApiModelProperty(value = "状态(0.失效(暂存);1.生效(确定))")
    private int status;

    @ApiModelProperty(value = "是否在2022-11月份之前(0.否;1.是)")
    private int isBeforMonth;

    @ApiModelProperty(value = "是否在2023-01月份之前(0.否;1.是)算区域经理的奖金,如果是2023-01之后的考核，按0.15算，如果不是2023-01之前的考核，按0.1算")
    private int isBeforMonthNovember;

    @ApiModelProperty(value = "当前最后一天")
    private LocalDate monthLastDay;

    @ApiModelProperty(value = "动态字段(后端使用)")
    private String dynamicField;

    @ApiModelProperty(value = "产品组ID")
    private Integer businessGroup;

    @ApiModelProperty(value = "排序名称(itemProfit:品项利润 targetReachRate:达成率 directProfitRate:直接利润率)")
    private String sortName;

    @ApiModelProperty(value = "排序类型(asc正序 desc倒序)")
    private String sortType;

    @ApiModelProperty(value = "是否评分列表(0.否;1.是)")
    private int scoreFlg;
}
