package com.wantwant.sfa.backend.bonusEvaluation.request;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Api(value = "绩效评分传参")
@Data
public class PerformanceEvaluationScoreDetailRequest {

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "employee_info表id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "评分")
    private Integer score;

    @ApiModelProperty(value = "备注")
    private String note;

}
