package com.wantwant.sfa.backend.bonusEvaluation.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "绩效评分传参")
@Data
public class PerformanceEvaluationScoreRequest {

    @ApiModelProperty(value = "操作人Id")
    @NotBlank(message = "操作人Id为空")
    private String  person;

    @ApiModelProperty(value = "岗位类型(1.大区;2.分公司;3.营业所;10.区域经理)")
    @NotNull(message = "岗位类型不能为空")
    private Integer positionType;

    @ApiModelProperty(value = "考核月份")
    private String assessmentMonth;

    @ApiModelProperty(value = "绩效奖金评定明细")
    private List<PerformanceEvaluationScoreDetailRequest> performanceEvaluationScoreDetailList;

    @ApiModelProperty(value = "状态(0.暂存;1.确定)")
    private int status;

    //是否考核季度(0.否；1.是)
    @ApiModelProperty(value = "是否考核季度(0.否；1.是) 前端不需要传",hidden = true)
    private int isQuarter;

}
