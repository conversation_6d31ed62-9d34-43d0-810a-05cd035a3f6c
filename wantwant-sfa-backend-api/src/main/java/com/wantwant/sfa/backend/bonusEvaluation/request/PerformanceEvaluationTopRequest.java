package com.wantwant.sfa.backend.bonusEvaluation.request;

import com.wantwant.sfa.backend.common.validation.AllowValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(value = "绩效奖金消息中间页排行传参")
@Data
public class PerformanceEvaluationTopRequest{

    @ApiModelProperty(value = "岗位类型(1.战区;2.分公司;3.营业所;10.区域经理;12大区;11省区)")
    @NotNull(message = "岗位类型不能为空")
    @AllowValue(value = "1,12,11,2,10")
    private Integer positionType;

    @ApiModelProperty("业务组")
    private Integer businessGroup;

    @ApiModelProperty("年月")
    @NotBlank(message = "年月不能为空")
    private String theYearMon;

    @ApiModelProperty(value = "年月所在季度的最后一个月",hidden = true)
    private String theYearMonQuarterLast;
}
