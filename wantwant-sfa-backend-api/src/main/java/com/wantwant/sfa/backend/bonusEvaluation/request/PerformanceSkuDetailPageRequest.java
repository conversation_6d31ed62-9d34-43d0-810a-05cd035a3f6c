package com.wantwant.sfa.backend.bonusEvaluation.request;


import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("绩效提成明细")
public class PerformanceSkuDetailPageRequest extends PageParam {

    private Integer businessGroupId;

    @ApiModelProperty(value = "月份", required = true)
    @NotBlank(message = "月份")
    private String theYearMonth;

    @ApiModelProperty(value = "组织ID", required = true)
    @NotBlank(message = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "人员id", required = true)
    @NotNull(message = "人员id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "0:造旺本品,1:应有市场品,2:综合组品项")
    private Integer specialCommodity;

    @ApiModelProperty(value = "产品名称")
    private String skuName;

    @ApiModelProperty(value = "排序名称")
    private String sortName;

    @ApiModelProperty(value = "排序类型(asc正序 desc倒序)")
    private String sortType;

}
