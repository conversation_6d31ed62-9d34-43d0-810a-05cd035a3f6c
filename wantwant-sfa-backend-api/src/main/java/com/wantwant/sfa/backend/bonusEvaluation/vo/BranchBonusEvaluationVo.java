package com.wantwant.sfa.backend.bonusEvaluation.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "合伙人资金评定返回参数")
public class BranchBonusEvaluationVo {

  @ApiModelProperty(value = "是否提交(0.否；1.是)")
  private Integer isSubmit;

  private Integer employeeInfoId;

  @ApiModelProperty(value = "组织ID")
  private String organiztaionId;

  @ApiModelProperty(value = "大区组织名称")
  private String comapanyRegionName;

  @ApiModelProperty(value = "分公司组织名称")
  private String comapanyBranchName;

  @ApiModelProperty(value = "考核分类")
  private String companyAssessmentClassification;

  @ApiModelProperty(value = "合伙人姓名")
  private String partnerName;

  @ApiModelProperty(value = "考核月份")
  private String assessmentTime;

  @ApiModelProperty(value = "合伙人薪资方案")
  private String partnerSalaryScheme;

  @ApiModelProperty(value = "合伙人入职日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private LocalDateTime partnerOnboardDate;

  @ApiModelProperty(value = "合伙人离职日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private LocalDateTime partnerOffDate;

  @ApiModelProperty(value = "应出勤天数")
  private String partnerAttendanceDays;

  @ApiModelProperty(value = "实际出勤天数")
  private int partnerActualAttendanceDays;

  @ApiModelProperty(value = "盘价业绩")
  private Double partnerPerformance;

  @ApiModelProperty(value = "建档客户成交数")
  private Integer customerTransactionsNum;

  @ApiModelProperty(value = "设定标准底薪")
  private Double partnerStandardSalary;

  @ApiModelProperty(value = "设定业绩达成奖")
  private Double aimAchievementAward;

  @ApiModelProperty(value = "设定客户成交奖")
  private Double aimCustomerTransactionAward;

  @ApiModelProperty(value = "设定奖金上限")
  private Double bonusCap;

  @ApiModelProperty(value = "实际出勤底薪")
  private Double attendanceSalary;

  @ApiModelProperty(value = "实际品相利润")
  private Double productCommissionAmount;

  @ApiModelProperty(value = "实际业绩达成奖")
  private Double actualAchievementAward;

  @ApiModelProperty(value = "实际客户成交奖")
  private Double actualCustomerTransactionAward;

  @ApiModelProperty(value = "奖金合计")
  private Double totalAward;

  @ApiModelProperty(value = "薪资合计")
  private Double totalSalary;
}
