package com.wantwant.sfa.backend.bonusEvaluation.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "分公司资金评定返回参数")
public class CompanyBonusEvaluationVo {

  @ApiModelProperty(value = "是否提交(0.否；1.是)")
  private Integer isSubmit;

  private Integer employeeInfoId;

  @ApiModelProperty(value = "考核月份")
  private String assessmentTime;

  @ApiModelProperty(value = "组织ID")
  private String organiztaionId;

  @ApiModelProperty(value = "大区组织名称")
  private String comapanyRegionName;

  @ApiModelProperty(value = "分公司组织名称")
  private String comapanyBranchName;

  @ApiModelProperty(value = "区域总监姓名")
  private String managerName;

  @ApiModelProperty(value = "考核分类")
  private String companyAssessmentClassification;

  @ApiModelProperty(value = "入职日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private LocalDateTime managerOnboardDate;

  @ApiModelProperty(value = "离职日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private LocalDateTime managerOffDate;

  @ApiModelProperty(value = "应出勤天数")
  private Integer managerAttendanceDays;

  @ApiModelProperty(value = "实际出勤天数")
  private Integer managerActualAttendanceDays;

  @ApiModelProperty(value = "目标达成率")
  private Double managerGoalAchievementRate;

  @ApiModelProperty(value = "人效")
  private Integer managerHumanEffect;

  @ApiModelProperty(value = "设定标准底薪")
  private Double managerStandardSalary;

  @ApiModelProperty(value = "设定固定奖励")
  private Double managerFixedReward;

  @ApiModelProperty(value = "设定目标达成奖")
  private Double goalAchievementAward;

  @ApiModelProperty(value = "设定人效奖")
  private Double humanEffectAward;

  @ApiModelProperty(value = "设定薪资上限")
  private Double salaryCap;

  @ApiModelProperty(value = "实际出勤底薪")
  private Double actualAttendanceSalary;

  @ApiModelProperty(value = "实际固定奖励")
  private Double actualFixedReward;

  @ApiModelProperty(value = "实际目标达成奖")
  private Double actualGoalAchievementAward;

  @ApiModelProperty(value = "实际人效奖")
  private Double actualHumanEffectAward;

  @ApiModelProperty(value = "奖金合计")
  private Double totalAward;

  @ApiModelProperty(value = "薪资合计")
  private Double totalSalary;

  @ApiModelProperty(value = "数据更新时间")
  private LocalDateTime etlDate;
}
