package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "动态对象")
public class EvaluationDynamicVo {

    @ApiModelProperty(value = "列名称")
    private String columnName;

    @ApiModelProperty(value = "是否固定(0.否;1.是)")
    private Integer isFixed;

    @ApiModelProperty(value = "列宽")
    private Integer columnWidth;

    @ApiModelProperty(value = "最小列宽")
    private Integer minimumColumnWidth;

    @ApiModelProperty(value = "外部字段")
    private String externalFieldName;

    private List<EvaluationDynamicVo> list;

}