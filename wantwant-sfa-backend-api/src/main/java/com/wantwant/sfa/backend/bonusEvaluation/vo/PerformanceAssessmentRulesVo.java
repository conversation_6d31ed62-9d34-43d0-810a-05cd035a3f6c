package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "绩效考核规则")
public class PerformanceAssessmentRulesVo {

    @ApiModelProperty(name = "考核开始月份")
    private String assessmentMonthStart;

    @ApiModelProperty(name = "考核结束月份")
    private String assessmentMonthEnd;

    @ApiModelProperty(name = "岗位名称")
    private String positionName;

    @ApiModelProperty(name = "图片")
    private String picture;

    @ApiModelProperty(name = "文字")
    private String text;
}
