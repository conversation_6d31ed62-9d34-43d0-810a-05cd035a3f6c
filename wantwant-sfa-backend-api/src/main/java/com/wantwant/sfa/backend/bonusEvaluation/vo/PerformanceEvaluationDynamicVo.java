package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "绩效评定动态返回参数")
public class PerformanceEvaluationDynamicVo {

    @ApiModelProperty(value = "大区绩效字段返回")
    private List<EvaluationDynamicVo> evaluationDynamicArea;

    @ApiModelProperty(value = "分公司绩效字段返回")
    private List<EvaluationDynamicVo> evaluationDynamicCompany;

    @ApiModelProperty(value = "区域经理绩效字段返回")
    private List<EvaluationDynamicVo> evaluationDynamicdepartment;

    @ApiModelProperty(value = "合伙人绩效字段返回")
   private List<EvaluationDynamicVo> evaluationDynamicBranch;
}
