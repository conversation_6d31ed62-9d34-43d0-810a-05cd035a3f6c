package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "绩效评定list返回参数")
public class PerformanceEvaluationListVo {

    @ApiModelProperty(value = "绩效list")
    private List<PerformanceEvaluationMonthVo> list;

    @ApiModelProperty(value = "绩效数量")
    private Integer totalItem;

    @ApiModelProperty(value = "评定权限(1.事业主管;2.战区主管;3.分公司主管;10.区域经理;12大区主管;11省区主管)")
    private Integer scorePermissionsType;

    @ApiModelProperty(value = "评定权限(1.事业主管;2.战区主管;3.分公司主管;10.区域经理;12大区主管;11省区主管)")
    private List<Integer> scorePermissionsTypeAll;

}
