package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "绩效评定月份加集合返回参数")
public class PerformanceEvaluationMonthVo {

    @ApiModelProperty(name = "月份")
    private String month;

    @ApiModelProperty(value = "是否考核季度(0.否；1.是)")
    private int isQuarter;

    @ApiModelProperty(name = "绩效评定list")
    private List<PerformanceEvaluationVo>  list;
}
