package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "绩效奖金消息中间页排行")
public class PerformanceEvaluationTopVo {

    @ApiModelProperty(value = "原有考核月份(总督导，分公司会放季度)")
    private String assessmentMonth;

    @ApiModelProperty(value = "考核月份")
    private String assessmentMonthNew;

    @ApiModelProperty(value = "绩效奖金排行列表")
    private List<BigDecimal> performanceBonusCombinedList;

    @ApiModelProperty(value = "绩效奖金合计")
    private BigDecimal performanceBonusCombined;


}
