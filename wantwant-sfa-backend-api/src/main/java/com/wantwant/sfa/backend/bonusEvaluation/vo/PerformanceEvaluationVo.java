package com.wantwant.sfa.backend.bonusEvaluation.vo;

import com.wantwant.sfa.backend.performance.vo.ProcessManagmentRewardPunishmentInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "绩效评定返回参数")
public class PerformanceEvaluationVo {
    @ApiModelProperty("产品组")
    private String businessGroupName;

    private String organizationId;

    @ApiModelProperty(value = "战区组织名称")
    private String areaName;

    @ApiModelProperty(value = "大区组织名称")
    private String vareaName;

    @ApiModelProperty(value = "省区组织名称")
    private String provinceName;

    @ApiModelProperty(value = "分公司组织名称")
    private String companyName;

    @ApiModelProperty(value = "营业所组织名称")
    private String departmentName;

    @ApiModelProperty(value = "原有考核月份(总督导，分公司会放季度)")
    private String assessmentMonth;

    @ApiModelProperty(value = "考核月份")
    private String assessmentMonthNew;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "memberkey")
    private String memberkey;

    @ApiModelProperty(value = "身份证")
    private String identityCard;

    @ApiModelProperty(value = "手机号")
    private String mobileNumber;

    @ApiModelProperty(value = "employee_info表id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "入职日期")
    private String onboardDate;

    @ApiModelProperty(value = "离职日期")
    private String offDate;

    @ApiModelProperty(value = "入职公司")
    private String entryCorporation;

    @ApiModelProperty(value = "底薪")
    private BigDecimal baseSalary;

    @ApiModelProperty(value = "薪资方案")
    private String salaryPlan;

    //合伙人
    @ApiModelProperty(value = "绩效包")
    private BigDecimal performancePackage;

    @ApiModelProperty(value = "盘价业绩(全品项)")
    private BigDecimal allItemsPricePerformance;

    @ApiModelProperty(value = "发放比例(全品项)")
    private BigDecimal allItemsIssueProportion;

    @ApiModelProperty(value = "发放金额(全品项)")
    private BigDecimal allItemsIssueAmount;

    @ApiModelProperty(value = "人口目标(全品项)")
    private BigDecimal allItemsPopulationTarget;

    @ApiModelProperty(value = "达成率(全品项)")
    private BigDecimal allItemsAchievementRate;

    //分公司与大区返回共有的参数
    @ApiModelProperty(value = "固定发放(品项利润奖)")
    private BigDecimal fixedIssue;

    @ApiModelProperty(value = "品项利润奖")
    private BigDecimal itemProfitPrize;

    /**
     * 业绩目标达成
     * 2023/04/20 盘价业绩 字段 在大区，分公司，营业所在之前叫全品项业绩
     * 现在叫业绩达成的本月盘价业绩
     */

    @ApiModelProperty(value = "盘价业绩(业绩目标达成)---10.7.0(目标达成奖金/盘价业绩)--11.0.0(造旺本品业绩提成-业绩)")
    private BigDecimal targetPricePerformance;

    @ApiModelProperty(value = "人口目标(业绩目标达成)---10.7.0(目标达成奖金/目标)--11.0.0(造旺本品业绩提成-目标)")
    private Integer targetPopulationTarget;

    @ApiModelProperty(value = "达成率(业绩目标达成)---10.7.0(目标达成奖金/达成率)--11.0.0(造旺本品业绩提成-达成率)")
    private BigDecimal targetReachRate;

    @ApiModelProperty(value = "发放系数(业绩目标达成)---10.7.0(目标达成奖金/系数)")
    private BigDecimal targetIssueCoefficient;

    @ApiModelProperty(value = "发放金额(业绩目标达成)---10.7.0(目标达成奖金/奖金)")
    private BigDecimal targetIssueAmount;

    /**
     * 人均业绩
     */

    @ApiModelProperty(value = "人均业绩(人均业绩)")
    private BigDecimal perCapitaResults;

    @ApiModelProperty(value = "发放比例(人均业绩)")
    private BigDecimal perCapitaDistributionProportion;

    @ApiModelProperty(value = "发放金额(人均业绩)")
    private BigDecimal perCapitaDistributionAmount;

    /**
     *  在岗率目标达成
     */
    @ApiModelProperty(value = "达成率(在岗率目标达成)")
    private BigDecimal onJobreachRate;

    @ApiModelProperty(value = "发放系数(在岗率目标达成)")
    private BigDecimal onJobissueCoefficient;

    @ApiModelProperty(value = "发放金额(在岗率目标达成)")
    private BigDecimal onJobIssueAmount;

    /**
     *  B类品销售预估达成
     */

    @ApiModelProperty(value = "实际业绩(B类品销售预估达成)(全品项盘价金额)")
    private BigDecimal bclassActualPerformance;

    @ApiModelProperty(value = "销售预估(B类品销售预估达成)")
    private BigDecimal bclassSalesForecast;

    @ApiModelProperty(value = "达成率(B类品销售预估达成)")
    private BigDecimal bclassReachRate;

    @ApiModelProperty(value = "发放比例(B类品销售预估达成)(全品项发放系数)")
    private BigDecimal bclassDistributionProportion;

    @ApiModelProperty(value = "发放金额(B类品销售预估达成)(全品项发放金额)")
    private BigDecimal bclassDistributionAmount;

    //大区
    @ApiModelProperty(value = "品项利润---10.7.0(品项利润/造旺本品)")
    private BigDecimal itemProfit;

    @ApiModelProperty(value = "管理津贴")
    private BigDecimal managementAllowance;

    @ApiModelProperty(value = "岗位津贴")
    private BigDecimal postAllowance;

    //主管评分信息
    @ApiModelProperty(value = "满分奖金")
    private BigDecimal fullMarkBonus;

    @ApiModelProperty(value = "评分")
    private Integer score;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "默认100的发放金额")
    private Integer issueAmountBest;

    @ApiModelProperty(value = "发放金额")
    private BigDecimal issueAmount;

    @ApiModelProperty(value = "绩效奖金合计(去掉发放奖金)")
    private BigDecimal performanceBonus;

    @ApiModelProperty(value = "绩效奖金合计")
    private BigDecimal performanceBonusCombined;

    @ApiModelProperty(value = "绩效奖金合计原始")
    private BigDecimal performanceBonusCombinedPrimitive;

    @ApiModelProperty(value = "是否汰换(0.否;1.是)")
    private Integer isReplacing;

    @ApiModelProperty(value = "状态(0.暂存;1.确定)")
    private Integer status;

    @ApiModelProperty(value = "主推品list返回")
    private List<PerformanceMainProductVo>  performanceMainProductList;

    /**
     * 用人费用率
     */
    @ApiModelProperty(value = "用人费用率")
    private BigDecimal employmentRate;

    @ApiModelProperty(value = "用人费用率(发放比例)")
    private BigDecimal employmentDistributionProportion;

    @ApiModelProperty(value = "用人费用率(发放金额)")
    private BigDecimal employmentDistributionAmount;

    @ApiModelProperty(value = "人口人均业绩排名奖---12.7.0")
    private BigDecimal rankingAvgAward;

    @ApiModelProperty(value = "业绩排名奖---12.7.0")
    private BigDecimal rankingAward;

    @ApiModelProperty(value = "上月挂账---12.7.0")
    private BigDecimal remainingDeductionLm;

    @ApiModelProperty(value = "本月新增应扣---12.7.0")
    private BigDecimal totalDeductionCm;

    @ApiModelProperty(value = "本月新增应扣明细---12.7.0")
    List<ProcessManagmentRewardPunishmentInfoVo> rewardPunishmentList;

    @ApiModelProperty(value = "本月实扣---12.7.0")
    private BigDecimal actualDeductionCm;

    @ApiModelProperty(value = "本月挂帐---12.7.0")
    private BigDecimal remainingDeductionCm;

    @ApiModelProperty(value = "季度目标团队奖---13.2.0")
    private BigDecimal quarterlyTargetTeamAward;

    @ApiModelProperty(value = "补扣---10.7.0(补扣)")
    private BigDecimal reissue;

    @ApiModelProperty(value = "补发---10.7.0(补发)")
    private BigDecimal buttoning;

    @ApiModelProperty(value = "绩效奖金小计---10.7.0(绩效奖金小计)")
    private BigDecimal performanceBonusSubtotal;

    @ApiModelProperty(value = "在职期间业绩总占比---10.7.0(在职期间占比/品项利润占比)")
    private BigDecimal performanceRatio;

    @ApiModelProperty(value = "主推品业绩(业绩达成)")
    private BigDecimal mainProductPerformance;


    @ApiModelProperty(value = "10.7.0(主推品奖金/业绩)--主推品表")
    private BigDecimal mainProductActualResults;

    @ApiModelProperty(value = "10.7.0(主推品奖金/目标)--主推品表")
    private BigDecimal mainProductTargetResults;

    @ApiModelProperty(value = "10.7.0(主推品奖金/达成率)--主推品表")
    private BigDecimal mainProductReachRate;

    @ApiModelProperty(value = "10.7.0(主推品奖金/奖金)--主推品表")
    private BigDecimal mainProductIssueAmount;

    @ApiModelProperty(value = "奖金总包")
    private BigDecimal bonusPackage;

    @ApiModelProperty(value = "可领取奖金包")
    private BigDecimal receiveBonusPackage;

    @ApiModelProperty(value = "可领取奖金包原始")
    private BigDecimal receiveBonusPackagePrimitive;

    @ApiModelProperty(value = "发放金额原始")
    private BigDecimal issueAmountPrimitive;


    @ApiModelProperty(value = "盘价业绩(季度业绩达成)")
    private BigDecimal quarterPricePerformance;

    @ApiModelProperty(value = "主推品业绩(季度业绩达成)")
    private BigDecimal quarterMainPerformance;

    @ApiModelProperty(value = "直接利润率")
    private BigDecimal directProfitRate;

    @ApiModelProperty(value = "系数(直接利润率)")
    private BigDecimal directProfitRateCoefficient;

    @ApiModelProperty(value = "奖金(直接利润率)")
    private BigDecimal directProfitRateBonus;

    @ApiModelProperty(value = "经销商开发数(过程指标)")
    private BigDecimal dealerDevelopmentCourse;

    @ApiModelProperty(value = "优质陈列执行数(过程指标)")
    private BigDecimal qualityDisplayExecutionCourse;

    @ApiModelProperty(value = "新增终端焦点数(过程指标)")
    private BigDecimal terminalFocusNumberCourse;

    @ApiModelProperty(value = "奖金(过程指标)")
    private BigDecimal fundCourse;

    @ApiModelProperty(value = "综合组品项利润---10.7.0(品项利润/综合品项)")
    private BigDecimal assemblyGroupItemProfit;

    @ApiModelProperty(value = "个人综合组品相利润---10.7.0(个人在职期间绩效/综合组利润)--11.0.0(其它品项业绩提成-综合组品项")
    private BigDecimal personalItemProfitCombined;

    @ApiModelProperty(value = "个人集团品品相利润---10.7.0(个人在职期间绩效/集团品利润)--11.0.0(其它品项业绩提成-应有市场品项)")
    private BigDecimal personalItemProfitJt;

    @ApiModelProperty(value = "个人目标达成奖金---10.7.0(个人在职期间绩效/目标达成奖金)--11.0.0(造旺本品业绩提成-提成)")
    private BigDecimal personalTargetIssueAmount;

    @ApiModelProperty(value = "10.7.0(个人在职期间绩效/目标达成奖金)---主推品表")
    private BigDecimal personalMainProductBonus;

    @ApiModelProperty(value = "10.7.0(个人在职期间绩效合计--前端使用方便")
    private BigDecimal personalIncumbencyPerformanceTotal;

    @ApiModelProperty(value = "绩效奖金小计---11.0.0(绩效奖金小计)")
    private BigDecimal performanceBonusSubtotalBkd;

    @ApiModelProperty(value = "主管评分基数")
    private BigDecimal scoreBaseNumber;

    @ApiModelProperty(value = "过程管理奖金---10.7.0(过程管理奖金)")
    private BigDecimal processManagementBonuses;

    @ApiModelProperty(value = "在岗时间占比---10.7.0(在职期间占比/在岗时间占比)")
    private BigDecimal onJobTimeRate;

    @ApiModelProperty(value = "10.7.0(在职期间占比/主推品业绩占比)")
    private BigDecimal mainProductItemsSupplyTotalCmRate;

    @ApiModelProperty(value = "集团品品相利润---10.7.0(品项利润/集团品项)")
    private BigDecimal itemProfitJt;

    @ApiModelProperty(value = "奖金合计未扣罚---10.7.0  DF组使用(在职期间绩效(品项利润))")
    //绩效奖金-->df组没有评分 2024.7.4 本字段当作 绩效小计
    private BigDecimal performanceBonusCombinedUndeduction;

    @ApiModelProperty(value = "11.0.0(季度目标达成奖金-业绩)")
    private BigDecimal targetPricePerformanceQuarter;

    @ApiModelProperty(value = "11.0.0(季度目标达成奖金-目标)")
    private BigDecimal targetPopulationTargetQuarter;

    @ApiModelProperty(value = "11.0.0(季度目标达成奖金-达成率)")
    private BigDecimal targetReachRateQuarter;

    @ApiModelProperty(value = "11.0.0(季度目标达成奖金-奖金)")
    private BigDecimal targetBonusQuarter;

    @ApiModelProperty(value = "11.5.0(月度出勤率)")
    private BigDecimal monthAttendanceRate;

    @ApiModelProperty(value = "11.5.0(季度出勤率)")
    private BigDecimal quarterAttendanceRate;

    @ApiModelProperty(value = "11.5.0(月度实际出勤天数(包含节假日))")
    private BigDecimal monthAttendanceDays;

    @ApiModelProperty(value = "11.5.0(季度实际出勤天数(包含节假日))")
    private BigDecimal quarterAttendanceDays;

    @ApiModelProperty(value = "主岗标识")
    private BigDecimal partTime;

    @ApiModelProperty(value = "是否考核季度(0.否；1.是)")
    private int isQuarter;

    @ApiModelProperty(value = "岗位类型(1.战区;2.分公司;3.营业所;10.区域经理;12大区;11省区)")
    private Integer positionType;

    @ApiModelProperty(value = "岗位类型描述(1.战区;2.分公司;3.营业所;10.区域经理;12大区;11省区)")
    private String positionTypeDesc;


    @ApiModelProperty(value = "组织全名")
    private String fullName;

    @ApiModelProperty(value = "产品组排序sort")
    private Integer businessGroupSort;
}
