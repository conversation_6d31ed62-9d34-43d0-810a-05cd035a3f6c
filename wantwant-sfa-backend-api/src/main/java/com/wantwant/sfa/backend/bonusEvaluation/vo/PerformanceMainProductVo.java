package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "绩效评定主推品返回参数")
public class PerformanceMainProductVo {

    @ApiModelProperty(value = "主推品名称")
    private String mainProductName;

    @ApiModelProperty(value = "实际业绩")
    private BigDecimal actualResults;

    @ApiModelProperty(value = "目标业绩")
    private BigDecimal targetResults;

    @ApiModelProperty(value = "达成率")
    private BigDecimal reachRate;

    @ApiModelProperty(value = "发放系数")
    private BigDecimal issueCoefficient;

    @ApiModelProperty(value = "发放金额")
    private BigDecimal issueAmount;

    @ApiModelProperty(value = "在岗期间主推品业绩占比")
    private BigDecimal mainProductItemsSupplyTotalCmRate;

    @ApiModelProperty(value = "个人主推品奖金")
    private BigDecimal personalMainProductBonus;
}
