package com.wantwant.sfa.backend.bonusEvaluation.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("绩效提成明细")
public class PerformanceSkuDetailVo {

    @ApiModelProperty(value = "sku")
    @ExcelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "图片")
    @ExcelProperty(value = "图片")
    private String picUrl;

    @ApiModelProperty(value = "sku名称")
    @ExcelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "口味")
    @ExcelProperty(value = "口味")
    private String skuFlavor;

    @ApiModelProperty(value = "规格")
    @ExcelProperty(value = "规格")
    private String skuSpec;

    @ApiModelProperty(value = "sku名称规格口味")
    @ExcelIgnore
    private String skuNameSpecFlavor;

    @ApiModelProperty(value = "提成系数，返回5%")
    @ExcelProperty(value = "提成系数")
    private String profitRatio;

    @ApiModelProperty(value = "现金业绩")
    @ExcelProperty(value = "现金业绩")
    private BigDecimal itemsSupplyTotalCmCash;

    @ApiModelProperty(value = "提成业绩")
    @ExcelProperty(value = "提成金额")
    private BigDecimal commissionCmPersonal;

}
