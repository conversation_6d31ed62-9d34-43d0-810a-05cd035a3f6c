package com.wantwant.sfa.backend.bonusEvaluation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "部门目标返回参数")
@Data
public class SectionTargetVo {

    @ApiModelProperty(value = "部门名称")
    private String sectionName;

    @ApiModelProperty(value = "目标内容")
    private String targetContent;

    @ApiModelProperty(value = "业务组别")
    private Integer businessGroup;

}