package com.wantwant.sfa.backend.businessGroup.api;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupInfoUpdateRequest;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupSearchRequest;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupSortUpdateRequest;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupSearchVO;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupVo;
import com.wantwant.sfa.backend.organization.vo.BusinessOrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import retrofit2.http.POST;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/06/06/上午9:09
 */
@Api(value = "BusinessGroupApi",tags = "产品组API")
@RequestMapping("/businessGroup")
public interface BusinessGroupApi {

    @ApiOperation(value = "获取产品组信息", notes = "获取产品组信息", httpMethod = "GET")
    @GetMapping
    Response<List<BusinessGroupVo>> list();

    @ApiOperation(value = "获取业务组织信息" ,notes = "获取业务组织信息" ,httpMethod = "GET")
    @GetMapping("/organization")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "person",value="操作人",required = false),
            @ApiImplicitParam(name = "filterBusinessGroup",value="是否过滤业务组",required = false)
    })
    Response<List<BusinessOrganizationVo>> selectBusinessOrganization(@RequestParam(required = false) String person,@RequestParam(required = false)boolean filterBusinessGroup);

    @ApiOperation(value = "获取当前业务组名称" ,notes = "获取当前业务组名称" ,httpMethod = "GET")
    @GetMapping("/businessGroupName")
    Response<String> getCurrentBusinessGroupName();

    @ApiOperation(value = "查询当前产品组" ,notes = "查询当前产品组" ,httpMethod = "GET")
    @GetMapping("/search")
    Response<List<BusinessGroupSearchVO>> getFilterBusinessGroupName(BusinessGroupSearchRequest request);

    @ApiOperation(value = "更新产品组信息" ,notes = "更新产品组信息" ,httpMethod = "POST")
    @PostMapping("/update")
    Response updateBusinessGroupInfo(@RequestBody BusinessGroupInfoUpdateRequest request);

    @ApiOperation(value = "产品组排序" ,notes = "产品组排序" ,httpMethod = "POST")
    @PostMapping("/sort")
    Response updateBusinessGroupSort(@RequestBody BusinessGroupSortUpdateRequest request);

}
