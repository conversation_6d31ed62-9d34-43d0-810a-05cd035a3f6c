package com.wantwant.sfa.backend.businessGroup.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;


@Data
@ApiModel(value = "更新产品组信息")
@ToString
public class BusinessGroupInfoUpdateRequest {

    private Integer id;

    @ApiModelProperty("业务组名称")
    private String businessGroupName;

    @ApiModelProperty("业务组类型")
    private String businessGroupType;

    @ApiModelProperty("大图")
    private String largeIcon;

    @ApiModelProperty("小图")
    private String smallIcon;

    @ApiModelProperty("颜色")
    private String color;

    @ApiModelProperty("操作人工号")
    private String person;

    @ApiModelProperty("开启传入0，关闭传入1")
    private Integer forbiddenSignUp;

}
