package com.wantwant.sfa.backend.businessGroup.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "产品组")
@ToString
public class BusinessGroupRequest implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;
    @ApiModelProperty("产品组ID")
    private Integer businessGroup;

    @ApiModelProperty(value = "排除产品组Code")
    private List<String> excludeCodeList;

}
