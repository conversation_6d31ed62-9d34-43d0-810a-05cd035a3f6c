package com.wantwant.sfa.backend.businessGroup.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ApiModel(value = "查询所有产品组")
@ToString
public class BusinessGroupSearchRequest {


    @ApiModelProperty("产品组关键字")
    private String businessGroupNameKey;

    @ApiModelProperty("业务组类型")
    private String businessGroupType;

    @ApiModelProperty("是否无效")
    private Integer invalidate;

    @ApiModelProperty("事业部id")
    private String divisionId;

    @ApiModelProperty("区域")
    private String region;

}
