package com.wantwant.sfa.backend.businessGroup.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ApiModel(value = "更新产品组排序信息")
@ToString
public class BusinessGroupSortUpdateRequest {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("1标识上， 2标识下")
    private int type;

    @ApiModelProperty("操作人工号")
    private String person;
}
