package com.wantwant.sfa.backend.businessGroup.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "产品组")
@ToString
public class BusinessGroupSearchVO {

    private Integer id;

    @ApiModelProperty("业务组名称")
    private String businessGroupName;

    @ApiModelProperty("业务组CODE")
    private String businessGroupCode;

    @ApiModelProperty("业务组类型")
    private String businessGroupType;

    @ApiModelProperty("组织后缀字母")
    private String suffix;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("状态 0 标识有效 1无效")
    private Integer forbiddenSignUp;

    @ApiModelProperty("大图")
    private String largeIcon;

    @ApiModelProperty("小图")
    private String smallIcon;

    @ApiModelProperty("排序")
    private int sort;

    @ApiModelProperty("颜色")
    private String color;

}
