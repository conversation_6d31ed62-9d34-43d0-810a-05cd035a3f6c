package com.wantwant.sfa.backend.customerMaintain.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerIdRequest;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerListRequest;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerToPositionRequest;
import com.wantwant.sfa.backend.customerMaintain.request.SearchPositionListRequest;
import com.wantwant.sfa.backend.customerMaintain.request.VerifieRequest;
import com.wantwant.sfa.backend.customerMaintain.request.searchNameRequest;
import com.wantwant.sfa.backend.customerMaintain.vo.CustomerListResponse;
import com.wantwant.sfa.backend.customerMaintain.vo.EmployeeInfoResponse;
import com.wantwant.sfa.backend.customerMaintain.vo.PositionListResponse;
import com.wantwant.sfa.backend.customerMaintain.vo.PositionVo;
import com.wantwant.sfa.backend.customerMaintain.vo.VerifieInfoResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Api(value = "CustomerMaintainApi",tags = "客户管理相关接口")
@FeignClient(name = "wantwant-op-template1", url = "http://localhost:8080/op-demo")
public interface CustomerMaintainApi {

    @ApiOperation(value = "客户审核列表" ,notes = "客户审核列表" ,httpMethod = "POST")
    @PostMapping("/customermaintain/customerlist")
    Response<CustomerListResponse> customerList(@RequestBody CustomerListRequest request);
    
    @ApiOperation(value = "业务上级" ,notes = "业务上级" ,httpMethod = "POST")
    @PostMapping("/customermaintain/employee")
    Response<EmployeeInfoResponse> employeeInfo(@RequestBody CustomerIdRequest request);    
    
    @ApiOperation(value = "审核详情" ,notes = "审核详情" ,httpMethod = "POST")
    @PostMapping("/customermaintain/verifieinfo")
    Response<VerifieInfoResponse> verifieInfo(@RequestBody CustomerIdRequest request);
    
    @ApiOperation(value = "审核" ,notes = "审核" ,httpMethod = "POST")
    @PostMapping("/customermaintain/verifie")
    Response verifie(@RequestBody VerifieRequest request);
    
    @ApiOperation(value = "岗位搜索" ,notes = "岗位搜索" ,httpMethod = "POST")
    @PostMapping("/customermaintain/searchposition")
    Response<PositionListResponse> searchPosition(@RequestBody searchNameRequest request);
    
    @ApiOperation(value = "客户归属变更" ,notes = "客户归属变更" ,httpMethod = "POST")
    @PostMapping("/customermaintain/customertoposition")
    Response customerToPosition(@RequestBody CustomerToPositionRequest request);
    
    @ApiOperation(value = "客户历史归属" ,notes = "客户历史归属" ,httpMethod = "POST")
    @PostMapping("/customermaintain/customertopositionLog")
    Response<PositionVo> customerToPositionLog(@RequestBody CustomerIdRequest request);
    
    @ApiOperation(value = "列表导出", notes = "列表导出", httpMethod = "POST")
    @PostMapping("/customermaintain/customerlist/download")
    void export(@RequestBody CustomerListRequest request);

    @ApiOperation(value = "岗位列表" ,notes = "岗位列表" ,httpMethod = "POST")
    @PostMapping("/customermaintain/searchposition/tree")
    Response<PositionListResponse> searchPositionTree();
    
    @ApiOperation(value = "岗位列表" ,notes = "岗位列表" ,httpMethod = "POST")
    @PostMapping("/customermaintain/searchposition/list")
    Response<PositionListResponse> searchPositionList(@RequestBody SearchPositionListRequest request);

    @ApiOperation(value = "客户认证导入" ,notes = "客户认证导入" ,httpMethod = "POST")
    @PostMapping("/customermaintain/auth/import")
    Response importAuth(MultipartHttpServletRequest request);

}
