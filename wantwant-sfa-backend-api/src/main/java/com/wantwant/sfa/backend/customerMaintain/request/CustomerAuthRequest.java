package com.wantwant.sfa.backend.customerMaintain.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: rongwj
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @date: 2020/3/28 23:20
 */
@Data
@ApiModel("客户认证")
public class CustomerAuthRequest {   
	
    @ApiModelProperty(value ="客户号" ,required = true)
    @Excel(name = "客户编号")
    private String customerId; 
    
    @ApiModelProperty(value = "认证状态 1 认证成功,-1 认证失败,0未认证",required = true)
    @Excel(name = "是否认证(-1 认证失败,0未认证,1 认证成功)")
    private int isAuth; 
    
}
