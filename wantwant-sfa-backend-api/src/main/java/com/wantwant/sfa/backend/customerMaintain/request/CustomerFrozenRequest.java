package com.wantwant.sfa.backend.customerMaintain.request;

import com.wantwant.commons.core.base.query.AbstractQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.time.LocalDate;

import javax.validation.constraints.NotNull;

/**
 * @author: rongwj
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @date: 2020/3/28 23:20
 */
@Data
@ApiModel("客户冻结")
public class CustomerFrozenRequest {   
    
    @ApiModelProperty(value ="客户号" ,required = true)
    private String customerId; 
    
    @ApiModelProperty(value ="冻结状态 0未冻结,1冻结" ,required = true)
    private Integer isFrozen; 
    
    @ApiModelProperty(value ="冻结原因" ,required = true)
    private String frozenReason; 

    @ApiModelProperty(value = "操作人(暂有前端传值)",required = true)
    private String person;  
    
    public String getFrozenReason() {
    	return frozenReason == null ? "" : frozenReason;
    }
}
