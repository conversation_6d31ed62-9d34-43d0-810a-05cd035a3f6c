package com.wantwant.sfa.backend.customerMaintain.request;

import com.wantwant.commons.core.base.query.AbstractQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.time.LocalDate;

import javax.validation.constraints.NotNull;

/**
 * @author: rongwj
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @date: 2020/3/28 23:20
 */
@Data
@ApiModel("客户号")
public class CustomerIdRequest {   
    
    @ApiModelProperty(value ="客户号" ,required = true)
    private String customerId; 


}
