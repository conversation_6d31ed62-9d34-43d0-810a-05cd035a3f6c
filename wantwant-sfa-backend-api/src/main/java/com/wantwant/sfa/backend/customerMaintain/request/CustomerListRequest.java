package com.wantwant.sfa.backend.customerMaintain.request;

import com.wantwant.commons.core.base.query.AbstractQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @author: rongwj
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @date: 2020/3/28 23:20
 */
@Data
@ApiModel("审单列表查询请求对象")
public class CustomerListRequest extends AbstractQuery {

    @ApiModelProperty(value = "开始时间")
    private String beginDate;

    @ApiModelProperty("结束时间")
    private String endDate;
    
    @ApiModelProperty("证件号码")
    private String number;  
    
    @ApiModelProperty("客户搜索")
    private String searchCustomer;  
    
    @ApiModelProperty("业务搜索")
    private String searchBusiness;  
    
    @ApiModelProperty("大区")
    private String area;  
    
    @ApiModelProperty("分公司")
    private String company;  
    
    @ApiModelProperty("营业所")
    private String branch;      
    
    @ApiModelProperty("注册来源 0：sfa-app 1：旺铺-h5")
    private String source;
    
    @ApiModelProperty("特通4--1  特通--餐饮 4--2 特通--KTV 4--3 特通--其他 4–4 特通—网吧 4–5 特通—健身所  4–6 特通—企业")
    private String customerSubtype;  
    
    @ApiModelProperty("证件类型 1：身份证，2：营业执照（旧），3：⻝品流通许可证，4：统⼀社会信⽤代码（营业执照三证合一），5：烟草专卖零售许可证，6：餐饮服务许可证，7：食品经营许可证")
    private String licenseType; 
    
    @ApiModelProperty("客户类型")
    private String customerType;  
    
    @ApiModelProperty("审核状态 0 未审核,1 审核通过,2 驳回'")
    private String isVerified;

    @ApiModelProperty("1 sfa组织,2 123组织' 前端不需要传入")
    private int  channel;



}
