package com.wantwant.sfa.backend.customerMaintain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wn
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @date: 2021/3/17 13:20
 */
@Data
@ApiModel("搜索岗位列表请求对象")
public class SearchPositionListRequest {  
	
    @ApiModelProperty(value = "操作人(暂有前端传值)",required = true)
    private String person;
}
