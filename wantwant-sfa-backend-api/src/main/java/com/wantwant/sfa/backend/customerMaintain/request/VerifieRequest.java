package com.wantwant.sfa.backend.customerMaintain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: rongwj
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @date: 2020/3/28 23:20
 */
@Data
@ApiModel("客户编号请求对象")
public class VerifieRequest {   
    
    @ApiModelProperty(value = "审核编号",required = true)
    private String verifieId; 
    
    @ApiModelProperty(value = "审核状态 1 签核,2 驳回",required = true)
    private Integer isVerified; 
    
    @ApiModelProperty("处理意见")
    private String suggestions;       
    
    @ApiModelProperty(value ="客户号" ,required = true)
    private String customerId; 
    
    @ApiModelProperty("岗位Id")
    private String positionId;  

    @ApiModelProperty(value = "操作人(暂有前端传值)",required = true)
    private String person;  


}
