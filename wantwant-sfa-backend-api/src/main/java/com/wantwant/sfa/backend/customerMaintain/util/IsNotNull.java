package com.wantwant.sfa.backend.customerMaintain.util;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;

/**
 * <AUTHOR>
 * @Description 判空注解
 * @Date 18:07 2018/10/16
 * @Param
 * @return
 **/
@Target(FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface IsNotNull {

    /**
     * 是否可以为null
     * true 表示可以为null
     * @return
     */
    boolean isCanNull() default true;

    /**
     * 是否可以为空
     * true 表示可以为空
     * @return
     */
    boolean isCanEmpty() default true;
}
