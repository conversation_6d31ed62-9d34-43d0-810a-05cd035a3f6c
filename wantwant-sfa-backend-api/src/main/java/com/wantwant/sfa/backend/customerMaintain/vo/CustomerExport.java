package com.wantwant.sfa.backend.customerMaintain.vo;

import com.wantwant.sfa.backend.util.CustomerUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomerExport implements Serializable{	
    
    @Excel(name = "客户编号")
    private String customerId;  

    
	@Excel(name = "客户类型")
    private String customerTypeName ;  

    private int customerType;  
    
    private int customerSubtype;  
      
    @Excel(name = "客户名称")
    private String customerName;  
        
    @Excel(name = "门店名称")
    private String storeName;  
    
    @Excel(name = "手机号")
    private String customerMobile; 

	@Excel(name = "大区")
    private String area;  
    
	@Excel(name = "分公司")
    private String company;  
    
	@Excel(name = "营业所")
    private String branch;      
    
	@Excel(name = "专员工号")
    private String employeeId; 
    
    @Excel(name = "专员姓名")
    private String employeeName;  
    
    @Excel(name = "申请时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date requestDate; 
    
    @Excel(name = "证件号码")
    private String license;  
    
    @Excel(name = "审核状态",replace = {"未审核_0","审核通过_1","驳回_2","驳回_3"})
    private String isVerified;
    
    @Excel(name = "驳回次数",type =10)
    private int dismissedCount;
    
    @Excel(name = "注册渠道",replace = {"SFA-App_0","旺铺-h5_1"})
    private String source;
        
    @Excel(name = "操作人")
    private String person;  

    public String getCustomerTypeName() {
    	return CustomerUtils.transType(this.customerType,this.customerSubtype);
    }

    public String getLicense() {
    	return StringUtils.isBlank(license) ? "_" :license;
    }
    
    public String getBranch() {
    	return StringUtils.isBlank(branch) ? "_" :branch;
    }
    
    public String getCompany() {
    	return StringUtils.isBlank(company) ? "_" :company;
    }
    
    public String getEmployeeId() {
    	return StringUtils.isBlank(employeeId) ? "_" :employeeId;
    }
    
    public String getEmployeeName() {
    	return StringUtils.isBlank(employeeName) ? "_" :employeeName;
    }
    
    public String getCustomerMobile() {
    	return StringUtils.isBlank(customerMobile) ? "_" :customerMobile;
    }
        
}
