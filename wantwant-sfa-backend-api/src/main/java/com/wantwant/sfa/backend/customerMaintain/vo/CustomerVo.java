package com.wantwant.sfa.backend.customerMaintain.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustomerVo {
    @ApiModelProperty("大区")
    private String area;  
    
    @ApiModelProperty("分公司")
    private String company;  
    
    @ApiModelProperty("营业所")
    private String branch;  
    
    @ApiModelProperty("客户类型 ")
    private String customerType;  
    
    @ApiModelProperty("客户子类型")
    private String customerSubtype;  
    
    @ApiModelProperty("审核状态 0 未审核,1 审核通过,2 驳回'")
    private String isVerified;      
    
    @ApiModelProperty("客户编号")
    private String customerId;  
    
    @ApiModelProperty("客户名称")
    private String customerName;  
    
    @ApiModelProperty("实际门店名称")
    private String storeName; 
    
    @ApiModelProperty("证件门店名称")
    private String cerStoreName;  
    
    @ApiModelProperty("门店照片")
    private String storeImageUrl;  
    
    @ApiModelProperty("门店地址（省）")
    private String province;  
    
    @ApiModelProperty("门店地址（市）")
    private String city;  
    
    @ApiModelProperty("门店地址（区）")
    private String district;  
    
    @ApiModelProperty("门店地址（街道）")
    private String street;  
    
    @ApiModelProperty("收货 门店地址（省）")
    private String receiveProvince;  
    
    @ApiModelProperty("收货 门店地址（市）")
    private String receiveCity;  
    
    @ApiModelProperty("收货 门店地址（区）")
    private String receiveDistrict;  
    
    @ApiModelProperty("收货 门店地址（街道）")
    private String receiveStreet;  
    
    @ApiModelProperty("手机号")
    private String customerMobile; 
    
    @ApiModelProperty("专员工号")
    private String employeeId; 
    
    @ApiModelProperty("专员姓名")
    private String employeeName;  
    
    @ApiModelProperty("岗位id")
    private String positionId;  
    
    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date requestDate; 

    
    @ApiModelProperty("证件类型 1：身份证，2：营业执照（旧），3：⻝品流通许可证，4：统⼀社会信⽤代码（营业执照三证合一），5：烟草专卖零售许可证，6：餐饮服务许可证，7：食品经营许可证")
    private String licenseType;
    
    @ApiModelProperty("证件号码")
    private String license;  
    
    @ApiModelProperty("证件图片")
    private String lisenceImageName;  
    
    @ApiModelProperty("身份证号")
    private String iDCard; 
    
    @ApiModelProperty("身份证正面图片")
    private String iDCardFrontImage;  
    
    @ApiModelProperty("学生证图片")
    private String studentCardUrl;  
    
    @ApiModelProperty("学生证有效期")
    private Date studentCardValidity;   
    
    @ApiModelProperty("学生证号")
    private String studentCardNumber;  
    
    @ApiModelProperty("操作人")
    private String person;  
    
    @ApiModelProperty("操作人姓名")
    private String personName;

    @ApiModelProperty("注册来源 0：sfa-app 1：旺铺-h5")
    private String source;
        
}
