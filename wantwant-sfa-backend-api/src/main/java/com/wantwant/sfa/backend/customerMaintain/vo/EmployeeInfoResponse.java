package com.wantwant.sfa.backend.customerMaintain.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("业务上级返回对象")
public class EmployeeInfoResponse {
	
    @ApiModelProperty("大区")
    private String area;  
    
    @ApiModelProperty("分公司")
    private String company;  
    
    @ApiModelProperty("营业所")
    private String branch;  
	
	@ApiModelProperty(value = "业务姓名")
	String employeeName;	
	
	@ApiModelProperty(value = "工号")
	String employeeId;
	
	@ApiModelProperty(value = "入职时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	Date createDate;
	
	@ApiModelProperty(value = "总业绩")
	String sumPerformance;	
	
	@ApiModelProperty(value = "本月业绩")
	String monthPerformance;
	
	

}
