package com.wantwant.sfa.backend.customerMaintain.vo;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PositionVo {
    @ApiModelProperty("大区")
    private String area;  
    
    @ApiModelProperty("分公司")
    private String company;  
    
    @ApiModelProperty("营业所")
    private String branch;  
    
    @ApiModelProperty("岗位Id")
    private String positionId;  
    
    @ApiModelProperty("员工姓名")
    private String employeeName;  
    
    @ApiModelProperty("员工工号")
    private String employeeId;  
    
    @ApiModelProperty("岗位类型")
    private int positionType;  
    
    private String organizationId;
    private String organizationParentId;
    List<PositionVo> subPositionList = Lists.newArrayList();
  
    
}
