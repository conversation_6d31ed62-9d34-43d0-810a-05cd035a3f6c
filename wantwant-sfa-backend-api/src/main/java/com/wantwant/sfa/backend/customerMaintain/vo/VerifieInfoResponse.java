package com.wantwant.sfa.backend.customerMaintain.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("客户审核详情返回对象")
public class VerifieInfoResponse {
	
    @ApiModelProperty("表单编号")
    private String verifieId;  
    
    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date requestAt; 
    
    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date verifiedAt; 
    
    @ApiModelProperty("表单状态 0 未审核,1 审核通过,2 驳回")
    private String isVerified;  
        
    @ApiModelProperty("驳回意见")
    private List<String> dismissedSuggestions;      
    
    @ApiModelProperty("处理意见")
    private String verifieSuggestions; 
    
    @ApiModelProperty("审核记录")
    private List<CustomerVo> customerList;  
	
	@ApiModelProperty(value = "业务姓名")
	String employeeName;
	
	@ApiModelProperty("组织名称")
	String organizationName;	

    @ApiModelProperty("操作人")
    private String person;  
    
    @ApiModelProperty("操作人姓名")
    private String personName;    
  
    public String getEmployeeName() {
    	return employeeName == null ? "" : employeeName;
    }
    public String getOrganizationName() {
    	return organizationName == null ? "" : organizationName;
    }
}
