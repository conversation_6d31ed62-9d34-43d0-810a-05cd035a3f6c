package com.wantwant.sfa.backend.directOperatedGroup.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.directOperatedGroup.req.PurchasedItemsReq;
import com.wantwant.sfa.backend.directOperatedGroup.req.SystemClientCooperationProgressReq;
import com.wantwant.sfa.backend.directOperatedGroup.vo.PurchasedItemsListEnumsVo;
import com.wantwant.sfa.backend.directOperatedGroup.vo.PurchasedItemsVo;
import com.wantwant.sfa.backend.directOperatedGroup.vo.SystemClientCooperationProgressVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.controller
 * @Description:
 * @Date: 2024/11/5 8:51
 */

@Api(tags = "直营组实时数据相关接口")
@RequestMapping("/directOperatedGroup")
public interface DirectOperatedGroupRealtimeDataApi {

    @PostMapping("/cooperationProgress/list")
    @ApiOperation(value = "系统客户合作进度表", notes = "系统客户合作进度表")
    Response<IPage<SystemClientCooperationProgressVo>> systemClientCooperationProgressList(@RequestBody @Validated SystemClientCooperationProgressReq req);

    @PostMapping("/cooperationProgress/down")
    @ApiOperation(value = "系统客户合作进度表导出", notes = "系统客户合作进度表导出")
    void systemClientCooperationProgressDown(@RequestBody @Validated SystemClientCooperationProgressReq req, HttpServletRequest request, HttpServletResponse response);

    @PostMapping("/purchasedItems/list")
    @ApiOperation(value = "进货品项列表", notes = "进货品项列表")
    Response<IPage<PurchasedItemsVo>> purchasedItemsList(@RequestBody @Validated PurchasedItemsReq req);

    @PostMapping("/purchasedItems/enumsInfo")
    @ApiOperation(value = "进货品项列表枚举", notes = "进货品项列表枚举")
    Response<PurchasedItemsListEnumsVo> purchasedItemsListEnums(@RequestBody @Validated PurchasedItemsReq req);
    @PostMapping("/purchasedItems/down")
    @ApiOperation(value = "进货品项列表导出", notes = "进货品项列表导出")
    void purchasedItemsDown(@RequestBody @Validated PurchasedItemsReq req, HttpServletRequest request, HttpServletResponse response);


}
