package com.wantwant.sfa.backend.directOperatedGroup.req;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.req
 * @Description:
 * @Date: 2024/11/6 10:27
 */
@ApiModel("进货品项列表请求参数")
@Data
public class PurchasedItemsReq extends PageParam {

    @NotBlank(message = "月份不能为空")
    @ApiModelProperty(value = "月份:自然月(2024-05)/自然季(2024-Q2)/财务年(2024)")
    private String yearMonth;

    @NotBlank(message = "时间类型不能为空 10:自然月,11:自然季,2:财务年")
    @ApiModelProperty(value = "时间类型 10:自然月,11:自然季,2:财务年")
    private String dateTypeId;

    @ApiModelProperty(value = "组别",hidden = true)
    private Integer businessGroup;

    @ApiModelProperty("业务负责人岗位id")
    @NotBlank(message = "组织id不能为空")
    private String businessMainPostOrganizationId;

    @ApiModelProperty("区域系统id")
    @NotBlank(message = "区域系统id不能为空")
    private String systemRegionCode;

    @ApiModelProperty("品牌id")
    @NotBlank(message = "品牌id不能为空")
    private String regionBrandCode;

    @ApiModelProperty("线别")
    private String lineName;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("排序类型 desc/asc 默认desc")
    private String sortType;

    @ApiModelProperty("排序字段：直接输入排序字段名")
    private String sortName;

}
