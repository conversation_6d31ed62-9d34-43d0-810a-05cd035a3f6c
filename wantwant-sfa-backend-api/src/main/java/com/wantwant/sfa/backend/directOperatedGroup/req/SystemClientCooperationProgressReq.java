package com.wantwant.sfa.backend.directOperatedGroup.req;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.req
 * @Description:
 * @Date: 2024/11/6 9:10
 */
@ApiModel("系统客户合作进度表请求参数")
@Data
public class SystemClientCooperationProgressReq extends PageParam {


    @NotBlank(message = "月份不能为空")
    @ApiModelProperty(value = "月份:自然月(2024-05)/自然季(2024-Q2)/财务年(2024)")
    private String yearMonth;

    @NotBlank(message = "时间类型不能为空 10:自然月,11:自然季,2:财务年")
    @ApiModelProperty(value = "时间类型 10:自然月,11:自然季,2:财务年")
    private String dateTypeId;

    @ApiModelProperty(value = "组别",hidden = true)
    private Integer businessGroup;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "组织类型",hidden = true)
    private String organizationType;

    @ApiModelProperty("系统类型：1-全国NKA系统 2-地方LKA系统 3-地方AB类连锁 4-地方CVS系统")
    private Integer systemType;

    @ApiModelProperty("业务负责人姓名 支持模糊查询")
    private String businessEmployeeName;

    @ApiModelProperty("系统名称 支持模糊查询")
    private String systemRegionName;

    @ApiModelProperty("系统客户 支持模糊查询")
    private String systemCustomer;
    @ApiModelProperty("合伙人MemberKey")
    private String partnerMemberKey;

    @ApiModelProperty("覆盖省份")
    private String systemCoverageProvince;

    @ApiModelProperty("覆盖城市")
    private String systemCoverageCity;

    @ApiModelProperty(value = "合伙人采购类型")
    private String partnerProcurementType;

    @ApiModelProperty("排序类型 desc/asc 默认desc")
    private String sortType;

    @ApiModelProperty("排序字段：直接输入排序字段名")
    private String sortName;
}
