package com.wantwant.sfa.backend.directOperatedGroup.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.vo
 * @Description:
 * @Date: 2024/11/8 15:28
 */
@Data
public class PurchasedItemsListEnumsVo {
    @ApiModelProperty("产品线列表")
    private List<String> lineNames;
    @ApiModelProperty("sku信息列表")
    private List<Map<String,String>> skuInfos;
}
