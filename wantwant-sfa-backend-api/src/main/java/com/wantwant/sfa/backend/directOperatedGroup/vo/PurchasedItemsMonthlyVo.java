package com.wantwant.sfa.backend.directOperatedGroup.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.vo
 * @Description:
 * @Date: 2024/11/7 10:02
 */
@Data
public class PurchasedItemsMonthlyVo {

    @ApiModelProperty("线别")
    @ExcelProperty(value = "线别",index = 0)
    private String lineName;

    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码",index = 1)
    private String skuId;

    @ApiModelProperty("商品图片")
    @ExcelProperty(value = "商品图片",index = 2)
    private String skuImages;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = "产品名称",index = 3)
    private String skuName;

    @ApiModelProperty("口味")
    @ExcelProperty(value = "口味",index = 4)
    private String flavor;

    @ExcelProperty(value = "规格",index = 5)
    @ApiModelProperty("规格")
    private String skuSpec;

    /**
     * 业绩
     */
    @ExcelProperty(value = {"本月业绩","业绩"},index = 6)
    @ApiModelProperty(value = "业绩")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performance;

    @ExcelProperty(value = {"本月业绩","业绩环比"},index = 7)
    @ApiModelProperty(value = "业绩环比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceChainRatio;

    @ExcelProperty(value = {"本月业绩","业绩同比"},index = 8)
    @ApiModelProperty(value = "业绩同比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceYearRatio;

    @ExcelProperty(value = {"本月业绩","旺金币折扣率"},index = 9)
    @ApiModelProperty("业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "506")
    private BigDecimal performanceWantGoldDiscountRatio;

    @ExcelProperty(value = {"本月业绩","目标"},index = 10)
    @ApiModelProperty(value = "业绩目标-月目标")
    @PerformanceValue(serialNumber = "378")
    private BigDecimal goal;

    @ExcelProperty(value = {"本月业绩","目标达成率"},index = 11)
    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    private BigDecimal performanceAchievementRate;
}
