package com.wantwant.sfa.backend.directOperatedGroup.vo;

import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.vo
 * @Description:
 * @Date: 2024/11/6 10:28
 */
@ApiModel("进货品项列表返回参数")
@Data
public class PurchasedItemsVo {

    @ApiModelProperty("区域系统id")
    private String systemRegionCode;

    @ApiModelProperty("品牌id")
    private String regionBrandCode;

    @ApiModelProperty("线别")
    private String lineName;

    @ApiModelProperty("物料编码")
    private String skuId;

    @ApiModelProperty("商品图片")
    private String skuImages;

    @ApiModelProperty("商品名称")
    private String skuName;

    @ApiModelProperty("口味")
    private String flavor;

    @ApiModelProperty("规格")
    private String skuSpec;

    /**
     * 业绩
     */
    @ApiModelProperty(value = "月份:自然月(2024-05)/自然季(2024-Q2)/财务年(2024)")
    private String yearMonth;

    @ApiModelProperty(value = "时间类型 10:自然月,11:自然季,2:财务年")
    private String dateTypeId;

    @ApiModelProperty(value = "业绩")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performance;

    @ApiModelProperty(value = "业绩环比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceChainRatio;

    @ApiModelProperty(value = "业绩同比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceYearRatio;

    @ApiModelProperty("业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "506")
    private BigDecimal performanceWantGoldDiscountRatio;

    @ApiModelProperty(value = "业绩目标-月目标")
    @PerformanceValue(serialNumber = "378")
    private BigDecimal goal;

    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    private BigDecimal performanceAchievementRate;

    @ApiModelProperty("财年和季度额外的业绩信息--字段不支持排序")
    private List<PurchasedItemsPerformanceVo> performanceList;


}
