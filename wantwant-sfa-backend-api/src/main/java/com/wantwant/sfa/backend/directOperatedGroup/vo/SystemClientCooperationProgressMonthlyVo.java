package com.wantwant.sfa.backend.directOperatedGroup.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.vo
 * @Description:
 * @Date: 2024/11/6 11:02
 */
@ApiModel("系统客户合作进度表月度数据返回参数")
@Data
public class SystemClientCooperationProgressMonthlyVo {

    @ApiModelProperty("覆盖省份")
    @ExcelProperty(value = "省份",index = 0)
    private String systemCoverageProvince;

    @ApiModelProperty("覆盖省份人口数")
    @ExcelProperty(value = "人口数",index = 1)
    private BigDecimal systemCoverageProvincePopulation;

    @ApiModelProperty("覆盖城市")
    @ExcelProperty(value = "城市",index = 2)
    private String systemCoverageCity;

    @ApiModelProperty("覆盖省份人口数")
    @ExcelProperty(value = "人口数",index = 3)
    private BigDecimal systemCoverageCityPopulation;


    @ApiModelProperty("业务负责人姓名")
    @ExcelProperty(value = "业务负责人",index = 4)
    private String businessEmployeeName;

    /**
     * 渠道
     */
    @ApiModelProperty("系统类型")
    @ExcelProperty(value = {"渠道","系统类型"},index = 5)
    private String systemTypeDesc;


    @ApiModelProperty("系统名称")
    @ExcelProperty(value = {"渠道","系统名称"},index = 6)
    private String systemRegionName;

    @ApiModelProperty("系统门头照")
    @ExcelProperty(value = {"渠道","系统门头照"},index = 7)
    private String systemBrandHeaderImgs;

    @ApiModelProperty("系统年销售额")
    @ExcelProperty(value = {"渠道","系统规模(年销售额)"},index = 8)
    private BigDecimal systemAnnualSales;

    @ApiModelProperty("系统家数")
    @ExcelProperty(value = {"渠道","总数"},index = 9)
    private Integer systemCount;

    /**
     * 业绩
     */

    @ApiModelProperty(value = "业绩")
    @PerformanceValue(serialNumber = "405")
    @ExcelProperty(value = {"业绩达成","本月业绩","业绩"},index = 10)
    private BigDecimal performance;

    @ApiModelProperty(value = "业绩环比")
    @PerformanceValue(serialNumber = "405")
    @ExcelProperty(value = {"业绩达成","本月业绩","环比"},index = 11)
    private BigDecimal performanceChainRatio;

    @ApiModelProperty(value = "业绩同比")
    @PerformanceValue(serialNumber = "405")
    @ExcelProperty(value = {"业绩达成","本月业绩","同比"},index = 12)
    private BigDecimal performanceYearRatio;

    @ApiModelProperty("业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "506")
    @ExcelProperty(value = {"业绩达成","本月业绩","旺金币折扣率"},index = 13)
    private BigDecimal performanceWantGoldDiscountRatio;

    @ApiModelProperty(value = "业绩目标-月目标")
    @PerformanceValue(serialNumber = "378")
    @ExcelProperty(value = {"业绩达成","本月业绩","目标"},index = 14)
    private BigDecimal goal;

    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    @ExcelProperty(value = {"业绩达成","本月业绩","目标达成率"},index = 15)
    private BigDecimal performanceAchievementRate;

    /**
     * 采购类型
     */
    @ExcelProperty(value = {"采购类型","采购类型"},index = 16)
    @ApiModelProperty(value = "合伙人采购类型")
    private String partnerProcurementType;

    @ExcelProperty(value = {"采购类型","手机号"},index = 17)
    @ApiModelProperty(value = "合伙人手机号")
    private String partnerMobile;

    @ApiModelProperty(value = "合伙人姓名")
    @ExcelProperty(value = {"采购类型","合伙人名称"},index = 18)
    private String partnerName;

}
