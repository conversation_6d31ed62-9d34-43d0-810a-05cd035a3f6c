package com.wantwant.sfa.backend.directOperatedGroup.vo;

import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.vo
 * @Description:
 * @Date: 2024/11/6 13:23
 */
@ApiModel("系统客户合作进度业绩vo")
@Data
public class SystemClientCooperationProgressPerformanceVo {

    @ApiModelProperty(value = "月份:自然月(2024-05)/自然季(2024-Q2)/财务年(2024)")
    private String yearMonth;

    @ApiModelProperty(value = "时间类型 10:自然月,11:自然季,2:财务年")
    private String dateTypeId;

    @ApiModelProperty(value = "业绩")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performance;

    @ApiModelProperty(value = "业绩环比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceChainRatio;

    @ApiModelProperty(value = "业绩同比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceYearRatio;

    @ApiModelProperty("业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "506")
    private BigDecimal performanceWantGoldDiscountRatio;

    @ApiModelProperty(value = "业绩目标-月目标")
    @PerformanceValue(serialNumber = "378")
    private BigDecimal goal;

    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    private BigDecimal performanceAchievementRate;
}
