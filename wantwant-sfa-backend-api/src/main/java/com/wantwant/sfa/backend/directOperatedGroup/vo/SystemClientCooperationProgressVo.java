package com.wantwant.sfa.backend.directOperatedGroup.vo;

import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup.vo
 * @Description:
 * @Date: 2024/11/6 9:10
 */
@ApiModel("系统客户合作进度表返回参数")
@Data
public class SystemClientCooperationProgressVo {

    @ApiModelProperty("区域系统id")
    private String systemRegionCode;

    @ApiModelProperty("品牌id")
    private String regionBrandCode;

    @ApiModelProperty("覆盖省份")
    private String systemCoverageProvince;

    @ApiModelProperty("覆盖省份人口数")
    private BigDecimal systemCoverageProvincePopulation;

    @ApiModelProperty("覆盖城市")
    private String systemCoverageCity;

    @ApiModelProperty("覆盖省份人口数")
    private BigDecimal systemCoverageCityPopulation;

    @ApiModelProperty("业务负责人岗位id")
    private String businessMainPostOrganizationId;

    @ApiModelProperty("业务负责人姓名")
    private String businessEmployeeName;
    @ApiModelProperty("负责人状态")
    private String businessEmployeeStatus;
    @ApiModelProperty("负责人面试流程ID")
    private Integer businessInterviewRecordId;
    @ApiModelProperty("负责人岗位类型ID")
    private Integer businessPositionTypeId;
    @ApiModelProperty("负责人memberKey")
    private Long businessMemberkey;
    /**
     * 渠道
     */
    @ApiModelProperty("系统类型描述")
    private String systemTypeDesc;

    @ApiModelProperty("系统类型")
    private Integer systemType;

    @ApiModelProperty("系统名称")
    private String systemRegionName;

    @ApiModelProperty("系统门头照")
    private List<String> systemBrandHeaderImg;

    @ApiModelProperty("系统年销售额")
    private BigDecimal systemAnnualSales;

    @ApiModelProperty("系统家数")
    private Integer systemCount;

    /**
     * 业绩
     */
    @ApiModelProperty(value = "月份:自然月(2024-05)/自然季(2024-Q2)/财务年(2024)")
    private String yearMonth;

    @ApiModelProperty(value = "时间类型 10:自然月,11:自然季,2:财务年")
    private String dateTypeId;

    @ApiModelProperty(value = "业绩")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performance;

    @ApiModelProperty(value = "业绩环比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceChainRatio;

    @ApiModelProperty(value = "业绩同比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceYearRatio;

    @ApiModelProperty("业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "506")
    private BigDecimal performanceWantGoldDiscountRatio;

    @ApiModelProperty(value = "业绩目标-月目标")
    @PerformanceValue(serialNumber = "378")
    private BigDecimal goal;

    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    private BigDecimal performanceAchievementRate;

    @ApiModelProperty("财年和季度额外的业绩信息--字段不支持排序")
    private List<SystemClientCooperationProgressPerformanceVo> performanceList;

    /**
     * 采购类型
     */
    @ApiModelProperty(value = "合伙人采购类型")
    private String partnerProcurementType;
    @ApiModelProperty(value = "合伙人memberKey")
    private String partnerMemberKey;
    @ApiModelProperty(value = "合伙人姓名")
    private String partnerName;
    @ApiModelProperty(value = "合伙人手机号")
    private String partnerMobile;
}
