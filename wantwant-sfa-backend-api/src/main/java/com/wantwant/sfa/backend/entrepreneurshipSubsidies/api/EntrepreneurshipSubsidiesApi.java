package com.wantwant.sfa.backend.entrepreneurshipSubsidies.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.Entrepreneurship;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.EntrepreneurshipSubsidiesAudit;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.EntrepreneurshipSubsidiesDetailRequest;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.EntrepreneurshipSubsidiesRequest;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo.EntrepreneurshipSubsidiesDetailVo;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo.EntrepreneurshipVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "EntrepreneurshipSubsidiesApi",tags = "企业合伙人创业补贴Api")
public interface EntrepreneurshipSubsidiesApi {

    @ApiOperation(value = "提交创业补贴申请数据", notes = "提交创业补贴申请数据", httpMethod = "POST")
    @PostMapping("/entrepreneurshipSubsidies/addEntrepreneurshipSubsidiesApply")
    Response addEntrepreneurshipSubsidiesApply(
            @RequestBody Entrepreneurship request);

    @ApiOperation(value = "获取创业补贴详情", notes = "获取创业补贴详情", httpMethod = "POST")
    @PostMapping("/entrepreneurshipSubsidies/getEntrepreneurshipSubsidiesDetail")
    Response<EntrepreneurshipSubsidiesDetailVo> getEntrepreneurshipSubsidiesDetail(
            @RequestBody @Validated EntrepreneurshipSubsidiesDetailRequest request);

    @ApiOperation(value = "创业补贴审核", notes = "创业补贴审核", httpMethod = "POST")
    @PostMapping("/entrepreneurshipSubsidies/entrepreneurshipSubsidiesAudit")
    Response<Integer> entrepreneurshipSubsidiesAudit(
            @RequestBody @Validated EntrepreneurshipSubsidiesAudit request);


    @ApiOperation(value = "创业补贴发放", notes = "创业补贴发放", httpMethod = "POST")
    @PostMapping("/entrepreneurshipSubsidies/entrepreneurshipSubsidiesIssue")
    Response<Integer> entrepreneurshipSubsidiesIssue(
            @RequestBody @Validated EntrepreneurshipSubsidiesAudit request);
}
