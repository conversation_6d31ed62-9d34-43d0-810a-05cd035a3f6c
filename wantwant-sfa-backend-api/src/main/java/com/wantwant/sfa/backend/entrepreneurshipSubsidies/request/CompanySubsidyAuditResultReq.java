package com.wantwant.sfa.backend.entrepreneurshipSubsidies.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "旺铺审核结果返回参数")
@Data
public class CompanySubsidyAuditResultReq {

   @ApiModelProperty(value = "applyIds")
    private List<String> applyIds;

    @ApiModelProperty(value = "审核状态(2-驳回 3-通过 4-已发放)")
    private Integer auditStatus;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;
}
