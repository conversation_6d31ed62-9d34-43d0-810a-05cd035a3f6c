package com.wantwant.sfa.backend.entrepreneurshipSubsidies.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "创业补贴传参")
public class Entrepreneurship {

    @ApiModelProperty(value = "申请编号")
    private String applyId;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "申请补贴年月")
    private String applySubsidiesTime;

    @ApiModelProperty(value = "合伙人memberkey")
    private String partnerMemberkey;

    @ApiModelProperty(value = "发放方式(1.现金;2.旺金币)")
    private Integer issueWay;

    @ApiModelProperty(value = "发票图片")
    private String invoiceUrl;

    @ApiModelProperty(value = "盘价金额")
    private BigDecimal enterpriseAmount;

    @ApiModelProperty(value = "应发金额(补贴金额)")
    private BigDecimal subsidiesAmount;
}
