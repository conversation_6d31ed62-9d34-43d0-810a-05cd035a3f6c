package com.wantwant.sfa.backend.entrepreneurshipSubsidies.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "补贴审核")
@Data
public class EntrepreneurshipSubsidiesAudit {

    @ApiModelProperty(value = "创业补贴主键id")
    private List<Integer> subsidiesApplyId;

    @ApiModelProperty(value = "申请编号id")
    private List<String> applyId;

    @ApiModelProperty(value = "操作状态:1通过：2.不通过")
    @NotNull(message = "操作状态不为空")
    private Integer processResult;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "操作人")
    @NotBlank(message = "操作人不能为空")
    private String person;
}
