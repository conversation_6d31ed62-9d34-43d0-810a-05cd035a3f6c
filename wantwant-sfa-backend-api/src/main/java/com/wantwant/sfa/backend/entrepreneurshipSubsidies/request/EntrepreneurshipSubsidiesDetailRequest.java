package com.wantwant.sfa.backend.entrepreneurshipSubsidies.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(value = "合伙人详情补贴传参")
@Data
public class EntrepreneurshipSubsidiesDetailRequest {

    @ApiModelProperty(value = "是否详情(0.否;1.是)")
    @NotNull(message = "是否详情不能为空")
    private Integer isDetails;

    @ApiModelProperty(value = "合伙人memberkey")
    @NotBlank(message = "合伙人memberkey不能为空")
    private String partnerMemberkey;

    @ApiModelProperty(value = "申请补贴年月")
    @NotBlank(message = "申请补贴年月不能为空")
    private String applySubsidiesTime;

    @NotNull(message = "申请补贴id不能为空")
    @ApiModelProperty(value = "申请补贴id")
    private Integer subsidiesApplyId;

}
