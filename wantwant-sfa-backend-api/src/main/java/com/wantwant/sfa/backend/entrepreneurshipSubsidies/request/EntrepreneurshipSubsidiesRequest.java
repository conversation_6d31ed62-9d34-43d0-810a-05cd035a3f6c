package com.wantwant.sfa.backend.entrepreneurshipSubsidies.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "合伙人创业补贴")
@Data
public class EntrepreneurshipSubsidiesRequest {

    @ApiModelProperty(value = "大区组织id")
    private String areaId;

    @ApiModelProperty(value = "分公司组织id")
    private String companyId;

    @ApiModelProperty(value = "区域经理组织id")
    private String branchId;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "手机号或姓名")
    private String partner;

    @ApiModelProperty(value = "补贴年月")
    private String applySubsidiesTime;

    @ApiModelProperty(value = "发放方式(1.现金;2.旺金币)")
    private Integer issueWay;

    @ApiModelProperty(value = "状态(0.待审核;1.待发放;2.已发放;3.已驳回)")
    private Integer status;

    private Integer subsidiesApplyId;

    @ApiModelProperty(value = "操作人")
    @NotBlank(message = "操作人不能为空")
    private String person;

    @ApiModelProperty(value = "是否审核页面(0.否;1.是)")
    @NotBlank(message = "是否审核页面不能为空")
    private Integer isAudit;

}
