package com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "合伙人创业补贴明细list返回参数")
@Data
public class EntrepreneurshipSubsidiesDateListVo {


    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "合伙人姓名")
    private String partnerName;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "下单日期")
    private String orderTime;

    @ApiModelProperty(value = "订单编号")
    private String orderSerialNumber;

    @ApiModelProperty(value = "箱数")
    private Integer quantity;

    @ApiModelProperty(value = "盘价金额")
    private BigDecimal enterpriseAmount;

    @ApiModelProperty(value = "销售金额")
    private BigDecimal salesAmount;

    @ApiModelProperty(value = "利润")
    private BigDecimal profits;

    @ApiModelProperty(value = "支付流水号")
    private String payRunningWater;

    @ApiModelProperty(value = "sku编码")
    private String sku;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "商品标签")
    private String commodityTag;

}
