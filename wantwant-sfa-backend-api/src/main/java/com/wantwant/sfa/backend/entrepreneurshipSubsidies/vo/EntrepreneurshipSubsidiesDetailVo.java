package com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@ApiModel(value = "合伙人创业补贴明细返回参数")
@Data
public class EntrepreneurshipSubsidiesDetailVo {

    @ApiModelProperty(value = "申请补贴年月")
    private String applySubsidiesTime;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "盘价金额")
    private BigDecimal enterpriseAmount;

    @ApiModelProperty(value = "申请时间")
    private LocalDate applyTime;

    @ApiModelProperty(value = "合伙人姓名")
    private String partnerName;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "应发金额(补贴金额)")
    private BigDecimal subsidiesAmount;

    @ApiModelProperty(value = "发票图片")
    private String invoiceUrl;

    @ApiModelProperty(value = "审核日期")
    private LocalDate processTime;

    @ApiModelProperty(value = "审核结果")
    private String status;

    @ApiModelProperty(value = "审核人")
    private String processPerson;

    @ApiModelProperty(value = "审核意见")
    private String note;

    @ApiModelProperty(value = "是否标红(0,否;1.是)")
    private int isRed;

    @ApiModelProperty(value = "订单返回list")
    private List<EntrepreneurshipSubsidiesDateListVo> entrepreneurshipSubsidiesDateListVo;

}
