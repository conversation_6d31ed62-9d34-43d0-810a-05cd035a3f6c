package com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@ApiModel(value = "合伙人创业补贴列表返回参数")
@Data
public class EntrepreneurshipSubsidiesVo {

    @ApiModelProperty(value = "大区名称")
    private String areaName;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "营业所名称")
    private String departmentName;

    @ApiModelProperty(value = "合伙人姓名")
    private String partnerName;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "状态(0.待审核;1.待发放;2.已发放;3.已驳回)")
    private String status;

    @ApiModelProperty(value = "申请补贴年月")
    private String applySubsidiesTime;

    @ApiModelProperty(value = "盘价金额")
    private BigDecimal enterpriseAmount;

    @ApiModelProperty(value = "应发金额(补贴金额)")
    private BigDecimal subsidiesAmount;

    @ApiModelProperty(value = "发放方式(0.现金;1.旺金币)")
    private String issueWay;

    @ApiModelProperty(value = "发票图片")
    private String invoiceUrl;

    @ApiModelProperty(value = "申请时间")
    private LocalDate applyTime;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "审核日期")
    private LocalDate processTime;

    @ApiModelProperty(value = "审核人")
    private String processPerson;

    @ApiModelProperty(value = "合伙人memberkey")
    private String partnerMemberkey;

    @ApiModelProperty(value = "申请补贴主键id")
    private Integer subsidiesApplyId;

    @ApiModelProperty(value = "申请编号id")
    private String applyId;

}
