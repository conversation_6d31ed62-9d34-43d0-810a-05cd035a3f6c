package com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "合伙人创业补贴列表返回参数")
@Data
public class EntrepreneurshipVo {

    @ApiModelProperty(value = "创业补贴列表")
    private List<EntrepreneurshipSubsidiesVo> list;

    @ApiModelProperty(value = "是否是财务(0.否;1.是)")
    private int isFinancial;

    @ApiModelProperty(value = "数量")
    private int totalItem;

}
