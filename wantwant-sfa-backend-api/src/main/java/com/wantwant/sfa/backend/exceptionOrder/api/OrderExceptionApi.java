package com.wantwant.sfa.backend.exceptionOrder.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.exceptionOrder.request.OrderExceptionProcessRequest;
import com.wantwant.sfa.backend.exceptionOrder.request.OrderExceptionSearchRequest;
import com.wantwant.sfa.backend.exceptionOrder.vo.ExceptionOrderDetailVO;
import com.wantwant.sfa.backend.exceptionOrder.vo.ExceptionOrderVO;
import com.wantwant.sfa.backend.exceptionOrder.vo.OrderExceptionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Description: 订单异常API。
 * @Auther: zhengxu
 * @Date: 2021/08/11/下午2:05
 */
@Api(value = "EmployeeApi",tags = "异常订单管理接口")
public interface OrderExceptionApi {

    @ApiOperation(value = "导入异常订单模版" ,notes = "导入异常订单模版" ,httpMethod = "PUT")
    @PutMapping("/exception/upload")
    Response upload(@RequestParam(value = "file", required = true) MultipartFile file,
                    @RequestParam(value = "personId", required = true) String personId) throws IOException;

    @ApiOperation(value = "异常预警信息" ,notes = "异常预警信息" ,httpMethod = "GET")
    @GetMapping("/exception/alertInfo")
    Response<OrderExceptionVO> alertInfo();

    @ApiOperation(value = "异常订单列表" ,notes = "异常订单列表" ,httpMethod = "POST")
    @PostMapping("/exception/list")
    Response<Page<ExceptionOrderVO>> list(@RequestBody OrderExceptionSearchRequest request);

    @ApiOperation(value = "获取异常类型" ,notes = "获取异常类型" ,httpMethod = "GET")
    @GetMapping("/exception/items")
    Response<List<String>> getExceptionItem();

    @ApiOperation(value = "获取异常订单详情" ,notes = "获取异常类型" ,httpMethod = "POST")
    @GetMapping("/order/{orderKey}")
    Response<ExceptionOrderDetailVO> getOrderDetail(@PathVariable String orderKey);

    @ApiOperation(value = "异常处理",notes = "获取异常类型" ,httpMethod = "POST")
    @PostMapping("/exception/process")
    Response process(@RequestBody OrderExceptionProcessRequest request);

    @ApiOperation(value = "获取异常订单详情" ,notes = "获取异常类型" ,httpMethod = "POST")
    @PostMapping("/order/page")
    Response<ExceptionOrderDetailVO> getOrderDetailPage(@RequestBody OrderExceptionSearchRequest request);
}
