package com.wantwant.sfa.backend.exceptionOrder.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 异常订单处理用Request
 * @Auther: zhengxu
 * @Date: 2021/08/18/下午1:21
 */
@ApiModel("异常订单处理操作")
@Data
public class OrderExceptionProcessRequest {
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("操作人ID")
    private String personId;
    @ApiModelProperty("处理结果")
    private int processResult;
    @ApiModelProperty("备注")
    private String remark;
}
