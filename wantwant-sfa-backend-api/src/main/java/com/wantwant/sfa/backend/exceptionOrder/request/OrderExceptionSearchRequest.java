package com.wantwant.sfa.backend.exceptionOrder.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * @Description: 异常订单查询用request
 * @Auther: zhengxu
 * @Date: 2021/08/16/下午6:35
 */
@Data
@ApiModel("异常订单查询请求")
public class OrderExceptionSearchRequest extends PageParam {
    @ApiModelProperty("异常品类数,4为大于3种")
    private Integer itemCount;
    @ApiModelProperty("订单编号")
    private String orderKey;
    @ApiModelProperty("处理状态,0:未处理,1:已处理")
    private Integer processStatus;
    @ApiModelProperty("处理结果")
    private Integer processResult;
    @ApiModelProperty("客户编码/客户名称/手机号")
    private String clientCompositeSearch;
    @ApiModelProperty("订单状态")
    private String orderStatus;
    @ApiModelProperty("异常类型")
    private String exceptionType;
    @ApiModelProperty("订单来源")
    private String source;
    @ApiModelProperty("业务查询")
    private String employeeName;
    @ApiModelProperty(value="提交时间start",example = "2021-01-01")
    @Pattern(regexp = "^\\d{4}(-)\\d{1,2}$")
    private String createTimeStart;
    @ApiModelProperty(value="提交时间end",example = "2021-01-01")
    @Pattern(regexp = "^\\d{4}(-)\\d{1,2}$")
    private String createTimeEnd;
    @ApiModelProperty(value="大区ID")
    private String areaId;
    @ApiModelProperty(value="分公司名称")
    private String branchName;
    @ApiModelProperty(value="营业所名称")
    private String companyName;
    @ApiModelProperty(value="订单渠道")
    private String channel;
    @ApiModelProperty(value="记录当前条数",notes = "选择上条或下条数据是传递.记录当前记录属于第几条")
    private Integer current;
    @ApiModelProperty(value="选择上条或下条数据",notes = "选择上条或下条数据是传递.0:上条,1:下条")
    private Integer towards;
}
