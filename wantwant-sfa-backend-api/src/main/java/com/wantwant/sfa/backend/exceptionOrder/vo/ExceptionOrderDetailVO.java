package com.wantwant.sfa.backend.exceptionOrder.vo;

import com.wantwant.sfa.backend.receiptAndDelivery.vo.OrderDeliveryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 异常订单详情
 * @Auther: zhengxu
 * @Date: 2021/08/17/下午3:45
 */
@Data
@ApiModel("异常订单详情")
public class ExceptionOrderDetailVO {

    //---------订单信息--------
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("手机")
    private String mobile;
    @ApiModelProperty("客户类型")
    private String storeChannel;
    @ApiModelProperty("渠道")
    private String channel;
    @ApiModelProperty("订单类型")
    private String orderType;
    @ApiModelProperty("订单状态")
    private String statusOrder;
    //---------订单信息--------

    //-----收获信息相关--------
    @ApiModelProperty("收件人省")
    private String receiverProvince;
    @ApiModelProperty("收件人市")
    private String receiverCity;
    @ApiModelProperty("收件人区")
    private String receiverDistrict;
    @ApiModelProperty("收件人街道")
    private String receiverStreet;
    @ApiModelProperty("收件人姓名")
    private String receiverName;
    @ApiModelProperty("收件人手机")
    private String receiverMobile;
    //-----收获信息相关--------

    //-----状态信息相关--------
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("下单时间")
    private String placedAt;
    @ApiModelProperty("处理时间")
    private String processingAt;
    @ApiModelProperty("配送时间")
    private String deliveringAt;
    @ApiModelProperty("收获时间")
    private String receivedAt;
    @ApiModelProperty("完成时间")
    private String completeAt;
    //-----状态信息相关--------

    //-----发票信息相关--------
    @ApiModelProperty("发票状态")
    private String invoiceStatus;
    @ApiModelProperty("发票标题")
    private String invoiceTitle;
    @ApiModelProperty("发票收件人姓名")
    private String invoiceReceiverName;
    @ApiModelProperty("发票收件人手机号")
    private String invoiceReceiverMobileNumber;
    @ApiModelProperty("发票收件人地址省")
    private String invoiceReceiverProvince;
    @ApiModelProperty("发票收件人地址市")
    private String invoiceReceiverCity;
    @ApiModelProperty("发票收件人地址区")
    private String invoiceReceiverDistrict;
    @ApiModelProperty("发票收件人地址街道")
    private String invoiceReceiverStreet;
    @ApiModelProperty("发票银行")
    private String invoiceIssuedBank;
    @ApiModelProperty("行号")
    private String invoiceBankAcctNumber;
    @ApiModelProperty("开票公司地址省")
    private String invoiceCompanyProvince;
    @ApiModelProperty("开票公司地址市")
    private String invoiceCompanyCity;
    @ApiModelProperty("开票公司地址区")
    private String invoiceCompanyDistrict;
    @ApiModelProperty("开票公司地址街道")
    private String invoiceCompanyStreet;
    @ApiModelProperty("开票公司电话")
    private String invoiceCompanyPhone;
    @ApiModelProperty("税号")
    private String invoiceTaxId;
    @ApiModelProperty("发票类型")
    private String invoiceType;
    //-----发票信息相关--------

    //-----交易信息相关--------
    @ApiModelProperty("交易状态")
    private String rmbTransactionStatus;
    @ApiModelProperty("运输费用")
    private String deliverFee;
    @ApiModelProperty("折扣描述")
    private String discountDescription;
    @ApiModelProperty("商品合计")
    private String itemsTotal;
    @ApiModelProperty("订单总额")
    private String grandTotal;
    @ApiModelProperty("折扣金额")
    private String discountTotal;
    //-----交易信息相关--------


    //-----异常类型相关--------
    @ApiModelProperty("异常类型")
    private String exceptionItems;
    @ApiModelProperty("处理状态")
    private String processStatus;
    @ApiModelProperty("处理结果")
    private String processResult;
    //-----异常类型相关--------


    @ApiModelProperty("商品列表")
    private List<OrderItemVO> orderItemList;
    @ApiModelProperty("快递信息对象")
    private ExceptionOrderDeliveryVO orderDelivery;
}
