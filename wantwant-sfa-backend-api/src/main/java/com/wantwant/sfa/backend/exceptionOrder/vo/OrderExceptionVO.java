package com.wantwant.sfa.backend.exceptionOrder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 异常预警VO
 * @Auther: zhengxu
 * @Date: 2021/08/12/下午2:50
 */
@Data
@ApiModel("异常预警信息")
public class OrderExceptionVO {
    @ApiModelProperty("总异常数")
    private int exceptionCount;
    @ApiModelProperty("大于3种类型数")
    private int moreThanThreeItemCount;
    @ApiModelProperty("3种类型数")
    private int threeItemCount;
    @ApiModelProperty("2种类型数")
    private int twoItemCount;
    @ApiModelProperty("1种类型数")
    private int oneItemCount;
    @ApiModelProperty("异常占比数")
    private Integer exceptionOrderCount;
    @ApiModelProperty("正常订单数")
    private Integer normalOrderCount;
    @ApiModelProperty("大区占比数")
    private List<AreaExceptionRate> areaExceptionRateList;
    @ApiModelProperty("未处异常占比")
    private Integer unSureCount;
    @ApiModelProperty("确定异常占比")
    private Integer determineCount;
    @ApiModelProperty("非异常占比")
    private Integer misjudgmentCount;
    @ApiModelProperty("预警趋势")
    private List<ExceptionTrendVO> alertTrendList;
    @ApiModelProperty("结果趋势")
    private List<ExceptionTrendVO> resultTrendList;
}
