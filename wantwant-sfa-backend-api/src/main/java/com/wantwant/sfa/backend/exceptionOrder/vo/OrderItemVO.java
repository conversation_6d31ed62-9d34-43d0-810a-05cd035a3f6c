package com.wantwant.sfa.backend.exceptionOrder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 商品列表信息
 * @Auther: zhengxu
 * @Date: 2021/08/17/下午3:55
 */
@Data
@ApiModel("商品列表信息")
public class OrderItemVO {
    @ApiModelProperty("CEO昵称")
    private String ceoName;
    @ApiModelProperty("sku")
    private String sku;
    @ApiModelProperty("名称")
    private String skuName;
    @ApiModelProperty("口味")
    private String flavour;
    @ApiModelProperty("规格")
    private String spec;
    @ApiModelProperty("数量")
    private int quantity;
    @ApiModelProperty("小计")
    private String retailSubtotal;
    @ApiModelProperty("是否赠品")
    private Integer isGiveaway;
    @ApiModelProperty("是否售后")
    private Integer isAfterSale;
    @ApiModelProperty("退款金额")
    private String refundAmount;

}
