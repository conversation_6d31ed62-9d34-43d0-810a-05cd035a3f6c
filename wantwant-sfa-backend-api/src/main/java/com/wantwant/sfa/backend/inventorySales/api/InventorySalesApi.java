package com.wantwant.sfa.backend.inventorySales.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.inventorySales.request.InventorySalesDetailRequest;
import com.wantwant.sfa.backend.inventorySales.request.InventorySalesRateRequest;
import com.wantwant.sfa.backend.inventorySales.vo.InventorySalesDetailVo;
import com.wantwant.sfa.backend.inventorySales.vo.InventorySalesRateVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description：建档客户盘点动销Api
 * @Author： chen
 * @Date 2022/8/3
 */
@ApiModel("建档客户盘点动销")
public interface InventorySalesApi {

    @ApiOperation(value = "建档客户盘点动销率", notes = "建档客户盘点动销率", httpMethod = "POST")
    @PostMapping("/inventorySalesRate/list")
    Response<Page<InventorySalesRateVo>> inventorySalesRateList(@Validated @RequestBody InventorySalesRateRequest request);

    @ApiOperation(value = "建档客户盘点动销明细表", notes = "建档客户盘点动销明细表", httpMethod = "POST")
    @PostMapping("/inventorySalesDetail/list")
    Response<Page<InventorySalesDetailVo>> inventorySalesDetailList(@Validated @RequestBody InventorySalesDetailRequest request);

}

