package com.wantwant.sfa.backend.inventorySales.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description：建档客户盘点动销明细
 * @Author： chen
 * @Date 2022/8/8
 */
@ApiModel("建档客户盘点动销明细request")
@Data
public class InventorySalesDetailRequest extends PageParam {

    @ApiModelProperty(value = "年月", hidden = true)
    private String theYearMonth;


    @ApiModelProperty("组织Id")
    @NotNull(message = "组织Id不能为空")
    private String organizationId;

    @ApiModelProperty("大区")
    private String areaId;

    @ApiModelProperty("分公司")
    private String companyId;

    @ApiModelProperty("区域经理")
    private String departmentId;

    @ApiModelProperty("合伙人姓名、手机号查询")
    private String partnerNameMobile;


    @ApiModelProperty("SKU编码/名称")
    private String skuName;

    @ApiModelProperty("客户查询(请输入客户ID/客户姓名/手机号)")
    private String customerName;

    @ApiModelProperty("盘点库存是否超过30天：0、全部，1、是，2、否")
    private String stockTimeStatus;

    @ApiModelProperty("库存健康度：0、全部，1、不足，2、过剩")
    private String stockStatus;

    @ApiModelProperty("当月是否盘点：0、全部，1、是，2、否")
    private String isCheckCurrMonth;


}
