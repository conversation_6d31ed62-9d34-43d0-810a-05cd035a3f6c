package com.wantwant.sfa.backend.inventorySales.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description：建档客户盘点动销
 * @Author： chen
 * @Date 2022/8/3
 */
@ApiModel("建档客户盘点动销率request")
@Data
public class InventorySalesRateRequest extends PageParam { //

    @ApiModelProperty(value = "年月", hidden = true)
    private String theYearMonth;


    @ApiModelProperty("客户区域类型：1:大区，2：分公司，3：合伙人，4：总部 ，10：区域经理")
    private Integer customerAreaType;

    @ApiModelProperty("组织Id")
    @NotNull(message = "组织Id不能为空")
    private String organizationId;

    @ApiModelProperty("大区")
    private String areaId;

    @ApiModelProperty("分公司")
    private String companyId;

    @ApiModelProperty("区域经理")
    private String departmentId;

    @ApiModelProperty("合伙人姓名、手机号查询")
    private String partnerNameMobile;



}
