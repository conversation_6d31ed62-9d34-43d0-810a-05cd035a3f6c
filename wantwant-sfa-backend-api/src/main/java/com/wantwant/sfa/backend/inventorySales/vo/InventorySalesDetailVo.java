package com.wantwant.sfa.backend.inventorySales.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：建档客户盘点动销明细
 * @Author： chen
 * @Date 2022/8/3
 */
@Data
@ApiModel("建档客户盘点动销明细vo")
public class InventorySalesDetailVo {

    @ApiModelProperty("大区")
    private String regionName;

    @ApiModelProperty("分公司")
    private String branchName;

    @ApiModelProperty("区域经理")
    private String departmentName;

    @ApiModelProperty("合伙人姓名")
    private String partnerName;

    @ApiModelProperty("合伙人")
    private String partnerMemberKey;

    @ApiModelProperty("合伙人手机号")
    private String employeeMobile;

    @ApiModelProperty("合伙人类型")
    private String partnerType;

    @ApiModelProperty("合伙人状态")
    private String employeeStatus;



    @ApiModelProperty("客户ID")
    private String customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户姓名")
    private String contact;

    @ApiModelProperty("客户类型")
    private String customerType;

    @ApiModelProperty("客户等级")
    private String customerValueType;

    @ApiModelProperty("当月是否盘点")
    private String currMonthCheckStatus;

    @ApiModelProperty("客户手机号")
    private String contactPhone;


    @ApiModelProperty("SKU编码")
    private String sku;

    @ApiModelProperty("SKU名称")
    private String skuName;

    @ApiModelProperty("SKU标签")
    private String skuTag;

    @ApiModelProperty("口味")
    private String flavor;

    @ApiModelProperty("规格")
    private String skuSpec;

    @ApiModelProperty("线别")
    private String lineName;

    @ApiModelProperty("箱规")
    private String fullCaseNum;

    @ApiModelProperty("是否客户偏好品项")
    private Boolean isFavorSku;

    @ApiModelProperty("未盘点天数")
    private String noCheckDays;


    @ApiModelProperty("最后一次盘点合伙人_member_key")
    private String lastCheckPartnerMemberKey;

    @ApiModelProperty("最后一次盘点合伙人姓名")
    private String lastheckPartnerName;

    @ApiModelProperty("最近一次盘点日期")
    private String lastCheckTime;

    @ApiModelProperty("最后一次盘点库存量")
    private String lastCheckBoxNum;

    @ApiModelProperty("盘点后补货数量")
    private String afterCheckBuyBoxNumLast;

    @ApiModelProperty("上一次盘点日期")
    private String lastSecondCheckTime;

    @ApiModelProperty("上一次盘点库存数量")
    private String lastSecondCheckBoxNum;

    @ApiModelProperty("期间到货数量")
    private String periodBuyBoxNum;

    @ApiModelProperty("期间动销数量")
    private String periodDynamicSalesBoxNum;

    @ApiModelProperty("日均动销数量")
    private String dynamicSalesBoxNumAvg;

    @ApiModelProperty("可动销天数")
    private String dynamicSalesDays;

    @ApiModelProperty("库存健康度")
    private String inventoryHealth;

    @ApiModelProperty("两次盘点间隔天数")
    private int intervalDays;



}
