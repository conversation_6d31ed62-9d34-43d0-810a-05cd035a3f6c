package com.wantwant.sfa.backend.inventorySales.vo;

import com.wantwant.commons.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：建档客户盘点动销
 * @Author： chen
 * @Date 2022/8/3
 */

@Data
@ApiModel("建档客户盘点动销VO")
public class InventorySalesRateVo {

    @ApiModelProperty("大区")
    private String regionName;

    @ApiModelProperty("分公司")
    private String branchName;

    @ApiModelProperty("区域经理")
    private String departmentName;

    @ApiModelProperty("合伙人")
    private String partnerName;

    @ApiModelProperty("合伙人")
    private String partnerMemberKey;

    @ApiModelProperty("合伙人手机号")
    private String employeeMobile;


    @ApiModelProperty("累计交易客户数")
    private String cusCm;

    @ApiModelProperty("当月需盘点客户数")
    private String cusCmNeedCheck;

    @ApiModelProperty("当月已盘点客户数")
    private String cusCmCheckd;

    @ApiModelProperty("当月盘点率")
    private String checkRate;

    @ApiModelProperty("盘点过二次及以上的客户数")
    private String cusCheckedTwiceOrMore;

    @ApiModelProperty("库存不足客户数")
    private String cusInsufficientInventory;

    @ApiModelProperty("库存过剩客户数")
    private String cusExcessInventory;

}
