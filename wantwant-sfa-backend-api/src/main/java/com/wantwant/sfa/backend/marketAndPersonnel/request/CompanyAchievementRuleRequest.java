package com.wantwant.sfa.backend.marketAndPersonnel.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyAchievementRuleDetailVO;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.OrganizationVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 绩效规则新增修改请求
 *
 * @date 4/19/22 2:32 PM
 * @version 1.0
 */
@Data
public class CompanyAchievementRuleRequest implements Serializable {

	private static final long serialVersionUID = -8242336611032106191L;

	@ApiModelProperty(value = "id")
	private Integer id;

	@NotNull(message = "岗位不能为null！")
	@ApiModelProperty(value = "岗位(1:合伙人,2:总监)",required = true)
	private Integer position;

	@NotEmpty(message = "规则名称不能为null！")
	@ApiModelProperty(value = "规则名称",required = true)
	private String name;

	@ApiModelProperty(value = "规则简介")
	private String describe;

	@NotNull(message = "A指标不能为null！")
	@ApiModelProperty(value = "A指标:目标达成率(总监),盘价业绩(合伙人)",required = true)
	private BigDecimal aIndex;

	@NotNull(message = "A指标详情不能为null！")
	@ApiModelProperty(value = "A指标详情",required = true)
	private List<CompanyAchievementRuleDetailVO> aDetail;

	@NotNull(message = "B指标详情不能为null！")
	@ApiModelProperty(value = "B指标:人效(总监),建档客户成交数(合伙人)",required = true)
	private BigDecimal bIndex;

	@NotNull(message = "B指标详情不能为null！")
	@ApiModelProperty(value = "B指标详情",required = true)
	private List<CompanyAchievementRuleDetailVO> bDetail;

	@NotNull(message = "适用分公司不能为null！")
	@ApiModelProperty(value = "适用分公司",required = true)
	private List<OrganizationVO> organization;

	@NotNull(message = "开始执行时间不能为null！")
	@ApiModelProperty(value = "开始执行时间",required = true)
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private LocalDate startDate;

	@NotNull(message = "更新人员不能为null！")
	@ApiModelProperty(value = "更新人员",required = true)
	private String updatedBy;

}
