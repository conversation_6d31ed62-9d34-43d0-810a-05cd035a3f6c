package com.wantwant.sfa.backend.marketAndPersonnel.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分公司考核批量导入DTO
 *
 * @date 2021-11-22 10:50
 * @version 1.0
 */
@Data
public class CompanyClassImportDTO implements Serializable {

  private static final long serialVersionUID = -7860702804085386184L;

  @ApiModelProperty(value = "分公司ID")
  private String organizationId;

  @Excel(name = "分公司")
  private String company;

  @Excel(name = "本月考核分类")
  private String classification;

  @Excel(name = "下月考核分类")
  private String classificationNext;

}
