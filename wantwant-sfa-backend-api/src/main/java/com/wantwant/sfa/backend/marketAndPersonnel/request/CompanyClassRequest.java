package com.wantwant.sfa.backend.marketAndPersonnel.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 *
 * @date 4/18/22 10:56 PM
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyClassRequest implements Serializable {

    private static final long serialVersionUID = 6751155360254621614L;

    @ApiModelProperty(value = "本月考核分类")
    private String classification;

    @ApiModelProperty(value = "下月考核分类")
    private String classificationNext;

    @NotNull(message = "操作人不能为空")
    @ApiModelProperty(value = "操作人ID",required = true)
    private String updatedBy;

}
