package com.wantwant.sfa.backend.marketAndPersonnel.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@Data
public class ManagerSalaryImportDTO implements Serializable {

    private static final long serialVersionUID = -7607044444485355184L;

    @Excel(name = "薪资月份")
    private String month;

//    @Excel(name = "分公司")
//    private String company;

    @Excel(name = "岗位")
    private String positionTypeName;

    @Excel(name = "memberKey")
    private Long memberKey;

    @Excel(name = "工号")
    private String employeeId;

    @Excel(name = "姓名")
    private String employeeName;

    @Excel(name = "身份证号")
    private String cardNumber;

    @Excel(name = "核定薪资")
    private BigDecimal approvedSalary;

    @Excel(name = "岗位津贴")
    private BigDecimal postAllowance;

    @Excel(name = "绩效奖金")
    private BigDecimal achievementBonus;

    @Excel(name = "补发")
    private BigDecimal responsibilityAward;

    @Excel(name = "补扣")
    private BigDecimal supplementaryDeduction;

    @Excel(name = "其它津贴合计")
    private BigDecimal otherAllowanceTotal;

    @Excel(name = "补贴合计")
    private BigDecimal otherPayablesTotal;

    @Excel(name = "扣款合计")
    private BigDecimal deductionOther;

    @Excel(name = "应付薪资")
    private BigDecimal salariesPayable;

    @Excel(name = "税后扣款合计")
    private BigDecimal afterTaxDeductionTotal;

    @Excel(name = "企业承担社保公积金")
    private BigDecimal enterpriseSocialFund;

    @Excel(name = "个人社保")
    private BigDecimal withholdSocial;

    @Excel(name = "个人公积金")
    private BigDecimal withholdFund;

    @Excel(name = "个人所得税")
    private BigDecimal withholdTax;

    @Excel(name = "实发薪资")
    private BigDecimal salariesActual;

    @Excel(name = "备注")
    private String remark;

}
