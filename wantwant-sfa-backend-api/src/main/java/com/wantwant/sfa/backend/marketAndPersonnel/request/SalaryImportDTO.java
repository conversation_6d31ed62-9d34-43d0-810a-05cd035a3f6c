package com.wantwant.sfa.backend.marketAndPersonnel.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 薪资方案批量导入DTO
 *
 * @date 2021-11-22 10:50
 * @version 1.0
 */
@Data
public class SalaryImportDTO implements Serializable {

    private static final long serialVersionUID = -7860702804085386184L;

    @Excel(name = "分公司")
    private String company;

    @Excel(name = "合伙人姓名")
    private String employeeName;

    @Excel(name = "手机号")
    private String mobile;

    @Excel(name = "薪资方案")
    private String salaryDescribe;

    @Excel(name = "合伙人底薪")
    private BigDecimal employeeBaseSalary;

    /**
     * yyyyMM
     */
    @Excel(name = "开始月份")
    private String startDate;
}
