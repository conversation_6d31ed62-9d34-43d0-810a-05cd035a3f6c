package com.wantwant.sfa.backend.marketAndPersonnel.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 合伙人薪资方案请求
 *
 * @date 4/20/22 4:37 PM
 * @version 1.0
 */
@Data
@ApiModel("合伙人薪资方案请求")
public class SalaryQueryRequest extends PageParam implements Serializable {

    private static final long serialVersionUID = -1393768384573935050L;

    @ApiModelProperty(value = "大区ID")
    private String areaOrganizationId;

    @ApiModelProperty(value = "分公司ID")
    private String companyOrganizationId;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "考核月份(yyyy-MM)",required = true)
    private String yyyyMM;

}
