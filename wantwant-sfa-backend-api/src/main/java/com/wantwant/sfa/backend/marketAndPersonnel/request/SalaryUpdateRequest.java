package com.wantwant.sfa.backend.marketAndPersonnel.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 合伙人底薪和奖金包请求
 *
 * @date 4/20/22 4:37 PM
 * @version 1.0
 */
@Data
public class SalaryUpdateRequest implements Serializable {

    private static final long serialVersionUID = -41411852293886226L;

    @ApiModelProperty(value = "薪资方案")
    private String salaryDescribe;

    @ApiModelProperty(value = "底薪金额")
    private BigDecimal employeeBaseSalary;

    @NotNull(message = "开始月份不能为null！")
    @ApiModelProperty(value = "开始月份")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    @NotNull(message = "更新人员不能为null！")
    @ApiModelProperty(value = "更新人员",required = true)
    private String updatedBy;
}
