package com.wantwant.sfa.backend.marketAndPersonnel.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分公司考核批量导入DTO
 *
 * @date 2021-11-22 10:50
 * @version 1.0
 */
@Data
public class StructureImportDTO implements Serializable {

    private static final long serialVersionUID = -7860702804085386184L;

    @Excel(name = "分公司")
    private String company;

    @Excel(name = "合伙人标准底薪")
    private BigDecimal baseSalary;

    @Excel(name = "合伙人奖金包")
    private BigDecimal bonus;

    /**
     * yyyyMM
     */
    @Excel(name = "开始月份")
    private String startDate;
}
