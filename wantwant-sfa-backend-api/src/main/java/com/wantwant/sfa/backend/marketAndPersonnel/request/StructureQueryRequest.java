package com.wantwant.sfa.backend.marketAndPersonnel.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 合伙人底薪和奖金包请求
 *
 * @date 4/20/22 4:37 PM
 * @version 1.0
 */
@Data
@ApiModel("合伙人底薪和奖金包请求")
public class StructureQueryRequest implements Serializable {

    private static final long serialVersionUID = -41411852293886226L;

    @ApiModelProperty(value = "大区ID")
    private String areaOrganizationId;

    @ApiModelProperty(value = "分公司ID")
    private String companyOrganizationId;

    @ApiModelProperty(value = "组织ID")
    private String orgainizationId;

    @ApiModelProperty(value = "考核月份(yyyy-MM)",required = true)
    private String yyyyMM;

}
