package com.wantwant.sfa.backend.marketAndPersonnel.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 绩效规则详情
 *
 * @date 4/19/22 3:11 PM
 * @version 1.0
 */
@Data
public class CompanyAchievementRuleDetailVO implements Serializable {

	private static final long serialVersionUID = -613504113727925536L;

	@ApiModelProperty(value = "id")
	private Integer id;

	@ApiModelProperty(value = "指标")
	private String index;

	@ApiModelProperty(value = "界限(1:含下限不含上限,2:含上限不含下限)")
	private Integer definition;

	@ApiModelProperty(value = "数值类型(1:数字,100:百分比)")
	private Integer type;

	@ApiModelProperty(value = "开始范围")
	private BigDecimal startRange;

	@ApiModelProperty(value = "结束范围")
	private BigDecimal endRange;

	@ApiModelProperty(value = "比例")
	private BigDecimal ratio;



}
