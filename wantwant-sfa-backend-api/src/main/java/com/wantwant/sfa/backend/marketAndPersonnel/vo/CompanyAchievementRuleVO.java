package com.wantwant.sfa.backend.marketAndPersonnel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 绩效规则VO
 *
 * @date 4/19/22 2:32 PM
 * @version 1.0
 */
@Data
public class CompanyAchievementRuleVO implements Serializable {

	private static final long serialVersionUID = -4479899215582556289L;

	@ApiModelProperty(value = "id")
	private Integer id;

	@ApiModelProperty(value = "岗位(1:合伙人,2:总监)")
	private Integer position;

	@ApiModelProperty(value = "规则名称")
	private String name;

	@ApiModelProperty(value = "规则简介")
	private String describe;

	@ApiModelProperty(value = "A指标:目标达成率(总监),盘价业绩(合伙人)")
	private BigDecimal aIndex;

	@ApiModelProperty(value = "A指标详情")
	private List<CompanyAchievementRuleDetailVO> aDetail;

	@ApiModelProperty(value = "B指标:人效(总监),建档客户成交数(合伙人)")
	private BigDecimal bIndex;

	@ApiModelProperty(value = "B指标详情")
	private List<CompanyAchievementRuleDetailVO> bDetail;

	@ApiModelProperty(value = "适用分公司")
	private List<OrganizationVO> organization;

	@ApiModelProperty(value = "开始月份")
	@JsonFormat(pattern = "yyyyMM", timezone = "GMT+8")
	private LocalDate startDate;

	@ApiModelProperty(value = "更新日期")
	@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
	private LocalDateTime updatedTime;

	@ApiModelProperty(value = "更新人员")
	private String updatedBy;

}
