package com.wantwant.sfa.backend.marketAndPersonnel.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分公司考核分类下拉框VO
 *
 * @date 4/18/22 10:31 PM
 * @version 1.0
 */
@Data
public class CompanyClassVO implements Serializable {

    private static final long serialVersionUID = 1374916626757201506L;

    @ApiModelProperty(value = "考核分类")
    private String classification;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "分公司ID")
    private String organizationId;

}
