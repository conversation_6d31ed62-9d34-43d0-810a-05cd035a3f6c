package com.wantwant.sfa.backend.marketAndPersonnel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分公司考核分类VO
 *
 * @date 4/18/22 10:31 PM
 * @version 1.0
 */
@Data
public class CompanyClassificationVO implements Serializable {

    private static final long serialVersionUID = 2619689922959295704L;

    private Integer id;

    @ApiModelProperty(value = "大区")
    private String area;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "分公司ID")
    private String organizationId;

    @ApiModelProperty(value = "总监姓名")
    private String employeeName;

    @ApiModelProperty(value = "总监入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime onboardTime;

    @ApiModelProperty(value = "总监在职天数")
    private Integer workingDays;

    @ApiModelProperty(value = "上月考核分类")
    private String classificationLast;

    @ApiModelProperty(value = "本月考核分类")
    private String classification;

    @ApiModelProperty(value = "下月考核分类")
    private String classificationNext;

}
