package com.wantwant.sfa.backend.marketAndPersonnel.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 绩效规则适用分公司VO
 *
 * @date 4/19/22 3:17 PM
 * @version 1.0
 */
@Data
public class OrganizationVO implements Serializable {

    private static final long serialVersionUID = -615405679972638443L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "大区")
    private String area;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "分公司组织ID")
    private String organizationId;


}
