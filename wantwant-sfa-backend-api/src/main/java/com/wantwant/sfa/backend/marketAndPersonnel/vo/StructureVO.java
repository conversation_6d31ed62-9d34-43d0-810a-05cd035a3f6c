package com.wantwant.sfa.backend.marketAndPersonnel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合伙人底薪和奖金包列表VO
 *
 * @date 4/20/22 6:22 PM
 * @version 1.0
 */
@Data
public class StructureVO implements Serializable {

    private static final long serialVersionUID = 8696216518913433721L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "大区")
    private String area;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "营业所名称")
    private String departmentName;

    @ApiModelProperty(value = "分公司ID")
    private String organizationId;

    @ApiModelProperty(value = "考核分类")
    private String classification;

    @ApiModelProperty(value = "合伙人标准底薪")
    private BigDecimal baseSalary;

    @ApiModelProperty(value = "合伙人标准奖金包")
    private BigDecimal bonus;

    @ApiModelProperty(value = "岗位津贴")
    private BigDecimal allowance;

    /** 
     * 岗位(101:全职合伙人,102:承揽合伙人,201:区域总监,401:区域经理)
     */
    @ApiModelProperty(value = "岗位")
    private Integer position;

    @ApiModelProperty(value = "等级")
    private String grade;

    @ApiModelProperty(value = "开始月份")
    @JsonFormat(pattern = "yyyyMM", timezone = "GMT+8")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束月份")
    @JsonFormat(pattern = "yyyyMM", timezone = "GMT+8")
    private LocalDate endDate;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "更新人员")
    private String updatedBy;
}
