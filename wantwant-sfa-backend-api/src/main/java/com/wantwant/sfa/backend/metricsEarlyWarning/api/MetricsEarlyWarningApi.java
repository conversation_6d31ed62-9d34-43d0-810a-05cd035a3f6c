package com.wantwant.sfa.backend.metricsEarlyWarning.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.MetricsEarlyWarningEnumDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.MetricsEarlyWarningRuleInfoDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.PageQueryMetricsEarlyWarningRuleInfoDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.QueryMetricsInfoBeforeAddRuleDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.request.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.api
 * @Description:
 * @Date: 2025/2/7 14:50
 */
@Api(value = "MetricsEarlyWarningApi", tags = "指标预警API(2025)")
@RequestMapping("/metricsEarlyWarning")
public interface MetricsEarlyWarningApi {

    @ApiOperation(value = "新增指标预警", notes = "新增指标预警", httpMethod = "POST")
    @PostMapping("/add")
    Response add(@RequestBody @Validated  AddMetricsEarlyWarningRuleReq request);

    @ApiOperation(value = "修改指标预警", notes = "修改指标预警", httpMethod = "POST")
    @PostMapping("/update")
    Response update(@RequestBody @Validated UpdateMetricsEarlyWarningRuleReq request);

    @ApiOperation(value = "复制指标预警", notes = "复制指标预警", httpMethod = "POST")
    @PostMapping("/copy")
    Response copy(@RequestBody @Validated CopyMetricsEarlyWarningRuleReq request);

    @ApiOperation(value = "修改指标预警状态", notes = "修改指标预警状态", httpMethod = "POST")
    @PostMapping("/updateStatus")
    Response updateStatus(@RequestBody @Validated UpdateMetricsEarlyWarningRuleStatusReq request);

    @ApiOperation(value = "批量删除指标预警", notes = "批量删除指标预警", httpMethod = "POST")
    @PostMapping("/batchDelete")
    Response batchDelete(@RequestBody @Validated BatchDeleteMetricsEarlyWarningRuleReq request);

    @ApiOperation(value = "获取所有枚举", notes = "获取所有枚举", httpMethod = "POST")
    @PostMapping("/getEnumInfo")
    Response<MetricsEarlyWarningEnumDto> getEnumInfo();

    @ApiOperation(value = "获取指标预警详情", notes = "获取指标预警详情", httpMethod = "POST")
    @PostMapping("/getMetricsEarlyWarningRuleInfo")
    Response<MetricsEarlyWarningRuleInfoDto> getMetricsEarlyWarningRuleInfo(@RequestBody @Validated QueryMetricsEarlyWarningRuleDetailReq request);

    @ApiOperation(value = "分页查询指标预警", notes = "分页查询指标预警", httpMethod = "POST")
    @PostMapping("/pageQuery")
    Response<Page<PageQueryMetricsEarlyWarningRuleInfoDto>> pageQuery(@RequestBody @Validated PageQueryMetricsEarlyWarningRuleInfoReq request);

//    @ApiOperation(value = "预警前添加 查询指标信息", notes = "查询指标预警前添加指标", httpMethod = "POST")
//    @PostMapping("/queryMetricsInfoBeforeAddRule")
    Response<List<QueryMetricsInfoBeforeAddRuleDto>> queryMetricsInfoBeforeAddRule(@RequestBody QueryMetricsInfoBeforeAddRuleRequest request);
}
