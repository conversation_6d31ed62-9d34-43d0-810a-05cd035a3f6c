package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import com.wantwant.sfa.backend.metrics.vo.MetricsDimensionInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/10 15:23
 */
@Data
public class MetricsEarlyWarningEnumDto {
    @ApiModelProperty("判断方式枚举列表")
    private List<CommonEnumInfoDto> judgmentMethodEnums;

    @ApiModelProperty("对比值类型枚举列表")
    private List<CommonEnumInfoDto> comparisonValueEnums;

    @ApiModelProperty("审批状态枚举列表")
    private List<CommonEnumInfoDto> auditStatusEnums;

    @ApiModelProperty("规则状态枚举列表")
    private List<CommonEnumInfoDto> ruleStatusEnums;

    @ApiModelProperty("标签枚举列表")
    private List<CommonEnumInfoDto> tagEnums;

    @ApiModelProperty("所属应用枚举")
    private List<CommonEnumInfoDto> applicationEnums;

    @ApiModelProperty("字段类型枚举")
    private List<CommonEnumInfoDto> fieldTypeEnums;

    @ApiModelProperty("场景枚举列表")
    private List<MetricsEarlyWarningRuleSceneRelationDto> sceneList;

    @ApiModelProperty("时间维度")
    private List<MetricsDimensionInfoDto> timeDimension;

    @ApiModelProperty("颜色枚举列表")
    private List<ColorValueEnumDto> colorValueEnums;

}
