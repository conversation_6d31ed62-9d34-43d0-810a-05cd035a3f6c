package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/11 13:52
 */
@Data
public class MetricsEarlyWarningRuleConfigDetailDto {

    @ApiModelProperty("字段类型(0.本值 1.环比 2.同比 3.环比差值 4.同比差值)")
    private Integer fieldType;

    @ApiModelProperty("对比值(0.固定值 1.时间进度 2.时间进度预警值 3.全国平均值)")
    private Integer comparisonValue;

    @ApiModelProperty("判断方式(0.< 1.> 2.>= 3.<= 4.= 5.!= 6.in 7.not in)")
    private Integer judgmentMethod;

    @ApiModelProperty("判断数值区间的起始值")
    private String judgmentStart;

    @ApiModelProperty("判断数值区间的结束值")
    private String judgmentEnd;

    @ApiModelProperty("色值")
    private String colorValue;

    @ApiModelProperty("标签(0.向上 1.向下 2.持平)")
    private Integer tag;
}
