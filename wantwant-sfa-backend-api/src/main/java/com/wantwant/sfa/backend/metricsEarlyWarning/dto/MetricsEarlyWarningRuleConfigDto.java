package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/11 13:52
 */
@Data
public class MetricsEarlyWarningRuleConfigDto {

    @ApiModelProperty("关联指标id")
    private Integer metricsId;

    @ApiModelProperty("关联指标名称")
    private String metricsName;

    @ApiModelProperty("计量单位")
    private Integer metricsUnit;

    @ApiModelProperty("计量单位")
    private String metricsUnitDesc;

    @ApiModelProperty("关联指标时间维度")
    private String metricsSourceDimensionInfo;

    @ApiModelProperty("时间维度信息")
    private String timeDimensionInfo;

    @ApiModelProperty("时间维度提示信息")
    private String timeDimensionNotifyInfo;

    @ApiModelProperty("排序")
    private Integer rank;

    @ApiModelProperty("是否重点关注(0.否 1.是)")
    private Integer focusOn;

    @ApiModelProperty("预警配置描述")
    private String remark;

    @ApiModelProperty("指标预警规则配置详情列表")
    private List<MetricsEarlyWarningRuleConfigDetailDto> ruleConfigDetailList;
}
