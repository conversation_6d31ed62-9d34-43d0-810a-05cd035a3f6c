package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import com.wantwant.sfa.backend.personscopeselect.dto.PersonScopeSelectRuleInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/11 11:29
 */
@Data
public class MetricsEarlyWarningRuleInfoDto {

    @ApiModelProperty("指标预警规则ID")
    private Long id;

    @ApiModelProperty("指标预警规则名称")
    private String ruleName;

    @ApiModelProperty("生效开始时间")
    private LocalDate effectiveStartTime;

    @ApiModelProperty("生效结束时间")
    private LocalDate effectiveEndTime;

    @ApiModelProperty("预警描述")
    private String remark;

    @ApiModelProperty(value = "应用信息",hidden = true)
    private String applicationInfo;

    @ApiModelProperty("所属应用列表")
    private List<Integer> applicationList;

    @ApiModelProperty("关联维度id列表")
    private List<Long> dimensionList;

    @ApiModelProperty("预警规则场景配置列表")
    private List<Long> sceneList;

    @ApiModelProperty(value = "预警规则场景配置列表",hidden = true)
    private List<MetricsEarlyWarningRuleSceneDto> ruleSceneList;

    @ApiModelProperty("指标预警规则配置列表")
    private List<MetricsEarlyWarningRuleConfigDto> ruleConfigList;

    @ApiModelProperty(value = "人员范围选择器ID",hidden = true)
    private Long personScopeSelectRuleId;

    @ApiModelProperty(value = "人员范围选择器信息")
    private PersonScopeSelectRuleInfoDto personScopeSelectRuleInfo;
}
