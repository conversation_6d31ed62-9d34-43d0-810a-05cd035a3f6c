package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/11 19:06
 */
@Data
public class MetricsEarlyWarningRuleSceneRelationDto {

    @ApiModelProperty("指标预警规则场景ID")
    private Long id;

    @ApiModelProperty("场景key")
    private String sceneKey;

    @ApiModelProperty("场景名称")
    private String sceneName;

    @ApiModelProperty("父场景ID")
    private Long parentId;

    @ApiModelProperty("层级")
    private Integer level;

    @ApiModelProperty("指标预警规则子场景信息")
    private List<MetricsEarlyWarningRuleSceneRelationDto> children;
}
