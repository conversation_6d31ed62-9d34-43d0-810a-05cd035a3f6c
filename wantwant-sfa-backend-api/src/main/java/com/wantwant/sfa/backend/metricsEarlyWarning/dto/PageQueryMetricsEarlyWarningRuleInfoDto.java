package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/12 10:41
 */
@Data
public class PageQueryMetricsEarlyWarningRuleInfoDto {

    @ApiModelProperty("指标预警规则ID")
    private Long id;

    @ApiModelProperty("指标预警规则名称")
    private String ruleName;

    @ApiModelProperty("复制来源id:复制时记录")
    private Long sourceId;

    @ApiModelProperty("生效开始时间")
    private String effectiveStartTime;

    @ApiModelProperty("生效结束时间")
    private String effectiveEndTime;

    @ApiModelProperty("关联场景名称")
    private String sceneNames;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime  createTime;

    @ApiModelProperty("审核时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime auditTime;

    @ApiModelProperty("审核状态(0.待提交 1.待审核 2.已通过 3.驳回)")
    private Integer auditStatus;

    @ApiModelProperty("审核状态描述")
    private String auditStatusDesc;

    @ApiModelProperty("审核人")
    private String auditPerson;

    @ApiModelProperty("状态(0.初始化中 1.启用 2.禁用)")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("编辑标识 1可编辑")
    private Integer canEdit;

    @ApiModelProperty("修改状态标识 1可修改")
    private Integer canUpdateStatus;
}
