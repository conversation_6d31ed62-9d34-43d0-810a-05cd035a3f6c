package com.wantwant.sfa.backend.metricsEarlyWarning.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.dto
 * @Description:
 * @Date: 2025/2/13 10:25
 */
@Data
public class QueryMetricsInfoBeforeAddRuleDto {
    @ApiModelProperty("指标id")
    private Long id;
    @ApiModelProperty("指标名称")
    private String metricsName;
    @ApiModelProperty("指标来源维度信息")
    private String metricsSourceDimensionInfo;
    @ApiModelProperty("支持字段的类型")
    private List<Integer> fieldTypeList;
}
