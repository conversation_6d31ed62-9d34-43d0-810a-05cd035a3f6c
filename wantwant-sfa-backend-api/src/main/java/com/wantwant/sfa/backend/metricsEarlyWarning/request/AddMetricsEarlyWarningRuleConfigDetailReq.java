package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/10 10:14
 */
@Data
public class AddMetricsEarlyWarningRuleConfigDetailReq implements Serializable {

    @ApiModelProperty("字段类型(0.本值 1.环比 2.同比 3.环比差值 4.同比差值)")
    @NotNull(message = "对比值不能为空")
    private Integer fieldType;

    @ApiModelProperty("对比值(0.固定值 1.时间进度 2.时间进度预警值 3.全国平均值)")
    @NotNull(message = "对比值不能为空")
    private Integer comparisonValue;

    @ApiModelProperty("判断方式(0.< 1.> 2.>= 3.<= 4.= 5.!= 6.in 7.not in)")
    @NotNull(message = "判断方式不能为空")
    private Integer judgmentMethod;

    @ApiModelProperty("判断数值区间的起始值")
    private String judgmentStart;

    @ApiModelProperty("判断数值区间的结束值")
    private String judgmentEnd;

    @ApiModelProperty("色值")
    private String colorValue;

    @ApiModelProperty("标签(0.向上 1.向下 2.持平)")
    private Integer tag;
}
