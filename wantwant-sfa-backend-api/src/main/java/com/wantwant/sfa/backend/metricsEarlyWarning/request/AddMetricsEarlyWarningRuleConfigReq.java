package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/10 10:12
 */
@Data
public class AddMetricsEarlyWarningRuleConfigReq implements Serializable {

    @ApiModelProperty("关联指标id")
    @NotNull(message = "关联指标id不能为空")
    private Integer metricsId;

    @ApiModelProperty("排序")
    @NotNull(message = "排序不能为空")
    private Integer rank;

    @ApiModelProperty("是否重点关注(0.否 1.是)")
    private Integer focusOn;

    @ApiModelProperty("备注")
    @Length(max = 200,message = "预警描述不能超过200个字符")
    private String remark;

    @Valid
    @ApiModelProperty("指标预警规则配置详情列表")
    private List<AddMetricsEarlyWarningRuleConfigDetailReq> ruleConfigDetailList;
}
