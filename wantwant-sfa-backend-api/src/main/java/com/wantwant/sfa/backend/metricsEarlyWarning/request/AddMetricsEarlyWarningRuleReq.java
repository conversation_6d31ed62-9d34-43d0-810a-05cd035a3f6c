package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/10 9:46
 */
@Data
public class AddMetricsEarlyWarningRuleReq implements Serializable {

    @ApiModelProperty("指标预警规则名称")
    @NotBlank(message = "指标预警规则名称不能为空")
    @Length(max = 20,message = "指标预警规则名称不能超过20个字符")
    private String ruleName;

    @ApiModelProperty("所属应用列表")
    @NotEmpty(message = "所属应用列表不能为空")
    private List<Integer> applicationList;

    @ApiModelProperty("生效开始时间")
    @NotNull(message = "生效开始时间不能为空")
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private LocalDate effectiveStartTime;

    @ApiModelProperty("生效结束时间")
    @NotNull(message = "生效结束时间不能为空")
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private LocalDate effectiveEndTime;

    @ApiModelProperty("预警描述")
    @Length(max = 200,message = "预警描述不能超过200个字符")
    private String remark;

    @ApiModelProperty("人员范围选择器详情")
    @NotNull(message = "适用范围详情不能为空")
    private PersonScopeSelectRuleRequest personScopeSelectRuleInfo;

    @ApiModelProperty("关联场景id列表")
    @NotEmpty(message = "关联场景不能为空")
    private List<Long> sceneList;

    @ApiModelProperty("关联维度id列表")
    @NotEmpty(message = "关联维度不能为空")
    private List<Long> dimensionList;

    @Valid
    @ApiModelProperty("预警规则配置列表")
    private List<AddMetricsEarlyWarningRuleConfigReq> ruleConfigList;

    @ApiModelProperty("用户id")
    @NotBlank(message = "用户id不能为空")
    private String employeeId;

    @ApiModelProperty(value = "复制来源id:复制时记录",hidden = true)
    private Long sourceId;
}
