package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/10 15:18
 */
@Data
public class BatchDeleteMetricsEarlyWarningRuleReq {
    @NotEmpty(message = "删除时必须输入预警规则id")
    private List<Long> idList;
    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private String employeeId;
}
