package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/12 10:42
 */
@Data
@ApiModel(value = "分页查询指标预警请求对象", description = "分页查询指标预警请求对象")
public class PageQueryMetricsEarlyWarningRuleInfoReq extends PageParam {

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private String employeeId;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("关联场景id列表")
    private List<Long> sceneList;

    @ApiModelProperty("生效开始时间")
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private LocalDate effectiveStartTime;

    @ApiModelProperty("生效结束时间")
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = LocalDateTimeUtils.yyyy_MM_dd,timezone = "GMT+8")
    private LocalDate effectiveEndTime;

    @ApiModelProperty("审核状态(0.待提交 1.待审核 2.已通过 3.驳回)")
    private Integer auditStatus;

    @ApiModelProperty("状态(0.初始化中 1.启用 2.禁用)")
    private Integer status;

    @ApiModelProperty("排序字段名")
    @Pattern(regexp = "^[a-zA-Z]*$", message = "排序字段可以为空，不为空时必须都是字母")
    private String sortName;
    @ApiModelProperty("排序类型 desc/asc")
    private String sortType;
}
