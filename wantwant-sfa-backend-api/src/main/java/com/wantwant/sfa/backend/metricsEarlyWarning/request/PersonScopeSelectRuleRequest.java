package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import com.wantwant.sfa.backend.personscopeselect.request.PersonScopeSelectRuleEmployeeInfoRequest;
import com.wantwant.sfa.backend.personscopeselect.request.PersonScopeSelectRuleOrganizationInfoRequest;
import com.wantwant.sfa.backend.personscopeselect.request.PersonScopeSelectRulePositionTypeInfoRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/20 14:14
 */
@Data
public class PersonScopeSelectRuleRequest implements Serializable {
    @ApiModelProperty("人员选择id列表")
    private List<PersonScopeSelectRuleEmployeeInfoRequest> empInfos;

    @ApiModelProperty("组织选择列表")
    private List<PersonScopeSelectRuleOrganizationInfoRequest> organizationInfos;

    @ApiModelProperty("岗位类型列表")
    private List<PersonScopeSelectRulePositionTypeInfoRequest> positionTypeInfos;
}
