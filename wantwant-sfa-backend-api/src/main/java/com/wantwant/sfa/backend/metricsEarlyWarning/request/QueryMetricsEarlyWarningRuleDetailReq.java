package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/11 14:27
 */
@Data
public class QueryMetricsEarlyWarningRuleDetailReq {
    @ApiModelProperty("指标预警规则ID")
    @NotNull(message = "指标预警规则ID不能为空")
    private Long id;
}
