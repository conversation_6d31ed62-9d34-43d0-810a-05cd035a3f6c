package com.wantwant.sfa.backend.metricsEarlyWarning.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.request
 * @Description:
 * @Date: 2025/2/18 19:35
 */

@Data
public class UpdateMetricsEarlyWarningRuleStatusReq {
    @ApiModelProperty("指标预警id")
    @NotNull(message = "指标预警id必输")
    private Long id;
    @ApiModelProperty("状态(0.初始化中 1.启用 2.禁用)")
    @NotNull(message = "指标预警状态必输")
    private Integer status;
    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private String employeeId;
}
