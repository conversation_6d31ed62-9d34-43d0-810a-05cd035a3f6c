package com.wantwant.sfa.backend.monitoringSku.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.monitoringSku.vo.SkuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;

@Api(value = "MonitoringSkuApi", tags = "监控大数据与旺铺skuAPI")
public interface MonitoringSkuApi {

  @ApiOperation(value = "监控大数据与旺铺sku", notes = "监控大数据与旺铺sku", httpMethod = "POST")
  @PostMapping("/monitoring/sku/list")
  Response<Page<SkuVo>> MonitoringSkuList();
}
