package com.wantwant.sfa.backend.monitoringSku.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("监控大数据与旺铺的sku")
public class SkuVo {

  private String sku;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    SkuVo skuVo = (SkuVo) o;
    return (sku == skuVo.sku) || (sku != null && sku.equalsIgnoreCase(skuVo.sku));
  }
}
