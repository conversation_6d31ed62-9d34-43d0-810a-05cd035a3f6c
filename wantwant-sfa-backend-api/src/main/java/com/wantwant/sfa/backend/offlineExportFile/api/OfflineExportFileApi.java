package com.wantwant.sfa.backend.offlineExportFile.api;

import com.wantwant.sfa.backend.offlineExportFile.request.AgainDownloadRequest;
import com.wantwant.sfa.backend.offlineExportFile.request.OfflineExportFileRequest;
import com.wantwant.commons.web.response.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "OfflineExportFileApi", tags = "离线导出API")
public interface OfflineExportFileApi {

  @ApiOperation(value = "查询离线导出列表", notes = "查询离线导出列表", httpMethod = "POST")
  @PostMapping("/offlineExportFile/visit/list")
  Response offlineExportFileList(@RequestBody @Validated OfflineExportFileRequest request);

  @ApiOperation(value = "重新导出", notes = "重新导出", httpMethod = "POST")
  @PostMapping("/offlineExportFile/listing/AgainDownload")
  Response AgainDownload(@RequestBody  @Validated AgainDownloadRequest request);

}
