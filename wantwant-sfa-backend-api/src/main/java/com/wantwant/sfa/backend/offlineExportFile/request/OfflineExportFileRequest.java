package com.wantwant.sfa.backend.offlineExportFile.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.wantwant.commons.pagination.Pagination;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel("离线导出信息请求参数")
public class OfflineExportFileRequest extends Pagination{

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "导出类型名称")
    private String exportName;

    @ApiModelProperty(value = "状态，0 导出中，1成功，-1失败")
    private Integer status;

    public String getStartTime(){
        if(StringUtils.isNotBlank(this.startTime)) {
            return startTime+" 00:00:00";
        }
        return this.startTime;
    }


    public String getEndTime(){
        if(StringUtils.isNotBlank(this.endTime)) {
            return endTime+" 23:59:59";
        }
        return this.endTime;
    }

}
