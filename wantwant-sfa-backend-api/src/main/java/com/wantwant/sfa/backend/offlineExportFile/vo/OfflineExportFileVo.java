package com.wantwant.sfa.backend.offlineExportFile.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class OfflineExportFileVo {

    @ApiModelProperty(value = "id编号")
    private Integer id;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "导出时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "导出类型名称")
    private String exportName;

    @ApiModelProperty(value = "请求参数")
    private String requestParam;

    @ApiModelProperty(value = "状态，0 导出中，1成功，-1失败")
    private Integer status;

    @ApiModelProperty(value = "文件路径")
    private String fileDir;




}
