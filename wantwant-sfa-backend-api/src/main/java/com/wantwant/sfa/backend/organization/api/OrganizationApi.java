package com.wantwant.sfa.backend.organization.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.info.vo.CompanyStoreMessageVo;
import com.wantwant.sfa.backend.info.vo.OrganizationVo;
import com.wantwant.sfa.backend.organization.request.*;
import com.wantwant.sfa.backend.organization.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Api(value = "OrganizationApi",tags = "组织相关接口")
public interface OrganizationApi {

    @ApiOperation(value = "组织范围选取" ,notes = "组织范围选取" ,httpMethod = "POST")
    @PostMapping("/organization/region/sava")
    Response savaRegion(@RequestBody SavaRegionlRequest request);

    @ApiOperation(value = "组织区域人员配置" ,notes = "组织区域人员配置" ,httpMethod = "POST")
    @PostMapping("/organization/staffing")
    Response<StaffingResponse> staffing(@RequestBody StaffingReq request);

    //废弃
    @ApiOperation(value = "组织区域数据" ,notes = "组织区域人员配置" ,httpMethod = "GET")
    @GetMapping("/organization/areaFor123")
    Response<List<OrganizationTreeVO>> organizationAreaFor123(OrganizationInfoReq request);

    @ApiOperation(value="导入组织信息", notes = "导入组织信息", httpMethod = "PUT")
    @PutMapping("/organization/upload")
    Response upload(@RequestParam(value = "file", required = true) MultipartFile file);

    @ApiOperation(value = "根据渠道ID获取大区信息" ,notes = "根据渠道ID获取大区信息" ,httpMethod = "GET")
    @GetMapping("/organization/{channelId}")
    Response<List<OrganizationTreeVO>> organizationArea(@PathVariable String channelId);

    @ApiOperation(value = "获取SFA组织信息" ,notes = "获取SFA组织信息" ,httpMethod = "POST")
    @PostMapping("/sfaOrganization")
    Response<List<OrganizationVo>> getSfaOrganizationInfo(@Validated  @RequestBody IwantwantOrganizationReq request);

    @ApiOperation(value = "获取仓库对应的分公司" ,notes = "获取仓库对应的分公司" ,httpMethod = "GET")
    @GetMapping("/store/relation")
    Response<List<CompanyStoreMessageVo>> getStoreCompanyRelation();

    @ApiOperation(value = "获取登陆人组织信息" ,notes = "获取登陆人组织信息" ,httpMethod = "GET")
    @GetMapping("/login/org/{person}")
    Response<List<LoginUserOrgVo>> getLoginOrg(@PathVariable String person);

    @ApiOperation(value = "获取所有组织信息" ,notes = "获取所有组织信息" ,httpMethod = "GET")
    @GetMapping("/selectAllOrg")
    Response<List<OrgVo>> selectAllOrg(@ApiParam("是否过滤总部") @RequestParam("filterZB") boolean filterZB,
                                       @ApiParam("是否过滤营业所") @RequestParam("filterBranch") boolean filterBranch,
                                       @ApiParam("产品组") @RequestParam(value = "businessGroup",required = false) Integer businessGroup);

    @ApiOperation(value = "获取总部组织code" ,notes = "获取总部组织code" ,httpMethod = "GET")
    @GetMapping("/getZbOrgCode")
    Response<String> getZbOrgCode();

    @ApiOperation(value = "根据省市区获取组织" ,notes = "根据省市区获取组织" ,httpMethod = "POST")
    @PostMapping("/selectOrgByArea")
    Response<String> selectOrgByArea(@RequestBody OrgAreaSearchRequest orgAreaSearchRequest);

    @ApiOperation(value = "组织业务组关系校验", notes = "组织业务组关系校验", httpMethod = "POST")
    @PostMapping("/organization/verify")
    Response<Boolean> fitBusinessGroup(@RequestBody VerifyOrganizationFitBusinessGroupRequest request);
}
