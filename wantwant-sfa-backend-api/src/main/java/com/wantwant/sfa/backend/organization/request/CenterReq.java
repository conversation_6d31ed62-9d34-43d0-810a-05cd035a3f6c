package com.wantwant.sfa.backend.organization.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CenterReq extends OrgBaseReq{

	@ApiModelProperty(value = "Q",required = true)
	@JsonProperty("Q")
	private String Q;

	@ApiModelProperty(value = "R",required = true)
	@JsonProperty("R")
	private String R;

	@ApiModelProperty(value = "纬度",required = true)
	private Double lat;

	@ApiModelProperty(value = "经度",required = true)
	private Double lng;
}
