package com.wantwant.sfa.backend.organization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 爱旺旺组织信息获取。
 * @Auther: zhengxu
 * @Date: 2021/10/23/上午11:22
 */
@Data
@ApiModel("爱旺旺组织信息获取用request")
public class IwantwantOrganizationReq {
    @NotNull
    @ApiModelProperty("组织层级")
    private Integer level;
    @ApiModelProperty("父组织code")
    private String parentCode;

    private Integer businessGroup;
}
