package com.wantwant.sfa.backend.organization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2025/03/07/下午8:59
 */
@Data
@ApiModel("根据省市区查询组织")
@ToString
public class OrgAreaSearchRequest {
    @ApiModelProperty("省")
    private String province;
    @ApiModelProperty("市")
    private String city;
    @ApiModelProperty("区")
    private String district;
    @ApiModelProperty("产品组ID")
    @NotNull(message = "缺少产品组")
    private Integer businessGroupId;
}
