package com.wantwant.sfa.backend.organization.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("组织渠道标识")
public class OrgBaseReq {
    @ApiModelProperty(value = "区分组织类型：1：代表sfa组织， 2：代表123专案组织 ",required = false)
    private int channel;

}
