package com.wantwant.sfa.backend.organization.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ApiModel("获取大区相关信息")
public class OrganizationInfoReq{

    @ApiModelProperty(value = "操作人(暂有前端传值)",required = true)
    @NotEmpty(message = "请传入员工ID")
    private String employeeId;

    //用于请求
    private int channel;



}
