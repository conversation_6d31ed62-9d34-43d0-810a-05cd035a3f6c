package com.wantwant.sfa.backend.organization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RegionReq {
    
	@ApiModelProperty(value = "区域类型",required = true)
	private String administrationName;      
    
	@ApiModelProperty(value = "区域名称(详细名称)",required = true)
	private String areaName;      
    
	@ApiModelProperty(value = "区域Id",required = true)
	private String code;      
    
	@ApiModelProperty(value = "颜色",required = true)
	private String color;      
    
	@ApiModelProperty(value = "区域名称",required = true)
	private String currentName;      
   
	//前端要求，必须要存在一个单独的对象,来放4个值
	@ApiModelProperty(value = "区域经纬度",required = true)
	private CenterReq center;      

	
}
