package com.wantwant.sfa.backend.organization.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
* <AUTHOR>
* @description: //模块目的、功能描述
* @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
* @Time 2020-9-7 17:23:27
 */
@Data
@ApiModel("组织范围选取请求")
public class SavaRegionlRequest extends OrgBaseReq {
        
    @ApiModelProperty(value = "操作人(暂有前端传值)",required = true)
    private String employeeId;      
    
    @ApiModelProperty(value = "组织ID",required = true)
    private String organizationId; 
    
    @ApiModelProperty(value = "组织名称",required = true)
    private String organizationName; 
    
    @ApiModelProperty(value = "添加区域",required = true)
    private List<RegionReq> allCurrentSelectAdcodeList;  


}
