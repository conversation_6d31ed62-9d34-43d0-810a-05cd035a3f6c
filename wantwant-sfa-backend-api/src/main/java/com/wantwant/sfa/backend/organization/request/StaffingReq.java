package com.wantwant.sfa.backend.organization.request;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("组织人员配置读取请求")
public class StaffingReq extends OrgBaseReq{
    
    
	@ApiModelProperty(value = "操作人(暂有前端传值)",required = true)
	private String employeeId;      

	@ApiModelProperty(value = "组织ID",required = true)
	private String organizationId; 

	@ApiModelProperty(value = "选定组织的级别(-1上级，0当前组织，1下级)",required = true)
	private int next; 
	
}
