package com.wantwant.sfa.backend.organization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/06/06/下午3:54
 */
@Data
@ApiModel("业务-组织")
public class BusinessOrganizationVo {
    @ApiModelProperty("组织CODE")
    private String organizationId;
    @ApiModelProperty("组织名称")
    private String organizationName;
    @ApiModelProperty("组织类型")
    private String orgType;
    @ApiModelProperty("子类")
    private List<BusinessOrganizationVo> children;
}
