package com.wantwant.sfa.backend.organization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/31/下午2:52
 */
@Data
@ApiModel("登陆人组织信息")
public class LoginUserOrgVo {
    @ApiModelProperty("产品组ID")
    private Integer businessGroup;
    @ApiModelProperty("产品组名称")
    private String businessGroupName;
    @ApiModelProperty("组织名称")
    private String organizationName;
    @ApiModelProperty("组织CODE")
    private String organizationId;
    @ApiModelProperty("岗位类型ID")
    private Integer positionTypeId;

    @ApiModelProperty("组织信息")
    private List<String> orgPath;
}
