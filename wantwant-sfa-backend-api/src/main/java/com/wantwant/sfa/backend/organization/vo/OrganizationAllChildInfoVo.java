package com.wantwant.sfa.backend.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.organization.vo
 * @Description:
 * @Date: 2024/5/24 9:35
 */
@Data
public class OrganizationAllChildInfoVo{

    @ApiModelProperty(value = "组织ID")
    private String regions;

    @ApiModelProperty(value = "组织名称")
    private String title;

    @ApiModelProperty(value = "上级ID")
    private String organizationParentId;

    @ApiModelProperty(value = "下级 0:自己 1:直属下级 2:下下级 ")
    private int level;

    @ApiModelProperty("人员的memberKey")
    private Long memberKey;
}
