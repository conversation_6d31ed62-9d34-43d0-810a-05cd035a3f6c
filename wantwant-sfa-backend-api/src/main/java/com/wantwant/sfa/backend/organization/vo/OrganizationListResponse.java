package com.wantwant.sfa.backend.organization.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrganizationListResponse {

    
    @ApiModelProperty(value = "选定组织ID",required = true)
    private String firstOrganizationId; 
    
    
    @ApiModelProperty(value = "选定组织岗位类型",required = true)
    private int firstPositionTypeId; 
    
    @ApiModelProperty(value = "组织区域",required = true)
    private List<OrganizationVo> organizationList; 
  
}
