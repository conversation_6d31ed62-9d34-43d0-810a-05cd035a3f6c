package com.wantwant.sfa.backend.organization.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel("组织信息")
public class OrganizationTreeVO implements Serializable {

    private static final long serialVersionUID = 3786718092059192247L;

    @ApiModelProperty(value = "组织ID")
    private String regions;

    @ApiModelProperty(value = "组织名称")
    private String title;

    @ApiModelProperty(value = "上级ID")
    private String organizationParentId;

    @ApiModelProperty(value = "下级组织")
    private List<OrganizationTreeVO> children;
}
