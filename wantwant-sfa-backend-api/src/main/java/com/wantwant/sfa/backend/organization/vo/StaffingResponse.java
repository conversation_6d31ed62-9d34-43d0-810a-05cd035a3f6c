package com.wantwant.sfa.backend.organization.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StaffingResponse {

    @ApiModelProperty(value = "经理职位",required = true)
    private int managerPosition; 
    
    @ApiModelProperty(value = "经理",required = true)
    private int managers; 
    
    @ApiModelProperty(value = "经理缺编率",required = true) 
    private BigDecimal managerVacancyRate;

    @ApiModelProperty(value = "专员职位",required = true)
    private int commissionerPosition; 
    
    @ApiModelProperty(value = "专员",required = true)
    private int commissioners; 
    
    @ApiModelProperty(value = "专员缺编率",required = true) 
    private BigDecimal commissionerVacancyRate;

    @ApiModelProperty(value = "分公司职位",required = true)
    private int companyPosition; 
    
    @ApiModelProperty(value = "分公司在岗",required = true)
    private int companys; 
    
    @ApiModelProperty(value = "分公司缺编率",required = true) 
    private BigDecimal companyVacancyRate;
    
    @ApiModelProperty(value = "上级主管姓名",required = true)
    private String firstEmployeeName; 
    
    @ApiModelProperty(value = "选定组织岗位类型",required = true)
    private int firstPositionTypeId; 

}
