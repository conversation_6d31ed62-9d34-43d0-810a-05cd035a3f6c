package com.wantwant.sfa.backend.organizationGoal.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/** 
 * 全品项目标导入模板
 */
@Data
public class AllItemsOrganizationGoalDTO {

    @Excel(name = "组织ID",orderNum = "1")
    private String organizationId;

    @Excel(name = "组织名称",orderNum = "2")
    private String organizationName;

    @Excel(name = "上级组织ID",orderNum = "3")
    private String organizationParentId;

    @Excel(name = "上级组织名称",orderNum = "4")
    private String organizationParentName;

    @Excel(name = "全品项目标",orderNum = "5")
    private BigDecimal transAmountImport;

    @Excel(name = "乳品目标",orderNum = "6")
    @ApiModelProperty(value = "乳品目标")
    private BigDecimal dairyAmountImport;

    @Excel(name = "饮品目标",orderNum = "7")
    @ApiModelProperty(value = "饮品目标")
    private BigDecimal beverageAmountImport;

}
