package com.wantwant.sfa.backend.organizationGoal.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
public class OrganizationGoalDTO {

    private Integer id;

    @ApiModelProperty(value = "生效年月yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM",timezone = "GMT+8")
    private LocalDate effectiveDate;

    /**
     * sfa_organization_goal_excel.id
     */
    private Integer excelId;

    @Excel(name = "组织ID",orderNum = "1")
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "组织层级")
    private String organizationType;

    @Excel(name = "组织层级",orderNum = "2")
    @ApiModelProperty(value = "组织层级")
    private String organizationTypeName;

    @Excel(name = "组织名称",orderNum = "3")
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    /*@Excel(name = "合伙人-编制数量",orderNum = "4")
    @ApiModelProperty(value = "合伙人-编制数量")
    private Integer partnerNum;

    @Excel(name = "合伙人-招聘管控数量",orderNum = "5")
    @ApiModelProperty(value = "合伙人-招聘管控数量")
    private Integer partnerControlledNum;

    @Excel(name = "合伙人-在岗目标数量",orderNum = "6")
    @ApiModelProperty(value = "合伙人-在岗目标数量")
    private Integer partnerOnjob;*/

    @Excel(name = "区域总监-编制",orderNum = "4")
    @ApiModelProperty(value = "区域总监-编制")
    private Integer companyNum;

    @Excel(name = "区域总监-在岗目标数量",orderNum = "5")
    @ApiModelProperty(value = "区域总监-在岗目标数量")
    private Integer companyOnjob;

    @Excel(name = "区域总监-招聘管控数量",orderNum = "6")
    @ApiModelProperty(value = "区域总监-招聘管控数量")
    private Integer companyControlledNum;

    @Excel(name = "区域经理-编制数量",orderNum = "7")
    @ApiModelProperty(value = "区域经理-编制数量")
    private Integer departmentNum;

    @Excel(name = "区域经理-招聘管控数量",orderNum = "8")
    @ApiModelProperty(value = "区域经理-招聘管控数量")
    private Integer departmentControlledNum;

    @Excel(name = "区域经理-在岗目标数量",orderNum = "9")
    @ApiModelProperty(value = "区域经理-在岗目标数量")
    private Integer departmentOnjob;

    @Excel(name = "更新日期",orderNum = "10",exportFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updatedTime;

    @Excel(name = "更新人",orderNum = "11")
    @ApiModelProperty("更新人")
    private String updatedName;



}
