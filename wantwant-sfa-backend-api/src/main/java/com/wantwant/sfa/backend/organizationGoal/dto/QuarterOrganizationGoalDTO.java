package com.wantwant.sfa.backend.organizationGoal.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class QuarterOrganizationGoalDTO {

    private Integer id;

    @Excel(name = "组织ID", orderNum = "1")
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "组织层级")
    private String organizationType;

    @Excel(name = "组织层级", orderNum = "2")
    @ApiModelProperty(value = "组织层级")
    private String organizationTypeName;

    @Excel(name = "组织名称", orderNum = "3")
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @Excel(name = "客户数", orderNum = "4")
    @ApiModelProperty(value = "客户数")
    private Integer customersNum;

    @Excel(name = "交易客户数", orderNum = "5")
    @ApiModelProperty(value = "交易客户数")
    private Integer tradeCustomerNum;

    @Excel(name = "客单价", orderNum = "6")
    @ApiModelProperty(value = "客单价")
    private Integer customerUnitPrice;

    @Excel(name = "管理岗在职人数", orderNum = "7")
    @ApiModelProperty(value = "管理岗在职人数")
    private Integer managementPositionOnJobNum;

    @Excel(name = "管理岗人均业绩", orderNum = "8")
    @ApiModelProperty(value = "管理岗人均业绩")
    private Integer managementPositionUnitPrice;

    @Excel(name = "更新日期", orderNum = "9", exportFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    @Excel(name = "更新人", orderNum = "10")
    @ApiModelProperty("更新人")
    private String updatedName;


}
