package com.wantwant.sfa.backend.organizationGoal.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 总业绩目标导入模板
 */
@Data
public class TotalPerformanceGoalDTO {

    @Excel(name = "产品组ID",orderNum = "1")
    private Integer businessGroup;

    @Excel(name = "产品组名称",orderNum = "2")
    private String businessGroupName;

    @Excel(name = "组织ID",orderNum = "3")
    private String organizationId;

    @Excel(name = "组织名称",orderNum = "4")
    private String organizationName;

    @Excel(name = "上级组织ID",orderNum = "5")
    private String organizationParentId;

    @Excel(name = "上级组织名称",orderNum = "6")
    private String organizationParentName;

    @Excel(name = "全品项目标",orderNum = "7")
    private BigDecimal quarterTransAmount;

}
