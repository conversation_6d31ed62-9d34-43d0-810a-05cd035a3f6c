package com.wantwant.sfa.backend.organizationGoal.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@ToString
public class AllItemsSubmitRequest {

    @NotNull(message = "excelId不能为空！")
    @ApiModelProperty("excelId")
    private Integer excelId;

    @NotNull(message = "开始日期不能为空！")
    @ApiModelProperty("开始日期(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @NotNull(message = "分公司目标截止日期不能为空！")
    @ApiModelProperty("分公司目标截止日期(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate companyEndDate;

    @NotNull(message = "营业所目标截止日期不能为空！")
    @ApiModelProperty("营业所目标截止日期(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate departmentEndDate;

    @NotNull(message = "是否确认不能为空！")
    @ApiModelProperty(value = "是否确认(1:已确认)",required = true)
    private Integer state;

    @NotBlank(message = "登录人工号不能为空！")
    @ApiModelProperty(value = "登录人工号",required = true)
    private String employeeId;

}
