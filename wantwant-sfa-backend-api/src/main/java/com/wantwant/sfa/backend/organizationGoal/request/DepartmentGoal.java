package com.wantwant.sfa.backend.organizationGoal.request;

import com.wantwant.sfa.backend.mainProduct.vo.OrganizationProductVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Random;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

@Data
public class DepartmentGoal {

    @ApiModelProperty(value = "ID(根据ID判断新增修改)")
    private Integer id;

    @NotNull(message = "组织id不能为空!")
    @ApiModelProperty(value = "组织id",required = true)
    private String organizationId;

    @NotNull(message = "组织name不能为空!")
    @ApiModelProperty(value = "组织name",required = true)
    private String organizationName;

    @NotNull(message = "目标业绩不能为空!")
    @ApiModelProperty(value = "目标业绩",required = true)
    private BigDecimal transAmount;

    @ApiModelProperty(value = "主推品信息")
    private List<OrganizationProductVO> productList;

}
