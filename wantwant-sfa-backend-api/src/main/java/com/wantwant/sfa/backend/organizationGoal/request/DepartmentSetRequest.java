package com.wantwant.sfa.backend.organizationGoal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public class DepartmentSetRequest {

    @NotNull(message = "生效日期不能为空")
    @ApiModelProperty(value = "生效时间yyyy-MM-dd",required = true)
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "营业所目标",required = true)
    private List<DepartmentGoal> goals;

    @NotNull(message = "操作人不能为空")
    @ApiModelProperty(value = "操作人id",required = true)
    private String updatedBy;

    @ApiModelProperty(value = "操作人name")
    private String updatedName;
}
