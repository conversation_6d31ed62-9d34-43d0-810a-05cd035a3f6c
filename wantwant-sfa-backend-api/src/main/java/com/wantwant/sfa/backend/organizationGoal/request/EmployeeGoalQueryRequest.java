package com.wantwant.sfa.backend.organizationGoal.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 合伙人目标列表请求
 *
 * @date 2022-02-18 16:15
 * @version 1.0
 */
@Data
public class EmployeeGoalQueryRequest extends PageParam implements Serializable {

    private static final long serialVersionUID = 5292828443832225418L;

    @ApiModelProperty(value = "登录人工号")
    @NotBlank(message = "操作人工号不能为空")
    private String employeeId;

    @ApiModelProperty("需我设置：1，需我审核：2，默认：null")
    private Integer type;

    @ApiModelProperty(value = "年月(2024-05)")
    @NotBlank(message = "年月不能为空")
    private String yearMonth;

    private LocalDate ymd;

    @ApiModelProperty("状态")
    private Integer goalStatus;

    @ApiModelProperty(value = "操作人组织ID")
    private String organizationId;

    @ApiModelProperty(value = "合伙人姓名或手机")
    private String employeeNameOrMobile;

    @ApiModelProperty(value = "是否设置(1:是,2:否)")
    private Integer isSet;

    private Integer businessGroup;

    /**
     * 当前用户岗位
     */
    private String organizationType;

    /**
     * 当前用户组织
     */
    private List<String> organizationIds;


    @ApiModelProperty(value ="未反馈0 能达成1 不能达成需要帮助2")
    private Integer result;

    private Boolean btnFlg = true;

}
