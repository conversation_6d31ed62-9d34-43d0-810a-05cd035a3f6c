package com.wantwant.sfa.backend.organizationGoal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description：修改其他目标请求
 * @Author： chen
 * @Date 2023/3/23
 */
@Data
public class GoalModifyRequest implements Serializable {


    @ApiModelProperty(value = "目标ID", required = true)
    private Integer goalId;

   /* @ApiModelProperty(value = "合伙人-编制数量")
    private Integer partnerNum;

    @ApiModelProperty(value = "合伙人-招聘管控数量")
    private Integer partnerControlledNum;

    @ApiModelProperty(value = "合伙人-在岗目标数量")
    private Integer partnerOnjob;*/

    @ApiModelProperty(value = "客户数")
    private Integer customersNum;

    @ApiModelProperty(value = "交易客户数")
    private Integer tradeCustomerNum;

    @ApiModelProperty(value = "客单价")
    private Integer customerUnitPrice;

    @ApiModelProperty(value = "管理岗在职人数")
    private Integer managementPositionOnJobNum;

    @ApiModelProperty(value = "管理岗人均业绩")
    private Integer managementPositionUnitPrice;

    @ApiModelProperty("更新人")
    private String updatedBy;

}
