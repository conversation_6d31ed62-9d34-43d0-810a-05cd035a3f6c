package com.wantwant.sfa.backend.organizationGoal.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 目标分页查询入参
 *
 * @date 8/19/22 2:41 PM
 * @version 1.0
 */
@Data
public class GoalQueryRequest extends PageParam implements Serializable   {

    private static final long serialVersionUID = -1702631567884145283L;

    @ApiModelProperty(value = "生效日期yyyy-MM",required = true)
    private String effectiveDate;

    @ApiModelProperty(value = "登录人工号",required = true)
    private String employeeId;

    @ApiModelProperty(value = "当前用户组织")
    private List<String> organizationIds;

    @ApiModelProperty("排序名称(1:合伙人在岗目标,2:合伙人编制数,3:合伙人管控数量,4:区域经理编制,5:区域经理在岗目标,6:区域经理招聘管控数量)")
    private Integer orderName;

    @ApiModelProperty("排序(1:倒序,2:正序)")
    private Integer orderSort;

    @ApiModelProperty(value = "组织层级：null全部，zb:总部，area:大区，company:分公司")
    private String selOrganizationType;

    @ApiModelProperty(value = "组织ID")
    private String selOrganizationId;

    private Integer businessGroup;
}
