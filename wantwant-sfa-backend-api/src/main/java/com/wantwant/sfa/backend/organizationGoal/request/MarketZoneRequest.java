package com.wantwant.sfa.backend.organizationGoal.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/23/下午3:29
 */
@Data
@ApiModel("市场空间request")
@ToString
public class MarketZoneRequest extends PageParam {

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty(hidden = true)
    private String organizationType;

    @ApiModelProperty(hidden = true)
    private Integer businessGroup;

    @ApiModelProperty("时间类型 10:自然月,11:自然季,2:财务年")
    private String dateTypeId;
    @ApiModelProperty("对应自然月(2024-05)/自然季(2024-Q2)/财务年(2024) 的时间类型")
    private String yearMonth;
    @ApiModelProperty("区/县")
    private String countryName;
    @ApiModelProperty("排序字段:supplyPriceTotal/tradCus/supplyPriceTotalYoy/population/populationPerformanceAvg/tradCusYoy/cusPrice/countryMarketCount/cusPriceYoy")
    private String orderField;
    @ApiModelProperty("排序类型: asc/desc")
    private String orderType;

    @ApiModelProperty(value = "关联人员数据时间限制",required = true)
    private String theDate;

    @ApiModelProperty(value = "查询类型:0 全部  2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 ")
    private Integer searchType;
}
