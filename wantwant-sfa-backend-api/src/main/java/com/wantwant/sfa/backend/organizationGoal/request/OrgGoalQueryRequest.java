package com.wantwant.sfa.backend.organizationGoal.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 目标查询列表
 *
 * @date 3/27/23 11:12 AM
 * @version 1.0
 */
@Data
public class OrgGoalQueryRequest extends PageParam {

    @ApiModelProperty(value = "生效日期yyyy-MM",required = true)
    private List<String> effectiveDateList;

    @ApiModelProperty(value = "生效季度yyyy-MM",required = true)
    private String effectiveQuarter;

    //用于sql
    private int year;
    private int quarter;

    /**
     * zb 总部
     * area 战区
     * varea 区域
     * province 省区
     * company 分公司
     * department 营业所
     */
    @ApiModelProperty(value = "组织层级")
    private String organizationType;

    @ApiModelProperty(value = "组织Id")
    private String organizationId;

    @ApiModelProperty(value = "登录人工号",required = true)
    private String employeeId;

    /** 
     * 当前用户组织
     */
    private List<String> organizationIds;

    /**
     * 当前组织层级
     */
    private String curOrgType;

    private Integer businessGroup;

}
