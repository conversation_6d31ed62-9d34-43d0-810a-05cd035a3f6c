package com.wantwant.sfa.backend.organizationGoal.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ToString
public class TotalPerformanceConfirmRequest {

    @NotNull(message = "excelId不能为空！")
    @ApiModelProperty("excelId")
    private Integer excelId;

    @NotNull(message = "每月目标截止日期不能为空！")
    @ApiModelProperty("每月目标截止日期")
    private Integer day;

    @NotNull(message = "是否确认不能为空！")
    @ApiModelProperty(value = "是否确认(1:已确认)",required = true)
    private Integer state;

    @NotBlank(message = "登录人工号不能为空！")
    @ApiModelProperty(value = "登录人工号",required = true)
    private String employeeId;

}
