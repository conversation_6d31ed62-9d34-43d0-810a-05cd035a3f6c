package com.wantwant.sfa.backend.organizationGoal.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "总业绩目标查询请求")
public class TotalPerformanceRequest {

    @ApiModelProperty(value = "年",required = true)
    private Integer year;

    @ApiModelProperty(value = "季度",required = true)
    private Integer quarter;

    @ApiModelProperty(value = "登录人组织Id",required = true)
    private String organizationId;

}
