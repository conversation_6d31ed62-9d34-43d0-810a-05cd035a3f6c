package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/** 
 * 全品项目标返回VO
 */
@Data
public class AllItemsGoalInfoVO {

    @ApiModelProperty("excelId")
    private Integer excelId;

    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("分公司目标截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date companyEndDate;

    @ApiModelProperty("营业所目标截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date departmentEndDate;

    @ApiModelProperty("是否确认(0:未确认,1:已确认)")
    private Integer state;

    @ApiModelProperty("列表")
    private List<AllItemsGoalVO> goalList;
}
