package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("全品项目标列表")
public class AllItemsGoalVO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("生效年月")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date effectiveDate;

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("全品项目标")
    private BigDecimal transAmountImport;

    @ApiModelProperty("乳品目标")
    private BigDecimal dairyAmountImport;

    @ApiModelProperty("饮品目标")
    private BigDecimal beverageAmountImport;

    @ApiModelProperty(value = "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updatedTime;

    @ApiModelProperty(value = "更新人")
    private String updatedName;


}
