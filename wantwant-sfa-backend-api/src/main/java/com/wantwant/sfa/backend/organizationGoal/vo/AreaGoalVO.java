package com.wantwant.sfa.backend.organizationGoal.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/** 
 * 区域目标返回VO
 */
@Data
public class AreaGoalVO {

    /*@ApiModelProperty("战区/区域/省区区域总目标")
    private BigDecimal areaQuarterTransAmount;

    @ApiModelProperty("战区/区域/省区所有可销品项-月目标")
    List<OrgMonthGoalVO> areaMonthGoal;

    @ApiModelProperty("战区/区域/省区季度差异")
    private BigDecimal areaQuarterDiff;

    @ApiModelProperty("战区/区域/省区主推品")
    private List<QuarterOrganizationProductVO> areaOrgProductGoal;*/

    @ApiModelProperty("区域/省区/分公司目标列表")
    private List<VareaGoalDetail> vareaGoalList;

}
