package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.mainProduct.vo.OrganizationProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("分公司目标列表")
public class CompanyGoalDetail {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("大区")
    private String area;

    private String areaOrganizationId;

    @ApiModelProperty("分公司id")
    private String organizationId;

    @ApiModelProperty("分公司name")
    private String company;

    @ApiModelProperty("区域总监")
    private String employeeName;

    @ApiModelProperty("入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime onboardTime;

    @ApiModelProperty("人口数")
    private BigDecimal population;

    @ApiModelProperty("当前合伙人数量-全职")
    private Integer fullNum;

    @ApiModelProperty("当前合伙人数量-承揽")
    private Integer contractPartner;

    @ApiModelProperty("当前合伙人数量-企业")
    private Integer businessPartner;

    @ApiModelProperty("当前合伙人数量-兼职")
    private Integer partNum;

    @ApiModelProperty("当前合伙人数量-合计")
    private Integer allNum;

    @ApiModelProperty("同期业绩")
    private BigDecimal lastAchievement;

    @ApiModelProperty("上月业绩")
    private BigDecimal lastMonthAchievement;

    @ApiModelProperty("上月目标")
    private BigDecimal lastMonthAmountGoal;

    @ApiModelProperty("本月预估")
    private BigDecimal auditPriceSumCm;

    @ApiModelProperty("本月目标")
    private BigDecimal transAmount;

    private BigDecimal transAmount1;

    @ApiModelProperty(value = "主推品信息")
    private List<OrganizationProductVO> productList;

    /**
     * 前端使用
     */
    @ApiModelProperty(value = "同比")
    private BigDecimal tbRate;

    @ApiModelProperty(value = "环比")
    private BigDecimal hbRate;

    @ApiModelProperty(value = "预估达成")
    private BigDecimal reach;

}
