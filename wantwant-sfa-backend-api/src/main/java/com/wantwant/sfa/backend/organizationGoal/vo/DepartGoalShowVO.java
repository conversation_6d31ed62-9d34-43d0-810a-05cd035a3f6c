package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 营业所目标说明VO
 */
@Data
public class DepartGoalShowVO {

    @ApiModelProperty("组织id")
    private String organizationId;

    /** 
     * 1.未到当月开始日期或者分公司全品项目标为0时显示：分公司目标暂未设置，请稍后设置营业所目标。
     * 2.分公司全品项目标不为0且在设置营业所目标的时间段内时显示：分公司的全品项目标、主推品目标确定后，请在YYYY-MM-DD前，完成营业所目标确认。
     */
    @ApiModelProperty("说明类型")
    private Integer showType;

    @ApiModelProperty("分公司总目标")
    private BigDecimal transAmount;

    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDate startDate;

    @ApiModelProperty("分公司目标截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDate companyEndDate;

    @ApiModelProperty("营业所目标截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDate departmentEndDate;

}
