package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.mainProduct.vo.OrganizationProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("营业所目标列表")
public class DepartmentGoalDetail {

    @ApiModelProperty("id")
    private Integer id;

    private Integer cid;

    private String companyOrganizationId;

    @ApiModelProperty("分公司")
    private String company;

    @ApiModelProperty("营业所id")
    private String departmentId;
    @ApiModelProperty("营业所name")
    private String departmentName;

    @ApiModelProperty("区域经理")
    private String employeeName;

    @ApiModelProperty("入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime onboardTime;

    @ApiModelProperty("人口数")
    private BigDecimal population;

    @ApiModelProperty("当前合伙人数量-全职")
    private Integer fullNum;

    @ApiModelProperty("当前合伙人数量-承揽")
    private Integer contractPartner;

    @ApiModelProperty("当前合伙人数量-企业")
    private Integer businessPartner;

    @ApiModelProperty("当前合伙人数量-兼职")
    private Integer partNum;

    @ApiModelProperty("当前合伙人数量-合计")
    private Integer allNum;

    @ApiModelProperty("同期业绩")
    private BigDecimal lastAchievement;

    @ApiModelProperty("上月业绩")
    private BigDecimal lastMonthAchievement;

    @ApiModelProperty("上月目标")
    private BigDecimal lastMonthAmountGoal;

    @ApiModelProperty("本月预估")
    private BigDecimal auditPriceSumCm;

    @ApiModelProperty("本月目标")
    private BigDecimal transAmount;

    @ApiModelProperty(value = "主推品信息")
    private List<OrganizationProductVO> productList;

    /**
     * 前端使用
     */
    @ApiModelProperty(value = "同比")
    private BigDecimal tbRate;

    @ApiModelProperty(value = "环比")
    private BigDecimal hbRate;

    @ApiModelProperty(value = "预估达成")
    private BigDecimal reach;

    /**
     * 营业所权重=营业所上月业绩占分公司的比重*70%+营业所合伙人数占分公司的比重*30%
     */
    @ApiModelProperty("营业所权重")
    private BigDecimal scale;

}
