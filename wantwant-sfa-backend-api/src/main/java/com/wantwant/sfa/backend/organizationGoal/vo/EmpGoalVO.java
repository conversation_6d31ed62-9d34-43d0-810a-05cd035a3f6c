package com.wantwant.sfa.backend.organizationGoal.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.sfa.backend.mainProduct.vo.MainProductVO;
import com.wantwant.sfa.backend.realData.vo.EmployeeGoalDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 合伙人目标业绩
 *
 * @since 2022-02-18
 */
@Data
public class EmpGoalVO implements Serializable {

	private static final long serialVersionUID = -338546172401320589L;

	@ApiModelProperty(value = "id")
	private Integer id;

	@Excel(name = "月份",orderNum = "1")
	@ApiModelProperty("月份")
	private String theYearMon;

	private String areaOrganizationId;

	@Excel(name = "大区",orderNum = "2")
	@ApiModelProperty("大区")
	private String area;

	private String companyOrganizationId;

	private Long memberKey;

	@Excel(name = "分公司",orderNum = "3")
	@ApiModelProperty(value = "分公司")
	private String company;

	private String departmentId;

	@Excel(name = "营业所", orderNum = "4")
	@ApiModelProperty(value = "营业所")
	private String departmentName;

	@ApiModelProperty(value = "合伙人组织ID")
	private String branchOrganizationId;

	@ApiModelProperty(value = "业务组")
	private Integer businessGroup;

	@Excel(name = "岗位属性",orderNum = "5")
	@ApiModelProperty("岗位属性")
	private String positionType;

	@Excel(name = "入职时间",orderNum = "6",exportFormat = "yyyy-MM-dd")
	@ApiModelProperty("入职时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private LocalDate onJobTime;

//	@Excel(name = "离职时间",orderNum = "7",exportFormat = "yyyy-MM-dd")
	@ApiModelProperty("离职时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date offJobTime;

	@Excel(name = "负责人",orderNum = "8")
	@ApiModelProperty("负责人")
	private String employeeName;

	@Excel(name = "手机号",orderNum = "9")
	@ApiModelProperty("手机号")
	private String mobile;

	@Excel(name = "人口数",orderNum = "10")
	@ApiModelProperty("人口数")
	private BigDecimal populationNums;

	@Excel(name = "上月业绩",orderNum = "11")
	@ApiModelProperty("上月业绩")
	private BigDecimal lastZwbppjyj;

	@Excel(name = "近3月平均业绩",orderNum = "12")
	@ApiModelProperty("近3月平均业绩")
	private BigDecimal avgZwbppjyj;

	/*@Excel(name = "小标数",orderNum = "11")
	@ApiModelProperty("小标数")
	private BigDecimal smallMarketNums;

	@Excel(name = "小标总目标",orderNum = "12")
	@ApiModelProperty("小标总目标")
	private BigDecimal smallMarketTarget;*/

	@Excel(name = "本月全品项业绩目标",orderNum = "13")
	@ApiModelProperty(value = "本月全品项业绩目标")
	private BigDecimal saleGoal;

	@Excel(name = "季度全品项业绩目标",orderNum = "15")
	@ApiModelProperty(value = "季度全品项业绩目标")
	private BigDecimal quarterSaleGoal;

	@ApiModelProperty("状态")
	private Integer goalStatus;

	@Excel(name = "状态",orderNum = "15")
	private String goalStatusName;


	/*@Excel(name = "休食目标",orderNum = "14")
	@ApiModelProperty(value = "休食目标")
	private BigDecimal snackFoodTarget;

	@Excel(name = "乳品目标",orderNum = "15")
	@ApiModelProperty(value = "乳品目标")
	private BigDecimal dairyFoodTarget;

	@Excel(name = "饮品目标",orderNum = "16")
	@ApiModelProperty(value = "饮品目标")
	private BigDecimal beveragesTarget;*/

	@ApiModelProperty(value = "岗位id")
	private String positionId;

	@ApiModelProperty("合伙人员工ID")
	private Integer employeeInfoId;

	@ApiModelProperty(value = "未分配目标")
	private BigDecimal unUsedGoal;

	/*@Excel(name = "主推品",orderNum = "17")
	@ApiModelProperty(value = "主推品")
	List<OrganizationProductVO> product;*/

	@ApiModelProperty("合伙人考核目标")
	private BigDecimal basicIndicator;

//	@ApiModelProperty(value = "是否可设置(1:是,2:否)")
//	private Integer isSet;

	//按钮权限
	@ApiModelProperty(value = "申请按钮是否高亮")
	private boolean bShowBtn1 = false;
	@ApiModelProperty(value = "审核按钮是否高亮")
	private boolean bShowBtn2 = false;

	@ApiModelProperty(value = "是否可以编辑季度目标")
	private boolean isCanEdit = true;

	@Excel(name = "业务反馈",orderNum = "14")
	@ApiModelProperty(value = "业务反馈")
	private String result;

	/**
	 * 1.在每月10号之后不能设置目标
	 * 2.当月入职合伙人 允许入职后5天内 进行目标设置
	 * 例：合伙人A在2022年10月10号入职，+5天为2022年10月15号，与当月2023年1月10号比对，结果为小于，取当月10号为最后设置目标日期
	 *    合伙人B在2023年1月7号入职，+5天为2023年1月12号，与当月2023年1月10号比对，结果为大于，取2023年1月12号为最后设置目标日期
	 * 界面反馈：超过时间标准时“设置”按钮置灰，点击提示：已超过设置目标时间
	 */
//	public Integer getIsSet() {
//		if (null != isSet){
//			return isSet;
//		}
//		int dayOfMonth = LocalDateTime.now().getDayOfMonth();
//		if (Objects.nonNull(onJobTime)){
//			LocalDate localDate = onJobTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(6);
//			if (localDate.isAfter(LocalDate.now()) || dayOfMonth < 16){
//				return 1;
//			}
//		}
//		return 2;
//	}

	// 新增主推品列表展示
	@ApiModelProperty(value = "合伙人主推品")
	private List<EmployeeGoalDetailVO> mainProductList;

	@ApiModelProperty("员工工号")
	private String employeeId;

}
