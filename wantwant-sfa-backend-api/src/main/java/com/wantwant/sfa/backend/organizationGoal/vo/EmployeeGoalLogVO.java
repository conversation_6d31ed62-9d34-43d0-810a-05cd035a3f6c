package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class EmployeeGoalLogVO implements Serializable {

    private static final long serialVersionUID = -7648424653504044422L;

    private Integer id;

    @ApiModelProperty("goalId")
    private Integer goalId;

    @ApiModelProperty("月份")
    @JsonFormat(pattern = "yyyy-MM",timezone = "GMT+8")
    private LocalDate effectiveDate;

    @ApiModelProperty("作业指标")
    private String modifyAttribute;

    @ApiModelProperty("目标值")
    private String modifyValue;

    public String getModifyValue() {
        return (modifyValue == null||"null".equals(modifyValue)) ? "0" : modifyValue;
    }

    @ApiModelProperty("审核理由")
    private String reason;

    @ApiModelProperty("审核状态")
    private String goalStatus;

    @ApiModelProperty("操作人")
    private String updatedName;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updatedTime;

    private String createdName;

    private String createdBy;

    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createdTime;

}
