package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 组织季度下每月总目标
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgMonthGoalVO {

    @NotNull(message = "生效时间不能为空！")
    @ApiModelProperty(value = "生效时间(2023-06-01)",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDate effectiveDate;

    @ApiModelProperty("全品项业绩目标")
    private BigDecimal transAmount;
}
