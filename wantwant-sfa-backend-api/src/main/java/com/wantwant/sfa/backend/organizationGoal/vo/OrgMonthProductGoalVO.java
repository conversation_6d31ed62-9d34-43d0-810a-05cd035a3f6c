package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 组织季度下每月主题品目标
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgMonthProductGoalVO {


    @ApiModelProperty("生效时间(保存)")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDate effectiveDate;

    @ApiModelProperty("目标金额(保存)")
    private BigDecimal goalAmount;

    @ApiModelProperty("目标金额(总部设定)")
    private BigDecimal zbGoalAmount;
}
