package com.wantwant.sfa.backend.organizationGoal.vo;

import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.AllItemGoalDetailVO;
import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.CustomerUnitPriceDetailVO;
import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.CustomersNumDetailVO;
import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.GoalDetailMainProductVO;
import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.ManagementPositionOnJobNumDetailVO;
import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.ManagementPositionUnitPriceDetailVO;
import com.wantwant.sfa.backend.organizationGoal.vo.goalDetail.TradeCustomerNumDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrganizationGoalListVO{

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty(value = "组织层级")
    private String organizationType;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty("姓名")
    private String employeeName;

    @ApiModelProperty("全品项季度目标")
    private BigDecimal quarterTransAmount;

    @ApiModelProperty(value = "季度客户数")
    private Integer customersNum;

    @ApiModelProperty(value = "季度交易客户数")
    private Integer tradeCustomerNum;

    @ApiModelProperty(value = "季度客单价")
    private Integer customerUnitPrice;

    @ApiModelProperty(value = "季度管理岗在职人数")
    private Integer managementPositionOnJobNum;

    @ApiModelProperty(value = "季度管理岗人均业绩")
    private Integer managementPositionUnitPrice;

    @ApiModelProperty("全品项目标(季度和月度)")
    private List<AllItemGoalDetailVO> allItemGoalDetailVOList;

    @ApiModelProperty("客户数")
    private List<CustomersNumDetailVO> customersNumDetailVOList;

    @ApiModelProperty("交易客户数")
    private List<TradeCustomerNumDetailVO> tradeCustomerNumDetailVOList;

    @ApiModelProperty("客单价")
    private List<CustomerUnitPriceDetailVO> customerUnitPriceDetailVOList;

    @ApiModelProperty("管理岗在职人")
    private List<ManagementPositionOnJobNumDetailVO> managementPositionOnJobNumDetailVOList;

    @ApiModelProperty("管理岗人均业绩")
    private List<ManagementPositionUnitPriceDetailVO> managementPositionUnitPriceDetailVOList;

    @ApiModelProperty("主推品")
    private List<GoalDetailMainProductVO> goalDetailMainProductVOList;

//    @ApiModelProperty("全品项目标")
//    private BigDecimal transAmount;

//    @ApiModelProperty("主推品信息")
//    private List<OrganizationProductVO> productList;

//    @ApiModelProperty("合伙人-编制数量")
//    private Integer partnerNum;
//
//    @ApiModelProperty("合伙人-招聘管控数量")
//    private Integer partnerControlledNum;
//
//    @ApiModelProperty("合伙人-在岗目标数量")
//    private Integer partnerOnjob;
//
//    @ApiModelProperty(value = "区域总监-编制")
//    private Integer companyNum;
//
//    @ApiModelProperty(value = "区域总监-在岗目标数量")
//    private Integer companyOnjob;
//
//    @ApiModelProperty(value = "区域总监-招聘管控数量")
//    private Integer companyControlledNum;
//
//    @ApiModelProperty("区域经理-编制数量")
//    private Integer departmentNum;
//
//    @ApiModelProperty("区域经理-招聘管控数量")
//    private Integer departmentControlledNum;
//
//    @ApiModelProperty("区域经理-在岗目标数量")
//    private Integer departmentOnjob;

}
