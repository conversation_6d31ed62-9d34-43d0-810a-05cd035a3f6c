package com.wantwant.sfa.backend.organizationGoal.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
public class OrganizationGoalVO{

    private Integer id;

    @ApiModelProperty(value = "生效年月yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM",timezone = "GMT+8")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "季度")
    private Integer quarter;

    /**
     * sfa_organization_goal_excel.id
     */
    private Integer excelId;

    @Excel(name = "组织ID",orderNum = "1")
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    private Integer businessGroupId;

    @ApiModelProperty(value = "组织层级")
    private String organizationType;

    @Excel(name = "组织层级",orderNum = "2")
    @ApiModelProperty(value = "组织层级")
    private String organizationTypeName;

    @Excel(name = "组织名称",orderNum = "3")
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "合伙人-编制数量")
    private Integer partnerNum;

    @ApiModelProperty(value = "合伙人-招聘管控数量")
    private Integer partnerControlledNum;

    @ApiModelProperty(value = "合伙人-在岗目标数量")
    private Integer partnerOnjob;

    /* 于20240329版本废弃
    @Excel(name = "区域总监-编制",orderNum = "4")
    @ApiModelProperty(value = "区域总监-编制")
    private Integer companyNum;

    @Excel(name = "区域总监-在岗目标数量",orderNum = "5")
    @ApiModelProperty(value = "区域总监-在岗目标数量")
    private Integer companyOnjob;

    @Excel(name = "区域总监-招聘管控数量",orderNum = "6")
    @ApiModelProperty(value = "区域总监-招聘管控数量")
    private Integer companyControlledNum;

    @Excel(name = "区域经理-编制数量",orderNum = "7")
    @ApiModelProperty(value = "区域经理-编制数量")
    private Integer departmentNum;

    @Excel(name = "区域经理-招聘管控数量",orderNum = "8")
    @ApiModelProperty(value = "区域经理-招聘管控数量")
    private Integer departmentControlledNum;

    @Excel(name = "区域经理-在岗目标数量",orderNum = "9")
    @ApiModelProperty(value = "区域经理-在岗目标数量")
    private Integer departmentOnjob;
     */

    @Excel(name = "客户数",orderNum = "4")
    @ApiModelProperty(value = "客户数")
    private Integer customersNum;

    @Excel(name = "交易客户数",orderNum = "5")
    @ApiModelProperty(value = "交易客户数")
    private Integer tradeCustomerNum;

    @Excel(name = "客单价",orderNum = "6")
    @ApiModelProperty(value = "客单价")
    private Integer customerUnitPrice;

    @Excel(name = "管理岗在职人数",orderNum = "7")
    @ApiModelProperty(value = "管理岗在职人数")
    private Integer managementPositionOnJobNum;

    @Excel(name = "管理岗人均业绩",orderNum = "8")
    @ApiModelProperty(value = "管理岗人均业绩")
    private Integer managementPositionUnitPrice;


    //@Excel(name = "更新日期",orderNum = "3",exportFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updatedTime;

    @ApiModelProperty("更新人")
    private String updatedName;



    @ApiModelProperty(value = "全品项业绩目标")
    private BigDecimal transAmount;

    @ApiModelProperty(value = "人均业绩目标")
    private BigDecimal itemsSupplyTotalCmAvgTarget;

    @ApiModelProperty(value = "销售预估金额")
    private BigDecimal estimatePrice;

    @ApiModelProperty(value = "人均业绩预警")
    private BigDecimal capitaSaleAlerted;

    @ApiModelProperty(value = "在岗率预警")
    private BigDecimal onjobRateAlerted;

    @ApiModelProperty(value = "离职率预警")
    private BigDecimal offjobRateAlerted;

    @ApiModelProperty(value = "库存盘点率预警")
    private BigDecimal inventorycheckRateAlerted;

    @ApiModelProperty(value = "小标市场覆盖率预警")
    private BigDecimal smallmarketcoverageRateAlerted;

    @ApiModelProperty(value = "小标战略目标达成率预警")
    private BigDecimal smallmarketRateAlerted;

    @ApiModelProperty(value = "合伙人0开单率%")
    private BigDecimal partnerOrder0Rate;


    /**
     * 全品项目标(全品项目标导入)
     */
    private BigDecimal transAmountImport;

    /**
     * 乳品目标(全品项目标导入)
     */
    private BigDecimal dairyAmountImport;

    /**
     * 饮品目标(全品项目标导入)
     */
    private BigDecimal beverageAmountImport;


}
