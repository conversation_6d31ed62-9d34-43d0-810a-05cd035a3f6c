package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/** 
 * 总业绩目标列表
 */
@Data
public class TotalPerformanceGoalVO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("季度目标")
    private BigDecimal quarterTransAmount;

    @ApiModelProperty(value = "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updatedName;

}
