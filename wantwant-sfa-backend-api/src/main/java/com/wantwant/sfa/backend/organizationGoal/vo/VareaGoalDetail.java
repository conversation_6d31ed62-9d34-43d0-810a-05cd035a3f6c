package com.wantwant.sfa.backend.organizationGoal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.mainProduct.vo.QuarterOrganizationProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("区域列表")
public class VareaGoalDetail {

    @ApiModelProperty("战区")
    private String area;

    @ApiModelProperty("区域")
    private String varea;

    @ApiModelProperty("省区")
    private String province;

    @ApiModelProperty("分公司")
    private String company;

    @ApiModelProperty("当前组织名称")
    private String organizationName;

    @ApiModelProperty("当前组织id")
    private String organizationId;

    @ApiModelProperty("主管姓名")
    private String employeeName;

    @ApiModelProperty("入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDateTime onboardTime;

    @ApiModelProperty("所有可销季度目标")
    private BigDecimal quarterTransAmount;

    @ApiModelProperty("人口数")
    private BigDecimal population;

    @ApiModelProperty("合伙人数量-合计")
    private Integer allNum;

    @ApiModelProperty("所有可销月目标")
    private List<OrgMonthGoalVO> monthGoal;

    @ApiModelProperty("所有可销月业绩")
    private List<OrgMonthYjVO> monthYj;

    @ApiModelProperty("所有可销品项-季度差异")
    private BigDecimal quarterDiff;

    @ApiModelProperty(value = "主推品信息")
    private List<QuarterOrganizationProductVO> productList;

}
