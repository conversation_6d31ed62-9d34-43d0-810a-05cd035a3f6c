package com.wantwant.sfa.backend.organizationGoal.vo.goalDetail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
@ApiModel("目标管理-目标查询-全品项")
public class AllItemGoalDetailVO {

    @ApiModelProperty("是否是季度数据")
    private boolean quarterFlag;

    @ApiModelProperty("全品项日期字符，类似：2024Q2， 4月")
    private String dateStr;

    @ApiModelProperty("全品项业绩")
    private BigDecimal transAmount;


}
