package com.wantwant.sfa.backend.organizationGoal.vo.goalDetail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("目标管理-目标查询-客单价")
public class CustomerUnitPriceDetailVO {

    @ApiModelProperty("是否是季度数据")
    private boolean quarterFlag;

    @ApiModelProperty("日期字符，类似：4月")
    private String dateStr;

    @ApiModelProperty("客单价")
    private Integer customerUnitPrice;
}
