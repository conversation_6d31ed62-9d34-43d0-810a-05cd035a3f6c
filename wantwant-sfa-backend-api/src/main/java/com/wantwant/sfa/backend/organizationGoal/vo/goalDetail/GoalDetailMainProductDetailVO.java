package com.wantwant.sfa.backend.organizationGoal.vo.goalDetail;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("目标管理-目标查询-主推品-月度数据")
public class GoalDetailMainProductDetailVO {

    @ApiModelProperty("是否是季度数据")
    private boolean quarterFlag;

    @ApiModelProperty("主推品日期字符，类似：2024Q2， 4月")
    private String dateStr;

    @ApiModelProperty("主推品业绩")
    private BigDecimal transAmount;
}
