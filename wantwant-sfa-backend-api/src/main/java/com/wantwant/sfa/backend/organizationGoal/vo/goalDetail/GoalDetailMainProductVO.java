package com.wantwant.sfa.backend.organizationGoal.vo.goalDetail;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("目标管理-目标查询-主推品")
public class GoalDetailMainProductVO {

    @ApiModelProperty("主推品id")
    private Integer id;

    @ApiModelProperty("主推品名称")
    private String productName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("季度目标")
    private BigDecimal quarterTransAmount;


    @ApiModelProperty("主推品季度和月度数据")
    private List<GoalDetailMainProductDetailVO> goalDetailMainProductDetailVOList;

}
