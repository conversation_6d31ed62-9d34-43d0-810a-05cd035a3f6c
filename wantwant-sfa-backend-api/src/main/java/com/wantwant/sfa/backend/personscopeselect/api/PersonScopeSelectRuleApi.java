package com.wantwant.sfa.backend.personscopeselect.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.personscopeselect.dto.*;
import com.wantwant.sfa.backend.personscopeselect.request.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.api
 * @Description:
 * 1.产品组-组织
 * /businessGroup/organization?person=00441211
 *
 * 2。人员--使用全组会议里组织的选择
 * 第一层:/businessGroup/organization?person=00441211
 * 第二层:/meetingInfo/selectEmpForWeb?key=&day=2025-02-18&filterCeo=false&businessGroup=2&person=00462947
 * 查询包含业务bd
 *
 * 3.角色：上层包产品组 -->暂时使用岗位类型
 * /queryBusinessGroupPositionTypeIncludeBd
 * @Date: 2025/2/19 14:20
 */
@Api(value = "PersonScopeSelectRuleApi", tags = "人员范围选择器API")
@RequestMapping("/personScopeSelectRule")
public interface PersonScopeSelectRuleApi {

    @ApiOperation(value = "新增", notes = "新增", httpMethod = "POST")
    @PostMapping("/add")
    Response<Long> add(@RequestBody @Validated AddPersonScopeSelectRuleRequest request);

    @ApiOperation(value = "复制", notes = "复制", httpMethod = "POST")
    @PostMapping("/copy")
    Response<Long> copy(@RequestBody @Validated CopyPersonScopeSelectRuleRequest request);

    @ApiOperation(value = "修改", notes = "修改", httpMethod = "POST")
    @PostMapping("/update")
    Response update(@RequestBody @Validated UpdatePersonScopeSelectRuleRequest request);

    @ApiOperation(value = "详情查询", notes = "详情查询", httpMethod = "POST")
    @PostMapping("/queryDetail")
    Response<PersonScopeSelectRuleInfoDto> queryDetail(@RequestBody @Validated QueryPersonScopeSelectRuleInfoRequest request);

    @ApiOperation(value = "删除", notes = "删除", httpMethod = "POST")
    @PostMapping("/delete")
    Response delete(@RequestBody @Validated DeletePersonScopeSelectRuleInfoRequest request);

    @ApiOperation(value = "规则详情涉及人员信息查询", notes = "规则详情涉及人员信息查询", httpMethod = "POST")
    @PostMapping("/queryRuleDetails")
    Response<List<PersonScopeSelectRuleDetailDto>> queryRuleDetails(@RequestBody @Validated QueryPersonScopeSelectRuleDetailRequest request);

    @ApiOperation(value = "规则比较", notes = "规则比较", httpMethod = "POST")
    @PostMapping("/compared")
    Response<PersonScopeSelectRuleCompareDto> compared(@RequestBody @Validated ComparePersonScopeSelectRuleDetailRequest request);

    @ApiOperation(value = "规则比较-新建前", notes = "规则比较-新建前", httpMethod = "POST")
    @PostMapping("/comparedBeforeAdd")
    Response<PersonScopeSelectRuleCompareBeforeAddDto> comparedBeforeAdd(@RequestBody @Validated CompareBeforeAddPersonScopeSelectRuleDetailRequest request);

    @ApiOperation(value = "查询涉及场景人员的规则列表", notes = "查询涉及场景人员的规则列表", httpMethod = "POST")
    @PostMapping("/queryRuleListForPerson")
    Response<QueryPersonScopeSelectRuleListDto> queryRuleListForPerson(@RequestBody @Validated QueryPersonScopeSelectRuleListRequest request);

}
