package com.wantwant.sfa.backend.personscopeselect.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.dto
 * @Description:
 * @Date: 2025/3/10 10:59
 */
@Data
public class PersonScopeSelectRuleDetailDto {

    @ApiModelProperty("组织类型")
    private String orgType;

    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("产品组id")
    private String businessGroupName;

    @ApiModelProperty("战区CODE")
    private String areaCode;
    @ApiModelProperty("战区名称")
    private String areaName;

    @ApiModelProperty("大区CODE")
    private String vareaCode;
    @ApiModelProperty("大区名称")
    private String vareaName;

    @ApiModelProperty("省区CODE")
    private String provinceCode;
    @ApiModelProperty("省区名称")
    private String provinceName;

    @ApiModelProperty("分公司CODE")
    private String companyCode;
    @ApiModelProperty("分公司名称")
    private String companyName;

    @ApiModelProperty("区域经理层CODE")
    private String departmentCode;
    @ApiModelProperty("区域经理层名称")
    private String departmentName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("岗位id")
    private Integer positionTypeId;

    @ApiModelProperty("岗位名称")
    private String postName;

    @ApiModelProperty("员工id")
    private String employeeId;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("员工头像")
    private String picUrl;

    @ApiModelProperty("是否兼岗 0主岗")
    private Integer partTime;

    @ApiModelProperty("手机号")
    private String mobile;
}
