package com.wantwant.sfa.backend.personscopeselect.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 9:58
 */
@Data
public class PersonScopeSelectRuleEmployeeInfoDto {
    @ApiModelProperty("员工id")
    private String employeeId;
    @ApiModelProperty("员工展示信息")
    private String description;
    @ApiModelProperty("员工头像")
    private String avatar;
}
