package com.wantwant.sfa.backend.personscopeselect.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.dto
 * @Description:
 * @Date: 2025/2/20 11:08
 */
@Data
public class PersonScopeSelectRuleInfoDto {
    @ApiModelProperty("人员选择id列表")
    private List<PersonScopeSelectRuleEmployeeInfoDto> empInfos;

    @ApiModelProperty("组织选择列表")
    private List<PersonScopeSelectRuleOrganizationInfoDto> organizationInfos;

    @ApiModelProperty("岗位类型列表")
    private List<PersonScopeSelectRulePositionTypeInfoDto> positionTypeInfos;
}
