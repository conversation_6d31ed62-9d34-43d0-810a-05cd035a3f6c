package com.wantwant.sfa.backend.personscopeselect.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 9:56
 */
@Data
public class PersonScopeSelectRulePositionTypeInfoDto {

    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("岗位类型id")
    private Integer positionTypeId;

    @ApiModelProperty("额外的唯一key")
    private String showKey;

    @ApiModelProperty("员工展示信息")
    private String description;

}
