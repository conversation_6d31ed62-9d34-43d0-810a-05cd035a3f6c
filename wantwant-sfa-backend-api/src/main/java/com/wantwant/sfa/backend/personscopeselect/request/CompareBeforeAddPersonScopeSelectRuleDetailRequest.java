package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
@Data
public class CompareBeforeAddPersonScopeSelectRuleDetailRequest {
    @ApiModelProperty("人员选择id列表")
    private List<PersonScopeSelectRuleEmployeeInfoRequest> empInfos;

    @ApiModelProperty("组织选择列表")
    private List<PersonScopeSelectRuleOrganizationInfoRequest> organizationInfos;

    @ApiModelProperty("岗位类型列表")
    private List<PersonScopeSelectRulePositionTypeInfoRequest> positionTypeInfos;

    @ApiModelProperty(value = "待比较的规则id")
    @NotEmpty(message = "待比较的规则id不能为空")
    private List<Long> comparedIdList;

    @NotNull(message = "比较类型不能为空")
    @ApiModelProperty(value = "比较类型：1:有交集 2:相等")
    private Integer compareType;

    @ApiModelProperty("是否兼岗 0主岗 ")
    private Integer partTime = 0;
}
