package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 9:54
 */
@Data
public class PersonScopeSelectRuleOrganizationInfoRequest {

    @ApiModelProperty("组织类型")
    private String orgType;

    @ApiModelProperty("组织id")
    private String organizationId;


    @ApiModelProperty("额外的展示信息")
    private String description;
}
