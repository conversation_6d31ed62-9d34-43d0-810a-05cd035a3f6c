package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 9:56
 */
@Data
public class PersonScopeSelectRulePositionTypeInfoRequest {

    @ApiModelProperty("岗位类型id")
    private Integer positionTypeId;

    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("额外的唯一key")
    private String showKey;

    @ApiModelProperty("岗位类型展示信息")
    private String description;
}
