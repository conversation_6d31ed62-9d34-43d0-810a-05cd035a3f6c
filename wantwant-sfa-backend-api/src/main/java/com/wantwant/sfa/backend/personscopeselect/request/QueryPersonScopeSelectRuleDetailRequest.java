package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/3/10 16:55
 */
@Data
public class QueryPersonScopeSelectRuleDetailRequest {
    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("是否兼岗 0主岗")
    private Integer partTime;

    @ApiModelProperty(value = "国际化 0:中国 1海外", hidden = true)
    private Integer i18nFlag;
}
