package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class QueryPersonScopeSelectRuleListRequest {
    @ApiModelProperty("场景key")
    @NotNull(message = "场景key不能为空")
    private String serviceKey;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private String employeeId;

    @ApiModelProperty("用户组id")
    private Integer businessGroupId;


}
