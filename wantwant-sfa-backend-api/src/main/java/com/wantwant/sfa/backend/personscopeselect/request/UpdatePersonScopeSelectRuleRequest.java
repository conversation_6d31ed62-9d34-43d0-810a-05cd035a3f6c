package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 10:31
 */
@Data
public class UpdatePersonScopeSelectRuleRequest extends AddPersonScopeSelectRuleRequest{
    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    private Long id;
}
