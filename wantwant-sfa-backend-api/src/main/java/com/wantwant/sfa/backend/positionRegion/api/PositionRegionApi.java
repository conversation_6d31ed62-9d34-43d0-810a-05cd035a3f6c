package com.wantwant.sfa.backend.positionRegion.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.positionRegion.request.ReginInfoRequest;
import com.wantwant.sfa.backend.positionRegion.request.VillageRequest;
import com.wantwant.sfa.backend.positionRegion.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/19/下午4:38
 */
@Api(value = "合伙人区域信息", tags = "合伙人区域信息")
public interface PositionRegionApi {

    @ApiOperation(value = "根据memberKey获取区域信息" ,notes = "根据memberKey获取区域信息")
    @GetMapping("/regions")
    Response<List<RegionVo>> regions(@RequestParam Long memberKey);

    @ApiOperation(value = "根据memberKey获取区信息" ,notes = "根据memberKey获取区信息")
    @GetMapping("/districts")
    Response<List<DistrictVo>> districts(@RequestParam Long memberKey);

    @ApiOperation(value = "获取四级地" ,notes = "获取四级地")
    @PostMapping("/villages")
    Response<List<VillageVo>> villageVos(@RequestBody VillageRequest villageRequest);

    @ApiOperation(value ="获取四级地详情", notes = "获取四级地")
    @PostMapping("/regionInfo")
    Response<List<RegionInfoVo>> regionInfo(@RequestBody ReginInfoRequest request);

    @ApiOperation(value = "根据memberKey获取经销地区" ,notes = "根据memberKey获取经销地区")
    @GetMapping("/regionScope")
    Response<List<String>> regionScope(@RequestParam Long memberKey);

    @ApiOperation(value = "获取小标市场" ,notes = "获取小标市场")
    @GetMapping("/smallMarket")
    Response<List<SmallMarketVo>> smallMarket(@RequestParam Long memberKey);

    @ApiOperation(value = "根据小标市场ID获取小标市场信息" ,notes = "根据小标市场ID获取小标市场信息")
    @GetMapping("/getSmallMarketByIds")
    Response<List<SmallMarketVo>> getSmallMarketById(@RequestParam List<String> smallMarketIds);
}
