package com.wantwant.sfa.backend.positionRegion.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/01/下午5:42
 */
@Data
@ApiModel("根据四级CODE查询四级地上级信息")
public class ReginInfoRequest {
    @ApiModelProperty("四级地CODE")
    private List<String> districtCodes;
}
