package com.wantwant.sfa.backend.positionRegion.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/01/下午5:10
 */
@ApiModel("四级地详情")
@Data
public class RegionInfoVo {
    @ApiModelProperty("省Code")
    private String provinceCode;
    @ApiModelProperty("省名称")
    private String provinceName;
    @ApiModelProperty("市Code")
    private String cityCode;
    @ApiModelProperty("市名称")
    private String cityName;
    @ApiModelProperty("区Code")
    private String districtCode;
    @ApiModelProperty("区名称")
    private String districtName;
    @ApiModelProperty("乡Code")
    private String villageCode;
    @ApiModelProperty("乡名称")
    private String villageName;
}
