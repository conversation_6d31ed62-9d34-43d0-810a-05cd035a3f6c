package com.wantwant.sfa.backend.productSynchronization.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.productSynchronization.request.YearPerformanceTrendsRequest;
import com.wantwant.sfa.backend.productSynchronization.request.YearProductSyncCustomerRequest;
import com.wantwant.sfa.backend.productSynchronization.request.YearProductSynchronizationRequest;
import com.wantwant.sfa.backend.productSynchronization.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(value = "ProductSynchronizationApi", tags = "年节90天同期分析API")
@RequestMapping("/productSynchronization")
public interface ProductSynchronizationApi {

    @ApiOperation(value = "获取年节达成列表", notes = "获取年节达成列表", httpMethod = "POST")
    @PostMapping("/year/achieved/list")
    Response<Page<YearProductSynchronizationVo>> getYearProductAchievedList(
            @RequestBody YearProductSynchronizationRequest request);

    @ApiOperation(value = "获取年节达成列表(组织明细)", notes = "获取年节达成列表(组织明细)", httpMethod = "POST")
    @PostMapping("/year/achieved/organization/list")
    Response<List<YearProductSynchronizationVo>> getYearProductAchievedOrganizationList(
            @RequestBody YearProductSynchronizationRequest request);

    @ApiOperation(value = "获取年节交易客户数列表", notes = "获取年节交易客户数列表", httpMethod = "POST")
    @PostMapping("/year/transactionsNum/list")
    Response<Page<YearTransactionsNumVo>> getYearProductTransactionsNumList(
            @RequestBody YearProductSynchronizationRequest request);

    @ApiOperation(value = "获取年节交易客户数列表(组织明细)", notes = "获取年节交易客户数列表(组织明细)", httpMethod = "POST")
    @PostMapping("/year/transactionsNum/organization/list")
    Response<List<YearTransactionsNumVo>> getYearProductOrganizationList(
            @RequestBody YearProductSynchronizationRequest request);

    @ApiOperation(value = "获取年节建档客户详情", notes = "获取年节建档客户详情", httpMethod = "POST")
    @PostMapping("/year/filingClient/list")
    Response<Page<YearFilingClientVo>> getYearFilingClientList(
            @RequestBody YearProductSynchronizationRequest request);


    @ApiOperation(value = "获取年累计业绩数据", notes = "获取年累计业绩数据", httpMethod = "POST")
    @PostMapping("/year/performanceAchieved")
    Response<YearPerformanceAchievedVo> getYearPerformanceAchievedTrends(
            @RequestBody YearPerformanceTrendsRequest request);

    @ApiOperation(value = "获取年节达成的SPU", notes = "获取年节达成的SPU", httpMethod = "POST")
    @PostMapping("/year/achieved/spu")
    Response<TransactionsNumVo> getYearProductAchievedSpu(
            @RequestBody YearProductSynchronizationRequest request);

    @ApiOperation(value = "获取开单人数的SPU/SKU", notes = "获取开单人数的SPU/SKU", httpMethod = "POST")
    @PostMapping("/year/transactionsNum/spu/sku")
    Response<TransactionsNumVo> getYearTransactionsNumSpuSku(
            @RequestBody YearProductSynchronizationRequest request);

    @ApiOperation(value = "获取年节产品全部客户列表", notes = "获取年节产品全部客户列表", httpMethod = "POST")
    @PostMapping("/year/customer/list")
    Response<Page<YearProductSyncCustomerVo>> getYearProductCustomerList(@RequestBody YearProductSyncCustomerRequest request);

    @ApiOperation(value = "获取年节达成列表(组织)导出", notes = "获取年节达成列表(组织)导出", httpMethod = "GET")
    @PostMapping("/year/achieved/organization/list/export")
    void getYearProductAchievedOrganizationListExport(
         @RequestBody YearProductSynchronizationRequest request, HttpServletResponse response);

    @ApiOperation(value = "获取年节交易客户数列表(组织)导出", notes = "获取年节交易客户数列表(组织)导出", httpMethod = "GET")
    @PostMapping("/year/transactionsNum/organization/list/export")
    void getYearProductOrganizationNumListExport(
            @RequestBody YearProductSynchronizationRequest request,HttpServletResponse response);

    @ApiOperation(value = "获取年节达成列表导出", notes = "获取年节达成列表导出", httpMethod = "GET")
    @GetMapping("/year/achieved/list/export")
    void getYearProductAchievedListExport(
            YearProductSynchronizationRequest request, HttpServletResponse response);

    @ApiOperation(value = "获取年节交易客户数列表导出", notes = "获取年节交易客户数列表导出", httpMethod = "GET")
    @GetMapping("/year/transactionsNum/list/export")
    void getYearProductTransactionsNumListExport(
            YearProductSynchronizationRequest request,HttpServletResponse response);

    @ApiOperation(value = "获取年节建档客户详情导出", notes = "获取年节建档客户详情导出", httpMethod = "GET")
    @GetMapping("/year/filingClient/list/export")
    void getYearFilingClientListExport(
            YearProductSynchronizationRequest request,HttpServletResponse response);

    @ApiOperation(value = "获取年节产品全部客户列表导出", notes = "获取年节产品全部客户列表导出", httpMethod = "GET")
    @GetMapping("/year/customer/list/export")
    void getYearProductCustomerListExport(YearProductSyncCustomerRequest request,HttpServletResponse response);


}
