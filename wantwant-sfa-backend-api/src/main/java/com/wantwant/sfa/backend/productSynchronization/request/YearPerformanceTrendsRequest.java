package com.wantwant.sfa.backend.productSynchronization.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "年节累计传参")
@Data
public class YearPerformanceTrendsRequest {

    @ApiModelProperty(value = "员工工号")
    private String employeeId;

    @ApiModelProperty(value = "去年时间查询")
    private Integer lastYear;

    @ApiModelProperty(value = "时间查询")
    private String year;

    @ApiModelProperty(value = "是否spu 0 : 组织 1: spu")
    private int isSpu;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "spuid")
    private String spuId;

    @ApiModelProperty(value = "组别")
    private int businessGroup;
}
