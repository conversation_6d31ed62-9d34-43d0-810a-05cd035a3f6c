package com.wantwant.sfa.backend.productSynchronization.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：年节产品全部客户传参
 * @Author： chen
 * @Date 2023/10/30
 */

@Data
@ApiModel("年节产品全部客户传参")
public class YearProductSyncCustomerRequest extends PageParam {
    @ApiModelProperty(value = "员工工号")
    private String employeeId;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "时间查询：如'2023'")
    private String theYear;

    @ApiModelProperty(value = "客户查询")
    private String customerKey;

    @ApiModelProperty(value = "业务查询")
    private String businessKey;

    @ApiModelProperty(value = "客户类型：意向 潜在 陈列 合伙人")
    private String customerType;

    @ApiModelProperty(value = "客户子类型：终端 二批 经销商 企业合伙人  承揽合伙人")
    private String customerSubType;

    @ApiModelProperty(value = "组类别：1 业绩，2 A组，3 B组，4 饮料组，5 酒品组，6 E组，7 90-61天，8 60-31天，9 30-01天 , 10 距离上一次交易天数")
    private Integer orderSubType;

    @ApiModelProperty(value = "组index：1 年节，2 同期，3 差异, 默认1")
    private Integer orderSubIndex;

    @ApiModelProperty(value = "排序类型(asc.正序;desc.倒序)")
    private String orderType;

    @ApiModelProperty(value = "排序名称", hidden = true)
    private String orderName;

    @ApiModelProperty(value = "组别")
    private int businessGroup;
}
