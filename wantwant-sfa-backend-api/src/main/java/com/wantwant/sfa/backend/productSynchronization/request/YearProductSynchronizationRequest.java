package com.wantwant.sfa.backend.productSynchronization.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "年节产品同期传参")
@Data
public class YearProductSynchronizationRequest extends PageParam {

    @ApiModelProperty(value = "是否展示下级(0.否;1.是)[总部不用传下级]")
    private int isNextRealtime;

    @ApiModelProperty(value = "[后端处理]组织类型4.总部 1.战区 12.大区 11.省区 2.分公司 10.区域经理 3.营业所")
    private Integer organizationType;

    @ApiModelProperty(value = "时间查询")
    private String year;

    @ApiModelProperty(value = "员工工号")
    private String employeeId;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "是否 差异(0.否：1.是)")
    private int isDiscrepancy;

    @ApiModelProperty(value = "是否达成业绩 类型 0 : 同期 1: 当前年节 ")
    private int isCurrent;

    //同期页面的 年节开单人数传参是1（前端可以传1，后端处理sql处理成0）
    @ApiModelProperty(value = "是否达成业绩的开单人数 类型 0 : 同期 1: 当前年节 ")
    private Integer isBilling;

    @ApiModelProperty(value = "职位(岗位)")
    private String positionName;

    @ApiModelProperty(value = "业务信息（手机号或姓名）")
    private String employeeMessage;

    @ApiModelProperty(value = "spu名称")
    private String spu;

    @ApiModelProperty(value = "sku名称")
    private String sku;

    @ApiModelProperty(value = "客户类型")
    private String customerType;

    @ApiModelProperty(value = "排序名称")
    private String orderName;

    @ApiModelProperty(value = "排序类型(asc.正序;desc.倒序)")
    private String orderType;

    @ApiModelProperty(value = "memberkey")
    private String memberkey;

    @ApiModelProperty(value = "组别")
    private int businessGroup;
}
