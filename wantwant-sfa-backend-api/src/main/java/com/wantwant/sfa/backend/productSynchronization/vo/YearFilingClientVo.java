package com.wantwant.sfa.backend.productSynchronization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "建档客户详情返回参数")
@Data
public class YearFilingClientVo {

    @ApiModelProperty(value = "客户编号")
    private String customerFilingClient;

    @ApiModelProperty(value = "memberkey")
    private String memberkey;

    @ApiModelProperty(value = "客户类型")
    private String customerType;

    @ApiModelProperty(value = "客户名称")
    private String customer;

    @ApiModelProperty(value = "客户编号")
    private String customerId;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户手机号")
    private String customerMobile;

    @ApiModelProperty(value = "客户标签")
    private String customerTag;

    @ApiModelProperty(value = "当前业务姓名")
    private String businessName;

    @ApiModelProperty(value = "当前业务手机号")
    private String businessMobile;

    @ApiModelProperty(value = "距离上一次交易天数")
    private String distanceLastTradingDays;

    @ApiModelProperty(value = "最后一次下单时间")
    private String distanceFinallyTradingDays;

    @ApiModelProperty(value = "盘价业绩-年节")
    private String performanceYear;

    @ApiModelProperty(value = "盘价业绩-同期")
    private String performanceSamePeriod;

    @ApiModelProperty(value = "盘价业绩-差异")
    private String performanceDiscrepancy;

    @ApiModelProperty(value = "(90-61天)年节")
    private String ninetyYear;

    @ApiModelProperty(value = "(90-61天)同期")
    private String ninetySamePeriod;

    @ApiModelProperty(value = "(90-61天)差异")
    private String ninetyDiscrepancy;

    @ApiModelProperty(value = "(60-31天)年节")
    private String sixtyYear;

    @ApiModelProperty(value = "(60-31天)同期")
    private String sixtySamePeriod;

    @ApiModelProperty(value = "(60-31天)差异")
    private String sixtyDiscrepancy;

    @ApiModelProperty(value = "(30-01天)年节")
    private String thirtyYear;

    @ApiModelProperty(value = "(30-01天)同期")
    private String thirtySamePeriod;

    @ApiModelProperty(value = "(30-01天)差异")
    private String thirtyDiscrepancy;

}
