package com.wantwant.sfa.backend.productSynchronization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "年节90天累计业绩")
@Data
public class YearPerformanceAchievedTrendsVo {
    @ApiModelProperty(value = "实际日期")
    private String theDate;

    @ApiModelProperty(value = "编号日期")
    private String numberingDate;

    @ApiModelProperty(value = "去年业绩")
    private String annualItemsSupplyTotalLast;

    @ApiModelProperty(value = "今年业绩")
    private String annualItemsSupplyTotal;

    @ApiModelProperty(value = "累计目标达成率")
    private String annualItemsSupplyTotalAchievedRate;

    @ApiModelProperty(value = "累计目标")
    private String annualItemsSupplyTotalAchieved;

    @ApiModelProperty(value = "落差")
    private String dropValue;

    @ApiModelProperty(value = "同比")
    private String yoyRate;

}
