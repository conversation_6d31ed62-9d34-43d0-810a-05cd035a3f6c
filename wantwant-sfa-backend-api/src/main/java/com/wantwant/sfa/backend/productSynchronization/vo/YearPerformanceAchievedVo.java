package com.wantwant.sfa.backend.productSynchronization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "业绩")
@Data
public class YearPerformanceAchievedVo {

    @ApiModelProperty(value = "累计业绩")
    private List<YearPerformanceAchievedTrendsVo>  cumulativePerformance;

    @ApiModelProperty(value = "每日业绩")
    private List<YearPerformanceAchievedTrendsVo>  dailyPerformance;
}
