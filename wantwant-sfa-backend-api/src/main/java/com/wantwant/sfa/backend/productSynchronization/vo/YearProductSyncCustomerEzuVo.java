package com.wantwant.sfa.backend.productSynchronization.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description：
 * @Author： chen
 * @Date 2023/11/3
 */

@Data
@ApiModel("年节产品全部客户列表E组返回参数")
public class YearProductSyncCustomerEzuVo {

    @ApiModelProperty(value = "年节")
    private String theYear;

    @ApiModelProperty(value = "-")
    private String positionTypeId;

    @ApiModelProperty(value = "-")
    private String organizationId;


    @Excel(name = "战区",replace = {"-_null"})
    @ApiModelProperty(value = "战区")
    private String areaName;

    @Excel(name = "大区",replace = {"-_null"})
    @ApiModelProperty(value = "大区")
    private String vareaName;

    @Excel(name = "省区",replace = {"-_null"})
    @ApiModelProperty(value = "省区")
    private String provinceName;

    @Excel(name = "分公司",replace = {"-_null"})
    @ApiModelProperty(value = "分公司")
    private String companyName;

    @Excel(name = "营业所",replace = {"-_null"})
    @ApiModelProperty(value = "营业所")
    private String departmentName;

    @Excel(name = "客户编号",replace = {"-_null"})
    @ApiModelProperty(value = "客户编号")
    private String customerNum;

    @Excel(name = "客户类型",replace = {"-_null"})
    @ApiModelProperty(value = "客户类型")
    private String customerType;

    @Excel(name = "客户子类",replace = {"-_null"})
    @ApiModelProperty(value = "客户子类")
    private String customerSubType;

    @Excel(name = "客户名称",replace = {"-_null"})
    @ApiModelProperty(value = "客户名称")
    private String customerStoreName;

    @Excel(name = "客户姓名",replace = {"-_null"})
    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @Excel(name = "客户手机号",replace = {"-_null"})
    @ApiModelProperty(value = "客户手机号")
    private String customerMobile;

    @Excel(name = "客户标签",replace = {"-_null"})
    @ApiModelProperty(value = "客户标签")
    private String customerLabel;

    @Excel(name = "距离上一次交易天数",replace = {"-_null"})
    @ApiModelProperty(value = "距离上一次交易天数")
    private String lastOrdeDays;

    @Excel(name = "最后一次下单时间",replace = {"-_null"})
    @ApiModelProperty(value = "最后一次下单时间")
    private String lastOrderTime;


    @Excel(name = "盘价业绩年节",replace = {"-_null"})
    @ApiModelProperty(value = "盘价业绩年节", hidden = true)
    private BigDecimal annualItemsSupplyTotal;

    @Excel(name = "盘价业绩同期",replace = {"-_null"})
    @ApiModelProperty(value = "盘价业绩同期", hidden = true)
    private BigDecimal annualItemsSupplyTotalLy;

    @Excel(name = "盘价业绩差异",replace = {"-_null"})
    @ApiModelProperty(value = "盘价业绩差异", hidden = true)
    private BigDecimal annualItemsSupplyTotalDifference;

    @Excel(name = "E组业绩年节",replace = {"-_null"})
    @ApiModelProperty(value = "E组业绩年节", hidden = true)
    private BigDecimal annualItemsSupplyTotalE;

    @Excel(name = "E组业绩同期",replace = {"-_null"})
    @ApiModelProperty(value = "E组业绩同期", hidden = true)
    private BigDecimal annualItemsSupplyTotalLyE;

    @Excel(name = "E组业绩差异",replace = {"-_null"})
    @ApiModelProperty(value = "E组业绩差异", hidden = true)
    private BigDecimal annualItemsSupplyTotalDifferenceE;


    @Excel(name = "当前业务姓名",replace = {"-_null"})
    @ApiModelProperty(value = "当前业务姓名")
    private String employeeName;

    @Excel(name = "当前业务手机号",replace = {"-_null"})
    @ApiModelProperty(value = "当前业务手机号")
    private String employeeMobile;


}
