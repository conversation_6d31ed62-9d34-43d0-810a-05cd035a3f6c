package com.wantwant.sfa.backend.productSynchronization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description：
 * @Author： chen
 * @Date 2023/10/31
 */

@Data
@ApiModel("年节产品业绩数据组")
public class YearProductSyncCustomerSubVo {

    @ApiModelProperty(value = "组类别：1 业绩，2 A组，3 B组，4 饮料组，5 酒品组，6 E组，7 90-61天，8 60-31天，9 30-01天")
    private Integer subType;

    @ApiModelProperty(value = "组名")
    private String subName;

    @ApiModelProperty(value = "年节")
    private BigDecimal annualItemsTotal;

    @ApiModelProperty(value = "同期")
    private BigDecimal annualItemsLast;

    @ApiModelProperty(value = "差异")
    private BigDecimal annualItemsDifference;

}
