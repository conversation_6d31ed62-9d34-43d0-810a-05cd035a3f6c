package com.wantwant.sfa.backend.productSynchronization.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "年节产品同期返回参数")
@Data
public class YearProductSynchronizationVo {

    @ApiModelProperty(value = "岗位id 4.总部 1.战区 12.大区 11.省区 2.分公司 10.区域经理 3.营业所")
    private Integer positionTypeId;

    @ApiModelProperty(value = "是否展示下级(0.否;1.是)[总部不用传下级]")
    private int isNextRealtime;

    @ApiModelProperty(value = "战区")
    @Excel(name = "战区",replace = {"-_null"})
    private String area;

    @ApiModelProperty(value = "大区")
    @Excel(name = "大区",replace = {"-_null"})
    private String varea;

    @ApiModelProperty(value = "省区")
    @Excel(name = "省区",replace = {"-_null"})
    private String province;

    @ApiModelProperty(value = "分公司")
    @Excel(name = "分公司",replace = {"-_null"})
    private String company;

    @ApiModelProperty(value = "营业所")
    @Excel(name = "营业所",replace = {"-_null"})
    private String departmentName;

    @ApiModelProperty(value = "营业所(年节明细)")
    //@Excel(name = "营业所",replace = {"-_null"})
    private String branch;

    @ApiModelProperty(value = "战区Id")
    private String areaId;

    @ApiModelProperty(value = "大区Id")
    private String vareaId;

    @ApiModelProperty(value = "省区Id")
    private String provinceId;

    @ApiModelProperty(value = "分公司Id")
    private String companyId;

    @ApiModelProperty(value = "营业所Id")
    private String branchId;

    @ApiModelProperty(value = "岗位")
    @Excel(name = "岗位",replace = {"-_null"})
    private String postName;

    @ApiModelProperty(value = "头像")
    @Excel(name = "头像",replace = {"-_null"})
    private String url;

    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名",replace = {"-_null"})
    private String name;

    @ApiModelProperty(value = "在职天数")
    @Excel(name = "在职天数",replace = {"-_null"})
    private String onboardDays;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "图片")
    @Excel(name = "图片",replace = {"-_null"})
    private String picture;

    @ApiModelProperty(value = "SPU")
    @Excel(name = "SPU",replace = {"-_null"})
    private String spu;

    @ApiModelProperty(value = "spuId")
    @Excel(name = "spuId",replace = {"-_null"})
    private String spuId;

    @ApiModelProperty(value = "产品线")
    @Excel(name = "产品线",replace = {"-_null"})
    private String productLine;

    @ApiModelProperty(value = "业绩-年节")
    @Excel(name = "业绩-年节",replace = {"-_null"})
    private String performanceYear;

    @ApiModelProperty(value = "业绩-同期")
    @Excel(name = "业绩-同期",replace = {"-_null"})
    private String performanceSamePeriod;

    @ApiModelProperty(value = "业绩-同比")
    @Excel(name = "业绩-同比",replace = {"-_null"})
    private String performanceYearOnYear;

    @ApiModelProperty(value = "盘价业绩-同比(年节明细 页面要用)")
    @Excel(name = "盘价业绩-同期",replace = {"-_null"})
    private String performanceSameThan;

    @ApiModelProperty(value = "业绩-同期客户差异")
    @Excel(name = "业绩-同比",replace = {"-_null"})
    private String annualItemsSupplyTotalCustomDifference;

    @ApiModelProperty(value = "业绩-新增客户差异")
    @Excel(name = "业绩-同比",replace = {"-_null"})
    private String annualItemsSupplyTotalNewCustomDifference;

    @ApiModelProperty(value = "业绩-影响百分比")
    @Excel(name = "业绩-同比",replace = {"-_null"})
    private String annualItemsSupplyTotalInfluenceRate;

    @ApiModelProperty(value = "业绩-差异")
    @Excel(name = "业绩-差异",replace = {"-_null"})
    private String performanceDiscrepancy;

    @ApiModelProperty(value = "(90-61天)年节")
    @Excel(name = "(90-61天)年节",replace = {"-_null"})
    private String ninetyYear;

    @ApiModelProperty(value = "(90-61天)同期")
    @Excel(name = "(90-61天)同期",replace = {"-_null"})
    private String ninetySamePeriod;

    @ApiModelProperty(value = "(90-61天)同比")
    @Excel(name = "(90-61天)同比",replace = {"-_null"})
    private String ninetySameThan;

    @ApiModelProperty(value = "(90-61天)差异")
    @Excel(name = "(90-61天)差异",replace = {"-_null"})
    private String ninetyDiscrepancy;

    @ApiModelProperty(value = "(60-31天)年节")
    @Excel(name = "(60-31天)年节",replace = {"-_null"})
    private String sixtyYear;

    @ApiModelProperty(value = "(60-31天)同期")
    @Excel(name = "(60-31天)同期",replace = {"-_null"})
    private String sixtySamePeriod;

    @ApiModelProperty(value = "(60-31天)同比")
    @Excel(name = "(60-31天)同期",replace = {"-_null"})
    private String sixtySameThan;

    @ApiModelProperty(value = "(60-31天)差异")
    @Excel(name = "(60-31天)差异",replace = {"-_null"})
    private String sixtyDiscrepancy;

    @ApiModelProperty(value = "(30-01天)年节")
    @Excel(name = "(30-01天)年节",replace = {"-_null"})
    private String thirtyYear;

    @ApiModelProperty(value = "(30-01天)同期")
    @Excel(name = "(30-01天)同期",replace = {"-_null"})
    private String thirtySamePeriod;

    @ApiModelProperty(value = "(30-01天)同比")
    @Excel(name = "(30-01天)同期",replace = {"-_null"})
    private String thirtySameThan;

    @ApiModelProperty(value = "(30-01天)差异")
    @Excel(name = "(30-01天)差异",replace = {"-_null"})
    private String thirtyDiscrepancy;

    @ApiModelProperty(value = "交易人数(开单人数)-年节")
    @Excel(name = "交易人数(开单人数)-年节",replace = {"-_null"})
    private int transactionsNumYear;

    @ApiModelProperty(value = "交易人数(开单人数)-同期")
    @Excel(name = "交易人数(开单人数)-同期",replace = {"-_null"})
    private int transactionsNumSamePeriod;

    @ApiModelProperty(value = "交易人数(开单人数)-同比")
    @Excel(name = "交易人数(开单人数)-同期",replace = {"-_null"})
    private int transactionsNumSameThan;

    @ApiModelProperty(value = "交易人数(开单人数)-差异")
    @Excel(name = "交易人数(开单人数)-差异",replace = {"-_null"})
    private int transactionsNumDiscrepancy;

    @ApiModelProperty(value = "开单率-年节")
    @Excel(name = "开单率-年节",replace = {"-_null"})
    private String billingRateYear;

    @ApiModelProperty(value = "开单率-同期")
    @Excel(name = "开单率-同期",replace = {"-_null"})
    private String billingRateSamePeriod;

    @ApiModelProperty(value = "开单率-差异")
    @Excel(name = "开单率-差异",replace = {"-_null"})
    private String billingRateDiscrepancy;

    @ApiModelProperty(value = "客单价(开单人均业绩)-年节")
    @Excel(name = "客单价(开单人均业绩)-年节",replace = {"-_null"})
    private String customerPerYear;

    @ApiModelProperty(value = "客单价(开单人均业绩)-同期")
    @Excel(name = "客单价(开单人均业绩)-同期",replace = {"-_null"})
    private String customerPerSamePeriod;

    @ApiModelProperty(value = "客单价(开单人均业绩)-同比")
    @Excel(name = "客单价(开单人均业绩)-同期",replace = {"-_null"})
    private String customerPerSameThan;

    @ApiModelProperty(value = "客单价(开单人均业绩)-差异")
    @Excel(name = "客单价(开单人均业绩)-差异",replace = {"-_null"})
    private String customerPerDiscrepancy;

}
