package com.wantwant.sfa.backend.productSynchronization.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "年节交易人数返回参数")
@Data
public class YearTransactionsNumVo {

    private Integer positionTypeId;

    private String organizationId;

    @ApiModelProperty(value = "是否展示下级(0.否;1.是)[总部不用传下级]")
    private int isNextRealtime;

    @ApiModelProperty(value = "客户编号")
    private String customerId;

    @ApiModelProperty(value = "memberkey")
    private String memberkey;

    @ApiModelProperty(value = "战区")
    @Excel(name = "战区",replace = {"-_null"})
    private String area;

    @ApiModelProperty(value = "大区")
    @Excel(name = "大区",replace = {"-_null"})
    private String varea;

    @ApiModelProperty(value = "省区")
    @Excel(name = "省区",replace = {"-_null"})
    private String province;

    @ApiModelProperty(value = "分公司")
    @Excel(name = "分公司",replace = {"-_null"})
    private String company;

    @ApiModelProperty(value = "营业所")
    @Excel(name = "营业所",replace = {"-_null"})
    private String branch;

    @ApiModelProperty(value = "战区Id")
    private String areaId;

    @ApiModelProperty(value = "大区Id")
    private String vareaId;

    @ApiModelProperty(value = "省区Id")
    private String provinceId;

    @ApiModelProperty(value = "分公司Id")
    private String companyId;

    @ApiModelProperty(value = "营业所Id")
    private String branchId;

    @ApiModelProperty(value = "岗位")
    private String postName;

    @ApiModelProperty(value = "头像")
    private String url;

    @ApiModelProperty(value = "在职天数")
    private String onboardDays;

    @ApiModelProperty(value = "业务姓名")
    @Excel(name = "业务姓名",replace = {"-_null"})
    private String name;

    @ApiModelProperty(value = "职位")
    @Excel(name = "职位",replace = {"-_null"})
    private String position;

    @ApiModelProperty(value = "业务手机号")
    @Excel(name = "业务手机号",replace = {"-_null"})
    private String phoneNumber;

    @ApiModelProperty(value = "当前组别信息")
    @Excel(name = "当前组别信息",replace = {"-_null"})
    private String businessGroup;

    @ApiModelProperty(value = "状态")
    @Excel(name = "状态",replace = {"-_null"})
    private String status;

    @ApiModelProperty(value = "离职日期")
    @Excel(name = "离职日期",replace = {"-_null"})
    private String resignationDate;

    @ApiModelProperty(value = "建档客户数")
    @Excel(name = "建档客户数",replace = {"-_null"})
    private Integer filingClientNum;

    @ApiModelProperty(value = "距离上一次交易天数")
    @Excel(name = "距离上一次交易天数",replace = {"-_null"})
    private Integer distanceLastTradingDays;

    @ApiModelProperty(value = "最后一次下单时间")
    @Excel(name = "最后一次下单时间",replace = {"-_null"})
    private String distanceFinallyTradingDays;

    @ApiModelProperty(value = "盘价业绩-年节")
    @Excel(name = "盘价业绩-年节",replace = {"-_null"})
    private String performanceYear;

    @ApiModelProperty(value = "盘价业绩-同期")
    @Excel(name = "盘价业绩-同期",replace = {"-_null"})
    private String performanceSamePeriod;

    @ApiModelProperty(value = "盘价业绩-同比")
    @Excel(name = "盘价业绩-同期",replace = {"-_null"})
    private String performanceSameThan;

    @ApiModelProperty(value = "盘价业绩-差异")
    @Excel(name = "盘价业绩-差异",replace = {"-_null"})
    private String performanceDiscrepancy;

    @ApiModelProperty(value = "(90-61天)年节")
    @Excel(name = "(90-61天)年节",replace = {"-_null"})
    private String ninetyYear;

    @ApiModelProperty(value = "(90-61天)同期")
    @Excel(name = "(90-61天)同期",replace = {"-_null"})
    private String ninetySamePeriod;

    @ApiModelProperty(value = "(90-61天)同比")
    @Excel(name = "(90-61天)同期",replace = {"-_null"})
    private String ninetySameThan;

    @ApiModelProperty(value = "(90-61天)差异")
    @Excel(name = "(90-61天)差异",replace = {"-_null"})
    private String ninetyDiscrepancy;

    @ApiModelProperty(value = "(60-31天)年节")
    @Excel(name = "(60-31天)年节",replace = {"-_null"})
    private String sixtyYear;

    @ApiModelProperty(value = "(60-31天)同期")
    @Excel(name = "(60-31天)同期",replace = {"-_null"})
    private String sixtySamePeriod;

    @ApiModelProperty(value = "(60-31天)同比")
    @Excel(name = "(60-31天)同期",replace = {"-_null"})
    private String sixtySameThan;

    @ApiModelProperty(value = "(60-31天)差异")
    @Excel(name = "(60-31天)差异",replace = {"-_null"})
    private String sixtyDiscrepancy;

    @ApiModelProperty(value = "(30-01天)年节")
    @Excel(name = "(30-01天)年节",replace = {"-_null"})
    private String thirtyYear;

    @ApiModelProperty(value = "(30-01天)同期")
    @Excel(name = "(30-01天)同期",replace = {"-_null"})
    private String thirtySamePeriod;

    @ApiModelProperty(value = "(30-01天)同比")
    @Excel(name = "(30-01天)同期",replace = {"-_null"})
    private String thirtySameThan;

    @ApiModelProperty(value = "(30-01天)差异")
    @Excel(name = "(30-01天)差异",replace = {"-_null"})
    private String thirtyDiscrepancy;

}
