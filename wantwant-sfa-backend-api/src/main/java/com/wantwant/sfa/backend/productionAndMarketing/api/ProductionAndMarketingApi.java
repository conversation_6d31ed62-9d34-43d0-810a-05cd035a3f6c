package com.wantwant.sfa.backend.productionAndMarketing.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.productionAndMarketing.request.*;
import com.wantwant.sfa.backend.productionAndMarketing.vo.*;
import com.wantwant.sfa.backend.realData.vo.SafetyStockConditionVo;
import com.wantwant.sfa.backend.realData.vo.SafetyStockLogVo;
import com.wantwant.sfa.backend.realData.vo.SafetyStockVo;
import com.wantwant.sfa.backend.realData.vo.SkuSpecificationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(value = "ProductionAndMarketingApi", tags = "产销API")
public interface ProductionAndMarketingApi {

  @ApiOperation(value = "获取产销列表", notes = "获取产销列表", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/list")
  @Deprecated
  Response<Page<ProductionAndMarketingVo>> getProductionAndMarketingList(
      @RequestBody ProductionAndMarketingRequest request);

  @ApiOperation(value = "获取产销筛选条件", notes = "获取产销筛选条件", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/screening")
  @Deprecated
  Response<ProductionGroupbyVo> getProductionAndMarketingscreening();

  @ApiOperation(value = "销售预估导入数据", notes = "销售预估导入数据", httpMethod = "POST")
  @PostMapping("/salesForecast/import")
  Response<Integer> salesForecastImport(MultipartHttpServletRequest request);

  @ApiOperation(value = "获取库存列表", notes = "获取库存列表", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/Inventory/list")
  Response<Page<InventoryVo>> getInventoryList(@RequestBody InventoryRequest request);

  @ApiOperation(value = "库存列表获取产品下拉", notes = "库存列表获取产品下拉")
  @GetMapping("/ProductionAndMarketing/Inventory/analysisLine")
  Response<List<SkuSpecificationVo>> getInventorySkuSpecification(@ApiParam(name = "endDate", value = "日期") @RequestParam(value = "endDate",required = true) String endDate,
                                                                  @ApiParam(name = "month", value = "月份") @RequestParam(value = "month",required = true) String month,
                                                                       @ApiParam(name = "organizationId", value = "组织id") @RequestParam(value = "organizationId",required = true) String organizationId,
                                                                  @ApiParam(name = "person", value = "工号") @RequestParam(value = "person",required = true) String person);

  @ApiOperation(value = "获取库存筛选条件", notes = "获取库存筛选条件", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/Inventory/screening")
  Response<InventoryGroupbyVo> getInventoryscreening();

  @ApiOperation(value = "仓库详情&缺货明细",notes = "仓库详情&缺货明细", httpMethod = "POST")
  @PostMapping("/zwBigData/WarehouseStorage")
  Response<Page<InventoryDetailsVo>> WarehouseStorage(@RequestBody WarehouseStorageRequest request);

  @ApiOperation(value = "新实时库存-列表分页",notes = "新实时库存-列表分页")
  @PostMapping(value = "/realTimeInventory/query")
  Response<IPage<RealTimeInventoryVo>> queryRealTimeInventoryList(@RequestBody @Validated RealTimeInventoryRequest request);

  @ApiOperation(value = "新实时库存-列表下载",notes = "新实时库存-列表下载")
  @PostMapping(value = "/realTimeInventory/down")
  void downRealTimeInventoryList(@RequestBody @Validated RealTimeInventoryRequest request, HttpServletResponse res, HttpServletRequest req);

  @ApiOperation(value = "新实时库存-列表枚举",notes = "新实时库存-列表枚举")
  @PostMapping(value = "/realTimeInventory/queryEnums")
  Response<RealTimeInventoryEnums> queryRealTimeInventoryEnums(@RequestBody @Validated RealTimeInventoryRequest request);

  @ApiOperation(value = "新实时库存-异常锁库",notes = "新实时库存-异常锁库")
  @PostMapping(value = "/realTimeInventory/queryExceptionLockLibraryList")
  Response<List<RealTimeInventoryExceptionLockLibraryVo>> queryRealTimeInventoryExceptionLockLibraryList(@RequestBody @Validated RealTimeInventoryExceptionLockLibraryRequest request);

  @ApiOperation(value = "新实时库存-当前可出货数量列表 枚举查询请求",notes = "新实时库存-当前可出货数量列表 枚举查询请求")
  @PostMapping(value = "/realTimeInventory/queryInventoryDetailEnums")
  Response<InventoryDetailGroupEnumsVo> queryInventoryDetailEnums(@RequestBody @Validated InventoryDetailsQueryRequest request);

  @ApiOperation(value = "新实时库存-当前可出货数量列表",notes = "新实时库存-当前可出货数量列表")
  @PostMapping(value = "/realTimeInventory/queryNormalInventoryDetails")
  Response<IPage<NormalInventoryDetailVo>> queryNormalInventoryDetails(@RequestBody @Validated InventoryDetailsQueryRequest request);

  @ApiOperation(value = "新实时库存-异常库存明细列表",notes = "新实时库存-异常库存明细列表")
  @PostMapping(value = "/realTimeInventory/queryAbnormalInventoryDetails")
  @Deprecated
  Response<IPage<AbnormalInventoryDetailVo>> queryAbnormalInventoryDetails(@RequestBody @Validated InventoryDetailsQueryRequest request);

  @ApiOperation(value = "库存快速查找-列表枚举",notes = "库存快速查找-列表枚举")
  @PostMapping(value = "/inventoryQuickLook/queryEnums")
  Response<List<RealTimeInventorySkuInfoEnumsVo>> queryInventoryQuickLookEnumsList(@RequestBody @Validated InventoryQuickLookRequest request);
  @ApiOperation(value = "库存快速查找-列表分页",notes = "库存快速查找-列表分页")
  @PostMapping(value = "/inventoryQuickLook/query")
  Response<IPage<InventoryQuickLookVo>> queryInventoryQuickLookList(@RequestBody @Validated InventoryQuickLookRequest request);

  @ApiOperation(value = "库存快速查找-列表下载",notes = "库存快速查找-列表下载")
  @PostMapping(value = "/inventoryQuickLook/down")
  void downInventoryQuickLookList(@RequestBody @Validated InventoryQuickLookRequest request,HttpServletRequest req,HttpServletResponse res);

  @ApiOperation(value = "库存快速查找-优质/异常 库存月份明细",notes = "库存快速查找-优质/异常 库存月份明细")
  @PostMapping(value = "/inventoryQuickLook/detailsQuery")
  Response<IPage<InventoryQuickLookDetailsVo>> inventoryQuickLookDetailsQuery(@RequestBody @Validated InventoryQuickLookDetailsRequest request);

  @ApiOperation(value = "获取所有线别", notes = "获取商品数据与产品线")
  @GetMapping("/zwBigData/queryProductLineName")
  Response<List<String>> queryProductLineName();

  @ApiOperation(value = "仓库详情&缺货明细-导出", notes = "仓库详情&缺货明细-导出")
  @PostMapping(value = "/zwBigData/WarehouseStorage/export")
  void exportTransportMonitoring(@RequestBody WarehouseStorageRequest request);

  @ApiOperation(value = "获取上架率明细表", notes = "获取上架率明细表", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/shelvesRate/detail/list")
  @Deprecated
  Response<Page<ShelvesRateDetailVo>>  getShelvesRateDetail(@RequestBody @Valid ShelvesRateRequest request);


  @ApiOperation(value = "获取上下架监控表", notes = "获取上下架监控表", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/shelvesRate/monitoring/list")
  @Deprecated
  Response<Page<ShelvesRateMonitoringVo>>  getShelvesRateMonitoring(@RequestBody @Valid ShelvesMonitoringRequest request);


  @ApiOperation(value = "获取上架率明细标签", notes = "获取上架率明细标签", httpMethod = "GET")
  @GetMapping("/ProductionAndMarketing/nationLabel")
  Response<List<String>> selectNationLabel(@ApiParam(value = "month",required = true)  @RequestParam String month);

  /**
   * 列表查询为汇总信息
   * export导出接口/storageServiceAchievement/list/export为明细信息
   * @param request
   * @return
   */
  @ApiOperation(value = "获取承运商服务列表", notes = "获取承运商服务列表", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/storageServiceAchievement/list")
  Response<IPage<StorageServiceAchievementVo>>  getStorageServiceAchievement(@RequestBody @Valid StorageServiceAchievementRequest request);

  @ApiOperation(value = "获取承运商列表", notes = "获取承运商列表", httpMethod = "GET")
  @GetMapping("/ProductionAndMarketing/deliveryCompanyName/list")
  Response<List<String>> selectDeliveryCompanyName();


  @ApiOperation(value = "获取分公司招聘报表", notes = "获取分公司招聘报表", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/CompanyRecruitment/list")
  Response<Page<CompanyRecruitmentVo>>  getCompanyRecruitmentList(@RequestBody @Valid CompanyRecruitmentRequest request);

  @ApiOperation(value = "获取分公司招聘报表导出", notes = "获取分公司招聘报表导出", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/CompanyRecruitment/export")
  void  getCompanyRecruitmentExport(@RequestBody @Valid CompanyRecruitmentRequest request);


  @ApiOperation(value = "获取分公司招聘渠道", notes = "获取分公司招聘渠道", httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/CompanyRecruitment/source")
  Response<List<String>>  getCompanyRecruitmentSource();

  @ApiOperation(value = "添加,删除到货提醒",notes = "添加,删除到货提醒", httpMethod = "POST")
  @PostMapping("/zwBigData/WarehouseReminder")
  Response WarehouseReminder(@RequestBody WarehouseReminderRequest request);


  @ApiOperation(value = "到货通知接口", notes = "到货通知接口", httpMethod = "POST")
  @PostMapping("/zwBigData/NoticeGoods")
  Response NoticeGoods(@RequestBody List<NoticeGoodsRequest> request);

  @ApiOperation(value = "文宣品枚举列表查询", notes = "文宣品枚举列表查询", httpMethod = "POST")
  @PostMapping("/zwBigData/queryPublicityEnums")
  Response<PublicityEnumsDto> queryPublicityEnums();
  @ApiOperation(value = "文宣品查询", notes = "文宣品查询", httpMethod = "POST")
  @PostMapping("/zwBigData/QueryPublicity")
  Response<IPage<PublicityVo>> queryPublicity(@RequestBody PublicityRequest request);

  @ApiOperation(value = "文宣品导出", notes = "文宣品导出", httpMethod = "POST")
  @PostMapping("/zwBigData/QueryPublicity/export")
  void queryPublicityExport(@RequestBody PublicityRequest request,HttpServletRequest req,HttpServletResponse res);

  @ApiOperation(value = "根据组织ID获取仓库", notes = "根据组织ID获取仓库", httpMethod = "GET")
  @GetMapping("/zwBigData/queryWarehouse")
  Response<List<Map<String,Object>>> queryWarehouse(@ApiParam(name = "employeeId", value = "当前登录人", required = true) @RequestParam String employeeId);

  @ApiOperation(value = "货需管理",notes = "货需管理" , httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/safetyStock/select")
  Response<Page<SafetyStockVo>> querySafetyStock(@RequestBody SafetyStockRequest request);

  @ApiOperation(value = "货需管理新增/修改",notes = "货需管理新增/修改" , httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/safetyStock/insert")
  Response safetyStockInert(@RequestBody SafetyStockInsertRequest request);

  @ApiOperation(value = "货需管理历史",notes = "货需管理历史" , httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/safetyStock/log")
  Response<SafetyStockLogVo> safetyStockLog(@RequestBody safetyStockLogRequest request);

  @ApiOperation(value = "货需管理条件",notes = "货需管理条件" , httpMethod = "GET")
  @GetMapping("/ProductionAndMarketing/safetyStock/condition")
  Response<SafetyStockConditionVo> safetyStockCondition();

  @ApiOperation(value = "货需管理导出",notes = "货需管理新增/修改" , httpMethod = "POST")
  @PostMapping("/ProductionAndMarketing/safetyStock/export")
  void safetyStockExport(@RequestBody SafetyStockRequest request);


}
