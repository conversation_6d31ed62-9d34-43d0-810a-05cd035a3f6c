package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "分公司招聘报表传参参数")
public class CompanyRecruitmentRequest extends PageParam {

    @ApiModelProperty(value = "时间查询")
    @NotBlank(message = "时间不能为空")
    private String month;

    @ApiModelProperty(value = "区域查询")
    private String organizationId;

    @ApiModelProperty(value = "报名渠道")
    private String registrationChannel;

    @ApiModelProperty(value = "是否分页(0.否;1.是)")
    private Integer isPage;

    @ApiModelProperty(value = "岗位名称")
    private String position;
}
