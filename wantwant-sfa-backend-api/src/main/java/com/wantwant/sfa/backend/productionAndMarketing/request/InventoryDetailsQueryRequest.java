package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.request
 * @Description:
 * @Date: 2024/10/23 8:59
 */
@ApiModel("实时库存-常态库存/异常库存 明细查询请求")
@Data
public class InventoryDetailsQueryRequest extends PageParam {
    @ApiModelProperty("员工id")
    @NotBlank(message = "员工id不允许为空")
    private String employeeId;

    @ApiModelProperty("月份")
    @NotBlank(message = "月份不允许为空")
    private String yearMonth;

    @ApiModelProperty("时间类型")
    @NotBlank(message = "时间类型不允许为空")
    private String dateTypeId;

    @ApiModelProperty("sku")
    @NotBlank(message = "sku不允许为空")
    private String skuId;

    @ApiModelProperty("渠道id")
    @NotBlank(message = "渠道id不允许为空")
    private String channelId;

    @ApiModelProperty(value = "筛选组织类型",hidden = true)
    private String filterOrganizationType;

    @ApiModelProperty(value = "筛选字段：产品组")
    private Integer filterBusinessGroup;

    @ApiModelProperty(value = "筛选字段：组织id")
    private String filterOrganizationId;

    @ApiModelProperty(value = "筛选字段：数据留存月份")
    private String filterTheYearMonth;

    @ApiModelProperty("排序类型 desc/asc 默认desc")
    private String sortType;

    @ApiModelProperty("排序字段：直接输入排序字段名")
    private String sortName;

}
