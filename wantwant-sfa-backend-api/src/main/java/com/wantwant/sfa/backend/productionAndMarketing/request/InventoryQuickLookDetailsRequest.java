package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.request
 * @Description:
 * @Date: 2024/10/23 13:15
 */
@ApiModel("库存快速查找-优质库存 异常库存 月份明细 请求")
@Data
public class InventoryQuickLookDetailsRequest extends PageParam {

    @ApiModelProperty("月份")
    private String yearMonth;

    @ApiModelProperty("时间类型")
    private String dateTypeId;

    @ApiModelProperty(value = "时间维度下的最新月份",hidden = true)
    private String theMonth;

    @ApiModelProperty("员工id")
    @NotBlank(message = "员工id不允许为空")
    private String employeeId;

    @ApiModelProperty("sku")
    @NotBlank(message = "sku不允许为空")
    private String skuId;

    @ApiModelProperty("渠道id")
    @NotBlank(message = "渠道id不允许为空")
    private String channelId;

    @ApiModelProperty("查询类型 0优质库存明细 1异常库存明细 2实际可出货数量")
    @NotNull(message = "查询类型不允许为空")
    private Integer queryType;

    @ApiModelProperty("筛选产品组")
    @NotBlank(message = "筛选产品组id不允许为空")
    private String filterBusinessGroupId;

    @ApiModelProperty("筛选组织")
    @NotBlank(message = "筛选组织id不允许为空")
    private String filterOrganizationId;

    @ApiModelProperty("筛选组织类型")
    private String filterOrganizationType;

    @ApiModelProperty("排序类型 desc/asc 默认desc")
    private String sortType;

    @ApiModelProperty("排序字段：直接输入排序月份")
    private String sortName;

    @ApiModelProperty(hidden = true)
    private List<String> organizationIds;

    @ApiModelProperty(hidden = true)
    private String sortedYearMonth;
}
