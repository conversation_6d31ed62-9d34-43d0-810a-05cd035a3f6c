package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.request
 * @Description:
 * @Date: 2024/10/21 13:42
 */
@ApiModel("库存快速查找请求参数")
@Data
public class InventoryQuickLookRequest extends PageParam {

    @ApiModelProperty("员工id")
    @NotBlank(message = "员工id不允许为空")
    private String employeeId;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty("月份 不传默认为当前最新月份")
    private String yearMonth;

    @ApiModelProperty("时间类型")
    @NotBlank(message = "时间类型不允许为空")
    private String dateTypeId;


    @ApiModelProperty("查询类型：0 按产品组 1按战区 2按大区 3按省区 4按分公司")
//    @NotNull(message = "查询类型不能为空")
    private Integer queryType;

    @ApiModelProperty(value = "时间维度下的最新月份",hidden = true)
    private String theMonth;

    @ApiModelProperty(value = "组织类型",hidden = true)
    private String organizationType;

    @ApiModelProperty(value = "产品组")
    private Integer businessGroup;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("产品id")
    private String skuId;

    @ApiModelProperty("产品线列表")
    private List<String> lineNames;

    @ApiModelProperty("常态标签列表")
    private List<String> tagNames;

    @ApiModelProperty(value = "常态标签列表null 标识 1为null",hidden = true)
    private Integer tagNameFlag;

    @ApiModelProperty("状态列表")
    private List<String> statusNames;

    @ApiModelProperty(value = "状态列表null 标识 1为null",hidden = true)
    private Integer statusNameFlag;

    @ApiModelProperty("仓别列表")
    private List<String> channelNames;

    @ApiModelProperty("是否为管控品")
    private List<String> skuIsControlledList;

    @ApiModelProperty(value = "是否为管控品",hidden = true)
    private List<Integer> skuIsControlledStatusList;

    @ApiModelProperty(value = "null 标识 1为null",hidden = true)
    private Integer skuIsControlledFlag;

    @ApiModelProperty(value = "关联查询使用：skuIds",hidden = true)
    private List<String> skuIds;

    @ApiModelProperty(value = "关联查询使用：channelIds",hidden = true)
    private List<String> channelIds;

}
