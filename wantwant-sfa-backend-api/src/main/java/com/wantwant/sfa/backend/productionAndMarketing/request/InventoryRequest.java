package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "库存传参")
public class InventoryRequest extends PageParam {

  @ApiModelProperty(value = "操作人工号")
  private String person;

  @ApiModelProperty(value = "兼岗组织")
  private List<String> partJob;

  @ApiModelProperty(value = "操作人组织id")
  private String organizationId;

  @ApiModelProperty(value = "产品组")
  private Integer businessGroup;

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "结束时间")
  private String endTime;

  @ApiModelProperty(value = "月日期")
  private String month;

  @ApiModelProperty(value = "产品查询")
  private String skuName;

  @ApiModelProperty(value = "是否常态售卖")
  private String isNormalSale;

  @ApiModelProperty(value = "渠道")
  private String channelName;

  @ApiModelProperty(value = "效期标签")
  private String validityLabel;

  @ApiModelProperty(value = "生产线")
  private String lineName;

  @ApiModelProperty(value = "排序名称")
  private String orderName;

//  @ApiModelProperty(value = "商品标签")
//  private String skuTag;

  @ApiModelProperty(value = "排序类型(0.倒序；1.升序)")
  private Integer orderType;

  @ApiModelProperty(value = "常态标签")
  private String normalTag;


}
