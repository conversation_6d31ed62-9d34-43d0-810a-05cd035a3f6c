package com.wantwant.sfa.backend.productionAndMarketing.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("到货通知接口请求入参")
@ToString
public class NoticeGoodsRequest {

    @ApiModelProperty("渠道Id")
    @NotNull(message = "渠道Id不能为空")
    private String channelId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("sku")
    @NotNull(message = "sku不能为空")
    private String sku;

    @ApiModelProperty("货品名称")
    private String skuName;

    @ApiModelProperty("批次号")
    private String batchCode;

    @ApiModelProperty("本次上架库存")
    private String putOnShelvesNums;

    @ApiModelProperty("平台现可售库存")
    private String AvailableForSale;

    private Integer cycle;

    @ApiModelProperty("推送时间")
    private LocalDateTime createTime;

    @ApiModelProperty("产品组")
    private List<String> businessGroupList;

}
