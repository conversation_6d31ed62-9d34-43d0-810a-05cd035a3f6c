package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "产销传参")
public class ProductionAndMarketingRequest extends PageParam {

  @ApiModelProperty(value = "操作人组织id")
  private String organizationId;

  @ApiModelProperty(value = "开始时间")
  private String startTime;

  @ApiModelProperty(value = "结束时间")
  private String endTime;

  @ApiModelProperty(value = "月日期")
  private String month;

  @ApiModelProperty(value = "仓库")
  private String channelName;

  @ApiModelProperty(value = "上架状态")
  private Integer isShow;

  @ApiModelProperty(value = "产品查询")
  private String skuName;

  @ApiModelProperty(value = "标签")
  private String label;

  @ApiModelProperty(value = "生产线")
  private String lineName;

  @ApiModelProperty(value = "排序名称")
  private String orderName;

  @ApiModelProperty(value = "排序类型(0.倒序；1.升序)")
  private Integer orderType;
}
