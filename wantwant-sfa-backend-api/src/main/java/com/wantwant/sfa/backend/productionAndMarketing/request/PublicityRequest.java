package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "文宣品传参")
public class PublicityRequest extends PageParam {

    @ApiModelProperty(value = "仓库查询")
    private List<String> channelIds;

    @ApiModelProperty(value = "线别查询")
    private List<String> lineNames;

    @ApiModelProperty(value = "产品查询")
    private String skuId;

    @ApiModelProperty(value = "sku标签")
    private List<String> tagNames;

    @ApiModelProperty("平台上架状态(1:上架/10:补货中/0:下架)")
    private List<String> showFlags;

    @ApiModelProperty("预警状态：正常/紧急预警/预警")
    private List<String> warningStatusList;

    @ApiModelProperty("排序字段：直接输入字段名称")
    private String sortName;

    @ApiModelProperty("排序规则:desc/asc")
    private String sortType;


}
