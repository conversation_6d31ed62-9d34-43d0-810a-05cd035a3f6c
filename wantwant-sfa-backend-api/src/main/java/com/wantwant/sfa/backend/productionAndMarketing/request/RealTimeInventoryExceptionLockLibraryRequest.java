package com.wantwant.sfa.backend.productionAndMarketing.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.request
 * @Description:
 * @Date: 2024/9/23 9:06
 */
@ApiModel("新实时库存-异常锁库请求参数")
@Data
public class RealTimeInventoryExceptionLockLibraryRequest {

    @NotBlank(message = "员工id不允许为空")
    private String employeeId;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("仓别")
    private String channelName;

    @ApiModelProperty("月份")
    @NotBlank(message = "月份不允许为空")
    private String yearMonth;

    @ApiModelProperty("时间类型")
    @NotBlank(message = "时间类型不允许为空")
    private String dateTypeId;

    @ApiModelProperty(value = "时间维度下的最新月份",hidden = true)
    private String theMonth;

    @ApiModelProperty(value = "组织类型",hidden = true)
    private String organizationType;

    @ApiModelProperty(value = "产品组",hidden = true)
    private Integer businessGroup;

    @ApiModelProperty("排序类型 desc/asc 默认desc")
    private String sortType;

    @ApiModelProperty("排序字段：直接输入排序字段名")
    private String sortName;
}
