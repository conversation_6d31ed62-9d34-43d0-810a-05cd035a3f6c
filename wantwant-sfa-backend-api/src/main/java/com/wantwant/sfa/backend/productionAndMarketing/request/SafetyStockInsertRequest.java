package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.realData.vo.SafetyStockDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("货需管理新增/修改")
public class SafetyStockInsertRequest {

    @ApiModelProperty("物料编码")
    private String sku;

    @ApiModelProperty("物料名称")
    private String skuName;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("仓库数据")
    private List<SafetyStockDetailVo> channelMessage;

    @ApiModelProperty("渠道Id/前端不传")
    private String channelId;

    @ApiModelProperty("渠道名称/前端不传")
    private String channelName;

    @ApiModelProperty("天数/前端不传")
    private String dateNums;

    @ApiModelProperty("备注")
    private String noets;

    @ApiModelProperty("结束日期")
    private String deleteTime;

}
