package com.wantwant.sfa.backend.productionAndMarketing.request;


import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "安全库存查询传参")
public class SafetyStockRequest  extends PageParam {


    @ApiModelProperty("标签")
    private List<String> labels;

    @ApiModelProperty("线别")
    private List<String> lines;

}
