package com.wantwant.sfa.backend.productionAndMarketing.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "销售预估导入传参")
public class SalesForecastRequest {

  @Excel(name = "SKU")
  private String sku;

  @Excel(name = "月份")
  private String date;

  @Excel(name = "在库+在途周转天数")
  private Double medianargin;

  @Excel(name = "1期提报")
  private Double firstReport;

  @Excel(name = "3期提报")
  private Double thirdReport;

  @Excel(name = "3期调整量")
  private Double thirdAdjust;

  @Excel(name = "4期调整量")
  private Double fourthAdjust;

  @Excel(name = "1期开单")
  private Double firstBilling;

  @Excel(name = "2期开单")
  private Double secondBilling;

  @Excel(name = "3期开单")
  private Double thirdBilling;

  @Excel(name = "4期开单")
  private Double fourBilling;

  @Excel(name = "开单总量")
  private Double billingTotal;

  @Excel(name = "开单差异量")
  private Double billingDifference;

  @Excel(name = "无法供货")
  private Double unableSupply;

  @Excel(name = "实际供货")
  private Double actualSupply;

  @Excel(name = "已入库")
  private Double warehoused;

  @Excel(name = "借货调拨")
  private Double allot;

  @Excel(name = "调拨在途量截止至今")
  private Double allotTransfer;

  @Excel(name = "1期货需剩余")
  private Double firstSurplusDemand;

  @Excel(name = "2期货需剩余")
  private Double secondSurplusDemand;

  @Excel(name = "3期货需剩余")
  private Double thirdSurplusDemand;

  @Excel(name = "4期货需剩余")
  private Double fourSurplusDemand;

  @Excel(name = "本月货需剩余")
  private Double surplusDemand;

  @Excel(name = "调拔已入库")
  private Double allotInStorage;

  @Excel(name = "仓库类型(1.济南;2.长沙;3.隆昌;4.沈阳)")
  private Integer warehouseType;
}
