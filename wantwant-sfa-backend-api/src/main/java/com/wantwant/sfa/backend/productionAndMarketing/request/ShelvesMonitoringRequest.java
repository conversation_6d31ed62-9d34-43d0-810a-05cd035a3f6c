package com.wantwant.sfa.backend.productionAndMarketing.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@ApiModel(value = "上下架监控传参")
public class ShelvesMonitoringRequest{

    @ApiModelProperty(value = "时间 年-月-日")
    @NotNull(message = "时间不能为空")
    private LocalDate time;
}
