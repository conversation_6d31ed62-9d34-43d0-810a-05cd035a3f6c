package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "上架率传参")
public class ShelvesRateRequest extends PageParam {

    @ApiModelProperty(value = "月份")
    @NotBlank(message = "月份不能为空")
    private String month;

    @ApiModelProperty(value = "开始月份")
    private String startMonth;

    @ApiModelProperty(value = "结束月份")
    private String endMonth;

    @ApiModelProperty(value = "产品名称")
    private String skuName;

    @ApiModelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty(value = "标签")
    private String nationLabel;

    @ApiModelProperty(value = "是否显示当日sku(0.否;1.是)")
    private int isShowSku;
}
