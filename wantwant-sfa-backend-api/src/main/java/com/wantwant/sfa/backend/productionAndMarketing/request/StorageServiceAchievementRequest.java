package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@ApiModel(value = "承运商服务传参")
@Data
public class StorageServiceAchievementRequest extends PageParam {

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDate endTime;

    @ApiModelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty(value = "承运商")
    private List<String> deliveryCompanyNames;

    @ApiModelProperty("排序字段:直接传字段名称")
    private String sortName;
    @ApiModelProperty("排序：asc/desc")
    private String sortType;
}
