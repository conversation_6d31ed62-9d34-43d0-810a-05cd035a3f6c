package com.wantwant.sfa.backend.productionAndMarketing.request;


import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("添加,删除到货提醒 请求入参")
@ToString
public class WarehouseReminderRequest  {

    @ApiModelProperty("渠道Id")
    private String channelId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("skuId")
    private String sku;

    @ApiModelProperty(value = "产品名称")
    private String skuName;

    @ApiModelProperty(value = "规格")
    private String skuSpec;

    @ApiModelProperty(value = "口味")
    private String flavor;

    @ApiModelProperty("操作人Id")
    private String employeeId;

    @ApiModelProperty("组织id")
    private  String organizationId;

    @ApiModelProperty("0:添加 & 1:删除")
    @NotNull
    private Integer type;
}
