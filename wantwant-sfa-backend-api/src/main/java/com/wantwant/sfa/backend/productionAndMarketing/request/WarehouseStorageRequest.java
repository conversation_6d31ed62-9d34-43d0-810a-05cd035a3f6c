package com.wantwant.sfa.backend.productionAndMarketing.request;

import com.wantwant.commons.pagination.Pagination;
import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("仓库详情 及 缺货明细 请求入参")
@ToString
public class WarehouseStorageRequest  extends Pagination {



    @ApiModelProperty("组织Id")
    private String organizationId;

    @ApiModelProperty("渠道Id")
    private List<String> channelId;

    @ApiModelProperty("员工Id")
    private String employeeId;

    @ApiModelProperty("1:仓库详情 2：缺货详情")
    private Integer queryType;

    @ApiModelProperty("缺货明细 1：线别排序 2:产品名称 3:销售数量 4：库存 5：标签顺序  6：本月下架天数 7：业绩占比 8：常态库存数 null则默认按照线别排序 ")
    private Integer sortType;

    @ApiModelProperty("排序规则:1升序,2降序 null则标识升序规则")
    private Integer sortOrder;

    @ApiModelProperty("品项名称")
    private String skuName;

    @ApiModelProperty("线别筛选")
    private List<String> lineName;

    @ApiModelProperty("标签筛选")
    private List<String> label;

    @ApiModelProperty("平台筛选筛选")
    private List<String> sheftFlag;

    @ApiModelProperty("动销上下架")
    private List<String> mpStatus;

    @ApiModelProperty(name = "后台处理逻辑参数-前端无需关注", value = "后台处理逻辑参数-前端无需关注")
    private Integer pageFlag;

    @ApiModelProperty("是否新品 1:是 0:否")
    private Integer newProductFlag;

    @ApiModelProperty("wms上架状态 1-上架 10-缺货中")
    private Integer wmsShow;

    @ApiModelProperty("动销上架状态 1-上架 10-缺货中")
    private Integer  driveSalesIsshow;

    @ApiModelProperty("组别")
    private Integer businessGroupId;

    @ApiModelProperty("组别Code")
    private String businessGroupCode;

    @ApiModelProperty("默认组织Id")
    private List<String> deOrganizationIds;
}

