package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/10/23 8:57
 */
@ApiModel("实时库存-异常库存 明细查询返回")
@Data
public class AbnormalInventoryDetailVo {

    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("产品组code")
    private String businessGroupCode;

    @ApiModelProperty("产品组名称")
    private String businessGroupName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("仓库id")
    private String channelId;

    @ApiModelProperty("仓别")
    private String channelName;

    @ApiModelProperty("战区组织id")
    private String areaId;

    @ApiModelProperty("战区组织名称")
    private String areaName;

    @ApiModelProperty("大区组织id")
    private String vareaId;

    @ApiModelProperty("大区组织名称")
    private String vareaName;

    @ApiModelProperty("省区组织id")
    private String provinceId;

    @ApiModelProperty("省区组织名称")
    private String provinceName;

    @ApiModelProperty("分公司组织id")
    private String companyId;

    @ApiModelProperty("分公司组织名称")
    private String companyName;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("全组织名称")
    private String fullOrganizationName;


    @ApiModelProperty("异常锁库数量(箱数)")
    private Integer abnormalLockedInventoryBoxes;
}
