package com.wantwant.sfa.backend.productionAndMarketing.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "分公司招聘报表返回参数")
public class CompanyRecruitmentVo {

    @ApiModelProperty(value = "渠道")
    private String source;

    @Excel(name = "大区")
    @ApiModelProperty(value = "大区")
    private String area;

    @Excel(name = "大区id")
    @ApiModelProperty(value = "大区")
    private String areaId;

    @Excel(name = "分公司")
    @ApiModelProperty(value = "分公司")
    private String company;

    @Excel(name = "分公司id")
    @ApiModelProperty(value = "分公司")
    private String companyId;

    @Excel(name = "营业所")
    @ApiModelProperty(value = "营业所")
    private String branch;

    @Excel(name = "岗位")
    @ApiModelProperty(value = "岗位")
    private String jobs;

    @Excel(name = "姓名")
    @ApiModelProperty(value = "姓名")
    private String name;

    @Excel(name = "入职日期")
    @ApiModelProperty(value = "入职日期")
    private String inductionDate;

    @Excel(name = "负责人资姓名")
    @ApiModelProperty(value = "负责人资姓名")
    private String humanResourcesName;

    /**
     * 区域经理
     */

    @Excel(name = "在岗人数(区域经理)")
    @ApiModelProperty(value = "在岗人数(区域经理)")
    private Integer onJobNum;

    @Excel(name = "在岗率(区域经理)")
    @ApiModelProperty(value = "在岗率(区域经理)")
    private String onJobRate;

    @Excel(name = "编制管控在岗率(区域经理)")
    @ApiModelProperty(value = "编制管控在岗率(区域经理)")
    private String establishmentControlOnJobRate;

    @Excel(name = "招聘目标(区域经理)")
    @ApiModelProperty(value = "招聘目标(区域经理)")
    private Integer recruitmentTarget;

    @Excel(name = "招聘目标达成率(区域经理)")
    @ApiModelProperty(value = "招聘目标达成率(区域经理)")
    private String recruitmentTargetRate;

    @Excel(name = "本月入职人数(区域经理)")
    @ApiModelProperty(value = "本月入职人数(区域经理)")
    private Integer thisMonthEmployeesNum;

    @Excel(name = "本月离职人数(区域经理)")
    @ApiModelProperty(value = "本月离职人数(区域经理)")
    private Integer thisMonthDeparturesNum;

    @Excel(name = "招聘费用(区域经理)")
    @ApiModelProperty(value = "招聘费用(区域经理)")
    private Integer recruitmentCost;

    /**
     * 区域经理招聘流程
     */

    @Excel(name = "简历数(区域经理招聘流程)")
    @ApiModelProperty(value = "简历数(区域经理招聘流程)")
    private Integer resumeNumber;

    @Excel(name = "面试人数(区域经理招聘流程)")
    @ApiModelProperty(value = "面试人数(区域经理招聘流程)")
    private Integer intervieweesNum;

    @Excel(name = "面试率(区域经理招聘流程)")
    @ApiModelProperty(value = "面试率(区域经理招聘流程)")
    private String intervieweesRate;

    @Excel(name = "面试通过人数(区域经理招聘流程)")
    @ApiModelProperty(value = "面试通过人数(区域经理招聘流程)")
    private Integer interviewPassNum;

    @Excel(name = "面试通过率(区域经理招聘流程)")
    @ApiModelProperty(value = "面试通过率(区域经理招聘流程)")
    private String interviewPassRate;

    @Excel(name = "入职人数(区域经理招聘流程)")
    @ApiModelProperty(value = "入职人数(区域经理招聘流程)")
    private Integer inductionNum;

    @Excel(name = "面试入职率(区域经理招聘流程)")
    @ApiModelProperty(value = "面试入职率(区域经理招聘流程)")
    private String interviewEntryRate;

    /**
     * 合伙人人员数据
     */

    @Excel(name = "在岗人数(合伙人人员数据)")
    @ApiModelProperty(value = "在岗人数(合伙人人员数据)")
    private Integer onJobNumPartner;

    @Excel(name = "在岗率(合伙人人员数据)")
    @ApiModelProperty(value = "在岗率(合伙人人员数据)")
    private String onJobRatePartner;

    @Excel(name = "编制管控在岗率(合伙人人员数据)")
    @ApiModelProperty(value = "编制管控在岗率(合伙人人员数据)")
    private String establishmentControlOnJobRatePartner;

    @Excel(name = "招聘目标(合伙人人员数据)")
    @ApiModelProperty(value = "招聘目标(合伙人人员数据)")
    private Integer recruitmentTargetPartner;

    @Excel(name = "招聘目标达成率(合伙人人员数据)")
    @ApiModelProperty(value = "招聘目标达成率(合伙人人员数据)")
    private String recruitmentTargetRatePartner;

    @Excel(name = "本月总入职人数(合伙人人员数据)")
    @ApiModelProperty(value = "本月总入职人数(合伙人人员数据)")
    private Integer thisMonthEmployeesNumPartner;

    @Excel(name = "本月全职入职人数(合伙人人员数据)")
    @ApiModelProperty(value = "本月全职入职人数(合伙人人员数据)")
    private Integer thisMonthFullTimeNumPartner;

    @Excel(name = "本月企业入职人数(合伙人人员数据)")
    @ApiModelProperty(value = "本月企业入职人数(合伙人人员数据)")
    private Integer thisMonthEnterpriseNumPartner;

    @Excel(name = "本月承揽入职人数(合伙人人员数据)")
    @ApiModelProperty(value = "本月承揽入职人数(合伙人人员数据)")
    private Integer thisMonthUndertakeNumPartner;

    @Excel(name = "本月兼职入职人数(合伙人人员数据)")
    @ApiModelProperty(value = "本月兼职入职人数(合伙人人员数据)")
    private Integer thisMonthPartTimeNumPartner;

    @Excel(name = "本月离职合伙人数(合伙人人员数据)")
    @ApiModelProperty(value = "本月离职合伙人数(合伙人人员数据)")
    private Integer thisMonthDepartureNumPartner;

    @Excel(name = "离职率(合伙人人员数据)")
    @ApiModelProperty(value = "离职率(合伙人人员数据)")
    private String departureRatePartner;

    @Excel(name = "招聘费用(合伙人人员数据)")
    @ApiModelProperty(value = "招聘费用(合伙人人员数据)")
    private Integer recruitmentCostPartner;

    /**
     * 合伙人面试流程
     */
  /*  @ApiModelProperty(value = "在岗人数(合伙人面试流程)")
    private String onJobNumInterview;

    @ApiModelProperty(value = "在岗率(合伙人面试流程)")
    private String onJobRateInterview;

    @ApiModelProperty(value = "编制管控在岗率(合伙人面试流程)")
    private String establishmentControlOnJobRateInterview;

    @ApiModelProperty(value = "招聘目标(合伙人面试流程)")
    private String recruitmentTargetInterview;*/

    @Excel(name = "简历数(合伙人面试流程)")
    @ApiModelProperty(value = "简历数(合伙人面试流程)")
    private Integer resumeNumberInterview;

    @Excel(name = "面试人数(合伙人面试流程)")
    @ApiModelProperty(value = "面试人数(合伙人面试流程)")
    private Integer resumeNumInterview;

    @Excel(name = "面试率(合伙人面试流程)")
    @ApiModelProperty(value = "面试率(合伙人面试流程)")
    private String intervieweesRateInterview;

    @Excel(name = "面试时效(合伙人面试流程)")
    @ApiModelProperty(value = "面试时效(合伙人面试流程)")
    private Integer interviewLimitationInterview;

    @Excel(name = "面试通过人数(合伙人面试流程)")
    @ApiModelProperty(value = "面试通过人数(合伙人面试流程)")
    private Integer interviewPassNumInterview;

    @Excel(name = "面试通过率(合伙人面试流程)")
    @ApiModelProperty(value = "面试通过率(合伙人面试流程)")
    private String interviewPassRateInterview;

    @Excel(name = "市场走访中人数(合伙人面试流程)")
    @ApiModelProperty(value = "市场走访中人数(合伙人面试流程)")
    private Integer marketVisitsNumInterview;

    @Excel(name = "平均走访天数(合伙人面试流程)")
    @ApiModelProperty(value = "平均走访天数(合伙人面试流程)")
    private Integer avgVisitDayInterview;

    @Excel(name = "走访成功人数(合伙人面试流程)")
    @ApiModelProperty(value = "走访成功人数(合伙人面试流程)")
    private Integer visitSuccessfulNumInterview;

    @Excel(name = "市场走访成功率(合伙人面试流程)")
    @ApiModelProperty(value = "市场走访成功率(合伙人面试流程)")
    private String visitSuccessfulRateInterview;

    @Excel(name = "试岗中人数(合伙人面试流程)")
    @ApiModelProperty(value = "试岗中人数(合伙人面试流程)")
    private Integer postNumInterview;

    @Excel(name = "试岗不通过人数(合伙人面试流程)")
    @ApiModelProperty(value = "试岗不通过人数(合伙人面试流程)")
    private Integer postNotPassInterview;

    @Excel(name = "试岗通过人数(合伙人面试流程)")
    @ApiModelProperty(value = "试岗通过人数(合伙人面试流程)")
    private Integer postPassNumInterview;

    @Excel(name = "试岗通过率(合伙人面试流程)")
    @ApiModelProperty(value = "试岗通过率(合伙人面试流程)")
    private String postPassRateInterview;

    @Excel(name = "入职人数(合伙人面试流程)")
    @ApiModelProperty(value = "入职人数(合伙人面试流程)")
    private Integer inductionNumInterview;

    @Excel(name = "试岗通过入职率(合伙人面试流程)")
    @ApiModelProperty(value = "试岗通过入职率(合伙人面试流程)")
    private String postPassInductionRate;

    @Excel(name = "面试入职率(合伙人面试流程)")
    @ApiModelProperty(value = "面试入职率(合伙人面试流程)")
    private String interviewInductionRate;

    private List<CompanyRecruitmentVo> list;
}
