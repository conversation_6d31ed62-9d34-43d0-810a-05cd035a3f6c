package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/11/8 9:55
 */
@Data
@ApiModel("新实时库存-当前可出货数量列表 枚举查询请求返回")
public class InventoryDetailGroupEnumsVo {

    @ApiModelProperty("产品组枚举列表")
    private List<InventoryDetailGroupInfoVo> groupInfoList;

    @ApiModelProperty("可出货月份列表")
    private List<String> monthList;
}
