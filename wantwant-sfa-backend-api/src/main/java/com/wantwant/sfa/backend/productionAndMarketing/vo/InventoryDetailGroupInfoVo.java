package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/10/29 14:45
 */
@Data
@ApiModel("实时库存-常态库存/异常库存 明细产品组信息查询返回")
public class InventoryDetailGroupInfoVo {
    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("产品组名称")
    private String businessGroupName;
}
