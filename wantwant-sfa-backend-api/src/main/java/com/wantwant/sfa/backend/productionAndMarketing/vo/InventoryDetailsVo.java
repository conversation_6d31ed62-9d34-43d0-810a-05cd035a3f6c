package com.wantwant.sfa.backend.productionAndMarketing.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "库存与缺货详情")
@Data
public class InventoryDetailsVo {

    @ApiModelProperty(value = "产品图片")
    private String skuImage;

    @ApiModelProperty(value = "仓库名称")
    @Excel(name = "仓库名称")
    private String channelName;

    @ApiModelProperty(value = "销量标签")
    @Excel(name = "销量标签")
    private String saleLabel;

    @ApiModelProperty(value = "仓库Id")
    private String channelId;

    @ApiModelProperty(value = "线别")
    @Excel(name = "线别")
    private String lineName;

    @ApiModelProperty(value = "产品名称")
    @Excel(name = "产品名称")
    private String skuName;

    @ApiModelProperty(value = "sku")
    @Excel(name = "sku")
    private String sku;

    @ApiModelProperty(value = "规格")
    @Excel(name = "规格")
    private String skuSpec;

    @ApiModelProperty(value = "口味")
    @Excel(name = "口味")
    private String flavor;

    @ApiModelProperty(value = "标签")
    @Excel(name = "常态标签")
    private String label;

    @ApiModelProperty(value = "是否新品")
    @Excel(name = "是否新品")
    private String newProductFlag;

    @ApiModelProperty(value = "状态(上架/下架)")
    @Excel(name = "状态(上架/下架)")
    private String sheftFlag;

    @ApiModelProperty(value="WMS上架状态")
    private String wmsShow;

    @ApiModelProperty(value = "动销上架状态")
    @Excel(name = "动销上架状态")
    private String driveSalesIsshow;

    @ApiModelProperty(value = "本月下架天数")
    @Excel(name = "本月下架天数")
    private Integer offSheftDays;

    @ApiModelProperty(value = "业绩占比(当月该仓sku业绩占比)")
    @Excel(name = "业绩占比")
    private String channelSkuPerformanceCover;

    @ApiModelProperty(value = "销售数量")
    @Excel(name = "销售数量")
    private BigDecimal sumQuantity;

    @ApiModelProperty(value = "预估数量")
    @Excel(name = "预估数量")
    private BigDecimal auditQuantity;

    @ApiModelProperty(value = "预估达成率")
    @Excel(name = "预估达成率")
    private String estimatedRate;

    @ApiModelProperty(value = "周转天数")
    @Excel(name = "周转天数")
    private BigDecimal turnoverDays;

    @ApiModelProperty(value = "滚动60天日均合计数量")
    private String thirtyDays;

    @ApiModelProperty(value = "库存")
    @Excel(name = "库存")
    private String inventoryAvailable;

    @ApiModelProperty(value = "常态库存数")
    @Excel(name = "常态库存数")
    private String normalInventory;

    @ApiModelProperty(value = "异常库存数")
    @Excel(name = "异常库存数")
    private String abnormalCover;

    @ApiModelProperty(value = "本月货需在途(本月货需剩余)")
    @Excel(name = "本月货需在途")
    private String surplusDemandThisMonth;

    @ApiModelProperty(value = "次月货需在途(次月货需剩余)")
    @Excel(name = "次月货需在途")
    private String surplusDemandNextMonth;

    @ApiModelProperty(value = "预计到货数量")
    @Excel(name = "预计到货数量")
    private Integer expectNum;

    @ApiModelProperty(value = "预计到货时间")
    @Excel(name = "预计到货时间")
    private String expectDate;

    @ApiModelProperty(value = "当日入库数量")
    @Excel(name = "当日入库数量")
    private Integer stockNumDay;

    @ApiModelProperty(value = "当月累计入库数量")
    @Excel(name = "当月累计入库数量")
    private Integer stockNumMonth;

    @ApiModelProperty(value = "操作状态 1:设置提醒 0:取消提醒")
    private Integer status;



}