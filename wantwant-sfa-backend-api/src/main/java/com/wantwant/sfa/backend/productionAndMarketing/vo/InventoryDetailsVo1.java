package com.wantwant.sfa.backend.productionAndMarketing.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "库存与缺货详情")
@Data
public class InventoryDetailsVo1 {


    @ApiModelProperty(value = "线别")
    @Excel(name = "线别")
    private String lineName;

    @ApiModelProperty(value = "sku")
    @Excel(name = "sku")
    private String sku;

    @ApiModelProperty(value = "产品名称")
    @Excel(name = "产品名称")
    private String skuName;

    @ApiModelProperty(value = "仓库名称")
    @Excel(name = "仓库名称")
    private String channelName;

    @ApiModelProperty(value = "标签")
    @Excel(name = "标签")
    private String label;

    @ApiModelProperty(value = "销量标签")
    @Excel(name = "销量标签")
    private String saleLabel;

    @ApiModelProperty(value = "本月下架天数")
    @Excel(name = "本月下架天数")
    private Integer offSheftDays;

    @ApiModelProperty(value = "业绩占比(当月该仓sku业绩占比)")
    @Excel(name = "业绩占比")
    private String channelSkuPerformanceCover;


    @ApiModelProperty(value = "预计到货数量")
    @Excel(name = "预计到货数量")
    private Integer expectNum;

    @ApiModelProperty(value = "预计到货时间")
    @Excel(name = "预计到货时间")
    private String expectDate;


}