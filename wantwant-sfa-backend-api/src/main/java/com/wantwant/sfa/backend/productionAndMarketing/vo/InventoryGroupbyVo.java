package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "库存分组字段后台")
public class InventoryGroupbyVo {

  @ApiModelProperty(value = "渠道")
  private List<String> channelName;

  @ApiModelProperty(value = "效期标签")
  private List<String> validityLabel;

  @ApiModelProperty(value = "生产线")
  private List<String> lineName;

//  @ApiModelProperty(value = "商品标签")
//  private List<String> skuTag;
  @ApiModelProperty(value = "常态标签")
  private List<String> normalTag;
}
