package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/10/21 13:33
 */
@ApiModel("库存快速查找明细")
@Data
public class InventoryQuickLookDetailVo {

    private String skuId;

    private String channelId;



    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("产品组名称")
    private String businessGroupName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("销售预估-可出货数量(箱数)")
    private BigDecimal canBeShipmentBoxes;

    @ApiModelProperty("销售预估-可出货数量汇总(箱数)")
    private BigDecimal canBeShipmentTotalBoxes;

    @ApiModelProperty("销售预估-实际可出货数量(箱数)")
    private BigDecimal canBeShipmentBoxesActual;

    @ApiModelProperty("异常锁库数量(箱数)")
    private Integer abnormalLockedInventoryBoxes;

}
