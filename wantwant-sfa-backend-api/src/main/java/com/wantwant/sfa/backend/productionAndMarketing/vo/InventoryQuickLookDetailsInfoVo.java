package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/10/23 14:20
 */
@ApiModel("库存快速查找-优质库存 异常库存 月份明细 数据 返回")
@Data
public class InventoryQuickLookDetailsInfoVo {

    @ApiModelProperty("年")
    private String year;

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("年月")
    private String yearMonth;

    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("产品组code")
    private String businessGroupCode;

    @ApiModelProperty("产品组名称")
    private String businessGroupName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("spuId")
    private String spuId;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("仓库id")
    private String channelId;

    @ApiModelProperty("仓别")
    private String channelName;

    @ApiModelProperty("战区组织id")
    private String areaId;

    @ApiModelProperty("战区组织名称")
    private String areaName;

    @ApiModelProperty("大区组织id")
    private String vareaId;

    @ApiModelProperty("大区组织名称")
    private String vareaName;

    @ApiModelProperty("省区组织id")
    private String provinceId;

    @ApiModelProperty("省区组织名称")
    private String provinceName;

    @ApiModelProperty("分公司组织id")
    private String companyId;

    @ApiModelProperty("分公司组织名称")
    private String companyName;

    @ApiModelProperty("销售预估-可出货数量(箱数)")
    private BigDecimal canBeShipmentBoxes;

    @ApiModelProperty("销售预估-实际可出货数量(箱数)")
    private BigDecimal canBeShipmentBoxesActual;

    @ApiModelProperty("异常锁库数量(箱数)")
    private Integer abnormalLockedInventoryBoxes;
}
