package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/10/23 14:15
 */
@ApiModel("库存快速查找-优质库存 异常库存 月份明细 返回")
@Data
public class InventoryQuickLookDetailsVo {

    @ApiModelProperty("全组织名称")
    private String fullOrganizationName;

    @ApiModelProperty("产品组id")
    private Integer businessGroupId;

    @ApiModelProperty("产品组名称")
    private String businessGroupName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("数据信息")
    private List<InventoryQuickLookDetailsInfoVo> detailsInfoList;


}
