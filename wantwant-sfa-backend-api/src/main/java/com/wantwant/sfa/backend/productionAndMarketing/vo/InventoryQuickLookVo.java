package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/10/21 13:31
 */
@Data
public class InventoryQuickLookVo {

    @ApiModelProperty("sku产品图")
    private String skuImages;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("spuId")
    private String spuId;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("规格")
    private String skuSpec;

    @ApiModelProperty("口味")
    private String skuFlavor;

    @ApiModelProperty("线别")
    private String lineName;

    @ApiModelProperty("常态标签")
    private String tagName;

    @ApiModelProperty("状态")
    private String statusName;

    @ApiModelProperty("是否管控")
    private String skuIsControlled;

    @ApiModelProperty("仓别")
    private String channelName;

    @ApiModelProperty("仓别")
    private String channelId;

    @ApiModelProperty("明细数据")
    private List<InventoryQuickLookDetailVo> queryDetailList;

}
