package com.wantwant.sfa.backend.productionAndMarketing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "库存返回")
public class InventoryVo {

  @ApiModelProperty(value = "产品编码")
  private String sku;

  @ApiModelProperty(value = "渠道")
  private String channelName;

  @ApiModelProperty(value = "产品名称")
  private String skuName;

  @ApiModelProperty(value = "口味")
  private String flavor;

  @ApiModelProperty(value = "规格")
  private String skuSpec;

  @ApiModelProperty(value = "生产线")
  private String lineName;

  @ApiModelProperty(value = "箱规")
  private Integer fullCaseNum;

  @ApiModelProperty(value = "批次编码")
  private String batchMgmtNumber;

  @ApiModelProperty(value = "生产日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private LocalDateTime productionDate;

  @ApiModelProperty(value = "生产月份")
  private String roductionMonth;

  @ApiModelProperty(value = "失效日期")
  @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private LocalDateTime expireDate;

  @ApiModelProperty(value = "保质期（天）")
  private Integer shelfLifeDays;

  @ApiModelProperty(value = "剩余有效期")
  private Integer remainingValidity;

  @ApiModelProperty(value = "生产至今天数")
  private Integer daysSinceProduction;

  @ApiModelProperty(value = "效期标签")
  private String validityLabel;

  @ApiModelProperty(value = "是否常态售卖")
  private String isNormalSale;

  @ApiModelProperty(value = "满赠活动剩余库存箱数/预订单占用数量")
  private Integer inventoryOccupyBoxes;

  @ApiModelProperty(value = "批次可用库存件数")
  private Integer inventoryAvailable;

  @ApiModelProperty(value = "库存箱数")
  private Integer inventoryBoxes;

  @ApiModelProperty(value = "商品标签")
  private String skuTag;

  @ApiModelProperty(value = "商品合伙人价格")
  private String partnerPrice;

  @ApiModelProperty(value = "商品货值")
  private String commodityPriceTotal;

  @ApiModelProperty(value = "常态标签")
  private String normalTag;

  @ApiModelProperty(value = "是否经典品相")
  private String classicItem;

  @ApiModelProperty(value = "产品名称")
  private String skuSpecification;

  @ApiModelProperty(value = "业务组名称")
  private String businessGroupName;

  @ApiModelProperty(value = "spu名称")
  private String spuName;

  @ApiModelProperty(value = "转换阶段")
  private String transferStage;
}
