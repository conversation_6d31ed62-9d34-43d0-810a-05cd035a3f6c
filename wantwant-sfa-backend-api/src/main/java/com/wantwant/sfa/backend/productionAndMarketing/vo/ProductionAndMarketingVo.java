package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "产销返回参数")
public class ProductionAndMarketingVo {

  @ApiModelProperty(value = "产品编码")
  private String sku;

  @ApiModelProperty(value = "标签")
  private String label;

  @ApiModelProperty(value = "仓库")
  private String channelName;

  @ApiModelProperty(value = "产品名称")
  private String skuName;

  @ApiModelProperty(value = "生产线")
  private String lineName;

  @ApiModelProperty(value = "上架状态")
  private String isShow;

  @ApiModelProperty(value = "本月上架天数")
  private String isshowDay;

  @ApiModelProperty(value = "总库存")
  private BigDecimal inventoryNum;

  @ApiModelProperty(value = "优质库存箱数")
  private BigDecimal highQualityInventory;

  @ApiModelProperty(value = "优质库存箱数占比")
  private BigDecimal highQualityCover;

  @ApiModelProperty(value = "预警库存箱数")
  private BigDecimal waringInventory;

  @ApiModelProperty(value = "预警库存箱数占比")
  private BigDecimal waringCover;

  @ApiModelProperty(value = "超期库存箱数")
  private BigDecimal overdueInventory;

  @ApiModelProperty(value = "超期库存箱数占比")
  private BigDecimal overdueCover;

  @ApiModelProperty(value = "常态库存数")
  private BigDecimal normalInventory;

  @ApiModelProperty(value = "常态可用库存占比")
  private BigDecimal normalCover;

  @ApiModelProperty(value = "异常库存数")
  private BigDecimal abnormalInventory;

  @ApiModelProperty(value = "异常可用库存占比")
  private BigDecimal abnormalCover;

  @ApiModelProperty(value = "次月异常库存箱数")
  private BigDecimal nextAbnormalInventory;

  @ApiModelProperty(value = "次月超期库存箱数")
  private BigDecimal overdueNextmonthInventory;

  @ApiModelProperty(value = "当月常态销售箱数")
  private BigDecimal normalSalesBoxnum;

  @ApiModelProperty(value = "当月活动销售箱数")
  private BigDecimal activitySalesBoxnum;

  @ApiModelProperty(value = "当月赠品箱数")
  private BigDecimal giftBoxnum;

  @ApiModelProperty(value = "当月日均销售箱数")
  private BigDecimal saleBoxnumAvg;

  @ApiModelProperty(value = "滚动30天日均销售箱数")
  private BigDecimal saleBoxnumThirtydayAvg;

  @ApiModelProperty(value = "滚动7天日均销售箱数")
  private BigDecimal saleBoxnumSevendayAvg;

  @ApiModelProperty(value = "常态可售库存周转天数")
  private BigDecimal normalsaleTurnoverDays;

  @ApiModelProperty(value = "优质周转天数")
  private BigDecimal highQualityTurnoverDays;

  @ApiModelProperty(value = "在库+在途周转天数")
  private BigDecimal warehouseAndWayTurnoverDays;

  /*@ApiModelProperty(value = "销售预估")
  private List<SalesForecastCollectionVo> salesForecastCollectionList;*/

  @ApiModelProperty(value = "当月日期")
  private String thisDate;

  @ApiModelProperty(value = "当月1期提报")
  private BigDecimal firstReport;

  @ApiModelProperty(value = "当月3期提报")
  private BigDecimal thirdReport;

  @ApiModelProperty(value = "当月3期调整量")
  private BigDecimal thirdAdjust;

  @ApiModelProperty(value = "当月4期调整量")
  private BigDecimal fourthAdjust;

  @ApiModelProperty(value = "当月1期开单")
  private BigDecimal firstBilling;

  @ApiModelProperty(value = "当月2期开单")
  private BigDecimal secondBilling;

  @ApiModelProperty(value = "当月3期开单")
  private BigDecimal thirdBilling;

  @ApiModelProperty(value = "当月4期开单")
  private BigDecimal fourBilling;

  @ApiModelProperty(value = "当月开单总量")
  private BigDecimal billingTotal;

  @ApiModelProperty(value = "当月开单差异量")
  private BigDecimal billingDifference;

  @ApiModelProperty(value = "当月无法供货")
  private BigDecimal unableSupply;

  @ApiModelProperty(value = "当月实际供货")
  private BigDecimal actualSupply;

  @ApiModelProperty(value = "当月已入库")
  private BigDecimal warehoused;

  @ApiModelProperty(value = "当月借货调拨")
  private BigDecimal allot;

  @ApiModelProperty(value = "当月调拨在途量截止至今")
  private BigDecimal allotTransfer;

  @ApiModelProperty(value = "当月货需剩余")
  private BigDecimal surplusDemand;

  @ApiModelProperty(value = "次月调拨在途量截止至今")
  private BigDecimal allotTransferNextMonth;

  @ApiModelProperty(value = "到货数量")
  private BigDecimal expectNum;

  @ApiModelProperty(value = "当月调拔已入库")
  private BigDecimal allotInStorage;

  @ApiModelProperty(value = "到货日期")
  //@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
  private String expectDate;

  @ApiModelProperty(value = "规格")
  private String skuSpec;

  @ApiModelProperty(value = "口味")
  private String flavor;

  @ApiModelProperty(value = "滚动60天日均销售箱数(常态)")
  private BigDecimal saleBoxnumSixtydayNormalAvg;

  @ApiModelProperty(value = "滚动60天日均销售箱数(常态+活动)")
  private BigDecimal saleBoxnumSixtydayNormalactivityAvg;

  @ApiModelProperty(value = "动销上架状态 1-上架 0-下架 10-缺货中")
  private String driveSalesIsshow;

  @ApiModelProperty(value = "1期货需剩余")
  private BigDecimal firstSurplusDemand;

  @ApiModelProperty(value = "2期货需剩余")
  private BigDecimal secondSurplusDemand;

  @ApiModelProperty(value = "3期货需剩余")
  private BigDecimal thirdSurplusDemand;

  @ApiModelProperty(value = "4期货需剩余")
  private BigDecimal fourSurplusDemand;
}
