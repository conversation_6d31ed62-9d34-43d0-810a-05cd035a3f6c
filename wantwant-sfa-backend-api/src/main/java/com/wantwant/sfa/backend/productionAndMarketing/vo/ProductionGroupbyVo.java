package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "产销分组字段后台")
public class ProductionGroupbyVo {

  @ApiModelProperty(value = "仓库")
  private List<String> channelName;

  @ApiModelProperty(value = "标签")
  private List<String> label;

  @ApiModelProperty(value = "生产线")
  private List<String> lineName;

  @ApiModelProperty(value = "上架状态")
  private List<String> isShow;

  @ApiModelProperty(value = "上架状态")
  private List<Map<String,Object>> isDiverShow;
}
