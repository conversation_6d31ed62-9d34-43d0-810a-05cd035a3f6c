package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2025/1/3 11:26
 */
@Data
public class PublicityEnumsDto {
    @ApiModelProperty(value = "sku信息")
    private List<PublicityProductInfoEnums> skuInfos;
    @ApiModelProperty("标签信息")
    private List<String> tagNames;

}
