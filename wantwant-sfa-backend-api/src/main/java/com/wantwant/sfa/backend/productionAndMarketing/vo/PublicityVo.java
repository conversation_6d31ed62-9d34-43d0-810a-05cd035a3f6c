package com.wantwant.sfa.backend.productionAndMarketing.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "文宣品Vo")
public class PublicityVo {

    @ExcelProperty("产品图片")
    @ApiModelProperty(value = "产品图片")
    private String skuImage;

    @ExcelProperty("仓库")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ExcelProperty("线别")
    @ApiModelProperty(value = "线别")
    private String lineName;

    @ExcelProperty("sku")
    @ApiModelProperty(value = "sku")
    private String sku;

    @ExcelProperty("产品名称")
    @ApiModelProperty(value = "产品名称")
    private String skuName;

    @ExcelProperty("sku标签")
    @ApiModelProperty(value = "sku标签")
    private String tagName;

    @ExcelProperty("平台上架状态")
    @ApiModelProperty("平台上架状态")
    private String showFlag;

    @ExcelProperty("当月平台上架率(%)")
    @ApiModelProperty("平台上架率")
    private BigDecimal showRate;

    @ExcelProperty("库存安全阈值")
    @ApiModelProperty(value = "库存安全阈值")
    private BigDecimal saleNum90;

    @ExcelProperty("预警状态")
    @ApiModelProperty("预警状态")
    private String warningStatus;

    @ExcelProperty("平台总库存")
    @ApiModelProperty("平台总库存")
    private BigDecimal inventoryTotal;

    @ExcelProperty("可用库存(件)")
    @ApiModelProperty("可用库存")
    private BigDecimal inventoryAvailable;

    @ExcelProperty("库龄（产品入仓的第一天）")
    @ApiModelProperty("库龄（产品入仓的第一天）")
    private String warehouseAge;

    @ExcelProperty("当月销量（件）")
    @ApiModelProperty(value = "当月销量（件）")
    private  BigDecimal saleNumMonth;

    @ExcelProperty("近三个月月均消耗数量（件）试吃商城")
    @ApiModelProperty("近90天月均消耗数量（件）试吃商城")
    private BigDecimal saleNum90Sample;

    @ExcelProperty("近三个月月均消耗数量（件）手工导单")
    @ApiModelProperty("近90天月均消耗数量（件）手工导单")
    private BigDecimal saleNum90ManualGuide;

    @ExcelProperty("近30天销量（件）")
    @ApiModelProperty("近30天销量（件）")
    private BigDecimal saleNum30;

    @ExcelProperty("可售月数")
    @ApiModelProperty("平台可用库存可售月数")
    private BigDecimal inventoryAvailableTurnOverMonths;

    @ExcelProperty("可售月数（试吃商城）")
    @ApiModelProperty("可售月数（试吃商城）")
    private BigDecimal saleMonthSample;

    @ExcelProperty("可售月数（手工导单）")
    @ApiModelProperty("可售月数（手工导单）")
    private BigDecimal saleMonthManualGuide;

    @ExcelProperty("预计到货数量")
    @ApiModelProperty("预计到货数量")
    private BigDecimal expectNum;

    @ExcelProperty("预计到货时间")
    @ApiModelProperty("预计到货时间")
    private String expectDate;

    @ExcelProperty("商品货值")
    @ApiModelProperty("商品货值")
    private BigDecimal goodsValue;
}
