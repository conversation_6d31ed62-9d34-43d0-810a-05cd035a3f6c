package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.realData.vo
 * @Description:
 * @Date: 2024/9/20 19:12
 */
@ApiModel("实时库存枚举列表")
@Data
public class RealTimeInventoryEnums {

    @ApiModelProperty("产品名称列表")
    private List<RealTimeInventorySkuInfoEnumsVo> skuInfos;

    @ApiModelProperty("产品线列表")
    private List<String> lineNames;

    @ApiModelProperty("常态标签列表")
    private List<String> tagNames;

    @ApiModelProperty("状态列表")
    private List<String> statusNames;

    @ApiModelProperty("仓别列表")
    private List<String> channelNames;

}
