package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.productionAndMarketing.vo
 * @Description:
 * @Date: 2024/9/23 9:07
 */
@ApiModel("新实时库存-异常锁库返回参数")
@Data
public class RealTimeInventoryExceptionLockLibraryVo {

    @ApiModelProperty("生产月份")
    private String theYearMonth;

    @ApiModelProperty("skuId")
    private String skuId;

    @ApiModelProperty("spu")
    private String spuId;

    @ApiModelProperty("分公司id")
    private String companyId;

    @ApiModelProperty("分公司名称")
    private String companyName;

    @ApiModelProperty("最终可销数量(待处理数量) 箱数")
    private Integer finalSaleQuantityBox;

    @ApiModelProperty("保质期")
    private String sellByDate;

    @ApiModelProperty("异常开始日期")
    private String inventoryStartDay;

    @ApiModelProperty("处理截止日期")
    private String handleStopDay;
}
