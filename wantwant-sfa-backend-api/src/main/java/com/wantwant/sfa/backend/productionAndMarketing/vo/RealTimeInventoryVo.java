package com.wantwant.sfa.backend.productionAndMarketing.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.realData.vo
 * @Description:
 * @Date: 2024/9/20 15:03
 */
@ApiModel("新实时库存返回参数")
@Data
public class RealTimeInventoryVo {

    @ApiModelProperty("sku产品图")
    @ExcelProperty(value = "产品图片")
    private String skuImages;

    @ApiModelProperty("skuId")
    @ExcelProperty(value = "SKU")
    private String skuId;

    @ApiModelProperty("产品名称")
    @ExcelProperty(value = "产品名称")
    private String skuName;

    @ApiModelProperty("规格")
    @ExcelProperty(value = "规格")
    private String skuSpec;

    @ApiModelProperty("口味")
    @ExcelProperty(value = "口味")
    private String skuFlavor;

    @ApiModelProperty("线别")
    @ExcelProperty(value = "线别")
    private String lineName;

    @ApiModelProperty("常态标签")
    @ExcelProperty(value = "常态标签")
    private String tagName;

    @ApiModelProperty("状态")
    @ExcelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty("是否管控")
    @ExcelProperty(value = "是否为管控品")
    private String skuIsControlled;

    @ApiModelProperty("仓库id")
    @ExcelIgnore
    private String channelId;

    @ApiModelProperty("仓别")
    @ExcelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty("常态库存-m-2批次箱数")
    @ExcelProperty(value = {"库存","常态库存","${last2Month}"})
    private BigDecimal normalInventoryBoxesLast2Month;

    @ApiModelProperty("常态库存-m-1批次箱数")
    @ExcelProperty(value = {"库存","常态库存","${last1Month}"})
    private BigDecimal normalInventoryBoxesLast1Month;

    @ApiModelProperty("常态库存-当月批次箱数")
    @ExcelProperty(value = {"库存","常态库存","${currentMonth}"})
    private BigDecimal normalInventoryBoxesCurrentMonth;


    @ApiModelProperty("常态库存-总箱数")
    @ExcelProperty(value = {"库存","常态库存","合计"})
    private BigDecimal normalInventoryBoxesTotal;

    @ApiModelProperty("异常库存-超期")
    @ExcelProperty(value = {"库存","异常库存","超期"})
    private BigDecimal abnormalInventoryBoxesOverdue;

    @ApiModelProperty("异常库存-待报废")
    @ExcelProperty(value = {"库存","异常库存","待报废"})
    private BigDecimal abnormalInventoryBoxesWillScrap;

    @ApiModelProperty("异常库存-合计")
    @ExcelProperty(value = {"库存","异常库存","合计"})
    private BigDecimal abnormalInventoryBoxesWillTotal;

    @ApiModelProperty("锁定库存")
    @ExcelProperty(value = {"库存","锁定库存"})
    private BigDecimal lockedInventoryBoxes;

    @ApiModelProperty("占用库存")
    @ExcelProperty(value = {"库存","占用库存"})
    private BigDecimal occupiedInventoryBoxes;

    @ApiModelProperty("常态可用库存")
    @ExcelProperty(value = {"库存","常态可用库存"})
    private BigDecimal normalInventoryBoxesAvailable;

    @ApiModelProperty("60天日均销量")
    @ExcelProperty(value = {"库存","60天日均销量"})
    private BigDecimal avg60SaleBoxQuantity;

    @ApiModelProperty("周转天数")
    @ExcelProperty(value = {"库存","周转天数"})
    private BigDecimal turnaroundDays;

    @ApiModelProperty("到货计划-实际货需箱数")
    @ExcelProperty(value = {"到货计划","实际货需量"})
    private BigDecimal actualDemandBoxes;

    @ApiModelProperty("到货计划-当月累计已入库数量(箱数)")
    @ExcelProperty(value = {"到货计划","当月累计已入库数量"})
    private BigDecimal accumulatedWarehousedBoxesCurrentMonth;

    @ApiModelProperty("到货计划-在途未入库数量(箱数)")
    @ExcelProperty(value = {"到货计划","在途未入库数量"})
    private BigDecimal onWayNotStockingBoxes;

    @ApiModelProperty("到货计划-货需满足率")
    @ExcelProperty(value = {"到货计划","货需满足率"})
    private BigDecimal demandSatisfactionRatio;

    @ApiModelProperty("销售预估-预估数量(箱数)")
    @ExcelProperty(value = {"销售预估","预估数量"})
    private BigDecimal estimateBoxes;

    @ApiModelProperty("销售预估-分配数量(箱数调整后)")
    @ExcelProperty(value = {"销售预估","分配数量(调整后)"})
    private BigDecimal allocatedBoxesAfterAdjustmentBoxes;

    @ApiModelProperty("销售预估-预估满足率")
    @ExcelProperty(value = {"销售预估","预估满足率"})
    private BigDecimal estimateSatisfactionRatio;

    @ApiModelProperty("销售预估-实际出货数量(箱数)")
    @ExcelProperty(value = {"销售预估","实际出货数量"})
    private BigDecimal actualShipmentBoxes;

    @ApiModelProperty("销售预估-预估达成率")
    @ExcelProperty(value = {"销售预估","预估达成率"})
    private BigDecimal estimateAchievementRatio;

    @ApiModelProperty("销售预估-可出货数量(箱数)")
    @ExcelProperty(value = {"销售预估","可出货数量"})
    private BigDecimal canBeShipmentBoxes;

    @ApiModelProperty("销售预估-实际可出货数量(箱数)")
    @ExcelProperty(value = {"销售预估","实际可出货数量"})
    private BigDecimal canBeShipmentBoxesActual;

    @ApiModelProperty("销售预估-分配可出货数量(箱数)")
    @ExcelProperty(value = {"销售预估","分配可出货数量"})
    private BigDecimal canBeShipmentTotalBoxes;

    @ApiModelProperty("异常锁库数量(箱数)")
    @ExcelProperty(value = "异常锁库")
    private BigDecimal abnormalLockedInventoryBoxes;

    @ApiModelProperty("常态库存月份数据列表")
    @ExcelIgnore
    private List<Map<String,Object>> normalInventoryBoxesMonthData;

    @ApiModelProperty(value = "操作状态 1:设置提醒 0:取消提醒")
    @ExcelIgnore
    private Integer status;

}
