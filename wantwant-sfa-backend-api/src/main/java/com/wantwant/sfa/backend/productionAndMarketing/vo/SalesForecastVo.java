package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "销售预估返回参数")
public class SalesForecastVo {
  @ApiModelProperty(value = "1期提报")
  private BigDecimal firstReport;

  @ApiModelProperty(value = "3期提报")
  private BigDecimal thirdReport;

  @ApiModelProperty(value = "3期调整量")
  private BigDecimal thirdAdjust;

  @ApiModelProperty(value = "4期调整量")
  private BigDecimal fourthAdjust;

  @ApiModelProperty(value = "1期开单")
  private BigDecimal firstBilling;

  @ApiModelProperty(value = "2期开单")
  private BigDecimal secondBilling;

  @ApiModelProperty(value = "3期开单")
  private BigDecimal thirdBilling;

  @ApiModelProperty(value = "4期开单")
  private BigDecimal fourBilling;

  @ApiModelProperty(value = "开单总量")
  private BigDecimal billingTotal;

  @ApiModelProperty(value = "开单差异量")
  private BigDecimal billingDifference;

  @ApiModelProperty(value = "无法供货")
  private BigDecimal unableSupply;

  @ApiModelProperty(value = "实际供货")
  private BigDecimal actualSupply;

  @ApiModelProperty(value = "已入库")
  private BigDecimal warehoused;

  @ApiModelProperty(value = "借货调拨")
  private BigDecimal allot;

  @ApiModelProperty(value = "调拨在途量截止至今")
  private BigDecimal allotTransfer;

  @ApiModelProperty(value = "货需剩余")
  private BigDecimal surplusDemand;
}
