package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "上下架监控表返回标签明细")
public class ShelvesMonitoringVo {

    @ApiModelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty(value = "标签")
    private String nationLabel;

    @ApiModelProperty(value = "在售sku")
    private BigDecimal onSaleSKU;

    @ApiModelProperty(value = "在库sku")
    private BigDecimal inIibrarySKU;

    @ApiModelProperty(value = "当日上架率")
    private BigDecimal onshowRateDay;

    @ApiModelProperty(value = "当月累计上架率")
    private BigDecimal onshowRateMonth;

    @ApiModelProperty(value = "当月环比")
    private BigDecimal growthRate;

    @ApiModelProperty(value = "当日上架sku数")
    private BigDecimal onshowskuNumday;

    @ApiModelProperty(value = "当日上架总数")
    private BigDecimal totalskuNumday;

    @ApiModelProperty(value = "当月上架sku数")
    private BigDecimal thisOnshowskuNumday;

    @ApiModelProperty(value = "当月上架sku数")
    private BigDecimal thisTotalskuNumday;

    @ApiModelProperty(value = "上月上架sku数")
    private BigDecimal lastOnshowskuNumday;

    @ApiModelProperty(value = "上月上架sku数")
    private BigDecimal lastTotalskuNumday;
    
}
