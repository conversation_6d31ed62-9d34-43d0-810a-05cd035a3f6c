package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "上架率明细返回传参")
public class ShelvesRateDetailVo {

    @ApiModelProperty(value = "物料编码")
    private String sku;

    @ApiModelProperty(value = "销售标签")
    private String salesNationLabel;

    @ApiModelProperty(value = "常态标签")
    private String nationLabel;

    @ApiModelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty(value = "物料名称")
    private String skuName;

    @ApiModelProperty(value = "线别")
    private String lineName;

    @ApiModelProperty(value = "首次入库时间")
    private String firstInwarehouse;

    @ApiModelProperty(value = "当月下架天数")
    private String offshowDayMonth;

    @ApiModelProperty(value = "当月累计上架率")
    private String onshowRateTotal;

    @ApiModelProperty(value = "环比增长率")
    private String growthRate;

    @ApiModelProperty(value = "日")
    private String day;

    @ApiModelProperty(value = "是否上架")
    private String isShow;

    @ApiModelProperty(value = "每日上下架返回参数")
    private List<ShelvesDateVo> list;

}
