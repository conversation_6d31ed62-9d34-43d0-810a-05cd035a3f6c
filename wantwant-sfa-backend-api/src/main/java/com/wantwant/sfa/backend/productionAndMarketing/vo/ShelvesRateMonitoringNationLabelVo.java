package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "上下架监控表返回标签")
public class ShelvesRateMonitoringNationLabelVo {

    @ApiModelProperty(value = "标签的原有值")
    private String nationLabelEnum;

    @ApiModelProperty(value = "标签的处理（要展示出来）")
    private String nationLabel;

    @ApiModelProperty(value = "标签明细")
    private ShelvesRateMonitoringDetailVo labalDetail;
}
