package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "上下架监控表返回传参")
public class ShelvesRateMonitoringVo {

    @ApiModelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty(value = "仓别明细")
    private List<ShelvesRateMonitoringNationLabelVo> list;
}
