package com.wantwant.sfa.backend.productionAndMarketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "承运商服务返回参数")
@Data
public class StorageServiceAchievementVo {

    @ApiModelProperty(value = "仓别")
    private String channelName;

    @ApiModelProperty(value = "省份")
    private String receiverProvince;

    @ApiModelProperty(value = "合单量")
    private BigDecimal parentIdNum;

    @ApiModelProperty(value = "延误订单")
    private BigDecimal putoffNum;

    @ApiModelProperty(value = "理论送货及时率%（标准＞95%）")
    private BigDecimal theoreticalTimelyRate;

    @ApiModelProperty(value = "平均时效/天")
    private BigDecimal otorAvgDay;


    @ApiModelProperty(value = "理论送货完美率%（标准＞95%）")
    private BigDecimal theoreticalUndeliveryPerfectRate;

    @ApiModelProperty(value = "运费")
    private BigDecimal realDeliveryfee;

    @ApiModelProperty(value = "理论运费率%（标准＜8.5%）")
    private BigDecimal theoreticalDeliveryRate;

    @ApiModelProperty("货损货差箱数(货损货差率分子)")
    private Integer goodsLossDifferenceBox;

    @ApiModelProperty("发货箱数(货损货差率分母)")
    private BigDecimal deliverBox;

    @ApiModelProperty("货损货差率")
    private BigDecimal goodsLossDifferenceRate;

    @ApiModelProperty("客户不满意订单数（合单）")
    private BigDecimal customerDissatisfiedOrdersNum;

    @ApiModelProperty("公路运输回单上传及时率（标准≥95%）")
    private BigDecimal roadTransportReceiptTimelyRate;
}
