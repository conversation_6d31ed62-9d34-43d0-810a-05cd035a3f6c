package com.wantwant.sfa.backend.receiptAndDelivery.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.receiptAndDelivery.request.QueryDTO;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.DeliveryTimeVO;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.OrderDeliveryVO;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.ReceiptTimeResponse;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.TransportTimeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


@Api(value = "ReceiptAndDeliveryApi",tags = "收发货时效")
public interface ReceiptAndDeliveryApi {

    @ApiIgnore
    @GetMapping("/clearCache")
    @Deprecated
    void clearCache(@RequestParam(value = "key") String key);

    @ApiOperation(value = "仓库发货时效" ,notes = "仓库发货时效" ,httpMethod = "GET")
    @GetMapping("/delivery/getDeliveryTime")
    @Deprecated
    Response<List<DeliveryTimeVO>> getDeliveryTime();

    @ApiOperation(value = "客户收货时效" ,notes = "客户收获时效" ,httpMethod = "GET")
    @GetMapping("/receipt/getReceiptTime")
    @Deprecated
    Response<ReceiptTimeResponse> getReceiptTime();

    @ApiOperation(value = "作业时效" ,notes = "作业时效" ,httpMethod = "GET")
    @GetMapping("/delivery/getWorkTime")
    @Deprecated
    Response<List<DeliveryTimeVO>> getWorkTime();




    @ApiOperation(value = "订单出库", notes = "订单出库", httpMethod = "GET")
    @GetMapping("/delivery/getOrderDelivery")
    Response<List<OrderDeliveryVO>> orderDeliveryList(QueryDTO queryDTO);

    @ApiOperation(value = "仓库发货", notes = "仓库发货", httpMethod = "GET")
    @GetMapping("/delivery/getWarehouseDelivery")
    Response<List<OrderDeliveryVO>> warehouseDeliveryList(QueryDTO queryDTO);

    @ApiOperation(value = "运输时效", notes = "运输时效", httpMethod = "GET")
    @GetMapping("/delivery/getTransportTime")
    Response<List<TransportTimeVO>> transportTimeList(QueryDTO queryDTO);

    @ApiOperation(value = "订单交付", notes = "订单交付", httpMethod = "GET")
    @GetMapping("/delivery/getOrderConsign")
    Response<List<TransportTimeVO>> orderReceiptList(QueryDTO queryDTO);

}
