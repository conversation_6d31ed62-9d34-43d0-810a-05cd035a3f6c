package com.wantwant.sfa.backend.receiptAndDelivery.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 收发货查询dto
 *
 * <AUTHOR>
 * @date 2021-07-19 10:05
 * @version 1.0
 */
@Data
public class QueryDTO implements Serializable {

    private static final long serialVersionUID = 4575951629728125124L;


    @ApiModelProperty(value = "开始日期(yyyy-MM-dd)")
    private String startDate;

    @ApiModelProperty(value = "结束日期(yyyy-MM-dd)")
    private String endDate;

    @ApiModelProperty(value = "发货仓库(1:长沙,2:济南)")
    private Integer channel;
}
