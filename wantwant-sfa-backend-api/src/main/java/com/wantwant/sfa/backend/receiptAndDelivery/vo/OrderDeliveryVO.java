package com.wantwant.sfa.backend.receiptAndDelivery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单出库
 *
 * <AUTHOR>
 * @date 2021-07-19 10:29
 * @version 1.0
 */
@Data
@ApiModel("订单出库")
public class OrderDeliveryVO implements Serializable {

    private static final long serialVersionUID = -910855184247394823L;

    @ApiModelProperty(value ="组织名称")
    private String organizationName;

    @ApiModelProperty(value ="<=24H")
    private int orderQuantity24;

    @ApiModelProperty(value ="<=24H占比")
    private BigDecimal orderQuantity24Rate = BigDecimal.ZERO;

    @ApiModelProperty(value ="<=48H")
    private int orderQuantity48;

    @ApiModelProperty(value ="<=48H占比")
    private BigDecimal orderQuantity48Rate = BigDecimal.ZERO;

    @ApiModelProperty(value =">48H")
    private int orderQuantity72;

    @ApiModelProperty(value =">48占比")
    private BigDecimal orderQuantity72Rate = BigDecimal.ZERO;

    @ApiModelProperty(value ="总计")
    private int total;

}
