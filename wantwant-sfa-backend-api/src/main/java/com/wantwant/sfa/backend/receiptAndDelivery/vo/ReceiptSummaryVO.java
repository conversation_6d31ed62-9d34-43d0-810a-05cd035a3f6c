package com.wantwant.sfa.backend.receiptAndDelivery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户收货汇总
 *
 * <AUTHOR>
 * @date 2021-06-02 14:09
 * @version 1.0
 */
@Data
@ApiModel("客户收货汇总")
public class ReceiptSummaryVO implements Serializable {

    private static final long serialVersionUID = -6347485404647260970L;

    @ApiModelProperty(value ="组织名称")
    private String organizationName;

    @ApiModelProperty(value ="准时送货订单数")
    private int punctualOrderQuantity;

    @ApiModelProperty(value ="延误送货订单数")
    private int delayOrderQuantity;

    @ApiModelProperty(value ="准时送货率")
    private BigDecimal punctualOrderRate = BigDecimal.ZERO;

    @ApiModelProperty(value ="送货完美率")
    private BigDecimal perfectOrderRate = BigDecimal.ZERO;

}
