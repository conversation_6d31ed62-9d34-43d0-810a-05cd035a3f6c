package com.wantwant.sfa.backend.receiptAndDelivery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户收获时效返回
 *
 * <AUTHOR>
 * @date 2021-06-04 23:43
 * @version 1.0
 */
@Data
@ApiModel("客户收获时效返回")
public class ReceiptTimeResponse {

    @ApiModelProperty(value ="客户收货时效")
    List<ReceiptTimeVO> receiptTimes;

    @ApiModelProperty(value = "客户收货汇总")
    List<ReceiptSummaryVO> ReceiptSummaries;
}
