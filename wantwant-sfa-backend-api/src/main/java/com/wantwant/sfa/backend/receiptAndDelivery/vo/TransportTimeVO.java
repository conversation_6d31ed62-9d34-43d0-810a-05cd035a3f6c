package com.wantwant.sfa.backend.receiptAndDelivery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 时效
 *
 * <AUTHOR>
 * @date 2021-06-02 14:09
 * @version 1.0
 */
@Data
@ApiModel("时效")
public class TransportTimeVO implements Serializable {

    private static final long serialVersionUID = -8980402398956430652L;

    @ApiModelProperty(value ="组织名称")
    private String organizationName;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value ="1天")
    private int orderQuantity1;

    @ApiModelProperty(value ="1天占比")
    private BigDecimal orderQuantity1Rate = BigDecimal.ZERO;

    @ApiModelProperty(value ="2天")
    private int orderQuantity2;

    @ApiModelProperty(value ="2天占比")
    private BigDecimal orderQuantity2Rate = BigDecimal.ZERO;

    @ApiModelProperty(value ="3天")
    private int orderQuantity3;

    @ApiModelProperty(value ="3天占比")
    private BigDecimal orderQuantity3Rate = BigDecimal.ZERO;

    @ApiModelProperty(value ="4天")
    private int orderQuantity4;

    @ApiModelProperty(value ="4天占比")
    private BigDecimal orderQuantity4Rate = BigDecimal.ZERO;

    @ApiModelProperty(value =">4天")
    private int orderQuantity5;

    @ApiModelProperty(value =">4天占比")
    private BigDecimal orderQuantity5Rate = BigDecimal.ZERO;

    @ApiModelProperty(value ="总计")
    private int total;

}
