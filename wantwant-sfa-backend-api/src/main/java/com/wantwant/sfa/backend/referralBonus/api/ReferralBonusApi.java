package com.wantwant.sfa.backend.referralBonus.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.referralBonus.request.*;
import com.wantwant.sfa.backend.referralBonus.vo.ButtonVo;
import com.wantwant.sfa.backend.referralBonus.vo.ReferralBonusDailyVo;
import com.wantwant.sfa.backend.referralBonus.vo.ReplacementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Api(value = "ReferralBonusApi",tags = "推荐奖金Api")
public interface ReferralBonusApi {

    @ApiOperation(value = "获取资金报表", notes = "获取资金报表", httpMethod = "POST")
    @PostMapping("/ReferralBonusDaily/list")
    Response<Page<ReferralBonusDailyVo>> getReferralBonusDailyList(
            @RequestBody ReferralBonusDailyRequest request);

    @ApiOperation(value = "获取资金方案", notes = "获取资金方案", httpMethod = "POST")
    @PostMapping("/seleferralBonusRewardScheme")
    Response<List<String>> seleferralBonusRewardScheme();

    @ApiOperation(value = "处理发放结果", notes = "获取发放结果", httpMethod = "POST")
    @PostMapping("/ReferralIssueResult/update")
    Response referralIssueResultUpdate(
            @RequestBody ReferralBonusResultRequest request);

    @ApiOperation(value = "获取汰换记录列表", notes = "获取汰换记录列表", httpMethod = "POST")
    @PostMapping("/Replacement/list")
    Response<Page<ReplacementVo>> getReplacementList(
            @RequestBody ReplacementRequest request);

    @ApiOperation(value = "获取扣罚记录列表", notes = "获取扣罚记录列表", httpMethod = "POST")
    @PostMapping("/Button/list")
    Response<Page<ButtonVo>> getButtonList(
            @Validated  @RequestBody ButtonRequest request);

    @ApiOperation(value = "获取扣罚项目列表", notes = "获取扣罚项目列表", httpMethod = "POST")
    @PostMapping("/ButtonProject/list")
    Response<List<String>> getButtonProjectList();


}
