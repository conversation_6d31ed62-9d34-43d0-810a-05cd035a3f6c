package com.wantwant.sfa.backend.referralBonus.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BonuslssueListReq {

    @ApiModelProperty(value = "编号")
    private String grantId;

    @ApiModelProperty(value = "同意发放时间")
    //@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private String examineTime;

    @ApiModelProperty(value = "达标日期")
    private String standardDate;

    @ApiModelProperty(value = "申请日期")
    private String applyDate;

    @ApiModelProperty(value = "被推荐人memberkey")
    private Integer memberKey;

    @ApiModelProperty(value = "被推荐人姓名")
    private String passiveRecommendedName;

    @ApiModelProperty(value = "被推荐人手机号")
    private String passiveRecommendedMobilenumber;

    @ApiModelProperty(value = "被推荐人入职日期")
    private String workDate;

    @ApiModelProperty(value = "被推荐人岗位")
    private String ex1;

    @ApiModelProperty(value = "被推荐人在岗状态")
    private String ex2;

    @ApiModelProperty(value = "推荐人姓名")
    private String recommendedName;

    @ApiModelProperty(value = "推荐人手机号")
    private String recommendedMobilenumber;

    @ApiModelProperty("奖金金额")
    private Integer rewardAmount;

    @ApiModelProperty("奖金类型")
    private String rewardType;

}
