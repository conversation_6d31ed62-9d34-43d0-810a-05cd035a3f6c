package com.wantwant.sfa.backend.referralBonus.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@ApiModel(value = "扣罚记录传参")
@Data
public class ButtonRequest extends PageParam {

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "扣罚月份")
    @NotBlank(message = "扣罚月份不能为空")
    private String penaltyMonth;

    @ApiModelProperty(value = "扣罚项目")
    private List<String> penaltyProject;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "扣罚人员")
    private String personnel;

}
