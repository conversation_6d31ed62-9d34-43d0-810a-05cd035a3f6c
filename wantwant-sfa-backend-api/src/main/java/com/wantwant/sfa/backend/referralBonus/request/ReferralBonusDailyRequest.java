package com.wantwant.sfa.backend.referralBonus.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "资金报表传参")
@Data
public class ReferralBonusDailyRequest extends PageParam {

    @ApiModelProperty(value = "操作人工号")
    @NotBlank(message = "操作人工号不能为空")
    private String employeeId;

    @ApiModelProperty(value = "大区")
    private String regionName;

    @ApiModelProperty(value = "分公司")
    private String branchName;

    @ApiModelProperty(value = "区域经理")
    private String departmentName;

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "奖励方案")
    private String rewardScheme;

    @ApiModelProperty(value = "推荐人信息")
    private String refereesInformation;

    @ApiModelProperty(value = "被推荐人信息")
    private String referralInformation;
}
