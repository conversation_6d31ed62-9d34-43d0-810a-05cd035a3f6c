package com.wantwant.sfa.backend.referralBonus.request;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.time.LocalDate;

@Api(value = "推荐奖金发放传参")
@Data
public class ReferralBonusDetailRequest {

    @ApiModelProperty(value = "操作人工号")
    private String employeeId;

    @ApiParam(value = "发放日期")
    private LocalDate issueDate;

    @ApiModelProperty(value = "状态(0.未申请;1.已取消;2.待复核;3.待审批;4.已驳回;5.发放成功;6.发放失败)")
    private Integer status;

    @ApiModelProperty(value = "推荐人信息")
    private String refereesInformation;

    @ApiModelProperty(value = "被推荐人信息")
    private String referralInformation;
}
