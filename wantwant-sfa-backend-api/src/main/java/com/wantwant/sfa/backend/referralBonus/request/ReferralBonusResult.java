package com.wantwant.sfa.backend.referralBonus.request;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Api(value = "发放结果List")
@Data
public class ReferralBonusResult {

    @ApiModelProperty(value = "编号")
    private String grantId;

    @ApiModelProperty(value = "发放结果(5.成功;6.失败)")
    private Integer issueResult;

    @ApiModelProperty(value = "发败原因")
    private String failureCause;
}
