package com.wantwant.sfa.backend.referralBonus.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "汰换记录传参")
@Data
public class ReplacementRequest extends PageParam {

    @ApiModelProperty(value = "组织id")
    private String organizationId;

    @ApiModelProperty(value = "离职月份")
    private String dimissionMonth;

    @ApiModelProperty(value = "岗位名称（1.合伙人/全职 2.合伙人/兼职 3.合伙人/承揽 4.企业合伙人 5.区域经理 6.区域总监 7.所有合伙人）")
    private Integer postName;

    @ApiModelProperty(value = "离职人员")
    private String dimissionName;

}
