package com.wantwant.sfa.backend.referralBonus.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "扣罚记录返回参数")
@Data
public class ButtonVo {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "大区")
    private String area;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "营业所")
    private String department;

    @ApiModelProperty(value = "入职日期")
    private String entryDate;

    @ApiModelProperty(value = "扣罚月份")
    private String penaltyMonth;

    @ApiModelProperty(value = "扣罚金额")
    private String penaltyAmount;

    @ApiModelProperty(value = "扣罚项目")
    private String penaltyProject;

    @ApiModelProperty(value = "姓名(相关汰换人员信息)")
    private String nameReplacement;

    @ApiModelProperty(value = "岗位名称(相关汰换人员信息)")
    private String postNameReplacement;

    @ApiModelProperty(value = "入职日期(相关汰换人员信息)")
    private String entryDateReplacement;

    @ApiModelProperty(value = "离职日期(相关汰换人员信息)")
    private String dimissionDateReplacement;

    @ApiModelProperty(value = "在职天数(相关汰换人员信息)")
    private String onJobDayReplacement;

    @ApiModelProperty(value = "在职月数(相关汰换人员信息)")
    private String onJobMonthReplacement;


}
