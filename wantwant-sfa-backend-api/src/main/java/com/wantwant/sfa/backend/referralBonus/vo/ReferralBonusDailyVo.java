package com.wantwant.sfa.backend.referralBonus.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Api(value = "奖金报表返回参数")
@Data
public class ReferralBonusDailyVo {

    @ApiModelProperty(value = "被推荐人大区")
    private String regionName;

    @ApiModelProperty(value = "被推荐人分公司")
    private String branchName;

    @ApiModelProperty(value = "被推荐人姓名")
    private String employeeName;

    @ApiModelProperty(value = "被推荐人工号")
    private String employeeId;

    @ApiModelProperty(value = "被推荐人手机")
    private String userMobile;

    @ApiModelProperty(value = "被推荐人入职日期")
    private LocalDate onboardTime;

    @ApiModelProperty(value = "被推荐人入职岗位")
    private String positionName;

    @ApiModelProperty(value = "被推荐人岗位属性")
    private String postType;

    @ApiModelProperty(value = "被推荐人在岗状态")
    private String employeeStatus;

    @ApiModelProperty(value = "被推荐人在岗天数")
    private String onboardDays;

    @ApiModelProperty(value = "第1个月")
    private Integer firstMonthPerformance;

    @ApiModelProperty(value = "第2个月")
    private Integer secondMonthPerformance;

    @ApiModelProperty(value = "第3个月")
    private Integer thirdMonthPerformance;

    @ApiModelProperty(value = "第4个月")
    private Integer fourthMonthPerformance;

    @ApiModelProperty(value = "第5个月")
    private Integer fifthMonthPerformance;

    @ApiModelProperty(value = "第6个月")
    private Integer sixthMonthPerformance;

    @ApiModelProperty(value = "推荐人姓名")
    private String superiorName;

    @ApiModelProperty(value = "推荐人电话")
    private String superiorMobile;

    @ApiModelProperty(value = "推荐人岗位")
    private String superiorPosition;

    @ApiModelProperty(value = "推荐人工号")
    private String superiorEmployId;

    @ApiModelProperty(value = "适用奖励方案")
    private String rewardScheme;

    @ApiModelProperty(value = "奖励一达标日期")
    private String rewardOneStandardDate;

    @ApiModelProperty(value = "奖励一发放日期")
    private String rewardOneCompletionDate;

    @ApiModelProperty(value = "奖励一奖励金额")
    private Integer rewardOne;

    @ApiModelProperty(value = "奖励一奖励说明")
    private String rewardOneDescription;

    @ApiModelProperty(value = "奖励二达标日期")
    private String rewardTwoStandardDate;

    @ApiModelProperty(value = "奖励二发放日期")
    private String rewardTwoCompletionDate;

    @ApiModelProperty(value = "奖励二奖励金额")
    private Integer rewardTwo;

    @ApiModelProperty(value = "奖励二奖励说明")
    private String rewardTwoDescription;

    @ApiModelProperty(value = "奖励三达标日期(成功入职奖励)")
    private String rewardThreeStandardDate;

    @ApiModelProperty(value = "奖励三发放日期(成功入职奖励)")
    private String rewardThreeCompletionDate;

    @ApiModelProperty(value = "奖励三奖励金额(成功入职奖励)")
    private Integer rewardThree;

    @ApiModelProperty(value = "奖励三奖励说明")
    private String rewardThreeDescription;

    @ApiModelProperty(value = "应发奖金合计")
    private Integer bonusTotal;

}
