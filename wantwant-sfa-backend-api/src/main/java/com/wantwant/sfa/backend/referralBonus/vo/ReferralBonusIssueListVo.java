package com.wantwant.sfa.backend.referralBonus.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Api(value = "推荐奖金发放返回参数")
@Data
public class ReferralBonusIssueListVo {

    @ApiModelProperty("奖金发放id")
    private Integer bonusIssueRecordId;

    @ApiModelProperty(value = "达成日期")
    private String standardDate;

    @ApiModelProperty(value = "申请日期")
    private LocalDate applyDate;

    @ApiModelProperty(value = "发放日期")
    private LocalDate issueDate;

    @ApiModelProperty(value = "被推荐人empId")
    private String referralEmpId;

    @ApiModelProperty(value = "被推荐人大区")
    private String regionName;

    @ApiModelProperty(value = "被推荐人分公司")
    private String branchName;

    @ApiModelProperty(value = "被推荐人营业所")
    private String departmentName;

    @ApiModelProperty(value = "被推荐人姓名")
    private String employeeName;

    @ApiModelProperty(value = "被推荐人工号")
    private String employeeId;

    @ApiModelProperty(value = "被推荐人手机")
    private String userMobile;

    @ApiModelProperty(value = "被推荐人memberkey")
    private String memberKey;

    @ApiModelProperty(value = "第1个月")
    private Integer firstMonthPerformance;

    @ApiModelProperty(value = "第2个月")
    private Integer secondMonthPerformance;

    @ApiModelProperty(value = "第3个月")
    private Integer thirdMonthPerformance;

    @ApiModelProperty(value = "第4个月")
    private Integer fourthMonthPerformance;

    @ApiModelProperty(value = "第5个月")
    private Integer fifthMonthPerformance;

    @ApiModelProperty(value = "第6个月")
    private Integer sixthMonthPerformance;

    @ApiModelProperty(value = "被推荐人入职日期")
    private LocalDate onboardTime;

    @ApiModelProperty(value = "被推荐人入职日期(年月日)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime onboardDateTime;

    @ApiModelProperty(value = "被推荐人入职岗位")
    private String positionName;

    @ApiModelProperty(value = "被推荐人岗位属性")
    private String postType;

    @ApiModelProperty(value = "被推荐人在岗状态")
    private String employeeStatus;

    @ApiModelProperty(value = "被推荐人在岗天数")
    private String onboardDays;

    @ApiModelProperty(value = "推荐人姓名")
    private String superiorName;

    @ApiModelProperty(value = "推荐人电话")
    private String superiorMobile;

    @ApiModelProperty(value = "推荐人岗位")
    private String superiorPosition;

    @ApiModelProperty(value = "推荐人工号")
    private String superiorEmployId;

    @ApiModelProperty(value = "奖励方案")
    private String rewardPlan;

    @ApiModelProperty(value = "奖励类型")
    private String rewardType;

    @ApiModelProperty(value = "奖励金额")
    private Integer rewardAmount;

    @ApiModelProperty(value = "奖励说明")
    private String rewardInstructions;

    @ApiModelProperty(value = "状态(0.未申请;1.已取消;2.待复核;3.待审批;4.已驳回;5.发放成功;6.发放失败;7.发放中)")
    private String status;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "操作人类型(1.账务；2.总部)")
    private Integer type;
}
