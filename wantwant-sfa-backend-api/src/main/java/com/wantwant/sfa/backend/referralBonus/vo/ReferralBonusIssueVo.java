package com.wantwant.sfa.backend.referralBonus.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReferralBonusIssueVo {

    @ApiModelProperty(value = "操作人类型(1.申请组;1.复核组;1.审批组)")
    private Integer employeeIdType;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "奖金发放数据")
    private List<ReferralBonusIssueListVo> list;
}
