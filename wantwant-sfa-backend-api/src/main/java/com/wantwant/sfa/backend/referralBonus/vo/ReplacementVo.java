package com.wantwant.sfa.backend.referralBonus.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "汰换记录返回参数")
@Data
public class ReplacementVo {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "大区")
    private String area;

    @ApiModelProperty(value = "分公司")
    private String company;

    @ApiModelProperty(value = "营业所")
    private String department;

    @ApiModelProperty(value = "入职日期")
    private String entryDate;

    @ApiModelProperty(value = "离职日期")
    private String dimissionDate;

    @ApiModelProperty(value = "在职天数")
    private Integer onJobDay;

    @ApiModelProperty(value = "在职月数")
    private Integer onJobMonth;

    @ApiModelProperty(value = "招聘费或推荐费")
    private BigDecimal recruitmentFee;

    @ApiModelProperty(value = "岗位底薪")
    private Integer basicSalary;

    @ApiModelProperty(value = "应扣金额 (扣罚本人)")
    private BigDecimal amountPayable;

    @ApiModelProperty(value = "实扣金额 (扣罚本人)")
    private BigDecimal solidBucklePayable;

    @ApiModelProperty(value = "已扣金额 (扣罚本人)")
    private BigDecimal amountDeductedPayable;

    @ApiModelProperty(value = "未扣金额 (扣罚本人)")
    private BigDecimal unbuckledPayable;

    @ApiModelProperty(value = "应扣金额 (扣罚主管)")
    private BigDecimal amountPayableSupervisor;

    @ApiModelProperty(value = "实扣金额 (扣罚主管)")
    private BigDecimal solidBucklePayableSupervisor;

    @ApiModelProperty(value = "已扣金额 (扣罚主管)")
    private BigDecimal amountDeductedPayableSupervisor;

    @ApiModelProperty(value = "未扣金额 (扣罚主管)")
    private BigDecimal unbuckledPayableSupervisor;

}
