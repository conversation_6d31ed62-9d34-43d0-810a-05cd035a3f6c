package com.wantwant.sfa.backend.shoppingGuide.api;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideCommitRequest;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideDetailRequest;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideListRequest;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideUpdateRequest;
import com.wantwant.sfa.backend.shoppingGuide.vo.ShoppingGuideDetailVo;
import com.wantwant.sfa.backend.shoppingGuide.vo.ShoppingGuideListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Description: 导购人员API。
 * @Auther: zhangpengpeng
 * @Date: 2024/07/03
 */
@Api(value = "ShoppingGuideApi", tags = "导购人员API")
@RequestMapping("/shopping/guide")
public interface ShoppingGuideApi {
    @ApiOperation(value = "导购人员提交", notes = "导购人员提交", httpMethod = "POST")
    @PostMapping("/commit")
    Response shoppingGuideCommit(@RequestBody @Validated ShoppingGuideCommitRequest request);

    @ApiOperation(value = "导购人员更新", notes = "导购人员更新", httpMethod = "POST")
    @PostMapping("/update")
    Response shoppingGuideUpdate(@RequestBody @Validated ShoppingGuideUpdateRequest request);

    @ApiOperation(value = "导购人员列表", notes = "导购人员列表", httpMethod = "GET")
    @GetMapping("/list")
    Response<IPage<ShoppingGuideListVo>> shoppingGuideList(ShoppingGuideListRequest request);

    @ApiOperation(value = "导购人员详情", notes = "导购人员详情", httpMethod = "GET")
    @GetMapping("/detail")
    Response<ShoppingGuideDetailVo> shoppingGuideDetail(ShoppingGuideDetailRequest request);


}
