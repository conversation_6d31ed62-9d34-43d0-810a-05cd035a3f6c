package com.wantwant.sfa.backend.shoppingGuide.request;


import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@ApiModel(value = "导购人员提交")
@ToString
public class ShoppingGuideCommitRequest {

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;

    @ApiModelProperty(value = "身份:12-短促导购BD、14-长促导购BD", required = true)
    @NotBlank(message = "缺少身份")
    private String ex;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "性别", required = true)
    @NotNull(message = "性别不能为空")
    private Integer gender;

    @ApiModelProperty(value = "身份证号", required = true)
    @NotNull(message = "身份证号不能为空")
    private String idCardNum;

    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @ApiModelProperty(value = "出生日期", required = true)
    @NotNull(message = "出生日期不能为空")
    private LocalDate birthday;

    @ApiModelProperty(value = "手机", required = true)
    @NotBlank(message = "手机不能为空")
    private String mobile;

    @ApiModelProperty(value = "战区CODE", required = true)
    @NotBlank(message = "战区CODE不能为空")
    private String areaCode;

    @ApiModelProperty(value = "战区名称", required = true)
    @NotBlank(message = "战区名称不能为空")
    private String areaName;

    @ApiModelProperty(value = "大区CODE", required = true)
    @NotBlank(message = "大区CODE不能为空")
    private String vareaCode;

    @ApiModelProperty(value = "大区名称", required = true)
    @NotBlank(message = "大区名称不能为空")
    private String vareaName;

    @ApiModelProperty(value = "省区CODE", required = true)
    @NotBlank(message = "省区CODE不能为空")
    private String provinceCode;

    @ApiModelProperty(value = "省区名称", required = true)
    @NotBlank(message = "省区名称不能为空")
    private String provinceName;

    @ApiModelProperty(value = "分公司CODE", required = true)
    @NotBlank(message = "分公司CODE不能为空")
    private String companyCode;

    @ApiModelProperty(value = "分公司名称", required = true)
    @NotBlank(message = "分公司名称不能为空")
    private String companyName;

    @ApiModelProperty(value = "区域经理层CODE", required = true)
    @NotBlank(message = "区域经理层CODE不能为空")
    private String departmentCode;

    @ApiModelProperty(value = "区域经理层名称", required = true)
    @NotBlank(message = "区域经理层名称不能为空")
    private String departmentName;


    @ApiModelProperty(value = "备注", required = true)
    @Length(max = 200, message = "备注不能超过200个字符")
    private String remark;

}
