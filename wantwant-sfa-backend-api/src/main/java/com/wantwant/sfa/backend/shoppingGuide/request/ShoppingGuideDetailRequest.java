package com.wantwant.sfa.backend.shoppingGuide.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "导购人员详情")
@ToString
public class ShoppingGuideDetailRequest implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;

    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value = "memberKey", required = true)
    @NotNull(message = "memberKey不能为空")
    private Long memberKey;

}
