package com.wantwant.sfa.backend.shoppingGuide.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "导购人员列表")
@ToString
public class ShoppingGuideListRequest extends PageParam implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;

    @ApiModelProperty("组织Id")
    private String organizationId;

    @ApiModelProperty("姓名或工号，模糊匹配")
    private String employeeInfo;

    @ApiModelProperty("状态 0:关闭 1:正常")
    private Integer status;

    @ApiModelProperty(value = "业务组织", hidden = true)
    private List<String> organizationIds;
    @ApiModelProperty(value = "组别", hidden = true)
    private String organizationType;
    @ApiModelProperty(value = "组别", hidden = true)
    private Integer businessGroup;
}
