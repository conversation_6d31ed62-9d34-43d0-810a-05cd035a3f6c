package com.wantwant.sfa.backend.shoppingGuide.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "导购人员更新")
@ToString
public class ShoppingGuideUpdateRequest extends ShoppingGuideCommitRequest {

    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value = "memberKey", required = true)
    @NotNull(message = "memberKey不能为空")
    private Long memberKey;

    @ApiModelProperty(value = "状态", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

}
