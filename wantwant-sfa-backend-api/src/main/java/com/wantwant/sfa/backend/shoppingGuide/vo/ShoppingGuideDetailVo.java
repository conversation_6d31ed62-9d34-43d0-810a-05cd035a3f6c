package com.wantwant.sfa.backend.shoppingGuide.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

@Data
@ApiModel("导购人员详情返回")
public class ShoppingGuideDetailVo {

    @ApiModelProperty("ID/编号")
    private Long id;

    @ApiModelProperty("memberKey")
    private Long memberKey;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private Integer gender;


    @ApiModelProperty(value = "身份:12-短促导购BD、14-长促导购BD", required = true)
    private String ex;

    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "身份证")
    private String idCardNum;

    @ApiModelProperty(value = "战区CODE")
    private String areaCode;

    @ApiModelProperty(value = "战区名称")
    private String areaName;

    @ApiModelProperty(value = "大区CODE")
    private String vareaCode;

    @ApiModelProperty(value = "大区名称")
    private String vareaName;

    @ApiModelProperty(value = "省区CODE")
    private String provinceCode;

    @ApiModelProperty(value = "省区名称")
    private String provinceName;

    @ApiModelProperty(value = "分公司CODE")
    private String companyCode;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "区域经理层CODE")
    private String departmentCode;

    @ApiModelProperty(value = "区域经理层名称")
    private String departmentName;

    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty("状态")
    private Integer status;

}
