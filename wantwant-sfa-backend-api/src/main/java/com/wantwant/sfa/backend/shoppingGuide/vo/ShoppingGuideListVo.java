package com.wantwant.sfa.backend.shoppingGuide.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("导购人员列表返回")
public class ShoppingGuideListVo {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty(value = "memberKey")
    private Long memberKey;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private Integer gender;

    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    @ApiModelProperty("手机")
    private String mobile;

    @ApiModelProperty(value = "战区名称")
    private String areaName;

    @ApiModelProperty(value = "大区名称")
    private String vareaName;

    @ApiModelProperty(value = "省区名称")
    private String provinceName;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "区域经理层名称")
    private String departmentName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;

}
