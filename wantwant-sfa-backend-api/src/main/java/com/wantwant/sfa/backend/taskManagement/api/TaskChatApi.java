package com.wantwant.sfa.backend.taskManagement.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatDelRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatEditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskLogReplyRequest;
import com.wantwant.sfa.backend.taskManagement.vo.TaskChatReplyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/07/下午1:49
 */
@Api(value = "TaskChatApi", tags = "任务回复")
@RequestMapping("/taskChat")
public interface TaskChatApi {


    @ApiOperation(value = "回复任务", notes = "回复任务", httpMethod = "POST")
    @PostMapping("/reply")
    Response reply(@RequestBody @Validated TaskLogReplyRequest taskLogReplyRequest);

    @ApiOperation(value = "获取回复信息", notes = "获取回复信息", httpMethod = "GET")
    @GetMapping("/reply")
    Response<List<TaskChatReplyVo>> getReply(@ApiParam(name = "logId", value = "logId", required = true) @RequestParam(value = "logId") Long logId,
                                             @ApiParam(name = "person", value = "操作人工号", required = true) @RequestParam(value = "person") String person
                                             );

    @ApiOperation(value = "删除回复", notes = "删除回复", httpMethod = "POST")
    @PostMapping("/reply/delete")
    Response deleteReply(@RequestBody @Validated TaskChatDelRequest taskChatDelRequest);


    @ApiOperation(value = "编辑回复内容", notes = "编辑回复内容", httpMethod = "POST")
    @PostMapping("/reply/edit")
    Response edit(@RequestBody @Validated TaskChatEditRequest taskChatEditRequest);
}
