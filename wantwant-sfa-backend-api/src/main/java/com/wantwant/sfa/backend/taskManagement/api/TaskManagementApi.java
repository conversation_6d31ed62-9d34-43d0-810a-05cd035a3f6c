package com.wantwant.sfa.backend.taskManagement.api;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.taskManagement.request.*;
import com.wantwant.sfa.backend.taskManagement.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/上午9:14
 */

@Api(value = "TaskManagementApi", tags = "任务管理")
@RequestMapping("/taskManagement")
public interface TaskManagementApi {

    @ApiOperation(value = "创建任务", notes = "创建任务", httpMethod = "PUT")
    @PutMapping
    Response createTask(@RequestBody @Valid CreateTaskRequest createTaskRequest);

    @ApiOperation(value = "获取指派人信息", notes = "获取指派人信息", httpMethod = "GET")
    @GetMapping("/assign")
    Response<List<AssignVo>> getAssign(@RequestParam String employeeName);

    @ApiOperation(value = "关注任务", notes = "关注任务", httpMethod = "POST")
    @PostMapping("/follow")
    Response follow(@RequestBody @Valid TaskFollowRequest taskFollowRequest);

    @ApiOperation(value = "删除任务",notes = "删除任务",httpMethod = "DELETE")
    @DeleteMapping
    Response deleteTask(@RequestBody @Valid TaskOperatorRequest request);

    @ApiOperation(value = "任务列表查询", notes = "任务列表查询", httpMethod = "POST")
    @PostMapping("/list")
    Response<Page<TaskVo>> selectList(@RequestBody TaskSelectRequest taskSelectRequest);

    @ApiOperation(value = "任务列表-列表下载", notes = "任务列表列表下载", httpMethod = "POST")
    @PostMapping(value = "/download")
    void downloadList(@RequestBody @Validated TaskSelectRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "修改任务", notes = "修改任务", httpMethod = "POST")
    @PostMapping
    Response updateTask(@RequestBody @Valid UpdateTaskRequest updateTaskRequest);

    @ApiOperation(value = "修改进行中任务", notes = "修改进行中任务", httpMethod = "POST")
    @PostMapping("/modify")
    Response modifyTask(@RequestBody @Valid UpdateTaskRequest command);

    @ApiOperation(value = "修改截止日期", notes = "修改截止日期", httpMethod = "POST")
    @PostMapping("/modifyDeadline")
    Response modifyDeadline(@RequestBody @Valid ModifyDeadlineRequest modifyDeadlineRequest);

    @ApiOperation(value = "获取任务办理情况", notes = "获取任务办理情况", httpMethod = "GET")
    @GetMapping("/situation/{taskId}")
    Response<List<TaskSituationVo>> getSituation(@PathVariable Long taskId);

    @ApiOperation(value = "获取任务操作记录", notes = "获取任务操作记录", httpMethod = "GET")
    @GetMapping("/log/{taskId}")
    Response<List<TaskLogVo>> getTaskLog(@PathVariable Long taskId);

    @ApiOperation(value = "获取任务详情", notes = "获取任务详情", httpMethod = "GET")
    @GetMapping("/{taskId}")
    Response<TaskDetailVo> getDetail(@PathVariable Long taskId,@RequestParam("person")String person);

    @ApiOperation(value = "确认发布", notes = "确认发布", httpMethod = "POST")
    @PostMapping("/publish")
    Response publish(@RequestBody @Valid TaskOperatorRequest request);

    @ApiOperation(value = "批量发布", notes = "确认发布", httpMethod = "POST")
    @PostMapping("/batchPublish")
    Response batchPublish(@RequestBody @Valid TaskBatchCommand command);

    @ApiOperation(value = "提交撤回", notes = "提交撤回", httpMethod = "POST")
    @PostMapping("/revert") //任务发布前驳回
    Response revert(@RequestBody @Valid TaskOperatorRequest request);

    @ApiOperation(value = "签收", notes = "签收", httpMethod = "POST")
    @PostMapping("/sign")
    Response sign(@RequestBody @Valid TaskOperatorRequest request);

    @ApiOperation(value = "修改指派人", notes = "修改指派人", httpMethod = "POST")
    @PostMapping("/modifyAssign")
    Response modifyAssign(@RequestBody @Valid TaskAssignModifyRequest request);

    @ApiOperation(value = "提交结果", notes = "提交结果", httpMethod = "POST")
    @PostMapping("/submitSituation")//任务更新
    Response submitSituation(@RequestBody @Valid TaskSituationSubmitRequest taskSituationSubmitRequest);

    @ApiOperation(value = "完成任务并送审", notes = "完成任务并送审", httpMethod = "POST")
    @PostMapping("/complete")
    Response complete(@RequestBody @Valid TaskCompleteRequest request);


    @ApiOperation(value = "任务完结", notes = "任务完结", httpMethod = "POST")
    @PostMapping("/finish")
    Response finish(@RequestBody @Valid TaskOperatorRequest request);

    @ApiOperation(value = "批量完结", notes = "批量完结", httpMethod = "POST")
    @PostMapping("/batchFinish")
    Response batchFinish(@RequestBody @Valid TaskBatchCommand command);


    @ApiOperation(value = "任务重办", notes = "任务重办", httpMethod = "POST")
    @PostMapping("/redo")
    Response redo(@RequestBody @Valid TaskRedoRequest request);


    @ApiOperation(value = "任务挂起", notes = "任务挂起", httpMethod = "POST")
    @PostMapping("/suspend")
    Response suspend(@RequestBody @Valid TaskSuspendRequest request);


    @ApiOperation(value = "任务关闭", notes = "任务关闭", httpMethod = "POST")
    @PostMapping("/closed")
    Response closed(@RequestBody @Valid TaskOperatorRequest request);

    @ApiOperation(value = "根据名称查询任务", notes = "根据名称查询任务", httpMethod = "POST")
    @PostMapping("/searchTaskByName")
    Response<List<TaskSampleVo>> searchTaskByName(@RequestParam("taskName") String taskName);

    @ApiOperation(value = "任务信息", notes = "任务信息", httpMethod = "GET")
    @GetMapping("/taskInfo/{person}")
    Response<TaskInfoVo> taskInfo(@PathVariable String person);

    @ApiOperation(value = "任务追踪列表", notes = "任务追踪列表", httpMethod = "POST")
    @PostMapping("/trace")
    Response<Page<TaskTraceVo>> selectTaskTraceAudit(@RequestBody TaskTraceAuditSearchRequest request);

    @ApiOperation(value = "任务追踪列表导出", notes = "任务追踪列表导出", httpMethod = "POST")
    @PostMapping("/trace/export")
    void taskTraceExport(@RequestBody TaskTraceAuditSearchRequest request);


    @ApiOperation(value = "任务追踪回复", notes = "任务追踪回复", httpMethod = "POST")
    @PostMapping("/trace/callback")//老板回复
    Response callback(@Valid @RequestBody TraceCallBackRequest traceCallBackRequest);


    @ApiOperation(value = "审批追踪记录", notes = "审批追踪记录", httpMethod = "POST")
    @PostMapping("/trace/audit")//进度驳回
    Response audit(@Valid @RequestBody TraceAuditRequest traceAuditRequest);

    @ApiOperation(value = "查看是否有审核权限", notes = "查看是否有审核权限", httpMethod = "GET")
    @GetMapping("/trace/audit/{person}")
    Response<Boolean> auditPermission(@PathVariable String person);

    /**
     * 任务驳回
     * LEO的待确认完结、项目管理（杜颖、孙帆）的待审核，新增驳回按钮
     * @param request
     * @return
     */
    @ApiOperation(value = "任务驳回", notes = "发布后任务驳回", httpMethod = "POST")
    @PostMapping("/refuse")
    Response refuse(@RequestBody @Valid TaskRefuseRequest request);


    @ApiOperation(value = "发起会议", notes = "发起会议", httpMethod = "POST")
    @PostMapping("/launchMeeting")
    Response launchMeeting(@RequestBody @Valid LaunchMeetingRequest launchMeetingRequest);


    @ApiOperation(value = "待确认会议审核", notes = "待确认会议审核", httpMethod = "POST")
    @PostMapping("/meetingAudit")
    Response meetingAudit(@RequestBody @Valid TaskAuditRequest taskAuditRequest);


    @ApiOperation(value = "确认完结", notes = "确认完结", httpMethod = "POST")
    @PostMapping("/meetingAudit/finish")
    Response meetingAuditFinish(@RequestBody @Valid TaskAuditRequest taskAuditRequest);


    @ApiOperation(value = "修改任务关联", notes = "修改任务关联", httpMethod = "POST")
    @PostMapping("/taskContextModify")
    Response taskContextModify(@RequestBody @Valid TaskContextModifyRequest taskContextModifyRequest);


    @ApiOperation(value = "催办", notes = "催办", httpMethod = "POST")
    @PostMapping("/urge")
    Response urge(@RequestBody @Valid TaskAuditRequest taskAuditRequest);


    @ApiOperation(value = "批量催办", notes = "批量催办", httpMethod = "POST")
    @PostMapping("/batchUrge")
    Response batchUrge(@RequestBody @Valid TaskBatchCommand command);

}
