package com.wantwant.sfa.backend.taskManagement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/上午9:16
 */
@Data
@ApiModel("创建任务request")
public class CreateTaskRequest {

    @ApiModelProperty("操作人工号")
    @NotBlank(message = "缺少操作人工号")
    private String person;

    @ApiModelProperty("任务性质")
    private Integer taskNature;

    @ApiModelProperty("任务名称")
    @NotBlank(message = "缺少任务名称")
    private String taskName;

    @ApiModelProperty("关联任务ID")
    private Long contextTask;

    @ApiModelProperty("任务类型(1.交办任务 2.个人任务 3.部门任务)")
    @NotNull(message = "缺少任务类型")
    private Integer taskType;

    @ApiModelProperty("组织划分:1.后勤、2.市场、3.业务、4.产研")
    private Integer divisionType;

    @ApiModelProperty("任务大类(1.开源 2.截流)")
    private Integer category;

    @ApiModelProperty("任务子类(1.后勤 2.销售 3.造旺APP产品功能 4.SFA产品功能)")
    private Integer taskSubType;

    @ApiModelProperty("任务来源(1.常规 2.双周会 3.季度会议 4.月会 5.点对点交办 6.其他 7.周报 8.月报)")
    private Integer taskSource;

    @ApiModelProperty("任务来源选择其他时填写")
    private String taskSourceOther;

    @ApiModelProperty("所属部门CODE")
    private String deptCode;

    @ApiModelProperty("任务内容")
    @NotBlank(message = "缺少任务内容")
    private String content;

    @ApiModelProperty("任务背景")
    private String background;

    @ApiModelProperty("周报月报名称")
    private String reportTitle;

    @ApiModelProperty("任务目的")
    private String purpose;

    @ApiModelProperty("任务优先级(1.低 2.中 3.高 4.极高)")
    private Integer priority;

    @ApiModelProperty("紧急程度")
    private String urgencyLevel;
    @ApiModelProperty("紧急原因")
    @Size(max = 500,message = "紧急原因过长")
    private String urgencyReason;

    @ApiModelProperty("任务价值")
    private String worth;

    @ApiModelProperty("办理截止时间,格式yyyy-MM-dd")
    private LocalDate deadline;

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> annex;

    @ApiModelProperty("主办人工号")
    private TaskAssignRequest mainProcessUser;

    @ApiModelProperty("协办人工号")
    private List<TaskAssignRequest> assistedProcessUsers;

    @ApiModelProperty("抄送人工号")
    private List<TaskAssignRequest> ccProcessUsers;

    @ApiModelProperty("是否提交:1.提交 0.否")
    private Integer submit;

    @ApiModelProperty("是否转移:1.是")
    private Boolean transfer;

    @ApiModelProperty("关联外键")
    private Long fKey;
    @ApiModelProperty("类型:1.周报 2.月报")
    private Integer type;
    @ApiModelProperty("问题ID")
    private Long issueId;


}
