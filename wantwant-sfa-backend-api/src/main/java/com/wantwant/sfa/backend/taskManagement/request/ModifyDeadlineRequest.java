package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/28/上午9:57
 */
@Data
@ToString
public class ModifyDeadlineRequest extends TaskOperatorRequest {

    @ApiModelProperty("截止日期")
    @NotBlank(message = "缺少截止日期")
    private String deadline;
}
