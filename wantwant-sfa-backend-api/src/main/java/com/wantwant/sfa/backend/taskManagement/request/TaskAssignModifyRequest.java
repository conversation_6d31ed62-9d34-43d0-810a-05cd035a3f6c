package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午5:28
 */
@Data
@ApiModel("修改指派人")
public class TaskAssignModifyRequest extends TaskOperatorRequest {

    @ApiModelProperty("主办人")
    private TaskAssignRequest mainProcessUser;

    @ApiModelProperty("协办人")
    private List<TaskAssignRequest> assistedProcessUsers;

    @ApiModelProperty("抄送人")
    private List<TaskAssignRequest> ccProcessUsers;
}
