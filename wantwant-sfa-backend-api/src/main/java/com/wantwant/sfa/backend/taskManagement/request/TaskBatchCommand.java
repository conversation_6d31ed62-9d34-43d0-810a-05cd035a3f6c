package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/20/上午10:26
 */
@Data
@ApiModel("批处理command")
public class TaskBatchCommand {

    @ApiModelProperty("缺少任务ID")
    @NotEmpty(message = "缺少任务ID")
    private List<Long> taskIds;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;
    @ApiModelProperty("备注")
    private String remark;
}
