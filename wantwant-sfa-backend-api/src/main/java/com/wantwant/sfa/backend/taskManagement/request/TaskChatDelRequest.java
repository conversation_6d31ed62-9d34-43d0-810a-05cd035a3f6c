package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/08/上午8:45
 */
@Data
@ApiModel("删除内容request")
@ToString
public class TaskChatDelRequest {

    @ApiModelProperty("id")
    @NotNull(message = "缺少ID")
    private Long id;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;
}
