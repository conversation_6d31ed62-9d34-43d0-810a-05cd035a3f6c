package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/08/上午8:50
 */
@Data
@ApiModel("编辑回复内容")
public class TaskChatEditRequest {

    @ApiModelProperty("id")
    @NotNull(message = "缺少ID")
    private Long id;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("内容")
    @NotBlank(message = "缺少内容")
    private String content;
}
