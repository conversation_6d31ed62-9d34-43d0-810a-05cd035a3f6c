package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/14/下午5:38
 */
@Data
@ApiModel("任务完成")
@ToString
public class TaskCompleteRequest extends TaskOperatorRequest {

    @ApiModelProperty("任务标签")
    private String taskTag;
}
