package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/06/上午10:29
 */
@Data
@ApiModel("修改关联任务")
@ToString
public class TaskContextModifyRequest {

    @ApiModelProperty("任务ID")
    @NotNull(message = "缺少任务ID")
    private Long taskId;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("操作类型:1.关联主任务 2.关联自任务")
    private Integer type;

    @ApiModelProperty("原关联任务Id")
    private Long oldContextTaskId;

    @ApiModelProperty("新关联任务Id")
    private Long newContextTaskId;
}
