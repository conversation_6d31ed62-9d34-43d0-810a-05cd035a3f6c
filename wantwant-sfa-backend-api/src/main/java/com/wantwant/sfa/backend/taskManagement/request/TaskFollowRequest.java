package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/29/上午9:32
 */
@Data
@ApiModel("关注任务")
@ToString
public class TaskFollowRequest {
    @ApiModelProperty("任务ID")
    @NotNull(message = "缺少任务ID")
    private Long taskId;

    @ApiModelProperty("操作人工号")
    @NotBlank(message = "缺少操作人工号")
    private String person;


    @ApiModelProperty("关注:1.关注 2.取消关注")
    @NotNull(message = "缺少关注")
    private Integer follow;
}
