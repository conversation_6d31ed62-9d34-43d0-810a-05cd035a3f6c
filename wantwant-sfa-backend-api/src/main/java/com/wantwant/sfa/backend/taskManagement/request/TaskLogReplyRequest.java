package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/07/下午1:41
 */
@Data
@ApiModel("任务回复request")
@ToString
public class TaskLogReplyRequest {
    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;
    @ApiModelProperty("操作记录ID")
    @NotNull(message = "缺少操作记录ID")
    private Long logId;
    @ApiModelProperty("内容")
    @NotBlank(message = "缺少内容")
    private String context;
    @ApiModelProperty("上级ID")
    private Long parentId;
    @ApiModelProperty("@的用户")
    private List<TaskAssignRequest> users;
}
