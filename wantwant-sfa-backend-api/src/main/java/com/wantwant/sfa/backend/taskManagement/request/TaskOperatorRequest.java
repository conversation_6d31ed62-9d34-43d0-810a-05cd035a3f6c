package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午5:22
 */
@Data
@ToString
@ApiModel("任务操作")
public class TaskOperatorRequest {
    @ApiModelProperty("任务ID")
    @NotNull(message = "缺少任务ID")
    private Long taskId;
    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("任务标签")
    private String taskTag;
}
