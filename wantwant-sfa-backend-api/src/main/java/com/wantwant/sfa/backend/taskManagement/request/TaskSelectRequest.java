package com.wantwant.sfa.backend.taskManagement.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午3:28
 */
@Data
@ApiModel("任务查询request")
@ToString
public class TaskSelectRequest extends PageParam {

    @ApiModelProperty("登陆人信息")
    @NotBlank(message = "缺少登陆人信息")
    private String person;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("任务类型:1.交办任务 2.个人任务 3.部门任务")
    private String taskType;
    @ApiModelProperty("状态:10.草稿 20.待发布 21.发布审核 30.待签收 40.进行中 50.送审 51.确认完结 60.办结 70.重办待签收 80.关闭 401.挂起 402.到期 403.逾期")
    private String status;
    @ApiModelProperty("周更新状态:1.已更新 2.未更新")
    private Integer weekRefreshStatus;
    @ApiModelProperty("主办部门CODE")
    private String deptCode;
    @ApiModelProperty("协办部门CODE")
    private String assistDeptCode;
    @ApiModelProperty("时限开始时间")
    private String deadlineStart;
    @ApiModelProperty("提交时间")
    private String publishTime;
    @ApiModelProperty("时限结束日期")
    private String deadlineEnd;
    @ApiModelProperty("指派人姓名")
    private String assignUserName;
    @ApiModelProperty("是否是待办")
    private boolean processing;
    @ApiModelProperty("是否关注")
    private boolean follow;
    @ApiModelProperty("是否抄送")
    private boolean cc;
    @ApiModelProperty("任务优先级(1.低 2.中 3.高 4.极高)")
    private Integer priority;
    @ApiModelProperty("紧急程度查询")
    private String urgencyLevel;
    @ApiModelProperty("主办人姓名")
    private String mainDoUserName;
    @ApiModelProperty("协办人姓名")
    private String assistedUserName;
    @ApiModelProperty("排序字段 交办日期:publishTime 办理时限:deadline")
    private String sortFields;
    @ApiModelProperty("排序类型(0 不排序 1 正序 2 倒序)")
    private String sortType;

    private boolean isProject;
}
