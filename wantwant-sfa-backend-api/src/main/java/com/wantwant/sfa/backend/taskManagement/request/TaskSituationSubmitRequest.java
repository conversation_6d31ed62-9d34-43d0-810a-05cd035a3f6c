package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午5:31
 */
@Data
@ApiModel("提交结果request")
public class TaskSituationSubmitRequest extends TaskOperatorRequest {
    @ApiModelProperty("办理情况")
    @NotBlank(message = "缺少办理情况")
    private String situation;
    @ApiModelProperty("预计完成时间")
    @NotBlank(message = "缺少预计完成时间")
    private String expectFinishDate;
    @ApiModelProperty("是否需要回复:1.是 0.否")
    @NotNull(message = "缺少是否需要回复选项")
    private Integer requireCallback;
    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;
}
