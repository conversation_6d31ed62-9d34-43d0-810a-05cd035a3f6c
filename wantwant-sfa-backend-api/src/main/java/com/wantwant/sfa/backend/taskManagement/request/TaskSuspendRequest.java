package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/14/下午6:34
 */
@Data
@ApiModel("任务挂起request")
@ToString
public class TaskSuspendRequest extends TaskOperatorRequest{

    @ApiModelProperty("是否挂起:1.挂起 0.回复")
    @NotNull(message = "缺少挂起控制参数")
    private Integer suspend;
}
