package com.wantwant.sfa.backend.taskManagement.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/16/上午11:38
 */
@ApiModel("任务追踪审核查询request")
@ToString
@Data
public class TaskTraceAuditSearchRequest extends PageParam {

    @ApiModelProperty("部门CODE")
    private String deptCode;

    @ApiModelProperty("协办部门")
    private String assistDeptCode;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("提交人姓名")
    private String submitUserName;

    @ApiModelProperty("任务类型(1.交办任务 2.个人任务 3.部门任务)")
    private Integer taskType;

    @ApiModelProperty("本周更新状态:1.已更新 2.未更新")
    private Integer weekRefresh;

    @ApiModelProperty("状态:10.草稿 20.待发布 30.待签收 40.进行中 50.送审 60.办结 70.重办待签收 80.关闭 401.挂起 402.到期 403.逾期")
    private Integer status;

    @ApiModelProperty("traceId")
    private Long traceId;

    @ApiModelProperty("提交开始时间")
    private String submitStartTime;

    @ApiModelProperty("提交结束时间")
    private String submitEndTime;

    @ApiModelProperty("是否需要回复:1.需要 0.不需要")
    private Integer requireCallback;

    @ApiModelProperty("审核状态:0.未审核 1.通过 2.驳回")
    private Integer auditStatus;

    @ApiModelProperty("交办开始日期")
    private String publishStartTime;

    @ApiModelProperty("交办结束日期")
    private String publishEndTime;

    @ApiModelProperty("操作人")
    @NotBlank(message = "缺少操作人")
    private String person;

    @ApiModelProperty("主办人姓名")
    private String mainDoUserName;
    @ApiModelProperty("协办人姓名")
    private String assistedUserName;
    @ApiModelProperty("查看类型(1.主办 2.协办)")
    private Integer assignType;
    @ApiModelProperty("排序字段 交办日期:publishTime 办理时限:deadline")
    private String sortFields;
    @ApiModelProperty("排序类型(0 不排序 1 正序 2 倒序)")
    private String sortType;

    private boolean needPage =true;
}
