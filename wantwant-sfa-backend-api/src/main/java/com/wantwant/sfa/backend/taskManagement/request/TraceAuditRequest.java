package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/16/下午6:10
 */
@Data
@ApiModel("任务追踪审核request")
@ToString
public class TraceAuditRequest {
    @ApiModelProperty("traceIds")
    @NotEmpty(message = "缺少traceIds")
    private List<Long> traceIds;

    @ApiModelProperty("处理结果:1.同一 2.驳回")
    @NotNull(message = "缺少处理结果")
    private Integer result;

    @ApiModelProperty("处理意见")
    private String comment;

    @ApiModelProperty("操作人")
    private String person;
}
