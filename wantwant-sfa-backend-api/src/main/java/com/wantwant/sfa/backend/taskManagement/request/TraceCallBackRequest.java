package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/16/下午5:56
 */
@Data
@ToString
@ApiModel("任务追踪回复request")
public class TraceCallBackRequest {
    @ApiModelProperty("traceId")
    @NotNull(message = "缺少traceId")
    private Long traceId;
    @ApiModelProperty("内容")
    @NotBlank(message = "缺少内容")
    private String comment;
}
