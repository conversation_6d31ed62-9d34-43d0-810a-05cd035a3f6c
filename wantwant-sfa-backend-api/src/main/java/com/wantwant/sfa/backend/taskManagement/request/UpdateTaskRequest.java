package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/上午9:24
 */
@ApiModel("修改任务request")
@Data
public class UpdateTaskRequest extends CreateTaskRequest {

    @ApiModelProperty("任务ID")
    @NotNull(message = "缺少任务ID")
    private Long taskId;
}
