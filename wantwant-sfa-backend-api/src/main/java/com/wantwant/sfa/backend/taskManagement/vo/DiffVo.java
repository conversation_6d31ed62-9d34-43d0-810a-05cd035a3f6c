package com.wantwant.sfa.backend.taskManagement.vo;

import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.ObjectDiffModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/下午2:08
 */
@Data
@ApiModel("差异VO")
public class DiffVo {

    /** 字段名 */
    @ApiModelProperty("字段名")
    private String filedName;
    /** 字段注解 */
    @ApiModelProperty("字段注解")
    private String filedAnnotation;
    /** 原值 */
    @ApiModelProperty("原值")
    private String oldValue;
    /** 新值 */
    @ApiModelProperty("新值")
    private String value;

    public static DiffVo create(ObjectDiffModel objectDiffModel) {
        if(Objects.isNull(objectDiffModel)){
            return null;
        }

        DiffVo diffVo = new DiffVo();
        BeanUtils.copyProperties(objectDiffModel,diffVo);
        return diffVo;
    }
}
