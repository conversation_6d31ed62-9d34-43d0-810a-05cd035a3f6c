package com.wantwant.sfa.backend.taskManagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/07/下午2:54
 */
@Data
@ApiModel("任务记录回复Vo")
public class TaskChatReplyVo {
    @ApiModelProperty("回复内容ID")
    private Long id;
    @ApiModelProperty(hidden = true)
    private Long parentId;
    @ApiModelProperty("回复人姓名")
    private String employeeName;
    @ApiModelProperty("回复内容")
    private String content;
    @ApiModelProperty("回复时间")
    private String createTime;
    @ApiModelProperty("是否可操作")
    private boolean canProcess;
    @ApiModelProperty("是否可回复")
    private boolean canReply = true;
    @ApiModelProperty("子内容")
    private List<TaskChatReplyVo> children;
}
