package com.wantwant.sfa.backend.taskManagement.vo;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午3:55
 */
@Data
@ApiModel("任务明细")
public class TaskDetailVo {
    @ApiModelProperty("任务ID")
    private Long taskId;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("任务标签")
    private String taskTag;


    @ApiModelProperty("关联主任务")
    private ContextTaskVo parentTask;
    @ApiModelProperty("关联子任务")
    private List<ContextTaskVo> childrenTask;
    @ApiModelProperty("组织划分:1.后勤、2.市场、3.业务、4.产研")
    private Integer divisionType;
    @ApiModelProperty("任务内容")
    private String content;
    @ApiModelProperty("任务背景")
    private String background;
    @ApiModelProperty("周报月报名称")
    private String reportTitle;
    @ApiModelProperty("外部主键(周报，月报主键)")
    private Long fKey;
    @ApiModelProperty("任务目的")
    private String purpose;
    @ApiModelProperty("任务大类(1.开源 2.截流)")
    private Integer category;
    @ApiModelProperty("创建人姓名")
    private String createUserName;
    @ApiModelProperty("创建人部门")
    private String createUserDeptName;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("时限日期")
    private String deadline;
    @ApiModelProperty("任务价值")
    private String worth;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("任务优先级：1.低 2.中 3.高 4.极高")
    private Integer priority;
    @ApiModelProperty("紧急程度")
    private String urgencyLevel;
    @ApiModelProperty("紧急原因")
    private String urgencyReason;
    @ApiModelProperty("任务子类(1.后勤 2.销售 3.造旺APP产品功能 4.SFA产品功能)")
    private Integer taskSubType;
    @ApiModelProperty("任务来源(1.常规 2.双周会 3.季度会议 4.月会 5.点对点交办 6.其他)")
    private Integer taskSource;
    @ApiModelProperty("任务来源选择其他时填写")
    private String taskSourceOther;
    @ApiModelProperty("任务性质")
    @NotNull(message = "缺少任务性质")
    private Integer taskNature;
    @ApiModelProperty("发布时间")
    private String publishTime;

    @ApiModelProperty("部门code")
    private String deptCode;
    @ApiModelProperty("任务类型(1.交办任务 2.个人任务 3.部门任务)")
    private Integer taskType;
    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> annex;

    @ApiModelProperty("是否关注:0.否 1.是")
    private Integer follow;

    @ApiModelProperty("主办人信息")
    private AssignVo mainAssignUser;
    @ApiModelProperty("协办人信息")
    private List<AssignVo> assignUser;
    @ApiModelProperty("抄送人信息")
    private List<AssignVo> ccAssignUser;
    @ApiModelProperty("是否可修改任务")
    private boolean modify;
    @ApiModelProperty("是否可修改")
    private boolean update;
    @ApiModelProperty("是否可修改处理人")
    private boolean modifyAssign;
    @ApiModelProperty("是否可修改处理人")
    private boolean modifyMainAssign;
    @ApiModelProperty("是否可签收")
    private boolean sign;
    @ApiModelProperty("是否可推送")
    private boolean publish;
    @ApiModelProperty("是否可推送审核")
    private boolean publishAudit;
    @ApiModelProperty("是否可提交结果")
    private boolean submitSituation;
    @ApiModelProperty("是否可关闭")
    private boolean close;
    @ApiModelProperty("是否可挂起")
    private boolean suspend;
    @ApiModelProperty("是否可恢复")
    private boolean restore;
    @ApiModelProperty("是否可确认办结")
    private boolean finish;
    @ApiModelProperty("是否可确认办结审核")
    private boolean finishAudit;
    @ApiModelProperty("是否可重办")
    private boolean redone;
    @ApiModelProperty("是否可回退")
    private boolean revert;
    @ApiModelProperty("是否可送审")
    private boolean send;
    @ApiModelProperty("是否可修改截止日期")
    private boolean modifyDeadline;
    @ApiModelProperty("是否可发起会议")
    private boolean launchMeeting;
    @ApiModelProperty("是否可修改关联")
    private boolean modifyContext;
    @ApiModelProperty("是否可催办")
    private boolean urge;
    @ApiModelProperty("是否可创建会议")
    private boolean createMeeting;

    @ApiModelProperty(value = "是否驳回按钮",notes = "LEO的待确认发布、待确认完结、项目管理（杜颖、孙帆）的待发布、待审核，驳回按钮")
    private boolean refuseButton;
}
