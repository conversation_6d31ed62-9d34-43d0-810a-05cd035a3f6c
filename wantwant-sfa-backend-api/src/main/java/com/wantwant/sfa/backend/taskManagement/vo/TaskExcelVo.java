package com.wantwant.sfa.backend.taskManagement.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("任务列表导出")
public class TaskExcelVo {
    @ApiModelProperty("任务ID")
    @ExcelIgnore
    private Long taskId;

    @ApiModelProperty("任务名称")
    @ExcelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty("任务标签")
    @ExcelProperty(value = "任务标签")
    private String taskTag;

    @ApiModelProperty("任务类型")
    @ExcelIgnore
    private Integer taskType;
    @ExcelProperty(value = "任务形式")
    private String taskTypeStr;

    @ApiModelProperty("任务子类型")
    @ExcelIgnore
    private Integer taskSubType;
    @ExcelProperty(value = "任务类型")
    private String taskSubTypeStr;

    @ApiModelProperty("发布时间")
    @ExcelProperty(value = "交办日期")
    private String publishTime;

    @ApiModelProperty("办理时限")
    @ExcelProperty(value = "办理时限")
    private String deadline;

    @ApiModelProperty("主办")
    @ExcelProperty(value = "主办")
    private String mainAssignUser;

    @ApiModelProperty("协办")
    @ExcelProperty(value = "协办")
    private String assignUsers;

    @ApiModelProperty("优先级:1.低 2.中 3.高 4.极高")
    @ExcelIgnore
    private Integer priority;
    @ExcelProperty(value = "优先级")
    private String priorityStr;

    @ExcelProperty("紧急程度")
    private String urgencyLevel;

    @ApiModelProperty("状态")
    @ExcelProperty(value = "任务状态")
    private String status;

    @ApiModelProperty
    @ExcelProperty(value = "本周更新状态")
    private String weekRefreshStatus;

    @ApiModelProperty("创建人")
    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @ExcelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty("办理时限")
    @ExcelProperty(value = "预计完成时间")
    private String deadline1;

    @ApiModelProperty("完成时间")
    @ExcelProperty(value = "实际完成时间")
    private String finishTime;

    @ApiModelProperty("任务性质")
    @ExcelIgnore
    private Integer taskNature;
    @ExcelProperty(value = "任务性质")
    private String taskNatureStr;

}
