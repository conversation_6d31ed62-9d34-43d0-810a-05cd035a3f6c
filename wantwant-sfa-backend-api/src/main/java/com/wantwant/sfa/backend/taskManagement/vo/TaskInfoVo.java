package com.wantwant.sfa.backend.taskManagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/25/上午11:13
 */
@ApiModel("任务信息")
@Data
public class TaskInfoVo {
    @ApiModelProperty("全部任务")
    private int allTask;
    @ApiModelProperty("代办任务")
    private int processing;
    @ApiModelProperty("已办任务")
    private int solved;
    @ApiModelProperty("抄送任务")
    private int cc;
    @ApiModelProperty("到期任务")
    private int expire;
    @ApiModelProperty("逾期任务")
    private int delinquent;
    @ApiModelProperty("关注任务")
    private int follow;
    @ApiModelProperty("草稿")
    private int draft;
}
