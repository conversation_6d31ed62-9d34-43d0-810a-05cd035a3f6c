package com.wantwant.sfa.backend.taskManagement.vo;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.xmlbeans.impl.tool.Diff;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午5:18
 */
@Data
@ApiModel("操作记录")
public class TaskLogVo {

    @ApiModelProperty("logId")
    private Long logId;

    @ApiModelProperty("操作时间")
    private String processTime;

    @ApiModelProperty("处理描述")
    private String processDescription;

    @ApiModelProperty("操作对象")
    private String processObj;

    @ApiModelProperty("内容")
    private String message;

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;

    @ApiModelProperty("差异")
    private List<DiffVo> diffVos;

    @ApiModelProperty("预计完成时间")
    private String expectedFinishDate;

    @ApiModelProperty("指派类型(1.主办 2.协办 3.抄送)")
    private Integer assignType;

    @ApiModelProperty("回复数")
    private Integer replyCount;
}
