package com.wantwant.sfa.backend.taskManagement.vo;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午5:09
 */
@Data
@ApiModel("办理情况")
public class TaskSituationVo {
    @ApiModelProperty("部门(人)")
    private String userName;
    @ApiModelProperty("当前环节")
    private String currentStatus;
    @ApiModelProperty("开始距金")
    private Long elapsedDays;
    @ApiModelProperty("办理时限")
    private String deadline;
    @ApiModelProperty("预计完成时间")
    private String expectDeadline;
    @ApiModelProperty("办理情况")
    private String situation;
    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;
}
