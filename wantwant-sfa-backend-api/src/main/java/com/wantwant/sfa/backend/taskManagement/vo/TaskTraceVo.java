package com.wantwant.sfa.backend.taskManagement.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/16/上午11:24
 */
@ApiModel("任务跟踪VO")
@Data
public class TaskTraceVo {

    @ApiModelProperty("traceId")
    private Long traceId;

    @ApiModelProperty("任务类型(1.交办任务 2.个人任务 3.部门任务)")
    @Excel(name = "任务形式",replace = {"交办任务_1","部门任务_3"})
    private Integer taskType;

    @ApiModelProperty("部门名称")
    @Excel(name = "部门")
    private String deptName;

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("提交人")
    @Excel(name = "提交人")
    private String assignUserName;

    @ApiModelProperty("任务名称")
    @Excel(name = "任务名称")
    private String taskName;

    @ApiModelProperty("任务类型(1.开源 2.截流)")
    @Excel(name = "任务类型",replace = {"后勤_1","销售_2","造旺APP产品功能_3","SFA产品功能_4"})
    private Integer taskSubType;

    @ApiModelProperty("任务来源(1.常规 2.双周会 3.季度会议 4.月会 5.点对点交办 6.其他)")
    @Excel(name = "任务来源",replace = {"常规_1","双周会_2","季度会议_3","月会_4","点对点交办_5","其他_6"})
    private Integer taskSource;

    @ApiModelProperty("任务性质(1.长期 2.短期)")
    @Excel(name = "任务性质",replace = {"长期_1","短期_2"})
    private Integer taskNature;

    @ApiModelProperty("任务内容")
    @Excel(name = "任务内容")
    private String content;

    @ApiModelProperty("任务优先级：1.低 2.中 3.高 4.极高")
    @Excel(name = "任务优先级",replace = {"低_1","中_2","高_3","极高_4"})
    private Integer priority;

    @ApiModelProperty("紧急程度")
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    @ApiModelProperty("任务价值")
    @Excel(name = "任务价值")
    private String worth;

    @ApiModelProperty("发布时间")
    @Excel(name = "交办日期")
    private String publishTime;

    @ApiModelProperty("办理时限")
    @Excel(name = "办理时限")
    private String deadline;

    @ApiModelProperty("距今时间")
    @Excel(name = "交办距今（天）")
    private Long ago;

    @ApiModelProperty("提交进度时间")
    @Excel(name="提交进度时间")
    private String submitTime;

    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private String status;

    @ApiModelProperty("本周更新状态")
    @Excel(name = "本周更新状态")
    private String weekRefreshStatus;

    @ApiModelProperty("预计完成日期")
    @Excel(name = "预计完成日期")
    private String expectFinishDate;

    @ApiModelProperty("实际完成日期")
    @Excel(name = "实际完成日期")
    private String finishDate;

    @ApiModelProperty("当前任务描述")
    @Excel(name = "当前进度描述")
    private String situation;

    @ApiModelProperty("项目审核(0.未审核 1.审核通过 2.驳回)")
    @Excel(name = "项目审核",replace = {"未审核_0","审核通过_1","驳回_2"})
    private Integer auditStatus;

    @ApiModelProperty("审核意见")
    @Excel(name = "审核意见")
    private String auditComment;

    @ApiModelProperty("是否需要回复")
    @Excel(name = "是否需要回复",replace = {"不需要_0","需要_1"})
    private Integer requireCallback;

    @ApiModelProperty("回复内容")
    @Excel(name = "回复内容")
    private String callback;


    @ApiModelProperty("是否可审核")
    private boolean canAudit;

    @ApiModelProperty("是否可回复")
    private boolean canCallback;
}
