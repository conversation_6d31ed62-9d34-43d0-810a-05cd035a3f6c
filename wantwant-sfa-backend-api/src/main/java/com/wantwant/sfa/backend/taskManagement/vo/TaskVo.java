package com.wantwant.sfa.backend.taskManagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午3:22
 */
@Data
@ApiModel("任务列表信息")
public class TaskVo {
    @ApiModelProperty("任务ID")
    private Long taskId;
    @ApiModelProperty("任务标签")
    private String taskTag;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("任务性质")
    private Integer taskNature;
    @ApiModelProperty("任务类型")
    private Integer taskType;
    @ApiModelProperty("主办")
    private String mainAssignUser;
    @ApiModelProperty("协办")
    private String assignUsers;
    @ApiModelProperty("创建人")
    private String createUserName;
    @ApiModelProperty("任务ID")
    private Integer taskSubType;
    @ApiModelProperty("优先级:1.低 2.中 3.高 4.极高")
    private Integer priority;
    @ApiModelProperty("紧急程度")
    private String urgencyLevel;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("发布时间")
    private String publishTime;
    @ApiModelProperty
    private String weekRefreshStatus;
    @ApiModelProperty("办理时限")
    private String deadline;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("完成时间")
    private String finishTime;


    private boolean canPublish;

    private boolean canFinish;

    private boolean canDelete;

    private boolean canUrge;
}
