package com.wantwant.sfa.backend.thirdPlatform.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.thirdPlatform.request.FaceSimilarRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;



@Api(value = "FaceSimilarAPI",tags = "人脸相似度对比")
public interface FaceSimilarAPI {

    @ApiOperation(value = "人脸识别",httpMethod = "POST")
    @PostMapping("/face/similar")
    Response faceSimilar(@RequestBody @Validated FaceSimilarRequest similarRequest);

}
