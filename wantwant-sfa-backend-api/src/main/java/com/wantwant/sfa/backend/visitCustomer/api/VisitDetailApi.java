package com.wantwant.sfa.backend.visitCustomer.api;

//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.sun.org.apache.regexp.internal.RE;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.visitCustomer.request.InventoryCheckRequest;
import com.wantwant.sfa.backend.visitCustomer.request.VisitAuditRequest;
import com.wantwant.sfa.backend.visitCustomer.request.VisitDetailRequest;
import com.wantwant.sfa.backend.visitCustomer.vo.VisitDetailVo;
import com.wantwant.sfa.backend.visitCustomer.vo.VisitInventoryCheckVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.wantwant.commons.pagination.Page;


/**
 * @Description：拜访详情
 * @Author： chen
 * @Date 2022/5/20
 */
@ApiModel("拜访详情")
public interface VisitDetailApi {

    @ApiOperation(value = "拜访详情", notes = "", httpMethod = "POST")
    @PostMapping("/visitDetail")
    Response<VisitDetailVo> visitDetail(@RequestBody VisitDetailRequest request);

    @ApiOperation(value = "库存盘点", notes = "", httpMethod = "POST")
    @PostMapping("/inventoryCheck")
    Response<Page<VisitInventoryCheckVO>> inventoryCheck(@RequestBody InventoryCheckRequest request);

    @ApiOperation(value = "拜访稽核", notes = "拜访稽核", httpMethod = "POST")
    @PostMapping("/audit")
    Response visitAudit(@Validated @RequestBody  VisitAuditRequest request);

}
