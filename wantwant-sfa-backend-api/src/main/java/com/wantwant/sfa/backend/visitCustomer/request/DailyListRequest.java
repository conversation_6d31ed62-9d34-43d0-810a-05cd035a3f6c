package com.wantwant.sfa.backend.visitCustomer.request;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel(value = "日报拜访列表")
@ToString
public class DailyListRequest implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;

    @ApiModelProperty(value = "日期", hidden = true)
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate theDateFrom;

    @ApiModelProperty(value = "日期", hidden = true)
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate theDateTo;

    @ApiModelProperty(value = "直属下级员工表ID", hidden = true)
    private List<Integer> employeeInfoIdList;

}
