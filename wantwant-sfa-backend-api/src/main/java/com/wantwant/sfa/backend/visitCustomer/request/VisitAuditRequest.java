package com.wantwant.sfa.backend.visitCustomer.request;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@ApiModel("拜访详情稽核请求")
public class VisitAuditRequest {

    @ApiModelProperty("拜访标识")
    private  String visitId;

    @ApiModelProperty("稽核原因")
    @Length(max = 2000, message = "稽核原因超过最大长度2000")
    private String auditReason;

    @ApiModelProperty("0：正常 1：异常")
    @Min(value = 0, message = "审批类型传入不正确")
    @Max(value = 1, message = "审批类型传入不正确")
    private int auditStatus;

    @ApiModelProperty("审批人工号")
    private String person;
}
