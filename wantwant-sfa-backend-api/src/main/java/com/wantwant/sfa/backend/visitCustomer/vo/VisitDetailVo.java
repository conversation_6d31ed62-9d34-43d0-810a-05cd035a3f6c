package com.wantwant.sfa.backend.visitCustomer.vo;

import com.wantwant.sfa.backend.visitCustomer.vo.visitDetail.BusinessInfoVo;
import com.wantwant.sfa.backend.visitCustomer.vo.visitDetail.VisitCustomerInfoSupplementVo;
import com.wantwant.sfa.backend.visitCustomer.vo.visitDetail.VisitCustomerInfoVo;
import com.wantwant.sfa.backend.visitCustomer.vo.visitDetail.VisitInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description：拜访详情
 * @Author： chen
 * @Date 2022/5/20
 */
@ApiModel(value = "拜访详情数据")
@Data
public class VisitDetailVo {

    @ApiModelProperty("客户基本信息")
    private VisitCustomerInfoVo visitCustomerInfoVo;

    @ApiModelProperty("业务基本信息")
    private BusinessInfoVo businessInfoVo;

    @ApiModelProperty("拜访信息")
    private VisitInfoVo visitInfoVo;

    @ApiModelProperty("陈列核实")
    private List<VisitDisplayVO> displayList;

    @ApiModelProperty("基本信息补充")
    private VisitCustomerInfoSupplementVo supplementVo;
}
