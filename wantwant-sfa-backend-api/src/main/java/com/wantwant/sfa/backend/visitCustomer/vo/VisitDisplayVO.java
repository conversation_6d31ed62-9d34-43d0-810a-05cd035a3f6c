package com.wantwant.sfa.backend.visitCustomer.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VisitDisplayVO {

    @ApiModelProperty("陈列活动编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actId;

    @ApiModelProperty("陈列形式编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actDetailId;

    @ApiModelProperty("陈列编号")
    private Integer uploadId;

    @ApiModelProperty("陈列形式")
    private String actDetailName;

    @ApiModelProperty("陈列标准")
    private String displayStandard;

    @ApiModelProperty("陈列商品")
    private String displayCommodity;

    @ApiModelProperty("陈列具体要求")
    private String displayRequire;

    @ApiModelProperty("陈列位置")
    private String displayPosition;

    @ApiModelProperty("陈列排面数")
    private String displayRows;

    @ApiModelProperty("陈列近景照片")
    private String displayCloseImage;

    @ApiModelProperty("陈列核实照片")
    private String displayVerificationImage;

}
