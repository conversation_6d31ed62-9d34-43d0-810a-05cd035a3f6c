package com.wantwant.sfa.backend.visitCustomer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class VisitInfoForDailyVo {

    @ApiModelProperty(value = "拜访人-员工表id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "拜访编号")
    private Integer visitId;

    @ApiModelProperty(value = "拜访类型")
    private Integer visitType;

    @ApiModelProperty(value = "拜访类型")
    private String visitTypeStr;

    @ApiModelProperty(value = "客户类型")
    private String customerType;

    @ApiModelProperty(value = "客户类型")
    private String customerTypeStr;

    @ApiModelProperty(value = "开户类型")
    private Integer openType;

    @ApiModelProperty(value = "开户类型")
    private String openTypeStr;

    @ApiModelProperty(value = "客户Id")
    private String customerId;

    @ApiModelProperty(value = "客户或店铺名称")
    private String customerName;

    @ApiModelProperty(value = "建档时间")
    private LocalDateTime fillingTime;

    @ApiModelProperty(value = "拜访开始")
    private String startTime;

    @ApiModelProperty(value = "拜访结束")
    private String endTime;

    @ApiModelProperty(value = "停留时长")
    private String timeCost;

    @ApiModelProperty(value = "近三月业绩")
    private BigDecimal performanceInThreeMon;

}
