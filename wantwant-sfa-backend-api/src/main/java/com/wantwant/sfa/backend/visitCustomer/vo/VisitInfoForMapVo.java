package com.wantwant.sfa.backend.visitCustomer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class VisitInfoForMapVo {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户类型：0 - 终端、1 - 二批、2 - 经销商、3 - 其他")
    private Integer customerType;
    @ApiModelProperty(value = "开户类型：0 - 意向客户、1 - 潜在客户、2-陈列客户")
    private Integer openType;

    @ApiModelProperty("稽核状态 0-正常  1-异常")
    private Integer auditStatus;

    @ApiModelProperty("稽核原因")
    private String auditReason;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("拜访用时")
    private String timeCost;

    @ApiModelProperty("门店照片")
    private String storeImageUrl;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("是否出差")
    private Boolean isTrip;

    @ApiModelProperty(value = "员工表id")
    private Integer employeeInfoId;

}
