package com.wantwant.sfa.backend.visitCustomer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description： 库存盘点
 * @Author： chen
 * @Date 2022/5/24
 */
@ApiModel("拜访-库存盘点")
@Data
public class VisitInventoryCheckVO {

    @ApiModelProperty("")
    private String visitId;

    @ApiModelProperty("合伙人memberKey")
    private String partnerMemberKey;

    @ApiModelProperty("")
    private String customerId;

    @ApiModelProperty("")
    private String sku;

    @ApiModelProperty("产品名称")
    private String skuName;

    @ApiModelProperty("单位")
    private String singleUnit;

    @ApiModelProperty("箱数")
    private String boxNum;

    @ApiModelProperty("单位数量")
    private String singleUnitNum;

    @ApiModelProperty("sku图片url")
    private String skuImageUrl;

    @ApiModelProperty("数量(箱购)")
    private String quantity;




}
