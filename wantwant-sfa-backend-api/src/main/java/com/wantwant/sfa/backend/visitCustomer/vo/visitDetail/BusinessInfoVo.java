package com.wantwant.sfa.backend.visitCustomer.vo.visitDetail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description：业务员信息
 * @Author： chen
 * @Date 2022/5/20
 */
@Data
public class BusinessInfoVo {

    @ApiModelProperty(value = "业务姓名")
    private String partnerName;

    @ApiModelProperty(value = "业务手机号")
    private String partnerMobile;

    @ApiModelProperty(value = "业务入职时间")
    private String partnerOnboardTime;

    @ApiModelProperty("大区")
    private String regionOffice;

    @ApiModelProperty("分公司")
    private String branchOffice;

    @ApiModelProperty("营业所")
    private String departmentName;

//    @ApiModelProperty("职位类型：1，个人合伙人；2，企业合伙人")
//    private Integer postType;

    @ApiModelProperty(value = "负责市场")
    private String responsibleArea;

    @ApiModelProperty(value = "总业绩")
    String partnerTotalPerformance;

    @ApiModelProperty(value = "本月业绩")
    String partnerPerformance;

    @ApiModelProperty(value = "打卡经纬度  longitude(经度) latitude(纬度)")
    Map<String,String> clockGeoAddress;

    @ApiModelProperty(value = "拜访经纬度 longitude(经度) latitude(纬度)")
    List<Map<String,Object>> visitGeoAddress;

    @ApiModelProperty(value = "标识")
    private String memberKey;
}
