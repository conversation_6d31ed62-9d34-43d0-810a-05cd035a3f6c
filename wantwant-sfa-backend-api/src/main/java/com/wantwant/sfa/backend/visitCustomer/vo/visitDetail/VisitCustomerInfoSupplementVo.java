package com.wantwant.sfa.backend.visitCustomer.vo.visitDetail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VisitCustomerInfoSupplementVo {

    @ApiModelProperty("产品反馈")
    private String productFeedback;

    @ApiModelProperty("售后反馈")
    private String afterSalesFeedback;

    @ApiModelProperty("畅销竞品")
    private String bestsellingCompetitors;

    @ApiModelProperty("畅销原因")
    private String bestsellingReason;

    @ApiModelProperty("竞品照片")
    private String competitorImages;

    @ApiModelProperty("无意愿类型")
    private String unwillingnessType;

    @ApiModelProperty("无意愿原因")
    private String unwillingnessDesc;

    @ApiModelProperty("意向品项")
    private String intendedItemsName;

    @ApiModelProperty("意向金额")
    private String intendedAmount;

    @ApiModelProperty("预计时间")
    private String estimateTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty
    private String visitStatus;

}
