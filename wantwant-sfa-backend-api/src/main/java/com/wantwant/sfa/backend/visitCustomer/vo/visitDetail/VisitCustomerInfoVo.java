package com.wantwant.sfa.backend.visitCustomer.vo.visitDetail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：客户基本信息
 * @Author： chen
 * @Date 2022/5/20
 */
@Data
public class VisitCustomerInfoVo {

    @ApiModelProperty("客户或店铺名称")
    private String customerName;

    @ApiModelProperty(value = "开户类型：0 - 意向客户、1 - 潜在客户、2-陈列客户")
    private Integer openType;

    @ApiModelProperty(value = "客户类型：0-批发、1-终端")
    private Integer customerType;

    @ApiModelProperty("客户id")
    private	String customerId;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty(value = "成为客户时间，即建档客户去认证时间")
    private String fillingTime;

    @ApiModelProperty(value = "上次拜访时间")
    private String visitTime;

    @ApiModelProperty(value = "拜访类型 0客户 1合伙人 ")
    private int visitType;


}
