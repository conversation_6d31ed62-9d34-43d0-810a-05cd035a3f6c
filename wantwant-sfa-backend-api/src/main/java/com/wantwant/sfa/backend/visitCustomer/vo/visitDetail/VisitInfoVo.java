package com.wantwant.sfa.backend.visitCustomer.vo.visitDetail;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：拜访信息
 * @Author： chen
 * @Date 2022/5/20
 */
@Data
public class VisitInfoVo {

    @ApiModelProperty("拜访编号")
    private String visitId;

    @ApiModelProperty(value = "业务姓名")
    private String employeeName;

    @ApiModelProperty(value = "业务手机号")
    private String mobile;

    @ApiModelProperty(name="员工工号")
    private String employeeId;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

//    @ApiModelProperty("结束时间")
//    private String endTime;

    @ApiModelProperty("拜访用时")
    private String timeCost;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("门店照片")
    private String storeImageUrl;

    @ApiModelProperty("店内照片")
    private String storeInnerImageUrl;

    @ApiModelProperty("稽核状态 0-正常  1-异常")
    private Integer auditStatus;

    @ApiModelProperty("稽核原因")
    private String auditReason;

    @ApiModelProperty("稽核时间")
    private String auditTime;

    @ApiModelProperty("是否有造旺品项 0-无 1-有")
    private Integer isStoreItemsFlag;

    @ApiModelProperty("是否有合作意愿 0-暂无合作意愿/后续不再购买造旺产品 1-有合作意愿/会继续购买造旺产品")
    private Integer isCooperateInclination;
}
