package com.wantwant.sfa.backend.workCalendar.api;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.workCalendar.request.HolidaySearchRequest;
import com.wantwant.sfa.backend.workCalendar.vo.HolidayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/03/下午3:07
 */
@Api(value = "WorkCalendarApi", tags = "工作日历")
@RequestMapping("/workCalendar")
public interface WorkCalendarApi {


    @ApiOperation(value = "根据范围获取节假日", notes = "根据范围获取节假日", httpMethod = "POST")
    @PostMapping("/selectHolidayWithRange")
    Response<List<HolidayVo>> selectHolidayWithRange(@RequestBody HolidaySearchRequest holidaySearchRequest);
}
