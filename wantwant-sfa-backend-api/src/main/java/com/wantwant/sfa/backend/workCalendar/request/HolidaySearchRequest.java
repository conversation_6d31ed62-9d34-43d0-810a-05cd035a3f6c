package com.wantwant.sfa.backend.workCalendar.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/03/下午3:14
 */
@Data
@ApiModel("节假日查询request")
@ToString
public class HolidaySearchRequest {

    @ApiModelProperty("开始日期:yyyy-MM-dd")
    @NotBlank(message = "缺少开始日期")
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$",message = "日期格式错误")
    private String startDate;

    @ApiModelProperty("结束日期:yyyy-MM-dd")
    @NotBlank(message = "缺少结束日期")
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$",message = "日期格式错误")
    private String endDate;
}
