package com.wantwant.sfa.backend.yearFestival.api;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.yearFestival.request.YearFestivalRequest;
import com.wantwant.sfa.backend.yearFestival.vo.YearCustomerVo;
import com.wantwant.sfa.backend.yearFestival.vo.YearFestivalDetailVo;
import com.wantwant.sfa.backend.yearFestival.vo.YearProductOptions;
import com.wantwant.sfa.backend.yearFestival.vo.YearProductVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 年节API。
 * @Auther: zhangpengpeng
 * @Date: 2024/09/30
 */
@Api(value = "YearFestivalApi", tags = "年节API")
@RequestMapping("/year/festival")
public interface YearFestivalApi {
    @ApiOperation(value = "业绩总览", notes = "个人信息、业绩数据、每日&累计业绩趋势", httpMethod = "POST")
    @PostMapping("/detail")
    Response<YearFestivalDetailVo> yearFestivalDetail(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "业务数据明细-下拉模式", notes = "业务数据明细-下拉模式", httpMethod = "POST")
    @PostMapping("/queryPerformanceList")
    Response<List<YearFestivalDetailVo>> queryPerformanceList(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "业务数据明细-下拉模式-列表下载", notes = "业务数据明细-下拉模式-列表下载", httpMethod = "POST")
    @PostMapping(value = "/downLoadPerformanceList")
    void downLoadPerformanceList(@RequestBody @Validated YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "业务数据明细-平铺模式（分页）", notes = "业务数据明细-平铺模式（分页）", httpMethod = "POST")
    @PostMapping("/queryPerformancePage")
    Response<IPage<YearFestivalDetailVo>> queryPerformancePage(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "业务数据明细-平铺模式（分页）-列表下载", notes = "业务数据明细-平铺模式（分页）-列表下载", httpMethod = "POST")
    @PostMapping(value = "/downLoadPerformancePage")
    void downLoadPerformancePage(@RequestBody @Validated YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "产品列表", notes = "sku、sku、产品线", httpMethod = "POST")
    @PostMapping("/queryProductPage")
    Response<IPage<YearProductVo>> queryProductPage(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "产品列表-列表下载", notes = "产品列表-列表下载", httpMethod = "POST")
    @PostMapping(value = "/downLoadProductPage")
    void downLoadProductPage(@RequestBody @Validated YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "组织交易客户明细-下拉模式", notes = "组织交易客户明细-下拉模式", httpMethod = "POST")
    @PostMapping("/queryCustomerList")
    Response<List<YearCustomerVo>> queryCustomerList(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "组织交易客户明细-下拉模式（分页）", notes = "组织交易客户明细-下拉模式（分页）", httpMethod = "POST")
    @PostMapping("/queryCustomerListPage")
    Response<IPage<YearCustomerVo>> queryCustomerListPage(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "组织交易客户明细-下拉模式-列表下载", notes = "组织交易客户明细-下拉模式-列表下载", httpMethod = "POST")
    @PostMapping(value = "/downLoadCustomerList")
    void downLoadCustomerList(@RequestBody @Validated YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "组织交易客户明细-平铺模式（分页）", notes = "组织交易客户明细-平铺模式（分页）", httpMethod = "POST")
    @PostMapping("/queryCustomerPage")
    Response<IPage<YearCustomerVo>> queryCustomerPage(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "组织交易客户明细-平铺模式（分页）-列表下载", notes = "组织交易客户明细-平铺模式（分页）-列表下载", httpMethod = "POST")
    @PostMapping(value = "/downLoadCustomerPage")
    void downLoadCustomerPage(@RequestBody @Validated YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "产品交易客户明细列表", notes = "sku、sku、产品线", httpMethod = "POST")
    @PostMapping("/queryProductCustomerPage")
    Response<IPage<YearCustomerVo>> queryProductCustomerPage(@RequestBody @Validated YearFestivalRequest request);

    @ApiOperation(value = "产品交易客户明细列表-列表下载", notes = "产品交易客户明细列表-列表下载", httpMethod = "POST")
    @PostMapping(value = "/downLoadProductCustomerPage")
    void downLoadProductCustomerPage(@RequestBody @Validated YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res);

    @ApiOperation(value = "产品下拉选项", notes = "产品下拉选项", httpMethod = "POST")
    @PostMapping("/productOptions")
    Response<List<YearProductOptions>> productOptions(@RequestBody @Validated YearFestivalRequest request);

}
