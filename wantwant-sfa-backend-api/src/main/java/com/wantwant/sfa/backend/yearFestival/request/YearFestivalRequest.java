package com.wantwant.sfa.backend.yearFestival.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "年节传参")
public class YearFestivalRequest extends PageParam {
    /* 操作人相关字段 */
    @NotBlank(message = "操作人工号不能为空")
    @ApiModelProperty(value = "操作人工号", required = true)
    private String person;
    @ApiModelProperty(value = "业务组", hidden = true)
    private Integer personBusinessGroup;
    @ApiModelProperty(value = "组织类型", hidden = true)
    private String personOrganizationType;

    /* 配置相关字段 */
    @ApiModelProperty(value = "年节字段小数位", hidden = true)
    private Integer yearFestivalScale;
    @ApiModelProperty(value = "下载类型（0业务明细数据，1天数倒数数据）")
    private Integer downLoadType;
    @ApiModelProperty(value = "产品下拉选项类型（0产品，1产品交易客户）")
    private Integer productOptionsType;

    /* 排序通用字段 */
    @ApiModelProperty(value = "排序名称")
    private String sortName;
    @ApiModelProperty(value = "排序类型（desc.倒序 asc.正序）")
    private String sortType;

    /* 查询通用字段 */
    @ApiModelProperty("筛选的组织id")
    private List<String> chooseOrganizationIds;
    @ApiModelProperty(value = "查询类型:1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人")
    private Integer searchType;
    @ApiModelProperty(value = "是否查自己（下拉模式专用）")
    private Integer isNextRealtime;
    @ApiModelProperty(value = "sku/spu/line")
    private String productType;
    @ApiModelProperty(value = "产品筛选")
    private List<String> productIds;
    @ApiModelProperty("客户类型：收货客户、建档客户、合伙人")
    private String customerType;

    /* 目标人员相关字段 */
    @ApiModelProperty(value = "目标人员组织id")
    private String organizationId;
    @ApiModelProperty(value = "目标人员的直属下级组织ids")
    private List<String> childOrganizationIds;
    @ApiModelProperty(value = "目标人员岗位类型", hidden = true)
    private Integer positionTypeId;
    @ApiModelProperty(value = "目标人员工号", hidden = true)
    private String employeeId;
    @ApiModelProperty(value = "目标人员memberKey")
    private Long memberKey;

    /* 业务参数字段 */
    @NotNull(message = "年节不能为空")
    @ApiModelProperty(value = "年节", required = true)
    private Integer theYear;
    @ApiModelProperty(value = "去年年节", hidden = true)
    private Integer lastYear;
    @ApiModelProperty(value = "年月", hidden = true)
    private String yearMonth;

    @ApiModelProperty("客户")
    private String customerInfo;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("1本期客户,2同期客户")
    private Integer customerDescription;
    @ApiModelProperty("建档客户(建档客户为客户id，自屯客户为MK)")
    private String customerId;

}
