package com.wantwant.sfa.backend.yearFestival.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.coverter.LocalDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel("业绩数据明细")
public class YearFestivalDetailVo {

    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;
    @ApiModelProperty("业务组名称")
    @ExcelProperty(value = "产品组")
    private String businessGroupName;

    @ExcelIgnore
    private String areaCode;
    @ExcelIgnore
    private String vareaCode;
    @ExcelIgnore
    private String provinceCode;
    @ExcelIgnore
    private String companyCode;
    @ExcelIgnore
    private String departmentCode;

    @ApiModelProperty("组织Id-全称")
    @ExcelIgnore
    private String fullOrganizationId;

    @ApiModelProperty("组织信息")
    @ExcelIgnore
    private List<String> orgPath;

    @ApiModelProperty("战区名称")
    @ExcelProperty(value = "战区")
    private String areaName;
    @ApiModelProperty("大区名称")
    @ExcelProperty(value = "大区")
    private String vareaName;
    @ApiModelProperty("省区名称")
    @ExcelProperty(value = "省区")
    private String provinceName;
    @ApiModelProperty("分公司名称")
    @ExcelProperty(value = "分公司")
    private String companyName;
    @ApiModelProperty("区域经理层名称")
    @ExcelProperty(value = "营业所")
    private String departmentName;

    @ApiModelProperty("成员数量")
    @ExcelProperty(value = "成员数量")
    private String memberCount;

    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Long positionTypeId;
    @ApiModelProperty(value = "岗位类型名称")
    @ExcelProperty(value = "岗位")
    private String positionTypeName;
    @ApiModelProperty(value = "查询类型:1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人")
    @ExcelIgnore
    private Integer searchType;

    @ApiModelProperty("姓名")
    @ExcelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "人员状态")
    @ExcelProperty(value = "人员状态")
    private String employeeStatus;

    @ApiModelProperty("能否跳转作业轨迹")
    @ExcelIgnore
    private Boolean canMap;

    @ApiModelProperty("头像")
    @ExcelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty("能否跳转")
    @ExcelIgnore
    private Boolean canLink;
    @ApiModelProperty(value = "性别（0.未知 1.男 2.女）")
    @ExcelIgnore
    private Integer gender;
    @ApiModelProperty(value = "入职日期")
    @ExcelProperty(value = "入职日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    private LocalDate onboardTime;
    @ApiModelProperty(value = "入职天数")
    @ExcelIgnore
    private Integer onboardDays;
    @ApiModelProperty(value = "离职日期")
    @ExcelProperty(value = "离职日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    private LocalDate dischargeDate;

    @ApiModelProperty(value = "简历")
    @ExcelIgnore
    private String resumeUrl;

    @ApiModelProperty("系统类型")
    @ExcelIgnore
    private String systemType;
    @ApiModelProperty("系统名称")
    @ExcelIgnore
    private String systemName;

    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;
    @ApiModelProperty("组织名称")
    @ExcelIgnore
    private String organizationName;
    @ApiModelProperty("组织名称-全称")
    @ExcelIgnore
    private String fullOrganizationName;

    @ApiModelProperty(value = "员工号")
    @ExcelIgnore
    private String employeeId;
    @ApiModelProperty(value = "员工表id")
    @ExcelIgnore
    private Integer employeeInfoId;
    @ApiModelProperty("memberKey")
    @ExcelIgnore
    private Long memberKey;

    @ApiModelProperty("客户子类")
    @ExcelIgnore
    private String typeName;

    @ApiModelProperty(value = "是否能下拉")
    @ExcelIgnore
    private Integer isNextRealtime;

    @ApiModelProperty(value = "年节")
    @ExcelIgnore
    private Integer theYear;

    @ApiModelProperty(value = "当前产品组业绩/年节累计业绩")
    @ExcelProperty(value = "年节累计业绩")
    private BigDecimal annualItemsSupplyTotalCur;

    @ApiModelProperty(value = "旺金币业绩")
    @ExcelProperty(value = "旺金币业绩")
    private BigDecimal annualFreeTotalAmount;

    @ApiModelProperty(value = "旺金币折扣率")
    @ExcelProperty(value = "旺金币折扣率")
    private BigDecimal annualFreeTotalAmountDiscountRate;

    @ApiModelProperty(value = "年节目标")
    @ExcelProperty(value = "年节目标")
    private BigDecimal annualItemsSupplyTotalGoals;

    @ApiModelProperty(value = "目标达成率")
    @ExcelProperty(value = "目标达成率")
    private BigDecimal annualItemsSupplyTotalAchievementRate;
    @ApiModelProperty(value = "目标达成率颜色")
    @ExcelIgnore
    private String annualItemsSupplyTotalAchievementColor;

    @ApiModelProperty(value = "同期业绩")
    @ExcelProperty(value = "同期业绩")
    private BigDecimal annualItemsSupplyTotalCurLy;

    @ApiModelProperty(value = "业绩同比（按时间进度）")
    @ExcelProperty(value = "业绩同比（时间进度）")
    private BigDecimal annualItemsSupplyTotalCurYoyTime;

    @ApiModelProperty(value = "业绩同比（整个年节）")
    @ExcelProperty(value = "业绩同比（整个年节）")
    private BigDecimal annualItemsSupplyTotalCurYoyAll;

    @ApiModelProperty(value = "业绩差异")
    @ExcelProperty(value = "业绩差异")
    private BigDecimal annualItemsSupplyTotalCurDifference;

    @ApiModelProperty(value = "累计业绩趋势")
    @ExcelIgnore
    private List<YearPerformanceSummaryTrendsVo> cumulativePerformance;

    @ApiModelProperty(value = "每日业绩趋势")
    @ExcelIgnore
    private List<YearPerformanceSummaryTrendsVo> dailyPerformance;

    @ApiModelProperty(value = "同期客户业绩差异")
    @ExcelProperty(value = "同期客户业绩差异")
    private BigDecimal annualItemsSupplyTotalCustomDifference;

    @ApiModelProperty(value = "新增客户业绩差异")
    @ExcelProperty(value = "新增客户业绩差异")
    private BigDecimal annualItemsSupplyTotalNewCustomDifference;

    @ApiModelProperty(value = "预储值余额")
    @ExcelProperty(value = "预储值余额")
    private BigDecimal annualStoredValueBalance;

    @ApiModelProperty(value = "预订单业绩")
    @ExcelProperty(value = "预订单业绩")
    private BigDecimal annualAdvancedOrderItemsSupplyTotal;

    @ApiModelProperty(value = "预订单未发货业绩")
    @ExcelProperty(value = "预订单未发货业绩")
    private BigDecimal annualAdvancedOrderUnshippedItemsSupplyTotal;

    @ApiModelProperty(value = "本期管理岗在职人数")
    @ExcelProperty(value = "本期管理岗在职人数")
    private BigDecimal annualMangementNums;

    @ApiModelProperty(value = "同期管理岗在职人数")
    @ExcelProperty(value = "同期管理岗在职人数")
    private BigDecimal annualMangementNumsLy;

    @ApiModelProperty(value = "管理岗在职人数同比")
    @ExcelProperty(value = "管理岗在职人数同比")
    private BigDecimal annualMangementNumsYoyAll;

    @ApiModelProperty(value = "本期管理岗人均业绩（当前）")
    @ExcelProperty(value = "本期管理岗人均业绩")
    private BigDecimal mangementCapitaItemsSupplyTotal;

    @ApiModelProperty(value = "同期管理岗人均业绩（当前）")
    @ExcelProperty(value = "同期管理岗人均业绩")
    private BigDecimal mangementCapitaItemsSupplyTotalLy;

    @ApiModelProperty(value = "管理岗人均业绩同比（按时间进度）")
    @ExcelProperty(value = "管理岗人均业绩同比（时间进度）")
    private BigDecimal mangementCapitaItemsSupplyTotalYoyTime;

    @ApiModelProperty(value = "管理岗人均业绩同比（整个年节）")
    @ExcelProperty(value = "管理岗人均业绩同比（整个年节）")
    private BigDecimal mangementCapitaItemsSupplyTotalYoyAll;

    @ApiModelProperty(value = "本期交易客户数")
    @ExcelProperty(value = "本期交易客户数")
    private BigDecimal annualTradeCustomer;

    @ApiModelProperty(value = "同期交易客户数")
    @ExcelProperty(value = "同期交易客户数")
    private BigDecimal annualTradeCustomerLy;

    @ApiModelProperty(value = "交易客户数同比（按时间进度）")
    @ExcelProperty(value = "交易客户数同比（时间进度）")
    private BigDecimal annualTradeCustomerYoyTime;

    @ApiModelProperty(value = "交易客户数同比（整个年节）")
    @ExcelProperty(value = "交易客户数同比（整个年节）")
    private BigDecimal annualTradeCustomerYoyAll;

    @ApiModelProperty(value = "交易客户数差异")
    @ExcelProperty(value = "交易客户数差异")
    private BigDecimal annualTradeCustomerDifference;

    @ApiModelProperty(value = "本期客单价")
    @ExcelProperty(value = "本期客单价")
    private BigDecimal annualUnitPrice;

    @ApiModelProperty(value = "同期客单价")
    @ExcelProperty(value = "同期客单价")
    private BigDecimal annualUnitPriceLy;

    @ApiModelProperty(value = "客单价同比（按时间进度）")
    @ExcelProperty(value = "客单价同比（时间进度）")
    private BigDecimal annualUnitPriceYoyTime;

    @ApiModelProperty(value = "客单价同比（整个年节）")
    @ExcelProperty(value = "客单价同比（整个年节）")
    private BigDecimal annualUnitPriceYoyAll;

    @ApiModelProperty(value = "客单价差异")
    @ExcelProperty(value = "客单价差异")
    private BigDecimal annualUnitPriceDifference;

    @ApiModelProperty(value = "m1年节业绩")
    @ExcelProperty(value = "m1年节业绩")
    private BigDecimal annualItemsSupplyTotalM1;

    @ApiModelProperty(value = "m1同期业绩")
    @ExcelProperty(value = "m1同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM1;

    @ApiModelProperty(value = "m1年节业绩同比")
    @ExcelProperty(value = "m1年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM1;

    @ApiModelProperty(value = "m1年节差异")
    @ExcelProperty(value = "m1年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM1;

    @ApiModelProperty(value = "m2年节业绩")
    @ExcelProperty(value = "m2年节业绩")
    private BigDecimal annualItemsSupplyTotalM2;

    @ApiModelProperty(value = "m2同期业绩")
    @ExcelProperty(value = "m2同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM2;

    @ApiModelProperty(value = "m2年节业绩同比")
    @ExcelProperty(value = "m2年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM2;

    @ApiModelProperty(value = "m2年节差异")
    @ExcelProperty(value = "m2年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM2;

    @ApiModelProperty(value = "m3年节业绩")
    @ExcelProperty(value = "m3年节业绩" )
    private BigDecimal annualItemsSupplyTotalM3;

    @ApiModelProperty(value = "m3同期业绩")
    @ExcelProperty(value = "m3同期业绩" )
    private BigDecimal annualItemsSupplyTotalLyM3;

    @ApiModelProperty(value = "m3年节业绩同比")
    @ExcelProperty(value = "m3年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM3;

    @ApiModelProperty(value = "m3年节差异")
    @ExcelProperty(value = "m3年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM3;

    @ApiModelProperty(value = "m4年节业绩")
    @ExcelProperty(value = "m4年节业绩")
    private BigDecimal annualItemsSupplyTotalM4;

    @ApiModelProperty(value = "m4同期业绩")
    @ExcelProperty(value = "m4同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM4;

    @ApiModelProperty(value = "m4年节业绩同比")
    @ExcelProperty(value = "m4年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM4;

    @ApiModelProperty(value = "m4年节差异")
    @ExcelProperty(value = "m4年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM4;

}
