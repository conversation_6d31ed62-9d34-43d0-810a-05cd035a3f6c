package com.wantwant.sfa.backend.yearFestival.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "年节业绩趋势")
@Data
public class YearPerformanceSummaryTrendsVo {
    @ApiModelProperty(value = "实际日期")
    private String theDate;

    @ApiModelProperty(value = "编号日期")
    private String numberingDate;

    @ApiModelProperty(value = "年节目标")
    private BigDecimal annualItemsSupplyTotalGoals;

    @ApiModelProperty(value = "本期累计年节业绩")
    private BigDecimal annualItemsSupplyTotalCurCumulative;

    @ApiModelProperty(value = "同期累计年节业绩")
    private BigDecimal annualItemsSupplyTotalCurCumulativeLy;

    @ApiModelProperty(value = "累计差异")
    private BigDecimal annualItemsSupplyTotalCurDifference;

    @ApiModelProperty(value = "累计同比")
    private BigDecimal annualItemsSupplyTotalCurCumulativeYoy;

    @ApiModelProperty(value = "本期每日业绩")
    private BigDecimal annualItemsSupplyTotalCur;

    @ApiModelProperty(value = "同期每日业绩")
    private BigDecimal annualItemsSupplyTotalCurLast;

}
