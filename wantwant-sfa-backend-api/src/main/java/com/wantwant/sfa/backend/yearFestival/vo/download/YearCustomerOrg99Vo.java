package com.wantwant.sfa.backend.yearFestival.vo.download;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("年节交易客户明细")
public class YearCustomerOrg99Vo {

    @ExcelIgnore
    private String areaCode;
    @ExcelIgnore
    private String vareaCode;
    @ExcelIgnore
    private String provinceCode;
    @ExcelIgnore
    private String companyCode;
    @ExcelIgnore
    private String departmentCode;

    @ApiModelProperty("战区名称")
    @ExcelProperty(value = "战区")
    private String areaName;
    @ApiModelProperty("大区名称")
    @ExcelProperty(value = "大区")
    private String vareaName;
    @ApiModelProperty("省区名称")
    @ExcelProperty(value = "省区")
    private String provinceName;
    @ApiModelProperty("分公司名称")
    @ExcelProperty(value = "分公司")
    private String companyName;
    @ApiModelProperty("区域经理层名称")
    @ExcelProperty(value = "营业所")
    private String departmentName;


    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;
    @ApiModelProperty("组织名称")
    @ExcelIgnore
    private String organizationName;
    @ApiModelProperty("组织名称-全称")
    @ExcelIgnore
    private String fullOrganizationName;

    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Long positionTypeId;
    @ApiModelProperty(value = "岗位类型名称")
    @ExcelIgnore
    private String positionTypeName;

    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;
    @ExcelIgnore
    @ApiModelProperty("业务组名称")
    private String businessGroupName;

    @ApiModelProperty(value = "员工号")
    @ExcelIgnore
    private String employeeId;
    @ApiModelProperty(value = "员工表id")
    @ExcelIgnore
    private Integer employeeInfoId;

    @ApiModelProperty("是否本期客户")
    @ExcelIgnore
    private Boolean isCurrentCustomer;
    @ApiModelProperty("是否同期客户")
    @ExcelIgnore
    private Boolean isPastCustomer;
    @ApiModelProperty("客户类型")
    @ExcelProperty(value = "客户类型")
    private String customerType;

    @ApiModelProperty("memberKey")
    @ExcelProperty(value = "memberKey")
    private Long memberKey;
    @ApiModelProperty("建档客户(建档客户为客户id，自屯客户为MK)")
    @ExcelProperty(value = "建档客户ID")
    private String customerId;
    @ApiModelProperty("收货客户手机号")
    @ExcelProperty(value = "收货客户手机号")
    private String receiverMobileNumber;
    @ApiModelProperty("姓名")
    @ExcelProperty(value = "姓名")
    private String customerName;
    @ApiModelProperty("手机号")
    @ExcelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty("省")
    @ExcelProperty(value = "省")
    private String province;
    @ApiModelProperty("市")
    @ExcelProperty(value = "市")
    private String city;
    @ApiModelProperty("区")
    @ExcelProperty(value = "区")
    private String district;
    @ApiModelProperty("地址")
    @ExcelProperty(value = "地址")
    private String detailAddress;
    @ApiModelProperty("最近一次下单时间")
    @ExcelProperty(value = "最近一次下单时间")
    private String lastOrderTime;
    @ApiModelProperty("距离上一次下单天数")
    @ExcelProperty(value = "距离上一次下单天数")
    private Integer lastOrderIntervalDays;

    @ApiModelProperty(value = "是否能下拉")
    @ExcelIgnore
    private Integer isNextRealtime;

    @ApiModelProperty(value = "年节")
    @ExcelIgnore
    private Integer theYear;

    @ApiModelProperty(value = "旺金币业绩")
    @ExcelProperty(value = "旺金币业绩")
    private BigDecimal annualFreeTotalAmount;

    @ApiModelProperty(value = "旺金币折扣率")
    @ExcelProperty(value = "旺金币折扣率")
    private BigDecimal annualFreeTotalAmountDiscountRate;

    @ApiModelProperty(value = "本期业绩")
    @ExcelProperty(value = "年节累计业绩")
    private BigDecimal annualItemsSupplyTotalCur;

    @ApiModelProperty(value = "同期业绩")
    @ExcelProperty(value = "同期业绩")
    private BigDecimal annualItemsSupplyTotalCurLy;

    @ApiModelProperty(value = "业绩同比（按时间进度）")
    @ExcelProperty(value = "业绩同比（时间进度）")
    private BigDecimal annualItemsSupplyTotalCurYoyTime;

    @ApiModelProperty(value = " 业绩差异")
    @ExcelProperty(value = "业绩差异")
    private BigDecimal annualItemsSupplyTotalCurDifference;

    @ApiModelProperty(value = "m1年节业绩")
    @ExcelProperty(value = "m1年节业绩")
    private BigDecimal annualItemsSupplyTotalM1;

    @ApiModelProperty(value = "m1同期业绩")
    @ExcelProperty(value = "m1同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM1;

    @ApiModelProperty(value = "m1年节业绩同比")
    @ExcelProperty(value = "m1年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM1;

    @ApiModelProperty(value = "m1年节差异")
    @ExcelProperty(value = "m1年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM1;

    @ApiModelProperty(value = "m2年节业绩")
    @ExcelProperty(value = "m2年节业绩")
    private BigDecimal annualItemsSupplyTotalM2;

    @ApiModelProperty(value = "m2同期业绩")
    @ExcelProperty(value = "m2同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM2;

    @ApiModelProperty(value = "m2年节业绩同比")
    @ExcelProperty(value = "m2年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM2;

    @ApiModelProperty(value = "m2年节差异")
    @ExcelProperty(value = "m2年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM2;

    @ApiModelProperty(value = "m3年节业绩")
    @ExcelProperty(value = "m3年节业绩" )
    private BigDecimal annualItemsSupplyTotalM3;

    @ApiModelProperty(value = "m3同期业绩")
    @ExcelProperty(value = "m3同期业绩" )
    private BigDecimal annualItemsSupplyTotalLyM3;

    @ApiModelProperty(value = "m3年节业绩同比")
    @ExcelProperty(value = "m3年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM3;

    @ApiModelProperty(value = "m3年节差异")
    @ExcelProperty(value = "m3年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM3;

    @ApiModelProperty(value = "m4年节业绩")
    @ExcelProperty(value = "m4年节业绩")
    private BigDecimal annualItemsSupplyTotalM4;

    @ApiModelProperty(value = "m4同期业绩")
    @ExcelProperty(value = "m4同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM4;

    @ApiModelProperty(value = "m4年节业绩同比")
    @ExcelProperty(value = "m4年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM4;

    @ApiModelProperty(value = "m4年节差异")
    @ExcelProperty(value = "m4年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM4;

    @ApiModelProperty(value = " 造旺常态组本期业绩")
    @ExcelProperty(value = "造旺常态组本期业绩")
    private BigDecimal annualItemsSupplyTotalNormal;

    @ApiModelProperty(value = " 造旺常态组同期业绩")
    @ExcelProperty(value = "造旺常态组同期业绩")
    private BigDecimal annualItemsSupplyTotalNormalLy;

    @ApiModelProperty(value = " 造旺常态组业绩同比")
    @ExcelProperty(value = "造旺常态组业绩同比")
    private BigDecimal annualItemsSupplyTotalNormalYoy;

    @ApiModelProperty(value = " 造旺常态组业绩差异")
    @ExcelProperty(value = "造旺常态组业绩差异")
    private BigDecimal annualItemsSupplyTotalNormalDifference;

    @ApiModelProperty(value = " 造旺冲刺组本期业绩")
    @ExcelProperty(value = "造旺冲刺组本期业绩")
    private BigDecimal annualItemsSupplyTotalSprint;

    @ApiModelProperty(value = " 造旺冲刺组同期业绩")
    @ExcelProperty(value = "造旺冲刺组同期业绩")
    private BigDecimal annualItemsSupplyTotalSprintLy;

    @ApiModelProperty(value = " 造旺冲刺组业绩同比")
    @ExcelProperty(value = "造旺冲刺组业绩同比")
    private BigDecimal annualItemsSupplyTotalSprintYoy;

    @ApiModelProperty(value = " 造旺冲刺组业绩差异")
    @ExcelProperty(value = "造旺冲刺组业绩差异")
    private BigDecimal annualItemsSupplyTotalSprintDifference;

    @ApiModelProperty(value = " 零食直营组本期业绩")
    @ExcelProperty(value = "零食直营组本期业绩")
    private BigDecimal annualStoredValueSnacksDirect;

    @ApiModelProperty(value = " 零食直营组同期业绩")
    @ExcelProperty(value = "零食直营组同期业绩")
    private BigDecimal annualStoredValueSnacksDirectLy;

    @ApiModelProperty(value = " 零食直营组业绩同比")
    @ExcelProperty(value = "零食直营组业绩同比")
    private BigDecimal annualStoredValueSnacksDirectYoy;

    @ApiModelProperty(value = " 零食直营组业绩差异")
    @ExcelProperty(value = "零食直营组业绩差异")
    private BigDecimal annualStoredValueSnacksDirectDifference;

    @ApiModelProperty(value = " 造旺电销组本期业绩")
    @ExcelProperty(value = "造旺电销组本期业绩")
    private BigDecimal annualStoredValueTelemarketing;

    @ApiModelProperty(value = " 造旺电销组同期业绩")
    @ExcelProperty(value = "造旺电销组同期业绩")
    private BigDecimal annualStoredValueTelemarketingLy;

    @ApiModelProperty(value = " 造旺电销组业绩同比")
    @ExcelProperty(value = "造旺电销组业绩同比")
    private BigDecimal annualStoredValueTelemarketingYoy;

    @ApiModelProperty(value = " 造旺电销组业绩差异")
    @ExcelProperty(value = "造旺电销组业绩差异")
    private BigDecimal annualStoredValueTelemarketingDifference;

}
