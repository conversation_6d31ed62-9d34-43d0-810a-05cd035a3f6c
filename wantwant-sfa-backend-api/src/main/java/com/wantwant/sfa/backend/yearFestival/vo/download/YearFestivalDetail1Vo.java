package com.wantwant.sfa.backend.yearFestival.vo.download;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.coverter.LocalDateConverter;
import com.wantwant.sfa.backend.yearFestival.vo.YearPerformanceSummaryTrendsVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel("业绩数据明细")
public class YearFestivalDetail1Vo {

    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;
    @ApiModelProperty("业务组名称")
    @ExcelProperty(value = "产品组")
    private String businessGroupName;

    @ExcelIgnore
    private String areaCode;
    @ExcelIgnore
    private String vareaCode;
    @ExcelIgnore
    private String provinceCode;
    @ExcelIgnore
    private String companyCode;
    @ExcelIgnore
    private String departmentCode;

    @ApiModelProperty("战区名称")
    @ExcelProperty(value = "战区")
    private String areaName;
    @ApiModelProperty("大区名称")
    @ExcelProperty(value = "大区")
    private String vareaName;
    @ApiModelProperty("省区名称")
    @ExcelProperty(value = "省区")
    private String provinceName;
    @ApiModelProperty("分公司名称")
    @ExcelProperty(value = "分公司")
    private String companyName;
    @ApiModelProperty("区域经理层名称")
    @ExcelProperty(value = "营业所")
    private String departmentName;

    @ApiModelProperty("成员数量")
    @ExcelProperty(value = "成员数量")
    private String memberCount;

    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Long positionTypeId;
    @ApiModelProperty(value = "岗位类型名称")
    @ExcelProperty(value = "岗位")
    private String positionTypeName;

    @ApiModelProperty("姓名")
    @ExcelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "人员状态")
    @ExcelProperty(value = "人员状态")
    private String employeeStatus;

    @ApiModelProperty("头像")
    @ExcelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty("能否跳转")
    @ExcelIgnore
    private Boolean canLink;
    @ApiModelProperty(value = "性别（0.未知 1.男 2.女）")
    @ExcelIgnore
    private Integer gender;
    @ApiModelProperty(value = "入职日期")
    @ExcelProperty(value = "入职日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    private LocalDate onboardTime;
    @ApiModelProperty(value = "入职天数")
    @ExcelIgnore
    private Integer onboardDays;
    @ApiModelProperty(value = "离职日期")
    @ExcelProperty(value = "离职日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    private LocalDate dischargeDate;

    @ApiModelProperty(value = "简历")
    @ExcelIgnore
    private String resumeUrl;

    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;
    @ApiModelProperty("组织名称")
    @ExcelIgnore
    private String organizationName;
    @ApiModelProperty("组织名称-全称")
    @ExcelIgnore
    private String fullOrganizationName;

    @ApiModelProperty(value = "员工号")
    @ExcelIgnore
    private String employeeId;
    @ApiModelProperty(value = "员工表id")
    @ExcelIgnore
    private Integer employeeInfoId;
    @ApiModelProperty("memberKey")
    @ExcelIgnore
    private Long memberKey;

    @ApiModelProperty("客户子类")
    @ExcelIgnore
    private String typeName;

    @ApiModelProperty(value = "是否能下拉")
    @ExcelIgnore
    private Integer isNextRealtime;

    @ApiModelProperty(value = "年节")
    @ExcelIgnore
    private Integer theYear;

    @ApiModelProperty(value = "m1年节业绩")
    @ExcelProperty(value = "m1年节业绩")
    private BigDecimal annualItemsSupplyTotalM1;

    @ApiModelProperty(value = "m1同期业绩")
    @ExcelProperty(value = "m1同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM1;

    @ApiModelProperty(value = "m1年节业绩同比")
    @ExcelProperty(value = "m1年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM1;

    @ApiModelProperty(value = "m1年节差异")
    @ExcelProperty(value = "m1年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM1;

    @ApiModelProperty(value = "m2年节业绩")
    @ExcelProperty(value = "m2年节业绩")
    private BigDecimal annualItemsSupplyTotalM2;

    @ApiModelProperty(value = "m2同期业绩")
    @ExcelProperty(value = "m2同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM2;

    @ApiModelProperty(value = "m2年节业绩同比")
    @ExcelProperty(value = "m2年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM2;

    @ApiModelProperty(value = "m2年节差异")
    @ExcelProperty(value = "m2年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM2;

    @ApiModelProperty(value = "m3年节业绩")
    @ExcelProperty(value = "m3年节业绩" )
    private BigDecimal annualItemsSupplyTotalM3;

    @ApiModelProperty(value = "m3同期业绩")
    @ExcelProperty(value = "m3同期业绩" )
    private BigDecimal annualItemsSupplyTotalLyM3;

    @ApiModelProperty(value = "m3年节业绩同比")
    @ExcelProperty(value = "m3年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM3;

    @ApiModelProperty(value = "m3年节差异")
    @ExcelProperty(value = "m3年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM3;

    @ApiModelProperty(value = "m4年节业绩")
    @ExcelProperty(value = "m4年节业绩")
    private BigDecimal annualItemsSupplyTotalM4;

    @ApiModelProperty(value = "m4同期业绩")
    @ExcelProperty(value = "m4同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM4;

    @ApiModelProperty(value = "m4年节业绩同比")
    @ExcelProperty(value = "m4年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM4;

    @ApiModelProperty(value = "m4年节差异")
    @ExcelProperty(value = "m4年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM4;

}
