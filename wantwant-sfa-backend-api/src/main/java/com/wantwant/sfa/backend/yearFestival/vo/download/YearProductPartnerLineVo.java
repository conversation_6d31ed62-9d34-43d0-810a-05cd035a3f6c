package com.wantwant.sfa.backend.yearFestival.vo.download;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("产品列表")
public class YearProductPartnerLineVo {

    @ApiModelProperty("sku/spu/line id")
    @ExcelIgnore
    private String productId;

    @ApiModelProperty("生产线名称")
    @ExcelProperty(value = "线别")
    private String lineName;

    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;
    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Long positionTypeId;
    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;

    @ApiModelProperty(value = "年节")
    @ExcelIgnore
    private Integer theYear;

    @ApiModelProperty(value = "当前产品组业绩/年节累计业绩")
    @ExcelProperty(value = "年节累计业绩")
    private BigDecimal annualItemsSupplyTotalCur;

    @ApiModelProperty(value = "同期业绩")
    @ExcelProperty(value = "同期业绩")
    private BigDecimal annualItemsSupplyTotalCurLy;

    @ApiModelProperty(value = "业绩同比（整个年节）")
    @ExcelProperty(value = "业绩同比（整个年节）")
    private BigDecimal annualItemsSupplyTotalCurYoyAll;

    @ApiModelProperty(value = "业绩差异")
    @ExcelProperty(value = "业绩差异")
    private BigDecimal annualItemsSupplyTotalCurDifference;

    @ApiModelProperty(value = "m1年节业绩")
    @ExcelProperty(value = "m1年节业绩")
    private BigDecimal annualItemsSupplyTotalM1;

    @ApiModelProperty(value = "m1同期业绩")
    @ExcelProperty(value = "m1同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM1;

    @ApiModelProperty(value = "m1年节业绩同比")
    @ExcelProperty(value = "m1年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM1;

    @ApiModelProperty(value = "m1年节差异")
    @ExcelProperty(value = "m1年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM1;

    @ApiModelProperty(value = "m2年节业绩")
    @ExcelProperty(value = "m2年节业绩")
    private BigDecimal annualItemsSupplyTotalM2;

    @ApiModelProperty(value = "m2同期业绩")
    @ExcelProperty(value = "m2同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM2;

    @ApiModelProperty(value = "m2年节业绩同比")
    @ExcelProperty(value = "m2年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM2;

    @ApiModelProperty(value = "m2年节差异")
    @ExcelProperty(value = "m2年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM2;

    @ApiModelProperty(value = "m3年节业绩")
    @ExcelProperty(value = "m3年节业绩" )
    private BigDecimal annualItemsSupplyTotalM3;

    @ApiModelProperty(value = "m3同期业绩")
    @ExcelProperty(value = "m3同期业绩" )
    private BigDecimal annualItemsSupplyTotalLyM3;

    @ApiModelProperty(value = "m3年节业绩同比")
    @ExcelProperty(value = "m3年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM3;

    @ApiModelProperty(value = "m3年节差异")
    @ExcelProperty(value = "m3年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM3;

    @ApiModelProperty(value = "m4年节业绩")
    @ExcelProperty(value = "m4年节业绩")
    private BigDecimal annualItemsSupplyTotalM4;

    @ApiModelProperty(value = "m4同期业绩")
    @ExcelProperty(value = "m4同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM4;

    @ApiModelProperty(value = "m4年节业绩同比")
    @ExcelProperty(value = "m4年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM4;

    @ApiModelProperty(value = "m4年节差异")
    @ExcelProperty(value = "m4年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM4;

}
