package com.wantwant.sfa.backend.yearFestival.vo.download;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("产品列表")
public class YearProductSku1Vo {

    @ApiModelProperty("sku/spu/line id")
    @ExcelIgnore
    private String productId;

    @ApiModelProperty("生产线名称")
    @ExcelProperty(value = "线别")
    private String lineName;

    @ApiModelProperty("spu名称")
    @ExcelProperty(value = "SPU")
    private String spuName;

    @ApiModelProperty("sku产品图")
    @ExcelProperty(value = "产品图")
    private String skuImages;

    @ApiModelProperty("sku编码")
    @ExcelProperty(value = "SKU")
    private String skuId;

    @ApiModelProperty("sku名称")
    @ExcelProperty(value = "名称")
    private String skuName;

    @ApiModelProperty("sku规格")
    @ExcelProperty(value = "规格")
    private String skuSpec;

    @ApiModelProperty("口味")
    @ExcelProperty(value = "口味")
    private String flavor;

    @ApiModelProperty("sku名称+规格+口味")
    @ExcelIgnore
    private String skuNameSpecFlavor;

    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;
    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Long positionTypeId;
    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;

    @ApiModelProperty(value = "年节")
    @ExcelIgnore
    private Integer theYear;

    @ApiModelProperty(value = "m1年节业绩")
    @ExcelProperty(value = "m1年节业绩")
    private BigDecimal annualItemsSupplyTotalM1;

    @ApiModelProperty(value = "m1同期业绩")
    @ExcelProperty(value = "m1同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM1;

    @ApiModelProperty(value = "m1年节业绩同比")
    @ExcelProperty(value = "m1年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM1;

    @ApiModelProperty(value = "m1年节差异")
    @ExcelProperty(value = "m1年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM1;

    @ApiModelProperty(value = "m2年节业绩")
    @ExcelProperty(value = "m2年节业绩")
    private BigDecimal annualItemsSupplyTotalM2;

    @ApiModelProperty(value = "m2同期业绩")
    @ExcelProperty(value = "m2同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM2;

    @ApiModelProperty(value = "m2年节业绩同比")
    @ExcelProperty(value = "m2年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM2;

    @ApiModelProperty(value = "m2年节差异")
    @ExcelProperty(value = "m2年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM2;

    @ApiModelProperty(value = "m3年节业绩")
    @ExcelProperty(value = "m3年节业绩" )
    private BigDecimal annualItemsSupplyTotalM3;

    @ApiModelProperty(value = "m3同期业绩")
    @ExcelProperty(value = "m3同期业绩" )
    private BigDecimal annualItemsSupplyTotalLyM3;

    @ApiModelProperty(value = "m3年节业绩同比")
    @ExcelProperty(value = "m3年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM3;

    @ApiModelProperty(value = "m3年节差异")
    @ExcelProperty(value = "m3年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM3;

    @ApiModelProperty(value = "m4年节业绩")
    @ExcelProperty(value = "m4年节业绩")
    private BigDecimal annualItemsSupplyTotalM4;

    @ApiModelProperty(value = "m4同期业绩")
    @ExcelProperty(value = "m4同期业绩")
    private BigDecimal annualItemsSupplyTotalLyM4;

    @ApiModelProperty(value = "m4年节业绩同比")
    @ExcelProperty(value = "m4年节业绩同比")
    private BigDecimal annualItemsSupplyTotalYoyM4;

    @ApiModelProperty(value = "m4年节差异")
    @ExcelProperty(value = "m4年节差异")
    private BigDecimal annualItemsSupplyTotalDifferenceM4;

}
