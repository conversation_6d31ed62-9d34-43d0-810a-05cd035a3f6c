package com.wantwant.sfa.backend.yearFestival.vo.download;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("产品列表")
public class YearProductSpu0Vo {

    @ApiModelProperty("sku/spu/line id")
    @ExcelIgnore
    private String productId;

    @ApiModelProperty("生产线名称")
    @ExcelProperty(value = "线别")
    private String lineName;

    @ApiModelProperty("spu名称")
    @ExcelProperty(value = "SPU")
    private String spuName;

    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;
    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Long positionTypeId;
    @ApiModelProperty("业务组ID")
    @ExcelIgnore
    private Integer businessGroupId;

    @ApiModelProperty(value = "年节")
    @ExcelIgnore
    private Integer theYear;

    @ApiModelProperty(value = "当前产品组业绩/年节累计业绩")
    @ExcelProperty(value = "年节累计业绩")
    private BigDecimal annualItemsSupplyTotalCur;

    @ApiModelProperty(value = "本期业绩占比")
    @ExcelProperty(value = "本期业绩占比")
    private BigDecimal annualItemsSupplyTotalCurProportion;

    @ApiModelProperty(value = "同期业绩")
    @ExcelProperty(value = "同期业绩")
    private BigDecimal annualItemsSupplyTotalCurLy;

    @ApiModelProperty(value = "同期业绩占比")
    @ExcelProperty(value = "同期业绩占比")
    private BigDecimal annualItemsSupplyTotalCurProportionLy;

    @ApiModelProperty(value = "业绩同比（按时间进度）")
    @ExcelProperty(value = "业绩同比（时间进度）")
    private BigDecimal annualItemsSupplyTotalCurYoyTime;

    @ApiModelProperty(value = "业绩同比（整个年节）")
    @ExcelProperty(value = "业绩同比（整个年节）")
    private BigDecimal annualItemsSupplyTotalCurYoyAll;

    @ApiModelProperty(value = "业绩差异")
    @ExcelProperty(value = "业绩差异")
    private BigDecimal annualItemsSupplyTotalCurDifference;

    @ApiModelProperty(value = "同期客户业绩差异")
    @ExcelProperty(value = "同期客户业绩差异")
    private BigDecimal annualItemsSupplyTotalCustomDifference;

    @ApiModelProperty(value = "新增客户业绩差异")
    @ExcelProperty(value = "新增客户业绩差异")
    private BigDecimal annualItemsSupplyTotalNewCustomDifference;

    @ApiModelProperty(value = "旺金币业绩")
    @ExcelProperty(value = "旺金币业绩")
    private BigDecimal annualFreeTotalAmount;

    @ApiModelProperty(value = "旺金币折扣率")
    @ExcelProperty(value = "旺金币折扣率")
    private BigDecimal annualFreeTotalAmountDiscountRate;

    @ApiModelProperty(value = "预订单业绩")
    @ExcelProperty(value = "预订单业绩")
    private BigDecimal annualAdvancedOrderItemsSupplyTotal;

    @ApiModelProperty(value = "预订单未发货业绩")
    @ExcelProperty(value = "预订单未发货业绩")
    private BigDecimal annualAdvancedOrderUnshippedItemsSupplyTotal;

    @ApiModelProperty(value = "本期管理岗人均业绩（当前）")
    @ExcelProperty(value = "本期管理岗人均业绩（当前）")
    private BigDecimal mangementCapitaItemsSupplyTotal;

    @ApiModelProperty(value = "同期管理岗人均业绩（当前）")
    @ExcelProperty(value = "同期管理岗人均业绩（当前）")
    private BigDecimal mangementCapitaItemsSupplyTotalLy;

    @ApiModelProperty(value = "管理岗人均业绩同比（按时间进度）")
    @ExcelProperty(value = "管理岗人均业绩同比（时间进度）")
    private BigDecimal mangementCapitaItemsSupplyTotalYoyTime;

    @ApiModelProperty(value = "管理岗人均业绩同比（整个年节）")
    @ExcelProperty(value = "管理岗人均业绩同比（整个年节）")
    private BigDecimal mangementCapitaItemsSupplyTotalYoyAll;

    @ApiModelProperty(value = "本期交易客户数")
    @ExcelProperty(value = "本期交易客户数")
    private BigDecimal annualTradeCustomer;

    @ApiModelProperty(value = "同期交易客户数")
    @ExcelProperty(value = "同期交易客户数")
    private BigDecimal annualTradeCustomerLy;

    @ApiModelProperty(value = "交易客户数同比（按时间进度）")
    @ExcelProperty(value = "交易客户数同比（时间进度）")
    private BigDecimal annualTradeCustomerYoyTime;

    @ApiModelProperty(value = "交易客户数同比（整个年节）")
    @ExcelProperty(value = "交易客户数同比（整个年节）")
    private BigDecimal annualTradeCustomerYoyAll;

    @ApiModelProperty(value = "交易客户数差异")
    @ExcelProperty(value = "交易客户数差异")
    private BigDecimal annualTradeCustomerDifference;

    @ApiModelProperty(value = "本期客单价")
    @ExcelProperty(value = "本期客单价")
    private BigDecimal annualUnitPrice;

    @ApiModelProperty(value = "同期客单价")
    @ExcelProperty(value = "同期客单价")
    private BigDecimal annualUnitPriceLy;

    @ApiModelProperty(value = "客单价同比（按时间进度）")
    @ExcelProperty(value = "客单价同比（时间进度）")
    private BigDecimal annualUnitPriceYoyTime;

    @ApiModelProperty(value = "客单价同比（整个年节）")
    @ExcelProperty(value = "客单价同比（整个年节）")
    private BigDecimal annualUnitPriceYoyAll;

    @ApiModelProperty(value = "客单价差异")
    @ExcelProperty(value = "客单价差异")
    private BigDecimal annualUnitPriceDifference;

}
