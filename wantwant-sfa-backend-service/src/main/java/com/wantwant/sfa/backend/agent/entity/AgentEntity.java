package com.wantwant.sfa.backend.agent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/06/上午10:59
 */
@Data
@TableName("sfa_agent")
@ApiModel(value = "AgentEntity对象", description = "待办信息")
public class AgentEntity extends CommonEntity implements Comparable<AgentEntity>{

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @TableField("agent_name")
    private String agentName;

    @TableField("agent_type")
    private Integer agentType;

    @TableField("`order`")
    private Integer order;

    @TableField("route")
    private String route;

    @TableField("icon")
    private String icon;

    @Override
    public int compareTo(AgentEntity o) {
        if(this.order > o.order){
            return 1;
        }
        else if(this.order == o.order){
            return 0;
        }
        return -1;
    }
}
