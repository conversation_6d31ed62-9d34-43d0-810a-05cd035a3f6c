package com.wantwant.sfa.backend.agent.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/06/上午11:04
 */
public enum AgentEnum {
    INTERVIEW(1,"待面试"),
    INTERVIEW_PROCESSING(2,"待处理面试中"),
    MARKET_VISIT(25,"市场走访中"),
    ON_BOARD_APPLY(3,"待处理入职"),
    OFF_BOARD_AUDIT(4,"待处理离职"),
    ON_BOARD(5,"待入职办理"),
    AUDIT_WAIT(26,"待审核"),
    OFF_BOARD(6,"待离职办理"),
    OP_AUDIT(7,"待运营审核"),

    CUSTOMER_CREATE_AUDIT(8,"待审核客户建档"),
    CUSTOMER_TRANSFER(9,"待审核客户转移"),
    CUSTOMER_MODIFY_AUDIT(10,"待审核资料修改"),
    CUSTOMER_TRANSFER_OP(22,"待审核客户转移（运营审核）"),
    CUSTOMER_TRANSFER_MANAGER(38,"待审核客户转移总监审核"),


    AUTH_AUDIT(11,"待审核授权书"),
    DISPLAY_AUDIT(12,"待审核特陈"),
    BD_AUDIT(13,"待审核 BD建档"),
    LEAVE_AUDIT(14,"待审核请假"),

    GOLD_BUSINESS(15,"待审核旺金币（业务）"),
    GOLD_FINANCE(16,"待审核旺金币（财务））"),
    GOLD_BOSS(17,"待审核旺金币（主管）"),

    ESTIMATE_AREA_AUDIT(18,"待审核销售预估（大区)"),
    ESTIMATE_COMPANY_AUDIT(19,"待审核销售预估（分公司）"),
    ESTIMATE_DEPARTMENT_AUDIT(39,"待审核销售预估（营业所）"),
    ESTIMATE_PRODUCT_AUDIT(20,"待审核销售预估（产销）"),
    COMPLAINT_AUDIT(21,"待薪资申述"),



    REFERRAL_BONUS(23,"推荐奖金申请"),
    REFERRAL_BONUS_AUDIT(24,"推荐奖金审核"),
    WAREHOUSE_AUDIT(27,"待审核仓储费用"),

    CEO_PERFORMANCE(28,"合伙人绩效评定"),
    MANAGER_PERFORMANCE(29,"总监绩效评定"),
    AREA_PERFORMANCE(30,"总督导绩效评定"),
    CITY_MANAGER_PERFORMANCE(31,"区域经理绩效评定"),
    AFTER_SALES_AUDIT(32,"待审核临期售后"),
    FEEDBACK_AUDIT(33,"问题反馈"),
    MESSAGE_AUDIT(34,"公告审核"),
    DEPARTMENT_GOAL(35,"营业所目标设置"),


    REVIEW_REPORT_CHECK(36,"复盘报表待查看"),
    REVIEW_REPORT_EDIT(37,"复盘报表待填写"),
    COMPANY_GOAL(41,"分公司目标设置"),

    BARCODE_AUDIT(40,"条码费审核"),
    DISPLAY_RULE_AUDIT(44,"待处理特陈规则"),
    WORK_REPORT(42,"工作周报"),
    WORK_REPORT_REVIEW(43,"工作周报查看"),
    GOLD_TYPE_MANAGEMENT(45,"待审核旺金币（类型管理）"),

    ALTER_TEMPLATE(46,"待审核造旺预警规则"),
    TASK_PROCESSING(47,"任务代办"),

    WAIT_BUSINESS_TRIP(48,"待审批出差单"),
    WAIT_EXPENSE_APPLY(49,"待审批报销单"),
    ABNORMAL_INVENTORY_APPLY(50,"异常库存处理申请"),

    LOGISTICS_TEMPLATE(51,"待审核后勤预警规则"),
    PROFITLOSS_EXAMINE(52,"损益-线下数据预估"),
    PARTNER_GOAL_SET(53,"待设置合伙人目标"),
    PARTNER_GOAL_AUDIT(54,"待审核合伙人目标"),

    ESTIMATE_OPERATION_AUDIT(55,"待审核销售预估（营运）"),
    ESTIMATE_DEPARTMENT_SUBMIT(56,"待提报销售预估（营业所）"),
    ESTIMATE_COMPANY_SUBMIT(57,"待提报销售预估（分公司）"),


    POLICY(58,"待查看政策"),
    WALLET_AGENT(59,"旺金币代办"),
    ABNORMAL_LOGIN_AUDIT(60,"异常登录待稽核"),
    JOB_TRANSFER(61,"待异动办理"),
    CUSTOMER_CLUE(62,"客户线索待办"),

    PROVINCE_GOAL(63,"省区目标设置"),
    VARE_GOAL(64,"大区目标设置"),



    UNKNOWN(-1,"未知");



    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    AgentEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
