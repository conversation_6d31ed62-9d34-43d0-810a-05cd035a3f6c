package com.wantwant.sfa.backend.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginRequest;
import com.wantwant.sfa.backend.abnormalLogin.service.AbnormalLoginService;
import com.wantwant.sfa.backend.abnormalLogin.vo.AbnormalLoginListVo;
import com.wantwant.sfa.backend.activityQuota.model.ActivityQuotaApplicationModel;
import com.wantwant.sfa.backend.activityQuota.service.ICostTypeService;
import com.wantwant.sfa.backend.agent.entity.AgentEntity;
import com.wantwant.sfa.backend.agent.enums.AgentEnum;
import com.wantwant.sfa.backend.agent.service.IAgentService;
import com.wantwant.sfa.backend.agent.vo.AgentVo;
import com.wantwant.sfa.backend.application.EstimateApplication;
import com.wantwant.sfa.backend.applyMember.dto.ApplyMemberQueryDTO;
import com.wantwant.sfa.backend.applyMember.dto.ResignApprovalQueryDTO;
import com.wantwant.sfa.backend.applyMember.vo.ApplyApprovalVO;
import com.wantwant.sfa.backend.applyMember.vo.ApplyMemberVO;
import com.wantwant.sfa.backend.applyMember.vo.ApplyResignVO;
import com.wantwant.sfa.backend.arch.entity.DeptEmployeeRelationEntity;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.barcode.service.IBarcodeSearchService;
import com.wantwant.sfa.backend.bonusEvaluation.vo.PerformanceEvalustionMattersVo;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.businessGroup.service.IBusinessGroupService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.complaint.service.IComplaintSearchService;
import com.wantwant.sfa.backend.customer.request.ApplyForListCustomerRequest;
import com.wantwant.sfa.backend.customer.vo.TransferCustomerVO;
import com.wantwant.sfa.backend.domain.flow.service.IFlowService;
import com.wantwant.sfa.backend.estimate.service.IEstimateSearchV2Service;
import com.wantwant.sfa.backend.info.request.SenderBoxV2Request;
import com.wantwant.sfa.backend.interview.service.AuditService;
import com.wantwant.sfa.backend.leave.request.NewLeaveListRequest;
import com.wantwant.sfa.backend.leave.service.ILeaveService;
import com.wantwant.sfa.backend.leave.service.INewLeaveService;
import com.wantwant.sfa.backend.leave.vo.NewLeaveListVo;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.ActivityQuotaApplicationMapper;
import com.wantwant.sfa.backend.mapper.agent.AgentMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.authorization.AuthorizationSearchMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.estimate.EstimateStatisticsV2Mapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionApplyMapper;
import com.wantwant.sfa.backend.metrics.service.IAlterService;
import com.wantwant.sfa.backend.metrics.service.impl.LogisticsService;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.policy.service.IPolicySearchService;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.review.service.IReviewReportSearchService;
import com.wantwant.sfa.backend.review.vo.ReviewReportPointVo;
import com.wantwant.sfa.backend.service.*;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.taskManagement.request.TaskSelectRequest;
import com.wantwant.sfa.backend.transaction.model.PositionTransactionModel;
import com.wantwant.sfa.backend.transaction.request.PositionTransactionSearchRequest;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.wallet.dto.AgentDTO;
import com.wantwant.sfa.backend.wallet.service.IWalletAgentService;
import com.wantwant.sfa.backend.workReport.service.IWorkReportProcessService;
import com.wantwant.sfa.backend.zw.request.ZWDetailedInfoRequest;
import com.wantwant.sfa.backend.zw.request.ZWEmployeeInfoRequest;
import com.wantwant.sfa.common.base.dto.RpcResult;
import com.wantwant.sfa.customer.management.api.dto.common.BackendCommonReqDto;
import com.wantwant.sfa.customer.management.api.service.SalesLeadApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/06/上午10:58
 */
@Service
@Slf4j
public class AgentService implements IAgentService {
    @Autowired
    private AgentMapper agentMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private IZWSignupService izwSignupService;
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private AuthorizationSearchMapper authorizationSearchMapper;
    @Autowired
    private ActivityQuotaApplicationMapper activityQuotaApplicationMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;
    @Autowired
    private EstimateStatisticsV2Mapper estimateStatisticsV2Mapper;
    @Autowired
    private IComplaintSearchService complaintSearchService;
    @Autowired
    private DataModifyService dataModifyService;
    @Autowired
    private DisplayInfoService displayInfoService;
    @Autowired
    private BusinessService businessService;
    @Autowired
    private ILeaveService LeaveService;
    @Autowired
    private INewLeaveService iNewLeaveService;
    @Autowired
    private IRealtimeDataService iRealtimeDataService;
    @Autowired
    private ApplyForCustomerService applyForCustomerService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ReferralBonusService referralBonusService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private BonusEvaluationService bonusEvaluationService;

    @Autowired
    private AfterSalesInfoService afterSalesInfoService;
    @Autowired
    private IReviewReportSearchService reviewReportSearchService;

    @Autowired
    private FeedbackInfoService feedbackInfoService;

    @Autowired
    private OrganizationGoalService organizationGoalService;

    @Autowired
    private IEstimateSearchV2Service estimateSearchV2Service;

    @Autowired
    private InfoCentreService infoCentreService;
    @Autowired
    private IBarcodeSearchService barcodeSearchService;
    @Autowired
    private IndicatorsService indicatorsService;

    @Autowired
    private DisplayRuleService displayRuleService;
    @Autowired
    private IWorkReportProcessService workReportProcessService;

    @Autowired
    private ICostTypeService costTypeService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Autowired
    private SettingServiceImpl settingService;
    @Autowired
    private IAlterService alterService;
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private BusinessTripService businessTripService;

    @Autowired
    private ExpenseApplyService expenseApplyService;

    @Autowired
    private OrganizationAbnormalInventoryService abnormalInventoryService;
    @Autowired
    private ProfitLossForecastService profitLossForecastService;
    @Autowired
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;
    @Resource
    private IFlowService flowService;
    @Resource
    private EstimateApplication estimateApplication;
    @Resource
    private IPolicySearchService policySearchService;
    @Resource
    private IWalletAgentService walletAgentService;
    @Resource
    private AbnormalLoginService abnormalLoginService;
    @Resource
    private SfaTransactionApplyMapper sfaTransactionApplyMapper;
    @Resource
    private SalesLeadApi salesLeadApi;
    @Resource
    private IBusinessGroupService businessGroupService;

    private Long expiration = 300L;

    private String agentKey = "sfa-backend:agent:employeeId:";

    @Override
    public List<AgentVo> getAgentList(String person, boolean cache) {
        long startTime = System.currentTimeMillis();
        String startPoint = startTime + "-" + ThreadLocalRandom.current().nextInt(100000);

        List<AgentVo> agentVos = null;
        if (cache) {
            agentVos = (List<AgentVo>) redisUtil.get(agentKey + person);
            if (!CollectionUtils.isEmpty(agentVos)) {
                return agentVos;
            }
        }

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        log.info("loginInfo:{} ", loginInfo);

        List<RoleEmployeeRelationEntity> roleIds = Optional.ofNullable(roleEmployeeRelationMapper.selectList(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().eq(RoleEmployeeRelationEntity::getEmployeeId, person).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());

        // 获取登陆人信息
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person, loginInfo);

        String organizationType = loginInfo.getOrganizationType();
        // 获取所有代办事项
        List<AgentEntity> agentEntities = agentMapper.selectList(new QueryWrapper<AgentEntity>().eq("delete_flag", 0).orderByAsc("`order`"));
        // 人资
        String hrEmployeeId = settingService.getValue("zw_iwantwant_hr_employee_id");
        //
        String bossEmployeeId = settingService.getValue("zw_senior_hr_employee_id");
        // 授权审核
        String authorizationAuditor = settingService.getValue("authorization_audit_employee");
        // 旺金币财务
        String goldAccounting = settingService.getValue("wang_gold_accounting");
        // 旺金币
        String goldDirector = settingService.getValue("wang_gold_director");
        // 产销普通组
        String estimateEmployee = settingService.getValue("estimate_employee");
        // 产销管理组
        String estimateAdmin = settingService.getValue("estimate_admin");
        // 薪资申诉
        String complaintEmployee = settingService.getValue("complaint_employee");
        // 客户转移运营审核
        String customerTransferAudit = settingService.getValue("customer_transfer_audit");

        ConcurrentSkipListMap<AgentEntity, Integer> result = new ConcurrentSkipListMap<>();

        List<CompletableFuture<Integer>> completableFutures = new ArrayList<>();
        // 获取代办事项
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("auditService.pendingRedPointCount");
        Map<String, Integer> stringIntegerMap = auditService.pendingRedPointCount(personInfo.getEmployeeId());
        stopWatch.stop();
        log.info("agent-task({}): auditService.pendingRedPointCount used time :{}", startPoint, stopWatch.prettyPrint());


        String departmentManagerEmpId = businessGroupService.getDepartmentManagerEmpId(RequestUtils.getBusinessGroup());
        for (AgentEntity e : agentEntities) {// 待面试
            if (e.getAgentType() == AgentEnum.INTERVIEW.getType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("interviewCount");
                    Integer value = stringIntegerMap.get("interviewCount");
                    sw.stop();
                    log.info("agent-task({}): interviewCount used time :{}", startPoint, sw.prettyPrint());
                    return value;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 面试中
            else if (e.getAgentType() == AgentEnum.INTERVIEW_PROCESSING.getType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("interviewingCount");
                    Integer value = stringIntegerMap.get("interviewingCount");
                    sw.stop();
                    log.info("agent-task({}): interviewingCount used time :{}", startPoint, sw.prettyPrint());
                    return value;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 市场走访
            else if (e.getAgentType() == AgentEnum.MARKET_VISIT.getType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("marketVisitCount");
                    Integer value = stringIntegerMap.get("marketVisitCount");
                    sw.stop();
                    log.info("agent-task({}): marketVisitCount used time :{}", startPoint, sw.prettyPrint());
                    return value;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 入职申请
            else if (e.getAgentType() == AgentEnum.ON_BOARD_APPLY.getType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("auditAndApproveCount");
                    Integer value = stringIntegerMap.get("auditAndApproveCount");
                    sw.stop();
                    log.info("agent-task({}): auditAndApproveCount used time :{}", startPoint, sw.prettyPrint());
                    return value;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 待审核
            else if (e.getAgentType() == AgentEnum.AUDIT_WAIT.getType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("auditCount");
                    Integer value = stringIntegerMap.get("auditCount");
                    sw.stop();
                    log.info("agent-task({}): auditCount used time :{}", startPoint, sw.prettyPrint());
                    return value;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }


            // 入离职办理
            else if (hrEmployeeId.contains(personInfo.getEmployeeId())
                    && (AgentEnum.ON_BOARD.getType() == e.getAgentType() || AgentEnum.OFF_BOARD.getType() == e.getAgentType())
                    && !bossEmployeeId.equals(personInfo.getEmployeeId())
            ) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("onboard_offboard");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    Integer count = hrProcessCount(e.getAgentType());

                    sw.stop();
                    log.info("agent-task({}): onboard_offboard used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }
            // 异动办理
            else if (hrEmployeeId.contains(personInfo.getEmployeeId()) && AgentEnum.JOB_TRANSFER.getType() == e.getAgentType()
                    && !bossEmployeeId.equals(personInfo.getEmployeeId())) {
                StopWatch sw = new StopWatch();
                sw.start("onboard_offboard");

                RequestContextHolder.setRequestAttributes(requestAttributes);
                Integer count = hrProcessCount(e.getAgentType());
                sw.stop();
                log.info("agent-task({}): onboard_offboard(result count no used) used time :{}", startPoint, sw.prettyPrint());
            }

            // 待审核客户建党
            else if (AgentEnum.CUSTOMER_CREATE_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("customer_create_audit");
//                    int count = customerApproveService.queryCustomerApprovePendingAuditCount(personInfo.getOrganizationId(),personInfo.getEmployeeId());

                    sw.stop();
                    log.info("agent-task({}) customer_create_audit used time :{}", startPoint, sw.prettyPrint());
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 客户转移（督导审核）
            else if (AgentEnum.CUSTOMER_TRANSFER.getType() == e.getAgentType() && organizationType.equals("area")) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("customer_transfer");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    ApplyForListCustomerRequest request = new ApplyForListCustomerRequest();
                    request.setPerson(person);
                    request.setResult(0);
                    IPage<TransferCustomerVO> transferCustomerVOIPage = applyForCustomerService.queryTransferList(request);
                    if (Objects.isNull(transferCustomerVOIPage)) {
                        return 0;
                    }
                    int count = Long.valueOf(transferCustomerVOIPage.getTotal()).intValue();

                    sw.stop();
                    log.info("agent-task({}): customer_transfer used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 客户转移（督导总监审核）
            else if (AgentEnum.CUSTOMER_TRANSFER_MANAGER.getType() == e.getAgentType() && organizationType.equals("company")) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("customer_transfer_manager");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    ApplyForListCustomerRequest request = new ApplyForListCustomerRequest();
                    request.setPerson(person);
                    request.setResult(0);
                    IPage<TransferCustomerVO> transferCustomerVOIPage = applyForCustomerService.queryTransferList(request);
                    if (Objects.isNull(transferCustomerVOIPage)) {
                        return 0;
                    }
                    int count = Long.valueOf(transferCustomerVOIPage.getTotal()).intValue();

                    sw.stop();
                    log.info("agent-task({}): customer_transfer_manager used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 客户转移（运营审核）
            if (AgentEnum.CUSTOMER_TRANSFER_OP.getType() == e.getAgentType() && customerTransferAudit.contains(person)) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("customer_transfer_op");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    ApplyForListCustomerRequest request = new ApplyForListCustomerRequest();
                    request.setPerson(person);
                    request.setResult(0);
                    IPage<TransferCustomerVO> transferCustomerVOIPage = applyForCustomerService.queryTransferList(request);
                    if (Objects.isNull(transferCustomerVOIPage)) {
                        return 0;
                    }
                    int count = Long.valueOf(transferCustomerVOIPage.getTotal()).intValue();

                    sw.stop();
                    log.info("agent-task({}): customer_transfer_op used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }


            // 待审核资料修改
            else if (AgentEnum.CUSTOMER_MODIFY_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("customer_modify_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int customerModifyCount = dataModifyService.queryTaskCount(person, 1);
                    int ceoModifyCount = dataModifyService.queryTaskCount(person, 2);
                    int count = customerModifyCount + ceoModifyCount;
                    sw.stop();
                    log.info("agent-task({}): customer_modify_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }


            // 待审核授权书
            else if (AgentEnum.AUTH_AUDIT.getType() == e.getAgentType() && (authorizationAuditor.contains(person) || !organizationType.equals("zb"))) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("auth_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = authorizationCount(personInfo, e.getAgentType(), organizationType);
                    sw.stop();
                    log.info("agent-task({}): auth_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 待审核特陈
            else if (AgentEnum.DISPLAY_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("display_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = displayInfoService.queryTaskCount(person);
                    sw.stop();
                    log.info("agent-task({}): display_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }
            // 待审核特陈规则
            else if (AgentEnum.DISPLAY_RULE_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("display_rule_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = displayRuleService.queryTaskCount(person);
                    sw.stop();
                    log.info("agent-task({}): display_rule_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.AFTER_SALES_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("after_sales_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = afterSalesInfoService.queryTaskCount(person);
                    sw.stop();
                    log.info("agent-task({}): after_sales_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            //营业所目标设置
            else if (AgentEnum.DEPARTMENT_GOAL.getType() == e.getAgentType() && loginInfo.getPositionTypeId() == 2) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("department_goal");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = Optional.ofNullable(organizationGoalService.queryTaskCount(person)).orElse(0);
                    sw.stop();
                    log.info("agent-task({}): department_goal used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            //分公司目标设置
            else if (AgentEnum.COMPANY_GOAL.getType() == e.getAgentType() && loginInfo.getPositionTypeId() == 11) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("company_goal");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = Optional.ofNullable(organizationGoalService.queryTaskCount(person)).orElse(0);
                    sw.stop();
                    log.info("agent-task({}): company_goal used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 省区目标设置
            else if (AgentEnum.PROVINCE_GOAL.getType() == e.getAgentType() && loginInfo.getPositionTypeId() == 12) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("province_goal");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = Optional.ofNullable(organizationGoalService.queryTaskCount(person)).orElse(0);
                    sw.stop();
                    log.info("agent-task({}): province_goal used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 大区目标设置
            else if (AgentEnum.VARE_GOAL.getType() == e.getAgentType() && loginInfo.getPositionTypeId() == 1) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("varea_goal");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = Optional.ofNullable(organizationGoalService.queryTaskCount(person)).orElse(0);
                    sw.stop();
                    log.info("agent-task({}): varea_goal used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            //问题反馈待审批
            else if (AgentEnum.FEEDBACK_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("feedback_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = feedbackInfoService.queryTaskCount(person, personInfo.getOrganizationId());
                    sw.stop();
                    log.info("agent-task({}): feedback_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 待审核 BD建档
            else if (AgentEnum.BD_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("bd_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = businessService.queryTaskCount(person);
                    sw.stop();
                    log.info("agent-task({}): bd_audit used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 待审核请假
            else if (AgentEnum.LEAVE_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("leave_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
//                    LeaveListRequest request = new LeaveListRequest();
//                    request.setPerson(personInfo.getEmployeeId());
//                    request.setOrganizationId(request.getOrganizationId());
//                    request.setType(1);
//                    IPage<LeaveListVo> leaveList = LeaveService.getLeaveList(request);
                    NewLeaveListRequest request = new NewLeaveListRequest();
                    request.setPerson(personInfo.getEmployeeId());
                    request.setOrganizationId(request.getOrganizationId());
                    request.setType(1);
                    IPage<NewLeaveListVo> leaveList = iNewLeaveService.leaveList(request);
                    sw.stop();
                    log.info("agent-task({}): leave_audit used time :{}", startPoint, sw.prettyPrint());

                    if (Objects.isNull(leaveList)) {
                        return 0;
                    }
                    return Long.valueOf(leaveList.getTotal()).intValue();


                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 造旺币业务
            else if (AgentEnum.GOLD_BUSINESS.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("gold_business");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = quotaAuditCount(personInfo.getEmployeeId());
                    sw.stop();
                    log.info("agent-task({}): gold_business used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 待审核旺金币导入(财务)
            else if (AgentEnum.GOLD_FINANCE.getType() == e.getAgentType() && goldAccounting.contains(person)) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("gold_finance");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = quotaExportAuditCount(personInfo, 1);

                    sw.stop();
                    log.info("agent-task({}): gold_finance used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 待审核旺金币导入(财务)
            else if (AgentEnum.GOLD_BOSS.getType() == e.getAgentType() && departmentManagerEmpId.equals(personInfo.getEmployeeId())) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("gold_boss");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = quotaExportAuditCount(personInfo, 2);
                    
                    sw.stop();
                    log.info("agent-task({}): gold_boss used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }


            // 大区销售预估（大区）
            else if (AgentEnum.ESTIMATE_AREA_AUDIT.getType() == e.getAgentType() && (organizationType.equals("varea") || organizationType.equals("area"))) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {

                    StopWatch sw = new StopWatch();
                    sw.start("estimate_area_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getProcessCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_area_audit used time :{}", startPoint, sw.prettyPrint());


                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 大区销售预估（分公司）
            else if (AgentEnum.ESTIMATE_COMPANY_AUDIT.getType() == e.getAgentType() && organizationType.equals("company")) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("estimate_company_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getProcessCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_company_audit used time :{}", startPoint, sw.prettyPrint());

                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 大区销售预估（营业所）
            else if (AgentEnum.ESTIMATE_DEPARTMENT_AUDIT.getType() == e.getAgentType() && organizationType.equals("department")) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("estimate_department_audit");


                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getProcessCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_department_audit used time :{}", startPoint, sw.prettyPrint());

                    return count;

                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 大区销售预估（产销）
            else if (AgentEnum.ESTIMATE_PRODUCT_AUDIT.getType() == e.getAgentType() && roleIds.stream().filter(f -> f.getRoleId() == 70).findFirst().isPresent()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("estimate_product_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getProcessCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_product_audit used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 销售预估（营运）
            else if (AgentEnum.ESTIMATE_OPERATION_AUDIT.getType() == e.getAgentType() && roleIds.stream().filter(f -> f.getRoleId() == 30).findFirst().isPresent()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("estimate_operation_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getProcessCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_operation_audit used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }
            // 待提报销售预估营业所
            else if (AgentEnum.ESTIMATE_DEPARTMENT_SUBMIT.getType() == e.getAgentType() && organizationType.equals("department")) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("estimate_department_submit&department");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getSubmitCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_department_submit&department used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }
            // 待提报销售预估分公司
            else if (AgentEnum.ESTIMATE_DEPARTMENT_SUBMIT.getType() == e.getAgentType() && organizationType.equals("company")) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("estimate_department_submit&company");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = estimateApplication.getSubmitCount(person, RequestUtils.getLoginInfo().getPositionTypeId(), RequestUtils.getBusinessGroup());

                    sw.stop();
                    log.info("agent-task({}): estimate_department_submit&company used time :{}", startPoint, sw.prettyPrint());

                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 薪资申诉
            else if (AgentEnum.COMPLAINT_AUDIT.getType() == e.getAgentType() && complaintEmployee.contains(person)) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("complaint_audit");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        int count = complaintAuditCount(person);

                        sw.stop();
                        log.info("agent-task({}): complaint_audit used time :{}", startPoint, sw.prettyPrint());

                        return count;
                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }
            // 待审核仓储费用
            else if (AgentEnum.WAREHOUSE_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("warehouse_audit");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        int count = warehouseService.queryTaskCount(person);

                        sw.stop();
                        log.info("agent-task({}): warehouse_audit used time :{}", startPoint, sw.prettyPrint());

                        return count;
                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.CEO_PERFORMANCE.getType() == e.getAgentType()) {

                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("ceo_performance");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        PerformanceEvalustionMattersVo performanceEvalustionMattersVo = bonusEvaluationService.performanceEvaluatioMatters(personInfo.getEmployeeId(), 3);

                        sw.stop();
                        log.info("agent-task({}): ceo_performance used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.nonNull(performanceEvalustionMattersVo)) {
                            return performanceEvalustionMattersVo.getCount();
                        } else {
                            return 0;
                        }

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.CITY_MANAGER_PERFORMANCE.getType() == e.getAgentType()) {

                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("city_manager_performance");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        PerformanceEvalustionMattersVo performanceEvalustionMattersVo = bonusEvaluationService.performanceEvaluatioMatters(personInfo.getEmployeeId(), 10);

                        sw.stop();
                        log.info("agent-task({}): city_manager_performance used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.nonNull(performanceEvalustionMattersVo)) {
                            return performanceEvalustionMattersVo.getCount();
                        } else {
                            return 0;
                        }

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.MANAGER_PERFORMANCE.getType() == e.getAgentType()) {

                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("manager_performance");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        PerformanceEvalustionMattersVo performanceEvalustionMattersVo = bonusEvaluationService.performanceEvaluatioMatters(personInfo.getEmployeeId(), 2);

                        sw.stop();
                        log.info("agent-task({}): manager_performance used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.nonNull(performanceEvalustionMattersVo)) {
                            return performanceEvalustionMattersVo.getCount();
                        } else {
                            return 0;
                        }

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.AREA_PERFORMANCE.getType() == e.getAgentType()) {

                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("area_performance");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        PerformanceEvalustionMattersVo performanceEvalustionMattersVo = bonusEvaluationService.performanceEvaluatioMatters(personInfo.getEmployeeId(), 1);

                        sw.stop();
                        log.info("agent-task({}): area_performance used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.nonNull(performanceEvalustionMattersVo)) {
                            return performanceEvalustionMattersVo.getCount();
                        } else {
                            return 0;
                        }

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.MESSAGE_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("message_audit");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        SenderBoxV2Request request = new SenderBoxV2Request();
                        request.setEmployeeId(personInfo.getEmployeeId());
                        request.setResult(0);
                        int count = infoCentreService.getSenderBoxV2ListCount(request);
                        sw.stop();
                        log.info("agent-task({}): message_audit used time :{}", startPoint, sw.prettyPrint());
                        return count;
                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 复盘报表检查
            else if (AgentEnum.REVIEW_REPORT_CHECK.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("review_report_check");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                        String date = sdf.format(new Date());
                        ReviewReportPointVo reviewReportPoint = reviewReportSearchService.getReviewReportPoint(person, date, RequestUtils.getBusinessGroup());
                        sw.stop();
                        log.info("agent-task({}): review_report_check used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.nonNull(reviewReportPoint)) {
                            return reviewReportPoint.getReviewCount();
                        } else {
                            return 0;
                        }
                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 复盘报表待填写
            else if (AgentEnum.REVIEW_REPORT_EDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("review_report_edit");
                    try {

                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                        String date = sdf.format(new Date());
                        ReviewReportPointVo reviewReportPoint = reviewReportSearchService.getReviewReportPoint(person, date, RequestUtils.getBusinessGroup());
                        sw.stop();
                        log.info("agent-task({}): review_report_edit used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.nonNull(reviewReportPoint)) {
                            return reviewReportPoint.getUncommittedCount() + reviewReportPoint.getProcessingCount();
                        } else {
                            return 0;
                        }
                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }

            // 条码费
            else if (AgentEnum.BARCODE_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("barcode_audit");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): barcode_audit used time :{}", startPoint, sw.prettyPrint());
                        return barcodeSearchService.barcodeAgentCount(person);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.WORK_REPORT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("work_report");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): work_report used time :{}", startPoint, sw.prettyPrint());
                        return workReportProcessService.agentCount(person);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.WORK_REPORT_REVIEW.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("work_report_review");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): work_report_review used time :{}", startPoint, sw.prettyPrint());
                        return workReportProcessService.agentReviewCount(person);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.GOLD_TYPE_MANAGEMENT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("gold_type_management");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): gold_type_management used time :{}", startPoint, sw.prettyPrint());
                        return costTypeService.getPendingCostTypeCount(person);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.ALTER_TEMPLATE.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("alter_template");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): alter_template used time :{}", startPoint, sw.prettyPrint());
                        return alterService.auditCount(person);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.TASK_PROCESSING.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("task_processing");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
//                        Integer processingCount = sfaTaskMapper.getProcessingCount(person);
                        TaskSelectRequest taskSelectRequest = new TaskSelectRequest();
                        taskSelectRequest.setPerson(person);
                        taskSelectRequest.setProcessing(true);


                        DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getEmployeeId, person).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0).last("limit 1"));
                        if (Objects.nonNull(deptEmployeeRelationEntity)) {
                            taskSelectRequest.setProject(true);
                        }
                        Integer auditCount = sfaTaskMapper.selectCountByCondition(taskSelectRequest);

                        sw.stop();
                        log.info("agent-task({}): task_processing used time :{}", startPoint, sw.prettyPrint());
                        return auditCount;
                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.WAIT_BUSINESS_TRIP.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("wait_business_trip");
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = businessTripService.queryTaskCount(person);
                    sw.stop();
                    log.info("agent-task({}): wait_business_trip used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.WAIT_EXPENSE_APPLY.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("wait_expense_apply");
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = expenseApplyService.queryTaskCount(person, personInfo.getOrganizationId());
                    sw.stop();
                    log.info("agent-task({}): wait_expense_apply used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.ABNORMAL_INVENTORY_APPLY.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("abnormal_inventory_apply");
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    int count = abnormalInventoryService.getAbnormalInventoryAuditCount(person);
                    sw.stop();
                    log.info("agent-task({}): abnormal_inventory_apply used time :{}", startPoint, sw.prettyPrint());
                    return count;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.LOGISTICS_TEMPLATE.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("logistics_template");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): logistics_template used time :{}", startPoint, sw.prettyPrint());
                        return logisticsService.auditCount(person);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.PROFITLOSS_EXAMINE.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("profitloss_examine");
                    try {
                        RequestContextHolder.setRequestAttributes(requestAttributes);
                        sw.stop();
                        log.info("agent-task({}): profitloss_examine used time :{}", startPoint, sw.prettyPrint());
                        return Optional.ofNullable(profitLossForecastService.getProfitLossExamine(person)).orElse(0);

                    } catch (Exception ex) {

                    }
                    return 0;
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.POLICY.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("policy");
                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    long count = policySearchService.unreadCountForCurrentMonth(person);
                    sw.stop();
                    log.info("agent-task({}): policy; count:{}; used time: {}", startPoint, count, sw.prettyPrint());
                    return Long.valueOf(count).intValue();
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.ABNORMAL_LOGIN_AUDIT.getType() == e.getAgentType()) {
                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    StopWatch sw = new StopWatch();
                    sw.start("abnormal_login_audit");

                    RequestContextHolder.setRequestAttributes(requestAttributes);
                    AbnormalLoginRequest request = new AbnormalLoginRequest();
                    request.setPerson(personInfo.getEmployeeId());
                    if (StringUtils.isNotBlank(departmentManagerEmpId) && departmentManagerEmpId.equals(person)) {
                        request.setLeoAuditResult(0);
                    } else {
                        request.setAuditResult(0);
                    }
                    request.setRows(9999);
                    try {
                        IPage<AbnormalLoginListVo> list = abnormalLoginService.abnormalLoginPage(request);
                        sw.stop();
                        log.info("agent-task({}): abnormal_login_audit used time :{}", startPoint, sw.prettyPrint());

                        if (Objects.isNull(list)) {
                            return 0;
                        }
                        return Long.valueOf(list.getTotal()).intValue();
                    } catch (Exception ex) {
                        return 0;
                    }
                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            } else if (AgentEnum.CUSTOMER_CLUE.getType() == e.getAgentType()) {

                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
                    BackendCommonReqDto backendCommonReqDto = new BackendCommonReqDto();
                    backendCommonReqDto.setEmployeeCode(person);
                    backendCommonReqDto.setBusinessGroupId(RequestUtils.getBusinessGroup());
                    try {
                        RpcResult<Long> longRpcResult = salesLeadApi.leadsTodo(backendCommonReqDto);
                        return Optional.ofNullable(longRpcResult)
                                .map(RpcResult::getData)
                                .orElse(0L)
                                .intValue();
                    } catch (Exception ex) {
                        log.error("【customer clue】agent exception:{}", ex.getMessage());
                        return 0;
                    }


                }).whenComplete((r, ex) -> {
                    result.put(e, r);
                });
                completableFutures.add(cf);
            }


            // 待设置合伙人目标
//            else if (AgentEnum.PARTNER_GOAL_SET.getType() == e.getAgentType()) {
//                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
//                    StopWatch sw = new StopWatch();
//                    sw.start("partner_goal_set");
//                    RequestContextHolder.setRequestAttributes(requestAttributes);
//                    EmployeeGoalQueryRequest request = new EmployeeGoalQueryRequest();
//                    request.setEmployeeId(person);
//                    request.setYearMonth(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM")));
//                    request.setType(1);
//                    request.setBtnFlg(false);
//                    IPage<EmpGoalVO> pageList = iRealtimeDataService.pageEmpGoalNew(request);
//                    sw.stop();
//                    log.info("agent task partner_goal_set used time :{}", sw.prettyPrint());
//
//                    if (Objects.isNull(pageList)) {
//                        return 0;
//                    }
//                    return Long.valueOf(pageList.getTotal()).intValue();
//
//                }).whenComplete((r, ex) -> {
//                    result.put(e, r);
//                });
//                completableFutures.add(cf);
//            }
//
//            // 待审核合伙人目标
//            else if (AgentEnum.PARTNER_GOAL_AUDIT.getType() == e.getAgentType()) {
//                CompletableFuture<Integer> cf = CompletableFuture.supplyAsync(() -> {
//                    StopWatch sw = new StopWatch();
//                    sw.start("partner_goal_audit");
//                    RequestContextHolder.setRequestAttributes(requestAttributes);
//                    EmployeeGoalQueryRequest request = new EmployeeGoalQueryRequest();
//                    request.setEmployeeId(person);
//                    request.setYearMonth(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM")));
//                    request.setType(2);
//                    IPage<EmpGoalVO> pageList = iRealtimeDataService.pageEmpGoalNew(request);
//                    sw.stop();
//                    log.info("agent task partner_goal_audit used time :{}", sw.prettyPrint());
//
//                    if (Objects.isNull(pageList)) {
//                        return 0;
//                    }
//                    return Long.valueOf(pageList.getTotal()).intValue();
//
//                }).whenComplete((r, ex) -> {
//                    result.put(e, r);
//                });
//                completableFutures.add(cf);
//            }
        }


        // 等待所有任务执行完
        CompletableFuture.allOf(completableFutures.stream().toArray(CompletableFuture[]::new)).join();

        agentVos = convertToVo(result, person, personInfo.getOrganizationId());
        // 读取流程中的待办
        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = Optional.ofNullable(roleEmployeeRelationMapper.selectList(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().eq(RoleEmployeeRelationEntity::getEmployeeId, person).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());
        stopWatch = new StopWatch();
        stopWatch.start("flowService.findAgent");
        List<AgentVo> flowAgentList = flowService.findAgent(person, roleEmployeeRelationEntities.stream().map(RoleEmployeeRelationEntity::getRoleId).distinct().collect(Collectors.toList()), RequestUtils.getBusinessGroup());
        stopWatch.stop();
        log.info("agent-task({}): flowService.findAgent used time :{}", startPoint, stopWatch.prettyPrint());
        if (!CollectionUtils.isEmpty(flowAgentList)) {
            agentVos.addAll(flowAgentList);
        }

        redisUtil.set(agentKey + person, agentVos, expiration);

        log.info("agent-task({}): getAgentList used time :{}", startPoint, System.currentTimeMillis() - startTime);
        return agentVos;
    }

    private List<AgentVo> convertToVo(ConcurrentSkipListMap<AgentEntity, Integer> result, String person, String organizationId) {
        List<AgentVo> list = new ArrayList<>();


        if (CollectionUtils.isEmpty(result)) {
            return list;
        }

        String agentAdventDays = configMapper.getValueByCode("agent_advent_days");

        // 旺金币代办
        List<AgentDTO> walletAgentOrgList = walletAgentService.selectWalletAgentByEmpId(person);
        if (!CollectionUtils.isEmpty(walletAgentOrgList)) {
            walletAgentOrgList.forEach(e -> {
                AgentVo vo = new AgentVo();
                vo.setAgentName("【" + e.getOrganizationName() + "组织】下级旺金币处理");
                vo.setAgentType(AgentEnum.WALLET_AGENT.getType());
                vo.setCount(1);
                vo.setEmphases(1);
                vo.setRoute("/coinsToDoDetails?agentId=" + e.getAgentId());
                list.add(vo);
            });

        }

        result.forEach((key, value) -> {
            if (value > 0) {
                AgentVo vo = new AgentVo();
                vo.setAgentType(key.getAgentType());
                vo.setAgentName(key.getAgentName());
                vo.setRoute(key.getRoute());
                vo.setIcon(key.getIcon());
                vo.setEmphases(0);
                vo.setCount(value);
                if (StringUtils.isNotBlank(agentAdventDays)) {
                    // 月报助手临期检查
                    if (key.getAgentType() == AgentEnum.REVIEW_REPORT_EDIT.getType()) {
                        vo.setAdventCount(reviewReportSearchService.queryAdventCount(person));
                    }
                    // 目标设置
//                    else if (key.getAgentType() == AgentEnum.DEPARTMENT_GOAL.getType() || key.getAgentType() == AgentEnum.COMPANY_GOAL.getType()) {
//                        vo.setAdventCount(organizationGoalService.queryExpirationCount(organizationId, Integer.valueOf(agentAdventDays)));
//                    }
                    else if (key.getAgentType() == AgentEnum.CITY_MANAGER_PERFORMANCE.getType()
                            || key.getAgentType() == AgentEnum.MANAGER_PERFORMANCE.getType()
                            || key.getAgentType() == AgentEnum.AREA_PERFORMANCE.getType()
                    ) {
                        vo.setAdventCount(indicatorsService.performanceAboutExpire(person, Integer.valueOf(agentAdventDays)));
                    }
                }


                list.add(vo);
            }
        });

        return list;
    }

    private int complaintAuditCount(String person) {
        return complaintSearchService.getComplaintAuditCount(person);
    }

    private Integer estimateAuditCount(CeoBusinessOrganizationPositionRelation personInfo, int processStep) {
        int count = 0;
        count = count + estimateStatisticsV2Mapper.getAuditCount(processStep, personInfo.getPositionId(), RequestUtils.getBusinessGroup());
        return count;
    }

    private Integer quotaExportAuditCount(CeoBusinessOrganizationPositionRelation personInfo, int type) {
        int processType = type == 1 ? 2 : 3;
        return sfaGoldProcessRecordMapper.getAuditCount(processType, RequestUtils.getBusinessGroup());
    }

    private Integer hrProcessCount(Integer agentType) {
        ApplyMemberQueryDTO dto = new ApplyMemberQueryDTO();
        dto.setPage(1);
        dto.setRows(9999);
        dto.setShowThirdChannel(0);
        // 入职
        if (agentType == AgentEnum.ON_BOARD.getType()) {

            Page<ApplyMemberVO> applyMemberVOPage = applyMemberService.queryMemberPage(dto);
            if (Objects.isNull(applyMemberVOPage)) {
                return 0;
            }

            return Long.valueOf(applyMemberVOPage.getTotal()).intValue();
        }
        // 离职
        else if (agentType == AgentEnum.OFF_BOARD.getType()) {
            Page<ApplyResignVO> applyResignVOPage = applyMemberService.queryResignPage(dto);
            if (Objects.isNull(applyResignVOPage)) {
                return 0;
            }

            return Long.valueOf(applyResignVOPage.getTotal()).intValue();
        }

        // 异动
        else if (agentType == AgentEnum.JOB_TRANSFER.getType()) {
            PositionTransactionSearchRequest positionTransactionSearchRequest = new PositionTransactionSearchRequest();
            positionTransactionSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
            List<PositionTransactionModel> positionTransactionModels = sfaTransactionApplyMapper.searchList(null, positionTransactionSearchRequest);

            return Optional.ofNullable(positionTransactionModels).orElse(new ArrayList<>()).size();
        }
        return 0;
    }

    /**
     * 获取造旺币审核数量
     *
     * @param employeeId
     * @return
     */
    private Integer quotaAuditCount(String employeeId) {
        List<ActivityQuotaApplicationModel> activityQuotaApplicationModels = activityQuotaApplicationMapper.selectList(new QueryWrapper<ActivityQuotaApplicationModel>()
                .eq("audit_employee_id", employeeId)
                .eq("business_group", RequestUtils.getBusinessGroup())
                .eq("status", 0)
        );
        if (CollectionUtils.isEmpty(activityQuotaApplicationModels)) {
            return 0;
        }

        return activityQuotaApplicationModels.size();
    }

    /**
     * 获取授权审核条数
     *
     * @param personInfo
     * @param agentType
     * @param organizationType
     * @return
     */
    private Integer authorizationCount(CeoBusinessOrganizationPositionRelation personInfo, Integer agentType, String organizationType) {
        // 运营审核
        if (organizationType.equals("zb")) {
            // 检查是否有营运管理的角色
            String employeeId = personInfo.getEmployeeId();

            RoleEmployeeRelationEntity roleEmployeeRelationEntity = roleEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
                    .eq(RoleEmployeeRelationEntity::getEmployeeId, employeeId).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0).last("limit 1"));
            if (Objects.isNull(roleEmployeeRelationEntity)) {
                return 0;
            }

            SfaPositionRelationEntity positionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmpId, employeeId)
                    .eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0).eq(SfaPositionRelationEntity::getPositionTypeId, 7)
                    .eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup()).last("limit 1"));

            List<String> orgList = Arrays.asList(positionRelationEntity.getOrganizationCode());


            return authorizationSearchMapper.selectAutorizationAuditCount(orgList);
        }
        // 总部大区审核
        else {

            List<String> orgList = organizationMapper.getEmployeeOrganizationId(personInfo.getEmployeeId(), RequestUtils.getLoginInfo());

            return authorizationSearchMapper.selectAutorizationAuditCount(orgList);
        }

    }


    // 计算面试待办任务数
    private Integer interviewCount(CeoBusinessOrganizationPositionRelation personInfo, int type) {
        ZWEmployeeInfoRequest interviewInfoRequest = new ZWEmployeeInfoRequest();
        interviewInfoRequest.setEmpId(personInfo.getEmployeeId());

        LoginModel loginInfo = RequestUtils.getLoginInfo();

        List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationPositionRelations = ceoBusinessOrganizationPositionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("position_type_id", loginInfo.getPositionTypeId())
                .eq("employee_id", personInfo.getEmployeeId())
                .eq("business_group", loginInfo.getBusinessGroup())
        );
        if (CollectionUtils.isEmpty(ceoBusinessOrganizationPositionRelations)) {
            throw new ApplicationException("登陆人信息获取失败");
        }
        List<String> orgList = ceoBusinessOrganizationPositionRelations.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
        interviewInfoRequest.setOrganizationIds(orgList);


        // 待面试
        if (type == AgentEnum.INTERVIEW.getType()) {
            interviewInfoRequest.setType(1);
            return izwSignupService.getZWInterviewListCount(interviewInfoRequest);
        }
        // 面试中
        else if (type == AgentEnum.INTERVIEW_PROCESSING.getType()) {
            interviewInfoRequest.setType(2);
            return izwSignupService.getZWInterviewListCount(interviewInfoRequest);
        }
        // 入职申请与审核
        else if (type == AgentEnum.ON_BOARD_APPLY.getType()) {
            ZWDetailedInfoRequest detailedInfoRequest = new ZWDetailedInfoRequest();
            detailedInfoRequest.setEmpId(personInfo.getEmployeeId());
            detailedInfoRequest.setOrganizationIds(orgList);
            return izwSignupService.zwEnrollmentApplicationAndReviewListCount(detailedInfoRequest);
        }
        // 离职审核
        else {
            ResignApprovalQueryDTO dto = new ResignApprovalQueryDTO();
            dto.setEmpId(personInfo.getEmployeeId());
            dto.setOrganizationIds(orgList);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<ApplyApprovalVO> applyApprovalPage =
                    applyMemberService.queryResignApproval(dto);
            return Long.valueOf(applyApprovalPage.getTotal()).intValue();
        }
    }
}
