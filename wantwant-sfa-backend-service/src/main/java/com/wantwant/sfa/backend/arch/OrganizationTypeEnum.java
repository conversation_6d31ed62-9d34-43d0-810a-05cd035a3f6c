package com.wantwant.sfa.backend.arch;

import lombok.Getter;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/03/21/下午1:56
 */
@Getter
public enum OrganizationTypeEnum {
    ZB("zb","总部",1,0),
    AREA("area","战区督导",2,2),
    VARE("varea","大区总监",3,3),
    PROVINCE("province","省区总监",4,4),
    COMPANY("company","区域总监",5,5),
    DEPARTMENT("department","区域经理",6,6),
    BRANCH("branch","合伙人",7,7);

    private String organizationType;

    private String positionName;

    private int order;

    private Integer searchType;

    public static String getPositionName(String organizationType) {
        OrganizationTypeEnum[] values = OrganizationTypeEnum.values();
        for (OrganizationTypeEnum e : values){
            if(e.getOrganizationType().equals(organizationType)){
                return e.getPositionName();
            }
        }

        return "";
    }

    public void setPositionTypeId(String organizationType) {
        this.organizationType = organizationType;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    OrganizationTypeEnum(String organizationType, String positionName, int order, Integer searchType) {
        this.organizationType = organizationType;
        this.positionName = positionName;
        this.order = order;
        this.searchType = searchType;
    }

    public static int getOrder(String organizationType){
        OrganizationTypeEnum[] values = OrganizationTypeEnum.values();
        for (OrganizationTypeEnum e : values){
            if(e.getOrganizationType().equals(organizationType)){
                return e.getOrder();
            }
        }

        return -1;
    }

    public static int getSearchType(String organizationType){
        OrganizationTypeEnum[] values = OrganizationTypeEnum.values();
        for (OrganizationTypeEnum e : values){
            if(e.getOrganizationType().equals(organizationType)){
                return e.getSearchType();
            }
        }

        return -1;
    }
}
