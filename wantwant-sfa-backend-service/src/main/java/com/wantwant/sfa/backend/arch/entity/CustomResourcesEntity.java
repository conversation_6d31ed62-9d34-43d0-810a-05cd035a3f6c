package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 自定义菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@TableName("sfa_custom_resources")
@ApiModel(value = "SfaCustomResources对象", description = "自定义菜单")
@Data
public class CustomResourcesEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "`id`", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("员工工号")
    private String empId;

    @ApiModelProperty("1.快捷菜单")
    private Integer type;

    @ApiModelProperty("菜单ID")
    private Integer resourceId;

    @ApiModelProperty("是否删除(1.是 0.否)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
}
