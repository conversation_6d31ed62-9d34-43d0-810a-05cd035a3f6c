package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午3:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_dept")
@ApiModel(value = "DepartEntity对象", description = "SFA部门")
public class DepartEntity extends CommonEntity {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "dept_name")
    private String deptName;
    @TableField(value = "dept_code")
    private String deptCode;
    @TableField(value = "ancestors",strategy= FieldStrategy.IGNORED)
    private String ancestors;
    @TableField(value = "superior_dept_id")
    private Integer superiorDeptId;
    @TableField(value = "organization_id")
    private String organizationId;
    @TableField(exist = false)
    private Integer employeeSize;
    @TableField(value = "leader_id")
    private String leaderId;
    @TableField(value = "leader_name")
    private String leaderName;
    @TableField(value = "status")
    private Integer status;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("英文名")
    private String nickName;
}
