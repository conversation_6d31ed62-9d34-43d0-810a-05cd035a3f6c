package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午3:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_employee_dept_relation")
@ApiModel(value = "DeptEmployeeRelationEntity对象", description = "SFA部门关系表")
public class DeptEmployeeRelationEntity extends CommonEntity {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Long id;
    @TableField(value = "employee_id")
    private String employeeId;
    @TableField(value = "dept_id")
    private Integer deptId;

}
