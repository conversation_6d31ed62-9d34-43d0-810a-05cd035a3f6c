package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/上午9:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_resources")
@ApiModel(value = "ResourceEntity对象", description = "资源类")
public class ResourceEntity extends CommonEntity {
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "title")
    private String title;
    @TableField(value = "path")
    private String path;
    @TableField(value = "name")
    private String name;
    @TableField(value = "component")
    private String component;
    @TableField(value = "hidden")
    private Integer hidden;
    @TableField(value = "type")
    private String type;
    @TableField(value = "parent_id")
    private Integer parentId;
    @TableField(value = "order_num")
    private Integer orderNum;
    @TableField(value = "icon")
    private String icon;
    @TableField(value = "cache")
    private Integer cache;
    @TableField(value = "redirect")
    private String redirect;
    @TableField(value = "terminal")
    private Integer terminal;
    @TableField(value = "always_show")
    private Integer alwaysShow;
    @TableField(value = "context_menu")
    private String contextMenu;

    private String description;

    private Integer updatePrompt;

    private Integer appSupport;

    @TableField(exist = false)
    private Integer concern;

    @TableField(exist = false)
    private String originalTitle;
}
