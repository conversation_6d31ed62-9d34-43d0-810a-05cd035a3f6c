package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/下午8:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_employee_role_relation")
@ApiModel(value = "RoleEmployeeRelationEntity对象", description = "员工与角色关系表")
public class RoleEmployeeRelationEntity extends CommonEntity {
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @TableField("employee_id")
    private String employeeId;
    @TableField("position_id")
    private String positionId;
    @TableField("role_id")
    private Integer roleId;

}
