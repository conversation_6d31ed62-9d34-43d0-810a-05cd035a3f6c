package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午3:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_role")
@ApiModel(value = "RoleEntity对象", description = "SFA角色信息")
public class RoleEntity extends CommonEntity {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "role_name")
    private String roleName;
    @TableField(value = "terminal")
    private Integer terminal;
    @TableField(value = "data_permission")
    private Integer dataPermission;
    @TableField(value = "description")
    private String description;
    @TableField(exist = false)
    private Integer employeeCount;
}
