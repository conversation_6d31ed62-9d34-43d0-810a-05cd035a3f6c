package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午3:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_role_resources")
@ApiModel(value = "RoleResourcesRelationEntity对象", description = "SFA角色与资源关系")
public class RoleResourcesRelationEntity extends CommonEntity {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "role_id")
    private Integer roleId;
    @TableField(value = "resource_id")
    private Integer resourceId;


    private Integer concern;
}
