package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@TableName("sfa_dept_kpi")
@ApiModel(value = "SfaDeptKpi对象", description = "")
@Data
public class SfaDeptKpi extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("kpi指标")
    private String kpiMetrics;

    @ApiModelProperty("是否可量化(0.否 1.是)")
    private Integer quantify;

    @ApiModelProperty("部门CODE")
    private String deptCode;

    @ApiModelProperty("目标")
    private String target;

    @ApiModelProperty("状态(0.无效 1.有效)")
    private Integer status;

}
