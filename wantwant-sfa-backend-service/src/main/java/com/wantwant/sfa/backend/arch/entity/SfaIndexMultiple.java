package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/上午9:16
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_index_multiple")
@ApiModel(value = "SfaIndexMultiple对象", description = "sfa指标复盘季度")
@Data
public class SfaIndexMultiple {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "报表id")
    @TableField(value = "report_id")
    private String reportId;

    @ApiModelProperty(value = "指标名称")
    @TableField(value = "index_name")
    private String indexName;

    @ApiModelProperty(value = "季度1月数值")
    @TableField(value = "one_value")
    private BigDecimal oneValue;

    @ApiModelProperty(value = "季度1月颜色")
    @TableField(value = "one_color")
    private String oneColor;

    @ApiModelProperty(value = "季度1月单位")
    @TableField(value = "one_unit")
    private String oneUnit;

    @ApiModelProperty(value = "季度2月数值")
    @TableField(value = "two_value")
    private BigDecimal twoValue;

    @ApiModelProperty(value = "季度2月颜色")
    @TableField(value = "two_color")
    private String twoColor;

    @ApiModelProperty(value = "季度2月单位")
    @TableField(value = "two_unit")
    private String twoUnit;

    @ApiModelProperty(value = "季度3月数值")
    @TableField(value = "three_value")
    private BigDecimal threeValue;

    @ApiModelProperty(value = "季度3月颜色")
    @TableField(value = "three_color")
    private String threeColor;

    @ApiModelProperty(value = "季度3月单位")
    @TableField(value = "three_unit")
    private String threeUnit;

    @ApiModelProperty(value = "改善方案")
    @TableField(value = "improvement_plan")
    private String improvementPlan;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "是否删除")
    @TableField("is_delete")
    private Integer isDelete;

}
