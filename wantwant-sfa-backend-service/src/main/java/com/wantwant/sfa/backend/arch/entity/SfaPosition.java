package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@TableName("sfa_position")
@ApiModel(value = "SfaPosition对象", description = "")
@Data
public class SfaPosition extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "position_id", type = IdType.AUTO)
    @TableField("position_id")
    private Long positionId;

    private String positionName;

    @ApiModelProperty("祖先列表")
    private String ancestors;

    @ApiModelProperty("部门CODE")
    private String deptCode;

    @ApiModelProperty("上级岗位ID")
    private Long parentPositionId;

    @ApiModelProperty("状态(1.正常 0.停用)")
    private Integer status;

    @TableField(exist = false)
    private Integer partTime;
}
