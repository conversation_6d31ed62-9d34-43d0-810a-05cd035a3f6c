package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@TableName("sfa_position_duty")
@ApiModel(value = "SfaPositionDuty对象", description = "")
@Data
public class SfaPositionDuty extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long positionId;

    @TableId(value = "duty_id", type = IdType.AUTO)
    private Long dutyId;

    @ApiModelProperty("分类")
    private String category;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("频次")
    private String times;

}
