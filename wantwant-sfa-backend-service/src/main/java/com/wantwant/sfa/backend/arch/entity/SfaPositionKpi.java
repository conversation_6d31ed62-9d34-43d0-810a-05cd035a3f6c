package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@TableName("sfa_position_kpi")
@ApiModel(value = "SfaPositionKpi对象", description = "")
@Data
public class SfaPositionKpi extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("sfa_position主键")
    private Long positionId;

    @ApiModelProperty("kpi指标")
    private String kpiMetrics;

    @ApiModelProperty("达成")
    private String target;

    @ApiModelProperty("是否可量化(0.否 1.是)")
    private Integer quantify;
}
