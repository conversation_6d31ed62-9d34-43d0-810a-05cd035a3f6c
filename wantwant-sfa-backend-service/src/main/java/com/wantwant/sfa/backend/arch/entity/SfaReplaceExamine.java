package com.wantwant.sfa.backend.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/上午9:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_replace_examine")
@ApiModel(value = "SfaReplaceExamine对象", description = "汰换审核类")
public class SfaReplaceExamine {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "月份")
    @TableField(value = "the_year_month")
    private String theYearMonth;

    @ApiModelProperty(value = "员工表ID")
    @TableField(value = "employee_info_id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "组织ID")
    @TableField(value = "organization_id")
    private String organizationId;

    @ApiModelProperty(value = "审核结果(1.同意汰换 2.申请特批)")
    @TableField(value = "examine_type")
    private Integer examineType;

    @ApiModelProperty(value = "审核意见")
    @TableField(value = "examine_idea")
    private String examineIdea;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_people")
    private String createPeople;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_people")
    private String updatePeople;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField(value = "is_delete")
    private Integer isDelete;
}
