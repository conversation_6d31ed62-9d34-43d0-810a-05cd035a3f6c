package com.wantwant.sfa.backend.arch.model;

import com.alibaba.fastjson.JSONArray;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/28/下午2:25
 */
@Data
public class RealtimeDateModel {

    private List<JsonObject> areaDataJson = new ArrayList<>();
    private List<JsonObject> vareaDataJson = new ArrayList<>();
    private List<JsonObject> provinceDataJson = new ArrayList<>();
    private List<JsonObject> companyDataJson = new ArrayList<>();
    private List<JsonObject> branchDataJson = new ArrayList<>();

    private List<JsonObject> newAreaDataJson = new ArrayList<>();
    private List<JsonObject> newVAreaDataJson = new ArrayList<>();
    private List<JsonObject> newProvinceDataJson = new ArrayList<>();
    private List<JsonObject> newCompanyDataJson = new ArrayList<>();
    private List<JsonObject> newBranchDataJson = new ArrayList<>();


    // 大区
    JSONArray areaJsonArray = new JSONArray();
    // 虚拟大区
    JSONArray vareaJsonArray = new JSONArray();
    // 省
    JSONArray provinceJsonArray = new JSONArray();
    //分公司
    JsonArray companyJsonArray = new JsonArray();
    //营业所
    JsonArray branchJsonArray = new JsonArray();

    boolean isNotBranch = true;

    // 大区菜单
    JsonArray areaDataJsonArray = new JsonArray();
    // 虚拟大区菜单
    JSONArray vareaDataJsonArray = new JSONArray();
    // 省菜单
    JSONArray provinceDataJsonArray = new JSONArray();
    //分公司菜单
    JsonArray companyDataJsonArray = new JsonArray();
    //营业所菜单
    JsonArray branchDataJsonArray = new JsonArray();

    // 新大区菜单
    JsonArray newAreaDataJsonArray = new JsonArray();
    // 新虚拟大区菜单
    JSONArray newVareaDataJsonArray = new JSONArray();
    // 新省菜单
    JSONArray newProvinceDataJsonArray = new JSONArray();
    // 新营业所菜单
    JsonArray newBranchDataJsonArray = new JsonArray();
    // 新分公司菜单
    JsonArray newCompanyDataJsonArray = new JsonArray();

}
