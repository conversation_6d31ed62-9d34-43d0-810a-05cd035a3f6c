package com.wantwant.sfa.backend.arch.service;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.request.*;
import com.wantwant.sfa.backend.arch.vo.AccountInfoVo;
import com.wantwant.sfa.backend.arch.vo.AccountVo;
import com.wantwant.sfa.backend.arch.vo.QuitTaskInfoVo;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/下午7:08
 */
public interface IAccountService {

    /**
     * 获取账号列表
     *
     * @param request
     * @return
     */
    Page<AccountVo> selectList(SAccountRequest request);

    /**
     * 创建岗位
     *
     * @param request
     */
    void create(CAccountRequest request);

    /**
     * 编辑角色
     *
     * @param request
     */
    void edit(CAccountRequest request);

    /**
     * 账户详情
     *
     * @param employeeId
     * @return
     */
    AccountInfoVo getAccountInfo(String employeeId,String positionId);

    /**
     * 增加角色
     *
     * @param modifyRolesRequest
     */
    void addRoles(ModifyRolesRequest modifyRolesRequest);

    /**
     * 删除角色
     *
     * @param modifyRolesRequest
     */
    void deleteRoles(ModifyRolesRequest modifyRolesRequest);

    /**
     * 复制角色
     *
     * @param request
     */
    void copyRoles(CopyRolesRequest request);

    /**
     * 离职
     *
     * @param request
     */
    void quit(AccountQuitRequest request);

    /**
     * 获取离职任务详情
     *
     * @param employeeId
     * @return
     */
    QuitTaskInfoVo quitTaskInfo(String employeeId);

}
