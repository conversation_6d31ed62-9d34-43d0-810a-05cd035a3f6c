package com.wantwant.sfa.backend.arch.service;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.request.CDeptRequest;
import com.wantwant.sfa.backend.arch.request.DeptOperatorRequest;
import com.wantwant.sfa.backend.arch.request.EDeptRequest;
import com.wantwant.sfa.backend.arch.request.SDeptRequest;
import com.wantwant.sfa.backend.arch.vo.*;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午4:23
 */
public interface IDeptService {
    /**
     * 创建部门
     *
     * @param cDeptRequest
     */
    void createDept(CDeptRequest cDeptRequest);

    /**
     * 修改部门
     *
     * @param eDeptRequest
     */
    void update(EDeptRequest eDeptRequest);

    /**
     * 获取部门明细
     *
     * @param id
     * @return
     */
    DeptInfoVo getDeptInfo(int id);

    /**
     * 获取部门列表
     *
     * @return
     */
    List<DeptVo> selectDeptList(SDeptRequest request);

    /**
     * 部门下拉框查询
     *
     * @param deptId
     * @return
     */
    List<DeptSelectVo> departSelect(Integer deptId);

    /**
     * 根据部门CODE获取用户列表
     *
     * @param deptCode
     * @return
     */
    List<UserVo> getUserListByDeptCode(String deptCode);

    /**
     * 根据部门CODE获取对应的岗位
     *
     * @param deptCode
     * @return
     */
    List<DeptPositionVo> getPositionListByDeptId(Integer deptId);

    /**
     * 根据员工工号获取部门信息
     *
     * @param empId
     * @return
     */
    List<DeptSelectVo> departSelectByEmpId(String empId);

    /**
     * 删除部门
     *
     * @param deptOperatorRequest
     */
    void delete(DeptOperatorRequest deptOperatorRequest);

}
