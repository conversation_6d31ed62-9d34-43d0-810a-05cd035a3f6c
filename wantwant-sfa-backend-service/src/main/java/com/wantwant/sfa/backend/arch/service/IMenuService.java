package com.wantwant.sfa.backend.arch.service;

import com.google.gson.JsonObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.request.CustomMenuRequest;
import com.wantwant.sfa.backend.arch.vo.ModuleVo;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/23/下午4:04
 */
public interface IMenuService {
    /**
     * 获取菜单
     *
     * @param person
     * @return
     */
    Object getMenu(String person);

    /**
     * 自定义菜单
     *
     * @param customMenuRequest
     */
    void customMenu(CustomMenuRequest customMenuRequest);

    /**
     * 检查是否有完成过指引
     *
     * @param empId
     * @return
     */
    Boolean checkGuidanceFinish(String empId);

    /**
     * 记录一看完指引
     *
     * @param empId
     */
    void finishGuide(String empId);
}
