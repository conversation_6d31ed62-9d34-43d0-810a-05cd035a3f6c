package com.wantwant.sfa.backend.arch.service;

import com.wantwant.sfa.backend.arch.request.CPositionRequest;
import com.wantwant.sfa.backend.arch.request.PositionOperatorRequest;
import com.wantwant.sfa.backend.arch.request.PositionSearchRequest;
import com.wantwant.sfa.backend.arch.request.UPositionRequest;
import com.wantwant.sfa.backend.arch.vo.PositionDetailVo;
import com.wantwant.sfa.backend.arch.vo.PositionSelectVo;
import com.wantwant.sfa.backend.arch.vo.PositionVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/08/下午6:59
 */
public interface IPositionService {

    /**
     * 创建岗位
     *
     * @param positionRequest
     */
    void create(CPositionRequest positionRequest);

    /**
     * 岗位修改
     *
     * @param positionRequest
     */
    void modify(UPositionRequest positionRequest);

    /**
     * 岗位查询
     *
     * @param positionSearchRequest
     * @return
     */
    List<PositionVo> selectList(PositionSearchRequest positionSearchRequest);

    List<PositionSelectVo> selector();

    PositionDetailVo detail(Long positionId);

    void deletePosition(PositionOperatorRequest positionOperatorRequest);

}
