package com.wantwant.sfa.backend.arch.service;

import com.wantwant.sfa.backend.arch.request.CRoleRequest;
import com.wantwant.sfa.backend.arch.request.DRoleRequest;
import com.wantwant.sfa.backend.arch.request.ERoleRequest;
import com.wantwant.sfa.backend.arch.request.SRoleRequest;
import com.wantwant.sfa.backend.arch.vo.EmployeeRoleVo;
import com.wantwant.sfa.backend.arch.vo.RoleInfoVo;
import com.wantwant.sfa.backend.arch.vo.RoleSelectVo;
import com.wantwant.sfa.backend.arch.vo.RoleVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:11
 */
public interface IRoleService {

    /**
     * 创建角色
     *
     * @param cRoleRequest
     */
    void create(CRoleRequest cRoleRequest);

    /**
     * 编辑角色
     *
     * @param eRoleRequest
     */
    void edit(ERoleRequest eRoleRequest);

    /**
     * 查询角色
     *
     * @param request
     * @return
     */
    List<RoleVo> selectRoles(SRoleRequest request);

    /**
     * 获取角色明细
     *
     * @param id
     * @return
     */
    RoleInfoVo getRoleDetail(Integer id);

    /**
     * 删除角色
     *
     * @param request
     */
    void deleteRole(DRoleRequest request);

    /**
     * 获取角色下拉框
     *
     * @param terminal
     * @return
     */
    List<RoleSelectVo> getRoleSelect(Integer terminal);

    /**
     * 获取员工角色
     *
     * @param employeeId
     * @return
     */
    List<EmployeeRoleVo> selectEmployeeRoles(String employeeId);
}
