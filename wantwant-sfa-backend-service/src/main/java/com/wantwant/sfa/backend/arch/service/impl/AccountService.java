package com.wantwant.sfa.backend.arch.service.impl;

import cn.hutool.core.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.arch.entity.*;
import com.wantwant.sfa.backend.arch.model.AccountModel;
import com.wantwant.sfa.backend.arch.request.*;
import com.wantwant.sfa.backend.arch.service.IAccountService;
import com.wantwant.sfa.backend.arch.vo.*;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.employee.request.CancelChangeRequest;
import com.wantwant.sfa.backend.employee.request.ManualEntryRequest;
import com.wantwant.sfa.backend.employee.request.QuitRequest;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.EmployeeMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.*;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelationTask;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.IEmployeeService;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/下午7:08
 */
@Service
public class AccountService implements IAccountService {
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Autowired
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private IEmployeeService employeeService;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private SfaPositionEmpMapper sfaPositionEmpMapper;

    @Autowired
    private SfaPositionEmployeeRelationMapper employeeRelationMapper;
    @Resource
    private IEmpService empService;

    @Override
    public Page<AccountVo> selectList(SAccountRequest request) {

        List<AccountModel> list = accountMapper.selectList(request, RequestUtils.getChannel(),RequestUtils.getBusinessGroup());
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        Page<AccountVo>  page = new Page<AccountVo>();
        Integer count = accountMapper.selectListCount(request, RequestUtils.getChannel(),RequestUtils.getBusinessGroup());
        page.setTotalItem(count);
        page.setTotalPage(PageUtil.totalPage(count,request.getRows()));

        List<AccountVo> result = new ArrayList<>();
        list.forEach(e -> {
            AccountVo vo = new AccountVo();
            BeanUtils.copyProperties(e,vo);

            if(Objects.nonNull(e.getOnboardTime())){
                vo.setOnboardTime(e.getOnboardTime().toString());
            }
            result.add(vo);
        });

        page.setList(result);

        return page;
    }

    @Override
    @Transactional
    public void create(CAccountRequest request) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        ProcessUserDO processUserDO = empService.getUserById(request.getPerson());
        // 添加入职计划
        String positionId = addEntryTask(request);


        // 修改角色
        bindRole(request.getEmployeeId(),request.getRoleIds(),processUserDO,positionId);
        // 修改部门
        List<Integer> deptIds = request.getDeptIds().stream().map(CAccountDeptRequest::getDeptId).distinct().collect(Collectors.toList());
        bindDept(request.getEmployeeId(),deptIds,processUserDO);
        //添加sfa_position_emp表
        saveOrUpdatePositionEmployee(request);
    }

    private String addEntryTask(CAccountRequest request) {
        String organizationId = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
        // 获取空闲岗位
        String position = ceoBusinessOrganizationPositionRelationMapper.getEmptyPosition(organizationId, RequestUtils.getChannel());
        if(StringUtils.isBlank(position)){
            throw new ApplicationException("暂无空缺岗位");
        }

        ManualEntryRequest manualEntryRequest = new ManualEntryRequest();
        manualEntryRequest.setPositionId(position);
        manualEntryRequest.setEmployeeId(request.getEmployeeId());
        manualEntryRequest.setChangeDate(LocalDate.parse(request.getOnboardTime()));
        manualEntryRequest.setEmployeeName(request.getEmployeeName());
        manualEntryRequest.setPerson(request.getPerson());
        employeeService.changeEntry(manualEntryRequest, RequestUtils.getChannel());
        return position;
    }

    @Override
    @Transactional
    public void edit(CAccountRequest request) {

        ProcessUserDO processUserDO = empService.getUserById(request.getPerson());

        if(request.isChangeCancel()){
            // 获取入职计划
            CeoBusinessOrganizationPositionRelationTask task = employeeMapper.getChangeTask(request.getPositionId(),1,RequestUtils.getChannel());

            if(Objects.isNull(task)){
                throw new ApplicationException("获取入职计划失败");
            }
            CancelChangeRequest cancelChangeRequest = new CancelChangeRequest();
            cancelChangeRequest.setTaskId(task.getId().toString());
            cancelChangeRequest.setPerson(processUserDO.getEmployeeId());
            employeeService.cancelChange(cancelChangeRequest);

            // 解除部门，角色
            bindRole(request.getEmployeeId(), ListUtils.EMPTY_LIST,processUserDO,task.getPositionId());
            bindDept(request.getEmployeeId(),ListUtils.EMPTY_LIST,processUserDO);
            //保存

        }else{

            CeoBusinessOrganizationPositionRelation positionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", request.getEmployeeId())
                    .eq("position_id",request.getPositionId())
                    .eq("channel", RequestUtils.getChannel())
            );

            if(Objects.isNull(positionRelation)){
                // 获取入职计划
                CeoBusinessOrganizationPositionRelationTask task = employeeMapper.getChangeTask(request.getPositionId(),1,RequestUtils.getChannel());
                if(Objects.isNull(task)){
                    throw new ApplicationException("员工信息获取失败");
                }
                task.setChangeDate(LocalDate.parse(request.getOnboardTime()));
                employeeMapper.updateChangeDateTask(task.getId(),task.getEmployeeId(),task.getEmployeeName(),LocalDate.parse(request.getOnboardTime()),processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
            }else{
                positionRelation.setOnboardTime(LocalDate.parse(request.getOnboardTime()).atStartOfDay());
                positionRelation.setEmployeeName(request.getEmployeeName());
                ceoBusinessOrganizationPositionRelationMapper.updateById(positionRelation);
            }

            // 修改角色
            bindRole(request.getEmployeeId(),request.getRoleIds(),processUserDO,positionRelation.getPositionId());
            // 修改部门
            List<Integer> deptIds = request.getDeptIds().stream().map(CAccountDeptRequest::getDeptId).collect(Collectors.toList());
            bindDept(request.getEmployeeId(),deptIds,processUserDO);
            //保存
            saveOrUpdatePositionEmployee(request);
        }
    }


    @Override
    public AccountInfoVo getAccountInfo(String employeeId,String positionId) {
        List<AccountModel> accountModelList = accountMapper.getAccountInfo(employeeId,positionId,RequestUtils.getChannel());
        if(CollectionUtils.isEmpty(accountModelList)){
            throw new ApplicationException("账号信息获取失败");
        }

        Optional<AccountModel> first = accountModelList.stream().filter(f -> Objects.nonNull(f)).findFirst();
        if(!first.isPresent()){
            throw new ApplicationException("账号信息获取失败");
        }
        AccountModel accountModel = first.get();
        AccountInfoVo vo = new AccountInfoVo();
        BeanUtils.copyProperties(accountModel,vo);

        if(Objects.nonNull(accountModel.getOnboardTime())){
            vo.setOnboardTime(accountModel.getOnboardTime().toString());
        }

        if(StringUtils.isNotBlank(accountModel.getDeptCodes())){
            List<Integer> deptIds = Arrays.asList(accountModel.getDeptCodes().split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(deptIds)){
                List<DepartEntity> departEntities = deptMapper.selectList(new QueryWrapper<DepartEntity>().in("id", deptIds).eq("delete_flag", 0));
                List<AccountInfoDetailVo> detailVoList = new ArrayList<>();
                if(!CollectionUtils.isEmpty(departEntities)){
                    departEntities.forEach(e -> {
                        AccountInfoDetailVo detailVo = new AccountInfoDetailVo();
                        List<Integer> path = new ArrayList<>();
                        String ancestors = e.getAncestors();

//                        if(StringUtils.isNotBlank(ancestors)){
//
//                            List<DepartEntity> ancestorList = deptMapper.selectList(new QueryWrapper<DepartEntity>().in("dept_code", Arrays.asList(ancestors.split(","))).eq("delete_flag", 0));
//
//                            ancestorList.forEach(a -> {
//                                path.add(a.getId());
//                            });
//                        }

                        path.add(e.getId());
                        detailVo.setDeptIds(path);
                        detailVo.setDeptCode(e.getDeptCode());
                        SfaPosition sfaPosition = sfaPositionEmpMapper.getSfaPositionEmpByEmployeeId(accountModel.getEmployeeId(), e.getDeptCode());
                        if(!Objects.isNull(sfaPosition)) {
                            detailVo.setPositionName(sfaPosition.getPositionName());
                            detailVo.setPositionId(sfaPosition.getPositionId());
                            detailVo.setPartTime(sfaPosition.getPartTime());
                        }
                        detailVoList.add(detailVo);
                    });
                }
                vo.setDeptList(detailVoList);
            }
        }

        if(StringUtils.isNotBlank(accountModel.getRoleCodes())){
            List<Integer> roleIds = Arrays.asList(accountModel.getRoleCodes().split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            List<RoleEntity> roleEntities = roleMapper.selectList(new QueryWrapper<RoleEntity>().in("id", roleIds).eq("delete_flag", 0));
            if(!CollectionUtils.isEmpty(roleEntities)){
                List<RoleSelectVo> roleList = new ArrayList<>();
                roleEntities.stream().filter(f -> f.getDeleteFlag() == 0).forEach(e -> {
                    RoleSelectVo roleInfoVo = new RoleSelectVo();
                    BeanUtils.copyProperties(e,roleInfoVo);
                    roleInfoVo.setRoleId(e.getId());
                    roleList.add(roleInfoVo);
                });

                vo.setRoleIds(roleList);
            }
        }
        return vo;
    }

    @Override
    @Transactional
    public void addRoles(ModifyRolesRequest modifyRolesRequest) {
        String person = modifyRolesRequest.getPerson();
        ProcessUserDO processUserDO = empService.getUserById(person);

        // 绑定角色
        modifyRolesRequest.getEmployeeInfos().stream().forEach(e -> {
            bindRole(e.getEmpId(),modifyRolesRequest.getRoles(),processUserDO,e.getPositionId());
        });

    }

    @Override
    @Transactional
    public void deleteRoles(ModifyRolesRequest modifyRolesRequest) {
        String person = modifyRolesRequest.getPerson();
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person,loginInfo);

        // 绑定角色
        modifyRolesRequest.getEmployeeInfos().stream().forEach(e -> {
            unbindRole(e.getEmpId(),modifyRolesRequest.getRoles(),personInfo,e.getPositionId());
        });
    }

    @Override
    @Transactional
    public void copyRoles(CopyRolesRequest request) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        ProcessUserDO processUserDO = empService.getUserById(request.getPerson());
        ModifyEmpInfoRequest originalEmp = request.getOriginalEmp();

        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new QueryWrapper<RoleEmployeeRelationEntity>()
                .eq("employee_id", originalEmp.getEmpId())
                .eq("position_id",originalEmp.getPositionId())
                .eq("delete_flag", 0)
        );

        if(CollectionUtils.isEmpty(roleEmployeeRelationEntities)){
            throw new ApplicationException("选择的员工无可用角色");
        }

        List<Integer> roleIds = new ArrayList<>();
        roleEmployeeRelationEntities.forEach(e -> roleIds.add(e.getRoleId()));

        request.getCopyToEmpList().stream().forEach(e -> {
            bindRole(e.getEmpId(),roleIds,processUserDO,e.getPositionId());
        });
    }

    @Override
    @Transactional
    public void quit(AccountQuitRequest request) {

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);
        // 放弃离职
        if(request.isCancel()){
            // 获取入职计划
            CeoBusinessOrganizationPositionRelationTask task = employeeMapper.getChangeTask(request.getPositionId(),2,RequestUtils.getChannel());

            if(Objects.isNull(task)){
                throw new ApplicationException("获取入职计划失败");
            }
            CancelChangeRequest cancelChangeRequest = new CancelChangeRequest();
            cancelChangeRequest.setTaskId(task.getId().toString());
            cancelChangeRequest.setPerson(personInfo.getEmployeeId());
            employeeService.cancelChange(cancelChangeRequest);
            return;
        }


        CeoBusinessOrganizationPositionRelation quitPerson = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id",request.getPositionId())
        );


        // 离职
        QuitRequest quitRequest = new QuitRequest();
        quitRequest.setChangeDate(LocalDate.parse(request.getOffboardTime()));
        quitRequest.setPositionId(quitPerson.getPositionId());
        quitRequest.setPerson(personInfo.getEmployeeId());

        employeeService.changeQuit(quitRequest,RequestUtils.getChannel());
    }

    @Override
    public QuitTaskInfoVo quitTaskInfo(String positionId) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();


        CeoBusinessOrganizationPositionRelation personInfo = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id",positionId)
        );
        // 获取入职计划
        CeoBusinessOrganizationPositionRelationTask task = employeeMapper.getChangeTask(positionId,2,RequestUtils.getChannel());
        QuitTaskInfoVo vo = new QuitTaskInfoVo();
        vo.setEmployeeName(personInfo.getEmployeeName());
        if(Objects.nonNull(task) && Objects.nonNull(task.getChangeDate())){
            vo.setQuitTime(task.getChangeDate().toString());
        }

        return vo;
    }

    private void unbindRole(String employeeId, List<Integer> roles, CeoBusinessOrganizationPositionRelation personInfo,String positionId) {
        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new QueryWrapper<RoleEmployeeRelationEntity>().eq("employee_id", employeeId)
                .eq("position_id",positionId)
                .in("role_id", roles).eq("delete_flag", 0));
        roleEmployeeRelationEntities.forEach( e -> {
            e.setDeleteFlag(1);
            e.setUpdateUserName(personInfo.getEmployeeName());
            e.setUpdateTime(LocalDateTime.now());
            e.setUpdateUserId(personInfo.getEmployeeId());
        });
    }

    public void bindDept(String employeeId, List<Integer> deptIds, ProcessUserDO processUserDO ) {

        List<DeptEmployeeRelationEntity> deptEmployeeRelationEntities = deptEmployeeRelationMapper.selectList(new QueryWrapper<DeptEmployeeRelationEntity>().eq("employee_id", employeeId));

        if(CollectionUtils.isEmpty(deptIds) && CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
            return;
        }

        if(CollectionUtils.isEmpty(deptIds) && !CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
            deptEmployeeRelationEntities.stream().filter(f -> f.getDeleteFlag() == 0).forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateUserId(processUserDO.getEmployeeId());
                e.setUpdateUserName(processUserDO.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                deptEmployeeRelationMapper.updateById(e);
            });
            return;
        }

        if(!CollectionUtils.isEmpty(deptIds) && CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
            deptIds.forEach(e -> {
                saveDeptRelation(employeeId, processUserDO, e);
            });
            return;
        }

        deptEmployeeRelationEntities.forEach(e -> {
            Optional<Integer> first = deptIds.stream().filter(f -> f.equals(e.getDeptId())).findFirst();
            if(!first.isPresent()){
                e.setDeleteFlag(1);
                e.setUpdateUserId(processUserDO.getEmployeeId());
                e.setUpdateUserName(processUserDO.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                deptEmployeeRelationMapper.updateById(e);
            }
        });

        deptIds.forEach(e -> {
            Optional<DeptEmployeeRelationEntity> first = deptEmployeeRelationEntities.stream().filter(f -> f.getDeptId().equals(e)).findFirst();
            if(!first.isPresent()){
                saveDeptRelation(employeeId, processUserDO, e);
            }else{
                DeptEmployeeRelationEntity deptEmployeeRelationEntity = first.get();
                if(deptEmployeeRelationEntity.getDeleteFlag() == 1){
                    deptEmployeeRelationEntity.setDeleteFlag(0);
                    deptEmployeeRelationEntity.setUpdateUserId(processUserDO.getEmployeeId());
                    deptEmployeeRelationEntity.setUpdateUserName(processUserDO.getEmployeeName());
                    deptEmployeeRelationEntity.setUpdateTime(LocalDateTime.now());
                    deptEmployeeRelationMapper.updateById(deptEmployeeRelationEntity);
                }
            }
        });
    }

    private void saveOrUpdatePositionEmployee(CAccountRequest request) {
        if(StringUtils.isEmpty(request.getEmployeeId())) {
           throw new ApplicationException("传入工号为空");
        }
        List<SfaPositionEmp> list = sfaPositionEmpMapper.selectList(new LambdaQueryWrapper<SfaPositionEmp>().eq(SfaPositionEmp::getEmpId, request.getEmployeeId())
                                                                                                            .eq(SfaPositionEmp::getDeleteFlag, 0));
        Long positionEmpId = null;
        if(CollectionUtils.isEmpty(list)) {
            SfaPositionEmp sfaPositionEmp = new SfaPositionEmp();
            sfaPositionEmp.setEmpId(request.getEmployeeId());
            sfaPositionEmp.setAvator(request.getAvator());
            sfaPositionEmp.setJobLevel(request.getJobLevel());
            sfaPositionEmp.setCreateTime(LocalDateTime.now());
            sfaPositionEmp.setCreateUserId(request.getPerson());
            sfaPositionEmpMapper.insert(sfaPositionEmp);
            positionEmpId = sfaPositionEmp.getId();
        }else if(list.size() == 1){
            SfaPositionEmp sfaPositionEmp = list.get(0);
            sfaPositionEmp.setEmpId(request.getEmployeeId());
            sfaPositionEmp.setAvator(request.getAvator());
            sfaPositionEmp.setJobLevel(request.getJobLevel());
            sfaPositionEmp.setUpdateTime(LocalDateTime.now());
            sfaPositionEmp.setUpdateUserId(request.getPerson());
            sfaPositionEmpMapper.updateById(sfaPositionEmp);
            positionEmpId = sfaPositionEmp.getId();
        }else {
            throw new ApplicationException("员工信息重复");
        }
        //处理人员与岗位的关系
        List<SfaPositionEmployeeRelation> relationList = employeeRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionEmployeeRelation>().eq(SfaPositionEmployeeRelation::getPositionEmpId, positionEmpId)
                                                                               .eq(SfaPositionEmployeeRelation::getDeleteFlag, 0));

        if(!CollectionUtils.isEmpty(relationList)) {
            relationList.forEach(e->{
                e.setDeleteFlag(1);
                e.setUpdateUserId(request.getPerson());
                e.setUpdateTime(LocalDateTime.now());
                employeeRelationMapper.updateById(e);
            });
        }
        List<CAccountDeptRequest> deptRequestList =  request.getDeptIds();

        Long finalPositionEmpId = positionEmpId;
        deptRequestList.forEach(e -> {
            Long positionId = e.getPositionId();
            if(Objects.nonNull(positionId)){
                SfaPositionEmployeeRelation sfaPositionEmployeeRelation = new SfaPositionEmployeeRelation();
                sfaPositionEmployeeRelation.setPositionId(positionId);
                sfaPositionEmployeeRelation.setPositionEmpId(finalPositionEmpId);
                sfaPositionEmployeeRelation.setCreateTime(LocalDateTime.now());
                sfaPositionEmployeeRelation.setCreateUserId(request.getPerson());
                sfaPositionEmployeeRelation.setPartTime(e.getPartTime());
                employeeRelationMapper.insert(sfaPositionEmployeeRelation);
            }
        });

    }


    private void saveDeptRelation(String employeeId, ProcessUserDO processUserDO, Integer deptId) {
        DeptEmployeeRelationEntity deptEmployeeRelationEntity = new DeptEmployeeRelationEntity();
        deptEmployeeRelationEntity.setDeptId(deptId);
        deptEmployeeRelationEntity.setEmployeeId(employeeId);
        deptEmployeeRelationEntity.setDeleteFlag(0);
        deptEmployeeRelationEntity.setCreateUserId(processUserDO.getEmployeeId());
        deptEmployeeRelationEntity.setCreateUserName(processUserDO.getEmployeeName());
        deptEmployeeRelationEntity.setCreateTime(LocalDateTime.now());
        deptEmployeeRelationEntity.setUpdateUserId(processUserDO.getEmployeeId());
        deptEmployeeRelationEntity.setUpdateUserName(processUserDO.getEmployeeName());
        deptEmployeeRelationEntity.setUpdateTime(LocalDateTime.now());
        deptEmployeeRelationMapper.insert(deptEmployeeRelationEntity);
    }

    public void bindRole(String employeeId, List<Integer> roleIds,ProcessUserDO processUserDO,String positionId) {


        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new QueryWrapper<RoleEmployeeRelationEntity>()
                .eq("employee_id", employeeId)
                .eq("position_id",positionId)
        );

        if(CollectionUtils.isEmpty(roleIds) && CollectionUtils.isEmpty(roleEmployeeRelationEntities)){
            return;
        }

        if(CollectionUtils.isEmpty(roleIds) && !CollectionUtils.isEmpty(roleEmployeeRelationEntities)){
            roleEmployeeRelationEntities.stream().filter(f -> f.getDeleteFlag() == 0).forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateUserId(processUserDO.getEmployeeId());
                e.setUpdateUserName(processUserDO.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                roleEmployeeRelationMapper.updateById(e);
            });
            return;
        }

        if(!CollectionUtils.isEmpty(roleIds) && CollectionUtils.isEmpty(roleEmployeeRelationEntities)){
            roleIds.forEach(e -> {
                saveRoleRelation(employeeId, processUserDO, e,positionId);
            });
            return;
        }

        roleEmployeeRelationEntities.forEach(e -> {
            Optional<Integer> first = roleIds.stream().filter(f -> f.equals(e.getRoleId())).findFirst();
            if(!first.isPresent()){
                e.setDeleteFlag(1);
                e.setUpdateUserId(processUserDO.getEmployeeId());
                e.setUpdateUserName(processUserDO.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                roleEmployeeRelationMapper.updateById(e);
            }
        });

        roleIds.forEach(e -> {
            Optional<RoleEmployeeRelationEntity> first = roleEmployeeRelationEntities.stream().filter(f -> f.getRoleId().equals(e)).findFirst();
            if(!first.isPresent()){
                saveRoleRelation(employeeId, processUserDO, e,positionId);
            }else{
                RoleEmployeeRelationEntity roleEmployeeRelationEntity = first.get();
                if(roleEmployeeRelationEntity.getDeleteFlag() == 1){
                    roleEmployeeRelationEntity.setDeleteFlag(0);
                    roleEmployeeRelationEntity.setUpdateUserId(processUserDO.getEmployeeId());
                    roleEmployeeRelationEntity.setUpdateUserName(processUserDO.getEmployeeName());
                    roleEmployeeRelationEntity.setUpdateTime(LocalDateTime.now());
                    roleEmployeeRelationMapper.updateById(roleEmployeeRelationEntity);
                }
            }
        });
    }

    private void saveRoleRelation(String employeeId, ProcessUserDO processUserDO, Integer e,String positionId) {
        RoleEmployeeRelationEntity roleEmployeeRelationEntity = new RoleEmployeeRelationEntity();
        roleEmployeeRelationEntity.setRoleId(e);
        roleEmployeeRelationEntity.setPositionId(positionId);
        roleEmployeeRelationEntity.setEmployeeId(employeeId);
        roleEmployeeRelationEntity.setDeleteFlag(0);
        roleEmployeeRelationEntity.setCreateUserId(processUserDO.getEmployeeId());
        roleEmployeeRelationEntity.setCreateUserName(processUserDO.getEmployeeName());
        roleEmployeeRelationEntity.setCreateTime(LocalDateTime.now());
        roleEmployeeRelationEntity.setUpdateUserId(processUserDO.getEmployeeId());
        roleEmployeeRelationEntity.setUpdateUserName(processUserDO.getEmployeeName());
        roleEmployeeRelationEntity.setUpdateTime(LocalDateTime.now());
        roleEmployeeRelationMapper.insert(roleEmployeeRelationEntity);
    }
}
