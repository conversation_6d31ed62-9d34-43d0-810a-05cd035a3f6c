package com.wantwant.sfa.backend.arch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.arch.service.IArchService;
import com.wantwant.sfa.backend.arch.vo.ArchEmpVo;
import com.wantwant.sfa.backend.arch.vo.ArchVo;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.util.BeanUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/15/下午12:06
 */
@Service
public class ArchService implements IArchService {
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Override
    public ArchVo getArch(String deptCode) {
        DepartEntity entity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("dept_code", deptCode));
        if(Objects.isNull(entity)){
            throw new ApplicationException("部门CODE不存在");
        }

        ArchVo vo = new ArchVo();
        vo.setType(1);
        vo.setDeptId(entity.getId());
        vo.setDeptName(entity.getDeptName());
        vo.setDeptCode(entity.getDeptCode());

        List<ArchVo> children = new ArrayList<>();
        // 获取部门下的人员
        List<ArchVo> empVos = getDeptEmpList(entity.getDeptCode(),entity.getLeaderId());
        if(!CollectionUtils.isEmpty(empVos)){
            children.addAll(empVos);

            Optional<ArchVo> first = empVos.stream().filter(f -> f.getIsLeader() == 1).findFirst();
            if(first.isPresent()){
                ArchVo archVo = first.get();
                vo.setEmpId(archVo.getEmpId());
                vo.setPartTime(archVo.getPartTime());
                vo.setEmpName(archVo.getEmpName());
                vo.setAvatar(archVo.getAvatar());
                vo.setPositionId(archVo.getPositionId());
            }
        }


        // 获取所有子孙节点
        List<ArchVo> nextDeptList = getChildren(entity.getDeptCode(), entity.getId());
        if(!CollectionUtils.isEmpty(nextDeptList)){
            children.addAll(nextDeptList);
        }
        vo.setChildren(children);
        return vo;
    }

    private List<ArchVo> getChildren(String deptCode,Integer deptId) {
        // 根据部门code获取所有子孙节点
        List<DepartEntity> departEntities = deptMapper.selectList(new QueryWrapper<DepartEntity>().like("ancestors", deptCode).eq("delete_flag", 0).eq("status", 1));
        if(CollectionUtils.isEmpty(departEntities)){
            return ListUtils.EMPTY_LIST;
        }

        List<ArchVo> tree = convertToTree(departEntities,deptId);

        return tree;
    }

    private List<ArchVo> convertToTree(List<DepartEntity> departEntities,Integer parentId) {
        Map<Integer, List<ArchVo>> map = new HashMap<>();
        Map<Integer,ArchVo> node = new HashMap<>();
        List<ArchVo> tree = new ArrayList<>();

        // 分组合并
        departEntities.stream().forEach(e -> {

            ArchVo vo = new ArchVo();
            vo.setDeptId(e.getId());
            vo.setDeptName(e.getDeptName());
            vo.setDeptCode(e.getDeptCode());
            vo.setType(1);
            List<ArchVo> children = new ArrayList<>();
            List<ArchVo> empVos = (getDeptEmpList(e.getDeptCode(),e.getLeaderId()));
            if(!CollectionUtils.isEmpty(empVos)){
                children.addAll(empVos);
                Optional<ArchVo> first = empVos.stream().filter(f -> f.getIsLeader() == 1).findFirst();
                if(first.isPresent()){
                    ArchVo archVo = first.get();
                    vo.setEmpId(archVo.getEmpId());
                    vo.setEmpName(archVo.getEmpName());
                    vo.setPartTime(first.get().getPartTime());
                    vo.setAvatar(archVo.getAvatar());
                    vo.setPositionId(archVo.getPositionId());
                }
            }
            vo.setChildren(children);

            if(!map.containsKey(e.getSuperiorDeptId())){

                if(Objects.isNull(e.getSuperiorDeptId())){
                    map.put(e.getId(),new ArrayList<>());
                }else{
                    List<ArchVo> deptVos = map.get(e.getSuperiorDeptId());
                    if(CollectionUtils.isEmpty(deptVos)){
                        deptVos = new ArrayList<>();
                    }
                    deptVos.add(vo);
                    map.put(e.getSuperiorDeptId(),deptVos);
                }

            }else{
                List<ArchVo> deptVos = map.get(e.getSuperiorDeptId());
                deptVos.add(vo);
                map.put(e.getSuperiorDeptId(),deptVos);
            }

            node.put(e.getId(),vo);
            if(e.getSuperiorDeptId().equals(parentId)){
                tree.add(vo);
            }

        });


        tree.forEach(e -> {
            // 转换为tree
            adaptToChildrenList(e,map);
        });

        return tree;
    }


    private void adaptToChildrenList(ArchVo deptVo, Map<Integer, List<ArchVo>> map) {
        if(map.containsKey(deptVo.getDeptId())){
            List<ArchVo> children = map.get(deptVo.getDeptId());
            List<ArchVo> currentChildren = deptVo.getChildren();
            if(!CollectionUtils.isEmpty(children)){
                currentChildren.addAll(children);
            }
            deptVo.setChildren(currentChildren);
        }

        if(!CollectionUtils.isEmpty(deptVo.getChildren())){
            for(ArchVo vo : deptVo.getChildren()){
                adaptToChildrenList(vo,map);
            }
        }
    }


    private List<ArchVo> getDeptEmpList(String deptCode, String leaderId) {

        List<ArchEmpVo> list = deptMapper.getDeptEmpList(deptCode);
        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }

        list.forEach(e -> {
            if(StringUtils.isNotBlank(leaderId) && e.getEmpId().equals(leaderId)){
                e.setIsLeader(1);
            }else{
                e.setIsLeader(0);
            }
        });

        List<ArchEmpVo> collect = list.stream().sorted(Comparator.comparing(ArchEmpVo::getIsLeader).reversed()).collect(Collectors.toList());
        List<ArchVo> result = new ArrayList<>();
        collect.forEach(e -> {
            ArchVo vo = new ArchVo();
            BeanUtils.copyProperties(e,vo);
            vo.setType(2);
            vo.setPartTime(e.getPartTime());
            result.add(vo);
        });
        return result;
    }
}
