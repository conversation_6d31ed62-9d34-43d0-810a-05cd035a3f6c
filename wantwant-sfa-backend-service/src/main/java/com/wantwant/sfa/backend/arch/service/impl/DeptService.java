package com.wantwant.sfa.backend.arch.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.arch.entity.DeptEmployeeRelationEntity;
import com.wantwant.sfa.backend.arch.entity.SfaDeptKpi;
import com.wantwant.sfa.backend.arch.entity.SfaPosition;
import com.wantwant.sfa.backend.arch.request.*;
import com.wantwant.sfa.backend.arch.service.IDeptService;
import com.wantwant.sfa.backend.arch.vo.*;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.arch.SfaDeptKpiMapper;
import com.wantwant.sfa.backend.mapper.arch.SfaPositionMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.impl.CheckCustomerService;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.core.Local;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午4:22
 */
@Service
@Slf4j
public class DeptService implements IDeptService {
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private CheckCustomerService checkCustomerService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaDeptKpiMapper sfaDeptKpiMapper;

    @Autowired
    private SfaPositionMapper sfaPositionMapper;
    @Autowired
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;



    @Override
    @Transactional
    public void createDept(CDeptRequest cDeptRequest) {
        // 获取创建人信息
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(cDeptRequest.getPerson(),loginInfo);

        DepartEntity entity = new DepartEntity();
        BeanUtils.copyProperties(cDeptRequest, entity);
        entity.setCreateUserId(personInfo.getEmployeeId());
        entity.setCreateUserName(personInfo.getEmployeeName());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(personInfo.getEmployeeId());
        entity.setStatus(cDeptRequest.getStatus());
        entity.setUpdateUserName(personInfo.getEmployeeName());

        // 设置祖先列表
        if (Objects.nonNull(cDeptRequest.getSuperiorDeptId())) {
            setAncestors(cDeptRequest.getSuperiorDeptId(), entity);
        }

        String leader = cDeptRequest.getLeader();
        if(StringUtils.isNotBlank(leader)){
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", leader)
                    .eq("channel", 3)
                    .last("limit 1")
            );
            if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                entity.setLeaderId(ceoBusinessOrganizationPositionRelation.getEmployeeId());
                entity.setLeaderName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
            }
        }

        deptMapper.insert(entity);

        // 设置部门ID
        String deptCode = "D" + String.format("%08d", Integer.valueOf(entity.getId()));
        entity.setDeptCode(deptCode);
        deptMapper.updateById(entity);


        List<DeptKpiRequest> deptKpiRequestList = cDeptRequest.getDeptKpiRequestList();
        if(!CollectionUtils.isEmpty(deptKpiRequestList)){
            deptKpiRequestList.forEach(e -> {
                SfaDeptKpi sfaDeptKpi = new SfaDeptKpi();
                sfaDeptKpi.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaDeptKpi.setKpiMetrics(e.getKpiMetrics());
                sfaDeptKpi.setQuantify(e.getQuantify());
                sfaDeptKpi.setDeptCode(deptCode);
                sfaDeptKpi.setTarget(e.getTarget());
                sfaDeptKpi.setStatus(1);
                sfaDeptKpiMapper.insert(sfaDeptKpi);
            });

        }
    }

    private void setAncestors(int id, DepartEntity entity) {
        DepartEntity superiorDept = deptMapper.selectById(id);
        if (Objects.isNull(superiorDept)) {
            throw new ApplicationException("获取上级节点失败");
        }

        StringBuffer sb = new StringBuffer("");
        String ancestors = superiorDept.getAncestors();
        if(ancestors.contains(entity.getDeptCode())){
            throw new ApplicationException("部门上级节点选择异常");
        }
        if (StringUtils.isNotBlank(ancestors)) {
            sb.append(ancestors);
            sb.append(",");
        }
        sb.append(superiorDept.getDeptCode());
        entity.setAncestors(sb.toString());
    }

    @Override
    @Transactional
    public void update(EDeptRequest eDeptRequest) {
        // 获取创建人信息
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(eDeptRequest.getPerson(),loginInfo);

        DepartEntity entity = deptMapper.selectById(eDeptRequest.getDeptId());
        if (Objects.isNull(entity)) {
            throw new ApplicationException("部门不存在");
        }

        // 设置祖先列表
        if (Objects.nonNull(eDeptRequest.getSuperiorDeptId())) {
            setAncestors(eDeptRequest.getSuperiorDeptId(), entity);
        } else {
            entity.setAncestors(null);
        }

        String leader = eDeptRequest.getLeader();
        if(StringUtils.isNotBlank(leader)){
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", leader)
                    .eq("channel", 3)
                    .last("limit 1")
            );
            if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                entity.setLeaderId(ceoBusinessOrganizationPositionRelation.getEmployeeId());
                entity.setLeaderName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
            }
        }

        BeanUtils.copyProperties(eDeptRequest, entity);
        entity.setStatus(eDeptRequest.getStatus());
        entity.setUpdateUserName(personInfo.getEmployeeName());
        entity.setUpdateUserId(personInfo.getEmployeeId());
        entity.setUpdateTime(LocalDateTime.now());
        deptMapper.updateById(entity);

        // 删除原kpi
        List<SfaDeptKpi> sfaDeptKpis = sfaDeptKpiMapper.selectList(new QueryWrapper<SfaDeptKpi>().eq("dept_code", entity.getDeptCode()).eq("status", 1).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaDeptKpis)){
            sfaDeptKpis.forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateTime(LocalDateTime.now());
                e.setUpdateUserId(personInfo.getEmployeeId());
                e.setUpdateUserName(personInfo.getEmployeeName());
                sfaDeptKpiMapper.updateById(e);
            });
        }

        List<DeptKpiRequest> deptKpiRequestList = eDeptRequest.getDeptKpiRequestList();
        if(!CollectionUtils.isEmpty(deptKpiRequestList)){
            deptKpiRequestList.forEach(e -> {
                SfaDeptKpi sfaDeptKpi = new SfaDeptKpi();
                sfaDeptKpi.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaDeptKpi.setKpiMetrics(e.getKpiMetrics());
                sfaDeptKpi.setQuantify(e.getQuantify());
                sfaDeptKpi.setDeptCode(entity.getDeptCode());
                sfaDeptKpi.setTarget(e.getTarget());
                sfaDeptKpi.setStatus(1);
                sfaDeptKpiMapper.insert(sfaDeptKpi);
            });

        }

    }

    @Override
    public DeptInfoVo getDeptInfo(int id) {

        DepartEntity entity = deptMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new ApplicationException("部门不存在");
        }

        DeptInfoVo vo = new DeptInfoVo();
        BeanUtils.copyProperties(entity, vo);
        vo.setLeader(entity.getLeaderName());


        // 获取部门指标
        List<SfaDeptKpi> sfaDeptKpis = sfaDeptKpiMapper.selectList(new QueryWrapper<SfaDeptKpi>().eq("dept_code", entity.getDeptCode()).eq("delete_flag", 0).eq("status", 1));
        if(!CollectionUtils.isEmpty(sfaDeptKpis)){
            List<DeptKpiRequest> list = new ArrayList<>();
            sfaDeptKpis.forEach(e -> {
                DeptKpiRequest deptKpiRequest = new DeptKpiRequest();
                BeanUtils.copyProperties(e,deptKpiRequest);
                list.add(deptKpiRequest);
            });
            vo.setDeptKpiRequestList(list);
        }
        return vo;
    }

    @Override
    public List<DeptVo> selectDeptList(SDeptRequest request) {
        List<DepartEntity> list = deptMapper.selectDeptList(request);
        if (CollectionUtils.isEmpty(list)) {
            return ListUtils.EMPTY_LIST;
        }

        Map<Integer, List<DeptVo>> map = new HashMap<>();
        Map<Integer,DeptVo> node = new HashMap<>();
        List<DeptVo> tree = new ArrayList<>();
        // 分组合并
        list.stream().forEach(e -> {

            DeptVo vo = new DeptVo();
            vo.setStatus(e.getStatus());
            vo.setLeader(e.getLeaderName());
            vo.setDeptId(e.getId());

            String leaderId = e.getLeaderId();
            if(StringUtils.isNotBlank(leaderId)){
                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", leaderId).eq("channel", 3).last("limit 1"));
                if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                    vo.setLeader(ceoBusinessOrganizationPositionRelation.getEmployeeName());
                }
            }

            vo.setCreateDate(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            BeanUtils.copyProperties(e,vo);

            if(!map.containsKey(e.getSuperiorDeptId())){

                if(Objects.isNull(e.getSuperiorDeptId())){
                    map.put(e.getId(),new ArrayList<>());
                }else{
                    List<DeptVo> deptVos = map.get(e.getSuperiorDeptId());
                    if(CollectionUtils.isEmpty(deptVos)){
                        deptVos = new ArrayList<>();
                    }
                    deptVos.add(vo);
                    map.put(e.getSuperiorDeptId(),deptVos);
                }

            }else{
                List<DeptVo> deptVos = map.get(e.getSuperiorDeptId());
                deptVos.add(vo);
                map.put(e.getSuperiorDeptId(),deptVos);
            }

            node.put(e.getId(),vo);

        });

        list.stream().filter(f -> Objects.isNull(f.getSuperiorDeptId())).forEach(e -> {
            DeptVo vo = new DeptVo();
            vo.setDeptId(e.getId());
            vo.setStatus(e.getStatus());
            String leaderId = e.getLeaderId();
            if(StringUtils.isNotBlank(leaderId)){
                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", leaderId).eq("channel", 3).last("limit 1"));
                if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                    vo.setLeader(ceoBusinessOrganizationPositionRelation.getEmployeeName());
                }
            }
            vo.setCreateDate(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            BeanUtils.copyProperties(e,vo);
            tree.add(vo);
        });


        if(CollectionUtils.isEmpty(tree)){
            list.stream().filter(f -> Objects.nonNull(f.getSuperiorDeptId())).forEach(e -> {
                DeptVo vo = new DeptVo();
                vo.setStatus(e.getStatus());
                String leaderId = e.getLeaderId();
                if(StringUtils.isNotBlank(leaderId)){
                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", leaderId).eq("channel", 3).last("limit 1"));
                    if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                        vo.setLeader(ceoBusinessOrganizationPositionRelation.getEmployeeName());
                    }
                }
                vo.setDeptId(e.getId());
                vo.setCreateDate(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
                BeanUtils.copyProperties(e,vo);
                tree.add(vo);
            });
        }

        tree.forEach(e -> {
            // 转换为tree
            adaptToChildrenList(e,map);
        });


        return tree;
    }

    @Override
    public List<DeptSelectVo> departSelect(Integer deptId) {
        List<DepartEntity> departEntities = deptMapper.selectList(new QueryWrapper<DepartEntity>().eq("delete_flag", 0));
        if(CollectionUtils.isEmpty(departEntities)){
           return ListUtils.EMPTY_LIST;
        }
        Map<Integer, List<DeptSelectVo>> map = new HashMap<>();
        Map<Integer,DeptSelectVo> node = new HashMap<>();
        List<DeptSelectVo> tree = new ArrayList<>();

        // 分组合并
        departEntities.stream().forEach(e -> {

            DeptSelectVo vo = new DeptSelectVo();
            vo.setDeptId(e.getId());
            BeanUtils.copyProperties(e,vo);

            if(!map.containsKey(e.getSuperiorDeptId())){

                if(Objects.isNull(e.getSuperiorDeptId())){
                    map.put(e.getId(),new ArrayList<>());
                }else{
                    List<DeptSelectVo> deptVos = map.get(e.getSuperiorDeptId());
                    if(CollectionUtils.isEmpty(deptVos)){
                        deptVos = new ArrayList<>();
                    }
                    deptVos.add(vo);
                    map.put(e.getSuperiorDeptId(),deptVos);
                }

            }else{
                List<DeptSelectVo> deptVos = map.get(e.getSuperiorDeptId());
                deptVos.add(vo);
                map.put(e.getSuperiorDeptId(),deptVos);
            }

            node.put(e.getId(),vo);

        });

        departEntities.stream().filter(f -> Objects.isNull(f.getSuperiorDeptId())).forEach(e -> {
            DeptSelectVo vo = new DeptSelectVo();
            vo.setDeptId(e.getId());
            BeanUtils.copyProperties(e,vo);
            tree.add(vo);
        });

        tree.forEach(e -> {
            // 转换为tree
            adaptToChildrenList(e,map);
        });

        return tree;
    }


    @Override
    public List<DeptSelectVo> departSelectByEmpId(String empId) {
        List<DeptEmployeeRelationEntity> deptEmployeeRelationEntities = deptEmployeeRelationMapper.selectList(new QueryWrapper<DeptEmployeeRelationEntity>()
                .eq("employee_id", empId).eq("delete_flag", 0));
        if(CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
            return null;
        }

        List<Integer> ids = deptEmployeeRelationEntities.stream().map(DeptEmployeeRelationEntity::getDeptId).collect(Collectors.toList());
        List<DepartEntity> departEntities = deptMapper.selectBatchIds(ids);



        List<DeptSelectVo> list = new ArrayList<>();

        // 分组合并
        departEntities.stream().forEach(e -> {

            DeptSelectVo vo = new DeptSelectVo();
            vo.setDeptId(e.getId());
            BeanUtils.copyProperties(e,vo);
            list.add(vo);
        });



        return list;
    }

    @Override
    @Transactional
    public void delete(DeptOperatorRequest deptOperatorRequest) {
        // 获取创建人信息
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(deptOperatorRequest.getPerson(),loginInfo);

        DepartEntity entity = deptMapper.selectById(deptOperatorRequest.getDeptId());
        if (Objects.isNull(entity)) {
            throw new ApplicationException("部门不存在");
        }

        entity.setDeleteFlag(1);
        entity.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        deptMapper.updateById(entity);

        // 同时删除子孙节点
        List<DepartEntity> departEntities = deptMapper.selectList(new LambdaQueryWrapper<DepartEntity>().like(DepartEntity::getSuperiorDeptId, entity.getId()));
        if(!CollectionUtils.isEmpty(departEntities)){
            departEntities.forEach(e -> {
                e.setDeleteFlag(1);
                e.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                deptMapper.updateById(e);
            });
        }
    }


    @Override
    public List<UserVo> getUserListByDeptCode(String deptCode) {
        return deptMapper.getUserListByDeptCode(deptCode);
    }

    @Override
    public List<DeptPositionVo> getPositionListByDeptId(Integer deptId) {
        log.info("getPositionListByDeptId :{}", deptId);
        List<DeptPositionVo> retList = new ArrayList<>();
        List<SfaPosition> list = sfaPositionMapper.selectPositionListByDepatId(deptId);
        if(!CollectionUtils.isEmpty(list)) {
            list.forEach(e->{
                DeptPositionVo vo = new DeptPositionVo();
                BeanUtils.copyProperties(e, vo);
                retList.add(vo);
            });
        }
        return retList;
    }



    private void adaptToChildrenList(DeptVo deptVo, Map<Integer, List<DeptVo>> map) {
        if(map.containsKey(deptVo.getDeptId())){
            List<DeptVo> children = map.get(deptVo.getDeptId());
            deptVo.setChildren(children);
        }

        if(!CollectionUtils.isEmpty(deptVo.getChildren())){
            for(DeptVo vo : deptVo.getChildren()){
                adaptToChildrenList(vo,map);
            }
        }
    }


    private void adaptToChildrenList(DeptSelectVo deptVo, Map<Integer, List<DeptSelectVo>> map) {
        if(map.containsKey(deptVo.getDeptId())){
            List<DeptSelectVo> children = map.get(deptVo.getDeptId());
            deptVo.setChildren(children);
        }

        if(!CollectionUtils.isEmpty(deptVo.getChildren())){
            for(DeptSelectVo vo : deptVo.getChildren()){
                adaptToChildrenList(vo,map);
            }
        }
    }
}
