package com.wantwant.sfa.backend.arch.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.entity.CustomResourcesEntity;
import com.wantwant.sfa.backend.arch.entity.ResourceEntity;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.arch.model.RealtimeDateModel;
import com.wantwant.sfa.backend.arch.request.CustomMenuRequest;
import com.wantwant.sfa.backend.arch.service.IMenuService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.CustomResourcesMapper;
import com.wantwant.sfa.backend.mapper.arch.ResourceMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.OrganizationModel;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.policy.dto.MenuPolicyRelateDTO;
import com.wantwant.sfa.backend.policy.dto.PolicyItemDTO;
import com.wantwant.sfa.backend.policy.service.IPolicySearchService;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/23/下午4:05
 */
@Service
@Slf4j
public class MenuService implements IMenuService {
    @Autowired
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Autowired
    private ResourceMapper resourceMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private CustomResourcesMapper customResourcesMapper;
    @Autowired
    private IPolicySearchService policySearchService;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private RedisUtil redisUtil;


    private String POLICY_URL = "/policyView?categoryId={0}&storeyId={1}&policyId={2}";

    private static final String GUIDANCE_ZB = "sfa:guidance";

    private static final String GUIDANCE_MANAGER = "sfa:guidance:manager";

    @Override
    public Object getMenu(String person) {
        log.info("【get menu】person:{},channel:{}",person,RequestUtils.getChannel());

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        String language = loginInfo.getLanguage();
        log.info("get Resources language:{}",language);
        if(StringUtils.isBlank(language)){
            throw new ApplicationException(BizExceptionLanguageEnum.LANGUAGE_MUST_NOT_NULL.getTextMsg());
        }



        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, person)
                .eq(SfaPositionRelationEntity::getPositionTypeId, loginInfo.getPositionTypeId())
                .eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .orderByAsc(SfaPositionRelationEntity::getPartTime)
        );

        // 更新提示菜单
        List<CustomResourcesEntity> promptMenuEntities = Optional.ofNullable(customResourcesMapper.selectList(new LambdaQueryWrapper<CustomResourcesEntity>().eq(CustomResourcesEntity::getEmpId, person).eq(CustomResourcesEntity::getType, 2).eq(CustomResourcesEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());

        RealtimeDateModel realtimeDateModel = new RealtimeDateModel();
        Map<String, JsonObject> organizationMap = new HashMap<>();
        AtomicReference<String> organizationType = new AtomicReference();


        positionRelationEntityList.forEach(e -> {
            OrganizationModel organizationModel = organizationMapper.getOrganizationByPositionId(e.getPositionId());
            organizationType.set(organizationModel.getOrganizationType());
            // 填充organizationMap
            fillOrganizationMap(organizationMap,organizationModel);
            // 填充组织信息
            fillAreaArray(e.getOrganizationCode(),organizationModel.getOrganizationName(),organizationModel.getOrganizationType(),organizationMap,realtimeDateModel);
        });


        List<String> positionList = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getPositionId).collect(Collectors.toList());

        // 根据工号获取角色信息
        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().eq(RoleEmployeeRelationEntity::getEmployeeId, person).in(RoleEmployeeRelationEntity::getPositionId, positionList).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0));
        List<Integer> roleIds = roleEmployeeRelationEntities.stream().map(RoleEmployeeRelationEntity::getRoleId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleIds)){
            throw new ApplicationException("用户尚未拥有任何权限");
        }

        if(RequestUtils.getBusinessGroup() == 99){
            roleIds = Arrays.asList(11);
        }

        // 根据角色获取菜单
        List<ResourceEntity> resourceEntityList = resourceMapper.selectListByRoleId(roleIds,language);
        if(CollectionUtils.isEmpty(resourceEntityList)){
            throw new ApplicationException("无可用资源");
        }

        List<MenuPolicyRelateDTO> menuPolicyRelateDTOList = Optional.ofNullable(policySearchService.selectMenuPolicyRelate(person)).orElse(new ArrayList<>());

        // 记录创建的菜单
        Map<Integer,JsonObject> menuMap = new LinkedHashMap<>();
        // 记录创建的子菜单
        Map<Integer,JsonArray> childMap = new HashMap<>();
        // 创建一级菜单
        resourceEntityList.stream().filter(f -> Objects.isNull(f.getParentId())).forEach(e -> {
            boolean updatePrompt = promptMenuEntities.stream().filter(f -> f.getResourceId().equals(e.getId())).findFirst().isPresent();

            String policyUrl = null;

            Optional<MenuPolicyRelateDTO> first = menuPolicyRelateDTOList.stream().filter(f -> f.getMenuId().equals(e.getId())).findFirst();
            if(first.isPresent()){
                MenuPolicyRelateDTO menuPolicyRelateDTO = first.get();
                policyUrl = MessageFormat.format(POLICY_URL,String.valueOf(menuPolicyRelateDTO.getCategoryId()),String.valueOf(menuPolicyRelateDTO.getSecondaryCategoryId()),String.valueOf(menuPolicyRelateDTO.getPolicyId()));
            }
            JsonObject menu = commonCreateMenu(e, updatePrompt, false, person,policyUrl);
            if(Objects.nonNull(menu)){
                menuMap.put(e.getId(),menu);
            }

        });


        resourceEntityList.stream().filter(f -> Objects.nonNull(f.getParentId())).forEach(e -> {

            JsonArray jsonArray = childMap.get(e.getParentId());
            JsonObject menu = null;

            boolean updatePrompt = promptMenuEntities.stream().filter(f -> f.getResourceId().equals(e.getId())).findFirst().isPresent();

            String policyUrl = null;

            List<MenuPolicyRelateDTO> policyList = menuPolicyRelateDTOList.stream().filter(f -> f.getMenuId().equals(e.getId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(policyList)){
                // 取最后一条
                MenuPolicyRelateDTO menuPolicyRelateDTO = policyList.get(policyList.size() - 1);
                policyUrl = MessageFormat.format(POLICY_URL,String.valueOf(menuPolicyRelateDTO.getCategoryId()),String.valueOf(menuPolicyRelateDTO.getSecondaryCategoryId()),String.valueOf(menuPolicyRelateDTO.getPolicyId()));
            }

            if(e.getPath().equals("/newData")){
                SfaPositionRelationEntity sfaPositionRelationEntity = positionRelationEntityList.stream().findFirst().orElseThrow(() -> new ApplicationException("岗位获取失败"));
                menu = createNewDateMenu(e,sfaPositionRelationEntity.getOrganizationCode(),updatePrompt,policyUrl);
            }
            else{
                menu = commonCreateMenu(e,updatePrompt, true, person,policyUrl);
            }


            if(Objects.isNull(jsonArray)){
                jsonArray  = new JsonArray();
            }
            jsonArray.add(menu);
            childMap.put(e.getParentId(),jsonArray);

        });

        // 封装菜单
        JsonObject result = convertToMenuObj(menuMap,childMap);

        return JSONObject.parse(result.toString());
    }

    private JsonObject createNewDateMenu(ResourceEntity e, String organizationCode, boolean updatePrompt, String policyUrl) {
        JsonObject menu = new JsonObject();
        menu.addProperty("path", e.getPath()+"?orgId="+organizationCode);
        menu.addProperty("componentString", e.getComponent());
        menu.addProperty("name", e.getName());
        menu.addProperty("menuId",e.getId());

        Integer appSupport = e.getAppSupport();
        if(Objects.nonNull(appSupport) && appSupport == 1){
            menu.addProperty("appSupport", true);
        }



        Integer concern = e.getConcern();
        if(Objects.nonNull(concern) && concern == 1){
            menu.addProperty("concern", true);
        }

        if(StringUtils.isNotBlank(e.getRedirect())){
            menu.addProperty("redirect", e.getRedirect());
        }
        if(e.getAlwaysShow() == 1){
            menu.addProperty("alwaysShow",true);
        }
        if(e.getHidden() == 1){
            menu.addProperty("hidden",true);
        }

        JsonObject meta = new JsonObject();
        meta.addProperty("title", e.getTitle());
        meta.addProperty("icon", e.getIcon());
        if(e.getCache() == 0){
            meta.addProperty("noCache",true);
        }
        if(updatePrompt){
            menu.addProperty("showNew",true);
            meta.addProperty("showNew", true);
        }
        if(StringUtils.isNotBlank(policyUrl)){
            menu.addProperty("policyUrl",policyUrl);
            meta.addProperty("policyUrl", policyUrl);
        }
        
        meta.addProperty("menuId",e.getId());
        menu.add("meta", meta);
        return menu;
    }

    @Override
    @Transactional
    public void customMenu(CustomMenuRequest customMenuRequest) {
        Integer operation = customMenuRequest.getOperation();

        CustomResourcesEntity customResourcesEntity = customResourcesMapper.selectOne(new LambdaQueryWrapper<CustomResourcesEntity>()
                .eq(CustomResourcesEntity::getEmpId, customMenuRequest.getPerson())
                .eq(CustomResourcesEntity::getResourceId, customMenuRequest.getMenuId())
                .eq(CustomResourcesEntity::getType, customMenuRequest.getType())
                .eq(CustomResourcesEntity::getDeleteFlag, 0)
        );

        if(operation == 1){
            // 新增
            if(Objects.nonNull(customResourcesEntity)){
                return;
            }

            customResourcesEntity = new CustomResourcesEntity();
            customResourcesEntity.setCreateTime(LocalDateTime.now());
            customResourcesEntity.setUpdateTime(LocalDateTime.now());
            customResourcesEntity.setEmpId(customMenuRequest.getPerson());
            customResourcesEntity.setDeleteFlag(0);
            customResourcesEntity.setResourceId(customMenuRequest.getMenuId());
            customResourcesEntity.setType(customMenuRequest.getType());
            customResourcesMapper.insert(customResourcesEntity);

        }else{
            // 删除
            if(Objects.isNull(customResourcesEntity)){
                return;
            }

            customResourcesEntity.setDeleteFlag(1);
            customResourcesEntity.setUpdateTime(LocalDateTime.now());
            customResourcesMapper.updateById(customResourcesEntity);
        }
    }

    @Override
    public Boolean checkGuidanceFinish(String empId) {
        Integer positionTypeId = RequestUtils.getLoginInfo().getPositionTypeId();
        // 总部用工号
        if(positionTypeId == 7){
            return redisUtil.sHasKey(GUIDANCE_ZB,empId);
        }else{
            // 非总部用employeeInfoId
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, empId).last("limit 1"));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("员工信息异常");
            }
            return redisUtil.sHasKey(GUIDANCE_MANAGER,sfaEmployeeInfoModel.getId());
        }

    }

    @Override
    public void finishGuide(String empId) {
        Integer positionTypeId = RequestUtils.getLoginInfo().getPositionTypeId();
        // 总部用工号
        if(positionTypeId == 7){
            redisUtil.sSet(GUIDANCE_ZB,empId);
        }else{
            // 非总部用employeeInfoId
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, empId).last("limit 1"));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("员工信息异常");
            }
            redisUtil.sSet(GUIDANCE_MANAGER,sfaEmployeeInfoModel.getId());
        }
    }


    private JsonObject convertToMenuObj(Map<Integer, JsonObject> menuMap, Map<Integer, JsonArray> childMap) {
        JsonObject data = new JsonObject();
        JsonArray menu = new JsonArray();
        menuMap.forEach((key,value) -> {
            JsonArray child = Optional.ofNullable(childMap.get(key)).orElse(new JsonArray());
            if(Objects.nonNull(child) && child.size() > 0){
                // 递归设置菜单
                setChildren(child,childMap);
            }
            value.add("children",child);
            menu.add(value);
        });

        data.add("data", menu);
        return data;
    }

    private void setChildren(JsonArray child, Map<Integer, JsonArray> childMap) {
        for(int i=0; i< child.size();i++){
            JsonElement jsonElement = child.get(i);
            if (!jsonElement.isJsonNull()) {
                JsonObject obj = jsonElement.getAsJsonObject();
                if(Objects.nonNull(obj) && Objects.nonNull(obj.get("menuId"))){
                    JsonElement menu = obj.get("menuId");
                    int menuId = menu.getAsInt();

                    JsonArray childMenu = childMap.get(menuId);
                    if(Objects.nonNull(childMenu) && childMenu.size() > 0){
                        setChildren(childMenu,childMap);
                        obj.add("children",childMenu);
                    }
                }
            }
        }
    }


    private JsonObject createNewRealtimeDateMenu(ResourceEntity e, String organizationType, RealtimeDateModel realtimeDateModel, boolean updatePrompt, String policyUrl) {
        JsonObject newAreaMenu = new JsonObject();
        newAreaMenu.addProperty("path", "/areadata_Z-new");
        newAreaMenu.addProperty("componentString", "LayoutNew");
        newAreaMenu.addProperty("name", "AreaDataNew");
//        newAreaMenu.addProperty("menuId",e.getId());
        JsonObject newAreaMeta = new JsonObject();
        newAreaMeta.addProperty("menuId", e.getId());
        newAreaMeta.addProperty("title", "新实时数据");
        newAreaMeta.addProperty("icon", e.getIcon());
        newAreaMeta.addProperty("noCache", true);


        if(updatePrompt){
            newAreaMeta.addProperty("showNew", true);
            newAreaMenu.addProperty("showNew", true);
        }

        if(StringUtils.isNotBlank(policyUrl)){
            newAreaMeta.addProperty("policyUrl", policyUrl);
            newAreaMenu.addProperty("policyUrl", policyUrl);
        }

        newAreaMenu.add("meta", newAreaMeta);
//        menuMap.put(e.getId(),newAreaMenu);

        Integer appSupport = e.getAppSupport();
        if(Objects.nonNull(appSupport) && appSupport == 1){
            newAreaMenu.addProperty("appSupport", true);
        }


        Integer concern = e.getConcern();
        if(Objects.nonNull(concern) && concern == 1){
            newAreaMenu.addProperty("concern", true);
        }

        JsonArray totalBusiness = new JsonArray();

        if(RequestUtils.getChannel() == 3 && organizationType.equals("zb")){
            String organizationId = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
            JsonObject childrenOnj = new JsonObject();
            childrenOnj.addProperty("path", "/" + organizationId + "-new");
            childrenOnj.addProperty("componentString", "ZBDataNew");
            JsonObject meta = new JsonObject();
            meta.addProperty("title", "总部");
            meta.addProperty("regions", "ZB");
            meta.addProperty("noCache", true);
            childrenOnj.add("meta", meta);
            childrenOnj.add("children", realtimeDateModel.getNewAreaDataJsonArray());
            totalBusiness.add(childrenOnj);
        }else if(organizationType.equals("area")) {
            realtimeDateModel.getNewAreaDataJson().forEach(j -> {
                totalBusiness.add(j);
            });

        }else if(organizationType.equals("company")){
            realtimeDateModel.getNewCompanyDataJson().forEach(j -> {
                totalBusiness.add(j);
            });

        }else if(organizationType.equals("varea")){
            realtimeDateModel.getNewVAreaDataJson().forEach( j -> {
                totalBusiness.add(j);
            });

        }else if(organizationType.equals("province")){
            realtimeDateModel.getNewProvinceDataJson().forEach( j -> {
                totalBusiness.add(j);
            });
        }else{
            realtimeDateModel.getNewBranchDataJson().forEach(j -> {
                totalBusiness.add(j);
            });

        }

        newAreaMenu.add("children",totalBusiness);

        return newAreaMenu;
    }

    private void fillOrganizationMap(Map<String, JsonObject> organizationMap, OrganizationModel organizationModel) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("organization_id",organizationModel.getOrganizationId());
        jsonObject.addProperty("organization_name", organizationModel.getOrganizationName());
        jsonObject.addProperty("organization_type", organizationModel.getOrganizationType());
        organizationMap.put(organizationModel.getOrganizationId(), jsonObject);
    }

    /**
     * 填充areaArray
     * @param organizationId
     * @param organizationType
     * @param organizationMap
     * @param realtimeDateModel
     */
    private void fillAreaArray(String organizationId, String organizationName, String organizationType, Map<String, JsonObject> organizationMap, RealtimeDateModel realtimeDateModel) {
        int businessGroup = RequestUtils.getBusinessGroup();

        if(organizationType.equals("zb")){
            // 获取所有大区信息
            List<CeoBusinessOrganizationEntity> ceoBusinessOrganizationEntities = ceoBusinessOrganizationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq("organization_type", "area")
                    .eq("business_group",businessGroup)
                    .eq("channel", RequestUtils.getChannel())
            );
            ceoBusinessOrganizationEntities.forEach(e -> {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("organization_id", e.getOrganizationId());
                jsonObject.addProperty("organization_name", e.getOrganizationName());
                jsonObject.addProperty("organization_type", e.getOrganizationType());
                JsonObject areaMenu = convertAreaMenu(jsonObject);
                realtimeDateModel.getAreaJsonArray().add(areaMenu);


                //获取总部数据
                JsonObject areaDtaMenu = convertAreaDataMenu(jsonObject);
                realtimeDateModel.getAreaDataJsonArray().add(areaDtaMenu);
                areaDtaMenu.add("children", getOrganizationDataMenu(jsonObject.get("organization_id").getAsString(),RequestUtils.getChannel()));
                JsonObject newAreaDtaMenu = newConvertAreaDataMenu(jsonObject);
                realtimeDateModel.getNewAreaDataJsonArray().add(newAreaDtaMenu);
                newAreaDtaMenu.add("children", getNewOrganizationDataMenu(jsonObject.get("organization_id").getAsString(),RequestUtils.getChannel()));
            });
        }else if( "area".equals(organizationType)){
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("organization_id", organizationId);
            jsonObject.addProperty("organization_name", organizationName);
            jsonObject.addProperty("organization_type", organizationType);

            JsonObject areaMenu = convertAreaMenu(jsonObject);
            realtimeDateModel.getAreaJsonArray().add(areaMenu);

            //获取大区数据
            JsonObject areaDataMenu = convertAreaDataMenu(jsonObject);
            areaDataMenu.add("children", getOrganizationDataMenu(organizationId,RequestUtils.getChannel()));
            realtimeDateModel.getAreaDataJsonArray().add(areaDataMenu);
            realtimeDateModel.getAreaDataJson().add(areaDataMenu);


            JsonObject newAreaDataMenu = newConvertAreaDataMenu(jsonObject);
            newAreaDataMenu.add("children", getNewOrganizationDataMenu(organizationId, RequestUtils.getChannel()));
            realtimeDateModel.getNewAreaDataJsonArray().add(newAreaDataMenu);
            realtimeDateModel.getNewAreaDataJson().add(newAreaDataMenu);

        }else if("varea".equals(organizationType)){
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("organization_id", organizationId);
            jsonObject.addProperty("organization_name", organizationName);
            jsonObject.addProperty("organization_type", organizationType);

            JsonObject vareaMenu = convertRegion(jsonObject,"varea");
            realtimeDateModel.getVareaJsonArray().add(vareaMenu);

            //获取大区数据
            JsonObject vareaDataMenu = convertRegionMenu(jsonObject,"vareaData");
            vareaDataMenu.add("children", getOrganizationDataMenu(organizationId,RequestUtils.getChannel()));

            realtimeDateModel.getVareaDataJsonArray().add(vareaDataMenu);
            realtimeDateModel.getVareaDataJson().add(vareaDataMenu);


            JsonObject newVAreaDataMenu = convertRegionNew(jsonObject,"vareaDataNew");
            newVAreaDataMenu.add("children", getNewOrganizationDataMenu(organizationId, RequestUtils.getChannel()));
            realtimeDateModel.getNewVareaDataJsonArray().add(newVAreaDataMenu);
            realtimeDateModel.getNewVAreaDataJson().add(newVAreaDataMenu);
        }
        else if("province".equals(organizationType)){
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("organization_id", organizationId);
            jsonObject.addProperty("organization_name", organizationName);
            jsonObject.addProperty("organization_type", organizationType);

            JsonObject provinceMenu = convertRegion(jsonObject,"province");
            realtimeDateModel.getProvinceJsonArray().add(provinceMenu);

            //获取大区数据
            JsonObject provinceDataMenu = convertRegionMenu(jsonObject,"provinceData");
            provinceDataMenu.add("children", getOrganizationDataMenu(organizationId,RequestUtils.getChannel()));

            realtimeDateModel.getProvinceDataJson().add(provinceDataMenu);
            realtimeDateModel.getProvinceDataJsonArray().add(provinceDataMenu);


            JsonObject newProvinceDataMenu = convertRegionNew(jsonObject,"provinceDataNew");
            newProvinceDataMenu.add("children", getNewOrganizationDataMenu(organizationId, RequestUtils.getChannel()));
            realtimeDateModel.getNewProvinceDataJson().add(newProvinceDataMenu);
            realtimeDateModel.getNewProvinceDataJsonArray().add(newProvinceDataMenu);
        }
        else if ("company".equals(organizationType)){//获取分公司
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("organization_id", organizationId);
            jsonObject.addProperty("organization_name", organizationName);
            jsonObject.addProperty("organization_type", organizationType);
            JsonObject companyMenu = convertCompanyMenu(jsonObject);
            realtimeDateModel.getCompanyJsonArray().add(companyMenu);

            //获取分公司数据
            JsonObject companyDataMenu = convertCompanyDataMenu(jsonObject);
            companyDataMenu.add("children", getOrganizationDataMenu(organizationId, RequestUtils.getChannel()));
            realtimeDateModel.getCompanyDataJsonArray().add(companyDataMenu);
            realtimeDateModel.getCompanyDataJson().add(companyDataMenu);


            JsonObject newCompanyDataMenu = newConvertCompanyDataMenu(jsonObject);
            newCompanyDataMenu.add("children", getNewOrganizationDataMenu(organizationId, RequestUtils.getChannel()));
            realtimeDateModel.getNewCompanyDataJsonArray().add(newCompanyDataMenu);
            realtimeDateModel.getNewCompanyDataJson().add(newCompanyDataMenu);
        }else {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("organization_id", organizationId);
            jsonObject.addProperty("organization_name", organizationName);
            jsonObject.addProperty("organization_type", organizationType);

            JsonObject branchMenu = convertBranchMenu(jsonObject);
            branchMenu.add("children", new Gson().fromJson(organizationMap.toString(), JsonObject.class));
            realtimeDateModel.getBranchJsonArray().add(branchMenu);
            JsonObject branchDataMenu = convertBranchDataMenu(jsonObject);
            realtimeDateModel.getBranchDataJsonArray().add(branchDataMenu);
            realtimeDateModel.getBranchDataJson().add(branchDataMenu);

            JsonObject newBranchMenu = newConvertBranchMenu(jsonObject);
            newBranchMenu.add("children", new Gson().fromJson(organizationMap.toString(), JsonObject.class));
            realtimeDateModel.getBranchJsonArray().add(newBranchMenu);
            JsonObject newBranchDataMenu = newConvertBranchDataMenu(jsonObject);
            realtimeDateModel.getNewBranchDataJsonArray().add(newBranchDataMenu);
            realtimeDateModel.getNewBranchDataJson().add(newBranchDataMenu);
        }
    }

    private JsonObject newConvertBranchDataMenu(JsonObject org) {
        JsonObject branchMenu = new JsonObject();
        branchMenu.addProperty("path", "/" + org.get("organization_id").getAsString() + "-new");
        branchMenu.addProperty("name", org.get("organization_id").getAsString() + "-new");
        branchMenu.addProperty("componentString", "BranchDataNew");
        JsonObject meta = new JsonObject();
        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("branch", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);
        branchMenu.add("meta", meta);
        return branchMenu;
    }

    private JsonObject newConvertBranchMenu(JsonObject org) {
        JsonObject branchMenu = new JsonObject();
        branchMenu.addProperty("path", "/" + org.get("organization_id").getAsString() +"-new");
        branchMenu.addProperty("name", org.get("organization_id").getAsString() + "-new");
        branchMenu.addProperty("componentString", "BranchNew");
        JsonObject meta = new JsonObject();
        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);
        branchMenu.add("meta", meta);
        return branchMenu;
    }

    private JsonObject convertBranchDataMenu(JsonObject org) {
        JsonObject branchMenu = new JsonObject();

        branchMenu.addProperty("path", "/" + org.get("organization_id").getAsString());
        branchMenu.addProperty("name", org.get("organization_id").getAsString());
        branchMenu.addProperty("componentString", "BranchData");

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("branch", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        branchMenu.add("meta", meta);

        return branchMenu;
    }

    private JsonObject convertBranchMenu(JsonObject org) {
        JsonObject branchMenu = new JsonObject();

        branchMenu.addProperty("path", "/" + org.get("organization_id").getAsString());
        branchMenu.addProperty("name", org.get("organization_id").getAsString());
        branchMenu.addProperty("componentString", "Branch");

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        branchMenu.add("meta", meta);

        return branchMenu;
    }

    private JsonObject convertCompanyMenu(JsonObject org) {
        JsonObject companyMenu = new JsonObject();

        companyMenu.addProperty("path", "/" + org.get("organization_id").getAsString());
        companyMenu.addProperty("name", org.get("organization_id").getAsString());
        companyMenu.addProperty("componentString", "CompanyData");
        companyMenu.addProperty("alwaysShow", true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("company", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        companyMenu.add("meta", meta);

        return companyMenu;
    }

    private JsonArray getNewOrganizationDataMenu(String organizationId, int channel) {
        JsonArray result = new JsonArray();
        List<JsonObject> list = new ArrayList<>();
        List<OrganizationModel> organizationModels = organizationMapper.selectOrganizationByParentCode(organizationId, channel);
        organizationModels.forEach(e -> {
            JsonObject jsonObject = new JsonObject();

            jsonObject.addProperty("organization_id", e.getOrganizationId());
            jsonObject.addProperty("organization_name", e.getOrganizationName());
            jsonObject.addProperty("organization_type", e.getOrganizationType());
            jsonObject.addProperty("alwaysShow",true);

            if("varea".equals(e.getOrganizationType())){
                list.add(convertRegionNew(jsonObject,"vareaDataNew"));
            }
            else if("province".equals(e.getOrganizationType())){
                list.add(convertRegionNew(jsonObject,"provinceDataNew"));
            }
            else if ("company".equals(e.getOrganizationType())) {
                list.add(newConvertCompanyDataMenu(jsonObject));
            }

            else if("department".equals(e.getOrganizationType())){
                list.add(newConvertBranchDataMenu(jsonObject));
            }
        });


        for (int i = 0; i < list.size(); i++) {
            JsonObject menu = list.get(i);
            if (menu.get("componentString").getAsString() != null && "BranchDataNew".equals(menu.get("componentString").getAsString())) {
                result.add(menu);
                continue;
            }
            menu.add("children", getNewOrganizationDataMenu(
                    menu.get("meta").getAsJsonObject().get("organizationId").getAsString(),channel));
            result.add(menu);
        }


        return result;
    }

    private JsonObject newConvertCompanyDataMenu(JsonObject org) {
        JsonObject companyMenu = new JsonObject();

        companyMenu.addProperty("path", "/" + org.get("organization_id").getAsString() + "-new");
        companyMenu.addProperty("name", org.get("organization_id").getAsString() + "-new");
        companyMenu.addProperty("componentString", "CompanyDataNew");
        companyMenu.addProperty("alwaysShow",true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        companyMenu.add("meta", meta);

        return companyMenu;
    }

    /**
     * 获取子菜单数据
     *
     * @param organizationId
     * @param channel
     * @return
     */
    private JsonArray getOrganizationDataMenu(String organizationId, int channel) {
        JsonArray result = new JsonArray();
        List<JsonObject> list = new ArrayList<>();
        List<OrganizationModel> organizationModels = organizationMapper.selectOrganizationByParentCode(organizationId, channel);
        organizationModels.forEach(e -> {
            JsonObject jsonObject = new JsonObject();

            jsonObject.addProperty("organization_id", e.getOrganizationId());
            jsonObject.addProperty("organization_name", e.getOrganizationName());
            jsonObject.addProperty("organization_type", e.getOrganizationType());
            jsonObject.addProperty("alwaysShow",true);

            if("varea".equals(e.getOrganizationType())){
                list.add(convertRegion(jsonObject,"vareaData"));
            }
            else if("province".equals(e.getOrganizationType())){
                list.add(convertRegion(jsonObject,"provinceData"));
            }
            else if ("company".equals(e.getOrganizationType())) {
                list.add(convertCompanyDataMenu(jsonObject));
            }

            else if ("department".equals((e.getOrganizationType()))) {
                list.add(convertBranchDataMenu(jsonObject));
            }
        });


        for (int i = 0; i < list.size(); i++) {
            JsonObject menu = list.get(i);

            if (menu.get("componentString").getAsString() != null && "BranchData".equals(menu.get("componentString").getAsString())) {
                result.add(menu);
                continue;
            }
            menu.add("children", getOrganizationDataMenu(menu.get("meta").getAsJsonObject().get("organizationId").getAsString(), channel));
            result.add(menu);
        }


        return result;
    }


    private JsonObject convertCompanyDataMenu(JsonObject org) {
        JsonObject companyMenu = new JsonObject();

        companyMenu.addProperty("path", "/" + org.get("organization_id").getAsString());
        companyMenu.addProperty("name", org.get("organization_id").getAsString());
        companyMenu.addProperty("componentString", "CompanyData");
        companyMenu.addProperty("alwaysShow",true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        companyMenu.add("meta", meta);

        return companyMenu;
    }

    private JsonObject newConvertAreaDataMenu(JsonObject org) {
        JsonObject areaMenu = new JsonObject();
        areaMenu.addProperty("path", "/" + org.get("organization_id").getAsString() + "-new");
        areaMenu.addProperty("name", org.get("organization_id").getAsString() + "-new");
        areaMenu.addProperty("componentString", "AreaDataNew");
        areaMenu.addProperty("alwaysShow",true);
        JsonObject meta = new JsonObject();
        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);
        areaMenu.add("meta", meta);
        return areaMenu;
    }


    private JsonObject convertAreaMenu(JsonObject org) {
        JsonObject areaMenu = new JsonObject();

        areaMenu.addProperty("path", "/" + org.get("organization_id").getAsString());
        areaMenu.addProperty("name", org.get("organization_id").getAsString());
        areaMenu.addProperty("componentString", "Area");
        areaMenu.addProperty("alwaysShow",true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        areaMenu.add("meta", meta);

        return areaMenu;
    }

    private JsonObject convertRegion(JsonObject org,String componentString){
        JsonObject region = new JsonObject();

        region.addProperty("path", "/" + org.get("organization_id").getAsString());
        region.addProperty("name", org.get("organization_id").getAsString());
        region.addProperty("componentString", componentString);
        region.addProperty("alwaysShow",true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        region.add("meta", meta);

        return region;
    }

    private JsonObject convertRegionNew(JsonObject org,String componentString) {
        JsonObject areaMenu = new JsonObject();
        areaMenu.addProperty("path", "/" + org.get("organization_id").getAsString() + "-new");
        areaMenu.addProperty("name", org.get("organization_id").getAsString() + "-new");
        areaMenu.addProperty("componentString", componentString);
        areaMenu.addProperty("alwaysShow",true);
        JsonObject meta = new JsonObject();
        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);
        areaMenu.add("meta", meta);
        return areaMenu;
    }

    private JsonObject convertRegionMenu(JsonObject org,String componentString){
        JsonObject region = new JsonObject();

        region.addProperty("path", "/" + org.get("organization_id").getAsString());
        region.addProperty("name", org.get("organization_id").getAsString());
        region.addProperty("componentString", componentString);
        region.addProperty("alwaysShow",true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        region.add("meta", meta);

        return region;
    }

    private JsonObject convertAreaDataMenu(JsonObject org) {
        JsonObject areaMenu = new JsonObject();

        areaMenu.addProperty("path", "/" + org.get("organization_id").getAsString());
        areaMenu.addProperty("name", org.get("organization_id").getAsString());
        areaMenu.addProperty("componentString", "AreaData");
        areaMenu.addProperty("alwaysShow",true);

        JsonObject meta = new JsonObject();

        meta.addProperty("title", org.get("organization_name").getAsString());
        meta.addProperty("organizationId", org.get("organization_id").getAsString());
        meta.addProperty("noCache", true);

        areaMenu.add("meta", meta);

        return areaMenu;
    }

    /**
     * 实时数据创建
     *
     * @param organizationType
     * @param updatePrompt
     * @param policyId
     * @return
     */
    private JsonObject createRealtimeDateMenu(ResourceEntity resourceEntity, String organizationType, RealtimeDateModel realtimeDateModel, boolean updatePrompt, String policyUrl) {
        JsonObject menu = new JsonObject();

        menu.addProperty("path", resourceEntity.getPath());
        menu.addProperty("componentString", resourceEntity.getComponent());
        menu.addProperty("name", "AreaData");
        JsonObject meta = new JsonObject();

        meta.addProperty("title", resourceEntity.getTitle());
        meta.addProperty("menuId",resourceEntity.getId());
        meta.addProperty("icon", resourceEntity.getIcon());
        meta.addProperty("noCache", true);

        Integer appSupport = resourceEntity.getAppSupport();
        if(Objects.nonNull(appSupport) && appSupport == 1){
            menu.addProperty("appSupport", true);
        }

        if(updatePrompt){
            menu.addProperty("showNew", true);
            meta.addProperty("showNew", true);
        }
        if(StringUtils.isNotBlank(policyUrl)){
            menu.addProperty("policyUrl", policyUrl);
            meta.addProperty("policyUrl", policyUrl);
        }

        Integer concern = resourceEntity.getConcern();
        if(Objects.nonNull(concern) && concern == 1){
            menu.addProperty("concern", true);
        }

        menu.add("meta", meta);
        JsonArray totalBusiness = new JsonArray();

        if(RequestUtils.getChannel() ==3 && organizationType.equals("zb")){
            String organizationId = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
            JsonObject childrenObj = new JsonObject();
            childrenObj.addProperty("path", "/"+organizationId);
            childrenObj.addProperty("componentString", "ZBData");
            JsonObject childrenMeta = new JsonObject();
            childrenMeta.addProperty("title", "总部");
            childrenMeta.addProperty("regions", "ZB");
            childrenMeta.addProperty("noCache", true);
            childrenObj.add("meta", childrenMeta);
            childrenObj.add("children", realtimeDateModel.getAreaDataJsonArray());
            totalBusiness.add(childrenObj);
        }else if(organizationType.equals("area")) {
            realtimeDateModel.getAreaDataJson().forEach(e -> {
                totalBusiness.add(e);
            });

        }else if(organizationType.equals("varea")){
            realtimeDateModel.getVareaDataJson().forEach(e -> {
                totalBusiness.add(e);
            });
        }else if(organizationType.equals("province")){
            realtimeDateModel.getProvinceDataJson().forEach(e -> {
                totalBusiness.add(e);
            });
        }
        else if(organizationType.equals("company")){
            realtimeDateModel.getCompanyDataJson().forEach(e -> {
                totalBusiness.add(e);
            });

        }else{
            realtimeDateModel.getBranchDataJson().forEach(e -> {
                totalBusiness.add(e);
            });

        }

        // childMap
        menu.add("children",totalBusiness);

        return menu;
    }

    /**
     * 通用菜单创建
     *
     * @param e
     * @param updatePrompt
     * @return
     */
    private JsonObject commonCreateMenu(ResourceEntity e, boolean updatePrompt, boolean otherExecFlag, String person,String policyUrl) {
        JsonObject menu = new JsonObject();
        menu.addProperty("path", e.getPath());
        menu.addProperty("componentString", e.getComponent());
        menu.addProperty("name", e.getName());
        menu.addProperty("menuId",e.getId());

        Integer appSupport = e.getAppSupport();
        if(Objects.nonNull(appSupport) && appSupport == 1){
            menu.addProperty("appSupport", true);
        }



        Integer concern = e.getConcern();
        if(Objects.nonNull(concern) && concern == 1){
            menu.addProperty("concern", true);
        }

        if(StringUtils.isNotBlank(e.getRedirect())){
            menu.addProperty("redirect", e.getRedirect());
        }
        if(e.getAlwaysShow() == 1){
            menu.addProperty("alwaysShow",true);
        }
        if(e.getHidden() == 1){
            menu.addProperty("hidden",true);
        }

        JsonObject meta = new JsonObject();
        meta.addProperty("title", e.getTitle());
        meta.addProperty("icon", e.getIcon());

        Integer hidden = e.getHidden();
        if(Objects.nonNull(hidden) && hidden == 1){
            meta.addProperty("hidden",true);
        }

        if(e.getCache() == 0){
            meta.addProperty("noCache",true);
        }
        if(updatePrompt){
            menu.addProperty("showNew",true);
            meta.addProperty("showNew", true);
        }
        if(StringUtils.isNotBlank(policyUrl)){
            menu.addProperty("policyUrl",policyUrl);
            meta.addProperty("policyUrl", policyUrl);
        }

        //加小红点(查看政策)
        if (otherExecFlag && "PolicyProbe".equals(e.getName()) && !updatePrompt ) {
            long count = policySearchService.unreadCountForCurrentMonth(person);
            if(count > 0) {
                menu.addProperty("showNew",true);
                meta.addProperty("showNew", true);
            }
        }
        meta.addProperty("menuId",e.getId());
        menu.add("meta", meta);
        return menu;
    }
}
