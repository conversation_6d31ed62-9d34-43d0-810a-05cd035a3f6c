package com.wantwant.sfa.backend.arch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.entity.SfaPosition;
import com.wantwant.sfa.backend.arch.entity.SfaPositionDuty;
import com.wantwant.sfa.backend.arch.entity.SfaPositionKpi;
import com.wantwant.sfa.backend.arch.request.*;
import com.wantwant.sfa.backend.arch.service.IPositionService;
import com.wantwant.sfa.backend.arch.vo.DeptVo;
import com.wantwant.sfa.backend.arch.vo.PositionDetailVo;
import com.wantwant.sfa.backend.arch.vo.PositionSelectVo;
import com.wantwant.sfa.backend.arch.vo.PositionVo;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.arch.SfaPositionDutyMapper;
import com.wantwant.sfa.backend.mapper.arch.SfaPositionKpiMapper;
import com.wantwant.sfa.backend.mapper.arch.SfaPositionMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/08/下午7:01
 */
@Service
@Slf4j
public class PositionService implements IPositionService {
    @Autowired
    private SfaPositionMapper sfaPositionMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private SfaPositionDutyMapper sfaPositionDutyMapper;
    @Autowired
    private SfaPositionKpiMapper sfaPositionKpiMapper;

    @Override
    @Transactional
    public void create(CPositionRequest positionRequest) {
        log.info("【position create service】request:{}",positionRequest);

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(positionRequest.getPerson(), RequestUtils.getLoginInfo());

        SfaPosition position = new SfaPosition();
        position.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        position.setPositionName(positionRequest.getPositionName());
        Long superiorPositionId = positionRequest.getSuperiorPositionId();
        if(Objects.nonNull(superiorPositionId)){
            SfaPosition parent = sfaPositionMapper.selectById(superiorPositionId);
            position.setParentPositionId(superiorPositionId);
            if(Objects.nonNull(parent)){
                String ancestors = parent.getAncestors();
                if(StringUtils.isBlank(ancestors)){
                    position.setAncestors(superiorPositionId.toString());
                }else{
                    ancestors = ancestors +"," + superiorPositionId.toString();
                    position.setAncestors(ancestors);
                }
            }
        }

        position.setDeptCode(positionRequest.getDeptCode());
        position.setStatus(positionRequest.getStatus());
        sfaPositionMapper.insert(position);


        List<PositionDutyRequest> positionDutyRequests = positionRequest.getPositionDutyRequests();
        if(!CollectionUtils.isEmpty(positionDutyRequests)){
            positionDutyRequests.forEach(e -> {
                SfaPositionDuty sfaPositionDuty = new SfaPositionDuty();
                sfaPositionDuty.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaPositionDuty.setPositionId(position.getPositionId());
                sfaPositionDuty.setCategory(e.getCategory());
                sfaPositionDuty.setContent(e.getContent());
                sfaPositionDuty.setTimes(e.getTimes());
                sfaPositionDutyMapper.insert(sfaPositionDuty);
            });
        }

        List<PositionKpiRequest> positionKpiRequestList = positionRequest.getPositionKpiRequestList();
        if(!CollectionUtils.isEmpty(positionKpiRequestList)){
            positionKpiRequestList.forEach(e -> {
                SfaPositionKpi sfaPositionKpi = new SfaPositionKpi();
                sfaPositionKpi.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaPositionKpi.setPositionId(position.getPositionId());
                sfaPositionKpi.setKpiMetrics(e.getKpiMetrics());
                sfaPositionKpi.setTarget(e.getTarget());
                sfaPositionKpi.setQuantify(e.getQuantify());
                sfaPositionKpiMapper.insert(sfaPositionKpi);
            });
        }
    }

    @Override
    @Transactional
    public void modify(UPositionRequest positionRequest) {
        log.info("【position update service】request:{}",positionRequest);

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(positionRequest.getPerson(), RequestUtils.getLoginInfo());


        SfaPosition position = sfaPositionMapper.selectById(positionRequest.getPositionId());
        if(Objects.isNull(position)){
            throw new ApplicationException("岗位信息不存在");
        }
        position.setPositionName(positionRequest.getPositionName());
        Long superiorPositionId = positionRequest.getSuperiorPositionId();
        if(Objects.nonNull(superiorPositionId)){

            if(superiorPositionId.equals(positionRequest.getPositionId())){
                throw new ApplicationException("无法指定自身为父岗位");
            }

            SfaPosition parent = sfaPositionMapper.selectById(superiorPositionId);
            position.setParentPositionId(superiorPositionId);
            if(Objects.nonNull(parent)){
                String ancestors = parent.getAncestors();
                if(StringUtils.isBlank(ancestors)){
                    position.setAncestors(superiorPositionId.toString());
                }else{
                    ancestors = ancestors +"," + superiorPositionId.toString();
                    position.setAncestors(ancestors);
                }
            }
        }

        position.setDeptCode(positionRequest.getDeptCode());
        position.setStatus(positionRequest.getStatus());
        position.setUpdateUserId(personInfo.getEmployeeId());
        position.setUpdateUserName(personInfo.getEmployeeName());
        sfaPositionMapper.updateById(position);

        // 删除所有职责
        List<SfaPositionDuty> sfaPositionDuties = sfaPositionDutyMapper.selectList(new QueryWrapper<SfaPositionDuty>().eq("position_id", positionRequest.getPositionId()).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaPositionDuties)){
            sfaPositionDuties.forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateUserId(personInfo.getEmployeeId());
                e.setUpdateUserName(personInfo.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                sfaPositionDutyMapper.updateById(e);
            });
        }
        List<PositionDutyRequest> positionDutyRequests = positionRequest.getPositionDutyRequests();
        if(!CollectionUtils.isEmpty(positionDutyRequests)){
            positionDutyRequests.forEach(e -> {
                SfaPositionDuty sfaPositionDuty = new SfaPositionDuty();
                sfaPositionDuty.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaPositionDuty.setPositionId(position.getPositionId());
                sfaPositionDuty.setCategory(e.getCategory());
                sfaPositionDuty.setContent(e.getContent());
                sfaPositionDuty.setTimes(e.getTimes());
                sfaPositionDutyMapper.insert(sfaPositionDuty);
            });
        }

        // 删除所有kpi
        List<SfaPositionKpi> sfaPositionKpis = sfaPositionKpiMapper.selectList(new QueryWrapper<SfaPositionKpi>().eq("position_id", positionRequest.getPositionId()).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaPositionKpis)){
            sfaPositionKpis.forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateUserId(personInfo.getEmployeeId());
                e.setUpdateUserName(personInfo.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                sfaPositionKpiMapper.updateById(e);
            });
        }

        List<PositionKpiRequest> positionKpiRequestList = positionRequest.getPositionKpiRequestList();
        if(!CollectionUtils.isEmpty(positionKpiRequestList)){
            positionKpiRequestList.forEach(e -> {
                SfaPositionKpi sfaPositionKpi = new SfaPositionKpi();
                sfaPositionKpi.init(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaPositionKpi.setPositionId(position.getPositionId());
                sfaPositionKpi.setKpiMetrics(e.getKpiMetrics());
                sfaPositionKpi.setQuantify(e.getQuantify());
                sfaPositionKpi.setTarget(e.getTarget());
                sfaPositionKpiMapper.insert(sfaPositionKpi);
            });
        }

    }

    @Override
    public List<PositionVo> selectList(PositionSearchRequest positionSearchRequest) {

        List<PositionVo> list =  sfaPositionMapper.searchList(positionSearchRequest);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        Map<Long, List<PositionVo>> map = new HashMap<>();
        Map<Long,PositionVo> node = new HashMap<>();
        List<PositionVo> tree = new ArrayList<>();
        // 分组合并
        list.stream().forEach(e -> {


            if(!map.containsKey(e.getParentPositionId())){

                if(Objects.isNull(e.getParentPositionId())){
                    map.put(e.getPositionId(),new ArrayList<>());
                }else{
                    List<PositionVo> positionVos = map.get(e.getParentPositionId());
                    if(CollectionUtils.isEmpty(positionVos)){
                        positionVos = new ArrayList<>();
                    }
                    positionVos.add(e);
                    map.put(e.getParentPositionId(),positionVos);
                }

            }else{
                List<PositionVo> positionVos = map.get(e.getParentPositionId());
                positionVos.add(e);
                map.put(e.getParentPositionId(),positionVos);
            }

            node.put(e.getPositionId(),e);

        });

        list.stream().filter(f -> Objects.isNull(f.getParentPositionId())).forEach(e -> {
            tree.add(e);
        });


        if(CollectionUtils.isEmpty(tree)){
            list.stream().filter(f -> Objects.nonNull(f.getParentPositionId())).forEach(e -> {
                tree.add(e);
            });
        }

        tree.forEach(e -> {
            // 转换为tree
            adaptToChildrenList(e,map);
        });


        return tree;

    }

    @Override
    public List<PositionSelectVo> selector() {
        List<SfaPosition> sfaPositions = sfaPositionMapper.selectList(new QueryWrapper<SfaPosition>().eq("status", 1).eq("delete_flag", 0));
        if(CollectionUtils.isEmpty(sfaPositions)){
            return null;
        }

        List<PositionSelectVo> list = new ArrayList<>();
        sfaPositions.forEach(e -> {
            PositionSelectVo vo = new PositionSelectVo();
            vo.setPositionId(e.getPositionId());
            vo.setPositionName(e.getPositionName());
            list.add(vo);
        });
        return list;
    }

    @Override
    public PositionDetailVo detail(Long positionId) {
        log.info("【position detail】id:{}",positionId);
        SfaPosition position = sfaPositionMapper.selectById(positionId);
        if(Objects.isNull(position)){
            throw new ApplicationException("岗位获取失败");
        }

        Long parentPositionId = position.getParentPositionId();

        PositionDetailVo vo = new PositionDetailVo();
        BeanUtils.copyProperties(position,vo);
        vo.setSuperiorPositionId(parentPositionId);

        // 获取父岗位名称
        if(Objects.nonNull(parentPositionId)){
            SfaPosition parent = sfaPositionMapper.selectById(parentPositionId);
            if(Objects.nonNull(parent)){
                vo.setSuperiorPositionName(parent.getPositionName());
            }
        }


        List<SfaPositionDuty> sfaPositionDuties = sfaPositionDutyMapper.selectList(new QueryWrapper<SfaPositionDuty>().eq("position_id", positionId).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaPositionDuties)){
            List<PositionDutyRequest> dutyLists = new ArrayList<>();
            sfaPositionDuties.forEach(e -> {
                PositionDutyRequest dutyRequest = new PositionDutyRequest();
                BeanUtils.copyProperties(e,dutyRequest);
                dutyLists.add(dutyRequest);
            });
            vo.setPositionDutyRequests(dutyLists);
        }


        List<SfaPositionKpi> sfaPositionKpis = sfaPositionKpiMapper.selectList(new QueryWrapper<SfaPositionKpi>().eq("position_id", positionId).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaPositionKpis)){
            List<PositionKpiRequest> list = new ArrayList<>();
            sfaPositionKpis.forEach(e -> {
                PositionKpiRequest positionKpiRequest = new PositionKpiRequest();
                BeanUtils.copyProperties(e,positionKpiRequest);
                list.add(positionKpiRequest);
            });
            vo.setPositionKpiRequestList(list);
        }

        return vo;
    }

    @Override
    @Transactional
    public void deletePosition(PositionOperatorRequest positionOperatorRequest) {
        Long positionId = positionOperatorRequest.getPositionId();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(positionOperatorRequest.getPerson(), RequestUtils.getLoginInfo());
        SfaPosition position = sfaPositionMapper.selectById(positionId);
        if(Objects.isNull(position)){
            throw new ApplicationException("岗位获取失败");
        }

        position.setDeleteFlag(1);
        position.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        sfaPositionMapper.updateById(position);

        // 子孙节点一并删除
        List<SfaPosition> sfaPositions = sfaPositionMapper.selectList(new LambdaQueryWrapper<SfaPosition>().like(SfaPosition::getAncestors, positionId));
        if(!CollectionUtils.isEmpty(sfaPositions)){
            sfaPositions.forEach(e -> {
                e.setDeleteFlag(1);
                e.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
                sfaPositionMapper.updateById(e);
            });
        }
    }


    private void adaptToChildrenList(PositionVo positionVo, Map<Long, List<PositionVo>> map) {
        if(map.containsKey(positionVo.getPositionId())){
            List<PositionVo> children = map.get(positionVo.getPositionId());
            positionVo.setChildren(children);
        }

        if(!CollectionUtils.isEmpty(positionVo.getChildren())){
            for(PositionVo vo : positionVo.getChildren()){
                adaptToChildrenList(vo,map);
            }
        }
    }
}
