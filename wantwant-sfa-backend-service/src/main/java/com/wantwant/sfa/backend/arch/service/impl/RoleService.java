package com.wantwant.sfa.backend.arch.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.entity.RoleEntity;
import com.wantwant.sfa.backend.arch.request.CRoleRequest;
import com.wantwant.sfa.backend.arch.request.DRoleRequest;
import com.wantwant.sfa.backend.arch.request.ERoleRequest;
import com.wantwant.sfa.backend.arch.request.SRoleRequest;
import com.wantwant.sfa.backend.arch.service.IRoleResourcesRelationService;
import com.wantwant.sfa.backend.arch.service.IRoleService;
import com.wantwant.sfa.backend.arch.vo.EmployeeRoleVo;
import com.wantwant.sfa.backend.arch.vo.RoleInfoVo;
import com.wantwant.sfa.backend.arch.vo.RoleSelectVo;
import com.wantwant.sfa.backend.arch.vo.RoleVo;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.mapper.arch.RoleMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.BeanUtils;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:11
 */
@Service
public class RoleService implements IRoleService {
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private IRoleResourcesRelationService roleResourcesRelationService;

    @Override
    @Transactional
    public void create(CRoleRequest cRoleRequest) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(cRoleRequest.getPerson(),loginInfo);

        RoleEntity roleEntity = new RoleEntity();
        BeanUtils.copyProperties(cRoleRequest,roleEntity);
        roleEntity.setCreateUserId(personInfo.getEmployeeId());
        roleEntity.setCreateUserName(personInfo.getEmployeeName());
        roleEntity.setCreateTime(LocalDateTime.now());
        roleEntity.setUpdateTime(LocalDateTime.now());
        roleEntity.setUpdateUserId(personInfo.getEmployeeId());
        roleEntity.setUpdateUserName(personInfo.getEmployeeName());
        roleMapper.insert(roleEntity);

        // 绑定资源
        roleResourcesRelationService.bindResources(roleEntity.getId(),cRoleRequest.getResources(),personInfo);
    }

    @Override
    @Transactional
    public void edit(ERoleRequest eRoleRequest) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(eRoleRequest.getPerson(),loginInfo);

        RoleEntity roleEntity = roleMapper.selectById(eRoleRequest.getRoleId());
        if(Objects.isNull(roleEntity)){
            throw new ApplicationException("获取角色信息失败");
        }
        roleEntity.setDescription(eRoleRequest.getDescription());
        roleEntity.setTerminal(eRoleRequest.getTerminal());
        roleEntity.setDataPermission(eRoleRequest.getDataPermission());
        roleEntity.setDeleteFlag(eRoleRequest.getDeleteFlag());
        roleEntity.setUpdateTime(LocalDateTime.now());
        roleEntity.setUpdateUserId(personInfo.getEmployeeId());
        roleEntity.setUpdateUserName(personInfo.getEmployeeName());
        roleMapper.updateById(roleEntity);


        // 绑定资源
        roleResourcesRelationService.bindResources(eRoleRequest.getRoleId(),eRoleRequest.getResources(),personInfo);
    }

    @Override
    public List<RoleVo> selectRoles(SRoleRequest request) {

        List<RoleEntity> list = roleMapper.selectRoles(request);
        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }

        List<RoleVo> result = new ArrayList<>();
        list.forEach(e -> {
            RoleVo vo = new RoleVo();
            BeanUtils.copyProperties(e,vo);
            vo.setRoleId(e.getId());
            vo.setCreatTime(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            result.add(vo);
        });
        return result;
    }


    @Override
    public RoleInfoVo getRoleDetail(Integer id) {
        RoleEntity roleEntity = roleMapper.selectById(id);
        if(Objects.isNull(roleEntity)){
            throw new ApplicationException("获取角色信息失败");
        }

        RoleInfoVo vo = new RoleInfoVo();
        BeanUtils.copyProperties(roleEntity,vo);
        return vo;
    }

    @Override
    @Transactional
    public void deleteRole(DRoleRequest request) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);

        RoleEntity roleEntity = roleMapper.selectById(request.getRoleId());
        if(Objects.isNull(roleEntity)){
            throw new ApplicationException("获取角色信息失败");
        }

        roleEntity.setDeleteFlag(1);
        roleEntity.setUpdateUserId(personInfo.getEmployeeId());
        roleEntity.setUpdateUserName(personInfo.getEmployeeName());
        roleEntity.setUpdateTime(LocalDateTime.now());
        roleMapper.updateById(roleEntity);
    }

    @Override
    public List<RoleSelectVo> getRoleSelect(Integer terminal) {
        List<RoleEntity> roleEntities = ListUtils.EMPTY_LIST;
        if(Objects.isNull(terminal)){
            roleEntities = roleMapper.selectList(new QueryWrapper<RoleEntity>().eq("delete_flag", 0));

        }else{
            roleEntities = roleMapper.selectList(new QueryWrapper<RoleEntity>().eq("terminal", terminal).eq("delete_flag", 0));

        }


        if(CollectionUtils.isEmpty(roleEntities)){
            return ListUtils.EMPTY_LIST;
        }

        List<RoleSelectVo> result = new ArrayList<RoleSelectVo>();
        roleEntities.forEach(e -> {
            RoleSelectVo vo = new RoleSelectVo();
            vo.setRoleId(e.getId());
            vo.setRoleName(e.getRoleName());
            vo.setTerminal(e.getTerminal());
            result.add(vo);
        });
        return result;
    }

    @Override
    public List<EmployeeRoleVo> selectEmployeeRoles(String employeeId) {
        CeoBusinessOrganizationPositionRelation relation =getCeoBusinessOrganizationPositionRelation(employeeId);
        return roleMapper.selectRoleByEmployeeId(employeeId,relation.getPositionId());
    }
    private CeoBusinessOrganizationPositionRelation getCeoBusinessOrganizationPositionRelation(String person) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation relation = checkCustomerService.getPersonInfo(person, loginInfo);
        if (Objects.isNull(relation)) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        return relation;
    }
}
