package com.wantwant.sfa.backend.audit.dto;

import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/07/上午11:19
 */
@Data
public class SelectAuditDto {
    /** 当前组织ID */
    private String currentOrganizationId;
    /** 备用候选人 */
    private String standbyEmployeeId;

    private Integer channel;

    private Integer businessGroup;

    /** 是否不跳过缺岗 */
    private boolean notSkipEmptyPosition;
    /**.是否跳过代理督导 */
    private boolean filterAgent;
}
