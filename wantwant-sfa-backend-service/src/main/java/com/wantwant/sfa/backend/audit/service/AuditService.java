package com.wantwant.sfa.backend.audit.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/07/上午11:24
 */
@Service
public class AuditService implements IAuditService {


    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Override
    public CeoBusinessOrganizationPositionRelation chooseAuditPerson(SelectAuditDto dto) {
        // 当前应当审核的组织
        String currentOrganizationId = dto.getCurrentOrganizationId();

        // 检查是否不跳过缺岗位
        if(dto.isNotSkipEmptyPosition()){
            return ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("organization_id", dto.getCurrentOrganizationId())
                    .eq("channel", dto.getChannel()));
        }

        // 递归获取父级
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = getParentPositionRelation(currentOrganizationId, dto.getChannel());
        boolean filterAgent = dto.isFilterAgent();
        if(filterAgent && Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
            String employeeName = ceoBusinessOrganizationPositionRelation.getEmployeeName();
            if(StringUtils.isNotBlank(employeeName) && employeeName.equals("代理督导")){
                ceoBusinessOrganizationPositionRelation = getParentPositionRelation(ceoBusinessOrganizationPositionRelation.getOrganizationParentId(), dto.getChannel());
            }
        }

        // 没有可选审核人，使用备用
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation) && StringUtils.isNotBlank(dto.getStandbyEmployeeId())){
            return ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", dto.getStandbyEmployeeId())
                    .eq("business_group",dto.getBusinessGroup())
                    .eq("channel", dto.getChannel())
            );

        }


        return ceoBusinessOrganizationPositionRelation;

    }

    private CeoBusinessOrganizationPositionRelation getParentPositionRelation(String parentId, int channel) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", parentId)
                .eq("channel", channel));

        // 没有上级，返回null
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            return null;
        }

        if(Objects.nonNull(ceoBusinessOrganizationPositionRelation) && StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())){
            return ceoBusinessOrganizationPositionRelation;
        }


        return getParentPositionRelation(ceoBusinessOrganizationPositionRelation.getOrganizationParentId(),channel);
    }
}
