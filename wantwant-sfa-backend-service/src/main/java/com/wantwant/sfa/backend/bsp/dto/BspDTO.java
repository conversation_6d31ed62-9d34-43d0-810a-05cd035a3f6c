package com.wantwant.sfa.backend.bsp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/16/下午6:01
 */
@Data
public class BspDTO {

    @ApiModelProperty("服务商名称")
    private String bspName;

    @ApiModelProperty("费用")
    private BigDecimal expenses;

    @ApiModelProperty("服务商ID")
    private Long bspId;

    @ApiModelProperty("是否选中")
    private boolean checked;
}
