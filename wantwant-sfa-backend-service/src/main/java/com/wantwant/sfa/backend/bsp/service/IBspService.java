package com.wantwant.sfa.backend.bsp.service;

import com.wantwant.sfa.backend.bsp.request.BspModifyRequest;
import com.wantwant.sfa.backend.bsp.request.BspRangeRequest;
import com.wantwant.sfa.backend.bsp.vo.BspVo;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/16/下午5:53
 */
public interface IBspService {

    /**
     * 获取服务商信息
     *
     * @param bspRangeRequest
     * @return
     */
    List<BspVo> selectBspVo(BspRangeRequest bspRangeRequest);

    void modify(BspModifyRequest request);
}
