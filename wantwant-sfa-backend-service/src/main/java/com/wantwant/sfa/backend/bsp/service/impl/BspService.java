package com.wantwant.sfa.backend.bsp.service.impl;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.bsp.dto.BspDTO;
import com.wantwant.sfa.backend.bsp.request.BspModifyRequest;
import com.wantwant.sfa.backend.bsp.request.BspRangeRequest;
import com.wantwant.sfa.backend.bsp.service.IBspService;
import com.wantwant.sfa.backend.bsp.vo.BspDetailVo;
import com.wantwant.sfa.backend.bsp.vo.BspVo;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.interview.model.SynDeptRangeDto;
import com.wantwant.sfa.backend.mapper.bsp.BspMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.EHRConnectorUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/16/下午5:54
 */
@Service
public class BspService implements IBspService {
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private BspMapper bspMapper;
    @Autowired
    private EHRConnectorUtil ehrConnectorUtil;

    @Override
    public List<BspVo> selectBspVo(BspRangeRequest bspRangeRequest) {
        List<BspVo> result = new ArrayList<>();
        // 获取登陆人信息
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(bspRangeRequest.getPerson(),loginInfo);

        String departmentCode = bspRangeRequest.getDepartmentCode();
        if(StringUtils.isNotBlank(departmentCode)){
            // 获取合伙人的信息
            List<BspDTO> ceoList = bspMapper.selectBspVo(bspRangeRequest,1);
            BspVo ceoBspVo = new BspVo();
            ceoBspVo.setPosition(1);
            ceoBspVo.setOrganizationId(bspRangeRequest.getDepartmentCode());
            ceoBspVo.setList(convertToVo(ceoList));
            result.add(ceoBspVo);
        }


        Integer positionTypeId = personInfo.getPositionTypeId();

        String companyCode = bspRangeRequest.getCompanyCode();
        if(StringUtils.isNotBlank(companyCode) && StringUtils.isBlank(departmentCode)){
            // 区域经理信息
            if(positionTypeId == 1 || positionTypeId == 2 || positionTypeId == 7){
                List<BspDTO> cityManagerList = bspMapper.selectBspVo(bspRangeRequest,4);
                BspVo cityManagerVo = new BspVo();
                cityManagerVo.setPosition(4);
                cityManagerVo.setOrganizationId(bspRangeRequest.getCompanyCode());
                cityManagerVo.setList(convertToVo(cityManagerList));
                result.add(cityManagerVo);
            }


            // 造旺总监
            if(positionTypeId == 1 || positionTypeId == 7){
                List<BspDTO> managerList = bspMapper.selectBspVo(bspRangeRequest,2);
                BspVo managerVo = new BspVo();
                managerVo.setPosition(2);
                managerVo.setOrganizationId(bspRangeRequest.getCompanyCode());
                managerVo.setList(convertToVo(managerList));
                result.add(managerVo);
            }
        }


        return result;
    }

    @Override
    public void modify(BspModifyRequest request) {

        // 检查选项是否存在
        Integer id = bspMapper.checkBsp(request.getBspId(),request.getOrganizationId(),request.getPosition());
        if(Objects.isNull(id)){
            throw new ApplicationException("所选渠道范围不存在");
        }


        SynDeptRangeDto synDeptRangeDto = new SynDeptRangeDto();
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);
        synDeptRangeDto.setEmployeeId(personInfo.getEmployeeId());
        synDeptRangeDto.setEmployeeName(personInfo.getEmployeeName());
        synDeptRangeDto.setDeptId(request.getBspId());
        synDeptRangeDto.setPosition(request.getPosition());
        synDeptRangeDto.setOrganizationCode(request.getOrganizationId());
        synDeptRangeDto.setSynStatus(request.getStatus());
        ehrConnectorUtil.synDeptRange(synDeptRangeDto);
    }

    private List<BspDetailVo> convertToVo(List<BspDTO> ceoList) {
        List<BspDetailVo> list = new ArrayList<>();

        if(CollectionUtils.isEmpty(ceoList)){
            return list;
        }

        ceoList.forEach(e -> {
            BspDetailVo vo = new BspDetailVo();
            BeanUtils.copyProperties(e,vo);
            vo.setExpenses(e.getExpenses().toString());
            list.add(vo);
        });

        return list;
    }
}
