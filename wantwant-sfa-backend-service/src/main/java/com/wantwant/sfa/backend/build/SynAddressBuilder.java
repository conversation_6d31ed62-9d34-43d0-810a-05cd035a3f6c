package com.wantwant.sfa.backend.build;

import com.wantwant.sfa.backend.model.SynAddressModel;

/**
 * @Description: 构建同步地址用model。
 * @Auther: zhengxu
 * @Date: 2021/07/22/下午2:05
 */
public class SynAddressBuilder {
    /**
     * 构建同步地址用model
     *
     * @param city 城市
     * @param province 省份
     * @param district 区
     * @param street 街道
     * @param receiverName 收件人姓名
     * @param receiverMobile 收件人电话
     * @param gender 性别
     * @param memberKey
     * @param tag STORE：门店，WAREHOUSE：仓库
     * @return
     */
    public static SynAddressModel build(String city,String province,String district,String street,String receiverName,
                                 String receiverMobile,Integer gender,Long memberKey,String tag){
        SynAddressModel model = new SynAddressModel();
        model.setCity(city);
        model.setProvince(province);
        model.setDistrict(district);
        model.setStreet(street);
        model.setReceiverName(receiverName);
        model.setReceiverMobileNumber(receiverMobile);
        model.setReceiverGender(gender);
        model.setMemberKey(memberKey);
        model.setTag(tag);
        return model;
    }
}
