package com.wantwant.sfa.backend.common;

import org.apache.commons.lang.StringUtils;

public enum BusinessGroupEnum {
    A(1, "产品组A", "A组"),
    B(2, "儿童乳品组", "B组"),
    C(3, "产品饮料组", "饮料组"),
    D(4, "冲刺组(综合)", "冲刺组"),
    E(5, "产品组E", "E组"),
    F(6, "零食系统组", "零食系统组"),
    <PERSON>(7, "酒品组", "酒品组"),
    H(8, "直营组", "直营组"),
    I(9, "冲刺组(休闲)", "冲刺组(休闲)"),
    J(10, "冲刺组(乳品)", "冲刺组(乳品)"),
    K(11, "酒饮组", "酒饮组"),
    L(12, "常态电销组", "常态电销组"),
    M(13, "酒品事业部", "酒品事业部"),
    N(14, "FB组", "FB组"),
    O(15, "乳品事业部", "乳品事业部"),
    P(16, "膨化事业部", "膨化事业部"),
    Q(17, "特通组", "特通组"),
    R(18, "产品直营组(乳品)", "产品直营组(乳品)"),
    S(19, "辣人组", "辣人组"),
    T(20, "酒品组(特通)", "酒品组(特通)"),
    ALL(99, "全组", "全组");



    public static String findNameById(Integer businessGroupId) {
        BusinessGroupEnum[] values = BusinessGroupEnum.values();
        for (BusinessGroupEnum e : values) {
            if (e.getBusinessGroupId() == businessGroupId) {
                return e.getBusinessGroupName();
            }
        }
        return StringUtils.EMPTY;
    }
    public static String findShortNameById(Integer businessGroupId) {
        BusinessGroupEnum[] values = BusinessGroupEnum.values();
        for (BusinessGroupEnum e : values) {
            if (e.getBusinessGroupId() == businessGroupId) {
                return e.getBusinessGroupShortName();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer findIdByShortName(String businessGroupShortName) {
        BusinessGroupEnum[] values = BusinessGroupEnum.values();
        for (BusinessGroupEnum e : values) {
            if (e.getBusinessGroupShortName().equals(businessGroupShortName)) {
                return e.getBusinessGroupId();
            }
        }
        return null;
    }

    private Integer businessGroupId;
    private String businessGroupName;
    private String businessGroupShortName;

    public Integer getBusinessGroupId() {
        return businessGroupId;
    }

    public void setBusinessGroupId(Integer businessGroupId) {
        this.businessGroupId = businessGroupId;
    }

    public String getBusinessGroupName() {
        return businessGroupName;
    }

    public void setBusinessGroupName(String businessGroupName) {
        this.businessGroupName = businessGroupName;
    }

    public String getBusinessGroupShortName() {
        return businessGroupShortName;
    }

    public void setBusinessGroupShortName(String businessGroupShortName) {
        this.businessGroupShortName = businessGroupShortName;
    }

    BusinessGroupEnum(Integer businessGroupId, String businessGroupName, String businessGroupShortName) {
        this.businessGroupId = businessGroupId;
        this.businessGroupName = businessGroupName;
        this.businessGroupShortName = businessGroupShortName;
    }

}
