package com.wantwant.sfa.backend.common;

import lombok.Data;

/**
 * @Description: 渠道枚举。
 * @Auther: zhengxu
 * @Date: 2021/11/26/下午3:34
 */
public enum ChannelEnum {
    WP(1,"旺铺"),
    OWT(2,"旺江山"),
    ZW(3,"造旺");

    private int id;

    private String name;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    ChannelEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }
}
