package com.wantwant.sfa.backend.common;

import java.util.regex.Pattern;

/**
 * @Description: 常量池。
 * @Auther: zhengxu
 * @Date: 2021/07/14/下午3:39
 */
public class CommonConstants {
    public final static String DATE_FORMAT_YEAR_MONTH = "yyyy-MM";
    public final static String LAST_TIME = "T23:59:59";
    public final static String FIRST_TIME = "T00:00:00";
    public static final String DATE_FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String WX_PAY = "微信";
    public static final String ALI_PAY = "支付宝";

    public static final String PHONE_NUMBER_REGEX = "^1[3-9]\\d{9}$";

    public static final Pattern PHONE_NUMBER_PATTERN = Pattern.compile(PHONE_NUMBER_REGEX);

    public static final String ID_CARD_REGEX = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$";

    public static final Pattern ID_CARD_PATTERN = Pattern.compile(ID_CARD_REGEX);

    public static final Integer INDONESIA_BUSINESS_GROUP_ID = 50;

}
