package com.wantwant.sfa.backend.common;

import lombok.Getter;

/**
 * sfa_employee_info.post_type
 * 岗位类型（1全职. 2.兼职）
 */
@Getter
public enum EmployeeInfoPostTypeEnum {
    FULL_TIME(1, "全职"),
    PART_TIME(2, "兼职");

    private final Integer code;
    private final String name;

    EmployeeInfoPostTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        for (EmployeeInfoPostTypeEnum item : EmployeeInfoPostTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getName();
            }
        }
        return null;
    }
}
