package com.wantwant.sfa.backend.common;

import lombok.Getter;

/**
 * sfa_employee_info.type
 * 类型(1.业务合伙人 2.企业合伙人 3.承揽合伙人 6业务BD 7承揽BD)
 */
@Getter
public enum EmployeeInfoTypeEnum {
    PARTNER_BUSINESS(1,"业务合伙人"),
    PARTNER_ENTERPRISE(2,"企业合伙人"),
    PARTNER_CONTRACT(3,"承揽合伙人"),
    BD_BUSINESS(6,"业务BD"),
    BD_CONTRACT(7, "承揽业务BD");

    private final Integer code;
    private final String name;

    EmployeeInfoTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        for (EmployeeInfoTypeEnum item : EmployeeInfoTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getName();
            }
        }
        return null;
    }
}
