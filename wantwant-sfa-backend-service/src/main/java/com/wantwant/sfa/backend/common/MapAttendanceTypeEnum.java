package com.wantwant.sfa.backend.common;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public enum MapAttendanceTypeEnum {
    CHILD10(1, 10, "上班点"),
    CHILD11(1, 11, "迟到"),
    CHILD12(1, 12, "出差"),
    CHILD20(2, 20, "通关点"),
    CHILD21(2, 21, "异常"),
    <PERSON>ILD30(3, 30, "批发"),
    CHILD31(3, 31, "终端"),
    <PERSON><PERSON>D32(3, 32, "合伙人"),
    <PERSON>ILD33(3, 33, "建档"),
    <PERSON>ILD34(3, 34, "异常"),
    <PERSON><PERSON>D40(4, 40, "线上"),
    CHILD41(4, 41, "线下");


    public static List<Integer> findMapAttendanceChildTypeList(Integer mapAttendanceType) {
        List<Integer> attendanceChildTypeList = new ArrayList<>();
        MapAttendanceTypeEnum[] values = MapAttendanceTypeEnum.values();
        for (MapAttendanceTypeEnum e : values) {
            if (Objects.equals(e.getMapAttendanceType(), mapAttendanceType)) {
                attendanceChildTypeList.add(e.getMapAttendanceChildType());
            }
        }
        return attendanceChildTypeList;
    }

    public static Integer findMapAttendanceType(Integer mapAttendanceChildType) {
        MapAttendanceTypeEnum[] values = MapAttendanceTypeEnum.values();
        for (MapAttendanceTypeEnum e : values) {
            if (Objects.equals(e.getMapAttendanceChildType(), mapAttendanceChildType)) {
                return e.getMapAttendanceType();
            }
        }
        return null;
    }

    public static Integer findMapAttendanceChildType(String mapAttendanceChildTypeName) {
        MapAttendanceTypeEnum[] values = MapAttendanceTypeEnum.values();
        for (MapAttendanceTypeEnum e : values) {
            if (Objects.equals(e.getMapAttendanceChildTypeName(), mapAttendanceChildTypeName)) {
                return e.getMapAttendanceChildType();
            }
        }
        return null;
    }

    private Integer mapAttendanceType;
    private Integer mapAttendanceChildType;
    private String mapAttendanceChildTypeName;

    public Integer getMapAttendanceType() {
        return mapAttendanceType;
    }

    public void setMapAttendanceType(Integer mapAttendanceType) {
        this.mapAttendanceType = mapAttendanceType;
    }

    public Integer getMapAttendanceChildType() {
        return mapAttendanceChildType;
    }

    public void setMapAttendanceChildType(Integer mapAttendanceChildType) {
        this.mapAttendanceChildType = mapAttendanceChildType;
    }

    public String getMapAttendanceChildTypeName() {
        return mapAttendanceChildTypeName;
    }

    public void setMapAttendanceChildTypeName(String mapAttendanceChildTypeName) {
        this.mapAttendanceChildTypeName = mapAttendanceChildTypeName;
    }


    MapAttendanceTypeEnum(Integer mapAttendanceType, Integer mapAttendanceChildType, String mapAttendanceChildTypeName) {
        this.mapAttendanceType = mapAttendanceType;
        this.mapAttendanceChildType = mapAttendanceChildType;
        this.mapAttendanceChildTypeName = mapAttendanceChildTypeName;
    }

}
