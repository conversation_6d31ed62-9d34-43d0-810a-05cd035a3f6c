package com.wantwant.sfa.backend.common;

import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.common.base.CommonConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * @Description: requst工具
 * @Auther: zhengxu
 * @Date: 2021/08/27/下午7:54
 */

public class RequestUtils {
    /** 默认channel */
    private static final int DEFAULT_CHANNEL = 3;
    /** 默认产品组 */
    private static final int DEFAULT_BUSINESS_GROUP = 1;
    private static final String CHANNEL = "channel";

    private static final String BUSINESS_GROUP = "businessGroup";

    private static final String POSITION_TYPE = "positionTypeId";

    private static final String ORGANIZATION_TYPE = "organizationType";

    private static final String AUTHORIZATION = "Authorization";

    private static final String REGION = "ww-region";

    private static final String LANGUAGE = "ww-language";

    private static final String TIMEZONE = "ww-timezone";

    public static int getChannel(){
        return Integer.parseInt(getHeaderByKey(CHANNEL, String.valueOf(DEFAULT_CHANNEL)));
    }

    public static String getToken() {
        return getHeaderByKey(AUTHORIZATION, "");
    }


    public static int getBusinessGroup(){
        return Integer.parseInt(getHeaderByKey(BUSINESS_GROUP, String.valueOf(DEFAULT_BUSINESS_GROUP)));
    }

    public static String getTimezone() {
        return getHeaderByKey(TIMEZONE, CommonConstant.TIMEZONE_ASIA_SHANGHAI);
    }

    public static String getRegion() {
        return getHeaderByKey(REGION, CommonConstant.REGION_CHINESE);
    }

    public static String getLanguage() {
        return getHeaderByKey(LANGUAGE, CommonConstant.LANGUAGE_CHINESE);
    }


    public static String getHeaderByKey(String key,String defaultVal){
        HttpServletRequest request = null;
        try {
            request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return defaultVal;
        }

        if(Objects.isNull(request)){
            return defaultVal;
        }

        String value = request.getHeader(key);
        if(StringUtils.isEmpty(value)){
            return defaultVal;
        }

        return value;
    }
    

    public static LoginModel getLoginInfo(){
        LoginModel loginModel = new LoginModel();
        int businessGroup = Integer.parseInt(getHeaderByKey(BUSINESS_GROUP, String.valueOf(DEFAULT_BUSINESS_GROUP)));
        loginModel.setBusinessGroup(businessGroup);

        String positionType = getHeaderByKey(POSITION_TYPE, "7");
        loginModel.setPositionTypeId(Integer.valueOf(positionType));

        String organizationType = getHeaderByKey(ORGANIZATION_TYPE, StringUtils.EMPTY);
        loginModel.setOrganizationType(organizationType);

        int channel = Integer.parseInt(getHeaderByKey(CHANNEL, String.valueOf(DEFAULT_CHANNEL)));
        loginModel.setChannel(channel);

        loginModel.setRegion(getHeaderByKey(REGION, CommonConstant.REGION_CHINESE));
        loginModel.setLanguage(getHeaderByKey(LANGUAGE, CommonConstant.LANGUAGE_CHINESE));
        loginModel.setTimezone(getHeaderByKey(TIMEZONE, CommonConstant.TIMEZONE_ASIA_SHANGHAI));

        return loginModel;
    }
}
