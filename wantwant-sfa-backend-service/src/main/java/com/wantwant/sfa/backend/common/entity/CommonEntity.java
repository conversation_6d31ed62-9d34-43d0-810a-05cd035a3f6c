package com.wantwant.sfa.backend.common.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/21/上午10:26
 */
@Data
public class CommonEntity {

    @TableField("delete_flag")
    private int deleteFlag;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("create_user_name")
    private String createUserName;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("update_user_name")
    private String updateUserName;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    public void init(String employeeId,String employeeName){
        this.setCreateTime(LocalDateTime.now());
        this.setCreateUserId(employeeId);
        this.setCreateUserName(employeeName);
        this.setDeleteFlag(0);
        this.update(employeeId,employeeName);
    }
    public void init(String employeeId,String employeeName,LocalDateTime time){
        this.setCreateTime(time);
        this.setCreateUserId(employeeId);
        this.setCreateUserName(employeeName);
        this.setDeleteFlag(0);
        this.update(employeeId,employeeName,time);
    }

    public void update(String employeeId,String employeeName){
        this.setUpdateTime(LocalDateTime.now());
        this.setUpdateUserId(employeeId);
        this.setUpdateUserName(employeeName);
    }

    public void update(String employeeId,String employeeName,LocalDateTime time){
        this.setUpdateTime(time);
        this.setUpdateUserId(employeeId);
        this.setUpdateUserName(employeeName);
    }

    public void delete(String employeeId,String employeeName){
        this.setDeleteFlag(1);
        this.setUpdateTime(LocalDateTime.now());
        this.setUpdateUserId(employeeId);
        this.setUpdateUserName(employeeName);
    }

    public void delete(String employeeId,String employeeName,LocalDateTime time){
        this.setDeleteFlag(1);
        this.setUpdateTime(time);
        this.setUpdateUserId(employeeId);
        this.setUpdateUserName(employeeName);
    }
}
