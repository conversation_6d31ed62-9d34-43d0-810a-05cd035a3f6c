package com.wantwant.sfa.backend.common.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/24/下午5:49
 */
@Data
@ToString
public class LoginModel {

    private Integer businessGroup;

    private Integer positionTypeId;

    private String organizationType;

    private String currentOrganizationId;

    private Integer channel;

    @ApiModelProperty(value = "地区")
    private String region;
    @ApiModelProperty(value = "语言")
    private String language;
    @ApiModelProperty(value = "时区")
    private String timezone;
}
