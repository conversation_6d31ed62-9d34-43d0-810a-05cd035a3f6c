package com.wantwant.sfa.backend.common.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/21/上午9:59
 */
@Data
public class RegionModel {
    @ApiModelProperty("操作人ID")
    @NotBlank(message = "缺少操作人ID")
    private String person;

    @ApiModelProperty("父ID")
    private String parentCode;

    @ApiModelProperty("层级")
    @NotNull(message = "缺少层级")
    private Integer level;


    private List<String> organizationIds;

    private Integer businessGroup;

    private String orgType;

    private Long memberKey;
}
