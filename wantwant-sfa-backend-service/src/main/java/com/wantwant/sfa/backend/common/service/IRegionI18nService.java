package com.wantwant.sfa.backend.common.service;

import com.wantwant.sfa.backend.common.model.RegionModel;
import com.wantwant.sfa.backend.common.vo.BusinessAreaVo;
import com.wantwant.sfa.backend.positionRegion.vo.RegionVo;

import java.util.List;

/**
 * @Description:
 * @Auther: panghuidong
 * @Date: 2025/07/21/上午 10:03
 */
public interface IRegionI18nService {
    /**
     * 获取省市区信息
     *
     * @param request
     * @return
     */
    List<RegionVo> selectList(RegionModel request);

    /**
     * 根据memberKey获取省市区信息
     *
     * @param region
     * @return
     */
    List<RegionVo> selectListByMemberKey(RegionModel region);

    /**
     * 根据memberKey获取省市信息
     *
     * @param memberKey
     * @return
     */
    List<BusinessAreaVo> selectBusinessArea(Long memberKey);


}
