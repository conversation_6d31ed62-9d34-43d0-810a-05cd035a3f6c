package com.wantwant.sfa.backend.common.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.model.RegionModel;
import com.wantwant.sfa.backend.common.request.RegionSearchRequest;
import com.wantwant.sfa.backend.common.vo.BusinessAreaVo;
import com.wantwant.sfa.backend.common.vo.InternationalRegionVO;
import com.wantwant.sfa.backend.positionRegion.vo.RegionVo;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/19/下午7:03
 */
public interface IRegionService {
    /**
     * 获取省市区信息
     *
     * @param request
     * @return
     */
    List<RegionVo> selectList(RegionModel request);

    /**
     * 根据memberKey获取省市区信息
     *
     * @param region
     * @return
     */
    List<RegionVo> selectListByMemberKey(RegionModel region);

    /**
     * 根据memberKey获取省市信息
     *
     * @param memberKey
     * @return
     */
    List<BusinessAreaVo> selectBusinessArea(Long memberKey);

    /**
     * 查询海外项目四级地地址
     *
     * @param regionSearchRequest
     * @return
     */
    List<InternationalRegionVO> selectInternationalRegion(RegionSearchRequest regionSearchRequest);
}
