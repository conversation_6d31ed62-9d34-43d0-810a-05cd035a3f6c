package com.wantwant.sfa.backend.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.model.RegionModel;
import com.wantwant.sfa.backend.common.service.IRegionI18nService;
import com.wantwant.sfa.backend.common.vo.BusinessAreaVo;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.RegionI18nMapper;
import com.wantwant.sfa.backend.mapper.SfaCustomerMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaCustomer;
import com.wantwant.sfa.backend.positionRegion.model.RegionEntity;
import com.wantwant.sfa.backend.positionRegion.vo.RegionVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Auther: panghuidong
 * @Date: 2025/07/21/上午 10:03
 */
@Service
public class RegionI18nService implements IRegionI18nService {
    @Autowired
    private RegionI18nMapper regionMapper;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Override
    public List<RegionVo> selectList(RegionModel model) {

        List<RegionEntity> regionEntities = regionMapper.selectList(model);

        regionEntities = Optional.of(regionEntities).orElse(new ArrayList<>());

        List<RegionVo> result = new ArrayList<>();
        regionEntities.stream().filter(e -> Objects.nonNull(e)).forEach(e -> {
            RegionVo vo = new RegionVo();
            BeanUtils.copyProperties(e,vo);
            result.add(vo);
        });

        return result;
    }

    @Override
    public List<RegionVo> selectListByMemberKey(RegionModel model) {
        List<RegionEntity> regionEntities = regionMapper.selectListWithMemberKey(model);

        regionEntities = Optional.of(regionEntities).orElse(new ArrayList<>());

        List<RegionVo> result = new ArrayList<>();
        regionEntities.stream().filter(e -> Objects.nonNull(e)).forEach(e -> {
            RegionVo vo = new RegionVo();
            BeanUtils.copyProperties(e,vo);
            result.add(vo);
        });

        return result;
    }


    @Override
    public List<BusinessAreaVo> selectBusinessArea(Long memberKey) {

        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", memberKey));
        if(Objects.isNull(sfaCustomer)){
            throw new ApplicationException("错误的memberKey");
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", sfaCustomer.getPositionId()));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("无法获取岗位信息");
        }

        List<BusinessAreaVo> list = regionMapper.selectBusinessArea(memberKey,ceoBusinessOrganizationPositionRelation.getPositionTypeId());

        return list;
    }

}
