package com.wantwant.sfa.backend.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.model.OrgModel;
import com.wantwant.sfa.backend.common.model.RegionModel;
import com.wantwant.sfa.backend.common.request.RegionSearchRequest;
import com.wantwant.sfa.backend.common.service.IRegionService;
import com.wantwant.sfa.backend.common.vo.BusinessAreaVo;
import com.wantwant.sfa.backend.common.vo.InternationalRegionVO;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.RegionMapper;

import com.wantwant.sfa.backend.mapper.SfaCustomerMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaCustomer;
import com.wantwant.sfa.backend.positionRegion.model.RegionEntity;
import com.wantwant.sfa.backend.positionRegion.vo.RegionVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/19/下午7:04
 */
@Service
public class RegionService implements IRegionService {
    
    /** 渠道类型常量 */
    private static final int CHANNEL_TYPE = 3;
    
    @Autowired
    private RegionMapper regionMapper;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;


    @Override
    public List<RegionVo> selectList(RegionModel model) {

        List<RegionEntity> regionEntities = regionMapper.selectList(model);

        regionEntities = Optional.of(regionEntities).orElse(new ArrayList<>());

        List<RegionVo> result = new ArrayList<>();
        regionEntities.stream().filter(e -> Objects.nonNull(e)).forEach(e -> {
            RegionVo vo = new RegionVo();
            BeanUtils.copyProperties(e,vo);
            result.add(vo);
        });

        return result;
    }

    @Override
    public List<RegionVo> selectListByMemberKey(RegionModel model) {
        List<RegionEntity> regionEntities = regionMapper.selectListWithMemberKey(model);

        regionEntities = Optional.of(regionEntities).orElse(new ArrayList<>());

        List<RegionVo> result = new ArrayList<>();
        regionEntities.stream().filter(e -> Objects.nonNull(e)).forEach(e -> {
            RegionVo vo = new RegionVo();
            BeanUtils.copyProperties(e,vo);
            result.add(vo);
        });

        return result;
    }


    @Override
    public List<BusinessAreaVo> selectBusinessArea(Long memberKey) {

        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", memberKey));
        if(Objects.isNull(sfaCustomer)){
            throw new ApplicationException("错误的memberKey");
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", sfaCustomer.getPositionId()));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("无法获取岗位信息");
        }

        List<BusinessAreaVo> list = regionMapper.selectBusinessArea(memberKey,ceoBusinessOrganizationPositionRelation.getPositionTypeId());

        return list;
    }

    @Override
    public List<InternationalRegionVO> selectInternationalRegion(RegionSearchRequest regionSearchRequest) {

        // 获取组织绑定表的组织信息
        OrgModel orgModel = getOrgCode(regionSearchRequest);

        // MyBatis通过collection自动构建层级结构
        return regionMapper.selectI18NRegion(orgModel.getOrgCode(),orgModel.getOrgType());
    }

    private OrgModel getOrgCode(RegionSearchRequest regionSearchRequest) {
        OrgModel orgModel = new OrgModel();

        String organizationCode = regionSearchRequest.getOrganizationCode();


        // 通过组织代码获取组织信息
        if (StringUtils.isNotBlank(organizationCode)) {
            return buildOrgModelByCode(organizationCode);
        }


        return orgModel;
    }

    /**
     * 根据组织代码构建组织模型
     */
    private OrgModel buildOrgModelByCode(String organizationCode) {
        String organizationType = organizationMapper.getOrganizationType(organizationCode);
        
        if (StringUtils.isBlank(organizationType)) {
            return new OrgModel();
        }

        if(!organizationType.equals(OrganizationTypeEnum.AREA.getOrganizationType()) && !organizationType.equals(OrganizationTypeEnum.COMPANY.getOrganizationType())
                && !organizationType.equals(OrganizationTypeEnum.DEPARTMENT.getOrganizationType())){
            return new OrgModel();
        }
        
        String bindOrgCode = organizationMapper.getI18NBindOrganizationCode(organizationCode);
        return createOrgModel(organizationType, bindOrgCode);
    }


    /**
     * 创建组织模型
     */
    private OrgModel createOrgModel(String orgType, String orgCode) {
        OrgModel orgModel = new OrgModel();
        orgModel.setOrgType(orgType);
        orgModel.setOrgCode(orgCode);
        return orgModel;
    }


}
