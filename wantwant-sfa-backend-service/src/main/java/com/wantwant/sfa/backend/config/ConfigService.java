package com.wantwant.sfa.backend.config;

import com.wantwant.sfa.backend.mapper.ConfigMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Component
public class ConfigService {
    @Resource
    private ConfigMapper configMapper;

    public LocalDate getCurrentDate() {
        String baseTime = configMapper.getBaseTime();
        if(StringUtils.isNotBlank(baseTime)){
            return LocalDateTime.parse(baseTime).toLocalDate();
        }

        return LocalDate.now();
    }
}
