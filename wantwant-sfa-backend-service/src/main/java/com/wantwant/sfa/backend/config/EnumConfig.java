package com.wantwant.sfa.backend.config;

import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.common.base.enums.BaseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Slf4j
@Service
public class EnumConfig {
    @PostConstruct
    public void init() {
        log.info("项目启动时执行校验枚举初始化");
        BaseEnum.enumClasses.add(BizExceptionLanguageEnum.class);
    }
}
