package com.wantwant.sfa.backend.config;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.output.TeeOutputStream;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.config
 * @Description:
 * @Date: 2024/5/15 14:27
 */
@Slf4j
public class LogFilter extends OncePerRequestFilter {
    private static final List<String> PREFIX_EXCLUSIONS =
            CollectionUtil.list(false, "/druid/", "/actuator");
    /** 不处理的请求后缀 **/
    private static final List<String> SUFFIX_EXCLUSIONS =
            CollectionUtil.list(false, ".js", ".gif", ".jpg", ".png", ".css", ".ico");
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        String contextPath = request.getContextPath();
        String url = uri.substring(contextPath.length());

        for(String pre : PREFIX_EXCLUSIONS){
            if(url.startsWith(pre)){
                filterChain.doFilter(request, response);
                return;
            }
        }
        for(String suf : SUFFIX_EXCLUSIONS){
            if(url.endsWith(suf)){
                filterChain.doFilter(request, response);
                return;
            }
        }

        StringBuilder requestBuilder = new StringBuilder();

        //请求时间
        long requestTime = System.currentTimeMillis();
        //请求端ip
        String clientIp = ServletUtil.getClientIP(request);
        requestBuilder.append("clientIP：").append(clientIp).append("\n");
        requestBuilder.append("timestamp：").append(requestTime).append("\n");

        //请求头
        Map<String, String> headerMap = ServletUtil.getHeaderMap(request);
        requestBuilder.append("headers：");
        headerMap.forEach((k, v) -> requestBuilder.append(k).append("=").append(v).append("；"));
        requestBuilder.append("\n");

        //cookie
        Cookie[] cookies = request.getCookies();
        if(Objects.nonNull(cookies) && cookies.length > 0){
            requestBuilder.append("cookies：");
            for(Cookie cookie : cookies){
                requestBuilder.append(cookie.getName()).append("=").append(cookie.getValue()).append("；");
            }
            requestBuilder.append("\n");
        }

        String parameterMapStr = toJson(request.getParameterMap());
        requestBuilder.append("URL：").append(url).append("，method：").append(request.getMethod()).append("\n")
                .append("requestParameterMap：").append(parameterMapStr).append("\n");

        BodyReaderHttpServletRequestWrapper requestWrapper = null;
        if(request instanceof HttpServletRequest) {
            //直接进行包装
            requestWrapper = new BodyReaderHttpServletRequestWrapper(request);
        }
        String requestBody = requestWrapper.getBodyStr();
        requestBuilder.append("requestBody：").append(requestBody).append("\n");

        if(ServletFileUpload.isMultipartContent(request)){
            String fileMap = getFileParam(request);
            requestBuilder.append("requestFileMap：").append(fileMap).append("\n");
        }
        log.info("请求信息：{}", requestBuilder);

        final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        response = new HttpServletResponseWrapper(response) {
            @Override
            public ServletOutputStream getOutputStream() throws IOException {
                return new TeeServletOutputStream(super.getOutputStream(), byteArrayOutputStream);
            }
        };

        if(null == requestWrapper) {
            filterChain.doFilter(request, response);
        } else {
            filterChain.doFilter(requestWrapper, response);
        }

        long costTime = System.currentTimeMillis() - requestTime;
        String responseBody = byteArrayOutputStream.toString();
        log.info("响应信息：timestamp：{}, URL：{}, total time：{} ms, responseCode：{}, responseBody：{}", requestTime, url, costTime,
                response.getStatus(), responseBody);

    }

    private String getRequestBody(HttpServletRequest request) {
        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            return "";
        }
        try {
            return IoUtil.read(request.getReader());
        } catch (IOException e) {
            log.error("获取请求体失败", e);
            return "";
        }
    }

    private String getFileParam(HttpServletRequest request) {
        MultipartResolver resolver = new StandardServletMultipartResolver();
        MultipartHttpServletRequest mRequest = resolver.resolveMultipart(request);

        Map<String, Object> param = new HashMap<>(5);
        Map<String, MultipartFile> fileMap = mRequest.getFileMap();
        if (!fileMap.isEmpty()) {
            for (Map.Entry<String, MultipartFile> fileEntry : fileMap.entrySet()) {
                MultipartFile file = fileEntry.getValue();
                param.put(fileEntry.getKey(), file.getOriginalFilename() + "(" + file.getSize() + " byte)");
            }
        }
        return toJson(param);
    }

    private static String toJson(Object object) {
        return JSONUtil.toJsonStr(object);
    }

    private static class TeeServletOutputStream extends ServletOutputStream {

        private final TeeOutputStream teeOutputStream;

        public TeeServletOutputStream(OutputStream one, OutputStream two) {
            this.teeOutputStream = new TeeOutputStream(one, two);
        }

        @Override
        public void write(byte[] b) throws IOException {
            this.teeOutputStream.write(b);
        }

        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            this.teeOutputStream.write(b, off, len);
        }

        @Override
        public void write(int b) throws IOException {
            this.teeOutputStream.write(b);
        }

        @Override
        public void flush() throws IOException {
            super.flush();
            this.teeOutputStream.flush();
        }

        @Override
        public void close() throws IOException {
            super.close();
            this.teeOutputStream.close();
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {

        }
    }

    @Slf4j
    public static class BodyReaderHttpServletRequestWrapper extends HttpServletRequestWrapper {
        private byte[] body;


        public BodyReaderHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
            super(request);
            String sessionStream = getBodyString(request);
            this.body = sessionStream.getBytes(Charset.forName("UTF-8"));
        }

        public String getBodyStr() {
            return new String(this.body, Charset.forName(StandardCharsets.UTF_8.name()));
        }

        /**
         * 获取请求Body
         *
         * @param request
         * @return
         */
        public String getBodyString(final ServletRequest request) {
            StringBuilder sb = new StringBuilder();
            InputStream inputStream = null;
            BufferedReader reader = null;
            try {
                inputStream = cloneInputStream(request.getInputStream());
                reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));
                String line = "";
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
            }
            catch (IOException e) {
                log.error("读取body失败");
            } finally {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        log.error("关闭流失败");
                    }
                }
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException e) {
                        log.error("关闭reader失败");
                    }
                }
            }
            return sb.toString();
        }

        /**
         * 复制输入流</br>
         *
         * @param inputStream
         * @return</br>
         */
        public InputStream cloneInputStream(ServletInputStream inputStream) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            try {
                while ((len = inputStream.read(buffer)) > -1) {
                    byteArrayOutputStream.write(buffer, 0, len);
                }
                byteArrayOutputStream.flush();
            }
            catch (IOException e) {
                e.printStackTrace();
            }
            InputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            return byteArrayInputStream;
        }

        @Override
        public ServletInputStream getInputStream() {
            final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(body);

            return new ServletInputStream() {
                @Override
                public int read(){
                    return byteArrayInputStream.read();
                }

                @Override
                public boolean isFinished() {
                    return false;
                }

                @Override
                public boolean isReady() {
                    return false;
                }

                @Override
                public void setReadListener(ReadListener readListener) {
                }
            };
        }
        public void setInputStream(byte[] body) {
            this.body = body;
        }

    }
}
