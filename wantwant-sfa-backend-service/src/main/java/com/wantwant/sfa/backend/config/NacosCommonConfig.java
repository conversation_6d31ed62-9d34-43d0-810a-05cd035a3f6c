package com.wantwant.sfa.backend.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.config
 * @Description:
 * @Date: 2024/6/25 10:23
 */
@RefreshScope
@Component
@Getter
@Setter
public class NacosCommonConfig {

    @Value("${common.config.assessmentMonth.department:2024-04}")
    private String newDepartmentAssessmentMonth;

    @Value("${common.config.assessmentMonth.other:2024-Q2}")
    private String newOtherAssessmentMonth;
}
