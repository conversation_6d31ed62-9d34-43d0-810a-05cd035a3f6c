package com.wantwant.sfa.backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: service配置类。
 * @Auther: zhengxu
 * @Date: 2021/10/26/上午10:05
 */
@Configuration
public class ServiceConfig {

    @Bean
    public RestTemplate serviceRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 获取当前的消息转换器列表
        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();

        // 移除所有 XML 相关的消息转换器
        messageConverters.removeIf(converter ->
                converter.getClass().getName().contains("Xml") ||
                        converter.getClass().getName().contains("Jaxb")
        );

        // 确保 JSON 消息转换器存在并配置正确
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        jsonConverter.setSupportedMediaTypes(supportedMediaTypes);

        // 如果 JSON 转换器不存在，添加它
        boolean hasJsonConverter = messageConverters.stream()
                .anyMatch(converter -> converter instanceof MappingJackson2HttpMessageConverter);

        if (!hasJsonConverter) {
            messageConverters.add(jsonConverter);
        }

        // 设置消息转换器
        restTemplate.setMessageConverters(messageConverters);

        return restTemplate;
    }


}
