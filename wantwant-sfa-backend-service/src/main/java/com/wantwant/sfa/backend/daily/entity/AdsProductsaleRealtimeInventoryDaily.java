package com.wantwant.sfa.backend.daily.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ads_productsale_realtime_inventory_daily")
@ApiModel(value = "AdsProductsaleRealtimeInventoryDaily对象", description = "库存数据日维度表")
public class AdsProductsaleRealtimeInventoryDaily implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long rowId;

    private Long timingId;

    /**
     * 年月日
     */
    private String theYearMonth;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 产品组 id
     */
    private Integer businessGroupId;

    /**
     * 产品组code
     */
    private String businessGroupCode;

    /**
     * 产品组名称
     */
    private String businessGroupName;

    /**
     * 组织id
     */
    private String organizationId;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 组织类型
     */
    private String organizationType;

    /**
     * 战区组织id
     */
    private String areaId;

    /**
     * 战区组织名称
     */
    private String areaName;

    /**
     * 大区组织id
     */
    private String vareaId;

    /**
     * 大区组织名称
     */
    private String vareaName;

    /**
     * 省区组织id
     */
    private String provinceId;

    /**
     * 省区组织名称
     */
    private String provinceName;

    /**
     * 分公司组织id
     */
    private String companyId;

    /**
     * 分公司组织名称
     */
    private String companyName;

    /**
     * 区域经理营业所组织id
     */
    private String departmentId;

    /**
     * 区域经理营业所组织名称
     */
    private String departmentName;

    /**
     * sku编码
     */
    private String skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku产品图
     */
    private String skuImages;

    /**
     * sku规格
     */
    private String skuSpec;

    /**
     * sku口味
     */
    private String skuFlavor;

    /**
     * sku生产线ID
     */
    private String skuLineId;

    /**
     * sku生产线名称
     */
    private String skuLineName;

    /**
     * sku 标签
     */
    private String skuTagName;

    /**
     * sku状态 id
     */
    private String skuStatusId;

    /**
     * sku状态 名称
     */
    private String skuStatusName;

    /**
     * 是否为管控品 1-是 0-否
     */
    private Integer skuIsControlled;

    /**
     * 常态库存当月批次箱数
     */
    private BigDecimal normalInventoryBoxesCurrentmonth;

    /**
     * 常态库存m-1批次箱数
     */
    private BigDecimal normalInventoryBoxesLast1month;

    /**
     * 常态库存m-2批次箱数
     */
    private BigDecimal normalInventoryBoxesLast2month;

    /**
     * 常态库存总箱数
     */
    private BigDecimal normalInventoryBoxesTotal;

    /**
     * 异常库存超期箱数
     */
    private BigDecimal abnormalInventoryBoxesOverdue;

    /**
     * 异常库存待报废箱数
     */
    private BigDecimal abnormalInventoryBoxesWillscrapp;

    /**
     * 异常库存总箱数
     */
    private BigDecimal abnormalInventoryBoxesTotal;

    /**
     * 锁定库存箱数
     */
    private BigDecimal lockedInventoryBoxes;

    /**
     * 占用库存箱数
     */
    private BigDecimal occupiedInventoryBoxes;

    /**
     * 周转天数
     */
    private BigDecimal turnaroundDays;

    /**
     * 实际货需量(箱数)
     */
    private BigDecimal actualDemandBoxes;

    /**
     * 当月累计已入库数量(箱数)
     */
    private BigDecimal accumulatedWarehousedBoxesCurrentmonth;

    /**
     * 在途未入库数量(箱数)
     */
    private BigDecimal onwayNotstockinBoxes;

    /**
     * 货需满足率
     */
    private BigDecimal demandSatisfactionRatio;

    /**
     * 预估数量(箱数)
     */
    private BigDecimal estimateBoxes;

    /**
     * 分配数量(箱数调整后)
     */
    private BigDecimal allocatedBoxesAfteradjustmentBoxes;

    /**
     * 预估满足率
     */
    private BigDecimal estimateSatisfactionRatio;

    /**
     * 实际出货数量(箱数)
     */
    private BigDecimal actualShipmentBoxes;

    /**
     * 预估达成率
     */
    private BigDecimal estimateAchievementRatio;

    /**
     * 可出货数量(箱数)
     */
    private BigDecimal canbeShipmentBoxes;

    /**
     * 大区剩余可分配数量(箱数)
     */
    private BigDecimal vareaRemainAllocationBox;

    /**
     * 异常锁库数量(箱数)
     */
    private BigDecimal abnormalLockedInventoryBoxes;

    /**
     * 生成数据时间
     */
    private LocalDateTime createdataTime;


}
