package com.wantwant.sfa.backend.daily.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.daily.request.DailyEmployeeRequest;
import com.wantwant.sfa.backend.daily.request.DailyInventoryRequest;
import com.wantwant.sfa.backend.daily.request.DailyNewCustomerPageRequest;
import com.wantwant.sfa.backend.daily.request.DailyOrderPageRequest;
import com.wantwant.sfa.backend.daily.request.DailyPerformancePageRequest;
import com.wantwant.sfa.backend.daily.request.DailyPerformanceRequest;
import com.wantwant.sfa.backend.daily.request.DailyPopupRequest;
import com.wantwant.sfa.backend.daily.request.DailySkuPageRequest;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeInfoVo;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeVo;
import com.wantwant.sfa.backend.daily.vo.DailyInventoryCrossVo;
import com.wantwant.sfa.backend.daily.vo.DailyOrderVo;
import com.wantwant.sfa.backend.daily.vo.DailyPerformanceVo;
import com.wantwant.sfa.backend.daily.vo.DailyPopupVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuCrossVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuVo;

public interface DailyNewService {

    DailyPopupVo dailyPopup(DailyPopupRequest request);

    void dailyViewed(DailyPopupRequest request);

    DailyEmployeeInfoVo dailyEmployee(DailyEmployeeRequest request);

    DailyPerformanceVo dailyDetail(DailyPerformanceRequest request);

    IPage<DailyPerformanceVo> dailyList(DailyPerformancePageRequest request);

    IPage<DailyOrderVo> dailyOrderList(DailyOrderPageRequest request);

    IPage<DailySkuVo> dailySkuList(DailySkuPageRequest request);

    DailySkuCrossVo dailySkuCross(DailySkuPageRequest request);

    IPage<DailyEmployeeVo> dailyNewCustomerList(DailyNewCustomerPageRequest request);

    DailyInventoryCrossVo dailyInventoryCross(DailyInventoryRequest request);
}
