package com.wantwant.sfa.backend.daily.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wantwant.sfa.backend.daily.entity.SfaDailyPopup;
import com.wantwant.sfa.backend.daily.service.DailyPopupService;
import com.wantwant.sfa.backend.mapper.daily.SfaDailyPopupMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DailyPopupServiceImpl extends ServiceImpl<SfaDailyPopupMapper, SfaDailyPopup> implements DailyPopupService {

}
