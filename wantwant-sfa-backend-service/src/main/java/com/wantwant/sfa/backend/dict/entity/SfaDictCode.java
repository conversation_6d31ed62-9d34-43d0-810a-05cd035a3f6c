package com.wantwant.sfa.backend.dict.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_dict_code")
@ApiModel(value = "SfaDictCode对象", description = "字典表")
public class SfaDictCode extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类编号")
    private String classCd;

    @ApiModelProperty(value = "分类名称")
    private String className;

    @ApiModelProperty(value = "选项值")
    private String itemValue;

    @ApiModelProperty(value = "显示内容")
    private String itemContent;

    @ApiModelProperty(value = "排序值")
    private Integer orderId;

    @ApiModelProperty(value = "备用信息")
    private String itemInfo;
}
