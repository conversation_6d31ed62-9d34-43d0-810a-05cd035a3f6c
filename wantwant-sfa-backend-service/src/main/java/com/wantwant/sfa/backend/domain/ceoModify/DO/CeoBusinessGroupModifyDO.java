package com.wantwant.sfa.backend.domain.ceoModify.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/05/21/下午4:24
 */
@Data
@ToString
public class CeoBusinessGroupModifyDO {

    @ApiModelProperty("memberKey")
    @NotNull(message = "缺少memberKey")
    private Long memberKey;

    @ApiModelProperty("增加产品组code列表")
    private List<OrgBusinessGroup> addBusinessGroupCodes;
    @ApiModelProperty("减少产品组code列表")
    private List<OrgBusinessGroup> subBusinessGroupCodes;
}
