package com.wantwant.sfa.backend.domain.ceoModify.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/05/21/下午3:21
 */
@Data
@ToString
public class CeoReplacementDO {
    @ApiModelProperty(value = "memberKey")
    @NotNull(message = "缺少memberKey")
    private Long memberKey;
}
