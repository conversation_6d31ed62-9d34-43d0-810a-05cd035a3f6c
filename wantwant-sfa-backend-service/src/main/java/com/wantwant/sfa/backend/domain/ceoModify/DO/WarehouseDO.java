package com.wantwant.sfa.backend.domain.ceoModify.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/05/21/下午2:28
 */
@Data
public class WarehouseDO {

    @ApiModelProperty(value = "仓库地址")
    private String warehouse;
    @ApiModelProperty(value = "仓库面积")
    private String warehouseArea;
    @ApiModelProperty(value = "仓库地址-经度")
    private String warehouseLongitude;
    @ApiModelProperty(value = "仓库地址-纬度")
    private String warehouseLatitude;
    @ApiModelProperty(value = "仓库门头照 多个逗号隔开")
    private String warehouseDoorPhotoUrl;
}
