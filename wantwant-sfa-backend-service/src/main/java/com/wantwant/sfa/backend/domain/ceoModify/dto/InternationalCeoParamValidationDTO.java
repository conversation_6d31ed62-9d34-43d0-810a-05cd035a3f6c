package com.wantwant.sfa.backend.domain.ceoModify.dto;

import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.interview.enums.CeoExEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 海外合伙人参数验证结果DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InternationalCeoParamValidationDTO {
    
    /**
     * 角色枚举
     */
    private CeoExEnum ceoExEnum;
    
    /**
     * 业务组实体
     */
    private SfaBusinessGroupEntity sfaBusinessGroupEntity;

    /**
     * 组织视图
     */
    private CeoBusinessOrganizationViewEntity ceoBusinessOrganizationViewEntity;

    /**
     * 组织所属渠道
     */
    private String channel;
} 