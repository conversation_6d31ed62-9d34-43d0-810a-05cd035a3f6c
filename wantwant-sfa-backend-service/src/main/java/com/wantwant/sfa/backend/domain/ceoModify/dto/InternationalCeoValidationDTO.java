package com.wantwant.sfa.backend.domain.ceoModify.dto;

import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class InternationalCeoValidationDTO {
    /** 员工表 */
    private SfaEmployeeInfoModel sfaEmployeeInfoModel;
    /** 报名表 */
    private ApplyMemberPo applyMemberPo;
    /** 兼岗表 */
    private SfaPositionRelationEntity sfaPositionRelationEntity;
}
