package com.wantwant.sfa.backend.domain.chat.DO;

import com.wantwant.sfa.backend.taskManagement.request.TaskAssignRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/03/下午3:31
 */
@Data
@ToString
public class ChatDO {
    /** 主键 */
    private Long chatId;
    /** 父ID */
    private Long parentId;
    /** 关联外间 */
    private Long fKey;
    /** 会话模块 */
    private Integer chatModule;
    /** 内容 */
    private String content;
    /** 工号 */
    private String employeeId;
    /** 姓名 */
    private String employeeName;

    @ApiModelProperty("@的用户")
    private List<TaskAssignRequest> users;
}
