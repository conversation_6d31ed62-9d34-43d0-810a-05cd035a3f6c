package com.wantwant.sfa.backend.domain.chat.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/03/下午1:53
 */
public enum ChatModuleEnum {

    MONTH_REPORT(1,"周报","/WorkStatementDetail?taskId="),
    DAILY_REPORT(2,"日报","/workDailyDetail?taskId="),
    UNKNOWN(-1,"未知","");

    private Integer moduleId;

    private String moduleName;

    private String url;

    public static ChatModuleEnum findModuleName(Integer chatModule) {

        ChatModuleEnum[] values = ChatModuleEnum.values();
        for(ChatModuleEnum e : values){
            if(e.getModuleId() == chatModule){
                return e;
            }
        }
        return UNKNOWN;
    }

    public Integer getModuleId() {
        return moduleId;
    }

    public void setModuleId(Integer moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    ChatModuleEnum(Integer moduleId, String moduleName,String url) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
        this.url = url;
    }
}
