package com.wantwant.sfa.backend.domain.chat.repository.facade;

import com.wantwant.sfa.backend.chat.request.ChatSearchRequest;
import com.wantwant.sfa.backend.domain.chat.event.ChatEvent;
import com.wantwant.sfa.backend.domain.chat.repository.po.ChatPO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午8:21
 */
public interface ChatRepositoryInterface {
    /**
     * 保存对话
     *
     * @param chatPO
     */
    void save(ChatPO chatPO);

    /**
     * 修改对话
     *
     * @param chatPO
     */
    void update(ChatPO chatPO);

    /**
     * 查询会话
     *
     * @param chatSearchRequest
     * @return
     */
    List<ChatPO> selectChat(ChatSearchRequest chatSearchRequest);

    /**
     * 删除会话
     *
     * @param chatEvent
     */
    void delete(ChatEvent chatEvent);

    /**
     * 获取回复人工号
     *
     * @param parentId
     * @return
     */
    String getReplyEmpId(Long parentId);
}
