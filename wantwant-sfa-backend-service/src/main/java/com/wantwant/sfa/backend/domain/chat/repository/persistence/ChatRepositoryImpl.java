package com.wantwant.sfa.backend.domain.chat.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.chat.request.ChatSearchRequest;
import com.wantwant.sfa.backend.domain.chat.event.ChatEvent;
import com.wantwant.sfa.backend.domain.chat.mapper.ChatMapper;
import com.wantwant.sfa.backend.domain.chat.repository.facade.ChatRepositoryInterface;
import com.wantwant.sfa.backend.domain.chat.repository.po.ChatPO;
import com.wantwant.sfa.backend.task.entity.SfaTaskChatEntity;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午8:24
 */
@Repository
public class ChatRepositoryImpl implements ChatRepositoryInterface {
    @Resource
    private ChatMapper chatMapper;

    @Override
    public void save(ChatPO chatPO) {
        chatMapper.insert(chatPO);
    }

    @Override
    public void update(ChatPO chatPO) {
        chatMapper.updateById(chatPO);
    }

    @Override
    public List<ChatPO> selectChat(ChatSearchRequest chatSearchRequest) {

        List<ChatPO> chatPOS = chatMapper.selectList(new LambdaQueryWrapper<ChatPO>().eq(ChatPO::getChatModule, chatSearchRequest.getChatModule()).eq(ChatPO::getFKey, chatSearchRequest.getFKey())
                .and(i -> i.and(p1 -> p1.isNotNull(ChatPO::getParentId).eq(ChatPO::getDeleteFlag, 0)).or(p2 -> p2.isNull(ChatPO::getParentId))));

        return chatPOS;
    }

    @Override
    public void delete(ChatEvent chatEvent) {
        ChatPO chatPO = new ChatPO();
        chatPO.setUpdateTime(LocalDateTime.now());
        chatPO.setDeleteFlag(1);
        chatMapper.update(chatPO,new LambdaQueryWrapper<ChatPO>().eq(ChatPO::getChatId,chatEvent.getChatId()));
    }

    @Override
    public String getReplyEmpId(Long parentId) {
        ChatPO chatPO = chatMapper.selectById(parentId);
        return chatPO.getEmployeeId();
    }
}
