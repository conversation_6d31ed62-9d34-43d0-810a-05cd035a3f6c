package com.wantwant.sfa.backend.domain.chat.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午8:16
 */
@TableName("sfa_chat")
@ApiModel(value = "SfaChat对象", description = "")
@Data
public class ChatPO {

    @TableId(value = "`chat_id`", type = IdType.AUTO)
    private Long chatId;

    @ApiModelProperty("父级ID")
    private Long parentId;

    @ApiModelProperty("关联外键")
    private Long fKey;

    @ApiModelProperty("会话模块(1.周报)")
    private Integer chatModule;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("操作人工号")
    private String employeeId;

    @ApiModelProperty("操作人名称")
    private String employeeName;

    @ApiModelProperty("是否删除(1.是）")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;



}
