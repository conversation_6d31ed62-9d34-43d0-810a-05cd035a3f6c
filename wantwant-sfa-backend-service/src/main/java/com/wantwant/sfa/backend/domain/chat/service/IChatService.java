package com.wantwant.sfa.backend.domain.chat.service;

import com.wantwant.sfa.backend.chat.request.ChatSearchRequest;
import com.wantwant.sfa.backend.chat.vo.ChatVo;
import com.wantwant.sfa.backend.domain.chat.DO.ChatDO;
import com.wantwant.sfa.backend.domain.chat.event.ChatEvent;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午8:11
 */
public interface IChatService {
    /**
     * 保存会话
     *
     * @param chatDO
     */
    void saveChat(ChatDO chatDO);

    /**
     * 查询会话
     *
     * @param chatSearchRequest
     * @return
     */
    List<ChatVo> selectChat(ChatSearchRequest chatSearchRequest);


    /**
     * 删除会话
     *
     * @param chatEvent
     */
    void delete(ChatEvent chatEvent);

    /**
     * 获取回复人工号
     *
     * @param parentId
     * @return
     */
    String getReplyEmpId(Long parentId);
}
