package com.wantwant.sfa.backend.domain.chat.service.factory;

import com.wantwant.sfa.backend.domain.chat.DO.ChatDO;
import com.wantwant.sfa.backend.domain.chat.repository.po.ChatPO;
import com.wantwant.sfa.backend.util.BeanUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午8:36
 */
public class ChatFactory {


    public static ChatPO createChatPO(ChatDO chatDO){
        ChatPO chatPO = new ChatPO();
        BeanUtils.copyProperties(chatDO,chatPO);

        chatPO.setDeleteFlag(0);

        if(Objects.isNull(chatDO.getChatId())){
            chatPO.setCreateTime(LocalDateTime.now());
        }

        chatPO.setUpdateTime(LocalDateTime.now());
        return chatPO;
    }
}
