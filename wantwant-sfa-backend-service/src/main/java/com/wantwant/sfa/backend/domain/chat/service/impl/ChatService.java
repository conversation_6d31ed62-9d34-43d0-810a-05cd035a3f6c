package com.wantwant.sfa.backend.domain.chat.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.chat.request.ChatSearchRequest;
import com.wantwant.sfa.backend.chat.vo.ChatVo;
import com.wantwant.sfa.backend.domain.chat.DO.ChatDO;
import com.wantwant.sfa.backend.domain.chat.event.ChatEvent;
import com.wantwant.sfa.backend.domain.chat.repository.facade.ChatRepositoryInterface;
import com.wantwant.sfa.backend.domain.chat.repository.po.ChatPO;
import com.wantwant.sfa.backend.domain.chat.service.IChatService;
import com.wantwant.sfa.backend.domain.chat.service.factory.ChatFactory;
import com.wantwant.sfa.backend.util.TreeBuilder;
import com.wantwant.sfa.backend.util.TreeUtil;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午8:12
 */
@Service
public class ChatService implements IChatService {

    @Resource
    private ChatRepositoryInterface chatRepositoryInterface;

    @Override
    @Transactional
    public void saveChat(ChatDO chatDO) {
        Optional<ChatPO> chatPOOptional = Optional.of(chatDO).map(ChatFactory::createChatPO);
        if(!chatPOOptional.isPresent()){
            throw new ApplicationException("数据错误");
        }

        Long chatId = chatDO.getChatId();
        ChatPO chatPO = chatPOOptional.get();
        if(Objects.nonNull(chatId)){
            chatRepositoryInterface.update(chatPO);
        }else{
            chatRepositoryInterface.save(chatPO);
        }

    }


    @Override
    public List<ChatVo> selectChat(ChatSearchRequest chatSearchRequest) {

        List<ChatPO> list = chatRepositoryInterface.selectChat(chatSearchRequest);

        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }


        List<ChatVo> result = new ArrayList<>();
        list.forEach(e -> {
            ChatVo chatVo = new ChatVo();
            if(e.getEmployeeId().equals(chatSearchRequest.getEmployeeId())){
                chatVo.setCanProcess(true);
            }
            chatVo.setChatId(e.getChatId());
            chatVo.setEmployeeName(e.getEmployeeName());
            chatVo.setParentId(e.getParentId());
            chatVo.setContent(e.getContent());
            chatVo.setCreateTime(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            if(Objects.isNull(e.getParentId()) && e.getDeleteFlag() == 1){
                chatVo.setContent("该条评论已删除");
                chatVo.setCanReply(false);
                chatVo.setCanProcess(false);
            }
            result.add(chatVo);
        });

        TreeBuilder treeBuilder = new TreeBuilder();
        treeBuilder.asId( e -> BeanUtil.getProperty(e, "chatId"));
        List<ChatVo> chatVos = TreeUtil.list2Tree(result,treeBuilder);
        chatVos.forEach(e -> {
            List<ChatVo> children = e.getChildren();
            if(!CollectionUtils.isEmpty(children)){
                List<ChatVo> collect = new ArrayList<>();
                addChildren(collect,children);

                collect = collect.stream().sorted(Comparator.comparing(ChatVo::getCreateTime)).collect(Collectors.toList());
                e.setChildren(collect);


            }
        });

        return chatVos;

    }

    @Override
    @Transactional
    public void delete(ChatEvent chatEvent) {
        chatRepositoryInterface.delete(chatEvent);
    }

    @Override
    public String getReplyEmpId(Long parentId) {
        if(Objects.isNull(parentId)){
            return null;
        }
        return chatRepositoryInterface.getReplyEmpId(parentId);
    }


    private void addChildren(List<ChatVo> collect, List<ChatVo> children) {

        if(CollectionUtils.isEmpty(children)){
            return;
        }

        collect.addAll(children);

        children.forEach(e -> {
            List<ChatVo> c = e.getChildren();
            addChildren(collect,c);
            e.setChildren(null);
        });

    }
}
