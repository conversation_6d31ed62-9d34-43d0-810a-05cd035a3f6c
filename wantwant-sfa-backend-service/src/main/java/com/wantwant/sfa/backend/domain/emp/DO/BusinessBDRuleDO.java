package com.wantwant.sfa.backend.domain.emp.DO;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/20/下午4:34
 */
@Data
public class BusinessBDRuleDO {
    @Excel(name="月份")
    private String theYearMonth;
    @Excel(name="分公司")
    private String companyName;
    @Excel(name="全职业务BD配置业绩要求")
    private BigDecimal fullTimePerformanceRequire;
    @Excel(name="承揽业务BD配置业绩要求")
    private BigDecimal contractPerformanceRequire;
    @Excel(name="启动月份")
    private String startYearMonth;

    private String areaName;

    private String vareaName;

    private String provinceName;

    private String organizationId;

    private String ratio;

    public String getRatio(){
        BigDecimal divide = this.fullTimePerformanceRequire.divide(this.contractPerformanceRequire, 1, RoundingMode.HALF_UP);
        this.ratio = "1:"+divide.stripTrailingZeros();
        return this.ratio;
    }
}
