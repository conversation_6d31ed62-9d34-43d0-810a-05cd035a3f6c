package com.wantwant.sfa.backend.domain.emp.DO;

import com.alibaba.excel.util.CollectionUtils;
import com.wantwant.sfa.backend.domain.emp.enums.AuditStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午10:14
 */
@Data
@ToString
public class BusinessTagDO {
    /** 标签ID */
    private Long tagId;

    @ApiModelProperty("memberKey")
    private Long memberKey;

    private Integer applyId;
    /** sfa_employee_info主键 */
    private Integer employeeInfoId;
    /** 稽核状态 */
    private Integer auditStatus;
    /** 异常原因 */
    private Integer abnormalReason;
    /** 备注 */
    private String remark;

    private LocalDate processDate;

    private String processUserId;

    private String processUserName;


    private boolean canAdd;

    private boolean canDelete;
    /**
     * 初始化空的标签
     *
     * @param employeeInfoId
     * @param employeeStatus
     * @param roleIds
     */
    public static BusinessTagDO initEmptyTag(Integer employeeInfoId,Integer employeeStatus,  List<Integer> roleIds){
        BusinessTagDO businessTagDO = new BusinessTagDO();
        businessTagDO.setEmployeeInfoId(employeeInfoId);
        businessTagDO.setAuditStatus(AuditStatusEnum.NOT_AUDIT.getId());
        businessTagDO.checkCanProcess(employeeStatus,roleIds);
        return businessTagDO;
    }

    /**
     * 检查是否可操作
     *
     * @param employeeStatus
     * @param roleIds
     */
    public void checkCanProcess(Integer employeeStatus, List<Integer> roleIds) {
        if(!CollectionUtils.isEmpty(roleIds)){
            if(roleIds.contains(27) ||  roleIds.contains(30)){
                if(employeeStatus == 1 || employeeStatus == 2){
                    this.canAdd = true;
                }

                if(Objects.nonNull(tagId)){
                    this.canDelete = true;
                }
            }
        }


    }
}
