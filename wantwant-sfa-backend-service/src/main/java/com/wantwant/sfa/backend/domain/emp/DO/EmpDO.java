package com.wantwant.sfa.backend.domain.emp.DO;

import lombok.Data;

import java.time.LocalDate;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/24/上午9:04
 */
@Data
public class EmpDO {
    /** 员工姓名 */
    private String employeeName;
    /** 工号 */
    private String employeeId;
    /** 手机号 */
    private String mobile;
    /** 组织名称 */
    private String organizationName;
    /** 组织ID */
    private String organizationId;
    /** 岗位Id */
    private String positionId;
    /** 岗位名称 */
    private String positionTypeName;
    /** 入职日期 */
    private LocalDate onBoardDate;
    /** 产品组 */
    private Integer businessGroup;
    /** 在职天数 */
    private Long workDate;
    /** 头像 */
    private String avatar;

    private Integer positionTypeId;
}
