package com.wantwant.sfa.backend.domain.emp.DO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/30/上午8:27
 */
@Data
@ApiModel("员工明细DO")
public class EmpDetailDO {
    @ApiModelProperty("员工姓名")
    private String employeeName;
    @ApiModelProperty("头像")
    private String avatar;
    private Integer applyId;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("工号")
    private String empId;
    @ApiModelProperty("入职日期")
    private LocalDate onBoardDate;
    @ApiModelProperty("入职公司")
    private String joiningCompany;
    @ApiModelProperty("岗位类型")
    private Integer positionTypeId;
    @ApiModelProperty("岗位名称")
    private String positionName;
    @ApiModelProperty("业绩信息")
    private PerformanceDO performanceDO;
    @ApiModelProperty("薪资信息")
    private SalaryDO salaryDO;
    @ApiModelProperty("岗位描述")
    private List<PositionDO> positionDOS;
    @ApiModelProperty("服务对象")
    private List<ServerObjDO> serverObjDOS;
    @ApiModelProperty("员工状态")
    private String employeeStatus;

    @ApiModelProperty("办公地点code")
    private String workPlace;
    @ApiModelProperty("办公地点名称")
    private String workPlaceName;

    private Long memberKey;

    @ApiModelProperty(value = "入职天数")
    private Long onboardDays;
    /**
     * 主兼岗排序
     */
    public void orderByPositionByPartTime(){
        List<PositionDO> positionDOS = this.positionDOS;
        this.positionDOS = positionDOS.stream().sorted(Comparator.comparing(PositionDO::getPartTime)).collect(Collectors.toList());
    }
}
