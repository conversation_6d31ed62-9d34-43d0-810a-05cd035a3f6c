package com.wantwant.sfa.backend.domain.emp.DO;

import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2025/03/04/下午3:05
 */
@Data
@Builder
public class EstablishSearchDO {
    /** 类型 */
    private Integer checkType;
    /** 服务对象employeeInfoId */
    private Integer serverEmployeeInfoId;
    /** 申请ID */
    private Integer applyId;
    /** 岗位枚举 */
    private PositionEnum positionEnum;
    /** 旧岗位 */
    private PositionEnum oldPositionEnum;
    /** 年月 */
    private String theYearMonth;
    /** 产品组 */
    private Integer businessGroup;
    /** 是否更换服务对象 */
    private boolean changeServer;
    /** 营业所ID */
    private String departmentId;
    /** 原薪资 */
    private BigDecimal oldSalary;
    /** 新薪资 */
    private BigDecimal newSalary;
}
