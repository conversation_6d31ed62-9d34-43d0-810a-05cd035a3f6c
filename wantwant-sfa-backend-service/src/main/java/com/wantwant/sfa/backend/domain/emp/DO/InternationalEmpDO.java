package com.wantwant.sfa.backend.domain.emp.DO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class InternationalEmpDO {

    @ApiModelProperty("会员key")
    @NotNull(message = "Missing memberKey")
    private Long memberKey;

    @ApiModelProperty("sap编码")
    private String sapCode;

    @ApiModelProperty("sap客户名称")
    private String sapCustomerName;

    @ApiModelProperty("联系人-姓名")
    @NotBlank(message = "Missing memberName")
    private String memberName;

    @ApiModelProperty("联系人-手机号")
    @NotBlank(message = "Missing mobilePhone")
    private String mobilePhone;

    @ApiModelProperty("联系人-性别: M-男、F-女")
    @NotBlank(message = "Missing gender")
    private String gender;

    @ApiModelProperty("联系人-职位")
    private String jobTittle;

    @ApiModelProperty("联系人-省")
    private String province;

    @ApiModelProperty("联系人-市")
    private String city;

    @ApiModelProperty("联系人-区县")
    private String district;

    @ApiModelProperty("联系人-街道")
    private String street;

    @ApiModelProperty("联系人-详细地址")
    private String detailAddress;

    @ApiModelProperty("战区")
    private String areaName;

    @ApiModelProperty("大区")
    private String regionName;

    @ApiModelProperty("省区")
    private String provinceName;

    @ApiModelProperty("分公司")
    private String companyName;

    @ApiModelProperty("营业所")
    @NotBlank(message = "Missing branchName")
    private String branchName;


    @ApiModelProperty("角色类型:2:分公司总监  3:流通合伙人 4:区域经理  5:直营合伙人  6:战区督导、7-省区总监 11-大区总监")
    @NotBlank(message = "Missing roleType")
    private Integer roleType;

    @ApiModelProperty("产品组ID")
    @NotBlank(message = "Missing productGroupId")
    private String productGroupId;

    @ApiModelProperty("开户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openAccountTime;
}
