package com.wantwant.sfa.backend.domain.emp.DO;


import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/30/上午8:42
 */
@Data
public class PositionDO {
    @ApiModelProperty("业务组ID")
    private Integer businessGroup;
    @ApiModelProperty("业务组名称")
    private String businessGroupName;
    @ApiModelProperty("是否兼岗")
    private Integer partTime;
    @ApiModelProperty("开始日期")
    private LocalDate startTime;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("战区名字")
    private String areaName;
    @ApiModelProperty("战区code")
    private String areaCode;
    @ApiModelProperty("大区名字")
    private String vareaName;
    @ApiModelProperty("大区code")
    private String vareaCode;
    @ApiModelProperty("省区名字")
    private String provinceName;
    @ApiModelProperty("省区CODE")
    private String provinceCode;
    @ApiModelProperty("分公司名字")
    private String companyName;
    @ApiModelProperty("分公司CODE")
    private String companyCode;
    @ApiModelProperty("营业所名字")
    private String departmentName;
    @ApiModelProperty("营业所CODE")
    private String departmentCode;
    @ApiModelProperty("业务组名称")
    private String positionDesc;

    public void setPositionDesc(){
        String prefix = this.businessGroupName + ",";
        List<String> orgNames = new ArrayList<>();
        if(StringUtils.isNotBlank(this.areaName)){
            orgNames.add(this.areaName);
        }
        if(StringUtils.isNotBlank(this.vareaName)){
            orgNames.add(this.vareaName);
        }
        if(StringUtils.isNotBlank(this.provinceName)){
            orgNames.add(this.provinceName);
        }
        if(StringUtils.isNotBlank(this.companyName)){
            orgNames.add(this.companyName);
        }
        if(StringUtils.isNotBlank(this.departmentName)){
            orgNames.add(this.departmentName);
        }
        String orgDesc = String.join("/",orgNames);
        this.positionDesc = prefix + orgDesc;
    }

}
