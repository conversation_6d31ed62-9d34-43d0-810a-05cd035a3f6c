package com.wantwant.sfa.backend.domain.emp.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午4:42
 */
@Data
@ToString
public class ProcessUserDO {
    @ApiModelProperty("员工ID")
    private String employeeId;
    @ApiModelProperty("员工姓名")
    private String employeeName;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("组织名称")
    private String organizationName;
    @ApiModelProperty("岗位名称")
    private String positionName;

    private Integer businessGroup;
    @ApiModelProperty("角色ID")
    private List<Integer> roleIds;
}
