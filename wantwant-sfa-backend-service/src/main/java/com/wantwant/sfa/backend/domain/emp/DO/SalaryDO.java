package com.wantwant.sfa.backend.domain.emp.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/30/上午8:30
 */
@Data
public class SalaryDO {
    @ApiModelProperty("薪资方案ID")
    private Integer id;
    @ApiModelProperty("开始日期")
    private LocalDate startDate;
    @ApiModelProperty("总薪资")
    private String total;
    @ApiModelProperty("薪资方案等级")
    private String level;
    @ApiModelProperty("底薪")
    private String baseSalary;
    @ApiModelProperty("津贴")
    private String allowance;
    @ApiModelProperty("社保基数")
    private String socialSecurityBase;
}
