package com.wantwant.sfa.backend.domain.emp.convert;

import com.alibaba.excel.util.CollectionUtils;
import com.wantwant.sfa.backend.domain.emp.DO.*;
import com.wantwant.sfa.backend.employee.vo.*;
import com.wantwant.sfa.backend.util.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/30/下午6:02
 */
public class EmpConvert {

    public static EmpDetailVO convertToVO(EmpDetailDO empDetailDO){
        if(Objects.isNull(empDetailDO)){
            return null;
        }
        EmpDetailVO empDetailVO = new EmpDetailVO();
        BeanUtils.copyProperties(empDetailDO,empDetailVO);


        SalaryDO salaryDO = empDetailDO.getSalaryDO();
        if(Objects.nonNull(salaryDO)){
            SalaryVO salaryVO = new SalaryVO();
            BeanUtils.copyProperties(salaryDO,salaryVO);
            empDetailVO.setSalaryVo(salaryVO);
        }

        PerformanceDO performanceDO = empDetailDO.getPerformanceDO();
        if(Objects.nonNull(performanceDO)){
            PerformanceVo performanceVo = new PerformanceVo();
            BeanUtils.copyProperties(performanceDO,performanceVo);
            empDetailVO.setPerformanceVo(performanceVo);
        }

        List<PositionDO> positionDOS = empDetailDO.getPositionDOS();
        if(!CollectionUtils.isEmpty(positionDOS)){
            List<EmpPositionVO> list = new ArrayList<>();
            positionDOS.forEach(e -> {
                EmpPositionVO empPositionVO = new EmpPositionVO();
                BeanUtils.copyProperties(e,empPositionVO);
                list.add(empPositionVO);
            });
            empDetailVO.setPositionDOS(list);
        }

        List<ServerObjDO> serverObjDOS = empDetailDO.getServerObjDOS();
        if(!CollectionUtils.isEmpty(serverObjDOS)){
            List<ServerObjVO> list = new ArrayList<>();
            serverObjDOS.forEach(e -> {
                ServerObjVO serverObjVO = new ServerObjVO();
                BeanUtils.copyProperties(e,serverObjVO);
                list.add(serverObjVO);
            });
            empDetailVO.setServerObjDOS(list);
        }

        return empDetailVO;
    }
}
