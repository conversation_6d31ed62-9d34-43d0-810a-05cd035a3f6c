package com.wantwant.sfa.backend.domain.emp.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午9:46
 */
public enum AbnormalReasonEnum {
    NOTHING(0,""),
    PERFORMANCE_ATTACHMENT(1,"业绩挂靠"),
    CUSTOMER_FRAUD(2,"客户造假"),
    CUSTOMER_CASH_OUT(3,"客户套现"),
    OPERATE_CEO(4,"操作合伙人账号"),
    POOR_MANAGEMENT(5,"管理不力"),
    <PERSON><PERSON><PERSON>(6,"其他"),
    CEO_INFO_FACE(7,"合伙人虚假"),
    OR<PERSON>R_FACE(8,"订单虚假"),
    CEO_CASH_OUT(9,"协助业务套现");


    private int id;

    private String name;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    AbnormalReasonEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }


    public static String getNameById(int id){
        Optional<AbnormalReasonEnum> optional = Arrays.asList(AbnormalReasonEnum.values()).stream().filter(f -> f.getId() == id).findFirst();
        if(optional.isPresent()){
            return optional.get().getName();
        }

        return NOTHING.getName();
    }
}
