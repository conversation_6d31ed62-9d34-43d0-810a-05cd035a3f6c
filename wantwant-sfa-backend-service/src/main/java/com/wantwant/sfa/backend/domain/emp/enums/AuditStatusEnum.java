package com.wantwant.sfa.backend.domain.emp.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午11:16
 */
public enum AuditStatusEnum {
    NOT_AUDIT(0,"未稽核"),
    NORMAL(1,"正常"),
    ABNORMAL(2,"异常");

    private int id;

    private String name;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    AuditStatusEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }
}
