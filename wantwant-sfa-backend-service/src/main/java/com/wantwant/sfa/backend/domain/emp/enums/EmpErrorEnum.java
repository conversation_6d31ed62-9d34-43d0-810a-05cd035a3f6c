package com.wantwant.sfa.backend.domain.emp.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/30/上午10:44
 */
public enum EmpErrorEnum {
    INFO_ID_NOT_EXIST("E1000001","错误的员工ID"),
    POSITION_NOT_EXIST("E1000002","错误的岗位ID"),
    APPLY_MEMBER_NOT_EXIST("E1000003","错误的报名信息");

    private String errCode;

    private String errMsg;

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    EmpErrorEnum(String errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }
}
