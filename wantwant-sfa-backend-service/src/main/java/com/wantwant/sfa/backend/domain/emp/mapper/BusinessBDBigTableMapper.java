package com.wantwant.sfa.backend.domain.emp.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.CompileDepartmentData;
import com.wantwant.sfa.backend.domain.businessBd.DO.ExternalQuota;
import com.wantwant.sfa.backend.domain.emp.repository.model.BusinessBDCompileDetailModel;
import com.wantwant.sfa.backend.salary.request.BusinessBDSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/下午9:50
 */
public interface BusinessBDBigTableMapper {

    /**
     * 获取累计业绩
     *
     * @param theYearMonth
     * @param memberKey
     * @return
     */
    BusinessBDDetailVO getAddedPerformance(@Param("theYearMonth") String theYearMonth, @Param("memberKey") Long memberKey);

    List<BusinessBDDetailVO> getBusinessBDDetail(@Param("page") IPage<BusinessBDDetailVO> page, @Param("request") BusinessBDSearchRequest request);

    List<BusinessBDCompileDetailModel> getExceedEstablished(@Param("theYearMonth")String theYearMonth,@Param("orgCodes")List<String>orgCodes);

    /**
     * 查询大数据额度
     *
     * @param theYearMonth
     * @param businessGroupCodes
     * @return
     */
    List<ExternalQuota> selectExternalQuota(@Param("theYearMonth") String theYearMonth, @Param("businessGroupCodes") List<String> businessGroupCodes);

    List<CompileDepartmentData> selectCompileDepartmentDataList(@Param("departmentIds") List<String> departmentIds, @Param("theYearMonth") String yearMonth);
}
