package com.wantwant.sfa.backend.domain.emp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdRulePO;
import com.wantwant.sfa.backend.salary.request.BusinessBDRuleSearchRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/上午10:05
 */
public interface BusinessBDRuleMapper extends BaseMapper<BusinessBdRulePO> {

    List<BusinessBDRuleDO> searchBusinessBDRule(@Param("page") IPage page, @Param("request") BusinessBDRuleSearchRequest request);
}
