package com.wantwant.sfa.backend.domain.emp.repository.facade;

import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.domain.emp.DO.*;
import com.wantwant.sfa.backend.domain.emp.repository.model.EmpModel;
import com.wantwant.sfa.backend.entity.CustomerInfo;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/18/上午8:42
 */
public interface EmpRepositoryInterface {

    /**
     * 根据memberKey获取工号
     *
     * @param memberKeys
     * @return
     */
    List<SfaPositionRelationEntity> selectPositionRelationByMemberKey(List<Long> memberKeys);


    List<SfaPositionRelationEntity> selectPositionRelation(Long memberKey,boolean filterQuit);

    /**
     * 获取员工基本信息
     *
     * @param acceptedMemberKey
     * @return
     */
    EmpBaseDO selectEmpNameByMemberKey(Long acceptedMemberKey);

    /**
     * 根据组织查询人员状态
     *
     * @param acceptedOrganizationId
     * @return
     */
    EmpDO selectManagerByOrgCode(String acceptedOrganizationId);

    /**
     * 查询组织管理岗人员信息
     *
     * @param organizationId
     * @return
     */
    EmpDO selectManager(String organizationId);

    /**
     * 根据memberKey及产品组获取合伙人信息
     *
     * @param memberKey
     * @param productGroupId
     * @return
     */
    EmpDO selectEmpByMemberKey(Long memberKey, String productGroupId);

    /**
     * 根据工号获取角色id
     *
     * @param empId
     * @return
     */
    List<RoleEmployeeRelationEntity> selectRoleIdsByEmpId(String empId);

    /**
     * 根据employeeInfoId获取employeeInfo信息
     *
     * @param employeeInfoId
     * @return
     */
    SfaEmployeeInfoModel selectEmployeeInfoById(Integer employeeInfoId);

    /**
     * 根据applyId获取applyMember详情
     *
     * @param applicationId
     * @return
     */
    ApplyMemberPo selectApplyMemberById(Integer applicationId);

    /**
     * 根据employeeInfoId获取服务对象
     *
     * @param employeeInfoId
     * @return
     */
    List<ServerObjDO> selectServerObj(Integer employeeInfoId);

    /**
     * 获取业绩数据
     *
     * @param employeeInfoId
     * @param lastMonth
     * @param lastQuarter
     * @return
     */
    PerformanceDO getPerformanceRate(Integer employeeInfoId, String lastMonth, String lastQuarter);

    /**
     * 获取薪资信息
     *
     * @param employeeInfoId
     * @return
     */
    SalaryDO selectSalaryInfo(Integer employeeInfoId);

    /**
     * 获取登陆人组织code
     *
     * @param person
     * @param businessGroup
     * @param positionTypeId
     * @return
     */
    List<SfaPositionRelationEntity> selectLoginUserOrg(String person, int businessGroup, Integer positionTypeId);

    /**
     * 获取员工信息
     *
     * @param name
     * @return
     */
    List<EmpModel> selectEmployeeInfoByKey(String name,List<String>orgCodes,Integer positionTypeId,int businessGroup);

    /**
     * 获取客户信息
     *
     * @param name
     * @return
     */
    List<CustomerInfo> selectCustomerByKey(String name,List<String>orgNames,int positionTypeId,int businessGroup);

    SfaEmployeeInfoModel selectEmployeeInfoByApplyId(Integer applyId);

    SfaEmployeeInfoModel selectEmployeeInfoByMobile(String userMobile);

    SfaEmployeeInfoModel selectEmployeeInfoByMemberKey(Long memberKey);


    List<CeoDO> selectCeoByOrgCode(String organizationId);

    int getBusinessBDCount(int type, Long memberKey, int businessGroup);

    int selectJobTaskBDCount(int type, String companyOrganizationId, Integer serverEmpInfoId, LocalDate startDate, LocalDate endDate);

    List<BusinessBDDO> selectBusinessBDByMemberKey(@Param("memberKey") Long memberKey, @Param("businessGroupCode") String businessGroupCode);

    /**
     * 根据工号获取empID
     *
     * @param empId
     * @return
     */
    SfaEmployeeInfoModel selectEmployeeInfoByEmpId(String empId);

    /**
     * 获取角色ID
     *
     * @param person
     * @param businessGroup
     * @return
     */
    List<Integer> selectEmpRoleIds(String person, int businessGroup);


    String getEmpIdByMemberKey(Long memberKey);


}
