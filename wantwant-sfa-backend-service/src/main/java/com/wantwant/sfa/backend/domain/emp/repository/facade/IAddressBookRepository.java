package com.wantwant.sfa.backend.domain.emp.repository.facade;

import com.wantwant.sfa.backend.domain.emp.repository.po.AddressBookPO;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface IAddressBookRepository {

    /**
     * 批量插入数据
     *
     * @param addressBookPOList
     */
    void batchInsert(List<AddressBookPO> addressBookPOList);

    /**
     * 删除memberKey
     *
     * @param memberKey
     */
    void deleteByMemberKey(Long memberKey);

    /**
     * 根据类型删除
     *
     * @param memberKey
     * @param type
     */
    void deleteByType(@NotNull(message = "缺少memberKey") Long memberKey, int type);
}
