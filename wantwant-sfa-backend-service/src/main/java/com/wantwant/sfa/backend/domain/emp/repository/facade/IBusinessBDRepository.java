package com.wantwant.sfa.backend.domain.emp.repository.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdRulePO;
import com.wantwant.sfa.backend.interview.entity.BusinessBdControlEntity;
import com.wantwant.sfa.backend.salary.request.BusinessBDRuleSearchRequest;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/上午10:02
 */
public interface IBusinessBDRepository {
    /**
     * 插入数据
     *
     * @param businessBdRulePO
     */
    void insert(BusinessBdRulePO businessBdRulePO);

    /**
     * 查询业务BD编制自动化
     *
     * @param page
     * @param request
     * @return
     */
    List<BusinessBDRuleDO> searchBusinessBDRule(IPage page, BusinessBDRuleSearchRequest request);


    /**
     * 查询业务BD编制自动化
     *
     * @param request
     * @return
     */
    List<BusinessBDRuleDO> searchBusinessBDRule(BusinessBDRuleSearchRequest request);


    /**
     * 根据营业所获取配置
     *
     * @param departmentId
     * @return
     */
    BusinessBdConfigPO getConfig(String departmentId);

    /**
     * 查询组织业务BD数量配置
     *
     * @param departmentId
     * @return
     */
    BusinessBdControlEntity selectBusinessBDControl(String departmentId);

    /**
     * 根据组织ID，月份删除当前有效的业务BD
     *
     * @param theYearMonth
     * @param orgCode
     */
    void deleteLastRule(String theYearMonth, String orgCode);
}
