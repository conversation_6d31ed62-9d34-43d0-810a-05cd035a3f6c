package com.wantwant.sfa.backend.domain.emp.repository.facade;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessTagPO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午10:45
 */
public interface IBusinessTagRepository {

    /**
     * 增加标签
     *
     * @param businessTagPO
     */
    Long addTag(BusinessTagPO businessTagPO);

    /**
     * 根据ID删除标签
     *
     * @param tagId
     * @param processUserDO
     */
    void deleteTag(Long tagId, ProcessUserDO processUserDO);

    /**
     * 查询最新标签
     *
     * @param employeeInfoId
     * @return
     */
    BusinessTagPO selectLastTag(Integer employeeInfoId);

    /**
     * 查询历史
     *
     * @param employeeInfoId
     * @return
     */
    List<BusinessTagPO> selectHistory(Integer employeeInfoId);

}
