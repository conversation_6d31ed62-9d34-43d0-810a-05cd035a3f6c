package com.wantwant.sfa.backend.domain.emp.repository.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.CompileDepartmentData;
import com.wantwant.sfa.backend.domain.businessBd.DO.ExternalQuota;
import com.wantwant.sfa.backend.domain.emp.repository.model.BusinessBDCompileDetailModel;
import com.wantwant.sfa.backend.salary.request.BusinessBDSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDDetailVO;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/下午9:46
 */
public interface IEmpBigTableRepository {

    BusinessBDDetailVO getAddedPerformance(String theYearMonth, Long memberKey);

    List<BusinessBDDetailVO> getBusinessBDDetail(IPage<BusinessBDDetailVO> page, BusinessBDSearchRequest request);

    /**
     * 超出编制数查询
     *
     * @param theYearMonth
     * @return
     */
    List<BusinessBDCompileDetailModel> getExceedEstablished(String theYearMonth,List<String>orgCodes);

    /**
     * 外部指标查询
     *
     * @param theYearMonth
     * @param businessGroupCodes
     * @return
     */
    List<ExternalQuota> selectExternalQuota(String theYearMonth, List<String> businessGroupCodes);

    /**
     * 查询组织业绩及当月额度
     *
     * @param departmentIds
     * @param yearMonth
     * @return
     */
    List<CompileDepartmentData> selectCompileDepartmentDataList(List<String> departmentIds, @NotBlank(message = "缺少月份") String yearMonth);
}
