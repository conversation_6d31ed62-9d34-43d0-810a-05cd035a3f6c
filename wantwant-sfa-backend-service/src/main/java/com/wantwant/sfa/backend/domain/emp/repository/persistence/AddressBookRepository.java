package com.wantwant.sfa.backend.domain.emp.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wantwant.sfa.backend.domain.emp.mapper.AddressBookMapper;
import com.wantwant.sfa.backend.domain.emp.repository.facade.IAddressBookRepository;
import com.wantwant.sfa.backend.domain.emp.repository.po.AddressBookPO;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public class AddressBookRepository extends ServiceImpl<AddressBookMapper, AddressBookPO> implements IAddressBookRepository {

    @Override
    @Transactional
    public void batchInsert(List<AddressBookPO> addressBookPOList) {
        this.saveBatch(addressBookPOList);
    }

    @Override
    @Transactional
    public void deleteByMember<PERSON>ey(Long memberKey) {
        baseMapper.delete(new LambdaQueryWrapper<AddressBookPO>().eq(AddressBookPO::getMember<PERSON>ey,memberKey).eq(AddressBookPO::getDeleteFlag,0));
    }

    @Override
    @Transactional
    public void deleteByType(Long memberKey, int type) {
        baseMapper.delete(new LambdaQueryWrapper<AddressBookPO>().eq(AddressBookPO::getMemberKey,memberKey)
                .eq(AddressBookPO::getType,type).eq(AddressBookPO::getDeleteFlag,0));
    }
}
