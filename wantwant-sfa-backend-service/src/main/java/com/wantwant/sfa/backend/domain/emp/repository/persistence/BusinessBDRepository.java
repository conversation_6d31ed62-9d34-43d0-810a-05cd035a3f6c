package com.wantwant.sfa.backend.domain.emp.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.mapper.BusinessBDConfigMapper;
import com.wantwant.sfa.backend.domain.emp.mapper.BusinessBDRuleMapper;
import com.wantwant.sfa.backend.domain.emp.repository.facade.IBusinessBDRepository;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdRulePO;
import com.wantwant.sfa.backend.interview.entity.BusinessBdControlEntity;
import com.wantwant.sfa.backend.mapper.businessBD.BusinessBdControlMapper;
import com.wantwant.sfa.backend.salary.request.BusinessBDRuleSearchRequest;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/上午10:02
 */
@Repository
public class BusinessBDRepository implements IBusinessBDRepository {
    @Resource
    private BusinessBDRuleMapper businessBDRuleMapper;
    @Resource
    private BusinessBDConfigMapper businessBDConfigMapper;
    @Resource
    private BusinessBdControlMapper businessBdControlMapper;

    @Override
    public void insert(BusinessBdRulePO businessBdRulePO) {
        businessBDRuleMapper.insert(businessBdRulePO);
    }

    @Override
    public List<BusinessBDRuleDO> searchBusinessBDRule(IPage page, BusinessBDRuleSearchRequest request) {
        return businessBDRuleMapper.searchBusinessBDRule(page,request);
    }

    @Override
    public List<BusinessBDRuleDO> searchBusinessBDRule(BusinessBDRuleSearchRequest request) {
        return businessBDRuleMapper.searchBusinessBDRule(null,request);
    }

    @Override
    public BusinessBdConfigPO getConfig(String departmentId) {
        return businessBDConfigMapper.selectOne(new LambdaQueryWrapper<BusinessBdConfigPO>()
                .eq(BusinessBdConfigPO::getDepartmentId,departmentId)
                .eq(BusinessBdConfigPO::getDeleteFlag,0)
                .last("limit 1"));
    }

    @Override
    public BusinessBdControlEntity selectBusinessBDControl(String departmentId) {
        return businessBdControlMapper.selectOne(new LambdaQueryWrapper<BusinessBdControlEntity>()
                .eq(BusinessBdControlEntity::getOrganizationId,departmentId)
                .eq(BusinessBdControlEntity::getStatus,1)
                .last("limit 1"));
    }

    @Override
    public void deleteLastRule(String theYearMonth, String orgCode) {

        BusinessBdRulePO businessBdRulePO = new BusinessBdRulePO();
        businessBdRulePO.setDeleteFlag(1);

        businessBDRuleMapper.update(businessBdRulePO,new LambdaQueryWrapper<BusinessBdRulePO>().eq(BusinessBdRulePO::getTheYearMonth,theYearMonth)
            .eq(BusinessBdRulePO::getCompanyCode,orgCode).eq(BusinessBdRulePO::getDeleteFlag,0));
    }

    public BusinessBdRulePO selectLastRule(String theYearMonth, String organizationId) {
        return businessBDRuleMapper.selectOne(new LambdaQueryWrapper<BusinessBdRulePO>().le(BusinessBdRulePO::getTheYearMonth,theYearMonth)
                .eq(BusinessBdRulePO::getCompanyCode,organizationId).eq(BusinessBdRulePO::getDeleteFlag,0).orderByDesc(BusinessBdRulePO::getRuleId).last("limit 1"));
    }
}
