package com.wantwant.sfa.backend.domain.emp.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.mapper.BusinessTagMapper;
import com.wantwant.sfa.backend.domain.emp.repository.facade.IBusinessTagRepository;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessTagPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午10:45
 */
@Repository
public class BusinessTagRepository implements IBusinessTagRepository {

    @Resource
    private BusinessTagMapper businessTagMapper;

    @Override
    public Long addTag(BusinessTagPO businessTagPO) {
        businessTagMapper.insert(businessTagPO);
        return businessTagPO.getTagId();
    }

    @Override
    public void deleteTag(Long tagId, ProcessUserDO processUserDO) {
        BusinessTagPO deleteBusinessTag = new BusinessTagPO();
        deleteBusinessTag.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        deleteBusinessTag.setDeleteFlag(1);

        businessTagMapper.update(deleteBusinessTag,new LambdaQueryWrapper<BusinessTagPO>().eq(BusinessTagPO::getTagId,tagId));
    }


    @Override
    public BusinessTagPO selectLastTag(Integer employeeInfoId) {
        return businessTagMapper.selectOne(new LambdaQueryWrapper<BusinessTagPO>().eq(BusinessTagPO::getEmployeeInfoId,employeeInfoId)
            .eq(BusinessTagPO::getDeleteFlag,0).orderByDesc(BusinessTagPO::getTagId).last("limit 1")
        );
    }

    @Override
    public List<BusinessTagPO> selectHistory(Integer employeeInfoId) {
        return businessTagMapper.selectList(new LambdaQueryWrapper<BusinessTagPO>().eq(BusinessTagPO::getEmployeeInfoId,employeeInfoId)
                .eq(BusinessTagPO::getDeleteFlag,0).orderByDesc(BusinessTagPO::getTagId));
    }
}
