package com.wantwant.sfa.backend.domain.emp.repository.persistence;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.businessBd.DO.CompileDepartmentData;
import com.wantwant.sfa.backend.domain.businessBd.DO.ExternalQuota;
import com.wantwant.sfa.backend.domain.emp.mapper.BusinessBDBigTableMapper;
import com.wantwant.sfa.backend.domain.emp.repository.facade.IEmpBigTableRepository;
import com.wantwant.sfa.backend.domain.emp.repository.model.BusinessBDCompileDetailModel;
import com.wantwant.sfa.backend.salary.request.BusinessBDSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDDetailVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/下午9:46
 */
@Service
@DS("production_6")
public class EmpBigTableRepository implements IEmpBigTableRepository {

    @Resource
    private BusinessBDBigTableMapper businessBDBigTableMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BusinessBDDetailVO getAddedPerformance(String theYearMonth, Long memberKey) {
        return businessBDBigTableMapper.getAddedPerformance(theYearMonth,memberKey);
    }

    @Override
    public List<BusinessBDDetailVO> getBusinessBDDetail(IPage<BusinessBDDetailVO> page, BusinessBDSearchRequest request) {
        return businessBDBigTableMapper.getBusinessBDDetail(page,request);
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<BusinessBDCompileDetailModel> getExceedEstablished(String theYearMonth,List<String>orgCodes) {
        return businessBDBigTableMapper.getExceedEstablished(theYearMonth,orgCodes);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<ExternalQuota> selectExternalQuota(String theYearMonth, List<String> businessGroupCodes) {
        return businessBDBigTableMapper.selectExternalQuota(theYearMonth,businessGroupCodes);
    }

    @Override
    public List<CompileDepartmentData> selectCompileDepartmentDataList(List<String> departmentIds, String yearMonth) {
        return businessBDBigTableMapper.selectCompileDepartmentDataList(departmentIds,yearMonth);
    }
}
