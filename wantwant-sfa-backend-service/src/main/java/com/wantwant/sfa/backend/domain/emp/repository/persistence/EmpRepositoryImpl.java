package com.wantwant.sfa.backend.domain.emp.repository.persistence;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.domain.emp.DO.*;
import com.wantwant.sfa.backend.domain.emp.repository.facade.EmpRepositoryInterface;
import com.wantwant.sfa.backend.domain.emp.repository.model.EmpModel;
import com.wantwant.sfa.backend.employeeInfo.model.EmployeeInfoModel;
import com.wantwant.sfa.backend.entity.CustomerInfo;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.CustomerInfoMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryStructureMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/18/上午8:43
 */
@Repository
public class EmpRepositoryImpl implements EmpRepositoryInterface {
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Resource
    private ApplyMemberMapper applyMemberMapper;
    @Resource
    private EmployeeSalaryStructureMapper employeeSalaryStructureMapper;
    @Resource
    private CustomerInfoMapper customerInfoMapper;


    @Override
    public List<SfaPositionRelationEntity> selectPositionRelationByMemberKey(List<Long> memberKeys) {

        List<SfaEmployeeInfoModel> employeeInfoModels = sfaEmployeeInfoMapper.selectList(new LambdaQueryWrapper<SfaEmployeeInfoModel>().in(SfaEmployeeInfoModel::getMemberKey, memberKeys));

        if(CollectionUtils.isEmpty(employeeInfoModels)){
            return ListUtils.EMPTY_LIST;
        }

        List<Integer> employeeInfoIds = employeeInfoModels.stream().map(SfaEmployeeInfoModel::getId).collect(Collectors.toList());

        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().in(SfaPositionRelationEntity::getEmployeeInfoId, employeeInfoIds));

        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            return  ListUtils.EMPTY_LIST;
        }


        return positionRelationEntityList;
    }

    @Override
    public List<SfaPositionRelationEntity> selectPositionRelation(Long memberKey,boolean filterQuit) {

        SfaEmployeeInfoModel employeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().in(SfaEmployeeInfoModel::getMemberKey, memberKey).last("limit 1"));

        if(Objects.isNull(employeeInfoModel)){
            return ListUtils.EMPTY_LIST;
        }
        List<SfaPositionRelationEntity> positionRelationEntityList = null;
        if(filterQuit){
            positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, employeeInfoModel.getId()).eq(SfaPositionRelationEntity::getDeleteFlag,0)
                    .eq(SfaPositionRelationEntity::getStatus,1));
        }else{
            positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, employeeInfoModel.getId()).eq(SfaPositionRelationEntity::getDeleteFlag,0)
                    .orderByDesc(SfaPositionRelationEntity::getId));
        }


        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            return  ListUtils.EMPTY_LIST;
        }

        return positionRelationEntityList;
    }

    @Override
    public EmpBaseDO selectEmpNameByMemberKey(Long acceptedMemberKey) {
        return sfaEmployeeInfoMapper.selectEmpNameByMemberKey(acceptedMemberKey);
    }

    @Override
    public EmpDO selectManagerByOrgCode(String acceptedOrganizationId) {
        return sfaEmployeeInfoMapper.selectEmpInfoByOrgCode(acceptedOrganizationId);
    }

    @Override
    public EmpDO selectManager(String organizationId) {
        return sfaEmployeeInfoMapper.selectManager(organizationId);
    }


    @Override
    public EmpDO selectEmpByMemberKey(Long memberKey, String productGroupId) {
        return sfaEmployeeInfoMapper.selectEmpByMemberKey(memberKey,productGroupId);
    }

    @Override
    public List<RoleEmployeeRelationEntity> selectRoleIdsByEmpId(String empId) {

        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().eq(RoleEmployeeRelationEntity::getEmployeeId, empId).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0));
        return roleEmployeeRelationEntities;
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoById(Integer employeeInfoId) {
        return sfaEmployeeInfoMapper.selectById(employeeInfoId);
    }

    @Override
    public ApplyMemberPo selectApplyMemberById(Integer applicationId) {
        return applyMemberMapper.selectById(applicationId);
    }

    @Override
    public List<ServerObjDO> selectServerObj(Integer employeeInfoId) {
        return sfaEmployeeInfoMapper.selectServerObj(employeeInfoId);
    }

    @Override
    public PerformanceDO getPerformanceRate(Integer employeeInfoId, String lastMonth, String lastQuarter) {
        return sfaEmployeeInfoMapper.getPerformanceRate(employeeInfoId,lastMonth,lastQuarter);
    }

    @Override
    public SalaryDO selectSalaryInfo(Integer employeeInfoId) {
        return employeeSalaryStructureMapper.selectSalaryInfo(employeeInfoId);
    }

    @Override
    public List<SfaPositionRelationEntity> selectLoginUserOrg(String person, int businessGroup, Integer positionTypeId) {

        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, person)
                .eq(SfaPositionRelationEntity::getBusinessGroup, businessGroup).eq(SfaPositionRelationEntity::getPositionTypeId, positionTypeId)
                .eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0)
        );


        return positionRelationEntityList;
    }

    @Override
    public List<EmpModel> selectEmployeeInfoByKey(String key, List<String>orgCodes, Integer positionTypeId,int businessGroup) {
        return sfaEmployeeInfoMapper.selectEmployeeInfoByKey(key,orgCodes,positionTypeId,businessGroup);
    }

    @Override
    public List<CustomerInfo> selectCustomerByKey(String name,List<String>orgNames,int positionTypeId,int businessGroup) {
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerByKey(name,orgNames,positionTypeId,businessGroup);
        return customerInfos;
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByApplyId(Integer applyId) {
        return sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getApplicationId,applyId).last("limit 1"));
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByMobile(String userMobile) {
        return sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getMobile,userMobile).last("limit 1"));
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByMemberKey(Long memberKey) {
        return sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getMemberKey,memberKey).last("limit 1"));
    }

    @Override
    public List<CeoDO> selectCeoByOrgCode(String organizationId) {
        return sfaPositionRelationMapper.selectCeoByOrgCode(organizationId);
    }

    @Override
    public int getBusinessBDCount(int type, Long memberKey, int businessGroup) {
        return sfaEmployeeInfoMapper.getBusinessBDCount(type,memberKey,businessGroup);
    }

    @Override
    public int selectJobTaskBDCount(int type, String companyOrganizationId, Integer selectJobTaskBDCount, LocalDate startDate, LocalDate endDate) {
        return sfaEmployeeInfoMapper.selectJobTaskBDCount(type,companyOrganizationId,selectJobTaskBDCount,startDate,endDate);
    }

    @Override
    public List<BusinessBDDO> selectBusinessBDByMemberKey(Long memberKey, String businessGroupCode) {
        return sfaEmployeeInfoMapper.selectBusinessBDByMemberKey(memberKey,businessGroupCode);
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByEmpId(String empId) {
        return sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId,empId).last("limit 1"));
    }

    @Override
    public List<Integer> selectEmpRoleIds(String person, int businessGroup) {

        return roleEmployeeRelationMapper.selectEmpRoleIds(person,businessGroup);
    }

    @Override
    public String getEmpIdByMemberKey(Long memberKey) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getMemberKey, memberKey).last("limit 1"));
        return Optional.ofNullable(sfaEmployeeInfoModel).orElse(new SfaEmployeeInfoModel()).getEmployeeId();
    }

}
