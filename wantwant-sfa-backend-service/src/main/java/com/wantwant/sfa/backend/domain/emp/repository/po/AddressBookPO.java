package com.wantwant.sfa.backend.domain.emp.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 地址簿
 */
@Data
@TableName("sfa_address_book")
@ApiModel(value = "AddressBook对象", description = "地址簿")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddressBookPO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("会员key")
    @TableField("member_key")
    private Long memberKey;

    @ApiModelProperty("类型(1.联系地址 2.仓库地址)")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("门头照片")
    private String doorPhoto;

    @ApiModelProperty("详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("面积")
    @TableField("area")
    private String area;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private String longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private String latitude;

    @ApiModelProperty("是否删除(1.是)")
    @TableLogic(value = "0", delval = "1")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 