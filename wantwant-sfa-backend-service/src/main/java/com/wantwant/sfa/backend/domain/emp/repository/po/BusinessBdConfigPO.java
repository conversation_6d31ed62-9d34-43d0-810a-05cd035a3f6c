package com.wantwant.sfa.backend.domain.emp.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data // 使用 Lombok 自动生成 Getter 和 Setter 方法
@TableName("sfa_business_bd_config") // 指定数据库表名
public class BusinessBdConfigPO {

    @TableId(type = IdType.AUTO) // 标记主键并指定自增策略
    private Long id;

    @TableField("department_id")
    private String departmentId;

    @TableField("check_type")
    private Integer checkType;

    @TableField("delete_flag")
    private Byte deleteFlag;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("create_user_name")
    private String createUserName;

    @TableField(value = "create_time", fill = FieldFill.INSERT) // 自动填充创建时间
    private LocalDateTime createTime;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("update_user_name")
    private String updateUserName;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE) // 自动填充更新时间
    private LocalDateTime updateTime;
}