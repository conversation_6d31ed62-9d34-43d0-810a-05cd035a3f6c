package com.wantwant.sfa.backend.domain.emp.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 业务bd规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-21
 */
@TableName("sfa_business_bd_rule")
@ApiModel(value = "SfaBusinessBdRule对象", description = "业务bd规则表")
@Data
public class BusinessBdRulePO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "rule_id", type = IdType.AUTO)
    private Long ruleId;

    @ApiModelProperty("分公司code")
    private String companyCode;

    @ApiModelProperty("全职业务bd要求业绩")
    private BigDecimal fullTimePerformanceRequire;

    @ApiModelProperty("承揽BD要求业绩")
    private BigDecimal contractPerformanceRequire;

    @ApiModelProperty("所属月份")
    private String theYearMonth;

    @ApiModelProperty("开始月份")
    private LocalDate startYearMonth;



    @ApiModelProperty("结束日期")
    private LocalDate endYearMonth;



}
