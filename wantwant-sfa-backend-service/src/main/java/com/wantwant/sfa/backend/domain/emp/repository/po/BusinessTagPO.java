package com.wantwant.sfa.backend.domain.emp.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 业务标签
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@TableName("sfa_business_tag")
@ApiModel(value = "SfaBusinessTag对象", description = "业务标签")
@Data
public class BusinessTagPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("标签ID")
    @TableId(value = "tag_id", type = IdType.AUTO)
    private Long tagId;

    @ApiModelProperty("sfa_employee_info主键")
    private Integer employeeInfoId;

    @ApiModelProperty("稽核状态(1.正常 2.异常)")
    private Integer auditStatus;

    @ApiModelProperty("异常原因（0.无 1.业绩挂靠 2.客户造假 3.客户套现 4.操作合伙人账号 5.管理不力 6.其他）")
    private Integer abnormalReason;

    @ApiModelProperty("备注")
    private String remark;


}
