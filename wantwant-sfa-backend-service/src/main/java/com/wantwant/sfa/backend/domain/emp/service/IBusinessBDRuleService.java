package com.wantwant.sfa.backend.domain.emp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.DO.EstablishSearchDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.salary.request.BusinessBDRuleSearchRequest;
import com.wantwant.sfa.backend.salary.request.BusinessBDSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDDetailVO;
import com.wantwant.sfa.backend.salary.vo.BusinessBDRuleVo;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/20/下午4:33
 */
public interface IBusinessBDRuleService {

    /**
     * 导入规则
     *
     * @param businessBDRuleDOList
     * @param processUserDO
     */
    void importRule(List<BusinessBDRuleDO> businessBDRuleDOList, ProcessUserDO processUserDO);

    /**
     * 查询业务BD自动化
     *
     * @param request
     * @return
     */
    IPage<BusinessBDRuleVo> searchBusinessBDRule(BusinessBDRuleSearchRequest request);

    /**
     * 导出业务BD自动化
     *
     * @param request
     */
    void exportBusinessBDRule(BusinessBDRuleSearchRequest request);

    /**
     * 获取业务BD合伙人明细
     *
     * @param theYearMonth
     * @param organizationId
     * @return
     */
    List<BusinessBDDetailVO> getBusinessBDDetail(String theYearMonth, String organizationId);

    /**
     * 检查是否超出编制
     *
     * @param establishSearchDO
     */
    void checkOvershootEstablished(EstablishSearchDO establishSearchDO);

    /**
     * 查询业务BD明细
     *
     * @param businessBDSearchRequest
     * @return
     */
    IPage<BusinessBDDetailVO> selectBusinessBDDetail(BusinessBDSearchRequest businessBDSearchRequest);

    /**
     * 导出业务BD明细
     *
     * @param businessBDSearchRequest
     */
    void exportBusinessDetail(BusinessBDSearchRequest businessBDSearchRequest);

    /**
     * 根据营业所获取配置
     *
     * @param branchOrganizationId
     * @return
     */
    BusinessBdConfigPO getConfig(String branchOrganizationId);

    /**
     * 获取营业所剩余额度
     *
     * @param departmentId
     * @param applyId
     * @return
     */
    BigDecimal getBusinessBDPackageSalary(String departmentId, Integer applyId);
}
