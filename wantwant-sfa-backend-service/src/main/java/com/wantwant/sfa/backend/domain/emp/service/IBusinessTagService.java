package com.wantwant.sfa.backend.domain.emp.service;

import com.wantwant.sfa.backend.domain.emp.DO.BusinessTagDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午10:04
 */
public interface IBusinessTagService {
    /**
     * 添加标签
     *
     * @param businessTagDO
     * @param processUserDO
     */
    void addTag(BusinessTagDO businessTagDO, ProcessUserDO processUserDO);


    /**
     * 删除标签
     *
     * @param businessTagDO
     * @param processUserDO
     */
    void deleteTag(BusinessTagDO businessTagDO, ProcessUserDO processUserDO);


    /**
     * 根据工号获取稽核信息
     *
     * @param
     * @return
     */
    BusinessTagDO selectLastTag(Integer applyId,Long memberKey,List<Integer> roleIds);

    /**
     * 获取标签历史
     *
     * @param applyId
     * @return
     */
    List<BusinessTagDO> selectHistory(Integer applyId,Long memberKey,List<Integer>roleIds);

}
