package com.wantwant.sfa.backend.domain.emp.service;

import com.wantwant.sfa.backend.domain.emp.DO.EmpDO;
import com.wantwant.sfa.backend.domain.emp.DO.EmpDetailDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.employee.vo.AssociateObjVO;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notice.request.CeoNotifyRequest;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/18/上午8:41
 */
public interface IEmpService {

    /**
     * 更具memberKey查询工号
     *
     * @param memberKeys
     * @return
     */
    List<String> convertMemberKeyToEmpId(List<Long>memberKeys);

    /**
     * 获取员工信息
     *
     * @param acceptedMemberKey
     * @param acceptedOrganizationId
     * @param filterQuit 是否过滤离职
     * @return
     */
    EmpDO selectEmpInfo(Long acceptedMemberKey, String acceptedOrganizationId,boolean filterQuit);

    /**
     * 根据组织获取当前组织管理岗信息
     *
     * @param organizationId
     * @return
     */
    EmpDO selectManager(String organizationId);


    ProcessUserDO getUserById(String person);

    /**
     * 根据memberKey及产品组code获取信息-合伙人用
     *
     * @param memberKey
     * @param productGroupId
     * @return
     */
    EmpDO selectEmpByMemberKey(Long memberKey, String productGroupId);


    /**
     * 根据工号获取角色信息
     *
     * @param empId
     * @return
     */
    List<Integer> selectRoleIdsByEmpId(String empId);


    /**
     * 根据employeeInfoId获取员工明细
     *
     * @param employeeInfoId
     * @return
     */
    EmpDetailDO selectEmpDetailById(Integer employeeInfoId);

    /**
     * 获取操作人的组织
     *
     * @param person
     * @param businessGroup
     * @param positionTypeId
     * @return
     */
    List<String> selectLoginUserOrg(String person, int businessGroup, Integer positionTypeId);

    /**
     * 根据ID获取employeeInfo
     *
     * @param employeeInfoId
     * @return
     */
    SfaEmployeeInfoModel selectEmployeeInfoById(Integer employeeInfoId);

    /**
     * 获取关联对象信息
     *
     * @param type
     * @param name
     * @return
     */
    List<AssociateObjVO> getAssociate(int type, String name,String person);

    SfaEmployeeInfoModel selectEmployeeInfoByApplyId(Integer applyId);

    ApplyMemberPo selectApplyMemberById(Integer applyId);

    SfaEmployeeInfoModel selectEmployeeInfoByMobile(String userMobile);

    /**
     * 根据工号获取sfa_employee_info信息
     *
     * @param person
     * @return
     */
    SfaEmployeeInfoModel selectEmployeeInfoByEmpId(String person);


    String getEmpIdByMemberKey(Long memberKey);
}
