package com.wantwant.sfa.backend.domain.emp.service.factory;

import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdRulePO;
import com.wantwant.sfa.backend.util.BeanUtils;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/21/上午9:46
 */
public class BusinessBDRuleFactory {
    public static BusinessBdRulePO convertPO(BusinessBDRuleDO e, String orgCode, String employeeId, String employeeName) {
        BusinessBdRulePO businessBdRulePO = new BusinessBdRulePO();
        businessBdRulePO.init(employeeId,employeeName);
        BeanUtils.copyProperties(e,businessBdRulePO);
        String startYearMonth = e.getStartYearMonth();
        if(StringUtils.isNotBlank(startYearMonth)){
            businessBdRulePO.setStartYearMonth(LocalDate.parse(startYearMonth +"-01"));
        }
        businessBdRulePO.setCompanyCode(orgCode);

        return businessBdRulePO;
    }
}
