package com.wantwant.sfa.backend.domain.emp.service.factory;

import com.wantwant.sfa.backend.domain.emp.DO.BusinessTagDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessTagPO;
import com.wantwant.sfa.backend.util.BeanUtils;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午10:48
 */
public class BusinessTagFactory {


    public static BusinessTagPO convert2PO(BusinessTagDO businessTagDO, ProcessUserDO processUserDO){
        BusinessTagPO businessTagPO = new BusinessTagPO();
        businessTagPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        businessTagPO.setEmployeeInfoId(businessTagDO.getEmployeeInfoId());
        businessTagPO.setAbnormalReason(businessTagDO.getAbnormalReason());
        businessTagPO.setAuditStatus(businessTagDO.getAuditStatus());
        businessTagPO.setRemark(businessTagDO.getRemark());
        return businessTagPO;
    }

    public static BusinessTagDO convert2DO(BusinessTagPO businessTagPO) {
        BusinessTagDO businessTagDO = new BusinessTagDO();
        BeanUtils.copyProperties(businessTagPO,businessTagDO);
        LocalDateTime updateTime = businessTagPO.getUpdateTime();
        businessTagDO.setProcessDate(updateTime.toLocalDate());
        businessTagDO.setProcessUserId(businessTagPO.getUpdateUserId());
        businessTagDO.setProcessUserName(businessTagPO.getUpdateUserName());
        return businessTagDO;
    }
}
