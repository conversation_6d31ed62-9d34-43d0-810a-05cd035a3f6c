package com.wantwant.sfa.backend.domain.emp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdRecruitmentQuotaValue;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdSalaryControlDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.EmpSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.DO.OrgSalaryDO;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdOrgQuotaService;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdSalaryControlService;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.DO.EstablishSearchDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.facade.EmpRepositoryInterface;
import com.wantwant.sfa.backend.domain.emp.repository.facade.IEmpBigTableRepository;
import com.wantwant.sfa.backend.domain.emp.repository.persistence.BusinessBDRepository;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdRulePO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessBDRuleService;
import com.wantwant.sfa.backend.domain.emp.service.factory.BusinessBDRuleFactory;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.salary.model.OrganizationUsedSalaryModel;
import com.wantwant.sfa.backend.salary.request.BusinessBDRuleSearchRequest;
import com.wantwant.sfa.backend.salary.request.BusinessBDSearchRequest;
import com.wantwant.sfa.backend.salary.vo.BusinessBDDetailVO;
import com.wantwant.sfa.backend.salary.vo.BusinessBDRuleVo;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.ExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/20/下午4:34
 */
@Service
@Slf4j
public class BusinessBDRuleService implements IBusinessBDRuleService {
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private BusinessBDRepository businessBDRepository;
    @Resource
    private EmpRepositoryInterface empRepositoryInterface;
    @Resource
    private IEmpBigTableRepository empBigTableRepository;
    @Resource
    private BusinessBdOrgQuotaService businessBdOrgQuotaService;
    @Resource
    private BusinessBdSalaryControlService businessBdSalaryControlService;

    private static final String regex = "\\d{4}-\\d{2}";


    private static final String message = "当前剩余额度为：{0}    可配置全职BD为：{1} 个    可配置承揽BD为：{2} 个";
    @Autowired
    private EmployeeSalaryService employeeSalaryService;

    @Override
    @Transactional
    public void importRule(List<BusinessBDRuleDO> businessBDRuleDOList, ProcessUserDO processUserDO) {

        if (CollectionUtils.isEmpty(businessBDRuleDOList)) {
            throw new ApplicationException("导入文件内容异常");
        }

        // 检查文件中是否有重复
        Map<String, Long> checkRepeat = businessBDRuleDOList.stream().collect(Collectors.groupingBy(BusinessBDRuleDO::getCompanyName, Collectors.counting()));
        checkRepeat.forEach((k, v) -> {
            if (v > 1) {
                throw new ApplicationException("导入组织【" + k + "】存在重复数据");
            }
        });

        int businessGroup = RequestUtils.getBusinessGroup();

        Pattern pattern = Pattern.compile(regex);

        businessBDRuleDOList.forEach(e -> {
            String orgCode = organizationMapper.getOrganizationIdByName(e.getCompanyName(), 3, businessGroup);
            if (StringUtils.isBlank(orgCode)) {
                throw new ApplicationException("组织名:【" + e.getCompanyName() + "】不存在");
            }
            String organizationType = organizationMapper.getOrganizationType(orgCode);
            if (!"company".equals(organizationType)) {
                throw new ApplicationException("组织名:【" + e.getCompanyName() + "】类型错误");
            }


            Matcher matcherStartYearMonth = pattern.matcher(e.getStartYearMonth());
            if (!matcherStartYearMonth.matches()) {
                throw new ApplicationException("组织名:【" + e.getCompanyName() + "】启动月份错误");
            }

            Matcher matcherYearMonth = pattern.matcher(e.getTheYearMonth());
            if (!matcherYearMonth.matches()) {
                throw new ApplicationException("组织名:【" + e.getCompanyName() + "】月份错误");
            }

            BigDecimal fullTimePerformanceRequire = e.getFullTimePerformanceRequire();
            BigDecimal contractPerformanceRequire = e.getContractPerformanceRequire();
            if (fullTimePerformanceRequire.compareTo(contractPerformanceRequire) < 0) {
                throw new ApplicationException("组织名:【" + e.getCompanyName() + "】全职业务BD配置必须大于等于承揽业务BD配置");
            }

            BusinessBdRulePO businessBdRulePO = BusinessBDRuleFactory.convertPO(e, orgCode, processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
            // 插入前先删除历史的记录
            businessBDRepository.deleteLastRule(e.getTheYearMonth(), orgCode);

            businessBDRepository.insert(businessBdRulePO);
        });
    }

    @Override
    public IPage<BusinessBDRuleVo> searchBusinessBDRule(BusinessBDRuleSearchRequest request) {
        IPage page = new Page(request.getPage(), request.getRows());
        List<BusinessBDRuleDO> businessBDRuleExportDOS = Optional.ofNullable(businessBDRepository.searchBusinessBDRule(page, request)).orElse(new ArrayList<>());
        List<BusinessBDRuleVo> result = convert2VO(businessBDRuleExportDOS);
        page.setRecords(result);
        return page;
    }

    @Override
    public void exportBusinessBDRule(BusinessBDRuleSearchRequest request) {
        IPage page = new Page(request.getPage(), request.getRows());
        List<BusinessBDRuleDO> businessBDRuleExportDOS = Optional.ofNullable(businessBDRepository.searchBusinessBDRule(page, request)).orElse(new ArrayList<>());
        List<BusinessBDRuleVo> result = convert2VO(businessBDRuleExportDOS);

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        HttpServletRequest req = servletRequestAttributes.getRequest();


        ExportUtil.writeEasyExcelResponse(response, req, "业务BD编制自动化", BusinessBDRuleVo.class, result);

    }

    @Override
    public List<BusinessBDDetailVO> getBusinessBDDetail(String theYearMonth, String organizationId) {
        BusinessBDSearchRequest businessBDSearchRequest = new BusinessBDSearchRequest();
        businessBDSearchRequest.setYearMonth(theYearMonth);
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        businessBDSearchRequest.setOrgType(organizationId);
        List<String> organizationIds = new ArrayList<>();
        organizationIds.add(organizationId);
        businessBDSearchRequest.setOrganizationIds(organizationIds);


        List<BusinessBDDetailVO> list = Optional.ofNullable(empBigTableRepository.getBusinessBDDetail(null, businessBDSearchRequest)).orElse(new ArrayList<>());


        return list;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void checkOvershootEstablished(EstablishSearchDO establishSearchDO) {
        log.info("checkOvershootEstablished,DO:{}", establishSearchDO);
        Integer checkType = establishSearchDO.getCheckType();
        log.info("check over shoot established checkType:{}", checkType);
        switch (checkType) {
            // 检查合伙人额度
            case 1:
                checkEmpEstablish(establishSearchDO);
                break;
            // 检查营业所额度
            case 2:
                checkDepartmentEstablish(establishSearchDO);
                break;
            // 检查费用包管控额度
            case 3:
                checkSalaryPackage(establishSearchDO);
                break;
        }
    }

    private void checkSalaryPackage(EstablishSearchDO establishSearchDO) {
        boolean changeServer = establishSearchDO.isChangeServer();
        if (changeServer) {
            return;
        }

        // 原业务BD薪资
        BigDecimal oldSalary = Optional.ofNullable(establishSearchDO.getOldSalary()).orElse(BigDecimal.ZERO);
        // 现业务BD薪资
        BigDecimal newSalary = Optional.ofNullable(establishSearchDO.getNewSalary()).orElse(BigDecimal.ZERO);


        if(oldSalary.compareTo(newSalary) > 0){
            return;
        }


        BigDecimal surplus = calculateSurplus(establishSearchDO, newSalary, oldSalary);
        if(surplus.compareTo(BigDecimal.ZERO) < 0){
            throw new ApplicationException("费用包额度不足");
        }
    }

    private BigDecimal calculateSurplus(EstablishSearchDO establishSearchDO, BigDecimal newSalary, BigDecimal oldSalary) {
        // 获取当前费用包总额度
        BusinessBdSalaryControlDO salaryControl = businessBdSalaryControlService.getByOrganizationIdAndYearMonth(
                establishSearchDO.getTheYearMonth(), 
                establishSearchDO.getDepartmentId());
        
        // 参数验证
        String departmentId = establishSearchDO.getDepartmentId();
        String yearMonth = establishSearchDO.getTheYearMonth();
        if (departmentId == null || yearMonth == null) {
            log.error("Invalid input parameters: departmentId={}, yearMonth={}", departmentId, yearMonth);
            throw new IllegalArgumentException("部门ID和年月不能为空");
        }
        
        List<String> departmentIdList = Collections.singletonList(departmentId);
        
        // 计算总额度 = 平均薪资包 + 上月结余
        BigDecimal avgSalaryPackage = Optional.ofNullable(salaryControl.getAvgSalaryPackage()).orElse(BigDecimal.ZERO);
        BigDecimal lastMonthBalance = Optional.ofNullable(salaryControl.getLastMonthBalance()).orElse(BigDecimal.ZERO);
        BigDecimal totalBudget = avgSalaryPackage.add(lastMonthBalance);
        
        // 获取各类人员薪资数据
        SalaryData salaryData = getSalaryData(departmentIdList, yearMonth,establishSearchDO.getApplyId());
        
        // 记录详细薪资信息
        log.info("Department [{}] salary details - Current: {}, Transferring: {}, Onboarding: {}, New: {}, Old: {}", 
                departmentId, 
                salaryData.currentEmployeesSalary,
                salaryData.transferringSalary, 
                salaryData.onboardingSalary,
                newSalary,
                oldSalary);
                
        // 剩余额度计算：总额度 - 在职业务BD薪资 - 异动中业务BD薪资 - 入职中业务BD薪资 - 新薪资 + 原薪资
        return totalBudget
                .subtract(salaryData.currentEmployeesSalary)
                .subtract(salaryData.transferringSalary)
                .subtract(salaryData.onboardingSalary)
                .subtract(newSalary)
                .add(oldSalary);
    }

    /**
     * 获取部门的各类人员薪资数据
     *
     * @param departmentIds 部门ID列表
     * @param yearMonth     年月
     * @param excludeApplyId
     * @return 薪资数据对象
     */
    private SalaryData getSalaryData(List<String> departmentIds, String yearMonth, Integer excludeApplyId) {
        // 查询处于异动中的人员数据
        List<EmpSalaryDO> transactionEmpList = Optional.ofNullable(
                employeeSalaryService.selectTransactionBD(departmentIds, yearMonth))
                .orElse(Collections.emptyList());
        
        // 获取异动中人员薪资
        BigDecimal transferringSalary = Optional.ofNullable(
                employeeSalaryService.selectTransferringSalary(transactionEmpList))
                .orElse(Collections.emptyList())
                .stream()
                .findFirst()
                .map(OrgSalaryDO::getTotalSalary)
                .orElse(BigDecimal.ZERO);
        
        // 入职中人员薪资
        BigDecimal onboardingSalary = Optional.ofNullable(
                employeeSalaryService.selectOnBoardProcessingSalary(departmentIds, yearMonth,excludeApplyId))
                .orElse(Collections.emptyList())
                .stream()
                .findFirst()
                .map(OrgSalaryDO::getTotalSalary)
                .orElse(BigDecimal.ZERO);
        
        // 在职人员薪资
        List<Integer> excludeEmployeeInfoIds = transactionEmpList.stream()
                .map(EmpSalaryDO::getEmployeeInfoId)
                .collect(Collectors.toList());
                
        BigDecimal currentEmployeesSalary = Optional.ofNullable(
                employeeSalaryService.selectOnBoardBusinessBDSalary(departmentIds, excludeEmployeeInfoIds))
                .orElse(Collections.emptyList())
                .stream()
                .findFirst()
                .map(OrganizationUsedSalaryModel::getUsedQuota)
                .orElse(BigDecimal.ZERO);
        
        return new SalaryData(currentEmployeesSalary, transferringSalary, onboardingSalary);
    }
    
    /**
     * 薪资数据值对象
     */
    private static class SalaryData {
        private final BigDecimal currentEmployeesSalary;
        private final BigDecimal transferringSalary;
        private final BigDecimal onboardingSalary;
        
        public SalaryData(BigDecimal currentEmployeesSalary, BigDecimal transferringSalary, BigDecimal onboardingSalary) {
            this.currentEmployeesSalary = currentEmployeesSalary;
            this.transferringSalary = transferringSalary;
            this.onboardingSalary = onboardingSalary;
        }
    }

    @Override
    public BigDecimal getBusinessBDPackageSalary(String departmentId, Integer applyId) {
        EstablishSearchDO establishSearchDO = EstablishSearchDO.builder().applyId(applyId)
                .departmentId(departmentId)
                .theYearMonth(LocalDate.now().toString().substring(0, 7))
                .build();
        return calculateSurplus(establishSearchDO,BigDecimal.ZERO,BigDecimal.ZERO);
    }
    

    private void checkDepartmentEstablish(EstablishSearchDO establishSearchDO) {

        boolean changeServer = establishSearchDO.isChangeServer();
        if (changeServer) {
            return;
        }

        PositionEnum positionEnum = establishSearchDO.getPositionEnum();
        if(Objects.equals(positionEnum.getId(), PositionEnum.BUSINESS_BD_PART_TIME.getId())){
            return;
        }

        // 获取当前营业所总的额度
        BusinessBdRecruitmentQuotaValue businessBdRecruitmentQuotaValue = Optional.ofNullable(businessBdOrgQuotaService.calculateRecruitmentQuota(establishSearchDO.getDepartmentId(), establishSearchDO.getOldPositionEnum()))
                .orElse(new BusinessBdRecruitmentQuotaValue());


        BigDecimal contractBdCount = businessBdRecruitmentQuotaValue.getContractBdCount();
        if (contractBdCount.compareTo(BigDecimal.ONE) < 0) {
            throw new ApplicationException("无可用额度");
        }


        if (positionEnum.getId().equals(PositionEnum.BUSINESS_BD.getId())) {
            BigDecimal fullTimeBdCount = businessBdRecruitmentQuotaValue.getFullTimeBdCount();
            if (fullTimeBdCount.compareTo(BigDecimal.ONE) < 0) {
                throw new ApplicationException("额度不足");
            }
        }

        if (positionEnum.getId().equals(PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
            if (contractBdCount.compareTo(BigDecimal.ONE) < 0) {
                throw new ApplicationException("额度不足");
            }
        }
    }

    private void checkEmpEstablish(EstablishSearchDO establishSearchDO) {
        PositionEnum positionEnum = establishSearchDO.getPositionEnum();

        if (positionEnum.getId() != PositionEnum.BUSINESS_BD.getId() && positionEnum.getId() != PositionEnum.BUSINESS_BD_CONTRACT.getId()) {
            return;
        }

        Integer serverEmployeeInfoId = establishSearchDO.getServerEmployeeInfoId();
        SfaEmployeeInfoModel serverEmp = empRepositoryInterface.selectEmployeeInfoById(serverEmployeeInfoId);
        if (Objects.isNull(serverEmp)) {
            throw new ApplicationException("服务对象不存在");
        }

        // 获取累计业绩
        String theYearMonth = establishSearchDO.getTheYearMonth();

        Integer applyId = establishSearchDO.getApplyId();
        ApplyMemberPo applyMemberPo = empRepositoryInterface.selectApplyMemberById(applyId);

        BusinessBdRulePO businessBdRulePO = Optional.ofNullable(businessBDRepository.selectLastRule(theYearMonth, applyMemberPo.getCompanyOrganizationId())).orElse(new BusinessBdRulePO());
        BigDecimal ContractPerformanceRequire = Optional.ofNullable(businessBdRulePO.getContractPerformanceRequire()).orElse(BigDecimal.ZERO);
        if (ContractPerformanceRequire.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApplicationException(MessageFormat.format(message, "0", "0", "0"));
        }

        BigDecimal fullTimePerformanceRequire = Optional.ofNullable(businessBdRulePO.getFullTimePerformanceRequire()).orElse(BigDecimal.ZERO);


        BigDecimal ratio = BigDecimal.ONE;

        if (ContractPerformanceRequire.compareTo(BigDecimal.ZERO) > 0 && fullTimePerformanceRequire.compareTo(BigDecimal.ZERO) > 0) {
            ratio = fullTimePerformanceRequire.divide(ContractPerformanceRequire, 1, RoundingMode.DOWN);
        }


        BusinessBDDetailVO businessBDDetailVO = empBigTableRepository.getAddedPerformance(theYearMonth, serverEmp.getMemberKey());
        if (Objects.isNull(businessBDDetailVO)) {
            throw new ApplicationException(MessageFormat.format(message, "0", "0", "0"));
        }

        BigDecimal totalQuota = Optional.ofNullable(businessBDDetailVO.getRemainderConfigurable()).orElse(BigDecimal.ZERO);
        log.info("业务BD剩余编制数:{}", totalQuota);

        // 检查是否入职
        SfaEmployeeInfoModel sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByApplyId(applyId);
        boolean changeServer = establishSearchDO.isChangeServer();
        // 如果入职并且不是更换服务对象
        if (Objects.nonNull(sfaEmployeeInfoModel) && !changeServer) {
            int positionId = PositionEnum.getPositionId(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
            if (positionId == PositionEnum.BUSINESS_BD.getId()) {
                totalQuota = totalQuota.add(ratio.multiply(BigDecimal.ONE));
            } else if (positionId == PositionEnum.BUSINESS_BD_CONTRACT.getId()) {
                totalQuota = totalQuota.add(BigDecimal.ONE);
            }
        }


        if (totalQuota.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApplicationException(MessageFormat.format(message, "0", "0", "0"));
        }


        // 获取承揽业务BD数量
//        int contractCount = empRepositoryInterface.getBusinessBDCount(1,serverEmp.getMemberKey(),businessGroup);
//        log.info("当前承揽业务BD数:{}",contractCount);
        // 查询流程中的承揽业务BD数量
        LocalDate startDate = LocalDate.now().minusMonths(1L).withDayOfMonth(21);
        LocalDate endDate = LocalDate.now().withDayOfMonth(20);
        int contractJobTaskCount = empRepositoryInterface.selectJobTaskBDCount(1, applyMemberPo.getCompanyOrganizationId(), serverEmp.getId(), startDate, endDate);
        log.info("当前处于流程中的承揽业务BD数:{}", contractJobTaskCount);

        if (contractJobTaskCount > 0) {
            totalQuota = totalQuota.subtract(new BigDecimal(contractJobTaskCount));
        }
        // 获取全职业务BD数量
        int fullTimeJobTaskCount = empRepositoryInterface.selectJobTaskBDCount(2, applyMemberPo.getCompanyOrganizationId(), serverEmp.getId(), startDate, endDate);
        log.info("当前处于流程中的全职业务BD数:{}", fullTimeJobTaskCount);

        if (fullTimeJobTaskCount > 0) {
            totalQuota = totalQuota.subtract(ratio.multiply(new BigDecimal(fullTimeJobTaskCount)));
        }

        // 减去当前额度
        if (positionEnum.getId() == PositionEnum.BUSINESS_BD_CONTRACT.getId()) {
            totalQuota = totalQuota.subtract(BigDecimal.ONE);
        } else if (positionEnum.getId() == PositionEnum.BUSINESS_BD.getId()) {
            totalQuota = totalQuota.subtract(ratio.multiply(BigDecimal.ONE));
        }
        log.info("【剩余额度】:{}", totalQuota);
        if (totalQuota.compareTo(BigDecimal.ZERO) < 0) {
            throw new ApplicationException(MessageFormat.format(message,
                    "0",
                    "0",
                    businessBDDetailVO.getRemainderConfigurable().intValue())
            );
        }
    }

    @Override
    public IPage<BusinessBDDetailVO> selectBusinessBDDetail(BusinessBDSearchRequest businessBDSearchRequest) {

        IPage<BusinessBDDetailVO> page = new Page(businessBDSearchRequest.getPage(), businessBDSearchRequest.getRows());
        businessBDSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> organizationIds = businessBDSearchRequest.getOrganizationIds();
        if (!CollectionUtils.isEmpty(organizationIds)) {
            String orgCode = organizationIds.stream().findFirst().get();
            String organizationType = organizationMapper.getOrganizationType(orgCode);
            businessBDSearchRequest.setOrgType(organizationType);
        }

        List<BusinessBDDetailVO> list = Optional.ofNullable(empBigTableRepository.getBusinessBDDetail(page, businessBDSearchRequest)).orElse(new ArrayList<>());
        page.setRecords(list);

        return page;
    }

    @Override
    public void exportBusinessDetail(BusinessBDSearchRequest businessBDSearchRequest) {
        List<String> organizationIds = businessBDSearchRequest.getOrganizationIds();

        businessBDSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        if (!CollectionUtils.isEmpty(organizationIds)) {
            String orgCode = organizationIds.stream().findFirst().get();
            String organizationType = organizationMapper.getOrganizationType(orgCode);
            businessBDSearchRequest.setOrgType(organizationType);
        }

        List<BusinessBDDetailVO> list = Optional.ofNullable(empBigTableRepository.getBusinessBDDetail(null, businessBDSearchRequest)).orElse(new ArrayList<>());


        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "业务BD明细导出" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), BusinessBDDetailVO.class, list);
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public BusinessBdConfigPO getConfig(String departmentId) {
        return businessBDRepository.getConfig(departmentId);
    }



    private List<BusinessBDRuleVo> convert2VO(List<BusinessBDRuleDO> businessBDRuleExportDOS) {
        if (CollectionUtils.isEmpty(businessBDRuleExportDOS)) {
            return ListUtils.EMPTY_LIST;
        }

        List<BusinessBDRuleVo> result = new ArrayList<>();
        businessBDRuleExportDOS.forEach(e -> {
            BusinessBDRuleVo businessBDRuleVo = new BusinessBDRuleVo();
            BeanUtils.copyProperties(e, businessBDRuleVo);
            businessBDRuleVo.setOrganizationName(e.getAreaName() + "/" + e.getVareaName() + "/" + e.getProvinceName() + "/" + e.getCompanyName());
            result.add(businessBDRuleVo);
        });

        return result;
    }
}
