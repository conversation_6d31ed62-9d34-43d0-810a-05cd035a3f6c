package com.wantwant.sfa.backend.domain.emp.service.impl;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessTagDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.facade.EmpRepositoryInterface;
import com.wantwant.sfa.backend.domain.emp.repository.facade.IBusinessTagRepository;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessTagPO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessTagService;
import com.wantwant.sfa.backend.domain.emp.service.factory.BusinessTagFactory;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/上午10:07
 */
@Service
@Slf4j
public class BusinessTagService implements IBusinessTagService {

    @Resource
    private IBusinessTagRepository businessTagRepository;
    @Resource
    private EmpRepositoryInterface empRepositoryInterface;


    @Override
    public void addTag(BusinessTagDO businessTagDO, ProcessUserDO processUserDO) {
        log.info("【add tag】businessTagDO:{}",businessTagDO);


        Integer applyId = businessTagDO.getApplyId();
        SfaEmployeeInfoModel sfaEmployeeInfoModel = null;
        if(Objects.nonNull(applyId)){
            sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByApplyId(applyId);
        }else{
            sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByMemberKey(businessTagDO.getMemberKey());
        }

        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("员工信息表获取失败");
        }

        BusinessTagPO businessTagPO = BusinessTagFactory.convert2PO(businessTagDO, processUserDO);
        businessTagPO.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
        businessTagRepository.addTag(businessTagPO);
    }

    @Override
    public void deleteTag(BusinessTagDO businessTagDO, ProcessUserDO processUserDO) {
        businessTagRepository.deleteTag(businessTagDO.getTagId(),processUserDO);
    }

    @Override
    public BusinessTagDO selectLastTag(Integer applyId,Long memberKey,List<Integer> roleIds) {
        BusinessTagDO businessTagDO = new BusinessTagDO();


        SfaEmployeeInfoModel sfaEmployeeInfoModel = null;
        if(Objects.nonNull(applyId)){
            // 查询报名表
            ApplyMemberPo applyMemberPo = empRepositoryInterface.selectApplyMemberById(applyId);
            if(Objects.isNull(applyMemberPo)){
                throw new ApplicationException("报名表信息获取失败");
            }

            sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByApplyId(applyId);
            // 如果员工表没数据再用手机号查是否有历史的数据
            if(Objects.isNull(sfaEmployeeInfoModel)){
                sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByMobile(applyMemberPo.getUserMobile());
            }
        }else{
            sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByMemberKey(memberKey);
        }

        // 还未开户直接返回
        if(Objects.isNull(sfaEmployeeInfoModel)){
            return businessTagDO;
        }

        BusinessTagPO businessTagPO = businessTagRepository.selectLastTag(sfaEmployeeInfoModel.getId());
        if(Objects.isNull(businessTagPO)){
            return BusinessTagDO.initEmptyTag(sfaEmployeeInfoModel.getId(),sfaEmployeeInfoModel.getEmployeeStatus(),roleIds);
        }

        BeanUtils.copyProperties(businessTagPO,businessTagDO);
        businessTagDO.setProcessDate(businessTagPO.getUpdateTime().toLocalDate());
        businessTagDO.setProcessUserId(businessTagPO.getUpdateUserId());
        businessTagDO.setProcessUserName(businessTagPO.getUpdateUserName());
        businessTagDO.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
        businessTagDO.checkCanProcess(sfaEmployeeInfoModel.getEmployeeStatus(),roleIds);



        return businessTagDO;
    }

    @Override
    public List<BusinessTagDO> selectHistory(Integer applyId,Long memberKey,List<Integer> roleIds) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = null;

        if(Objects.nonNull(applyId)){
            // 查询报名表
            ApplyMemberPo applyMemberPo = empRepositoryInterface.selectApplyMemberById(applyId);
            if(Objects.isNull(applyMemberPo)){
                throw new ApplicationException("报名表信息获取失败");
            }

            sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByApplyId(applyId);
            // 如果员工表没数据再用手机号查是否有历史的数据
            if(Objects.isNull(sfaEmployeeInfoModel)){
                sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByMobile(applyMemberPo.getUserMobile());
            }
        }else{
            sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoByMemberKey(memberKey);
        }

        // 还未开户直接返回
        if(Objects.isNull(sfaEmployeeInfoModel)){
            return ListUtils.EMPTY_LIST;
        }

        List<BusinessTagPO> list = Optional.ofNullable(businessTagRepository.selectHistory(sfaEmployeeInfoModel.getId())).orElse(new ArrayList<>());

        SfaEmployeeInfoModel finalSfaEmployeeInfoModel = sfaEmployeeInfoModel;
        List<BusinessTagDO> collect = list.stream().map(BusinessTagFactory::convert2DO).collect(Collectors.toList());
        collect.forEach(e -> {
            e.checkCanProcess(finalSfaEmployeeInfoModel.getEmployeeStatus(),roleIds);
        });
        return collect;
    }
}
