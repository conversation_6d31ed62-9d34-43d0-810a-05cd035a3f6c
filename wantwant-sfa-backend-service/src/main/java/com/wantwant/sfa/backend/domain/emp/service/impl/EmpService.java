package com.wantwant.sfa.backend.domain.emp.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.*;
import com.wantwant.sfa.backend.domain.emp.enums.EmpErrorEnum;
import com.wantwant.sfa.backend.domain.emp.repository.facade.EmpRepositoryInterface;
import com.wantwant.sfa.backend.domain.emp.repository.model.EmpModel;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.domain.jobTransfer.constants.JobTransferConstants;
import com.wantwant.sfa.backend.employee.vo.AssociateObjVO;
import com.wantwant.sfa.backend.entity.CustomerInfo;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notice.request.CeoNotifyRequest;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/18/上午8:41
 */
@Service
@Slf4j
public class EmpService implements IEmpService {

    @Resource
    private EmpRepositoryInterface empRepositoryInterface;
    @Resource
    private ICheckCustomerService checkCustomerService;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;



    @Override
    public List<String> convertMemberKeyToEmpId(List<Long> memberKeys) {

        List<SfaPositionRelationEntity> positionRelationEntityList = empRepositoryInterface.selectPositionRelationByMemberKey(memberKeys);

        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            return ListUtils.EMPTY_LIST;
        }

        return positionRelationEntityList.stream().map(SfaPositionRelationEntity::getEmpId).distinct().collect(Collectors.toList());
    }

    @Override
    public EmpDO selectEmpInfo(Long acceptedMemberKey, String acceptedOrganizationId,boolean filterQuit) {
        EmpDO empDO = new EmpDO();
        if(Objects.nonNull(acceptedMemberKey)){
            List<SfaPositionRelationEntity> positionRelationEntityList = empRepositoryInterface.selectPositionRelation(acceptedMemberKey,filterQuit);
            if(CollectionUtils.isEmpty(positionRelationEntityList)){
                throw new ApplicationException("岗位信息获取失败");
            }

            Optional<SfaPositionRelationEntity> first = positionRelationEntityList.stream().filter(f -> f.getPartTime() == 0).findFirst();
            if(!first.isPresent()){
                throw new ApplicationException("岗位信息获取失败");
            }

            SfaPositionRelationEntity sfaPositionRelationEntity = first.get();
            LocalDateTime startValidDate = sfaPositionRelationEntity.getStartValidDate();
            LocalDateTime endValidDate = sfaPositionRelationEntity.getEndValidDate();
            if(endValidDate.isAfter(LocalDateTime.now())){
                endValidDate = LocalDateTime.now();
            }
            Duration duration = Duration.between(startValidDate,endValidDate);
            empDO.setOnBoardDate(startValidDate.toLocalDate());
            empDO.setWorkDate(duration.toDays());
            empDO.setOrganizationName(sfaPositionRelationEntity.getDepartmentName());
            empDO.setOrganizationId(sfaPositionRelationEntity.getOrganizationCode());

            EmpBaseDO empBaseDO = empRepositoryInterface.selectEmpNameByMemberKey(acceptedMemberKey);
            if(Objects.nonNull(empBaseDO)){
                empDO.setEmployeeName(empBaseDO.getEmployeeName());
                empDO.setAvatar(empBaseDO.getAvatar());
            }
        }else{
            empDO = empRepositoryInterface.selectManagerByOrgCode(acceptedOrganizationId);
        }

        return empDO;
    }

    @Override
    public EmpDO selectManager(String organizationId) {
        return empRepositoryInterface.selectManager(organizationId);
    }

    @Override
    public ProcessUserDO getUserById(String person) {
        if(StringUtils.isBlank(person) || "ROOT".equals(person)){
            ProcessUserDO processUserDO = new ProcessUserDO();
            processUserDO.setEmployeeId("ROOT");
            processUserDO.setEmployeeName("系统自动");
            return processUserDO;
        }

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person, RequestUtils.getLoginInfo());

        ProcessUserDO processUserDO = new ProcessUserDO();
        processUserDO.setEmployeeId(personInfo.getEmployeeId());
        processUserDO.setEmployeeName(personInfo.getEmployeeName());
        processUserDO.setPositionName(OrganizationTypeEnum.getPositionName(RequestUtils.getLoginInfo().getOrganizationType()));
        processUserDO.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<Integer> roleIds = empRepositoryInterface.selectEmpRoleIds(person,RequestUtils.getBusinessGroup());
        processUserDO.setRoleIds(roleIds);

        return processUserDO;
    }

    @Override
    public EmpDO selectEmpByMemberKey(Long memberKey, String productGroupId) {
        return empRepositoryInterface.selectEmpByMemberKey(memberKey,productGroupId);
    }

    @Override
    public List<Integer> selectRoleIdsByEmpId(String empId) {
        List<RoleEmployeeRelationEntity> list = Optional.ofNullable(empRepositoryInterface.selectRoleIdsByEmpId(empId)).orElse(new ArrayList<>());


        return list.stream().map(RoleEmployeeRelationEntity::getRoleId).collect(Collectors.toList());
    }

    @Override
    public EmpDetailDO selectEmpDetailById(Integer employeeInfoId) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = empRepositoryInterface.selectEmployeeInfoById(employeeInfoId);
        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException(EmpErrorEnum.INFO_ID_NOT_EXIST.getErrMsg());
        }
        EmpDetailDO empDetailDO = new EmpDetailDO();
        empDetailDO.setApplyId(sfaEmployeeInfoModel.getApplicationId());
        empDetailDO.setEmpId(sfaEmployeeInfoModel.getEmployeeId());
        empDetailDO.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        empDetailDO.setEmployeeStatus(EmployeeStatus.findNameByType(sfaEmployeeInfoModel.getEmployeeStatus()));
        empDetailDO.setMobile(sfaEmployeeInfoModel.getMobile());
        empDetailDO.setMemberKey(sfaEmployeeInfoModel.getMemberKey());


        String joiningCompany = sfaEmployeeInfoModel.getJoiningCompany();
        empDetailDO.setJoiningCompany(joiningCompany);
        if(!JobTransferConstants.WANT_WANT_COMPANY.equals(joiningCompany)){
            empDetailDO.setEmpId(StringUtils.EMPTY);
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId, sfaEmployeeInfoModel.getPositionId()).last("limit 1"));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException(EmpErrorEnum.POSITION_NOT_EXIST.getErrMsg());
        }
        empDetailDO.setPositionTypeId(ceoBusinessOrganizationPositionRelation.getPositionTypeId());

        ApplyMemberPo applyMemberPo =  empRepositoryInterface.selectApplyMemberById(sfaEmployeeInfoModel.getApplicationId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException(EmpErrorEnum.APPLY_MEMBER_NOT_EXIST.getErrMsg());
        }
        empDetailDO.setPositionName(PositionEnum.getPositionName(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition()));
        empDetailDO.setAvatar(applyMemberPo.getPicUrl());


        String workPlace = applyMemberPo.getWorkPlace();
        empDetailDO.setWorkPlace(workPlace);
        if(StringUtils.isNotBlank(workPlace)){
            empDetailDO.setWorkPlaceName(organizationMapper.getOrganizationName(workPlace));
        }

        // 获取管理业绩达成率
        if(applyMemberPo.getCeoType() == 1 && applyMemberPo.getPosition() != 1){

            String lastMonth = LocalDate.now().minusMonths(1L).toString().substring(0,7);
            String lastQuarter =  getLastQuarter();
            PerformanceDO performanceDO = empRepositoryInterface.getPerformanceRate(employeeInfoId,lastMonth,lastQuarter);
            if(Objects.nonNull(performanceDO)){
                performanceDO.setLastMonthPerformanceAchievementRate(Optional.ofNullable(performanceDO.getLastMonthPerformanceAchievementRate()).orElse(BigDecimal.ZERO).multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).setScale(2,BigDecimal.ROUND_UP));
                performanceDO.setLastQuarterPerformanceAchievementRate(Optional.ofNullable(performanceDO.getLastQuarterPerformanceAchievementRate()).orElse(BigDecimal.ZERO).multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).setScale(2,BigDecimal.ROUND_UP));
            }
            empDetailDO.setPerformanceDO(performanceDO);
        }

        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new LambdaQueryWrapper<SfaInterviewProcessModel>()
                .eq(SfaInterviewProcessModel::getApplicationId,applyMemberPo.getId()).last("limit 1")
        );

        if(Objects.nonNull(sfaInterviewProcessModel) && Objects.nonNull(sfaInterviewProcessModel.getOnboardTime())){
            LocalDateTime onBoardDate = LocalDateTimeUtils.convertDateToLDT(sfaInterviewProcessModel.getOnboardTime());
            empDetailDO.setOnBoardDate(onBoardDate.toLocalDate());

            LocalDateTime offBordDate = LocalDateTime.now();
            Date offTime = sfaInterviewProcessModel.getOffTime();
            if(Objects.nonNull(offTime)){
                offBordDate = LocalDateTimeUtils.convertDateToLDT(offTime);
            }

            long until = onBoardDate.until(offBordDate, ChronoUnit.DAYS);
            empDetailDO.setOnboardDays(until);
        }


        // 获取业务BD服务对象
        if(applyMemberPo.getCeoType() == 6 || applyMemberPo.getCeoType() == 7){
            List<ServerObjDO> serverObjDOS = empRepositoryInterface.selectServerObj(employeeInfoId);
            empDetailDO.setServerObjDOS(serverObjDOS);
        }

        // 查询薪资方案
        SalaryDO salaryDO = empRepositoryInterface.selectSalaryInfo(employeeInfoId);
        if(Objects.nonNull(salaryDO) && salaryDO.getId() != 0){
            empDetailDO.setSalaryDO(salaryDO);
        }


        // 获取岗位
        List<SfaPositionRelationEntity> positionRelationEntityList = Optional.ofNullable(empRepositoryInterface.selectPositionRelation(sfaEmployeeInfoModel.getMemberKey(),true)).orElse(new ArrayList<>());
        List<PositionDO> positionDOS = new ArrayList<>();
        positionRelationEntityList.forEach(e -> {
            PositionDO positionDO = new PositionDO();
            BeanUtils.copyProperties(e,positionDO);
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(e.getBusinessGroup());
            if(Objects.nonNull(sfaBusinessGroupEntity)){
                positionDO.setBusinessGroup(sfaBusinessGroupEntity.getId());
                positionDO.setBusinessGroupName(sfaBusinessGroupEntity.getBusinessGroupName());
            }
            positionDO.setStartTime(e.getStartValidDate().toLocalDate());
            positionDO.setPositionDesc();
            positionDO.setOrganizationId(e.getOrganizationCode());
            positionDOS.add(positionDO);
        });
        empDetailDO.setPositionDOS(positionDOS);
        if(!CollectionUtils.isEmpty(positionDOS)){
            empDetailDO.orderByPositionByPartTime();
        }

        return empDetailDO;
    }

    @Override
    public List<String> selectLoginUserOrg(String person, int businessGroup, Integer positionTypeId) {
        List<SfaPositionRelationEntity> positionRelationEntityList =  Optional.ofNullable(empRepositoryInterface.selectLoginUserOrg(person,businessGroup,positionTypeId)).orElse(new ArrayList<>());
        List<String> collect = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());
        return collect;
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoById(Integer employeeInfoId) {
        return empRepositoryInterface.selectEmployeeInfoById(employeeInfoId);
    }

    @Override
    public List<AssociateObjVO> getAssociate(int type, String name,String person) {
        List<AssociateObjVO> result = new ArrayList<>();

        List<SfaPositionRelationEntity> positionRelationEntityList = empRepositoryInterface.selectLoginUserOrg(person, RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getPositionTypeId());
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            throw new ApplicationException("登陆人组织信息异常");
        }


        List<String> orgCodes = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());

        // 查询合伙人
        if(type == 1){
           List<EmpModel> empModels = empRepositoryInterface.selectEmployeeInfoByKey(name,orgCodes,RequestUtils.getLoginInfo().getPositionTypeId(),RequestUtils.getBusinessGroup());
           if(CollectionUtils.isEmpty(empModels)){
               return result;
           }
            empModels.forEach(e -> {
                AssociateObjVO associateObjVO = new AssociateObjVO();
                Integer status = e.getStatus();
                String statusStr = "在职";
                if(status == 0){
                    statusStr = "离职";
                }
                associateObjVO.setName(e.getEmployeeName()+"-"+e.getMobile()+"-"+statusStr);
                associateObjVO.setMemberKey(e.getMemberKey());
                result.add(associateObjVO);
            });
        }else{

            List<String> organizationNames = organizationMapper.getOrganizationNames(orgCodes);


            // 查询客户
            List<CustomerInfo> customerInfos = empRepositoryInterface.selectCustomerByKey(name,organizationNames,RequestUtils.getLoginInfo().getPositionTypeId(),RequestUtils.getBusinessGroup());
            if(CollectionUtils.isEmpty(customerInfos)){
                return result;
            }
            customerInfos.forEach(e -> {
                AssociateObjVO associateObjVO = new AssociateObjVO();
                associateObjVO.setCustomerId(e.getCustomerId());
                associateObjVO.setName(e.getCustomerName());
                result.add(associateObjVO);
            });
        }

        return result;
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByApplyId(Integer applyId) {
        return empRepositoryInterface.selectEmployeeInfoByApplyId(applyId);
    }

    @Override
    public ApplyMemberPo selectApplyMemberById(Integer applyId) {
        return empRepositoryInterface.selectApplyMemberById(applyId);
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByMobile(String userMobile) {
        return empRepositoryInterface.selectEmployeeInfoByMobile(userMobile);
    }

    @Override
    public SfaEmployeeInfoModel selectEmployeeInfoByEmpId(String empId) {
        log.info("【select employee info by empId】empId:{}",empId);
        return empRepositoryInterface.selectEmployeeInfoByEmpId(empId);
    }


    @Override
    public String getEmpIdByMemberKey(Long memberKey) {
        return empRepositoryInterface.getEmpIdByMemberKey(memberKey);
    }

    private String getLastQuarter() {
        LocalDate currentDate = LocalDate.now();
        // 确定当前月份所在的季度
        int currentMonth = currentDate.getMonthValue();
        int currentQuarter = (currentMonth - 1) / 3 + 1;
        int year = currentDate.getYear();
        // 计算上一个季度的最后一个月份
        int previousQuarterMonthEnd = (currentQuarter - 2) * 3 + 3;
        // 跨年处理
        if(previousQuarterMonthEnd <= 0){
            previousQuarterMonthEnd += 12;
            year -= 1;
        }
        Month month = Month.of(previousQuarterMonthEnd);

        // 获取上一个季度的最后一天的日期
        LocalDate lastDayOfPreviousQuarter = LocalDate.of(year, month, month.length(currentDate.isLeapYear()));

        return lastDayOfPreviousQuarter.toString().substring(0,7);
    }

}
