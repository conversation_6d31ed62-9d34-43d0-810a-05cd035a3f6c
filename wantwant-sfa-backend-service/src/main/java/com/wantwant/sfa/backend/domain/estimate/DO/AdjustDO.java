package com.wantwant.sfa.backend.domain.estimate.DO;

import com.wantwant.sfa.backend.domain.estimate.DO.value.AdjustDetailValue;
import com.wantwant.sfa.backend.estimate.request.AdjustDetailRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/09/上午9:18
 */
@Data
public class AdjustDO {
    @ApiModelProperty("年月")
    private String yearMonth;
    @ApiModelProperty("sku")
    private String sku;
    @ApiModelProperty("期别ID")
    private Long shipPeriodId;
    @ApiModelProperty("调整物料明细")
    private List<AdjustDetailValue> adjustDetailList;

}
