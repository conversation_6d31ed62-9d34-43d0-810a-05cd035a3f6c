package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午1:23
 */
@Data
public class CeoEstimateApplyDO {

    @ApiModelProperty("销售预估单号")
    private String saleEstimateNo;

    @ApiModelProperty("产品组编码")
    private Integer businessGroup;

    @ApiModelProperty("申请人组织ID")
    private String applyOrganizationId;

    private Long instanceId;

    @ApiModelProperty("申请人岗位ID")
    private String applyPositionId;

    @ApiModelProperty("申请人姓名")
    private String applyUserName;

}
