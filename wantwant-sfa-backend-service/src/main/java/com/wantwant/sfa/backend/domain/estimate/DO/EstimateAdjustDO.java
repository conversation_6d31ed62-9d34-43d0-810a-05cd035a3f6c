package com.wantwant.sfa.backend.domain.estimate.DO;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/08/下午4:44
 */
@Data
public class EstimateAdjustDO {
    @ApiModelProperty("组织信息")
    @NotEmpty(message = "缺少组织信息")
    private List<EstimateAdjustOrgDO> orgList;
    @ApiModelProperty("年月")
    @NotBlank(message = "缺少年月")
    private String yearMonth;
    @ApiModelProperty("排期ID")
    @NotNull(message = "缺少排期ID")
    private Long shipPeriodId;
    @ApiModelProperty("sku")
    @NotBlank(message = "缺少sku")
    private String sku;
    @ApiModelProperty("处理结果:1.生产 2.不生产 3.驳回调整")
    @NotNull(message = "缺少处理结果")
    private Integer result;
}
