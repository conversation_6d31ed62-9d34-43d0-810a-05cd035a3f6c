package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/08/下午4:44
 */
@Data
public class EstimateAdjustOrgDO {
    @ApiModelProperty("组织code")
    @NotBlank(message = "缺少组织code")
    private String organizationId;
    @ApiModelProperty("类型：1.常规提报 2.追加提报")
    @NotNull(message = "缺少类型")
    private Integer type;
    @ApiModelProperty("原提报数量")
    private Integer rawEstimateCount;
}
