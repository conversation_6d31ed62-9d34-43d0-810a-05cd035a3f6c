package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/18/下午4:54
 */
@Data
public class EstimateApprovalDetailDO {
    @ApiModelProperty("sku")
    private String sku;
    @ApiModelProperty("sku名称")
    private String skuName;
    @ApiModelProperty("规格")
    private String fullCaseSpec;
    @ApiModelProperty("口味")
    private String flavor;
    @ApiModelProperty(value = "生产线ID")
    private String lineId;
    @ApiModelProperty("线别")
    private String lineName;
    @ApiModelProperty("spu")
    private String spu;
    @ApiModelProperty("spu名称")
    private String spuName;
    @ApiModelProperty(value = "保质期")
    private Integer shelfLife;
    @ApiModelProperty("盘价")
    private BigDecimal salePrice;
    @ApiModelProperty(value = "MOQ信息")
    private String moq;
    @ApiModelProperty("上市月份")
    private String expectListMonth;
    @ApiModelProperty("预警值 百分比形式 存小数")
    private BigDecimal warnPercent;
    @ApiModelProperty("标签名称集合")
    private String tagNameList;
    @ApiModelProperty("活动图片")
    private String activityImgUrl;
    @ApiModelProperty("活动标签")
    private String activityTags;
    @ApiModelProperty(value = "sku分类 1-造旺常规品项 2-造旺综合品项 3-集团经典品项")
    private Integer skuCategoryFlag;
    @ApiModelProperty("确认箱数")
    private Integer auditCount;
}
