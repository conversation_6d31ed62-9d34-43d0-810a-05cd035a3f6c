package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/19/上午10:50
 */
@Data
public class EstimateApprovalRejectDO {

    @ApiModelProperty("审核ID")
    @NotNull(message = "缺少审核ID")
    private Long approvalId;

    @ApiModelProperty("审核类型:10.营业所审核 20.分公司审核 30.大区审核 40.营运审核 50.产销审核")
    private Integer processStep;
}
