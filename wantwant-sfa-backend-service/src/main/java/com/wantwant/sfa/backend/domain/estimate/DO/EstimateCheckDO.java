package com.wantwant.sfa.backend.domain.estimate.DO;

import com.wantwant.sfa.backend.domain.estimate.DO.value.EstimateDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class EstimateCheckDO {
    @ApiModelProperty("提包ID")
    private Long approvalId;
    @ApiModelProperty("调整单ID")
    private Long adjustId;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("年月")
    private String theYearMonth;
    @ApiModelProperty("明细")
    private List<EstimateDetail> estimateDetailList;
}
