package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午5:45
 */
@Data
public class EstimateOrgSearchDO {
    @ApiModelProperty("排期ID")
    private Long scheduleId;
    @ApiModelProperty("产品组")
    private Integer businessGroup;
    @ApiModelProperty("排序字段:1.大区入职日期 2.分公司入职日期")
    private Integer orderField;
    @ApiModelProperty("排序类型:1.升 2.降")
    private Integer orderType;
}
