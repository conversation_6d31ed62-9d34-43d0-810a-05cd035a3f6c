package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午1:26
 */
@Data
public class EstimateOrganizationDO {

    @ApiModelProperty("大区code")
    private String vareaCode;
    @ApiModelProperty("大区名称")
    private String vareaName;
    @ApiModelProperty("大区主管姓名")
    private String vareaManagerName;
    @ApiModelProperty("大区入职日期")
    private String vareaOnBoardDate;
    @ApiModelProperty("分公司CODE")
    private String companyCode;
    @ApiModelProperty("分公司名称")
    private String companyName;
    @ApiModelProperty("分公司主管名称")
    private String companyManagerName;
    @ApiModelProperty("分公司入职日期")
    private String companyOnBoarDate;
    @ApiModelProperty("是否选中")
    private boolean checked;
}
