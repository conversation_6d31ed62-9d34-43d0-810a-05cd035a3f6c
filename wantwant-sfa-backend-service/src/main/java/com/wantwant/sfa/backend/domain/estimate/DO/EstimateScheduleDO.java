package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午1:22
 */
@Data
@ToString
public class EstimateScheduleDO {
    @ApiModelProperty("排期ID")
    private Long scheduleId;
    @ApiModelProperty("获需月份")
    private String theYearMonth;
    @ApiModelProperty("开始月份")
    private LocalDate startDate;
    @ApiModelProperty("结束月份")
    private LocalDate endDate;
    @ApiModelProperty("类型:1.常规 2.追加")
    private Integer type;
    @ApiModelProperty("货需期别")
    private Long shipPeriodId;

    private Integer businessGroup;
    @ApiModelProperty("可提报分公司")
    private List<EstimateOrganizationDO> estimateOrganizationDOList;
    @ApiModelProperty("可提报分公司显示用")
    private String estimateOrganizationListStr;
}
