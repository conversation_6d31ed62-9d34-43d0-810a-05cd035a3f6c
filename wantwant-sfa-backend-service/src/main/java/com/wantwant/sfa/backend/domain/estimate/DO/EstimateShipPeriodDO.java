package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/20/下午3:21
 */
@Data
public class EstimateShipPeriodDO {
    @ApiModelProperty("货需期别ID")
    private Long id;

    @ApiModelProperty("批次时段")
    private String batchPeriod;

    @ApiModelProperty("到货期限")
    private String deliveryDeadline;

    private Integer status;
}
