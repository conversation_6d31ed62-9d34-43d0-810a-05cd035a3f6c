package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午3:48
 */
@Data
public class EstimateSkuDO {

    @ApiModelProperty("sku")
    private String sku;

    @ApiModelProperty("spu")
    private String spu;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("产品线ID")
    private String lineId;

    @ApiModelProperty("sku名称")
    private String skuName;

    @ApiModelProperty("产品线")
    private String lineName;

    @ApiModelProperty("口味")
    private String flavor;

    @ApiModelProperty("规格")
    private String fullCaseSpec;

    @ApiModelProperty("保质期")
    private String shelfLife;

    @ApiModelProperty("MOQ")
    private Integer MOQ;

    @ApiModelProperty("生产排期")
    private String productionStage;

    @ApiModelProperty("上市月份")
    private String expectListMonth;

    @ApiModelProperty("合伙人盘价")
    private BigDecimal salesPrices;

    @ApiModelProperty("标签")
    private String tagName;

    @ApiModelProperty("预警阀值")
    private BigDecimal warnPercent;

    @ApiModelProperty("活动图片")
    private String activityImageUrl;

    @ApiModelProperty("活动标签")
    private String activityDescription;

    @ApiModelProperty("类型:1.常规品项 2.综合品香 3.经典品项")
    private Integer type;

    @ApiModelProperty("指定组织信息")
    private List<EstimateSkuOrgDO> orgVOS;

    private List<String> specifyOrgCodes;
}
