package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午3:47
 */
@Data
public class EstimateSkuGroupDO {

    @ApiModelProperty("物料组ID")
    private Long groupId;

    @ApiModelProperty("货需期别ID")
    private Long shipPeriodId;

    @ApiModelProperty("物料组名称")
    private String groupName;

    @ApiModelProperty("产品组")
    private Integer businessGroup;

    @ApiModelProperty("skuList")
    private List<EstimateSkuDO> skuDOList;


}
