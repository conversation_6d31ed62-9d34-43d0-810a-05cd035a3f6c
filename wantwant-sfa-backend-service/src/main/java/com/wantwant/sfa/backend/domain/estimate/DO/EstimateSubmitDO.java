package com.wantwant.sfa.backend.domain.estimate.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/19/下午1:31
 */
@Data
public class EstimateSubmitDO {

    @ApiModelProperty("排期ID")
    @NotNull(message = "缺少排期ID")
    private Long scheduleId;

    @ApiModelProperty("审核类型:0.合伙人审核 10.营业所审核 20.分公司审核 30.大区审核 40.营运审核 50.产销审核")
    private Integer processStep;


    @ApiModelProperty("组织ID")
    private String organizationId;

    private String applyPositionId;


    private Long instanceId;

    private boolean redo;

    private List<EstimateApprovalDetailDO> skuList;
}
