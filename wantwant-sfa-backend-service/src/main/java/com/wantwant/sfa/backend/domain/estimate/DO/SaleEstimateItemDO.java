package com.wantwant.sfa.backend.domain.estimate.DO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/28/下午2:29
 */
@Data
public class SaleEstimateItemDO {
    /** 商品SKU */
    private String sku;
    /** 商品PT */
    private String ptKey;

    private String reason;
    /** 预估数量 */
    private BigDecimal quantity;
    /** 预估金额 */
    private BigDecimal subtotal;
    /** 经销价 */
    private BigDecimal supplyPrice;
}
