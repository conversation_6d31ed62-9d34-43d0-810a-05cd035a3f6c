package com.wantwant.sfa.backend.domain.estimate.DO.value;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/09/上午9:19
 */
@Data
public class AdjustDetailValue {
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("类型:1.常规提报 2.追加提报")
    private Integer type;
    @ApiModelProperty("最终确认数")
    private Integer finalAuditCount;
}
