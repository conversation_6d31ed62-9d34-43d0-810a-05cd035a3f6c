package com.wantwant.sfa.backend.domain.estimate.enums;

/**
 * 调整类型枚举
 *
 * <AUTHOR> Assistant
 * @date 2024/11/29
 */
public enum AdjustTypeEnum {
    ESTIMATE(1, "预估"),
    APPEND(2, "追加");
    
    private final Integer code;
    private final String desc;
    
    AdjustTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举
     */
    public static AdjustTypeEnum fromCode(Integer code) {
        for (AdjustTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的调整类型代码: " + code);
    }
} 