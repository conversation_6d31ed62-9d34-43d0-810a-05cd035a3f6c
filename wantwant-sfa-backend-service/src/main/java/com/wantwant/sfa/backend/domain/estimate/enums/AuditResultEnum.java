package com.wantwant.sfa.backend.domain.estimate.enums;

/**
 * 审核结果枚举
 *
 * <AUTHOR> Assistant
 * @date 2024/11/29
 */
public enum AuditResultEnum {
    APPROVED(1, "审核通过"),
    REJECTED(2, "审核拒绝"), 
    RESUBMIT(3, "重新提交");
    
    private final Integer code;
    private final String desc;
    
    AuditResultEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举
     */
    public static AuditResultEnum fromCode(Integer code) {
        for (AuditResultEnum result : values()) {
            if (result.code.equals(code)) {
                return result;
            }
        }
        throw new IllegalArgumentException("未知的审核结果代码: " + code);
    }
} 