package com.wantwant.sfa.backend.domain.estimate.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/08/下午5:11
 */
public enum EstimateAdjustStatusEnum {
    ADJUST_PROCESSING(1,"调整中"),
    ADJUST_FINISH(2,"已调整"),
    ADJUST_NOT_PRODUCT(3,"不生产"),
    ADJUST_PRODUCT(4,"已完成"),
    ADJUST_NOT_PROCESS(0,"未处理");

    private int status;

    private String name;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    EstimateAdjustStatusEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }


}
