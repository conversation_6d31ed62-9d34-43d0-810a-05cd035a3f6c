package com.wantwant.sfa.backend.domain.estimate.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午4:34
 */
public enum EstimateErrorMsgEnum {
    GROUP_NAME_DUPLICATION("E0100001","sku组名称重复"),
    GROUP_ID_NOT_EXIST("E0100002","物料组ID不存在"),
    SKU_ERROR("E0100003","SKU编码异常"),
    SCHEDULE_CONFLICT("E0100004","排期冲突"),
    SCHEDULE_DATE_ERROR("E0100005","排期日期错误"),
    SALE_ESTIMATE_NO_ERROR("E0100006","销售预估单号不存在"),
    CEO_ERROR("E0100007","合伙人信息错误"),
    APPROVAL_NOT_EXIST("E0100008","申请信息获不存在"),
    SCHEDULE_NOT_MATCH("E0100009","无匹配的排期"),
    REPEATED_OPERATION("E0100010","请勿重复操作"),
    CEO_TYPE_ERROR("E0100011","不支持旺铺非合伙人申请"),
    SHIP_PERIOD_NOT_EXIST("E0100012","货需期别不存在"),
    ADJUST_NOT_EXIST("E0100013","调整单信息不存在"),
    MOQ_NOT_EXIST("E0100014", "MOQ不存在");




    private String code;

    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    EstimateErrorMsgEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
