package com.wantwant.sfa.backend.domain.estimate.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/上午9:26
 */
public enum EstimateStatus {
    NORMAL(1,"正常"),
    DISABLE(0,"停用");

    private int status;

    private String name;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    EstimateStatus(int status, String name) {
        this.status = status;
        this.name = name;
    }
}
