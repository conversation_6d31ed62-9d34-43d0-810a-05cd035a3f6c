package com.wantwant.sfa.backend.domain.estimate.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午2:09
 */
public enum EstimateSubmitEnum {
    SUBMIT(1,"提交"),
    UN_SUBMIT(0,"未提交");

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    EstimateSubmitEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
