package com.wantwant.sfa.backend.domain.estimate.enums;

import com.alibaba.excel.util.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午3:39
 */
public enum EstimateTypeEnum {
    ROUTINE(1,"常规提报"),
    APPEND(2,"追加提报");

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    EstimateTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getTypeName(int type){
        Optional<EstimateTypeEnum> existOptional = Arrays.asList(EstimateTypeEnum.values()).stream().filter(f -> f.getType() == type).findFirst();
        if(existOptional.isPresent()){
            return existOptional.get().getName();
        }
        return StringUtils.EMPTY;
    }
}
