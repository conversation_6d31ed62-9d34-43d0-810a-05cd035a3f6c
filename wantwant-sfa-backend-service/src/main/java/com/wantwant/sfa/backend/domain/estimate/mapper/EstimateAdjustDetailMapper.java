package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.estimate.DO.value.EstimateDetail;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateAdjustDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustDetailPO;
import com.wantwant.sfa.backend.estimate.request.AdjustDetailSearchRequest;
import com.wantwant.sfa.backend.estimate.request.EstimateAdjustSearchRequest;
import com.wantwant.sfa.backend.estimate.vo.EstimateAdjustVO;
import com.wantwant.sfa.backend.estimate.vo.OrgAdjustVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/08/下午3:27
 */
public interface EstimateAdjustDetailMapper extends BaseMapper<EstimateAdjustDetailPO> {

    /**
     * 查询当前有效的调整记录
     *
     * @param yearMonth
     * @param shipPeriodId
     * @param sku
     * @return
     */
    List<EstimateAdjustDTO> selectAdjust(@Param("yearMonth") String yearMonth, @Param("shipPeriodId")Long shipPeriodId, @Param("sku") String sku);

    /**
     * 获取大区调整列表
     *
     * @param estimateAdjustSearchRequest
     * @return
     */
    List<EstimateAdjustVO> selectAreaAdjust(@Param("page") IPage page,@Param("request") EstimateAdjustSearchRequest estimateAdjustSearchRequest);


    List<OrgAdjustVO> selectAdjustDetail(AdjustDetailSearchRequest adjustDetailSearchRequest);

    /**
     * 获取调整中的数量
     *
     * @param theYearMonth
     * @param organizationId
     * @param skuList
     * @param adjustId
     * @return
     */
    List<EstimateDetail> selectAdjustDetailBySku(@Param("theYearMonth") String theYearMonth, @Param("organizationId") String organizationId,
                                                 @Param("skuList") List<String> skuList, @Param("adjustId") Long adjustId);
}
