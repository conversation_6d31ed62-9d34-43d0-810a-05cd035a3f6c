package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateSkuDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateSummaryDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.MOQ;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailHistoryPO;
import com.wantwant.sfa.backend.estimate.request.EstimateSearchRequest;
import com.wantwant.sfa.backend.estimate.vo.EstimateSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/18/下午7:14
 */
public interface EstimateApprovalDetailHistoryMapper extends BaseMapper<EstimateApprovalDetailHistoryPO> {

    /**
     * 获取下级提报数据
     *
     * @param organizationId
     * @param organizationType
     * @param month
     * @return
     */
    List<EstimateSkuDTO> selectLowerEstimate(@Param("organizationId") String organizationId, @Param("organizationType") String organizationType, @Param("month") String month);

    /**
     * 查询汇总
     *
     * @param page
     * @param estimateSearchRequest
     * @return
     */
    List<EstimateSummaryDTO> selectSummary(@Param("page") Page<EstimateSummaryVO> page, @Param("request") EstimateSearchRequest estimateSearchRequest);

    /**
     * 获取已提报MOQ
     *
     * @param skuList
     * @param theYearMonth
     * @return
     */
    List<MOQ> selectCurrentMOQ(@Param("skuList") List<String> skuList, @Param("theYearMonth") String theYearMonth,@Param("excludeOrgCodes")List<String>excludeOrgCodes);

}
