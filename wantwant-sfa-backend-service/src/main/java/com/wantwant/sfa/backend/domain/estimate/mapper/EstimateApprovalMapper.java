package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalDetailDO;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.*;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalPO;
import com.wantwant.sfa.backend.estimate.request.*;
import com.wantwant.sfa.backend.estimate.vo.EstimateApprovalVO;
import com.wantwant.sfa.backend.estimate.vo.EstimateDetailVO;
import com.wantwant.sfa.backend.estimate.vo.EstimateMOQVO;
import com.wantwant.sfa.backend.estimate.vo.MOQSkuVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午2:05
 */
public interface EstimateApprovalMapper extends BaseMapper<EstimateApprovalPO> {

    /**
     * 查询审核列表
     *
     * @param page
     * @param estimateApprovalSearchRequest
     * @return
     */
    List<EstimateApprovalDTO> selectApprovalList(@Param("page") Page<EstimateApprovalVO> page, @Param("request") EstimateApprovalSearchRequest estimateApprovalSearchRequest);

    /**
     * 查询合计信息
     *
     * @param estimateApprovalSearchRequest
     * @return
     */
    EstimateApprovalSummaryDTO selectApprovalSummary( @Param("request")EstimateApprovalSearchRequest estimateApprovalSearchRequest);

    /**
     * 获取审核状态
     *
     * @param approvalId
     * @return
     */
    String selectProcessStatus(Long approvalId);

    /**
     * 获取可提报物料
     *
     * @param approvalId
     * @return
     */
    List<EstimateApprovalItemDTO> selectCanSubmitSku(@Param("approvalId") Long approvalId);

    /**
     * 获取已提报的物料
     *
     * @param approvalId
     * @return
     */
    List<EstimateApprovalItemDTO> selectSubmitSku(@Param("approvalId")Long approvalId);

    /**
     * 查询提报物料
     *
     * @param estimateSubmitSearchRequest
     * @return
     */
    List<EstimateSubmitDTO> selectEstimateSubmit(EstimateSubmitSearchRequest estimateSubmitSearchRequest);

    /**
     * 根据scheduleId获取可提报物料
     *
     * @param estimateSubmitSkuRequest
     * @return
     */
    List<EstimateApprovalItemDTO> selectSubmitSkuBySchedule(EstimateSubmitSkuRequest estimateSubmitSkuRequest);

    List<EstimateDetailDTO> selectDetail(@Param("page")Page<EstimateDetailVO> page, @Param("request")EstimateSearchRequest estimateSearchRequest);

    EstimateApprovalSummaryDTO selectDetailSummary(@Param("request")EstimateSearchRequest estimateSearchRequest);

    String getStoreName(@Param("organizationId") String organizationId);

    /**
     * 查询审核数量
     *
     * @param orgCodes
     * @param flowCode
     * @param roleIds
     * @return
     */
    int getEstimateProcessCount(@Param("orgCodes") List<String> orgCodes,@Param("orgType")String orgType, @Param("flowCode") String flowCode, @Param("roleIds") List<Integer> roleIds);

    int getSubmitCount(@Param("orgCodes") List<String> orgCodes,@Param("orgType")String orgType);

    List<EstimateMOQVO> selectMOQ(@Param("page") IPage<EstimateMOQVO> page, @Param("req") MOQSearchRequest moqSearchRequest);

    List<MOQSkuVO> selectMOQDetail(@Param("yearMonth") String yearMonth, @Param("shipPeriodId") Long shipPeriodId, @Param("sku") String sku);


    List<EstimateHistoryDTO> selectHistoryBySku(@Param("theYearMonth") String theYearMonth,
                                                @Param("organizationId") String organizationId,
                                                @Param("skuList") List<String> skuList);

    List<EstimateHistoryDTO> selectHistoryByOrgCode(@Param("month") String month, @Param("organizationId") String organizationId);

    List<EstimateSummaryDTO> selectHistoryByResult(@Param("list") List<EstimateSummaryDTO> list);

    /**
     * 根据结果获取MOQ
     *
     * @param list
     * @return
     */
    List<MOQ> selectMOQByResult(List<EstimateSummaryDTO> list);


    List<EstimateApprovalDetailPO> selectLastSubmitPO(@Param("organizationId") String organizationId, @Param("scheduleId") Long scheduleId);

    /**
     * 查询已提包物料总金额
     *
     * @param theYearMonth
     * @param organizationId
     * @param skuList
     * @return
     */
    List<EstimateApprovalDetailDO> selectSubmitPrice(@Param("theYearMonth") String theYearMonth, @Param("organizationId") String organizationId,
                                                     @Param("skuList") List<String> skuList, @Param("approvalId")Long approvalId);

    /**
     * 查询已提包完成的物料金额
     *
     * @param theYearMonth
     * @param organizationId
     * @param skuList
     * @return
     */
    List<EstimateApprovalDetailDO>  selectFinishedBySku(@Param("theYearMonth") String theYearMonth, @Param("organizationId") String organizationId, @Param("skuList") List<String> skuList);
}
