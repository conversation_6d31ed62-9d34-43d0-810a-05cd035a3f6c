package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/18/下午2:40
 */
@DS("production_6")
public interface EstimateBigTableMapper {

    /**
     * 查询实际销售箱数
     *
     * @param organizationId
     * @param month
     * @param skuList
     * @return
     */
    List<EstimateActualInfoDTO> selectEstimateActualInfo(@Param("organizationId") String organizationId, @Param("month") String month, @Param("skuList") List<String> skuList);

    /**
     * 根据查询结果获取实际箱数
     *
     * @param list
     * @return
     */
    List<EstimateActualInfoDTO> selectSummaryActualInfo(@Param("list") List<EstimateSummaryDTO> list);

    /**
     * 获取sku仓信息
     *
     * @param storeName
     * @param skuList
     * @param yearMonth
     * @return
     */
    List<SkuInventory> selectSkuInventory(@Param("storeName") String storeName, @Param("skuList") List<String> skuList, @Param("theYearMonth") String yearMonth,@Param("businessGroup")Integer businessGroup);

    /**
     * 获取预订单数量
     *
     * @param organizationId
     * @param organizationType
     * @param skuList
     * @param theYearMonth
     * @return
     */
    List<SkuAdvancedOrder> selectAdvancedOrderBox(@Param("organizationId") String organizationId,
                                                  @Param("orgType") String organizationType,
                                                  @Param("skuList") List<String> skuList,
                                                  @Param("theYearMonth") String theYearMonth);

    /**
     * 根据查询结果获取预定单
     *
     * @param list
     * @return
     */
    List<SkuAdvancedOrder> selectAdvancedOrderBoxByResult(@Param("list") List<EstimateSummaryDTO> list);


    List<SkuAdvancedOrder> selectZBAdvancedOrder(@Param("orgCode") String zbOrgCode,@Param("yearMonth")String yearMonth, @Param("skuList") List<String> skuList);

    BigDecimal getRecentThreeMonthsSalesPerformance(@Param("beforeMonth") String beforeMonth, @Param("endMonth") String theYearMonth, @Param("organizationId") String organizationId);
}
