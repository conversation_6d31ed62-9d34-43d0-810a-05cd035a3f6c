package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.wantwant.sfa.backend.domain.estimate.DO.SaleEstimateDO;
import org.apache.ibatis.annotations.Param;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午1:37
 */
public interface EstimateExternalMapper {

    /**
     * 根据预估单号获取预估单信息
     *
     * @param saleEstimateNo
     * @return
     */
    SaleEstimateDO getSaleEstimateBySaleEstimateNo(@Param("saleEstimateNo") String saleEstimateNo);
}
