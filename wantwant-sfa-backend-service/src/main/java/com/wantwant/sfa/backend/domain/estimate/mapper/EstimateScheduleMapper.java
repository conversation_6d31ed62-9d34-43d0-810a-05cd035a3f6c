package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrgSearchDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrganizationDO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSchedulePO;
import com.wantwant.sfa.backend.estimate.vo.EstimateOrganizationVO;
import com.wantwant.sfa.backend.estimate.vo.EstimateScheduleDetailVO;
import com.wantwant.sfa.backend.estimate.vo.EstimateScheduleVO;
import org.apache.ibatis.annotations.Param;


import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午1:48
 */
public interface EstimateScheduleMapper extends BaseMapper<EstimateSchedulePO> {

    /**
     * 检查是否冲突
     *
     * @param startDate
     * @param endDate
     * @param scheduleId
     * @param estimateOrganizationDOList
     * @return
     */
    Long checkScheduleConflict(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("yearMonth") String yearMonth, @Param("scheduleId") Long scheduleId, @Param("businessGroup") Integer businessGroup,@Param("shipPeriodId")Long shipPeriodId, @Param("orgList") List<EstimateOrganizationDO> estimateOrganizationDOList);

    /**
     * 获取排期列表
     *
     * @param yearMonth
     * @param businessGroup
     * @return
     */
    List<EstimateScheduleVO> selectSchedule(@Param("yearMonth") String yearMonth, @Param("businessGroup") Integer businessGroup);

    /**
     * 获取排期明细
     *
     * @param scheduleId
     * @return
     */
    EstimateScheduleDetailVO selectScheduleDetail(Long scheduleId);

    /**
     * 获取可提报组织
     *
     * @param estimateOrgSearchDO
     * @return
     */
    List<EstimateOrganizationVO> selectEstimateOrg(EstimateOrgSearchDO estimateOrgSearchDO);

    /**
     * 匹配排期
     *
     * @param currentDate
     * @param month
     * @param type
     * @return
     */
    EstimateSchedulePO matchSchedule(@Param("currentDate") LocalDate currentDate, @Param("yearMonth") String month, @Param("type") Integer type,@Param("businessGroup")Integer businessGroup,@Param("shipPeriodId") Integer shipPeriodId);

    /**
     * 根据排期号获取货需期别ID
     *
     * @param scheduleId
     * @return
     */
    Long selectShipPeriodBySchedule(@Param("scheduleId") Long scheduleId);

}
