package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateScheduleOrganizationRelationPO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午2:27
 */
public interface EstimateScheduleOrganizationRelationMapper extends BaseMapper<EstimateScheduleOrganizationRelationPO> {

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(List<EstimateScheduleOrganizationRelationPO> list);
}
