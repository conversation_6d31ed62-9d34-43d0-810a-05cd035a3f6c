package com.wantwant.sfa.backend.domain.estimate.mapper;

import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSkuGroupPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.estimate.vo.EstimateSkuGroupVO;
import com.wantwant.sfa.backend.estimate.vo.StoreVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 销售预估物料组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface EstimateSkuGroupMapper extends BaseMapper<EstimateSkuGroupPO> {

    /**
     * 根据产品组获取物料组信息
     *
     * @param businessGroup
     * @return
     */
    List<EstimateSkuGroupVO> selectEstimateGroupByBusinessGroup(@Param("businessGroup") int businessGroup);

    /**
     * 仓库查询
     *
     * @param businessGroup
     * @return
     */
    List<StoreVO> getStore(@Param("businessGroup") int businessGroup);

    /**
     * 获取SKU的MOQ
     *
     * @param sku
     * @param businessGroup
     * @return
     */
    BigDecimal getMOQ(@Param("sku") String sku, @Param("businessGroup") int businessGroup);
}
