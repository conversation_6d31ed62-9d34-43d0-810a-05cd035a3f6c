package com.wantwant.sfa.backend.domain.estimate.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSkuPO;
import com.wantwant.sfa.backend.estimate.vo.EstimateSkuVO;
import com.wantwant.sfa.backend.estimate.vo.SkuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售预估sku物料表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface EstimateSkuMapper extends BaseMapper<EstimateSkuPO> {

    List<EstimateSkuVO> selectSkuDetailByGroupId(@Param("groupId") Long groupId);

    List<SkuVO> getSku(@Param("businessGroup") Integer businessGroup);

}
