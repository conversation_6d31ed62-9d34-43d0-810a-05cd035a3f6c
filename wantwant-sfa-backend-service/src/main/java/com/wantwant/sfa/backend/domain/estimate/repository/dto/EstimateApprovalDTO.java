package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/18/上午9:26
 */
@Data
public class EstimateApprovalDTO {
    @ApiModelProperty("审核ID")
    private Long approvalId;
    @ApiModelProperty("货需月份")
    @Excel(name = "货需月份")
    private String yearMonth;
    @ApiModelProperty("提报开始日期")
    private LocalDate startDate;
    @ApiModelProperty("提报结束日期")
    private LocalDate endDate;
    @ApiModelProperty("类型")
    @Excel(name = "模式",replace = {"常规提报_1", "追加提报_2"})
    private Integer type;

    @ApiModelProperty("大区名称")
    @Excel(name = "大区名称")
    private String areaName;
    @ApiModelProperty("分公司名称")
    @Excel(name = "分公司名称")
    private String companyName;
    @ApiModelProperty("营业所名称")
    private String departmentName;
    @ApiModelProperty("仓库名称")
    @Excel(name = "仓库")
    private String storeName;

    @ApiModelProperty("提报时间")
    @Excel(name = "提报时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;
    @ApiModelProperty("流程步骤")
    private String processName;
    @ApiModelProperty("提报人")
    @Excel(name = "提报人")
    private String applyUserName;
    @ApiModelProperty("预估金额")
    @Excel(name = "预估金额")
    private int estimatePrice;
    @ApiModelProperty("确认金额")
    @Excel(name = "确认金额")
    private int auditPrice;
    @ApiModelProperty("预估箱数")
    @Excel(name = "预估箱数")
    private Integer estimateQuantity;
    @ApiModelProperty("确认箱数")
    @Excel(name = "确认箱数")
    private Integer auditQuantity;
    @ApiModelProperty("审核时间")
    @Excel(name = "审核时间",exportFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;
    @ApiModelProperty("审核状态")
    private Integer processResult;
    @ApiModelProperty("货需名称")
    private String shipPeriodName;
    @ApiModelProperty("批次时段")
    private String batchPeriod;
    @ApiModelProperty("到货期限")
    private String deliveryDeadline;

    private Integer finalResult;
    @ApiModelProperty("审核步骤")
    private Integer processStep;
    @ApiModelProperty("组织类型")
    private String organizationType;
    @ApiModelProperty("流程CODE")
    private String flowCode;
    @ApiModelProperty("是否提交")
    private Integer isSubmit;
    @ApiModelProperty("角色ID")
    private Integer roleId;
    @ApiModelProperty("组织ID")
    private String organizationId;
}
