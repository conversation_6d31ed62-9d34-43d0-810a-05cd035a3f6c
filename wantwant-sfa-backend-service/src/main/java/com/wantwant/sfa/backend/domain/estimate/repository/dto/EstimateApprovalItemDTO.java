package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import com.wantwant.sfa.backend.infrastructure.client.Estimate.model.ActivityTagDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/18/下午1:34
 */
@Data
public class EstimateApprovalItemDTO {

    @ApiModelProperty("sku名称")
    private String skuName;
    @ApiModelProperty("规格")
    private String fullCaseSpec;
    @ApiModelProperty("口味")
    private String flavor;
    @ApiModelProperty("sku编码")
    private String sku;
    @ApiModelProperty(value = "生产线ID")
    private String lineId;
    @ApiModelProperty("线别")
    private String lineName;
    @ApiModelProperty("spu")
    private String spu;
    @ApiModelProperty("spu")
    private String spuName;
    @ApiModelProperty(value = "保质期")
    private Integer shelfLife;
    @ApiModelProperty("盘价")
    private BigDecimal salePrice;
    @ApiModelProperty(value = "MOQ信息")
    private String moq;
    @ApiModelProperty("上市月份")
    private String expectListMonth;
    @ApiModelProperty("预警值 百分比形式 存小数")
    private BigDecimal warnPercent;
    @ApiModelProperty("标签名称集合")
    private String tagNameList;
    @ApiModelProperty(value = "sku分类 1-造旺常规品项 2-造旺综合品项 3-集团经典品项")
    private Integer skuCategoryFlag;
    @ApiModelProperty("活动图片")
    private String activityImgUrl;
    @ApiModelProperty("活动标签")
    private String activityTags;
    @ApiModelProperty("产品标签")
    private String tagName;
    @ApiModelProperty("上上月")
    private String lastLastMonth;
    @ApiModelProperty("上上月预估箱数")
    private Integer lastLastEstimateCount;
    @ApiModelProperty("上月")
    private String lastMonth;
    @ApiModelProperty("上月预估箱数")
    private Integer lastEstimateCount;
    @ApiModelProperty("提报箱数")
    private Integer estimateCount;
    @ApiModelProperty("确认箱数")
    private Integer auditCount;

}
