package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/30/上午11:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EstimateApprovalSummaryDTO {
    @ApiModelProperty("提报箱数合计")
    private BigDecimal totalEstimateQuantity;
    @ApiModelProperty("确认箱数合计")
    private BigDecimal totalAuditQuantity;
    @ApiModelProperty("提报金额合计")
    private BigDecimal totalEstimatePrice;
    @ApiModelProperty("确认金额合计")
    private BigDecimal totalAuditPrice;
}
