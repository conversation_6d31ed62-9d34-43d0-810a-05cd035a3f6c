package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/22/上午11:54
 */
@Data
public class EstimateDetailDTO {


    @ApiModelProperty("提报ID")
    @Excel(name = "提报ID")
    private Long approvalId;

    @ApiModelProperty("明细行ID")
    @Excel(name = "明细行ID")
    private Long detailId;

    @ApiModelProperty("产品组名称")
    @Excel(name = "产品组名称")
    private String businessGroupName;

    @ApiModelProperty("大区")
    @Excel(name = "大区名称")
    private String vareaName;

    @Excel(name = "分公司")
    @ApiModelProperty("分公司")
    private String companyName;
    @Excel(name = "营业所")
    @ApiModelProperty("营业所")
    private String departmentName;
    @Excel(name = "提报时间")
    @ApiModelProperty("提报时间")
    private LocalDateTime submitTime;

    @ApiModelProperty("提报模式：1.常规 2.追加")
    @Excel(name = "提报模式", replace = {"常规提报_1","追加提报_2"})
    private Integer type;

    @ApiModelProperty("期别")
    @Excel(name = "期别")
    private String shipPeriodName;

    @Excel(name = "仓库")
    @ApiModelProperty("仓库")
    private String storeName;

    @Excel(name = "线别")
    @ApiModelProperty("线别")
    private String lineName;

    @Excel(name = "物料编码")
    @ApiModelProperty("物料编码")
    private String sku;


    @ApiModelProperty("物料名称")
    private String skuName;
    @ApiModelProperty("口味")
    private String flavor;
    @ApiModelProperty("规格")
    private String fullCaseSpec;

    @ApiModelProperty("提报箱数")
    @Excel(name = "提报箱数")
    private Integer estimateCount;
    @ApiModelProperty("提报金额")
    @Excel(name = "提报金额")
    private Integer estimatePrice;
    @ApiModelProperty("确认箱数")
    @Excel(name = "确认箱数")
    private Integer auditCount;
    @ApiModelProperty("确认金额")
    @Excel(name = "确认金额")
    private Integer auditPrice;

    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private Integer result;

    private Integer processStep;

    private String flowCode;


}
