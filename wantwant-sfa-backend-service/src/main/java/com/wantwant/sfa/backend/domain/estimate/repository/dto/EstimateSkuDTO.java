package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailHistoryPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/19/下午4:13
 */
@Data
public class EstimateSkuDTO {

    private String sku;

    private Integer estimateCount;

    public static EstimateSkuDTO build(EstimateApprovalDetailHistoryPO estimateApprovalDetailHistoryPO) {
        EstimateSkuDTO estimateSkuDTO = new EstimateSkuDTO();
        estimateSkuDTO.setSku(estimateApprovalDetailHistoryPO.getSku());

        Integer appendQuantity = Optional.ofNullable(estimateApprovalDetailHistoryPO.getAppendQuantity()).orElse(0);
        Integer estimateQuantity = Optional.ofNullable(estimateApprovalDetailHistoryPO.getEstimateQuantity()).orElse(0);

        estimateSkuDTO.setEstimateCount(appendQuantity+estimateQuantity);
        return estimateSkuDTO;
    }
}
