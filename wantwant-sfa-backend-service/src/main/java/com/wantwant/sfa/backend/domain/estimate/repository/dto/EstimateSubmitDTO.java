package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/19/上午9:08
 */
@Data
public class EstimateSubmitDTO {
    private Long scheduleId;
    @ApiModelProperty("审核ID")
    private Long approvalId;
    @ApiModelProperty("月份")
    private String yearMonth;
    @ApiModelProperty("开始日期")
    private LocalDate startDate;
    @ApiModelProperty("结束日期")
    private LocalDate endDate;
    @ApiModelProperty("提交模式")
    private Integer type;
    @ApiModelProperty("大区名称")
    private String vareaName;
    @ApiModelProperty("分公司名称")
    private String companyName;
    @ApiModelProperty("提交时间")
    private LocalDateTime submitTime;
    @ApiModelProperty("提报金额")
    private int estimatePrice;
    @ApiModelProperty("确认金额")
    private int auditPrice;
    @ApiModelProperty("审核时间")
    private LocalDateTime processTime;

    private String submitUserName;

    @ApiModelProperty("货需名称")
    private String shipPeriodName;
    @ApiModelProperty("批次时段")
    private String batchPeriod;
    @ApiModelProperty("到货期限")
    private String deliveryDeadline;

    @ApiModelProperty("审核人")
    private String processUserName;
    @ApiModelProperty("审核步骤")
    private Integer processStep;
    @ApiModelProperty("审核状态")
    private Integer processResult;

    private Integer processRoleId;

    private String organizationId;

    private String organizationType;

    private String processName;



}
