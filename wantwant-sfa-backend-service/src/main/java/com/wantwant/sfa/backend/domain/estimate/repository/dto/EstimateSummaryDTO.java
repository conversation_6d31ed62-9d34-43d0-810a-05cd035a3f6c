package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/22/上午10:12
 */
@Data
public class EstimateSummaryDTO {
    @ApiModelProperty("产品组")
    private String businessGroupName;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("获需月份")
    private String yearMonth;
    @ApiModelProperty("大区")
    private String vareaName;
    @ApiModelProperty("分公司")
    private String companyName;
    @ApiModelProperty("营业所")
    private String departmentName;
    @ApiModelProperty("大区名称")
    private String storeName;
    @ApiModelProperty("线别")
    private String lineName;
    @ApiModelProperty("sku")
    private String sku;
    @ApiModelProperty("物料名称")
    private String skuName;
    @ApiModelProperty("口味")
    private String flavor;
    @ApiModelProperty("规格")
    private String fullCaseSpec;
    @ApiModelProperty("上上月")
    private String lastLastMonth;
    @ApiModelProperty("上月")
    private String lastMonth;
    @ApiModelProperty("常规提报箱数")
    private Integer estimateCount;
    @ApiModelProperty("追加提报箱数")
    private Integer appendCount;
    @ApiModelProperty("合计箱数")
    private Integer totalCount;
    @ApiModelProperty("合计金额")
    private Integer totalPrice;

    private String shipPeriodName;

    private String companyCode;

    private BigDecimal MOQ;

    private BigDecimal currentMOQ;

    private Integer lastEstimateCount;

    private String orgType;

    private Integer lastLastEstimateCount;

    private BigDecimal advancedOrderBox;

    private BigDecimal thirdOrderPrice;
}
