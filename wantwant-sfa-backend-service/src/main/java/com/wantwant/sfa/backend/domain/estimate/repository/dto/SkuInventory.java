package com.wantwant.sfa.backend.domain.estimate.repository.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/25/上午10:27
 */
@Data
public class SkuInventory {

    private String sku;
//    /** 异常库存数 */
//    private BigDecimal abnormalInventoryBoxesTotal;
//    /** 常态库存数 */
//    private BigDecimal normalInventoryBoxesTotal;

    /** 可出货数量 */
    private BigDecimal canbeShipmentBoxes;
    /** 异常锁库数量 */
    private BigDecimal abnormalLockedInventoryBoxes;

}
