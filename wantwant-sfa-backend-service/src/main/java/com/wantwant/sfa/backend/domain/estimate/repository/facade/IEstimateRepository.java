package com.wantwant.sfa.backend.domain.estimate.repository.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalDetailDO;
import com.wantwant.sfa.backend.domain.estimate.DO.value.EstimateDetail;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.*;
import com.wantwant.sfa.backend.domain.estimate.repository.po.*;
import com.wantwant.sfa.backend.estimate.request.*;
import com.wantwant.sfa.backend.estimate.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午2:05
 */
public interface IEstimateRepository {

    /**
     * 保存申请信息
     *
     * @param estimateApprovalPO
     */
    void saveEstimateApproval(EstimateApprovalPO estimateApprovalPO);

    /**
     * 保存销售预估明细
     *
     * @param estimateApprovalDetailPO
     */
    void saveEstimateApprovalDetail(EstimateApprovalDetailPO estimateApprovalDetailPO);

    /**
     * 冗余预估金额
     *
     * @param approvalId
     * @param totalPrice
     * @param zero
     */
    void savePrice(Long approvalId, BigDecimal totalPrice, BigDecimal zero);

    /**
     * 查询审核列表
     *
     * @param page
     * @param estimateApprovalSearchRequest
     * @return
     */
    List<EstimateApprovalDTO> selectApprovalList(Page<EstimateApprovalVO> page, EstimateApprovalSearchRequest estimateApprovalSearchRequest);

    /**
     * 根据审核ID获取详情
     *
     * @param approvalId
     * @return
     */
    EstimateApprovalPO selectApprovalById(Long approvalId);

    /**
     * 根据审核ID获取审核状态
     *
     * @param approvalId
     * @return
     */
    String selectProcessStatus(Long approvalId);

    /**
     * 获取可提报物料
     *
     * @param approvalId
     * @return
     */
    List<EstimateApprovalItemDTO> selectCanSubmitSku(Long approvalId);

    /**
     * 获取已提报的sku信息
     *
     * @param approvalId
     * @return
     */
    List<EstimateApprovalItemDTO> selectSubmitSku(Long approvalId);


    /**
     * 根据scheduleId获取可提报物料
     *
     * @param estimateSubmitSkuRequest
     * @return
     */
    List<EstimateApprovalItemDTO> selectSubmitSkuBySchedule(EstimateSubmitSkuRequest estimateSubmitSkuRequest);

    /**
     * 从大数据获取实际箱数
     *
     * @param organizationId
     * @param month
     * @param skuList
     * @return
     */
    List<EstimateActualInfoDTO> selectEstimateActualInfo(String organizationId, String month, List<String> skuList);

    /**
     * 获取已提报的sku信息
     *
     * @param approvalId
     * @return
     */
    List<EstimateApprovalDetailPO> selectApprovalDetailByApprovalId(Long approvalId);

    /**
     * 保存申请明细
     *
     * @param e
     */
    void updateApprovalDetail(EstimateApprovalDetailPO e);

    /**
     * 保存申请明细
     *
     * @param estimateApprovalDetailPO
     */
    void saveApprovalDetail(EstimateApprovalDetailPO estimateApprovalDetailPO);

    /**
     * 获取销售预估记录
     *
     * @param month
     * @param organizationId
     * @return
     */
    List<EstimateApprovalDetailHistoryPO> selectHistory(String month,Long shipPeriod, String organizationId);

    /**
     * 修改历史
     *
     * @param e
     */
    void updateApprovalDetailHistory(EstimateApprovalDetailHistoryPO e);

    /**
     * 保存历史
     *
     * @param estimateApprovalDetailHistoryPO
     */
    void saveApprovalDetailHistory(EstimateApprovalDetailHistoryPO estimateApprovalDetailHistoryPO);

    /**
     * 查询提报
     *
     * @param estimateSubmitSearchRequest
     * @return
     */
    List<EstimateSubmitDTO> selectEstimateSubmit(EstimateSubmitSearchRequest estimateSubmitSearchRequest);

    /**
     * 根据排期id及组织获取提报记录
     *
     * @param organizationId
     * @param scheduleId
     * @return
     */
    EstimateApprovalPO selectApprovalByScheduleId(String organizationId, Long scheduleId);

    /**
     * 获取上次提报的金额
     *
     * @param organizationId
     * @param month
     * @return
     */
    List<EstimateApprovalDetailHistoryPO> selectLastEstimateFromHistory(String organizationId, String month,Long shipPeriodId);

    /**
     * 获取下级提报
     *
     * @param organizationId
     * @param organizationType
     * @param month
     * @return
     */
    List<EstimateSkuDTO> selectLowerEstimate(String organizationId, String organizationType, String month);

    void updateEstimateApproval(EstimateApprovalPO estimateApprovalPO);

    /**
     * 查询销售预估汇总
     *
     * @param page
     * @param estimateSearchRequest
     * @return
     */
    List<EstimateSummaryDTO> selectSummary(Page<EstimateSummaryVO> page, EstimateSearchRequest estimateSearchRequest);

    /**
     * 根据查询条件获取上月/上上月实际销售箱数
     *
     * @param list
     * @return
     */
    List<EstimateActualInfoDTO> selectSummaryActualInfo(List<EstimateSummaryDTO> list);

    /**
     * 查询销售预估明细
     *
     *
     * @param page
     * @param estimateSearchRequest
     * @return
     */
    List<EstimateDetailDTO> selectDetail(Page<EstimateDetailVO> page, EstimateSearchRequest estimateSearchRequest);

    /**
     * 销售预估合计明细
     *
     * @param estimateSearchRequest
     * @return
     */
    EstimateApprovalSummaryDTO selectDetailSummary(EstimateSearchRequest estimateSearchRequest);
    /**
     * 根据组织获取仓库名字
     *
     * @param organizationId
     * @return
     */
    String getStoreName(String organizationId);

    /**
     * 查询审核数量
     *
     * @param orgCodes
     * @param flowCode
     * @param roleIds
     * @return
     */
    int getEstimateProcessCount(List<String> orgCodes,String orgType, String flowCode, List<Integer> roleIds);

    /**
     * 获取提报数量
     *
     * @param orgCodes
     * @param orgType
     * @param flowCode
     * @return
     */
    int getSubmitCount(List<String> orgCodes, String orgType);

    /**
     * 根据预估单号获取预估单
     *
     * @param saleEstimateNo
     * @return
     */
    EstimateApprovalPO selectEstimateApprovalByEstimateNo(String saleEstimateNo);

    /**
     * MOQ查询
     *
     * @param page
     * @param moqSearchRequest
     * @return
     */
    List<EstimateMOQVO> selectMOQ(@Param("page") IPage<EstimateMOQVO> page, @Param("request") MOQSearchRequest moqSearchRequest);

    /**
     * 获取MOQ sku明细
     *
     * @param yearMonth
     * @param shipPeriodId
     * @param sku
     * @return
     */
    List<MOQSkuVO> selectMOQDetail(String yearMonth, Long shipPeriodId, String sku);

    /**
     * 获取sku库存信息
     *
     * @param skuList
     * @param yearMonth
     * @return
     */
    List<SkuInventory> selectSkuInventory(String storeName, List<String> skuList, String yearMonth,Integer businessGroup);

    /**
     * 获取已提报的MOQ信息
     *
     * @param skuList
     * @param theYearMonth
     * @return
     */
    List<MOQ> selectCurrentMOQ(List<String> skuList, String theYearMonth,List<String>excludeOrgCodes);

    /**
     * 获取预定单数量
     *
     * @param organizationId
     * @param organizationType
     * @param skuList
     * @param theYearMonth
     * @return
     */
    List<SkuAdvancedOrder> selectAdvancedOrderBox(String organizationId, String organizationType, List<String> skuList, String theYearMonth);

    /**
     * 获取前2个月的销售预估数据
     */
    List<EstimateHistoryDTO>  selectHistoryBySku(String theYearMonth, String organizationId, List<String> skuList);

    /**
     * 更具结果获取上月数据
     *
     * @param list
     * @return
     */
    List<EstimateSummaryDTO> selectHistoryByResult(List<EstimateSummaryDTO> list);

    /**
     * 根据结果获取当前MOQ
     *
     * @param list
     * @return
     */
    List<MOQ> selectMOQByResult(List<EstimateSummaryDTO> list);

    /**
     * 根据查询结果获取预定单数
     *
     * @param list
     * @return
     */
    List<SkuAdvancedOrder> selectAdvancedOrderBoxByResult(List<EstimateSummaryDTO> list);

    /**
     * 获取组织当月提报数据
     *
     * @param month
     * @param organizationId
     * @return
     */
    List<EstimateHistoryDTO> selectHistoryByOrgCode(String month, String organizationId);

    List<EstimateApprovalDetailPO> selectLastSubmitVO(String organizationId, Long scheduleId);

    List<SkuAdvancedOrder> selectZBAdvancedOrder(String zbOrgCode, String yearMonth, List<String> skuList);

    EstimateAdjustPO selectCurrentMoqAudit(String yearMonth, Long shipPeriodId, String sku);

    void updateEstimateAdjustPO(EstimateAdjustPO estimateAdjustPO);

    void saveEstimateAdjust(EstimateAdjustPO estimateAdjustPO);

    void saveAdjustDetail(EstimateAdjustDetailPO estimateAdjustDetailPO);

    void updateDetailStatus(Long adjustId, int status,EstimateAdjustDetailPO estimateAdjustDetailPO);

    List<EstimateApprovalDetailHistoryPO> selectHistoryByCondition(String yearMonth, Long shipPeriodId, List<String> orgCodes);

    List<EstimateAdjustDetailPO> selectEstimateAdjustDetailByAdjustId(Long adjustId);

    EstimateAdjustPO selectAdjust(String yearMonth, Long shipPeriodId, String sku, int businessGroup);

    void updateEstimateAdjustDetail(EstimateAdjustDetailPO e);

    /**
     * 获取大区调整
     *
     * @param estimateAdjustSearchRequest
     * @return
     */
    List<EstimateAdjustVO> selectAreaAdjust(IPage<EstimateAdjustVO> page,EstimateAdjustSearchRequest estimateAdjustSearchRequest);


    EstimateAdjustPO selectAdjustById(Long adjustId);

    /**
     * 查询调整单信息
     *
     * @param adjustDetailSearchRequest
     * @return
     */
    List<OrgAdjustVO> selectAdjustDetail(AdjustDetailSearchRequest adjustDetailSearchRequest);

    EstimateAdjustDetailPO selectAdjustDetailPO(Long adjustId, String organizationId, Integer type);

    EstimateApprovalSummaryDTO selectApprovalSummary(EstimateApprovalSearchRequest estimateApprovalSearchRequest);

    /**
     * 查询销售预估管控
     *
     * @param theYearMonth
     * @param organizationId
     * @return
     */
    List<EstimateControlPO> selectEstimateControl(String theYearMonth, String organizationId);

    /**
     * 查询销售预估管控明细
     *
     * @param controlIds
     * @return
     */
    List<EstimateControlDetailPO> selectEstimateControlDetail(List<Long> controlIds);

    /**
     * 获取已提报额度
     *
     * @param theYearMonth
     * @param organizationId
     * @param skuList
     * @return
     */
    List<EstimateApprovalDetailDO> selectSubmitPrice(String theYearMonth, String organizationId, List<String> skuList, Long approvalId);

    /**
     * 查询已审核通过的额度
     *
     * @param theYearMonth
     * @param organizationId
     * @param skuList
     * @return
     */
    List<EstimateApprovalDetailDO> selectFinishedBySku(String theYearMonth, String organizationId, List<String> skuList);

    /**
     * 获取调整中的数量
     *
     */
    List<EstimateDetail>  selectAdjustDetailBySku(String theYearMonth, String organizationId, List<String> skuList, Long adjustId);
}
