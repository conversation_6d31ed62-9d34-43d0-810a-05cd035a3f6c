package com.wantwant.sfa.backend.domain.estimate.repository.facade;


import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrgSearchDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrganizationDO;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateAdjustDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.*;
import com.wantwant.sfa.backend.estimate.vo.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午4:09
 */
public interface IEstimateSkuRepository {

    /**
     * 检查sku组名是否重复
     *
     * @param groupName
     * @param businessGroup
     * @param groupId
     * @return
     */
    boolean checkGroupNameExist(String groupName,Integer businessGroup, Long groupId);

    /**
     * 根据sku获取销售预估sku信息
     *
     * @param sku
     * @return
     */
    EstimateSkuPO selectSkuByCode(String sku,Integer businessGroup);

    /**
     * 修改销售预估sku
     *
     * @param estimateSkuPO
     */
    void updateEstimateSku(EstimateSkuPO estimateSkuPO);

    /**
     * 保存sku
     *
     * @param estimateSkuPO
     */
    void insertEstimateSku(EstimateSkuPO estimateSkuPO);

    /**
     * 根据ID获取物料组ID
     *
     * @param groupId
     */
    EstimateSkuGroupPO selectSkuGroupById(Long groupId);

    /**
     * 修改物料组信息
     *
     * @param estimateSkuGroupPO
     */
    void updateEstimateSkuGroup(EstimateSkuGroupPO estimateSkuGroupPO);

    /**
     * 插入物料组信息
     *
     * @param estimateSkuGroupPO
     */
    void insertEstimateSkuGroup(EstimateSkuGroupPO estimateSkuGroupPO);

    /**
     * 根据groupId获取已配置的sku表信息
     *
     * @param groupId
     * @return
     */
    List<EstimateSkuOrganizationRelationPO> selectSkuRelationByGroupId(Long groupId);

    /**
     * 保存分公司与组织的关系
     *
     * @param initEstimateSkuOrganization
     */
    void saveOrganizationRelation(EstimateSkuOrganizationRelationPO initEstimateSkuOrganization);

    /**
     * 根据groupId删除原记录
     *
     * @param groupId
     * @param processUserDO
     */
    void deleteAllOrganizationRelationByGroupId(Long groupId, ProcessUserDO processUserDO);

    /**
     * 根据产品组获取物料组信息
     *
     * @param businessGroup
     * @return
     */
    List<EstimateSkuGroupVO> selectEstimateGroupByBusinessGroup(int businessGroup);

    /**
     * 根据groupId获取sku信息
     *
     * @param groupId
     * @return
     */
    List<EstimateSkuVO> selectSkuDetailByGroupId(Long groupId);

    /**
     * 修改排期表
     *
     * @param estimateSchedulePO
     */
    void updateEstimateSchedule(EstimateSchedulePO estimateSchedulePO);

    /**
     * 插入排期表
     *
     * @param estimateSchedulePO
     */
    void insertEstimateSchedule(EstimateSchedulePO estimateSchedulePO);

    /**
     * 删除排期组织
     *
     * @param scheduleId
     */
    void clearScheduleOrganization(Long scheduleId, ProcessUserDO processUserDO);

    /**
     * 保存排期组织
     *
     * @param list
     */
    void saveEstimateOrganizationList(List<EstimateScheduleOrganizationRelationPO> list);

    /**
     * 检查是否有冲突
     *
     * @param startDate
     * @param endDate
     * @param scheduleId
     * @param estimateOrganizationDOList
     * @return
     */
    boolean checkScheduleConflict(LocalDate startDate, LocalDate endDate, String yearMonth, Long scheduleId, Integer businessGroup,Long shipPeriodId, List<EstimateOrganizationDO> estimateOrganizationDOList);

    /**
     * 根据货需月份获取排期
     *
     * @param yearMonth
     * @param businessGroup
     * @return
     */
    List<EstimateScheduleVO> selectSchedule(String yearMonth, Integer businessGroup);

    /**
     * 获取物料组
     *
     * @param businessGroup
     * @return
     */
    List<EstimateSkuGroupPO> selectSkuGroupByBusinessGroupId(int businessGroup);

    /**
     * 获取排期明细
     *
     * @param scheduleId
     * @return
     */
    EstimateScheduleDetailVO selectScheduleDetail(Long scheduleId);

    /**
     * 查询可提报组织
     *
     * @param estimateOrgSearchDO
     * @return
     */
    List<EstimateOrganizationVO> selectEstimateOrg(EstimateOrgSearchDO estimateOrgSearchDO);

    /**
     * 匹配排期表
     *
     * @param currentDate
     * @param month
     * @param type
     * @return
     */
    EstimateSchedulePO matchSchedule(LocalDate currentDate, String month, Integer type,Integer businessGroup,Integer shipPeriodId);

    /**
     * 更具sku获取盘价
     *
     * @param sku
     * @return
     */
    EstimateSkuPO selectPriceBySku(String sku,Integer businessGroup);

    EstimateSchedulePO selectScheduleById(Long scheduleId);

    List<StoreVO> getStore(int businessGroup);

    List<SkuVO> getSku(Integer businessGroup);

    EstimateShipPeriodPO selectShipPeriodById(Long id);

    void updateShipPeriod(EstimateShipPeriodPO estimateShipPeriodPO);

    List<EstimateShipPeriodPO> selectShipPeriod(Integer status);

    /**
     * 根基排期ID获取货需期别ID
     *
     * @param scheduleId
     * @return
     */
    Long selectShipPeriodBySchedule(Long scheduleId);

    /**
     * 获取调整的数据信息
     *
     * @param yearMonth
     * @param shipPeriodId
     * @param sku
     * @return
     */
    List<EstimateAdjustDTO>  selectAdjust(String yearMonth, Long shipPeriodId, String sku);


    /**
     * 获取集团MOQ
     *
     * @param sku
     * @param businessGroup
     * @return
     */
    BigDecimal getMOQ(String sku, int businessGroup);

    EstimateSkuPO getSkuByCode(String sku, int businessGroup);
}
