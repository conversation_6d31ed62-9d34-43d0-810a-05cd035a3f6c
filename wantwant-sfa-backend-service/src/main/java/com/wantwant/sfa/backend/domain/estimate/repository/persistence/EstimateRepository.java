package com.wantwant.sfa.backend.domain.estimate.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalDetailDO;
import com.wantwant.sfa.backend.domain.estimate.DO.value.EstimateDetail;
import com.wantwant.sfa.backend.domain.estimate.mapper.*;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.*;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.*;
import com.wantwant.sfa.backend.estimate.request.*;
import com.wantwant.sfa.backend.estimate.vo.*;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午2:06
 */
@Repository
public class EstimateRepository implements IEstimateRepository {

    @Resource
    private EstimateApprovalMapper estimateApprovalMapper;
    @Resource
    private EstimateApprovalDetailMapper estimateApprovalDetailMapper;
    @Resource
    private EstimateBigTableMapper estimateBigTableMapper;
    @Resource
    private EstimateApprovalDetailHistoryMapper estimateApprovalDetailHistoryMapper;
    @Resource
    private EstimateAdjustMapper estimateAdjustMapper;
    @Resource
    private EstimateAdjustDetailMapper estimateAdjustDetailMapper;
    @Resource
    private EstimateControlMapper estimateControlMapper;
    @Resource
    private EstimateControlDetailMapper estimateControlDetailMapper;


    @Override
    public void saveEstimateApproval(EstimateApprovalPO estimateApprovalPO) {
        estimateApprovalMapper.insert(estimateApprovalPO);
    }

    @Override
    public void saveEstimateApprovalDetail(EstimateApprovalDetailPO estimateApprovalDetailPO) {
        estimateApprovalDetailMapper.insert(estimateApprovalDetailPO);
    }

    @Override
    public void savePrice(Long approvalId, BigDecimal estimatePrice, BigDecimal auditPrice) {
        EstimateApprovalPO estimateApprovalPO = estimateApprovalMapper.selectById(approvalId);


        estimateApprovalPO.setEstimatePrice(estimatePrice);
        estimateApprovalPO.setAuditPrice(auditPrice);

        estimateApprovalMapper.updateById(estimateApprovalPO);
    }

    @Override
    public List<EstimateApprovalDTO> selectApprovalList(Page<EstimateApprovalVO> page, EstimateApprovalSearchRequest estimateApprovalSearchRequest) {

        List<EstimateApprovalDTO> list = estimateApprovalMapper.selectApprovalList(page,estimateApprovalSearchRequest);

        return list;
    }

    @Override
    public EstimateApprovalPO selectApprovalById(Long approvalId) {
        return estimateApprovalMapper.selectById(approvalId);
    }

    @Override
    public String selectProcessStatus(Long approvalId) {
        return estimateApprovalMapper.selectProcessStatus(approvalId);
    }

    @Override
    public List<EstimateApprovalItemDTO> selectCanSubmitSku(Long approvalId) {
        return estimateApprovalMapper.selectCanSubmitSku(approvalId);
    }

    @Override
    public List<EstimateApprovalItemDTO> selectSubmitSku(Long approvalId) {
        return estimateApprovalMapper.selectSubmitSku(approvalId);
    }


    @Override
    public List<EstimateApprovalItemDTO> selectSubmitSkuBySchedule(EstimateSubmitSkuRequest estimateSubmitSkuRequest) {
        return estimateApprovalMapper.selectSubmitSkuBySchedule(estimateSubmitSkuRequest);
    }



    @Override
    public List<EstimateActualInfoDTO> selectEstimateActualInfo(String organizationId, String month, List<String> skuList) {
        return estimateBigTableMapper.selectEstimateActualInfo(organizationId,month,skuList);
    }

    @Override
    public List<EstimateApprovalDetailPO> selectApprovalDetailByApprovalId(Long approvalId) {
        return estimateApprovalDetailMapper.selectList(new LambdaQueryWrapper<EstimateApprovalDetailPO>().eq(EstimateApprovalDetailPO::getApprovalId,approvalId).eq(EstimateApprovalDetailPO::getDeleteFlag,0));
    }

    @Override
    public void updateApprovalDetail(EstimateApprovalDetailPO e) {
        estimateApprovalDetailMapper.updateById(e);
    }

    @Override
    public void saveApprovalDetail(EstimateApprovalDetailPO estimateApprovalDetailPO) {
        estimateApprovalDetailMapper.insert(estimateApprovalDetailPO);
    }

    @Override
    public List<EstimateApprovalDetailHistoryPO> selectHistory(String month,Long shipPeriod, String organizationId) {

        LambdaQueryWrapper<EstimateApprovalDetailHistoryPO> query = new LambdaQueryWrapper<EstimateApprovalDetailHistoryPO>()
                .eq(EstimateApprovalDetailHistoryPO::getTheYearMonth, month).eq(EstimateApprovalDetailHistoryPO::getOrganizationId, organizationId)
                .eq(EstimateApprovalDetailHistoryPO::getDeleteFlag, 0);
        if(Objects.nonNull(shipPeriod)){
            query.eq(EstimateApprovalDetailHistoryPO::getShipPeriodId,shipPeriod);
        }

        List<EstimateApprovalDetailHistoryPO> estimateApprovalDetailHistoryPOS = estimateApprovalDetailHistoryMapper.selectList(query);

        return estimateApprovalDetailHistoryPOS;
    }

    @Override
    public void updateApprovalDetailHistory(EstimateApprovalDetailHistoryPO e) {
        estimateApprovalDetailHistoryMapper.updateById(e);
    }

    @Override
    public void saveApprovalDetailHistory(EstimateApprovalDetailHistoryPO estimateApprovalDetailHistoryPO) {
        estimateApprovalDetailHistoryMapper.insert(estimateApprovalDetailHistoryPO);
    }

    @Override
    public List<EstimateSubmitDTO> selectEstimateSubmit(EstimateSubmitSearchRequest estimateSubmitSearchRequest) {
        return estimateApprovalMapper.selectEstimateSubmit(estimateSubmitSearchRequest);
    }

    @Override
    public EstimateApprovalPO selectApprovalByScheduleId(String organizationId, Long scheduleId) {
        return estimateApprovalMapper.selectOne(new LambdaQueryWrapper<EstimateApprovalPO>().eq(EstimateApprovalPO::getOrganizationId,organizationId).eq(EstimateApprovalPO::getScheduleId,scheduleId).eq(EstimateApprovalPO::getDeleteFlag,0).last("limit 1"));
    }

    @Override
    public List<EstimateApprovalDetailHistoryPO> selectLastEstimateFromHistory(String organizationId, String month,Long shipPeriodId) {
        List<EstimateApprovalDetailHistoryPO> estimateApprovalDetailHistoryPOS = estimateApprovalDetailHistoryMapper.selectList(new LambdaQueryWrapper<EstimateApprovalDetailHistoryPO>()
                .eq(EstimateApprovalDetailHistoryPO::getOrganizationId, organizationId).eq(EstimateApprovalDetailHistoryPO::getTheYearMonth, month)
                .eq(EstimateApprovalDetailHistoryPO::getShipPeriodId,shipPeriodId)
                .eq(EstimateApprovalDetailHistoryPO::getDeleteFlag, 0));

        return estimateApprovalDetailHistoryPOS;
    }

    @Override
    public List<EstimateSkuDTO> selectLowerEstimate(String organizationId, String organizationType, String month) {
        return estimateApprovalDetailHistoryMapper.selectLowerEstimate(organizationId,organizationType,month);
    }

    @Override
    public void updateEstimateApproval(EstimateApprovalPO estimateApprovalPO) {
        estimateApprovalMapper.updateById(estimateApprovalPO);
    }

    @Override
    public List<EstimateSummaryDTO> selectSummary(Page<EstimateSummaryVO> page, EstimateSearchRequest estimateSearchRequest) {
        List<EstimateSummaryDTO> list = estimateApprovalDetailHistoryMapper.selectSummary(page,estimateSearchRequest);
        return list;
    }

    @Override
    public List<EstimateActualInfoDTO> selectSummaryActualInfo(List<EstimateSummaryDTO> list) {
        return estimateBigTableMapper.selectSummaryActualInfo(list);
    }

    @Override
    public List<EstimateDetailDTO> selectDetail(Page<EstimateDetailVO> page, EstimateSearchRequest estimateSearchRequest) {
        return estimateApprovalMapper.selectDetail(page,estimateSearchRequest);
    }

    @Override
    public EstimateApprovalSummaryDTO selectDetailSummary(EstimateSearchRequest estimateSearchRequest) {
        return estimateApprovalMapper.selectDetailSummary(estimateSearchRequest);
    }

    @Override
    public String getStoreName(String organizationId) {
        return estimateApprovalMapper.getStoreName(organizationId);
    }

    @Override
    public int getEstimateProcessCount(List<String> orgCodes,String orgType, String flowCode, List<Integer> roleIds) {
        return estimateApprovalMapper.getEstimateProcessCount(orgCodes,orgType,flowCode,roleIds);
    }

    @Override
    public int getSubmitCount(List<String> orgCodes, String orgType) {
        return estimateApprovalMapper.getSubmitCount(orgCodes,orgType);
    }

    @Override
    public EstimateApprovalPO selectEstimateApprovalByEstimateNo(String saleEstimateNo) {

        EstimateApprovalPO estimateApprovalPO = estimateApprovalMapper.selectOne(new LambdaQueryWrapper<EstimateApprovalPO>().eq(EstimateApprovalPO::getSaleEstimateNo, saleEstimateNo).eq(EstimateApprovalPO::getDeleteFlag, 0).last("limit 1"));
        return estimateApprovalPO;
    }

    @Override
    public List<EstimateMOQVO> selectMOQ(IPage<EstimateMOQVO> page, MOQSearchRequest moqSearchRequest) {
        return estimateApprovalMapper.selectMOQ(page,moqSearchRequest);
    }

    @Override
    public List<MOQSkuVO> selectMOQDetail(String yearMonth, Long shipPeriodId, String sku) {
        return estimateApprovalMapper.selectMOQDetail(yearMonth,shipPeriodId,sku);
    }

    @Override
    public List<SkuInventory> selectSkuInventory(String storeName,List<String> skuList, String yearMonth,Integer businessGroup) {
        return estimateBigTableMapper.selectSkuInventory(storeName,skuList,yearMonth,businessGroup);
    }

    @Override
    public List<MOQ> selectCurrentMOQ(List<String> skuList, String theYearMonth,List<String>excludeOrgCodes) {
        return estimateApprovalDetailHistoryMapper.selectCurrentMOQ(skuList,theYearMonth,excludeOrgCodes);
    }

    @Override
    public List<SkuAdvancedOrder> selectAdvancedOrderBox(String organizationId, String organizationType, List<String> skuList, String theYearMonth) {
        return estimateBigTableMapper.selectAdvancedOrderBox(organizationId,organizationType,skuList,theYearMonth);
    }

    @Override
    public List<EstimateHistoryDTO> selectHistoryBySku(String theYearMonth, String organizationId, List<String> skuList) {
        return estimateApprovalMapper.selectHistoryBySku(theYearMonth,organizationId,skuList);
    }

    @Override
    public List<EstimateSummaryDTO> selectHistoryByResult(List<EstimateSummaryDTO> list) {
        return estimateApprovalMapper.selectHistoryByResult(list);
    }

    @Override
    public List<MOQ> selectMOQByResult(List<EstimateSummaryDTO> list) {
        return estimateApprovalMapper.selectMOQByResult(list);
    }

    @Override
    public List<SkuAdvancedOrder> selectAdvancedOrderBoxByResult(List<EstimateSummaryDTO> list) {
        return estimateBigTableMapper.selectAdvancedOrderBoxByResult(list);
    }

    @Override
    public List<EstimateHistoryDTO> selectHistoryByOrgCode(String month, String organizationId) {
        return estimateApprovalMapper.selectHistoryByOrgCode(month,organizationId);
    }

    @Override
    public List<EstimateApprovalDetailPO> selectLastSubmitVO(String organizationId, Long scheduleId) {
        return estimateApprovalMapper.selectLastSubmitPO(organizationId,scheduleId);
    }

    @Override
    public List<SkuAdvancedOrder> selectZBAdvancedOrder(String zbOrgCode,String yearMonth, List<String> skuList) {
        return estimateBigTableMapper.selectZBAdvancedOrder(zbOrgCode,yearMonth,skuList);
    }

    @Override
    public EstimateAdjustPO selectCurrentMoqAudit(String yearMonth, Long shipPeriodId, String sku) {
        return estimateAdjustMapper.selectOne(new LambdaQueryWrapper<EstimateAdjustPO>()
                .eq(EstimateAdjustPO::getMonth,yearMonth).eq(EstimateAdjustPO::getShipPeriodId,shipPeriodId)
                .eq(EstimateAdjustPO::getIsCurrent,1)
                .eq(EstimateAdjustPO::getSku,sku).eq(EstimateAdjustPO::getDeleteFlag,0).last("limit 1"));
    }

    @Override
    public void updateEstimateAdjustPO(EstimateAdjustPO estimateAdjustPO) {
        estimateAdjustMapper.updateById(estimateAdjustPO);
    }

    @Override
    public void saveEstimateAdjust(EstimateAdjustPO estimateAdjustPO) {
        estimateAdjustMapper.insert(estimateAdjustPO);
    }

    @Override
    public void saveAdjustDetail(EstimateAdjustDetailPO estimateAdjustDetailPO) {
        estimateAdjustDetailMapper.insert(estimateAdjustDetailPO);
    }

    @Override
    public void updateDetailStatus(Long adjustId, int status,EstimateAdjustDetailPO updateStatus) {

        estimateAdjustDetailMapper.update(updateStatus,new LambdaQueryWrapper<EstimateAdjustDetailPO>()
                .eq(EstimateAdjustDetailPO::getAdjustId,adjustId).eq(EstimateAdjustDetailPO::getDeleteFlag,0)
                .ne(EstimateAdjustDetailPO::getStatus,status)
        );
    }

    @Override
    public List<EstimateApprovalDetailHistoryPO> selectHistoryByCondition(String yearMonth, Long shipPeriodId, List<String> orgCodes) {
        return estimateApprovalDetailHistoryMapper.selectList(new LambdaQueryWrapper<EstimateApprovalDetailHistoryPO>().eq(EstimateApprovalDetailHistoryPO::getTheYearMonth,yearMonth)
            .eq(EstimateApprovalDetailHistoryPO::getShipPeriodId,shipPeriodId).in(EstimateApprovalDetailHistoryPO::getOrganizationId,orgCodes).eq(EstimateApprovalDetailHistoryPO::getDeleteFlag,0)
        );
    }

    @Override
    public List<EstimateAdjustDetailPO> selectEstimateAdjustDetailByAdjustId(Long adjustId) {
        return estimateAdjustDetailMapper.selectList(new LambdaQueryWrapper<EstimateAdjustDetailPO>().eq(EstimateAdjustDetailPO::getAdjustId,adjustId).eq(EstimateAdjustDetailPO::getDeleteFlag,0));
    }

    @Override
    public EstimateAdjustPO selectAdjust(String yearMonth, Long shipPeriodId, String sku, int businessGroup) {
        return estimateAdjustMapper.selectOne(new LambdaQueryWrapper<EstimateAdjustPO>().eq(EstimateAdjustPO::getMonth,yearMonth)
            .eq(EstimateAdjustPO::getShipPeriodId,shipPeriodId).eq(EstimateAdjustPO::getSku,sku)
            .eq(EstimateAdjustPO::getIsCurrent,1).eq(EstimateAdjustPO::getDeleteFlag,0)
                .last("limit 1")
        );
    }

    @Override
    public void updateEstimateAdjustDetail(EstimateAdjustDetailPO e) {
        estimateAdjustDetailMapper.updateById(e);
    }

    @Override
    public List<EstimateAdjustVO> selectAreaAdjust(IPage<EstimateAdjustVO> page , EstimateAdjustSearchRequest estimateAdjustSearchRequest) {
        return estimateAdjustDetailMapper.selectAreaAdjust(page,estimateAdjustSearchRequest);
    }

    @Override
    public EstimateAdjustPO selectAdjustById(Long adjustId) {
        return estimateAdjustMapper.selectById(adjustId);
    }

    @Override
    public List<OrgAdjustVO> selectAdjustDetail(AdjustDetailSearchRequest adjustDetailSearchRequest) {
        return estimateAdjustDetailMapper.selectAdjustDetail(adjustDetailSearchRequest);
    }

    @Override
    public EstimateAdjustDetailPO selectAdjustDetailPO(Long adjustId, String organizationId, Integer type) {
        return estimateAdjustDetailMapper.selectOne(new LambdaQueryWrapper<EstimateAdjustDetailPO>()
                .eq(EstimateAdjustDetailPO::getAdjustId,adjustId).eq(EstimateAdjustDetailPO::getOrganizationId,organizationId).eq(EstimateAdjustDetailPO::getType,type).eq(EstimateAdjustDetailPO::getDeleteFlag,0).last("limit 1"));
    }

    @Override
    public EstimateApprovalSummaryDTO selectApprovalSummary(EstimateApprovalSearchRequest estimateApprovalSearchRequest) {
        return estimateApprovalMapper.selectApprovalSummary(estimateApprovalSearchRequest);
    }

    @Override
    public List<EstimateControlPO> selectEstimateControl(String theYearMonth, String organizationId) {

        return estimateControlMapper.selectList(new LambdaQueryWrapper<EstimateControlPO>()
                .eq(EstimateControlPO::getCompanyCode,organizationId)
                .le(EstimateControlPO::getStartDate,theYearMonth)
                .ge(EstimateControlPO::getEndDate,theYearMonth)
                .eq(EstimateControlPO::getDeleteFlag,0)
        );

    }

    @Override
    public List<EstimateControlDetailPO> selectEstimateControlDetail(List<Long> controlIds) {
        return estimateControlDetailMapper.selectList(new LambdaQueryWrapper<EstimateControlDetailPO>().in(EstimateControlDetailPO::getControlId,controlIds).eq(EstimateControlDetailPO::getDeleteFlag,0));
    }

    @Override
    public List<EstimateApprovalDetailDO> selectSubmitPrice(String theYearMonth, String organizationId, List<String> skuList, Long approvalId) {
        return estimateApprovalMapper.selectSubmitPrice(theYearMonth,organizationId,skuList,approvalId);
    }

    @Override
    public List<EstimateApprovalDetailDO> selectFinishedBySku(String theYearMonth, String organizationId, List<String> skuList) {
        return estimateApprovalMapper.selectFinishedBySku(theYearMonth,organizationId,skuList);
    }

    @Override
    public List<EstimateDetail> selectAdjustDetailBySku(String theYearMonth, String organizationId, List<String> skuList, Long adjustId) {
        return estimateAdjustDetailMapper.selectAdjustDetailBySku(theYearMonth,organizationId,skuList,adjustId);
    }

}
