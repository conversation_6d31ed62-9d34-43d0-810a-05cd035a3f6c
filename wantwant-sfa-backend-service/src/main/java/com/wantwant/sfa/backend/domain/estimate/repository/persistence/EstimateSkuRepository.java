package com.wantwant.sfa.backend.domain.estimate.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrgSearchDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrganizationDO;
import com.wantwant.sfa.backend.domain.estimate.mapper.*;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateAdjustDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateSkuRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.*;
import com.wantwant.sfa.backend.estimate.vo.*;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午4:09
 */
@Repository
public class EstimateSkuRepository implements IEstimateSkuRepository {
    @Resource
    private EstimateSkuGroupMapper estimateSkuGroupMapper;
    @Resource
    private EstimateSkuMapper estimateSkuMapper;
    @Resource
    private EstimateSkuOrganizationMapper estimateSkuOrganizationMapper;
    @Resource
    private EstimateScheduleMapper estimateScheduleMapper;
    @Resource
    private EstimateScheduleOrganizationRelationMapper estimateScheduleOrganizationRelationMapper;
    @Resource
    private EstimateShipPeriodMapper estimateShipPeriodMapper;
    @Resource
    private EstimateAdjustDetailMapper estimateAdjustDetailMapper;

    @Override
    public boolean checkGroupNameExist(String groupName,Integer businessGroup, Long groupId) {

        LambdaQueryWrapper<EstimateSkuGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EstimateSkuGroupPO::getGroupName,groupName);
        queryWrapper.eq(EstimateSkuGroupPO::getBusinessGroup,businessGroup);
        if(Objects.nonNull(groupId)){
            queryWrapper.ne(EstimateSkuGroupPO::getGroupId,groupId);
        }
        queryWrapper.last("limit 1");
        EstimateSkuGroupPO estimateSkuGroupPO = estimateSkuGroupMapper.selectOne(queryWrapper);
        return Objects.nonNull(estimateSkuGroupPO);
    }

    @Override
    public EstimateSkuPO selectSkuByCode(String sku,Integer businessGroup) {
        return estimateSkuMapper.selectOne(new LambdaQueryWrapper<EstimateSkuPO>().eq(EstimateSkuPO::getSku,sku).eq(EstimateSkuPO::getBusinessGroup,businessGroup).eq(EstimateSkuPO::getDeleteFlag,0).last("limit 1"));
    }

    @Override
    public void updateEstimateSku(EstimateSkuPO estimateSkuPO) {
        estimateSkuMapper.updateById(estimateSkuPO);
    }

    @Override
    public void insertEstimateSku(EstimateSkuPO estimateSkuPO) {
        estimateSkuMapper.insert(estimateSkuPO);
    }

    @Override
    public EstimateSkuGroupPO selectSkuGroupById(Long groupId) {
        return estimateSkuGroupMapper.selectById(groupId);
    }

    @Override
    public void updateEstimateSkuGroup(EstimateSkuGroupPO estimateSkuGroupPO) {
        estimateSkuGroupMapper.updateById(estimateSkuGroupPO);
    }

    @Override
    public void insertEstimateSkuGroup(EstimateSkuGroupPO estimateSkuGroupPO) {
        estimateSkuGroupMapper.insert(estimateSkuGroupPO);
    }

    @Override
    public List<EstimateSkuOrganizationRelationPO> selectSkuRelationByGroupId(Long groupId) {
        return estimateSkuOrganizationMapper.selectList(new LambdaQueryWrapper<EstimateSkuOrganizationRelationPO>().eq(EstimateSkuOrganizationRelationPO::getGroupId,groupId).eq(EstimateSkuOrganizationRelationPO::getDeleteFlag,0));
    }

    @Override
    public void saveOrganizationRelation(EstimateSkuOrganizationRelationPO initEstimateSkuOrganization) {
        estimateSkuOrganizationMapper.insert(initEstimateSkuOrganization);
    }

    @Override
    public void deleteAllOrganizationRelationByGroupId(Long groupId, ProcessUserDO processUserDO) {
        EstimateSkuOrganizationRelationPO deleteOperation = new EstimateSkuOrganizationRelationPO();
        deleteOperation.setDeleteFlag(1);
        deleteOperation.setUpdateUserId(processUserDO.getEmployeeId());
        deleteOperation.setUpdateUserName(processUserDO.getEmployeeName());
        estimateSkuOrganizationMapper.update(deleteOperation,new LambdaQueryWrapper<EstimateSkuOrganizationRelationPO>().eq(EstimateSkuOrganizationRelationPO::getGroupId,groupId).eq(EstimateSkuOrganizationRelationPO::getDeleteFlag,0));
    }

    @Override
    public List<EstimateSkuGroupVO> selectEstimateGroupByBusinessGroup(int businessGroup) {
        return estimateSkuGroupMapper.selectEstimateGroupByBusinessGroup(businessGroup);
    }

    @Override
    public List<EstimateSkuVO> selectSkuDetailByGroupId(Long groupId) {
        return estimateSkuMapper.selectSkuDetailByGroupId(groupId);
    }

    @Override
    public void updateEstimateSchedule(EstimateSchedulePO estimateSchedulePO) {
        estimateScheduleMapper.updateById(estimateSchedulePO);
    }

    @Override
    public void insertEstimateSchedule(EstimateSchedulePO estimateSchedulePO) {
        estimateScheduleMapper.insert(estimateSchedulePO);
    }

    @Override
    public void clearScheduleOrganization(Long scheduleId, ProcessUserDO processUserDO) {
        EstimateScheduleOrganizationRelationPO deleteOperation = new EstimateScheduleOrganizationRelationPO();
        deleteOperation.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        deleteOperation.setDeleteFlag(1);
        estimateScheduleOrganizationRelationMapper.update(deleteOperation,new LambdaQueryWrapper<EstimateScheduleOrganizationRelationPO>().eq(EstimateScheduleOrganizationRelationPO::getScheduleId,scheduleId).eq(EstimateScheduleOrganizationRelationPO::getDeleteFlag,0));
    }

    @Override
    public void saveEstimateOrganizationList(List<EstimateScheduleOrganizationRelationPO> list) {
        estimateScheduleOrganizationRelationMapper.batchInsert(list);
    }

    @Override
    public boolean checkScheduleConflict(LocalDate startDate, LocalDate endDate, String yearMonth, Long scheduleId, Integer businessGroup,Long shipPeriodId, List<EstimateOrganizationDO> estimateOrganizationDOList) {

        Long id = estimateScheduleMapper.checkScheduleConflict(startDate,endDate,yearMonth,scheduleId,businessGroup,shipPeriodId,estimateOrganizationDOList);
        if(Objects.nonNull(id)){
            return true;
        }
        return false;
    }

    @Override
    public List<EstimateScheduleVO> selectSchedule(String yearMonth, Integer businessGroup) {
        return estimateScheduleMapper.selectSchedule(yearMonth,businessGroup);
    }

    @Override
    public List<EstimateSkuGroupPO> selectSkuGroupByBusinessGroupId(int businessGroup) {
        return estimateSkuGroupMapper.selectList(new LambdaQueryWrapper<EstimateSkuGroupPO>().eq(EstimateSkuGroupPO::getBusinessGroup,businessGroup).eq(EstimateSkuGroupPO::getStatus,1).eq(EstimateSkuGroupPO::getDeleteFlag,0));
    }

    @Override
    public EstimateScheduleDetailVO selectScheduleDetail(Long scheduleId) {
        return estimateScheduleMapper.selectScheduleDetail(scheduleId);
    }

    @Override
    public List<EstimateOrganizationVO> selectEstimateOrg(EstimateOrgSearchDO estimateOrgSearchDO) {

        return estimateScheduleMapper.selectEstimateOrg(estimateOrgSearchDO);
    }

    @Override
    public EstimateSchedulePO matchSchedule(LocalDate currentDate, String month, Integer type,Integer businessGroup,Integer shipPeriodId) {
        return estimateScheduleMapper.matchSchedule(currentDate,month,type,businessGroup,shipPeriodId);
    }

    @Override
    public EstimateSkuPO selectPriceBySku(String sku,Integer businessGroup) {

        if(Objects.isNull(businessGroup)){
            return estimateSkuMapper.selectOne(new LambdaQueryWrapper<EstimateSkuPO>().eq(EstimateSkuPO::getSku, sku)
                    .eq(EstimateSkuPO::getDeleteFlag, 0)
                    .last("limit 1"));
        }

        EstimateSkuPO estimateSkuPO = estimateSkuMapper.selectOne(new LambdaQueryWrapper<EstimateSkuPO>().eq(EstimateSkuPO::getSku, sku)
                .eq(EstimateSkuPO::getDeleteFlag, 0)
                .eq(EstimateSkuPO::getBusinessGroup,businessGroup)
         .last("limit 1"));



        return estimateSkuPO;
    }

    @Override
    public EstimateSchedulePO selectScheduleById(Long scheduleId) {

        return estimateScheduleMapper.selectById(scheduleId);
    }

    @Override
    public List<StoreVO> getStore(int businessGroup) {
        return estimateSkuGroupMapper.getStore(businessGroup);
    }

    @Override
    public List<SkuVO> getSku(Integer businessGroup) {
        return estimateSkuMapper.getSku(businessGroup);
    }

    @Override
    public EstimateShipPeriodPO selectShipPeriodById(Long id) {
        return estimateShipPeriodMapper.selectById(id);
    }

    @Override
    public void updateShipPeriod(EstimateShipPeriodPO estimateShipPeriodPO) {
        estimateShipPeriodMapper.updateById(estimateShipPeriodPO);
    }

    @Override
    public List<EstimateShipPeriodPO> selectShipPeriod(Integer status) {
        LambdaQueryWrapper<EstimateShipPeriodPO> query = new LambdaQueryWrapper<>();
        if(Objects.nonNull(status)){
            query.eq(EstimateShipPeriodPO::getStatus,status);
        }
        return estimateShipPeriodMapper.selectList(query);
    }

    @Override
    public Long selectShipPeriodBySchedule(Long scheduleId) {
        return estimateScheduleMapper.selectShipPeriodBySchedule(scheduleId);
    }

    @Override
    public List<EstimateAdjustDTO> selectAdjust(String yearMonth, Long shipPeriodId, String sku) {
        return estimateAdjustDetailMapper.selectAdjust(yearMonth,shipPeriodId,sku);
    }

    @Override
    public BigDecimal getMOQ(String sku, int businessGroup) {
        return estimateSkuGroupMapper.getMOQ(sku,businessGroup);
    }

    @Override
    public EstimateSkuPO getSkuByCode(String sku, int businessGroup) {
        return estimateSkuMapper.selectOne(new LambdaQueryWrapper<EstimateSkuPO>().eq(EstimateSkuPO::getSku,sku).eq(EstimateSkuPO::getBusinessGroup,businessGroup).eq(EstimateSkuPO::getDeleteFlag,0).last("limit 1"));
    }
}
