package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估调整明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@TableName("sfa_estimate_adjust_detail")
@ApiModel(value = "SfaEstimateAdjustDetail对象", description = "销售预估调整明细")
@Data
public class EstimateAdjustDetailPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "detail_id", type = IdType.AUTO)
    private Long detailId;

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty("物料sku")
    private String sku;

    @ApiModelProperty("确认箱数")
    private Integer auditCount;

    @ApiModelProperty("状态(0.未处理 1.已处理)")
    private Integer status;

    @ApiModelProperty("提报模式(1.常规提报 2.追加提报)")
    private Integer type;

    @ApiModelProperty("销售预估调整主表ID")
    private Long adjustId;

    @ApiModelProperty("原预估数量")
    private Integer rawEstimateCount;
}
