package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估调整
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@TableName("sfa_estimate_adjust")
@ApiModel(value = "SfaEstimateAdjust对象", description = "销售预估调整")
@Data
public class EstimateAdjustPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "adjust_id", type = IdType.AUTO)
    private Long adjustId;

    @ApiModelProperty("物料sku")
    private String sku;

    @ApiModelProperty("状态(1.调整中 2.已调整 3.不生产 4.已完成)")
    private Integer status;

    @ApiModelProperty("货需月份")
    private String month;

    @ApiModelProperty("货需期别ID")
    private Long shipPeriodId;

    @ApiModelProperty("是否当前值(1.是）")
    private Integer isCurrent;



}
