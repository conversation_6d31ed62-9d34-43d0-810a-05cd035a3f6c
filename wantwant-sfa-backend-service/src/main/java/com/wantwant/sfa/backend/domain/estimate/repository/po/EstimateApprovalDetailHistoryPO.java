package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估提报结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@TableName("sfa_estimate_approval_detail_history")
@ApiModel(value = "SfaEstimateApprovalDetailHistory对象", description = "销售预估提报结果表")
@Data
public class EstimateApprovalDetailHistoryPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "history_id", type = IdType.AUTO)
    private Long historyId;

    @ApiModelProperty("组织CODE")
    private String organizationId;

    @ApiModelProperty("获需月份")
    private String theYearMonth;

    @ApiModelProperty("商品SKU")
    private String sku;

    @ApiModelProperty("常规提报数量")
    private Integer estimateQuantity;

    @ApiModelProperty("盘价")
    private BigDecimal salePrice;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("追加数量")
    private Integer appendQuantity;

    @ApiModelProperty("产品组")
    private Integer businessGroup;

    @ApiModelProperty("仓库名字")
    private String storeName;

    @ApiModelProperty("货需期别ID")
    private Long shipPeriodId;

}
