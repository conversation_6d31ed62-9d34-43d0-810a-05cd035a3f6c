package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估申请详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@TableName("sfa_estimate_approval_detail_v2")
@ApiModel(value = "SfaEstimateApprovalDetailV2对象", description = "销售预估申请详情表")
@Data
public class EstimateApprovalDetailPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "detail_id", type = IdType.AUTO)
    private Long detailId;

    @ApiModelProperty("sfa_estimate_approval_v2主键")
    private Long approvalId;

    @ApiModelProperty("商品SKU")
    private String sku;

    @ApiModelProperty("申请数量")
    private Integer estimateQuantity;

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty("确认数量")
    private Integer auditQuantity;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("盘价")
    private BigDecimal salePrice;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;
}
