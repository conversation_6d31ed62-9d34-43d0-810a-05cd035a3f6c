package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@TableName("sfa_estimate_approval_v2")
@ApiModel(value = "SfaEstimateApprovalV2对象", description = "销售预估申请表")
@Data
public class EstimateApprovalPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "approval_id", type = IdType.AUTO)
    private Long approvalId;

    @ApiModelProperty("预估单号")
    private String saleEstimateNo;

    @ApiModelProperty("获需月份")
    private String month;

    @ApiModelProperty("申请人岗位ID")
    private String applyPositionId;

    @ApiModelProperty("申请人姓名")
    private String applyUserName;

    @ApiModelProperty("是否提交(0.否 1.是 2.保存)")
    private Integer isSubmit;

    @ApiModelProperty("申请组织")
    private String organizationId;

    @ApiModelProperty("销售预估排期ID")
    private Long scheduleId;

    @ApiModelProperty("销售预估提报类型(1.常规提报 2.追加)")
    private Integer type;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人ID")
    private String updateUserId;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("流程实例ID")
    private Long instanceId;


    private BigDecimal estimatePrice;

    private BigDecimal auditPrice;
}
