package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;


/**
 * 销售预估管控明细
 */
@Data
@TableName("sfa_estimate_control_detail")
public class EstimateControlDetailPO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * sfa_estimate_control主键
     */
    private Long controlId;

    /**
     * 管控sku
     */
    private String sku;

    /**
     * 是否删除(1.是)
     */
    private Boolean deleteFlag;

    /**
     * 创建时间
     */
    private LocalDate createTime;

    /**
     * 修改时间
     */
    private LocalDate updateTime;
}