package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 销售预估管控
 */
@Data
@TableName("sfa_estimate_control")
public class EstimateControlPO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分公司ID
     */
    private String companyCode;

    /**
     * 管控名称
     */
    private String controlName;

    /**
     * 管控额度
     */
    private BigDecimal controlRatio;

    /**
     * 有效开始日期
     */
    private Date startDate;

    /**
     * 有效结束日期
     */
    private Date endDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除(1.是)
     */
    private Boolean deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}   