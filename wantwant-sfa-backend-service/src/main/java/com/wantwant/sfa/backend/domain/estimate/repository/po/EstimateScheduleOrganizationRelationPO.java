package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估排期与组织关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@TableName("sfa_estimate_schedule_organization_relation")
@ApiModel(value = "SfaEstimateScheduleOrganizationRelation对象", description = "销售预估排期与组织关系表")
@Data
public class EstimateScheduleOrganizationRelationPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "related_id", type = IdType.AUTO)
    private Long relatedId;

    @ApiModelProperty("sfa_estimate_schedule主键")
    private Long scheduleId;

    @ApiModelProperty("组织CODE")
    private String organizationId;

    @ApiModelProperty("组织类型")
    private String organizationType;
}
