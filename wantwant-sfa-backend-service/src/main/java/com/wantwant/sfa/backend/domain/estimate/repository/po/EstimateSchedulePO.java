package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估排期表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@TableName("sfa_estimate_schedule")
@ApiModel(value = "SfaEstimateSchedule对象", description = "销售预估排期表")
@Data
public class EstimateSchedulePO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "schedule_id", type = IdType.AUTO)
    private Long scheduleId;

    @ApiModelProperty("获需月份")
    private String theYearMonth;

    private Integer businessGroup;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("类型(1.常规 2.追加)")
    private Integer type;

    @ApiModelProperty("sfa_estimate_sku_group主键")
    private Long groupId;

    private Long shipPeriodId;

}
