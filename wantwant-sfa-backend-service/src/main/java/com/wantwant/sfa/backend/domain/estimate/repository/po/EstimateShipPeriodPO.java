package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估货需期别
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@TableName("sfa_estimate_ship_period")
@ApiModel(value = "SfaEstimateShipPeriod对象", description = "销售预估货需期别")
@Data
public class EstimateShipPeriodPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("批次时段")
    private String batchPeriod;

    @ApiModelProperty("到货期限")
    private String deliveryDeadline;

    @ApiModelProperty("创建人工号")
    private String createUserId;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人工号")
    private String updateUserId;

    @ApiModelProperty("修改人名称")
    private String updateUserName;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除")
    private Integer deleteFlag;

    @ApiModelProperty("状态（1.启动 0.停用）")
    private Integer status;



}
