package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估物料组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@TableName("sfa_estimate_sku_group")
@ApiModel(value = "SfaEstimateSkuGroup对象", description = "销售预估物料组")
@Data
public class EstimateSkuGroupPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;

    @ApiModelProperty("物料组名称")
    private String groupName;

    @ApiModelProperty("产品组")
    private Integer businessGroup;

    @ApiModelProperty("货需期别ID")
    private Long shipPeriodId;

    @ApiModelProperty("备注信息")
    private String remark;

    private Integer status;

}
