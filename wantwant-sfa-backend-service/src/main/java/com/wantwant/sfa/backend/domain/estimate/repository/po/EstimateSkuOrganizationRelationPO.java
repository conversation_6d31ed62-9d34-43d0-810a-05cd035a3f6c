package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估物料与分公司关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@TableName("sfa_estimate_sku_organization_relation")
@ApiModel(value = "SfaEstimateSkuOrganizationRelation对象", description = "销售预估物料与分公司关系表")
@Data
public class EstimateSkuOrganizationRelationPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "related_id", type = IdType.AUTO)
    private Long relatedId;

    @ApiModelProperty("sfa_estimate_sku主键")
    private Long skuId;

    private String sku;

    @ApiModelProperty("对应组织ID")
    private String organizationId;

    @ApiModelProperty("组织类型")
    private String organizationType;

    @ApiModelProperty("sfa_estimate_sku_group主键")
    private Long groupId;

}
