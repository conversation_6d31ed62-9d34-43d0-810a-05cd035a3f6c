package com.wantwant.sfa.backend.domain.estimate.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 销售预估sku物料表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@TableName("sfa_estimate_sku")
@ApiModel(value = "SfaEstimateSku对象", description = "销售预估sku物料表")
@Data
public class EstimateSkuPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sku_id", type = IdType.AUTO)
    private Long skuId;

    @ApiModelProperty("产品组")
    private Integer businessGroup;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("产品线ID")
    private String lineId;

    @ApiModelProperty("产品线名称")
    private String lineName;

    @ApiModelProperty("spu")
    private String spu;

    @ApiModelProperty("sku编码")
    private String sku;

    @ApiModelProperty("sku名称")
    private String skuName;

    @ApiModelProperty("规格")
    private String fullCaseSpec;

    @ApiModelProperty("口味")
    private String flavor;

    @ApiModelProperty("保质期")
    private String shelfLife;

    @ApiModelProperty("MOQ")
    private String moq;

    @ApiModelProperty("生产排期")
    private String productionStage;

    @ApiModelProperty("三阶价")
    private BigDecimal thirdOrderPrice;

    @ApiModelProperty("预计上市月份")
    private String expectListMonth;

    @ApiModelProperty("预警值 百分比形式 存小数")
    private BigDecimal warnPercent;

    @ApiModelProperty("标签")
    private String tagName;

    @ApiModelProperty("活动图片")
    private String activityImageUrl;

    @ApiModelProperty("活动描述")
    private String activityDescription;

    @ApiModelProperty("类型(1.常规品项 2.综合品项 3.集团经典品项)")
    private Integer type;

}
