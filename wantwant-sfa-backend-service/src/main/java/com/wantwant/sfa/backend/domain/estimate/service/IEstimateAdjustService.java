package com.wantwant.sfa.backend.domain.estimate.service;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustPO;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/08/下午4:57
 */
public interface IEstimateAdjustService {

    /**
     * 创建调整单
     *
     * @param yearMonth
     * @param shipPeriodId
     * @param sku
     * @param businessGroup
     * @return
     */
    EstimateAdjustPO createMoqAudit(String yearMonth, Long shipPeriodId, String sku, ProcessUserDO processUserDO);

    /**
     * 初始化子表信息
     *
     * @param adjustId
     * @param organizationId
     * @param sku
     * @param type
     */
    void initAdjustDetail(Long adjustId, String organizationId, String sku, Integer type,Integer rawEstimateCount, ProcessUserDO processUserDO);

    /**
     * 获取调整单
     *
     * @param yearMonth
     * @param shipPeriodId
     * @param sku
     * @return
     */
    EstimateAdjustPO findEstimateAdjust(String yearMonth, Long shipPeriodId, String sku);

    /**
     * 修改调整单
     *
     * @param estimateAdjustPO
     */
    void updateEstimateAdjust(EstimateAdjustPO estimateAdjustPO);

    /**
     * 设置子表状态
     *
     * @param adjustId
     * @param status
     */
    void updateDetailStatus(Long adjustId, int status,ProcessUserDO processUserDO);

    /**
     * 查询调整明细
     *
     * @param adjustId
     * @param organizationId
     * @param type
     * @return
     */
    EstimateAdjustDetailPO selectAdjustDetail(Long adjustId, String organizationId, Integer type);

    /**
     * 跟新调整明细
     *
     * @param estimateAdjustDetailPO
     */
    void updateEstimateAdjustDetail(EstimateAdjustDetailPO estimateAdjustDetailPO);

}
