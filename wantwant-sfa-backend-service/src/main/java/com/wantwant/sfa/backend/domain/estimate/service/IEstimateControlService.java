package com.wantwant.sfa.backend.domain.estimate.service;


import com.wantwant.sfa.backend.domain.estimate.DO.EstimateCheckDO;
import com.wantwant.sfa.backend.estimate.request.EstimateControlSearchRequest;
import com.wantwant.sfa.backend.estimate.vo.EstimateControlVO;

public interface IEstimateControlService {

    /**
     * 获取管控信息
     *
     * @param estimateControlSearchRequest
     * @return
     */
    EstimateControlVO selectEstimateControl(EstimateControlSearchRequest estimateControlSearchRequest);

    /**
     * 管控信息检查
     */
    void checkEstimateControl(EstimateCheckDO estimateCheckDO);
}
