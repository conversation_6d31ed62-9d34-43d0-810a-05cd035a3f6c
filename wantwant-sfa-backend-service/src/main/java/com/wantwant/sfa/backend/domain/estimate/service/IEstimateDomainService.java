package com.wantwant.sfa.backend.domain.estimate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.*;
import com.wantwant.sfa.backend.estimate.request.*;
import com.wantwant.sfa.backend.estimate.vo.*;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午1:22
 */
public interface IEstimateDomainService {

    /**
     * 合伙人
     *
     * @param ceoEstimateApplyDO
     * @return
     */
    Long ceoEstimateApply(CeoEstimateApplyDO ceoEstimateApplyDO);

    /**
     * 审核列表查询
     *
     * @param estimateApprovalSearchRequest
     * @param roleIds
     * @return
     */
    EstimateApprovalInfoVO selectApprovalList(EstimateApprovalSearchRequest estimateApprovalSearchRequest, List<Integer> roleIds);

    /**
     * 获取销售预估提报明细
     *
     * @param approvalId
     * @param mode
     * @return
     */
    EstimateApprovalDetailVO getEstimateApprovalDetail(Long approvalId, Integer mode);

    /**
     * 获取instanceId
     *
     * @param approvalId
     * @return
     */
    Long getInstanceId(Long approvalId);

    /**
     * 审核通过
     *
     * @param convertEstimateApprovalPassDO
     * @param processUserDO
     */
    void pass(EstimateApprovalPassDO convertEstimateApprovalPassDO, ProcessUserDO processUserDO);

    /**
     * 提报列表查询
     *
     * @param estimateSubmitSearchRequest
     * @param roleIds
     * @return
     */
    List<EstimateSubmitVO> selectEstimateSubmit(EstimateSubmitSearchRequest estimateSubmitSearchRequest, List<Integer> roleIds);

    /**
     * 驳回
     *
     * @param convertEstimateApprovalRejectDO
     * @param processUserDO
     */
    void reject(EstimateApprovalRejectDO convertEstimateApprovalRejectDO, ProcessUserDO processUserDO);

    /**
     * 提报
     *
     * @param estimateSubmitDO
     * @param processUserDO
     */
    void submit(EstimateSubmitDO estimateSubmitDO, ProcessUserDO processUserDO);

    /**
     * 获取可提报物料
     *
     * @param estimateSubmitSkuRequest
     * @return
     */
    EstimateApprovalDetailVO selectSubmitSku(EstimateSubmitSkuRequest estimateSubmitSkuRequest);

    /**
     * 获取仓库
     *
     * @param businessGroup
     * @return
     */
    List<StoreVO> getStore(int businessGroup);

    /**
     * 获取sku
     *
     * @param businessGroup
     * @return
     */
    List<SkuVO> getSku(Integer businessGroup);

    /**
     * 查询汇总
     *
     * @param estimateSearchRequest
     * @return
     */
    IPage<EstimateSummaryVO> selectSummary(EstimateSearchRequest estimateSearchRequest);

    /**
     * 查询明细
     *
     * @param estimateSearchRequest
     * @return
     */
    EstimateDetailInfoVO selectDetail(EstimateSearchRequest estimateSearchRequest);

    /**
     * 导出
     *
     * @param estimateSearchRequest
     */
    void exportDetail(EstimateSearchRequest estimateSearchRequest);

    /**
     * 导出
     *
     * @param estimateSearchRequest
     */
    void exportSummary(EstimateSearchRequest estimateSearchRequest);

    /**
     * 查询待审核数量
     *
     * @param orgCodes
     * @param positionTypeId
     * @param roleIds
     * @return
     */
    int getEstimateProcessCount(List<String> orgCodes, int positionTypeId, List<Integer> roleIds);

    /**
     * 获取待提交数量
     *
     * @param orgCodes
     * @param positionTypeId
     * @return
     */
    int getSubmitCount(List<String> orgCodes, int positionTypeId);

    /**
     * 取消销售预估
     *
     * @param saleEstimateNo
     */
    void cancel(String saleEstimateNo);

    /**
     * 查询MOQ
     *
     * @param moqSearchRequest
     * @return
     */
    IPage<EstimateMOQVO> selectMOQ(MOQSearchRequest moqSearchRequest);

    /**
     * 获取MOQ明细
     *
     * @param yearMonth
     * @param shipPeriodId
     * @param sku
     * @return
     */
    MOQDetailVO getMOQDetail(String yearMonth, Long shipPeriodId, String sku);

    /**
     * moq审核
     *
     * @param estimateAdjustDO
     */
    void moqAudit(EstimateAdjustDO estimateAdjustDO,ProcessUserDO processUserDO);

    /**
     * 大区物料调整
     *
     * @param adjustDO
     * @param processUserDO
     */
    void adjust(AdjustDO adjustDO, ProcessUserDO processUserDO);

    /**
     * 大区调整列表
     *
     * @param estimateAdjustSearchRequest
     * @return
     */
    IPage<EstimateAdjustVO> selectAdjust(EstimateAdjustSearchRequest estimateAdjustSearchRequest);

    /**
     * 获取销售预估调整明细
     *
     * @param adjustDetailSearchRequest
     * @return
     */
    EstimateAdjustDetailVO getEstimateAdjustDetail(AdjustDetailSearchRequest adjustDetailSearchRequest);

    /**
     * 导出审核列表
     *
     * @param estimateApprovalSearchRequest
     */
    void estimateApprovalExport(EstimateApprovalSearchRequest estimateApprovalSearchRequest,List<Integer> roleIds);

    /**
     * 获取销售预估权限
     *
     * @param person
     * @return
     */
    EstimatePermissionVO getEstimatePermission(String person);

}
