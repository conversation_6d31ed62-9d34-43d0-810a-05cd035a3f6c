package com.wantwant.sfa.backend.domain.estimate.service;


import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.*;
import com.wantwant.sfa.backend.estimate.request.EstimateSkuExportRequest;
import com.wantwant.sfa.backend.estimate.vo.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午4:02
 */
public interface IEstimateSkuService {

    /**
     * 保存物料组
     *
     * @param estimateSkuGroupDO
     * @param processUserDO
     * @return
     */
    EstimateSkuGroupDO saveSkuGroup(EstimateSkuGroupDO estimateSkuGroupDO, ProcessUserDO processUserDO);


    /**
     * 根据产品组获取物料组信息
     *
     * @param businessGroup
     * @return
     */
    List<EstimateSkuGroupVO> selectEstimateGroupByBusinessGroup(int businessGroup);

    /**
     * 修改状态
     *
     * @param estimateSkuGroupStatusDO
     * @param processUserDO
     */
    void updateStatus(EstimateSkuGroupStatusDO estimateSkuGroupStatusDO, ProcessUserDO processUserDO);

    /**
     * 查询物料组清单
     *
     * @param businessGroup
     * @param groupId
     * @return
     */
    EstimateSkuGroupDetailVO selectSkuGroupDetail(int businessGroup,Long groupId);

    /**
     * 抓取物料
     *
     * @param businessGroup
     * @return
     */
    List<EstimateSkuVO> catchSku(int businessGroup,Long shipPeriodId);

    /**
     * 保存日程安排
     *
     * @param estimateScheduleDO
     * @param processUserDO
     * @return
     */
    EstimateScheduleDO saveSchedule(EstimateScheduleDO estimateScheduleDO, ProcessUserDO processUserDO);

    /**
     * 根据月份获取排期信息
     *
     * @param yearMonth
     * @return
     */
    List<EstimateScheduleVO> selectSchedule(String yearMonth,Integer businessGroup);

    /**
     * 获取物料组
     *
     * @param businessGroup
     * @return
     */
    List<EstimateSkuGroupSelectVO> selectEstimateSkuGroup(int businessGroup);


    /**
     * 获取排期明细
     *
     * @param scheduleId
     * @return
     */
    EstimateScheduleDetailVO selectScheduleDetail(Long scheduleId);

    /**
     * 查询提报分公司
     *
     * @param estimateOrgSearchDO
     * @return
     */
    List<EstimateOrganizationVO> selectEstimateOrg(EstimateOrgSearchDO estimateOrgSearchDO);

    /**
     * 导出sku
     *
     * @param estimateSkuExportRequest
     */
    void exportSkuList(EstimateSkuExportRequest estimateSkuExportRequest);

    /**
     * 货需期别设置
     *
     * @param estimateShipPeriodDO
     * @param processUserDO
     */
    void setShipPeriod(EstimateShipPeriodDO estimateShipPeriodDO, ProcessUserDO processUserDO);

    /**
     * 查看货需期别
     *
     * @return
     */
    List<ShipPeriodVO> selectShipPeriod(Integer status);

    /**
     * 保存并更新sku信息
     *
     * @param skuList
     */
    void saveOrUpdateSku(List<EstimateApprovalDetailDO> skuList,Integer businessGroup,ProcessUserDO processUserDO);



}
