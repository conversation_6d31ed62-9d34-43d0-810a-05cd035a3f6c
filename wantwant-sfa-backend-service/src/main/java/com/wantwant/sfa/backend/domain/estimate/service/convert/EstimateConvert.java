package com.wantwant.sfa.backend.domain.estimate.service.convert;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateTypeEnum;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.*;
import com.wantwant.sfa.backend.domain.estimate.util.EstimateConstants;
import com.wantwant.sfa.backend.domain.flow.enums.ProcessResultEnum;
import com.wantwant.sfa.backend.estimate.vo.*;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.util.CalculateUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/18/上午9:44
 */
public class EstimateConvert {

    public static List<EstimateApprovalVO> convertToVO(List<EstimateApprovalDTO> list, List<Integer> roleIds, Integer processStep){
        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }

        List<EstimateApprovalVO> result = new ArrayList<>();


        list.forEach(e -> {
            EstimateApprovalVO estimateApprovalVO = new EstimateApprovalVO();
            BeanUtils.copyProperties(e,estimateApprovalVO);
            Integer type = Optional.ofNullable(e.getType()).orElse(EstimateTypeEnum.ROUTINE.getType());
            estimateApprovalVO.setTypeStr(EstimateTypeEnum.getTypeName(type));
            estimateApprovalVO.setApplyTime(LocalDateTimeUtils.formatTime(e.getApplyTime(), EstimateConstants.TIME_FORMAT));
            LocalDateTime auditTime = e.getAuditTime();
            if(Objects.nonNull(auditTime)){
                estimateApprovalVO.setAuditTime(LocalDateTimeUtils.formatTime(auditTime, EstimateConstants.TIME_FORMAT));
            }
            Integer processResult = e.getProcessResult();

            String flowCode = e.getFlowCode();

            Integer isSubmit = e.getIsSubmit();

            Integer finalResult = e.getFinalResult();

            if(finalResult == 2 || isSubmit == 0){
                estimateApprovalVO.setStatus("已驳回");
            }else if(finalResult == 1){
                estimateApprovalVO.setStatus("已通过");
            }else if(finalResult == 0 && !flowCode.equals(EstimateConstants.CEO_FLOW_CODE) && !flowCode.equals(EstimateConstants.DEPARTMENT_FLOW_CODE)){
                estimateApprovalVO.setStatus(e.getProcessName()+"中");
            }else{
                estimateApprovalVO.setStatus("审核中");
            }



            LocalDate now = LocalDate.now();
            LocalDate startDate = e.getStartDate();
            LocalDate endDate = e.getEndDate();

            Integer roleId = e.getRoleId();

            if((processStep == 50 && (e.getProcessStep() == 3 || e.getProcessStep() == 2) ||
                    (processStep == 40 && e.getProcessStep() == 2))){
                if(Objects.nonNull(roleId) && isSubmit == 1 && processResult == 0) {
                    Optional<Integer> first = roleIds.stream().filter(f -> f.equals(roleId)).findFirst();
                    if (first.isPresent()) {
                        estimateApprovalVO.setCanProcess(true);
                    }
                }
            }


            if(isSubmit == 1 && processResult == 0){
                String organizationType = e.getOrganizationType();
                if( (processStep == 30 && e.getProcessStep() == 1 && e.getFlowCode().equals(EstimateConstants.COMPANY_FLOW_CODE)) ||
                        (processStep == 20 && e.getProcessStep() == 1 && e.getFlowCode().equals(EstimateConstants.DEPARTMENT_FLOW_CODE)) ||
                        (processStep == 10 && e.getProcessStep() == 1 && e.getFlowCode().equals(EstimateConstants.CEO_FLOW_CODE))
                ){
                    if(StringUtils.isNotBlank(organizationType)){
                        int order = OrganizationTypeEnum.getOrder(organizationType);
                        int curOrder = OrganizationTypeEnum.getOrder(RequestUtils.getLoginInfo().getOrganizationType());
                        if(curOrder <= order) {
                            estimateApprovalVO.setCanProcess(true);
                        }else if(Objects.nonNull(roleId)){
                            Optional<Integer> first = roleIds.stream().filter(f -> f.equals(roleId)).findFirst();
                            if(first.isPresent()){
                                estimateApprovalVO.setCanProcess(true);
                            }
                        }
                    }
                }
            }



            result.add(estimateApprovalVO);
        });

        return result;
    }

    public static List<EstimateApprovalItemVO> convertToItemVO(List<EstimateApprovalItemDTO> estimateApprovalItemDTOList,
                                                               List<EstimateActualInfoDTO> actualInfoDTOS,
                                                               List<EstimateSkuDTO> lastEstimateList,
                                                               List<EstimateSkuDTO> lowerEstimateList,
                                                               List<SkuInventory> skuInventories,
                                                               List<MOQ> moqList,
                                                               List<SkuAdvancedOrder> skuAdvancedOrderList) {
        if(CollectionUtils.isEmpty(estimateApprovalItemDTOList)){
            return ListUtils.EMPTY_LIST;
        }

        List<EstimateApprovalItemVO> result = new ArrayList<>();
        estimateApprovalItemDTOList.forEach(e -> {
            EstimateApprovalItemVO estimateApprovalItemVO = new EstimateApprovalItemVO();
            BeanUtils.copyProperties(e,estimateApprovalItemVO);
            estimateApprovalItemVO.setEstimatePrice(new BigDecimal(Optional.ofNullable(e.getEstimateCount()).orElse(0)).multiply(Optional.ofNullable(e.getSalePrice()).orElse(BigDecimal.ZERO)).intValue());
            estimateApprovalItemVO.setAuditPrice(new BigDecimal(Optional.ofNullable(e.getAuditCount()).orElse(0)).multiply(Optional.ofNullable(e.getSalePrice()).orElse(BigDecimal.ZERO)).intValue());

            Optional<SkuInventory> inventoryOptional = skuInventories.stream().filter(f ->Objects.nonNull(f.getSku()) && f.getSku().equals(e.getSku())).findFirst();
            if(inventoryOptional.isPresent()){
                SkuInventory skuInventory = inventoryOptional.get();
                estimateApprovalItemVO.setAbnormalInventoryBoxesTotal(Optional.ofNullable(skuInventory.getAbnormalLockedInventoryBoxes()).orElse(BigDecimal.ZERO));
                estimateApprovalItemVO.setNormalInventoryBoxesTotal(Optional.ofNullable(skuInventory.getCanbeShipmentBoxes()).orElse(BigDecimal.ZERO));
            }else{
                estimateApprovalItemVO.setAbnormalInventoryBoxesTotal(BigDecimal.ZERO);
                estimateApprovalItemVO.setNormalInventoryBoxesTotal(BigDecimal.ZERO);
            }

            Optional<SkuAdvancedOrder> skuAdvancedOrderOptional = skuAdvancedOrderList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(skuAdvancedOrderOptional.isPresent()){
                SkuAdvancedOrder skuAdvancedOrder = skuAdvancedOrderOptional.get();
                estimateApprovalItemVO.setAdvancedOrderBox(Optional.ofNullable(skuAdvancedOrder.getAdvancedOrderBox()).orElse(BigDecimal.ZERO).stripTrailingZeros());
            }else{
                estimateApprovalItemVO.setAdvancedOrderBox(BigDecimal.ZERO);
            }

            Optional<MOQ> moqOptional = moqList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(moqOptional.isPresent()){
                MOQ moq = moqOptional.get();
                estimateApprovalItemVO.setCurrentMOQ(Optional.ofNullable(moq.getCurrentMOQ()).orElse(BigDecimal.ZERO));
            }else{
                estimateApprovalItemVO.setCurrentMOQ(BigDecimal.ZERO);
            }
            // V11.6.0当前MOQ数量需要+预定单数
            estimateApprovalItemVO.setCurrentMOQ(estimateApprovalItemVO.getCurrentMOQ().add(estimateApprovalItemVO.getAdvancedOrderBox()));



            Optional<EstimateActualInfoDTO> skuOptional = actualInfoDTOS.stream().filter(f -> f.getSku().equals(e.getSku()) && f.getYearMonth().equals(e.getLastMonth())).findFirst();
            if(skuOptional.isPresent()){
                estimateApprovalItemVO.setLastActualCount(skuOptional.get().getQuantityCm());
            }else{
                estimateApprovalItemVO.setLastActualCount(0);
            }

            Optional<EstimateActualInfoDTO> skuOptional2 = actualInfoDTOS.stream().filter(f -> f.getSku().equals(e.getSku()) && f.getYearMonth().equals(e.getLastLastMonth())).findFirst();
            if(skuOptional2.isPresent()){
                estimateApprovalItemVO.setLastLastActualCount(skuOptional2.get().getQuantityCm());
            }else{
                estimateApprovalItemVO.setLastLastActualCount(0);
            }

            BigDecimal salePrice = Optional.ofNullable(e.getSalePrice()).orElse(BigDecimal.ZERO);
            Integer auditCount = Optional.ofNullable(e.getAuditCount()).orElse(0);
            int auditPrice = salePrice.multiply(new BigDecimal(auditCount)).intValue();
            estimateApprovalItemVO.setAuditPrice(auditPrice);

            Integer estimateCount = Optional.ofNullable(e.getEstimateCount()).orElse(0);
            estimateApprovalItemVO.setDiffCount(auditCount - estimateCount);

            Integer lastLastActualCount = Optional.ofNullable(estimateApprovalItemVO.getLastLastActualCount()).orElse(0);

            BigDecimal ratio = CalculateUtils.ratioPercent(new BigDecimal(auditCount).subtract(new BigDecimal(lastLastActualCount)), new BigDecimal(lastLastActualCount), 3);
            estimateApprovalItemVO.setGrowthRate(ratio);

            // 填充上次提报数据
            Optional<EstimateSkuDTO> lastOptional = lastEstimateList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(lastOptional.isPresent()){
                estimateApprovalItemVO.setLastCount(lastOptional.get().getEstimateCount());
            }else{
                estimateApprovalItemVO.setLastCount(0);
            }

            // 填充下级提报
            Optional<EstimateSkuDTO> lowerOptional = lowerEstimateList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(lowerOptional.isPresent()){
                estimateApprovalItemVO.setLowerCount(lowerOptional.get().getEstimateCount());
            }else{
                estimateApprovalItemVO.setLowerCount(0);
            }
            result.add(estimateApprovalItemVO);
        });
        return result;
    }

    public static List<EstimateSubmitVO> convertSubmitVo(List<EstimateSubmitDTO> estimateSubmitDTOS, List<Integer> roleIds,Integer currentProcessType) {
        if(CollectionUtils.isEmpty(estimateSubmitDTOS)){
            return ListUtils.EMPTY_LIST;
        }

        String curOrganizationType = RequestUtils.getLoginInfo().getOrganizationType();

        List<EstimateSubmitVO> result = new ArrayList<>();

        LocalDate now = LocalDate.now();

        estimateSubmitDTOS.forEach(e -> {
            EstimateSubmitVO estimateSubmitVO = new EstimateSubmitVO();
            BeanUtils.copyProperties(e,estimateSubmitVO);
            estimateSubmitVO.setTypeStr(EstimateTypeEnum.getTypeName(e.getType()));
            LocalDateTime submitTime = e.getSubmitTime();
            if(Objects.nonNull(submitTime)){
                estimateSubmitVO.setSubmitTime(LocalDateTimeUtils.formatTime(submitTime,EstimateConstants.TIME_FORMAT));
            }

            LocalDateTime processTime = e.getProcessTime();
            if(Objects.nonNull(processTime)){
                estimateSubmitVO.setProcessTime(LocalDateTimeUtils.formatTime(submitTime,EstimateConstants.TIME_FORMAT));
            }

            Integer processResult = e.getProcessResult();

            String organizationType = e.getOrganizationType();
            // 检查提报时间
            if((now.isEqual(e.getStartDate()) || now.isAfter(e.getStartDate())) &&
                    (now.isEqual(e.getEndDate()) || now.isBefore(e.getEndDate()))
            ) {
                // 检查是否可提报
                if (Objects.isNull(processResult) || processResult == ProcessResultEnum.FAIL.getResult()) {

                    if (StringUtils.isNotBlank(organizationType)) {
                        int order = OrganizationTypeEnum.getOrder(organizationType);
                        int curOrder = OrganizationTypeEnum.getOrder(curOrganizationType);
                        if (curOrder <= order) {

                            estimateSubmitVO.setCanSubmit(true);
                        }

                    }
                }

                if(Objects.nonNull(processResult) && processResult == ProcessResultEnum.WAIT.getResult()){
                    if(currentProcessType == 10 && e.getProcessName().contains("分公司")){
                        estimateSubmitVO.setCanRedo(true);
                    }
                    if(currentProcessType == 20 && e.getProcessName().contains("大区")){
                        estimateSubmitVO.setCanRedo(true);
                    }
                    if(currentProcessType == 30 && e.getProcessName().contains("产销")){
                        estimateSubmitVO.setCanRedo(true);
                    }
                }
            }

            // 日程安排不限制业务管理每个环节审核时间
            if (Objects.nonNull(processResult) && processResult == ProcessResultEnum.WAIT.getResult()) {
                if (StringUtils.isNotBlank(organizationType) && Objects.isNull(e.getProcessRoleId())) {
                    int order = OrganizationTypeEnum.getOrder(organizationType);
                    int curOrder = OrganizationTypeEnum.getOrder(curOrganizationType);
                    if (curOrder <= order) {
                        estimateSubmitVO.setCanProcess(true);
                    }
                } else {
                    Optional<Integer> roleOptional = roleIds.stream().filter(f -> f.equals(e.getProcessRoleId())).findFirst();
                    if (roleOptional.isPresent()) {
                        estimateSubmitVO.setCanProcess(true);
                    }
                }
            }


            // 设置状态
            if(Objects.isNull(processResult)){
                estimateSubmitVO.setProcessStatus(EstimateConstants.NOT_SUBMIT);
            }else{

                if(processResult == ProcessResult.PASS.getResultCode()){
                    estimateSubmitVO.setProcessStatus(EstimateConstants.PASS);
                }

                else if(processResult == ProcessResult.FAILED.getResultCode()){
                    estimateSubmitVO.setProcessStatus(EstimateConstants.REJECT);
                }
                else {
                    if("department".equals(organizationType)){
                        estimateSubmitVO.setProcessStatus(EstimateConstants.PROCESSING);
                    }else{
                        estimateSubmitVO.setProcessStatus(e.getProcessName()+"中");
                    }

                }
            }
            result.add(estimateSubmitVO);
        });

        return result;
    }

    public static List<EstimateSummaryVO> convertToSummerVO(List<EstimateSummaryDTO> list, List<EstimateActualInfoDTO> actualInfoDTOS) {
        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }

        List<EstimateSummaryVO> result = new ArrayList<>();
        list.forEach(e -> {
            EstimateSummaryVO estimateSummaryVO = new EstimateSummaryVO();
            BeanUtils.copyProperties(e,estimateSummaryVO);

            Optional<EstimateActualInfoDTO> lastMonthOptional = actualInfoDTOS.stream().filter(f -> f.getSku().equals(e.getSku())
                    && f.getYearMonth().equals(e.getLastMonth()) && f.getOrganizationId().equals(e.getOrganizationId())).findFirst();
            if(lastMonthOptional.isPresent()){
                estimateSummaryVO.setLastMonthCount(Optional.ofNullable(lastMonthOptional.get().getQuantityCm()).orElse(0));
            }else{
                estimateSummaryVO.setLastMonthCount(0);
            }

            Optional<EstimateActualInfoDTO> lastLastMonthOptional = actualInfoDTOS.stream().filter(f -> f.getSku().equals(e.getSku())
                    && f.getYearMonth().equals(e.getLastLastMonth()) && f.getOrganizationId().equals(e.getOrganizationId())).findFirst();
            if(lastLastMonthOptional.isPresent()){
                estimateSummaryVO.setLastLastMonthCount(Optional.ofNullable(lastLastMonthOptional.get().getQuantityCm()).orElse(0));
            }else{
                estimateSummaryVO.setLastLastMonthCount(0);
            }


            estimateSummaryVO.setSkuNameStr(e.getSkuName() +" " + e.getFullCaseSpec() +"_"+e.getFlavor());
            
            // 计算成长率
            BigDecimal denominator = new BigDecimal(estimateSummaryVO.getTotalCount()).subtract(new BigDecimal(estimateSummaryVO.getLastLastMonthCount()));


            BigDecimal growthRate = CalculateUtils.ratioPercent(denominator, new BigDecimal(estimateSummaryVO.getLastLastMonthCount()), 3);

            estimateSummaryVO.setGrowthRate(growthRate);
            result.add(estimateSummaryVO);
        });

        return result;
    }

    public static List<EstimateDetailVO> convertToDetailVO(List<EstimateDetailDTO> estimateDetailDTOS, Integer submitLayer) {

        if(CollectionUtils.isEmpty(estimateDetailDTOS)){
            return ListUtils.EMPTY_LIST;
        }

        List<EstimateDetailVO> result = new ArrayList<>();
        estimateDetailDTOS.forEach(e -> {
            EstimateDetailVO estimateDetailVO = new EstimateDetailVO();
            BeanUtils.copyProperties(e,estimateDetailVO);
            if(Objects.nonNull(submitLayer) && submitLayer == 30){
                estimateDetailVO.setCompanyName(null);
                estimateDetailVO.setDepartmentName(null);
            }
            LocalDateTime submitTime = e.getSubmitTime();
            if(Objects.nonNull(submitTime)){
                estimateDetailVO.setSubmitTime(LocalDateTimeUtils.formatTime(submitTime,EstimateConstants.TIME_FORMAT));
            }

            estimateDetailVO.setSkuNameStr(e.getSkuName() +" " + e.getFullCaseSpec() +"_"+e.getFlavor());

            Integer processResult = e.getResult();
            if(processResult == 1){
                estimateDetailVO.setProcessStatus(EstimateConstants.PASS);
            }else if(processResult == 2){
                estimateDetailVO.setProcessStatus(EstimateConstants.REJECT);
            }else{

                Integer processStep = e.getProcessStep();
                String flowCode = e.getFlowCode();
                if(EstimateConstants.COMPANY_FLOW_CODE.equals(flowCode) && processStep == 1){
                    estimateDetailVO.setProcessStatus("大区审核中");
                }else if(EstimateConstants.COMPANY_FLOW_CODE.equals(flowCode) && processStep == 2){
                    estimateDetailVO.setProcessStatus("产销审核中");
                }else{
                    estimateDetailVO.setProcessStatus(EstimateConstants.PROCESSING);
                }
            }



            result.add(estimateDetailVO);
        });

        return result;
    }
}
