package com.wantwant.sfa.backend.domain.estimate.service.factory;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateTypeEnum;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailHistoryPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalPO;
import org.apache.commons.collections.ListUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午2:04
 */
public class EstimateFactory {

    public static EstimateApprovalPO buildEstimateApproval(String saleEstimateNo, String month, String applyPositionId, String applyOrganizationId,
                                                           String applyUserName, int submitType, int type, Long scheduleId, Long instanceId) {

        EstimateApprovalPO estimateApprovalPO = new EstimateApprovalPO();
        estimateApprovalPO.setSaleEstimateNo(saleEstimateNo);
        estimateApprovalPO.setMonth(month);
        estimateApprovalPO.setApplyPositionId(applyPositionId);
        estimateApprovalPO.setApplyUserName(applyUserName);
        estimateApprovalPO.setOrganizationId(applyOrganizationId);
        estimateApprovalPO.setIsSubmit(submitType);
        estimateApprovalPO.setScheduleId(scheduleId);
        estimateApprovalPO.setType(type);
        estimateApprovalPO.setInstanceId(instanceId);
        estimateApprovalPO.setCreateTime(LocalDateTime.now());
        estimateApprovalPO.setUpdateTime(LocalDateTime.now());
        estimateApprovalPO.setUpdateUserId(applyUserName);
        return estimateApprovalPO;
    }

    public static EstimateApprovalDetailPO buildEstimateApprovalDetail(String sku, Integer quantity,BigDecimal price, Long approvalId, String organizationId, String reason) {
        EstimateApprovalDetailPO estimateApprovalDetailPO = new EstimateApprovalDetailPO();
        estimateApprovalDetailPO.setApprovalId(approvalId);
        estimateApprovalDetailPO.setSku(sku);
        estimateApprovalDetailPO.setEstimateQuantity(quantity);
        estimateApprovalDetailPO.setOrganizationId(organizationId);
        estimateApprovalDetailPO.setDeleteFlag(0);
        estimateApprovalDetailPO.setRemark(reason);
        estimateApprovalDetailPO.setSalePrice(price);
        estimateApprovalDetailPO.setCreateTime(LocalDateTime.now());
        estimateApprovalDetailPO.setUpdateTime(LocalDateTime.now());
        return estimateApprovalDetailPO;
    }

    public static EstimateApprovalDetailPO buildEstimateApprovalDetailHistory(EstimateApprovalDetailPO e) {
        EstimateApprovalDetailPO estimateApprovalDetailPO = new EstimateApprovalDetailPO();


        return estimateApprovalDetailPO;
    }

    public static EstimateApprovalDetailHistoryPO buildEstimateApprovalDetailHistory(EstimateApprovalDetailPO estimateApprovalDetailPO,int type, String organizationId, String month, ProcessUserDO processUserDO) {

        EstimateApprovalDetailHistoryPO estimateApprovalDetailHistoryPO = new EstimateApprovalDetailHistoryPO();
        estimateApprovalDetailHistoryPO.setOrganizationId(organizationId);
        estimateApprovalDetailHistoryPO.setTheYearMonth(month);
        estimateApprovalDetailHistoryPO.setSku(estimateApprovalDetailPO.getSku());
        if(type == 1){
            estimateApprovalDetailHistoryPO.setEstimateQuantity(estimateApprovalDetailPO.getAuditQuantity());
        }else{
            estimateApprovalDetailHistoryPO.setAppendQuantity(estimateApprovalDetailPO.getAuditQuantity());
        }
        estimateApprovalDetailHistoryPO.setSalePrice(estimateApprovalDetailPO.getSalePrice());
        estimateApprovalDetailHistoryPO.setDeleteFlag(0);
        estimateApprovalDetailHistoryPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());

        return estimateApprovalDetailHistoryPO;
    }
}
