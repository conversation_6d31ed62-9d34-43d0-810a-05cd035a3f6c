package com.wantwant.sfa.backend.domain.estimate.service.factory;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateScheduleDO;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateStatus;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateScheduleOrganizationRelationPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSchedulePO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSkuGroupPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSkuOrganizationRelationPO;
import com.wantwant.sfa.backend.util.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午5:25
 */
public class EstimateSkuFactory {

    public static EstimateSkuGroupPO initEstimateSkuGroup(String groupName, Integer businessGroup, ProcessUserDO processUserDO){
        EstimateSkuGroupPO estimateSkuGroupPO = new EstimateSkuGroupPO();
        estimateSkuGroupPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        estimateSkuGroupPO.setBusinessGroup(businessGroup);
        estimateSkuGroupPO.setStatus(EstimateStatus.NORMAL.getStatus());
        estimateSkuGroupPO.setGroupName(groupName);
        return estimateSkuGroupPO;
    }

    public static EstimateSkuOrganizationRelationPO initEstimateSkuOrganization(String organizationId,String organizationType,Long skuId,String sku,Long groupId, ProcessUserDO processUserDO){
        EstimateSkuOrganizationRelationPO estimateSkuOrganizationRelationPO = new EstimateSkuOrganizationRelationPO();
        estimateSkuOrganizationRelationPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        estimateSkuOrganizationRelationPO.setSkuId(skuId);
        estimateSkuOrganizationRelationPO.setSku(sku);
        estimateSkuOrganizationRelationPO.setOrganizationId(organizationId);
        estimateSkuOrganizationRelationPO.setOrganizationType(organizationType);
        estimateSkuOrganizationRelationPO.setGroupId(groupId);
        return estimateSkuOrganizationRelationPO;
    }

    public static EstimateSchedulePO initEstimateSchedule(EstimateScheduleDO estimateScheduleDO, ProcessUserDO processUserDO) {
        EstimateSchedulePO estimateSchedulePO = new EstimateSchedulePO();
        Long scheduleId = estimateScheduleDO.getScheduleId();
        if(Objects.nonNull(scheduleId)){
            estimateSchedulePO.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        }else{
            estimateSchedulePO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        }
        BeanUtils.copyProperties(estimateScheduleDO,estimateSchedulePO);
        return estimateSchedulePO;
    }

    public static List<EstimateScheduleOrganizationRelationPO> initEstimateScheduleOrganizationList(List<String> companyCodes, Long scheduleId, ProcessUserDO processUserDO) {
        List<EstimateScheduleOrganizationRelationPO> list = new ArrayList<>();
        companyCodes.forEach(e -> {
            EstimateScheduleOrganizationRelationPO estimateScheduleOrganizationRelationPO = new EstimateScheduleOrganizationRelationPO();
            estimateScheduleOrganizationRelationPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
            estimateScheduleOrganizationRelationPO.setOrganizationId(e);
            estimateScheduleOrganizationRelationPO.setOrganizationType("company");
            estimateScheduleOrganizationRelationPO.setScheduleId(scheduleId);
            list.add(estimateScheduleOrganizationRelationPO);
        });

        return list;
    }
}
