package com.wantwant.sfa.backend.domain.estimate.service.impl;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateAdjustStatusEnum;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustPO;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateAdjustService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/08/下午4:58
 */
@Service
@Slf4j
public class EstimateAdjustService implements IEstimateAdjustService {

    @Resource
    private IEstimateRepository estimateRepository;

    @Override
    public EstimateAdjustPO createMoqAudit(String yearMonth, Long shipPeriodId, String sku, ProcessUserDO processUserDO) {

        // 将当前同批次的调整单状态设置未非当前
        EstimateAdjustPO estimateAdjustPO = estimateRepository.selectCurrentMoqAudit(yearMonth,shipPeriodId,sku);
        if(Objects.nonNull(estimateAdjustPO)){
            estimateAdjustPO.setIsCurrent(0);
            estimateAdjustPO.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
            estimateRepository.updateEstimateAdjustPO(estimateAdjustPO);
        }

        estimateAdjustPO = new EstimateAdjustPO();
        estimateAdjustPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        estimateAdjustPO.setMonth(yearMonth);
        estimateAdjustPO.setIsCurrent(1);
        estimateAdjustPO.setSku(sku);
        estimateAdjustPO.setShipPeriodId(shipPeriodId);
        estimateAdjustPO.setStatus(EstimateAdjustStatusEnum.ADJUST_PROCESSING.getStatus());
        estimateRepository.saveEstimateAdjust(estimateAdjustPO);
        return estimateAdjustPO;
    }

    @Override
    public void initAdjustDetail(Long adjustId, String organizationId, String sku, Integer type,Integer rawEstimateCount, ProcessUserDO processUserDO) {
        EstimateAdjustDetailPO estimateAdjustDetailPO = new EstimateAdjustDetailPO();
        estimateAdjustDetailPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        estimateAdjustDetailPO.setAdjustId(adjustId);
        estimateAdjustDetailPO.setOrganizationId(organizationId);
        estimateAdjustDetailPO.setSku(sku);
        estimateAdjustDetailPO.setType(type);
        estimateAdjustDetailPO.setStatus(0);
        estimateAdjustDetailPO.setRawEstimateCount(rawEstimateCount);
        estimateAdjustDetailPO.setAuditCount(rawEstimateCount);
        estimateRepository.saveAdjustDetail(estimateAdjustDetailPO);
    }

    @Override
    public EstimateAdjustPO findEstimateAdjust(String yearMonth, Long shipPeriodId, String sku) {
        return estimateRepository.selectCurrentMoqAudit(yearMonth,shipPeriodId,sku);
    }

    @Override
    public void updateEstimateAdjust(EstimateAdjustPO estimateAdjustPO) {
        estimateRepository.updateEstimateAdjustPO(estimateAdjustPO);
    }

    @Override
    public void updateDetailStatus(Long adjustId, int status,ProcessUserDO processUserDO) {
        EstimateAdjustDetailPO updateStatus = new EstimateAdjustDetailPO();
        updateStatus.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        updateStatus.setStatus(status);
        estimateRepository.updateDetailStatus(adjustId,status,updateStatus);
    }

    @Override
    public EstimateAdjustDetailPO selectAdjustDetail(Long adjustId, String organizationId, Integer type) {
        return estimateRepository.selectAdjustDetailPO(adjustId,organizationId,type);
    }

    @Override
    public void updateEstimateAdjustDetail(EstimateAdjustDetailPO estimateAdjustDetailPO) {
        estimateRepository.updateEstimateAdjustDetail(estimateAdjustDetailPO);
    }
}
