package com.wantwant.sfa.backend.domain.estimate.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wantwant.sfa.backend.domain.estimate.mapper.EstimateBigTableMapper;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateBigTableService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;

@Service
@DS("production_6")
public class EstimateBigTableService implements IEstimateBigTableService {
    @Resource
    private EstimateBigTableMapper estimateBigTableMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BigDecimal getRecentThreeMonthsSalesPerformance(String beforeMonth, String theYearMonth, String organizationId) {
        return estimateBigTableMapper.getRecentThreeMonthsSalesPerformance(before<PERSON><PERSON><PERSON>,the<PERSON><PERSON><PERSON><PERSON><PERSON>,organizationId);
    }
}
