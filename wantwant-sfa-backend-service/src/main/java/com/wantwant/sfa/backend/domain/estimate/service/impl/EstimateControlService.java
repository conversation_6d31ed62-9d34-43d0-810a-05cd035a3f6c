package com.wantwant.sfa.backend.domain.estimate.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalDetailDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateCheckDO;
import com.wantwant.sfa.backend.domain.estimate.DO.value.EstimateDetail;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateHistoryDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.persistence.EstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateControlDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateControlPO;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateControlService;
import com.wantwant.sfa.backend.estimate.request.EstimateControlSearchRequest;
import com.wantwant.sfa.backend.estimate.vo.EstimateControlDetailVO;
import com.wantwant.sfa.backend.estimate.vo.EstimateControlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class EstimateControlService implements IEstimateControlService {
    @Resource
    private EstimateRepository estimateRepository;
    @Resource
    private EstimateBigTableService estimateBigTableService;


    private static final String CONTROL_ERR_MSG = "{0}货需中提报的{1}产品盘价金额之和需要小于分公司最近3个月的平均业绩的{2}";
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public EstimateControlVO selectEstimateControl(EstimateControlSearchRequest estimateControlSearchRequest) {
        EstimateControlVO estimateControlVO = new EstimateControlVO();
        String theYearMonth = estimateControlSearchRequest.getTheYearMonth();
        String organizationId = estimateControlSearchRequest.getOrganizationId();
        LocalDate currentYearMonth = LocalDate.now().withDayOfMonth(1);

        List<EstimateControlPO> estimateControlPOS = estimateRepository.selectEstimateControl(currentYearMonth.toString(), organizationId);
        if(CollectionUtils.isEmpty(estimateControlPOS)){
            return estimateControlVO;
        }


        List<Long> controlIds = estimateControlPOS.stream().map(EstimateControlPO::getId).collect(Collectors.toList());
        List<EstimateControlDetailPO> estimateControlDetailPOS = estimateRepository.selectEstimateControlDetail(controlIds);
        if(CollectionUtils.isEmpty(estimateControlDetailPOS)){
            return estimateControlVO;
        }

        estimateControlVO.setEstimateControl(true);

        // 读取大数据累计业绩,货需月份-2
        LocalDate beforeMonth = LocalDate.parse(theYearMonth+"-01").minusMonths(4L);
        LocalDate afterMonth = beforeMonth.plusMonths(3);
        // 计算管控额度
        BigDecimal salesPerformance = Optional.ofNullable(estimateBigTableService.getRecentThreeMonthsSalesPerformance(beforeMonth.toString().substring(0,7),afterMonth.toString().substring(0,7),organizationId)).orElse(BigDecimal.ZERO);

        List<EstimateControlDetailVO> estimateControlDetailVOList = new ArrayList<>();
        estimateControlPOS.forEach(e -> {

            List<String> skuList = estimateControlDetailPOS.stream().filter(f -> f.getControlId().equals(e.getId()))
                    .map(EstimateControlDetailPO::getSku).collect(Collectors.toList());

            BigDecimal controlQuotaRatio = Optional.ofNullable(e.getControlRatio()).orElse(BigDecimal.ZERO);
            BigDecimal controlQuota = salesPerformance.multiply(controlQuotaRatio).divide(BigDecimal.valueOf(3),2, RoundingMode.HALF_UP);

            // 获取已提报的信息
            List<EstimateApprovalDetailDO> mergedList = getEstimateApprovalDetailDOS(estimateControlSearchRequest.getApprovalId(),estimateControlSearchRequest.getAdjustId(), theYearMonth, organizationId, skuList);

            // 计算已提报额度
            BigDecimal totalPrice = mergedList.stream().map(item -> Optional.of(BigDecimal.valueOf(item.getAuditCount())).orElse(BigDecimal.ZERO)
                            .multiply(Optional.ofNullable(item.getSalePrice()).orElse(BigDecimal.ZERO)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            EstimateControlDetailVO estimateControlDetailVO = EstimateControlDetailVO.builder().controlName(e.getControlName())
                    .skuList(skuList)
                    .submitPrice(totalPrice)
                    .quota(controlQuota)
                    .build();
            estimateControlDetailVOList.add(estimateControlDetailVO);

        });
        estimateControlVO.setEstimateControlDetailVOList(estimateControlDetailVOList);

        return estimateControlVO;
    }

    private List<EstimateApprovalDetailDO> getEstimateApprovalDetailDOS(Long approvalId,Long adjustId, String theYearMonth, String organizationId, List<String> skuList) {
        // 已提报额度
        List<EstimateApprovalDetailDO> estimateApprovalDetailDOS = Optional.ofNullable(estimateRepository.selectSubmitPrice(theYearMonth, organizationId, skuList, approvalId)).orElse(new ArrayList<>());
        // 已审核通过的额度
        List<EstimateApprovalDetailDO> finishedDetailList = Optional.ofNullable(estimateRepository.selectFinishedBySku(theYearMonth, organizationId, skuList)).orElse(new ArrayList<>());
        // 合并两个 list，并对相同 sku 进行 salePrice 和 auditCount 累加
        List<EstimateApprovalDetailDO> mergedList = mergeList(estimateApprovalDetailDOS, finishedDetailList);
        // 调整中的数量
        List<EstimateDetail> adjustDetailList = Optional.ofNullable(estimateRepository.selectAdjustDetailBySku(theYearMonth, organizationId, skuList, adjustId)).orElse(new ArrayList<>());

        if(!CollectionUtils.isEmpty(adjustDetailList)){
            // 将 mergedList 转换为 Map<sku, DO>，便于后续查找
            Map<String, EstimateApprovalDetailDO> mergedMap = mergedList.stream()
                    .collect(Collectors.toMap(
                            EstimateApprovalDetailDO::getSku,
                            item -> item,
                            (existing, replacement) -> existing // 如果有重复 sku，保留旧值（理论上不会发生）
                    ));

            // 遍历 adjustDetailList 处理每个调整项
            for (EstimateDetail a : adjustDetailList) {
                String sku = a.getSku();
                Integer currentCount = Optional.ofNullable(a.getCurrentCount()).orElse(0);
                Integer rawCount = Optional.ofNullable(a.getRawCount()).orElse(0);

                if (mergedMap.containsKey(sku)) {
                    // 匹配到：修改 auditCount
                    EstimateApprovalDetailDO existing = mergedMap.get(sku);
                    existing.setAuditCount(existing.getAuditCount() + currentCount - rawCount);
                } else {
                    // 没匹配到：新增记录
                    EstimateApprovalDetailDO newDO = new EstimateApprovalDetailDO();
                    newDO.setSku(sku);
                    newDO.setSalePrice(Optional.ofNullable(a.getSalePrice()).orElse(BigDecimal.ZERO));
                    newDO.setAuditCount(currentCount); // 初始数量为差值
                    mergedMap.put(sku, newDO);
                }
            }
            // 更新 mergedList（如果需要后续使用 List 结构）
            mergedList.clear();
            mergedList.addAll(mergedMap.values());
        }
        return mergedList;
    }

    private List<EstimateApprovalDetailDO> mergeList(List<EstimateApprovalDetailDO> estimateApprovalDetailDOS, List<EstimateApprovalDetailDO> finishedDetailList) {
        // 如果 sku 相同，则累加 salePrice 和 auditCount
        return new ArrayList<>(Stream.concat(finishedDetailList.stream(), estimateApprovalDetailDOS.stream())
                .collect(Collectors.toMap(
                        EstimateApprovalDetailDO::getSku, // 以 sku 作为 key
                        item -> {
                            EstimateApprovalDetailDO copy = new EstimateApprovalDetailDO();
                            copy.setSku(item.getSku());
                            copy.setSkuName(item.getSkuName());
                            copy.setSalePrice(Optional.ofNullable(item.getSalePrice()).orElse(BigDecimal.ZERO));
                            copy.setAuditCount(Optional.ofNullable(item.getAuditCount()).orElse(0));
                            return copy;
                        },
                        (existing, replacement) -> {
                            // 如果 sku 相同，则累加 salePrice 和 auditCount
                            existing.setSalePrice(existing.getSalePrice());
                            existing.setAuditCount(existing.getAuditCount() + replacement.getAuditCount());
                            return existing;
                        }
                ))
                .values());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void checkEstimateControl(EstimateCheckDO estimateCheckDO) {
        String theYearMonth = estimateCheckDO.getTheYearMonth();
        String organizationId = estimateCheckDO.getOrganizationId();
        LocalDate currentYearMonth = LocalDate.now();

        List<EstimateControlPO> estimateControlPOS = estimateRepository.selectEstimateControl(currentYearMonth.toString(), organizationId);
        if(CollectionUtils.isEmpty(estimateControlPOS)){
            return;
        }

        List<Long> controlIds = estimateControlPOS.stream().map(EstimateControlPO::getId).collect(Collectors.toList());
        List<EstimateControlDetailPO> estimateControlDetailPOS = estimateRepository.selectEstimateControlDetail(controlIds);
        if(CollectionUtils.isEmpty(estimateControlDetailPOS)){
            return;
        }

        List<EstimateDetail> estimateDetailList = estimateCheckDO.getEstimateDetailList();

        // 读取大数据累计业绩,货需月份-2
        LocalDate beforeMonth = LocalDate.parse(theYearMonth+"-01").withDayOfMonth(1).minusMonths(4L);
        LocalDate afterMonth = beforeMonth.plusMonths(3);
        // 计算管控额度
        BigDecimal salesPerformance = Optional.ofNullable(estimateBigTableService.getRecentThreeMonthsSalesPerformance(beforeMonth.toString().substring(0,7),afterMonth.toString().substring(0,7),organizationId)).orElse(BigDecimal.ZERO);

        for (EstimateControlPO e : estimateControlPOS) {
            BigDecimal controlQuotaRatio = Optional.ofNullable(e.getControlRatio()).orElse(BigDecimal.ZERO);
            // 管控的额度
            BigDecimal controlQuota = salesPerformance.multiply(controlQuotaRatio).divide(BigDecimal.valueOf(3), RoundingMode.HALF_UP);

            // 获取该管控项对应的SKU列表
            List<String> controlSkuList = estimateControlDetailPOS.stream()
                    .filter(f -> f.getControlId().equals(e.getId()))
                    .map(EstimateControlDetailPO::getSku)
                    .collect(Collectors.toList());
            
            // 转换为Set提高查询性能
            Set<String> controlSkuSet = new HashSet<>(controlSkuList);

            // 获取已提报的信息
            List<EstimateApprovalDetailDO> mergedList = getEstimateApprovalDetailDOS(estimateCheckDO.getApprovalId(), estimateCheckDO.getAdjustId(), theYearMonth, organizationId, controlSkuList);


            // 将 mergedList 转换为 Map<sku, DO>，便于后续查找和修改
            Map<String, EstimateApprovalDetailDO> mergedMap = mergedList.stream()
                    .collect(Collectors.toMap(
                            EstimateApprovalDetailDO::getSku,
                            item -> item,
                            (existing, replacement) -> existing // 如果有重复 sku，保留旧值（理论上不会发生）
                    ));

            // 如果本次提报没有相关的sku则不处理
            BigDecimal currentTotalSalePrice = estimateDetailList.stream()
                    .filter(f -> controlSkuSet.contains(f.getSku()))
                    .map(item -> Optional.ofNullable(item.getSalePrice()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(Optional.ofNullable(item.getCurrentCount()).orElse(0))))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if(currentTotalSalePrice.compareTo(BigDecimal.ZERO) == 0){
                continue;
            }

            // 遍历 estimateDetailList 处理每个调整项
            for (EstimateDetail detail : estimateDetailList) {
                String sku = detail.getSku();

                // 无需管控的sku
                if(!controlSkuSet.contains(sku)){
                    continue;
                }

                Integer rawCount = Optional.ofNullable(detail.getRawCount()).orElse(0);
                Integer currentCount = Optional.ofNullable(detail.getCurrentCount()).orElse(0);

                if (mergedMap.containsKey(sku)) {
                    // 匹配到：更新 auditCount = auditCount - rawCount + currentCount
                    EstimateApprovalDetailDO existing = mergedMap.get(sku);
                    existing.setAuditCount(existing.getAuditCount() - rawCount + currentCount);
                } else {
                    // 没匹配到：新增记录
                    EstimateApprovalDetailDO newDO = new EstimateApprovalDetailDO();
                    newDO.setSku(sku);
                    newDO.setSalePrice(Optional.ofNullable(detail.getSalePrice()).orElse(BigDecimal.ZERO));
                    newDO.setAuditCount(currentCount); // 初始数量为 currentCount
                    mergedMap.put(sku, newDO);
                }
            }

            // 更新 mergedList（保持 List 结构）
            mergedList.clear();
            mergedList.addAll(mergedMap.values());

            // 计算总提交金额
            BigDecimal totalPrice = mergedList.stream().map(item -> Optional.of(BigDecimal.valueOf(item.getAuditCount())).orElse(BigDecimal.ZERO)
                            .multiply(Optional.ofNullable(item.getSalePrice()).orElse(BigDecimal.ZERO)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("当前管控额度：{},已提报额度：{}", controlQuota, totalPrice);

            if (totalPrice.compareTo(controlQuota) > 0) {
                String formatDate = LocalDateTimeUtils.formatTime(currentYearMonth.atStartOfDay(), "yyyy年MM月");
                String controlName = e.getControlName();
                BigDecimal controlQuotaPercent = controlQuotaRatio.multiply(BigDecimal.valueOf(100));
                throw new ApplicationException(MessageFormat.format(CONTROL_ERR_MSG, formatDate, controlName, controlQuotaPercent.setScale(0, RoundingMode.HALF_UP).toPlainString()+"%"));
            }
        }


    }


}
