package com.wantwant.sfa.backend.domain.estimate.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.*;
import com.wantwant.sfa.backend.domain.estimate.DO.value.AdjustDetailValue;
import com.wantwant.sfa.backend.domain.estimate.DO.value.EstimateDetail;
import com.wantwant.sfa.backend.domain.estimate.enums.AdjustTypeEnum;
import com.wantwant.sfa.backend.domain.estimate.enums.AuditResultEnum;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateAdjustStatusEnum;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateErrorMsgEnum;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateSubmitEnum;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateTypeEnum;
import com.wantwant.sfa.backend.domain.estimate.mapper.EstimateApprovalDetailMapper;
import com.wantwant.sfa.backend.domain.estimate.mapper.EstimateExternalMapper;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.*;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateSkuRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.*;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateAdjustService;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateControlService;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateDomainService;
import com.wantwant.sfa.backend.domain.estimate.service.convert.EstimateConvert;
import com.wantwant.sfa.backend.domain.estimate.service.factory.EstimateFactory;
import com.wantwant.sfa.backend.domain.estimate.util.EstimateConstants;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.estimate.model.SaveSkuSaleModel;
import com.wantwant.sfa.backend.estimate.model.SkuEstimateBoxNumberDTO;
import com.wantwant.sfa.backend.estimate.model.SkuModel;
import com.wantwant.sfa.backend.estimate.model.SkuOverSaleControlSaveWithPhaseRequest;
import com.wantwant.sfa.backend.estimate.request.*;
import com.wantwant.sfa.backend.estimate.vo.*;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.EstimateClient;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.model.ActivityTagDTO;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.model.SkuInfoDTO;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.request.QuerySkuRequest;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.model.estimate.EstimateDetailModel;
import com.wantwant.sfa.backend.model.estimate.EstimateModel;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EstimateConnectorUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午1:22
 */
@Service
@Slf4j
public class EstimateDomainService implements IEstimateDomainService {

    @Resource
    private EstimateExternalMapper estimateExternalMapper;
    @Resource
    private IEstimateSkuRepository estimateSkuRepository;
    @Resource
    private IEstimateRepository estimateRepository;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private EstimateConnectorUtil estimateConnectorUtil;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private EstimateClient estimateClient;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private EstimateSkuService estimateSkuService;
    @Resource
    private IEstimateAdjustService estimateAdjustService;

    public static final String ESTIMATE_ADJUST_KEY = "sfa:estimate:adjust";
    
    // 状态常量
    private static final int STATUS_PROCESSED = 1;
    private static final int STATUS_UNPROCESSED = 0;
    private static final int AUDIT_STATUS_APPROVED = 4;
    private static final int AUDIT_STATUS_REJECTED = 3;
    
    // 默认超时时间（秒）
    private static final int DEFAULT_LOCK_TIMEOUT = 10;
    
    // 默认数量值
    private static final int DEFAULT_QUANTITY = 0;
    @Resource
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Resource
    private IEstimateControlService estimateControlService;
    @Autowired
    private EstimateApprovalDetailMapper estimateApprovalDetailMapper;

    @Override
    @Transactional
    public Long ceoEstimateApply(CeoEstimateApplyDO ceoEstimateApplyDO) {
        // 增加分布式锁
        if(!redisUtil.setLockIfAbsent(EstimateConstants.CEO_APPLY_LOCK,ceoEstimateApplyDO.getSaleEstimateNo(),5, TimeUnit.SECONDS)){
            throw new ApplicationException(EstimateErrorMsgEnum.REPEATED_OPERATION.getMsg());
        }
        Long approvalId = null;
        try {
            // 获取旺铺预估单信息
            SaleEstimateDO saleEstimateBySaleEstimateNo = estimateExternalMapper.getSaleEstimateBySaleEstimateNo(ceoEstimateApplyDO.getSaleEstimateNo());
            if (Objects.isNull(saleEstimateBySaleEstimateNo)) {
                throw new ApplicationException(EstimateErrorMsgEnum.SALE_ESTIMATE_NO_ERROR.getMsg());
            }



            // 获取排期信息
            EstimateSchedulePO estimateSchedulePO = estimateSkuRepository.matchSchedule(LocalDate.now(), saleEstimateBySaleEstimateNo.getMonth(), EstimateTypeEnum.ROUTINE.getType(),ceoEstimateApplyDO.getBusinessGroup(),3);
            if (Objects.isNull(estimateSchedulePO)) {
                throw new ApplicationException(EstimateErrorMsgEnum.SCHEDULE_NOT_MATCH.getMsg());
            }

            // 创建销售预估申请表
            log.info("【ceo estimate apply】init estimate apply start");
            approvalId = saveEstimateApproval(saleEstimateBySaleEstimateNo, ceoEstimateApplyDO, estimateSchedulePO);
            log.info("【ceo estimate apply】init estimate apply finish,approvalId:{}", approvalId);

            // 保存销售预估申请信息
            log.info("【ceo estimate apply】save estimate detail start");
            BigDecimal totalPrice = saveEstimateDetail(saleEstimateBySaleEstimateNo, approvalId, ceoEstimateApplyDO.getApplyOrganizationId(),ceoEstimateApplyDO.getBusinessGroup());
            log.info("【ceo estimate apply】save estimate detail finish");

            // 冗余预估金额
            estimateRepository.savePrice(approvalId, totalPrice, BigDecimal.ZERO);
        }finally {
            redisUtil.unLock(EstimateConstants.CEO_APPLY_LOCK,ceoEstimateApplyDO.getSaleEstimateNo());
        }
        return approvalId;
    }

    @Override
    public EstimateApprovalInfoVO selectApprovalList(EstimateApprovalSearchRequest estimateApprovalSearchRequest, List<Integer> roleIds) {

        // 根据step 获取流程CODE
        String flowCode = EstimateConstants.COMPANY_FLOW_CODE;
        Integer step = estimateApprovalSearchRequest.getProcessStep();
        if(step == 20){
            flowCode = EstimateConstants.DEPARTMENT_FLOW_CODE;
        }else if(step == 10){
            flowCode = EstimateConstants.CEO_FLOW_CODE;
        }
        estimateApprovalSearchRequest.setFlowCode(flowCode);

        List<String> organizationIds = Optional.ofNullable(estimateApprovalSearchRequest.getOrganizationIds()).orElse(new ArrayList<>());
        if(organizationIds.stream().findFirst().isPresent()){
            String organizationType = organizationMapper.getOrganizationType(organizationIds.stream().findFirst().get());
            estimateApprovalSearchRequest.setOrganizationType(organizationType);
        }
        log.info("【select approval list】request:{}",estimateApprovalSearchRequest);


        Page<EstimateApprovalVO> page = new Page<>(estimateApprovalSearchRequest.getPage(), estimateApprovalSearchRequest.getRows());
        List<EstimateApprovalDTO> list = estimateRepository.selectApprovalList(page,estimateApprovalSearchRequest);
        page.setRecords(EstimateConvert.convertToVO(list,roleIds,estimateApprovalSearchRequest.getProcessStep()));
        EstimateApprovalSummaryDTO estimateApprovalSummaryDTO = Optional.ofNullable(estimateRepository.selectApprovalSummary(estimateApprovalSearchRequest))
                .orElse(EstimateApprovalSummaryDTO.builder().totalAuditPrice(BigDecimal.ZERO).totalEstimatePrice(BigDecimal.ZERO)
                .totalAuditQuantity(BigDecimal.ZERO).totalEstimateQuantity(BigDecimal.ZERO).build());

        return EstimateApprovalInfoVO.builder().page(page).totalAuditPrice(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalAuditPrice()).orElse(BigDecimal.ZERO).intValue())
                .totalEstimatePrice(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalEstimatePrice()).orElse(BigDecimal.ZERO).intValue())
                .totalAuditQuantity(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalAuditQuantity()).orElse(BigDecimal.ZERO).intValue())
                .totalEstimateQuantity(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalEstimateQuantity()).orElse(BigDecimal.ZERO).intValue()).build();
    }

    @Override
    public EstimateApprovalDetailVO getEstimateApprovalDetail(Long approvalId, Integer mode) {
        EstimateApprovalPO estimateApprovalPO = estimateRepository.selectApprovalById(approvalId);
        if(Objects.isNull(estimateApprovalPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.APPROVAL_NOT_EXIST.getMsg());
        }

        Long scheduleId = estimateApprovalPO.getScheduleId();
        Long shipPeriodByScheduleId = estimateSkuRepository.selectShipPeriodBySchedule(scheduleId);
        if(Objects.isNull(shipPeriodByScheduleId)){
            throw new ApplicationException(EstimateErrorMsgEnum.SHIP_PERIOD_NOT_EXIST.getMsg());
        }
        EstimateShipPeriodPO estimateShipPeriodPO = estimateSkuRepository.selectShipPeriodById(shipPeriodByScheduleId);
        if(Objects.isNull(estimateShipPeriodPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SHIP_PERIOD_NOT_EXIST.getMsg());
        }

        CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, estimateApprovalPO.getOrganizationId()).last("limit 1"));

        EstimateApprovalDetailVO estimateApprovalDetailVO = new EstimateApprovalDetailVO();
        estimateApprovalDetailVO.setTypeStr(EstimateTypeEnum.getTypeName(estimateApprovalPO.getType()));
        String year = estimateApprovalPO.getMonth().split("-")[0];
        String month = estimateApprovalPO.getMonth().split("-")[1];
        String applyUserName = estimateApprovalPO.getApplyUserName();
        String organizationType = organizationMapper.getOrganizationType(estimateApprovalPO.getOrganizationId());
        if(!"branch".equals(organizationType)){
            applyUserName = organizationMapper.getOrganizationName(estimateApprovalPO.getOrganizationId());
        }

        // 设置标题
        estimateApprovalDetailVO.setTitle(MessageFormat.format(EstimateConstants.APPROVAL_TITLE,year,month,estimateShipPeriodPO.getName(),applyUserName));

        EstimateSchedulePO estimateSchedulePO = estimateSkuRepository.selectScheduleById(estimateApprovalPO.getScheduleId());
        if(Objects.isNull(estimateSchedulePO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SCHEDULE_NOT_MATCH.getMsg());
        }

        // 获取审核状态
        String processResult = estimateRepository.selectProcessStatus(approvalId);
        estimateApprovalDetailVO.setProcessResult(processResult);
        estimateApprovalDetailVO.setStartDate(estimateSchedulePO.getStartDate().toString());
        estimateApprovalDetailVO.setEndDate(estimateSchedulePO.getEndDate().toString());

        // 根据模式获取不同的数据::1.编辑模式 2.查看模式
        List<EstimateApprovalItemDTO> estimateApprovalItemDTOList = new ArrayList<>();

        estimateApprovalItemDTOList =  Optional.ofNullable(estimateRepository.selectSubmitSku(approvalId)).orElse(new ArrayList<>());



        List<String> skuList = estimateApprovalItemDTOList.stream().map(EstimateApprovalItemDTO::getSku).collect(Collectors.toList());

        // 获取上月提报的额度
        String theYearMonth = estimateSchedulePO.getTheYearMonth();
        String lastYearMonth = LocalDate.parse(theYearMonth + "-01").minusMonths(1L).toString().substring(0, 7);
        List<EstimateHistoryDTO> lastYearMonthHistoryList = Optional.ofNullable(estimateRepository.selectHistoryBySku(lastYearMonth,estimateApprovalPO.getOrganizationId(),skuList)).orElse(new ArrayList<>());

        estimateApprovalItemDTOList.forEach(e -> {
            e.setLastMonth(lastYearMonth);
            Optional<EstimateHistoryDTO> skuOptional = lastYearMonthHistoryList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(skuOptional.isPresent()){
                e.setLastEstimateCount(skuOptional.get().getEstimateCount());
            }else{
                e.setLastEstimateCount(0);
            }
        });

        // 获取上上月
        String lastLastYearMonth = LocalDate.parse(theYearMonth + "-01").minusMonths(2L).toString().substring(0, 7);
        List<EstimateHistoryDTO> lastLastYearMonthHistoryList = Optional.ofNullable(estimateRepository.selectHistoryBySku(lastLastYearMonth,estimateApprovalPO.getOrganizationId(),skuList)).orElse(new ArrayList<>());
        estimateApprovalItemDTOList.forEach(e -> {
            e.setLastLastMonth(lastLastYearMonth);
            Optional<EstimateHistoryDTO> skuOptional = lastLastYearMonthHistoryList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(skuOptional.isPresent()){
                e.setLastLastEstimateCount(skuOptional.get().getEstimateCount());
            }else{
                e.setLastLastEstimateCount(0);
            }
        });


        // 去大数据表查询sku实际销售箱数
        List<EstimateActualInfoDTO> actualInfoDTOS = new ArrayList<>();
        if(!CollectionUtils.isEmpty(skuList)){
            actualInfoDTOS =  Optional.ofNullable(estimateRepository.selectEstimateActualInfo(estimateApprovalPO.getOrganizationId(),estimateApprovalPO.getMonth(),skuList)).orElse(new ArrayList<>());
        }

        // 获取上次提报
        List<EstimateSkuDTO> lastEstimateList = Optional.ofNullable(estimateRepository.selectLastEstimateFromHistory(estimateApprovalPO.getOrganizationId(),
                estimateApprovalPO.getMonth(),estimateShipPeriodPO.getId())).orElse(new ArrayList<>()).stream().map(EstimateSkuDTO::build).collect(Collectors.toList());

        // 获取下级提报
        List<EstimateSkuDTO> lowerEstimateList = Optional.ofNullable(estimateRepository.selectLowerEstimate(estimateApprovalPO.getOrganizationId(),organizationMapper.getOrganizationType(estimateApprovalPO.getOrganizationId()),estimateApprovalPO.getMonth())).orElse(new ArrayList<>());

        // 获取发货仓信息
        String storeName = estimateRepository.getStoreName(viewEntity.getOrgId2());

        // 获取MOQ信息
        List<SkuInventory> skuInventories =  Optional.ofNullable(estimateRepository.selectSkuInventory(storeName,skuList,LocalDate.now().toString().substring(0,7),organizationMapper.getBusinessGroupById(viewEntity.getOrgId2()))).orElse(new ArrayList<>());

        // 获取当月已提交的sku信息
        List<MOQ> moqList =  Optional.ofNullable(estimateRepository.selectCurrentMOQ(skuList,estimateSchedulePO.getTheYearMonth(),null)).orElse(new ArrayList<>());

        // 获取预订单数量
        List<SkuAdvancedOrder> skuAdvancedOrderList =  Optional.ofNullable(estimateRepository.selectAdvancedOrderBox(estimateApprovalPO.getOrganizationId(),organizationType,skuList,estimateSchedulePO.getTheYearMonth())).orElse(new ArrayList<>());



        List<EstimateApprovalItemVO> estimateApprovalItemVOS = EstimateConvert.convertToItemVO(estimateApprovalItemDTOList,actualInfoDTOS,lastEstimateList,lowerEstimateList, skuInventories, moqList, skuAdvancedOrderList);
        estimateApprovalDetailVO.setEstimateApprovalItemVOList(estimateApprovalItemVOS);

        return estimateApprovalDetailVO;
    }

    @Override
    public Long getInstanceId(Long approvalId) {
        EstimateApprovalPO estimateApprovalPO = estimateRepository.selectApprovalById(approvalId);
        if(Objects.isNull(estimateApprovalPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.APPROVAL_NOT_EXIST.getMsg());
        }

        return estimateApprovalPO.getInstanceId();
    }

    @Override
    @Transactional
    public void pass(EstimateApprovalPassDO convertEstimateApprovalPassDO, ProcessUserDO processUserDO) {
        // 增加分布式锁
        if(!redisUtil.setLockIfAbsent(EstimateConstants.OPERATION_LOCK,String.valueOf(convertEstimateApprovalPassDO.getApprovalId()),5, TimeUnit.SECONDS)){
            throw new ApplicationException(EstimateErrorMsgEnum.REPEATED_OPERATION.getMsg());
        }

        try {
            // 1. 获取并验证申请信息
            EstimateApprovalPO estimateApprovalPO = getAndCheckApproval(convertEstimateApprovalPassDO.getApprovalId());
            Long shipPeriod = getAndCheckShipPeriod(estimateApprovalPO.getScheduleId());
            
            // 2. 构建预估详情并检查额度管控
            List<EstimateApprovalDetailDO> skuList = Optional.ofNullable(convertEstimateApprovalPassDO.getSkuList()).orElse(new ArrayList<>());
            List<EstimateDetail> details = buildEstimateDetails(skuList, convertEstimateApprovalPassDO.getApprovalId());
            checkEstimateControl(estimateApprovalPO, details);

            // 3. 获取提报的物料信息
            List<EstimateApprovalDetailPO> approvalDetailPOList = getApprovalDetailList(convertEstimateApprovalPassDO.getApprovalId());

            // 4. 更新物料确认信息(营运/产销不需要更新)
            Integer processStep = convertEstimateApprovalPassDO.getProcessStep();
            if (!isOperationStep(processStep)) {
                updateSkuList(approvalDetailPOList, skuList, convertEstimateApprovalPassDO.getApprovalId(), estimateApprovalPO.getOrganizationId());
            }

            // 5. 更新申请表金额信息
            updateApprovalPrice(convertEstimateApprovalPassDO.getApprovalId(), approvalDetailPOList);

            // 6. 最后一步审核处理(保存history、同步旺铺等)
            if (isFinalStep(processStep)) {
                handleFinalStep(estimateApprovalPO, approvalDetailPOList, processUserDO, shipPeriod, processStep);
            }
        } finally {
            redisUtil.unLock(EstimateConstants.OPERATION_LOCK,String.valueOf(convertEstimateApprovalPassDO.getApprovalId()));
        }
    }

    /**
     * 获取并检查申请信息
     */
    private EstimateApprovalPO getAndCheckApproval(Long approvalId) {
        EstimateApprovalPO estimateApprovalPO = estimateRepository.selectApprovalById(approvalId);
        if (Objects.isNull(estimateApprovalPO)) {
            throw new ApplicationException(EstimateErrorMsgEnum.APPROVAL_NOT_EXIST.getMsg());
        }
        return estimateApprovalPO;
    }

    /**
     * 获取并检查货需期别ID
     */
    private Long getAndCheckShipPeriod(Long scheduleId) {
        Long shipPeriod = estimateSkuRepository.selectShipPeriodBySchedule(scheduleId);
        if (Objects.isNull(shipPeriod)) {
            throw new ApplicationException(EstimateErrorMsgEnum.SHIP_PERIOD_NOT_EXIST.getMsg());
        }
        return shipPeriod;
    }

    /**
     * 构建预估详情列表
     */
    private List<EstimateDetail> buildEstimateDetails(List<EstimateApprovalDetailDO> skuList, Long approvalId) {
        if (!CollectionUtils.isEmpty(skuList)) {
            return skuList.stream()
                .map(item -> EstimateDetail.builder()
                    .sku(item.getSku())
                    .currentCount(item.getAuditCount())
                    .salePrice(item.getSalePrice())
                    .rawCount(0)
                    .build())
                .collect(Collectors.toList());
        } else {
            // 如果没有物料信息，则从历史表中获取
            List<EstimateApprovalDetailPO> estimateApprovalDetailPOS = Optional.ofNullable(
                estimateApprovalDetailMapper.selectList(
                    new LambdaQueryWrapper<EstimateApprovalDetailPO>()
                        .eq(EstimateApprovalDetailPO::getApprovalId, approvalId)
                        .eq(EstimateApprovalDetailPO::getDeleteFlag, 0)
                )).orElse(new ArrayList<>());
            return estimateApprovalDetailPOS.stream()
                .map(item -> EstimateDetail.builder()
                    .sku(item.getSku())
                    .currentCount(item.getAuditQuantity())
                    .salePrice(item.getSalePrice())
                    .rawCount(0)
                    .build())
                .collect(Collectors.toList());
        }
    }

    /**
     * 检查额度是否超管控
     */
    private void checkEstimateControl(EstimateApprovalPO estimateApprovalPO, List<EstimateDetail> details) {
        EstimateCheckDO estimateCheckDO = EstimateCheckDO.builder()
            .theYearMonth(estimateApprovalPO.getMonth())
            .organizationId(estimateApprovalPO.getOrganizationId())
            .approvalId(estimateApprovalPO.getApprovalId())
            .estimateDetailList(details)
            .build();
        estimateControlService.checkEstimateControl(estimateCheckDO);
    }

    /**
     * 获取申请详情列表
     */
    private List<EstimateApprovalDetailPO> getApprovalDetailList(Long approvalId) {
        return Optional.ofNullable(estimateRepository.selectApprovalDetailByApprovalId(approvalId))
            .orElse(new ArrayList<>());
    }

    /**
     * 判断是否为营运/产销步骤
     */
    private boolean isOperationStep(Integer processStep) {
        return processStep != null && (processStep == 40 || processStep == 50);
    }

    /**
     * 更新物料确认信息
     */
    private void updateSkuList(List<EstimateApprovalDetailPO> approvalDetailPOList, List<EstimateApprovalDetailDO> skuList, 
                              Long approvalId, String organizationId) {
        saveOrUpdateSkuList(approvalDetailPOList, skuList, approvalId, organizationId, 2);
    }

    /**
     * 更新申请表金额信息
     */
    private void updateApprovalPrice(Long approvalId, List<EstimateApprovalDetailPO> approvalDetailPOList) {
        BigDecimal auditPrice = calculateAuditPrice(approvalDetailPOList);
        BigDecimal estimatePrice = calculateEstimatePrice(approvalDetailPOList);
        estimateRepository.savePrice(approvalId, estimatePrice, auditPrice);
    }

    /**
     * 计算审核价格
     */
    private BigDecimal calculateAuditPrice(List<EstimateApprovalDetailPO> approvalDetailPOList) {
        return approvalDetailPOList.stream()
            .map(m -> new BigDecimal(Optional.ofNullable(m.getAuditQuantity()).orElse(0))
                .multiply(Optional.ofNullable(m.getSalePrice()).orElse(BigDecimal.ZERO)))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算预估价格
     */
    private BigDecimal calculateEstimatePrice(List<EstimateApprovalDetailPO> approvalDetailPOList) {
        return approvalDetailPOList.stream()
            .map(m -> new BigDecimal(m.getEstimateQuantity()).multiply(m.getSalePrice()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 判断是否为最后一步审核
     */
    private boolean isFinalStep(Integer processStep) {
        return processStep != null && (processStep == 10 || processStep == 20 || processStep == 50);
    }

    /**
     * 处理最后一步审核(保存history、同步旺铺等)
     */
    private void handleFinalStep(EstimateApprovalPO estimateApprovalPO, List<EstimateApprovalDetailPO> approvalDetailPOList, 
                                ProcessUserDO processUserDO, Long shipPeriod, Integer processStep) {
        // 保存history信息
        Integer businessGroupById = organizationMapper.getBusinessGroupById(estimateApprovalPO.getOrganizationId());
        String storeName = estimateRepository.getStoreName(estimateApprovalPO.getOrganizationId());
        saveHistory(estimateApprovalPO, approvalDetailPOList, processUserDO, businessGroupById, storeName, shipPeriod);

        // 获取组织信息用于后续处理
        EstimateApprovalDetailPO firstDetail = approvalDetailPOList.stream().findFirst().orElse(null);
        if (Objects.isNull(firstDetail)) {
            return;
        }
        
        String organizationId = firstDetail.getOrganizationId();
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        BigDecimal auditPrice = calculateAuditPrice(approvalDetailPOList);

        // 营业所审核同步旺铺
        if (processStep == 10) {
            synEstimate(approvalDetailPOList, auditPrice, estimateApprovalPO, processUserDO, 2);
        }
        
        // 产销审核且为分公司时同步SKU销售数据
        if (processStep == 50 && "company".equals(organizationType)) {
            syncSkuSaleData(estimateApprovalPO, processUserDO, organizationId,shipPeriod);
        }
    }

    /**
     * 同步SKU销售数据
     */
    private void syncSkuSaleData(EstimateApprovalPO estimateApprovalPO, ProcessUserDO processUserDO, String organizationId, Long shipPeriod) {
        // 查询历史数据
        List<EstimateHistoryDTO> historyDTOS = estimateRepository.selectHistoryByOrgCode(
            estimateApprovalPO.getMonth(), estimateApprovalPO.getOrganizationId());
        
        if (CollectionUtils.isEmpty(historyDTOS)) {
            log.warn("未找到历史数据，组织ID: {}, 月份: {}", estimateApprovalPO.getOrganizationId(), estimateApprovalPO.getMonth());
            return;
        }

        // 获取组织基础信息
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        String productGroupCode = getProductGroupCode(organizationId);

        // 同步SKU销售数据
        syncSkuSaleModel(historyDTOS, estimateApprovalPO, processUserDO, organizationName, productGroupCode);
        
        // 同步超卖管控数据
        syncOverSaleControlData(historyDTOS, estimateApprovalPO, processUserDO, organizationName, productGroupCode, shipPeriod);
    }

    /**
     * 获取产品组编码
     */
    private String getProductGroupCode(String organizationId) {
        Integer businessGroupId = organizationMapper.getBusinessGroupById(organizationId);
        return sfaBusinessGroupMapper.selectById(businessGroupId).getBusinessGroupCode();
    }

    /**
     * 同步SKU销售模型数据
     */
    private void syncSkuSaleModel(List<EstimateHistoryDTO> historyDTOS, EstimateApprovalPO estimateApprovalPO, 
                                 ProcessUserDO processUserDO, String organizationName, String productGroupCode) {
        // 构建SKU销售模型
        SaveSkuSaleModel saveSkuSaleModel = buildSkuSaleModel(historyDTOS, estimateApprovalPO, processUserDO, organizationName, productGroupCode);
        
        // 调用外部接口同步
        estimateConnectorUtil.saveSkuSale(saveSkuSaleModel);
    }

    /**
     * 构建SKU销售模型
     */
    private SaveSkuSaleModel buildSkuSaleModel(List<EstimateHistoryDTO> historyDTOS, EstimateApprovalPO estimateApprovalPO,
                                              ProcessUserDO processUserDO, String organizationName, String productGroupCode) {
        SaveSkuSaleModel saveSkuSaleModel = new SaveSkuSaleModel();
        saveSkuSaleModel.setCompanyName(organizationName);
        saveSkuSaleModel.setEmployeeId(processUserDO.getEmployeeId());
        saveSkuSaleModel.setEstimateMonth(estimateApprovalPO.getMonth());
        saveSkuSaleModel.setProductGroupId(productGroupCode);
        
        // 聚合SKU数据
        List<SkuModel> skuModels = aggregateSkuData(historyDTOS);
        saveSkuSaleModel.setSkuList(skuModels);
        
        return saveSkuSaleModel;
    }

    /**
     * 聚合SKU数据
     */
    private List<SkuModel> aggregateSkuData(List<EstimateHistoryDTO> historyDTOS) {
        Map<String, BigDecimal> skuCountMap = historyDTOS.stream()
            .filter(this::isValidEstimateCount)
            .collect(Collectors.groupingBy(EstimateHistoryDTO::getSku, 
                Collectors.reducing(BigDecimal.ZERO, 
                    e -> new BigDecimal(e.getEstimateCount()), 
                    BigDecimal::add)));
        
        return skuCountMap.entrySet().stream()
            .map(this::convertToSkuModel)
            .collect(Collectors.toList());
    }

    /**
     * 验证预估数量是否有效
     */
    private boolean isValidEstimateCount(EstimateHistoryDTO dto) {
        return Objects.nonNull(dto.getEstimateCount()) && dto.getEstimateCount() > 0;
    }

    /**
     * 转换为SKU模型
     */
    private SkuModel convertToSkuModel(Map.Entry<String, BigDecimal> entry) {
        SkuModel skuModel = new SkuModel();
        skuModel.setSku(entry.getKey());
        skuModel.setEstimateBoxNumber(entry.getValue());
        return skuModel;
    }

    /**
     * 同步超卖管控数据
     */
    private void syncOverSaleControlData(List<EstimateHistoryDTO> historyDTOS, EstimateApprovalPO estimateApprovalPO,
                                        ProcessUserDO processUserDO, String organizationName, String productGroupCode, Long shipPeriod) {
        // 构建超卖管控请求
        SkuOverSaleControlSaveWithPhaseRequest request = buildOverSaleControlRequest(
            historyDTOS, estimateApprovalPO, processUserDO, organizationName, productGroupCode, shipPeriod);
        
        // 调用外部接口同步
        estimateConnectorUtil.saveSkuOverSaleControlInfoWithPhase(request);
    }

    /**
     * 构建超卖管控请求
     */
    private SkuOverSaleControlSaveWithPhaseRequest buildOverSaleControlRequest(List<EstimateHistoryDTO> historyDTOS,
                                                                              EstimateApprovalPO estimateApprovalPO,
                                                                              ProcessUserDO processUserDO,
                                                                              String organizationName,
                                                                              String productGroupCode,
                                                                              Long shipPeriod) {
        SkuOverSaleControlSaveWithPhaseRequest request = new SkuOverSaleControlSaveWithPhaseRequest();
        request.setEstimateMonth(estimateApprovalPO.getMonth());
        request.setEstimatePhase(shipPeriod.intValue());
        request.setCompanyName(organizationName);
        request.setEmployeeId(processUserDO.getEmployeeId());
        request.setProductGroupId(productGroupCode);
        
        // 构建SKU列表（按货需期别过滤）
        List<SkuEstimateBoxNumberDTO> skuList = buildSkuEstimateList(historyDTOS, shipPeriod);
        request.setSkuList(skuList);
        
        return request;
    }

    /**
     * 构建SKU预估列表
     */
    private List<SkuEstimateBoxNumberDTO> buildSkuEstimateList(List<EstimateHistoryDTO> historyDTOS, Long shipPeriod) {
        return historyDTOS.stream()
            .filter(dto -> Objects.equals(dto.getShipPeriodId(), shipPeriod))
            .collect(Collectors.groupingBy(EstimateHistoryDTO::getSku))
            .values()
            .stream()
            .map(skuGroup -> {
                SkuEstimateBoxNumberDTO skuEstimateBoxNumberDTO = new SkuEstimateBoxNumberDTO();
                skuEstimateBoxNumberDTO.setSku(skuGroup.get(0).getSku());
                Integer totalEstimateCount = skuGroup.stream()
                    .map(EstimateHistoryDTO::getEstimateCount)
                    .reduce(0, Integer::sum);
                skuEstimateBoxNumberDTO.setEstimateBoxNumber(totalEstimateCount);
                return skuEstimateBoxNumberDTO;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换为SKU预估DTO
     */
    private SkuEstimateBoxNumberDTO convertToSkuEstimateDto(EstimateHistoryDTO dto) {
        SkuEstimateBoxNumberDTO skuEstimateBoxNumberDTO = new SkuEstimateBoxNumberDTO();
        skuEstimateBoxNumberDTO.setSku(dto.getSku());
        skuEstimateBoxNumberDTO.setEstimateBoxNumber(dto.getEstimateCount());
        return skuEstimateBoxNumberDTO;
    }

    @Override
    public List<EstimateSubmitVO> selectEstimateSubmit(EstimateSubmitSearchRequest estimateSubmitSearchRequest, List<Integer> roleIds) {

        List<EstimateSubmitDTO> estimateSubmitDTOS = estimateRepository.selectEstimateSubmit(estimateSubmitSearchRequest);

        if(CollectionUtils.isEmpty(estimateSubmitDTOS)){
            return Collections.emptyList();
        }

        return EstimateConvert.convertSubmitVo(estimateSubmitDTOS,roleIds,estimateSubmitSearchRequest.getProcessStep());
    }

    @Override
    @Transactional
    public void reject(EstimateApprovalRejectDO convertEstimateApprovalRejectDO, ProcessUserDO processUserDO) {
        // 增加分布式锁
        if(!redisUtil.setLockIfAbsent(EstimateConstants.OPERATION_LOCK,String.valueOf(convertEstimateApprovalRejectDO.getApprovalId()),5, TimeUnit.SECONDS)){
            throw new ApplicationException(EstimateErrorMsgEnum.REPEATED_OPERATION.getMsg());
        }

        try {
            Integer processStep = convertEstimateApprovalRejectDO.getProcessStep();

            // 获取申请信息
            EstimateApprovalPO estimateApprovalPO = estimateRepository.selectApprovalById(convertEstimateApprovalRejectDO.getApprovalId());
            if (Objects.isNull(estimateApprovalPO)) {
                throw new ApplicationException(EstimateErrorMsgEnum.APPROVAL_NOT_EXIST.getMsg());
            }

            // 获取提报的物料信息
            List<EstimateApprovalDetailPO> approvalDetailPOList = Optional.ofNullable(estimateRepository.selectApprovalDetailByApprovalId(convertEstimateApprovalRejectDO.getApprovalId())).orElse(new ArrayList<>());

            // 更新物料确认信息
            saveOrUpdateSkuList(approvalDetailPOList, new ArrayList<>(), convertEstimateApprovalRejectDO.getApprovalId(), estimateApprovalPO.getOrganizationId(), 2);

            // 更新申请表信息
            BigDecimal auditPrice = approvalDetailPOList.stream().map(m -> new BigDecimal(m.getAuditQuantity()).multiply(m.getSalePrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal estimatePrice = approvalDetailPOList.stream().map(m -> new BigDecimal(m.getEstimateQuantity()).multiply(m.getSalePrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
            estimateRepository.savePrice(convertEstimateApprovalRejectDO.getApprovalId(), estimatePrice, auditPrice);
            // 同步旺铺
            if (processStep == 10) {
                // 同步旺铺
                synEstimate(approvalDetailPOList, auditPrice, estimateApprovalPO, processUserDO, 3);
            }
        }finally {
            redisUtil.unLock(EstimateConstants.OPERATION_LOCK,String.valueOf(convertEstimateApprovalRejectDO.getApprovalId()));
        }
    }

    @Override
    @Transactional
    public void submit(EstimateSubmitDO estimateSubmitDO, ProcessUserDO processUserDO) {
        // 增加分布式锁
        if(!redisUtil.setLockIfAbsent(EstimateConstants.SUBMIT_LOCKED,estimateSubmitDO.getOrganizationId(),5, TimeUnit.SECONDS)){
            throw new ApplicationException(EstimateErrorMsgEnum.REPEATED_OPERATION.getMsg());
        }



        try {
            // 根据排期ID获取排期
            EstimateSchedulePO estimateSchedulePO = estimateSkuRepository.selectScheduleById(estimateSubmitDO.getScheduleId());
            if (Objects.isNull(estimateSchedulePO)) {
                throw new ApplicationException(EstimateErrorMsgEnum.SCHEDULE_NOT_MATCH.getMsg());
            }


            Long approvalId = null;
            // 根据排期号，组织ID检查是否有提交过，有的话就是被驳回的
            EstimateApprovalPO estimateApprovalPO = estimateRepository.selectApprovalByScheduleId(estimateSubmitDO.getOrganizationId(), estimateSubmitDO.getScheduleId());
            // 驳回重新提交
            if (Objects.nonNull(estimateApprovalPO) && !estimateSubmitDO.isRedo()) {
                // 更新instanceId等信息
                estimateApprovalPO.setInstanceId(estimateSubmitDO.getInstanceId());
                estimateApprovalPO.setIsSubmit(EstimateSubmitEnum.SUBMIT.getType());
                estimateApprovalPO.setUpdateTime(LocalDateTime.now());
                estimateApprovalPO.setUpdateUserId(processUserDO.getEmployeeId());
                estimateRepository.updateEstimateApproval(estimateApprovalPO);
            }
            // 新申请
            else if(Objects.isNull(estimateApprovalPO)){
                // 创建销售预估申请
                estimateApprovalPO = EstimateFactory.buildEstimateApproval("0", estimateSchedulePO.getTheYearMonth(), estimateSubmitDO.getApplyPositionId()
                        , estimateSubmitDO.getOrganizationId(), processUserDO.getEmployeeName(), EstimateSubmitEnum.SUBMIT.getType(), estimateSchedulePO.getType(), estimateSchedulePO.getScheduleId(), estimateSubmitDO.getInstanceId());
                estimateRepository.saveEstimateApproval(estimateApprovalPO);
            }else {
                approvalId = estimateApprovalPO.getApprovalId();
            }
            List<EstimateDetail> details = new ArrayList<>();
            estimateSubmitDO.getSkuList().forEach(item -> {
                details.add(EstimateDetail.builder().sku(item.getSku()).currentCount(item.getAuditCount()).salePrice(item.getSalePrice()).rawCount(0).build());
            });
            // 检查额度是否超管控
            EstimateCheckDO estimateCheckDO = EstimateCheckDO.builder().theYearMonth(estimateSchedulePO.getTheYearMonth())
                    .organizationId(estimateSubmitDO.getOrganizationId())
                    .approvalId(approvalId)
                    .estimateDetailList(details)
                    .build();
            estimateControlService.checkEstimateControl(estimateCheckDO);

            // 获取提报的物料信息
            List<EstimateApprovalDetailPO> approvalDetailPOList = Optional.ofNullable(estimateRepository.selectApprovalDetailByApprovalId(estimateApprovalPO.getApprovalId())).orElse(new ArrayList<>());

            if(estimateSubmitDO.isRedo()){
                if(!CollectionUtils.isEmpty(approvalDetailPOList)){
                    approvalDetailPOList.forEach(e -> {
                        e.setDeleteFlag(1);
                        e.setUpdateTime(LocalDateTime.now());
                        estimateRepository.updateApprovalDetail(e);
                    });
                }
            }



            // 更新物料确认信息
            saveOrUpdateSkuList(approvalDetailPOList, estimateSubmitDO.getSkuList(), estimateApprovalPO.getApprovalId(), estimateApprovalPO.getOrganizationId(), 1);

            // 更新申请表信息
            BigDecimal auditPrice = approvalDetailPOList.stream().filter(f -> f.getDeleteFlag() == 0).map(m -> new BigDecimal(Optional.ofNullable(m.getAuditQuantity()).orElse(0)).multiply(m.getSalePrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal estimatePrice = approvalDetailPOList.stream().filter(f -> f.getDeleteFlag() == 0).map(m -> new BigDecimal(Optional.ofNullable(m.getEstimateQuantity()).orElse(0)).multiply(m.getSalePrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
            estimateRepository.savePrice(estimateApprovalPO.getApprovalId(), estimatePrice, auditPrice);
        }finally {
            redisUtil.unLock(EstimateConstants.SUBMIT_LOCKED,estimateSubmitDO.getOrganizationId());
        }
    }

    @Override
    public EstimateApprovalDetailVO selectSubmitSku(EstimateSubmitSkuRequest estimateSubmitSkuRequest) {
        EstimateSchedulePO estimateSchedulePO = estimateSkuRepository.selectScheduleById(estimateSubmitSkuRequest.getScheduleId());
        if(Objects.isNull(estimateSchedulePO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SCHEDULE_NOT_MATCH.getMsg());
        }

        EstimateShipPeriodPO estimateShipPeriodPO = estimateSkuRepository.selectShipPeriodById(estimateSchedulePO.getShipPeriodId());
        if(Objects.isNull(estimateShipPeriodPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SHIP_PERIOD_NOT_EXIST.getMsg());
        }

        EstimateApprovalDetailVO estimateApprovalDetailVO = new EstimateApprovalDetailVO();
        estimateApprovalDetailVO.setTypeStr(EstimateTypeEnum.getTypeName(estimateSchedulePO.getType()));
        String year = estimateSchedulePO.getTheYearMonth().split("-")[0];
        String month = estimateSchedulePO.getTheYearMonth().split("-")[1];
        estimateApprovalDetailVO.setStartDate(estimateSchedulePO.getStartDate().toString());
        estimateApprovalDetailVO.setEndDate(estimateSchedulePO.getEndDate().toString());
        estimateApprovalDetailVO.setProcessResult(EstimateConstants.NOT_SUBMIT);

        String applyUserName = organizationMapper.getOrganizationName(estimateSubmitSkuRequest.getOrganizationId());


        // 设置标题
        estimateApprovalDetailVO.setTitle(MessageFormat.format(EstimateConstants.APPROVAL_TITLE,year,month,estimateShipPeriodPO.getName(),applyUserName));

        String companyCode = estimateSubmitSkuRequest.getOrganizationId();
        String organizationType = organizationMapper.getOrganizationType(companyCode);
        if("department".equals(organizationType)){
            companyCode = organizationMapper.getOrganizationParentId(companyCode);
        }
        estimateSubmitSkuRequest.setCompanyCode(companyCode);

        // 读取旺铺数据可提报sku
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(organizationMapper.getBusinessGroupById(estimateSubmitSkuRequest.getOrganizationId()));
        QuerySkuRequest querySkuRequest = new QuerySkuRequest();
        querySkuRequest.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
        querySkuRequest.setCommitType(estimateSchedulePO.getType());
        querySkuRequest.setSaleCompanyName(organizationMapper.getOrganizationName(companyCode));
        querySkuRequest.setSupplyPhaseFlag(estimateShipPeriodPO.getId().intValue());
        List<SkuInfoDTO> skuInfoDTOS = Optional.ofNullable(estimateClient.querySkuInfo(querySkuRequest)).orElse(new ArrayList<>());
        List<String> skuList = skuInfoDTOS.stream().map(SkuInfoDTO::getSku).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuList)){
            throw new ApplicationException("无可提报SKU");
        }

        // 获取上月提报的额度
        String theYearMonth = estimateSchedulePO.getTheYearMonth();
        String lastYearMonth = LocalDate.parse(theYearMonth + "-01").minusMonths(1L).toString().substring(0, 7);
        List<EstimateHistoryDTO> lastYearMonthHistoryList = Optional.ofNullable(estimateRepository.selectHistoryBySku(lastYearMonth,estimateSubmitSkuRequest.getOrganizationId(),skuList)).orElse(new ArrayList<>());


        // 获取上上月
        String lastLastYearMonth = LocalDate.parse(theYearMonth + "-01").minusMonths(2L).toString().substring(0, 7);
        List<EstimateHistoryDTO> lastLastYearMonthHistoryList = Optional.ofNullable(estimateRepository.selectHistoryBySku(lastLastYearMonth,estimateSubmitSkuRequest.getOrganizationId(),skuList)).orElse(new ArrayList<>());
        // 将旺铺返回的结构转换成DTO
        List<EstimateApprovalItemDTO> estimateApprovalItemDTOList =  convertDTO(skuInfoDTOS,lastYearMonth,lastLastYearMonth,lastYearMonthHistoryList,lastLastYearMonthHistoryList);




        // 去大数据表查询sku实际销售箱数
        List<EstimateActualInfoDTO> actualInfoDTOS = new ArrayList<>();
        if(!CollectionUtils.isEmpty(skuList)){
            actualInfoDTOS =  Optional.ofNullable(estimateRepository.selectEstimateActualInfo(estimateSubmitSkuRequest.getOrganizationId(),estimateSchedulePO.getTheYearMonth(),skuList)).orElse(new ArrayList<>());
        }

        // 获取上次提报
        List<EstimateSkuDTO> lastEstimateList = Optional.ofNullable(estimateRepository.selectLastEstimateFromHistory(estimateSubmitSkuRequest.getOrganizationId(),
                estimateSchedulePO.getTheYearMonth(),estimateShipPeriodPO.getId())).orElse(new ArrayList<>()).stream().map(EstimateSkuDTO::build).collect(Collectors.toList());



        // 获取下级提报
        List<EstimateSkuDTO> lowerEstimateList = Optional.ofNullable(estimateRepository.selectLowerEstimate(estimateSubmitSkuRequest.getOrganizationId(),organizationMapper.getOrganizationType(estimateSubmitSkuRequest.getOrganizationId()),estimateSchedulePO.getTheYearMonth())).orElse(new ArrayList<>());

        // 获取发货仓信息
        String storeName = estimateRepository.getStoreName(companyCode);

        // 获取MOQ信息
        List<SkuInventory> skuInventories =  Optional.ofNullable(estimateRepository.selectSkuInventory(storeName,skuList,LocalDate.now().toString().substring(0,7),organizationMapper.getBusinessGroupById(companyCode))).orElse(new ArrayList<>());

        // 获取当月已提交的sku信息
        List<MOQ> moqList =  Optional.ofNullable(estimateRepository.selectCurrentMOQ(skuList,estimateSchedulePO.getTheYearMonth(),null)).orElse(new ArrayList<>());

        // 获取预订单数量
        List<SkuAdvancedOrder> skuAdvancedOrderList =  Optional.ofNullable(estimateRepository.selectAdvancedOrderBox(estimateSubmitSkuRequest.getOrganizationId(),organizationType,skuList,estimateSchedulePO.getTheYearMonth())).orElse(new ArrayList<>());



        List<EstimateApprovalItemVO> estimateApprovalItemVOS = EstimateConvert.convertToItemVO(estimateApprovalItemDTOList,
                actualInfoDTOS,lastEstimateList,lowerEstimateList,skuInventories,moqList,skuAdvancedOrderList);

        List<EstimateApprovalDetailPO> approvalDetailPOList = Optional.ofNullable(estimateRepository.selectLastSubmitVO(estimateSubmitSkuRequest.getOrganizationId(), estimateSubmitSkuRequest.getScheduleId())).orElse(new ArrayList<>());

        estimateApprovalItemVOS.forEach(e -> {
            Optional<EstimateApprovalDetailPO> first = approvalDetailPOList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(first.isPresent()){
                EstimateApprovalDetailPO estimateApprovalDetailPO = first.get();
                e.setEstimateCount(estimateApprovalDetailPO.getEstimateQuantity());
            }
        });

        estimateApprovalDetailVO.setEstimateApprovalItemVOList(estimateApprovalItemVOS);
        return estimateApprovalDetailVO;
    }

    private List<EstimateApprovalItemDTO> convertDTO(List<SkuInfoDTO> skuInfoDTOS, String lastYearMonth, String lastLastYearMonth, List<EstimateHistoryDTO> lastYearMonthHistoryList, List<EstimateHistoryDTO> lastLastYearMonthHistoryList) {

        List<EstimateApprovalItemDTO> list = new ArrayList<>();

        skuInfoDTOS.forEach(e -> {
            EstimateApprovalItemDTO estimateApprovalItemDTO = new EstimateApprovalItemDTO();
            BeanUtils.copyProperties(e,estimateApprovalItemDTO);
            estimateApprovalItemDTO.setFullCaseSpec(e.getSkuSpec());
            estimateApprovalItemDTO.setSpu(e.getSpuId());
            estimateApprovalItemDTO.setSalePrice(e.getThirdOrderPrice());
            estimateApprovalItemDTO.setLastMonth(lastYearMonth);
            estimateApprovalItemDTO.setLastLastMonth(lastLastYearMonth);
            List<String> tagNameList = e.getTagNameList();
            if(!CollectionUtils.isEmpty(tagNameList)){
                estimateApprovalItemDTO.setTagName(String.join(",",tagNameList));
            }

            Optional<EstimateHistoryDTO> lastOptional = lastYearMonthHistoryList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(lastOptional.isPresent()){
                EstimateHistoryDTO estimateHistoryDTO = lastOptional.get();
                estimateApprovalItemDTO.setLastEstimateCount(estimateHistoryDTO.getEstimateCount());
            }else{
                estimateApprovalItemDTO.setLastEstimateCount(0);
            }

            Optional<EstimateHistoryDTO> lastLastOptional = lastLastYearMonthHistoryList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(lastLastOptional.isPresent()){
                EstimateHistoryDTO estimateHistoryDTO = lastLastOptional.get();
                estimateApprovalItemDTO.setLastLastEstimateCount(estimateHistoryDTO.getEstimateCount());
            }else{
                estimateApprovalItemDTO.setLastLastEstimateCount(0);
            }

            list.add(estimateApprovalItemDTO);
        });

        return list;
    }

    @Override
    public List<StoreVO> getStore(int businessGroup) {
        return estimateSkuRepository.getStore(businessGroup);
    }

    @Override
    public List<SkuVO> getSku(Integer businessGroup) {
        return estimateSkuRepository.getSku(businessGroup);
    }

    @Override
    public IPage<EstimateSummaryVO> selectSummary(EstimateSearchRequest estimateSearchRequest) {

        Page<EstimateSummaryVO> page = new Page<>(estimateSearchRequest.getPage(), estimateSearchRequest.getRows());
        List<EstimateSummaryDTO> list = estimateRepository.selectSummary(page,estimateSearchRequest);
        if(CollectionUtils.isEmpty(list)){
            page.setRecords(Collections.emptyList());
            return page;
        }

        Integer submitLayer = estimateSearchRequest.getSubmitLayer();

        // 获取上月数据
        List<EstimateSummaryDTO> lastHistory = Optional.ofNullable(estimateRepository.selectHistoryByResult(list)).orElse(new ArrayList<>());
        // 获取当前MOQ数
        List<MOQ> moqList = Optional.ofNullable(estimateRepository.selectMOQByResult(list)).orElse(new ArrayList<>());

        List<SkuAdvancedOrder> advancedOrders =  Optional.ofNullable(estimateRepository.selectAdvancedOrderBoxByResult(list)).orElse(new ArrayList<>());

        list.forEach(e -> {
            Optional<EstimateSummaryDTO> lastOptional = lastHistory.stream().filter(f -> f.getSku().equals(e.getSku()) && e.getLastMonth().equals(f.getYearMonth()) && e.getOrganizationId().equals(f.getOrganizationId())).findFirst();
            if(lastOptional.isPresent()){
                EstimateSummaryDTO estimateSummaryDTO = lastOptional.get();
                e.setLastEstimateCount(Optional.ofNullable(estimateSummaryDTO.getTotalCount()).orElse(0));
            }else{
                e.setLastEstimateCount(0);
            }

            if(Objects.nonNull(submitLayer) && submitLayer == 30){
                e.setCompanyName(null);
                e.setDepartmentName(null);
            }

            Optional<EstimateSummaryDTO> lastLastOptional = lastHistory.stream().filter(f -> f.getSku().equals(e.getSku()) && e.getLastLastMonth().equals(f.getYearMonth()) && e.getOrganizationId().equals(f.getOrganizationId())).findFirst();
            if(lastLastOptional.isPresent()){
                EstimateSummaryDTO estimateSummaryDTO = lastLastOptional.get();
                e.setLastLastEstimateCount(Optional.ofNullable(estimateSummaryDTO.getTotalCount()).orElse(0));
            }else{
                e.setLastLastEstimateCount(0);
            }

            Optional<MOQ> moqOptional = moqList.stream().filter(f -> f.getOrganizationId().equals(e.getCompanyCode()) && f.getSku().equals(e.getSku()) && f.getTheYearMonth().equals(e.getYearMonth())).findFirst();
            if(moqOptional.isPresent()){
                MOQ moq = moqOptional.get();
                e.setCurrentMOQ(Optional.ofNullable(moq.getCurrentMOQ()).orElse(BigDecimal.ZERO));
            }else{
                e.setCurrentMOQ(BigDecimal.ZERO);
            }

            Optional<SkuAdvancedOrder> advancedOrderOptional = advancedOrders.stream().filter(f -> f.getSku().equals(e.getSku()) && f.getOrganizationId().equals(e.getOrganizationId()) && f.getTheYearMonth().equals(e.getYearMonth())).findFirst();
            if(advancedOrderOptional.isPresent()){
                SkuAdvancedOrder skuAdvancedOrder = advancedOrderOptional.get();
                e.setAdvancedOrderBox(Optional.ofNullable(skuAdvancedOrder.getAdvancedOrderBox()).orElse(BigDecimal.ZERO));
            }else{
                e.setAdvancedOrderBox(BigDecimal.ZERO);
            }


            Integer totalCount = e.getTotalCount();
            e.setTotalCount(totalCount + e.getAdvancedOrderBox().intValue());
            e.setTotalPrice(new BigDecimal(e.getTotalCount()).multiply(Optional.ofNullable(e.getThirdOrderPrice()).orElse(BigDecimal.ZERO)).intValue());
        });





        // 获取上月，上上月实际箱数
        List<EstimateActualInfoDTO> actualInfoDTOS = Optional.ofNullable(estimateRepository.selectSummaryActualInfo(list)).orElse(new ArrayList<>());
        page.setRecords(EstimateConvert.convertToSummerVO(list,actualInfoDTOS));
        return page;
    }

    @Override
    public EstimateDetailInfoVO selectDetail(EstimateSearchRequest estimateSearchRequest) {
        Page<EstimateDetailVO> page = new Page<>(estimateSearchRequest.getPage(), estimateSearchRequest.getRows());

        List<EstimateDetailDTO> estimateDetailDTOS = Optional.ofNullable(estimateRepository.selectDetail(page,estimateSearchRequest)).orElse(Collections.emptyList());
        if(!CollectionUtils.isEmpty(estimateDetailDTOS)){
            page.setRecords(EstimateConvert.convertToDetailVO(estimateDetailDTOS,estimateSearchRequest.getSubmitLayer()));
        }

        EstimateApprovalSummaryDTO estimateApprovalSummaryDTO = Optional.ofNullable(estimateRepository.selectDetailSummary(estimateSearchRequest)).orElse(EstimateApprovalSummaryDTO.builder()
                .totalEstimateQuantity(BigDecimal.ZERO).totalAuditQuantity(BigDecimal.ZERO).totalEstimatePrice(BigDecimal.ZERO).totalAuditPrice(BigDecimal.ZERO).build());

        return EstimateDetailInfoVO.builder().page(page).totalAuditPrice(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalAuditPrice()).orElse(BigDecimal.ZERO).intValue())
                .totalAuditQuantity(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalAuditQuantity()).orElse(BigDecimal.ZERO).intValue())
                .totalEstimatePrice(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalEstimatePrice()).orElse(BigDecimal.ZERO).intValue())
                .totalEstimateQuantity(Optional.ofNullable(estimateApprovalSummaryDTO.getTotalEstimateQuantity()).orElse(BigDecimal.ZERO).intValue()).build();
    }

    @Override
    public void exportDetail(EstimateSearchRequest estimateSearchRequest) {
        List<EstimateDetailDTO> estimateDetailDTOS = Optional.ofNullable(estimateRepository.selectDetail(null,estimateSearchRequest)).orElse(Collections.emptyList());
        List<EstimateDetailVO> estimateDetailVOS = EstimateConvert.convertToDetailVO(estimateDetailDTOS, estimateSearchRequest.getSubmitLayer());


        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "销售预估明细" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), EstimateDetailVO.class, estimateDetailVOS);
        assert response != null;
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public void exportSummary(EstimateSearchRequest estimateSearchRequest) {

        List<EstimateSummaryDTO> list = Optional.ofNullable(estimateRepository.selectSummary(null,estimateSearchRequest)).orElse(new ArrayList<>());

        if(Objects.nonNull(estimateSearchRequest.getSubmitLayer()) && estimateSearchRequest.getSubmitLayer() == 30){
            list.forEach(e -> {
                e.setCompanyName(null);
                e.setDepartmentName(null);
            });

        }

//        if(!CollectionUtils.isEmpty(list) && list.size() > 1000){
//            throw new ApplicationException("数据量过大，无法导出");
//        }
        // 获取上月，上上月实际箱数
//        List<EstimateActualInfoDTO> actualInfoDTOS = Optional.ofNullable(estimateRepository.selectSummaryActualInfo(list)).orElse(new ArrayList<>());
        // 获取预定单数
//        List<SkuAdvancedOrder> advancedOrders =  Optional.ofNullable(estimateRepository.selectAdvancedOrderBoxByResult(list)).orElse(new ArrayList<>());
//        if(!CollectionUtils.isEmpty(list)){
//            list.forEach(e -> {
//                Optional<SkuAdvancedOrder> advancedOrderOptional = advancedOrders.stream().filter(f -> f.getOrganizationId().equals(e.getOrganizationId()) && f.getSku().equals(e.getSku())).findFirst();
//                if(advancedOrderOptional.isPresent()){
//                    SkuAdvancedOrder skuAdvancedOrder = advancedOrderOptional.get();
//                    e.setAdvancedOrderBox(skuAdvancedOrder.getAdvancedOrderBox());
//                }
//            });
//        }


        List<EstimateSummaryVO> estimateSummaryVOS = EstimateConvert.convertToSummerVO(list, Collections.emptyList());



        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "销售预估汇总" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), EstimateSummaryVO.class, estimateSummaryVOS);
        assert response != null;
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public int getEstimateProcessCount(List<String> orgCodes, int positionTypeId, List<Integer> roleIds) {

        String flowCode = EstimateConstants.COMPANY_FLOW_CODE;
        if(positionTypeId == 10){
            flowCode = EstimateConstants.CEO_FLOW_CODE;
        }else if(positionTypeId == 2){
            flowCode = EstimateConstants.DEPARTMENT_FLOW_CODE;
        }

        String orgType = organizationMapper.getOrganizationType(orgCodes.stream().findFirst().orElse(""));

        return estimateRepository.getEstimateProcessCount(orgCodes,orgType,flowCode,roleIds);

    }

    @Override
    public int getSubmitCount(List<String> orgCodes, int positionTypeId) {
        String flowCode = EstimateConstants.COMPANY_FLOW_CODE;
        if(positionTypeId == 10){
            flowCode = EstimateConstants.CEO_FLOW_CODE;
        }

   
        String orgType = organizationMapper.getOrganizationType(orgCodes.stream().findFirst().orElse(""));


        return estimateRepository.getSubmitCount(orgCodes,orgType);
    }

    @Override
    @Transactional
    public void cancel(String saleEstimateNo) {
        EstimateApprovalPO estimateApprovalPO = estimateRepository.selectEstimateApprovalByEstimateNo(saleEstimateNo);

        if(Objects.isNull(estimateApprovalPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SALE_ESTIMATE_NO_ERROR.getMsg());
        }

        estimateApprovalPO.setDeleteFlag(1);
        estimateRepository.updateEstimateApproval(estimateApprovalPO);
    }

    @Override
    public IPage<EstimateMOQVO> selectMOQ(MOQSearchRequest moqSearchRequest) {
        IPage<EstimateMOQVO> page = new Page<>(moqSearchRequest.getPage(),moqSearchRequest.getRows());
        List<EstimateMOQVO> list = estimateRepository.selectMOQ(page,moqSearchRequest);

        if(!CollectionUtils.isEmpty(list)){
            List<String> skuList = list.stream().map(EstimateMOQVO::getSku).collect(Collectors.toList());
            // 获取当前产品组总部code
            String zbOrgCode = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
            // 获取总部预定单数量
            List<SkuAdvancedOrder> skuAdvancedOrderList = Optional.ofNullable(estimateRepository.selectZBAdvancedOrder(zbOrgCode,moqSearchRequest.getYearMonth(),skuList)).orElse(new ArrayList<>());

            list.forEach(e -> {
                Optional<SkuAdvancedOrder> advancedOrderOptional = skuAdvancedOrderList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
                if(advancedOrderOptional.isPresent() && e.getStatus() != EstimateAdjustStatusEnum.ADJUST_NOT_PRODUCT.getStatus()){
                    SkuAdvancedOrder skuAdvancedOrder = advancedOrderOptional.get();
                    BigDecimal moq = Optional.ofNullable(e.getMOQ()).orElse(BigDecimal.ZERO);
                    BigDecimal totalBox = Optional.ofNullable(e.getTotalBox()).orElse(BigDecimal.ZERO);
                    BigDecimal advancedOrderBox = Optional.ofNullable(skuAdvancedOrder.getAdvancedOrderBox()).orElse(BigDecimal.ZERO);
                    // 当前MOQ数需要加上预定单数量
                    e.setTotalBox(totalBox.add(advancedOrderBox));
                    // 重新计算差值
                    e.setDiff(moq.subtract(e.getTotalBox()));
                }
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public MOQDetailVO getMOQDetail(String yearMonth, Long shipPeriodId, String sku) {
        MOQDetailVO moqDetailVO = new MOQDetailVO();
        EstimateShipPeriodPO estimateShipPeriodPO = estimateSkuRepository.selectShipPeriodById(shipPeriodId);
        if(Objects.isNull(estimateShipPeriodPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SHIP_PERIOD_NOT_EXIST.getMsg());
        }

        String monthTitle = LocalDateTimeUtils.formatTime(LocalDate.parse(yearMonth + "-01").atStartOfDay(), "yyyy年MM月");
        String title = monthTitle + " " + estimateShipPeriodPO.getName()+ " " + "销售预估";
        moqDetailVO.setTitle(title);
        moqDetailVO.setSku(sku);
        EstimateSkuPO estimateSkuPO = estimateSkuRepository.selectPriceBySku(sku,null);
        if(Objects.isNull(estimateSkuPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SKU_ERROR.getMsg());
        }
        moqDetailVO.setSkuName(estimateSkuPO.getSkuName());
        moqDetailVO.setFullCaseSpec(estimateSkuPO.getFullCaseSpec());
        moqDetailVO.setFlavor(estimateSkuPO.getFlavor());
        moqDetailVO.setMoq(estimateSkuPO.getMoq());

        List<MOQSkuVO> moqSkuVOList = Optional.ofNullable(estimateRepository.selectMOQDetail(yearMonth,shipPeriodId,sku)).orElse(Collections.emptyList());
        if(CollectionUtils.isEmpty(moqSkuVOList)){
            return moqDetailVO;
        }
        // 获取预定单箱数
        List<EstimateSummaryDTO> queryList = new ArrayList<>();
        moqSkuVOList.forEach(e -> {
            EstimateSummaryDTO estimateSummaryDTO = new EstimateSummaryDTO();
            estimateSummaryDTO.setYearMonth(yearMonth);
            estimateSummaryDTO.setSku(sku);
            estimateSummaryDTO.setOrganizationId(e.getOrganizationId());
            queryList.add(estimateSummaryDTO);
        });

        List<SkuAdvancedOrder> advanceOrderList = Optional.ofNullable(estimateRepository.selectAdvancedOrderBoxByResult(queryList)).orElse(new ArrayList<>());





        // 获取当前有效的调整信息
        List<EstimateAdjustDTO> estimateAdjustDTOList = Optional.ofNullable(estimateSkuRepository.selectAdjust(yearMonth,shipPeriodId,sku)).orElse(new ArrayList<>());

        if(CollectionUtils.isEmpty(estimateAdjustDTOList)){
            moqDetailVO.setStatus(0);
        }else{
            EstimateAdjustDTO estimateAdjustDTO = estimateAdjustDTOList.stream()
                    .findFirst()
                    .orElseThrow(() -> new ApplicationException(EstimateErrorMsgEnum.ADJUST_NOT_EXIST.getMsg()));
            moqDetailVO.setStatus(estimateAdjustDTO.getStatus());
        }

        moqSkuVOList.forEach(e -> {

            Optional<SkuAdvancedOrder> advancedOrderOptional = advanceOrderList.stream().filter(f -> f.getOrganizationId().equals(e.getOrganizationId())).findFirst();
            if(advancedOrderOptional.isPresent()){
                SkuAdvancedOrder skuAdvancedOrder = advancedOrderOptional.get();
                BigDecimal advancedOrderCount = Optional.ofNullable(skuAdvancedOrder.getAdvancedOrderBox()).orElse(BigDecimal.ZERO);
                e.setAdvancedOrderCount(advancedOrderCount.intValue());
            }else{
                e.setAdvancedOrderCount(0);
            }

            Optional<EstimateAdjustDTO> adjustOptional = estimateAdjustDTOList.stream().filter(f -> f.getOrganizationId().equals(e.getOrganizationId()) && f.getType().equals(e.getType())).findFirst();

            if(adjustOptional.isPresent()){
                EstimateAdjustDTO estimateAdjustDTO = adjustOptional.get();
                e.setStatus(estimateAdjustDTO.getStatus());

                Integer detailStatus = estimateAdjustDTO.getDetailStatus();
                e.setStatus(detailStatus);
                Integer finalAuditCount = estimateAdjustDTO.getFinalAuditCount();
                if(Objects.nonNull(finalAuditCount)){
                    // 计算增减箱数
                    Integer auditBox = e.getAuditBox();
                    Integer finalAuditBox =  estimateAdjustDTO.getFinalAuditCount();
                    e.setFinalAuditBox(finalAuditBox);
                    e.setDiff(finalAuditBox - auditBox);
                }
            }else{
                e.setStatus(-1);
            }

            // 计算合计值
            Integer auditBox = e.getAuditBox();
            Integer finalAuditBox = e.getFinalAuditBox();
            Integer advancedOrderCount = e.getAdvancedOrderCount();
            if(Objects.isNull(finalAuditBox)){
                e.setTotalCount(auditBox + advancedOrderCount);
            }else{
                e.setTotalCount(finalAuditBox + advancedOrderCount);
            }

        });

        // 计算当前MOQ
        List<MOQ> moqList = estimateRepository.selectCurrentMOQ(Collections.singletonList(sku), yearMonth,null);
        BigDecimal currentMOQ = BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(moqList)){
            currentMOQ = moqList.stream().findFirst().orElseThrow(() -> new ApplicationException(EstimateErrorMsgEnum.MOQ_NOT_EXIST.getMsg())).getCurrentMOQ();
        }
        // 确认值
        Integer auditCount = moqSkuVOList.stream().map(MOQSkuVO::getAuditBox).reduce(0, Integer::sum);
        // 最终确认值
        Integer finalAuditCount = moqSkuVOList.stream().map(MOQSkuVO::getFinalAuditBox).filter(Objects::nonNull).reduce(0, Integer::sum);

        Integer rawEstimateCount = moqSkuVOList.stream().filter(f -> Objects.isNull(f.getFinalAuditBox())).map(MOQSkuVO::getAuditBox).reduce(0, Integer::sum);



        int current = currentMOQ.intValue();
        Integer status = moqDetailVO.getStatus();
        if(EstimateAdjustStatusEnum.ADJUST_PRODUCT.getStatus() != status && EstimateAdjustStatusEnum.ADJUST_NOT_PRODUCT.getStatus() != status){
            current = current - auditCount + finalAuditCount + rawEstimateCount ;
        }

        if(current < 0){
            current = 0;
        }

        // 获取预定单数
        List<SkuAdvancedOrder> allAdvanceOrderList = Optional.ofNullable(estimateRepository.selectAdvancedOrderBox(null, "zb", Collections.singletonList(sku), yearMonth)).orElse(new ArrayList<>());
        if(!CollectionUtils.isEmpty(allAdvanceOrderList) && moqDetailVO.getStatus() != EstimateAdjustStatusEnum.ADJUST_NOT_PRODUCT.getStatus()){
            BigDecimal totalAdvance = allAdvanceOrderList.stream().map(SkuAdvancedOrder::getAdvancedOrderBox).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            current = current + totalAdvance.intValue();
        }



        moqDetailVO.setCurrentMOQ(current);
        moqDetailVO.setMoqSkuVOList(moqSkuVOList);

        return moqDetailVO;
    }

    @Override
    @Transactional
    public void moqAudit(EstimateAdjustDO estimateAdjustDO, ProcessUserDO processUserDO) {
        // 参数校验
        validateMoqAuditParams(estimateAdjustDO, processUserDO);
        
        AuditResultEnum auditResult = AuditResultEnum.fromCode(estimateAdjustDO.getResult());
        
        // 获取或创建调整单
        EstimateAdjustPO estimateAdjustPO = getOrCreateAdjustRecord(estimateAdjustDO, processUserDO, auditResult);
        
        // 如果是重新提交，只更新状态后返回
        if (auditResult == AuditResultEnum.RESUBMIT) {
            handleResubmitResult(estimateAdjustPO);
            return;
        }
        
        // 获取相关数据
        List<String> orgCodes = extractOrgCodes(estimateAdjustDO);
        List<EstimateApprovalDetailHistoryPO> historyPOS = getHistoryRecords(estimateAdjustDO, orgCodes);
        List<EstimateAdjustDetailPO> adjustDetailPOS = getAdjustDetailRecords(estimateAdjustPO.getAdjustId());
        
        // 使用分布式锁确保并发安全
        String lockKey = estimateAdjustPO.getAdjustId().toString();
        if (!redisUtil.setLockIfAbsent(ESTIMATE_ADJUST_KEY, lockKey, DEFAULT_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            throw new ApplicationException("任务正在处理中,请勿重复操作");
        }
        
        try {
            processAuditResult(auditResult, estimateAdjustDO, estimateAdjustPO, historyPOS, 
                             adjustDetailPOS, processUserDO, orgCodes,estimateAdjustDO.getShipPeriodId());
        } finally {
            redisUtil.unLock(ESTIMATE_ADJUST_KEY, lockKey);
        }
    }
    
    /**
     * 参数校验
     */
    private void validateMoqAuditParams(EstimateAdjustDO estimateAdjustDO, ProcessUserDO processUserDO) {
        if (Objects.isNull(estimateAdjustDO)) {
            throw new IllegalArgumentException("调整单信息不能为空");
        }
        if (Objects.isNull(processUserDO)) {
            throw new IllegalArgumentException("审核人信息不能为空");
        }
        if (Objects.isNull(estimateAdjustDO.getResult())) {
            throw new IllegalArgumentException("审核结果不能为空");
        }
        if (StringUtils.isBlank(estimateAdjustDO.getSku())) {
            throw new IllegalArgumentException("SKU不能为空");
        }
    }
    
    /**
     * 获取或创建调整单记录
     */
    private EstimateAdjustPO getOrCreateAdjustRecord(EstimateAdjustDO estimateAdjustDO, 
                                                   ProcessUserDO processUserDO, 
                                                   AuditResultEnum auditResult) {
        EstimateAdjustPO estimateAdjustPO = estimateAdjustService.findEstimateAdjust(
            estimateAdjustDO.getYearMonth(), 
            estimateAdjustDO.getShipPeriodId(), 
            estimateAdjustDO.getSku()
        );
        
        if (Objects.isNull(estimateAdjustPO)) {
            return createAdjust(estimateAdjustDO, processUserDO);
        } else if (auditResult == AuditResultEnum.RESUBMIT) {
            updateAdjustDetail(estimateAdjustPO.getAdjustId(), estimateAdjustDO, processUserDO);
        }
        
        return estimateAdjustPO;
    }
    
    /**
     * 处理重新提交结果
     */
    private void handleResubmitResult(EstimateAdjustPO estimateAdjustPO) {
            estimateAdjustPO.setStatus(EstimateAdjustStatusEnum.ADJUST_PROCESSING.getStatus());
            estimateAdjustService.updateEstimateAdjust(estimateAdjustPO);
    }
    
    /**
     * 提取组织代码列表
     */
    private List<String> extractOrgCodes(EstimateAdjustDO estimateAdjustDO) {
        return estimateAdjustDO.getOrgList().stream()
            .map(EstimateAdjustOrgDO::getOrganizationId)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取历史记录
     */
    private List<EstimateApprovalDetailHistoryPO> getHistoryRecords(EstimateAdjustDO estimateAdjustDO, 
                                                                  List<String> orgCodes) {
        return Optional.ofNullable(
            estimateRepository.selectHistoryByCondition(
                estimateAdjustDO.getYearMonth(), 
                estimateAdjustDO.getShipPeriodId(), 
                orgCodes
            )
        ).orElse(new ArrayList<>());
    }
    
    /**
     * 获取调整明细记录
     */
    private List<EstimateAdjustDetailPO> getAdjustDetailRecords(Long adjustId) {
        return Optional.ofNullable(
            estimateRepository.selectEstimateAdjustDetailByAdjustId(adjustId)
        ).orElse(new ArrayList<>());
    }
    
    /**
     * 处理审核结果
     */
    private void processAuditResult(AuditResultEnum auditResult,
                                    EstimateAdjustDO estimateAdjustDO,
                                    EstimateAdjustPO estimateAdjustPO,
                                    List<EstimateApprovalDetailHistoryPO> historyPOS,
                                    List<EstimateAdjustDetailPO> adjustDetailPOS,
                                    ProcessUserDO processUserDO,
                                    List<String> orgCodes,
                                    Long shipPeriodId) {
        
        if (auditResult == AuditResultEnum.APPROVED) {
            processApprovedResult(estimateAdjustDO, estimateAdjustPO, historyPOS, 
                                adjustDetailPOS, processUserDO);
        } else if (auditResult == AuditResultEnum.REJECTED) {
            processRejectedResult(estimateAdjustDO, estimateAdjustPO, historyPOS, 
                                adjustDetailPOS, processUserDO);
        }
        
        // 更新调整单状态
        updateAdjustStatus(estimateAdjustPO, auditResult, processUserDO);
        
        // 修改子表状态为已处理
        estimateAdjustService.updateDetailStatus(estimateAdjustPO.getAdjustId(), STATUS_PROCESSED, processUserDO);
        
        // 同步旺铺数据
        syncWpData(orgCodes, estimateAdjustDO, processUserDO,shipPeriodId);
    }
    
    /**
     * 处理审核通过结果
     */
    private void processApprovedResult(EstimateAdjustDO estimateAdjustDO,
                                     EstimateAdjustPO estimateAdjustPO,
                                     List<EstimateApprovalDetailHistoryPO> historyPOS,
                                     List<EstimateAdjustDetailPO> adjustDetailPOS,
                                     ProcessUserDO processUserDO) {
        
        // 更新历史记录
        updateHistoryForApproved(estimateAdjustDO, historyPOS, adjustDetailPOS);
        
        // 补充缺失的历史记录
        supplementMissingHistoryRecords(estimateAdjustDO, adjustDetailPOS, historyPOS, processUserDO);
    }
    
    /**
     * 处理审核拒绝结果
     */
    private void processRejectedResult(EstimateAdjustDO estimateAdjustDO,
                                     EstimateAdjustPO estimateAdjustPO,
                                     List<EstimateApprovalDetailHistoryPO> historyPOS,
                                     List<EstimateAdjustDetailPO> adjustDetailPOS,
                                     ProcessUserDO processUserDO) {
        
        // 重置调整明细
        resetAdjustDetails(adjustDetailPOS, processUserDO);
        
        // 补充调整明细
        supplementAdjustDetails(estimateAdjustDO, historyPOS, adjustDetailPOS, 
                              estimateAdjustPO, processUserDO);
        
        // 重置历史记录数量
        resetHistoryQuantities(estimateAdjustDO, historyPOS, adjustDetailPOS);
    }
    
    /**
     * 更新历史记录（审核通过）
     */
    private void updateHistoryForApproved(EstimateAdjustDO estimateAdjustDO,
                                        List<EstimateApprovalDetailHistoryPO> historyPOS,
                                        List<EstimateAdjustDetailPO> adjustDetailPOS) {
        
        historyPOS.stream()
            .filter(h -> h.getSku().equals(estimateAdjustDO.getSku()))
            .forEach(h -> {
                List<EstimateAdjustDetailPO> matchingDetails = adjustDetailPOS.stream()
                    .filter(f -> f.getOrganizationId().equals(h.getOrganizationId()))
                    .collect(Collectors.toList());
                
                updateHistoryFromAdjustDetails(h, matchingDetails);
            });
    }
    
    /**
     * 根据调整明细更新历史记录
     */
    private void updateHistoryFromAdjustDetails(EstimateApprovalDetailHistoryPO history,
                                              List<EstimateAdjustDetailPO> adjustDetails) {
        if (CollectionUtils.isEmpty(adjustDetails)) {
            return;
        }
        
        adjustDetails.forEach(adjust -> {
            if (adjust.getStatus() == STATUS_PROCESSED) { // 已处理状态
                updateHistoryQuantityByType(history, adjust);
                estimateRepository.updateApprovalDetailHistory(history);
            }
        });
    }
    
    /**
     * 根据类型更新历史记录数量
     */
    private void updateHistoryQuantityByType(EstimateApprovalDetailHistoryPO history,
                                           EstimateAdjustDetailPO adjust) {
        AdjustTypeEnum adjustType = AdjustTypeEnum.values()[adjust.getType() - 1];
        
        if (adjustType == AdjustTypeEnum.ESTIMATE) {
            history.setEstimateQuantity(adjust.getAuditCount());
        } else if (adjustType == AdjustTypeEnum.APPEND) {
            history.setAppendQuantity(adjust.getAuditCount());
        }
    }
    
    /**
     * 补充缺失的历史记录
     */
    private void supplementMissingHistoryRecords(EstimateAdjustDO estimateAdjustDO,
                                               List<EstimateAdjustDetailPO> adjustDetailPOS,
                                               List<EstimateApprovalDetailHistoryPO> historyPOS,
                                               ProcessUserDO processUserDO) {
        
        EstimateSkuPO estimateSkuPO = estimateSkuRepository.selectSkuByCode(
            estimateAdjustDO.getSku(), RequestUtils.getBusinessGroup());
        
        adjustDetailPOS.forEach(adjustDetail -> {
            boolean historyExists = historyPOS.stream()
                .anyMatch(h -> h.getOrganizationId().equals(adjustDetail.getOrganizationId()));
            
            if (!historyExists) {
                createMissingHistoryRecord(estimateAdjustDO, adjustDetail, estimateSkuPO, processUserDO);
            }
        });
    }
    
    /**
     * 创建缺失的历史记录
     */
    private void createMissingHistoryRecord(EstimateAdjustDO estimateAdjustDO,
                                          EstimateAdjustDetailPO adjustDetail,
                                          EstimateSkuPO estimateSkuPO,
                                          ProcessUserDO processUserDO) {
        
        EstimateApprovalDetailHistoryPO historyRecord = new EstimateApprovalDetailHistoryPO();
        historyRecord.init(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
        historyRecord.setOrganizationId(adjustDetail.getOrganizationId());
        historyRecord.setTheYearMonth(estimateAdjustDO.getYearMonth());
        historyRecord.setSku(estimateAdjustDO.getSku());
        historyRecord.setSalePrice(estimateSkuPO.getThirdOrderPrice());
        historyRecord.setShipPeriodId(estimateAdjustDO.getShipPeriodId());
        
        Integer businessGroupId = organizationMapper.getBusinessGroupById(adjustDetail.getOrganizationId());
        historyRecord.setBusinessGroup(businessGroupId);
        historyRecord.setStoreName(estimateRepository.getStoreName(adjustDetail.getOrganizationId()));
        
        // 根据调整类型设置数量
        updateHistoryQuantityByType(historyRecord, adjustDetail);
        
        estimateRepository.saveApprovalDetailHistory(historyRecord);
    }
    
    /**
     * 重置调整明细
     */
    private void resetAdjustDetails(List<EstimateAdjustDetailPO> adjustDetailPOS,
                                  ProcessUserDO processUserDO) {
        adjustDetailPOS.forEach(detail -> {
            detail.update(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
            detail.setAuditCount(DEFAULT_QUANTITY);
            detail.setStatus(STATUS_PROCESSED);
            estimateRepository.updateEstimateAdjustDetail(detail);
        });
    }
    
    /**
     * 补充调整明细
     */
    private void supplementAdjustDetails(EstimateAdjustDO estimateAdjustDO,
                                       List<EstimateApprovalDetailHistoryPO> historyPOS,
                                       List<EstimateAdjustDetailPO> adjustDetailPOS,
                                       EstimateAdjustPO estimateAdjustPO,
                                       ProcessUserDO processUserDO) {
        
        historyPOS.forEach(history -> {
            List<Integer> requiredTypes = determineRequiredAdjustTypes(history);
            
            requiredTypes.forEach(type -> {
                boolean detailExists = adjustDetailPOS.stream()
                    .anyMatch(detail -> detail.getOrganizationId().equals(history.getOrganizationId()) &&
                                      detail.getSku().equals(estimateAdjustDO.getSku()) &&
                                      detail.getType().equals(type));
                
                if (!detailExists) {
                    createAdjustDetail(estimateAdjustDO, history, type, estimateAdjustPO, 
                                     processUserDO, adjustDetailPOS);
                }
            });
        });
    }
    
    /**
     * 确定需要的调整类型
     */
    private List<Integer> determineRequiredAdjustTypes(EstimateApprovalDetailHistoryPO history) {
                    List<Integer> types = new ArrayList<>();
        
        if (Objects.nonNull(history.getEstimateQuantity()) && history.getEstimateQuantity() > 0) {
            types.add(AdjustTypeEnum.ESTIMATE.getCode());
        }
        if (Objects.nonNull(history.getAppendQuantity()) && history.getAppendQuantity() > 0) {
            types.add(AdjustTypeEnum.APPEND.getCode());
        }
        
        if (CollectionUtils.isEmpty(types)) {
            types.add(AdjustTypeEnum.ESTIMATE.getCode());
        }
        
        return types;
    }
    
    /**
     * 创建调整明细
     */
    private void createAdjustDetail(EstimateAdjustDO estimateAdjustDO,
                                  EstimateApprovalDetailHistoryPO history,
                                  Integer type,
                                  EstimateAdjustPO estimateAdjustPO,
                                  ProcessUserDO processUserDO,
                                  List<EstimateAdjustDetailPO> adjustDetailPOS) {
        
        EstimateAdjustDetailPO adjustDetail = new EstimateAdjustDetailPO();
        adjustDetail.init(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
        adjustDetail.setOrganizationId(history.getOrganizationId());
        adjustDetail.setAuditCount(DEFAULT_QUANTITY);
        adjustDetail.setStatus(STATUS_PROCESSED);
        adjustDetail.setAdjustId(estimateAdjustPO.getAdjustId());
        adjustDetail.setSku(estimateAdjustDO.getSku());
        adjustDetail.setType(type);
        
        // 设置原始数量
        Integer rawCount = (type.equals(AdjustTypeEnum.ESTIMATE.getCode())) ?
            history.getEstimateQuantity() : history.getAppendQuantity();
        adjustDetail.setRawEstimateCount(rawCount);
        
        estimateRepository.saveAdjustDetail(adjustDetail);
        adjustDetailPOS.add(adjustDetail);
    }
    
    /**
     * 重置历史记录数量
     */
    private void resetHistoryQuantities(EstimateAdjustDO estimateAdjustDO,
                                      List<EstimateApprovalDetailHistoryPO> historyPOS,
                                      List<EstimateAdjustDetailPO> adjustDetailPOS) {
        
        historyPOS.stream()
            .filter(h -> h.getSku().equals(estimateAdjustDO.getSku()))
            .forEach(history -> {
                List<EstimateAdjustDetailPO> matchingDetails = adjustDetailPOS.stream()
                    .filter(detail -> detail.getOrganizationId().equals(history.getOrganizationId()) &&
                                    detail.getSku().equals(estimateAdjustDO.getSku()))
                    .collect(Collectors.toList());
                
                resetHistoryQuantitiesFromDetails(history, matchingDetails);
            });
    }
    
    /**
     * 根据调整明细重置历史记录数量
     */
    private void resetHistoryQuantitiesFromDetails(EstimateApprovalDetailHistoryPO history,
                                                 List<EstimateAdjustDetailPO> adjustDetails) {
        if (CollectionUtils.isEmpty(adjustDetails)) {
            return;
        }
        
        adjustDetails.forEach(adjust -> {
            AdjustTypeEnum adjustType = AdjustTypeEnum.values()[adjust.getType() - 1];
            
            if (adjustType == AdjustTypeEnum.ESTIMATE) {
                history.setEstimateQuantity(0);
            } else if (adjustType == AdjustTypeEnum.APPEND) {
                history.setAppendQuantity(0);
            }
            
            estimateRepository.updateApprovalDetailHistory(history);
        });
    }
    
    /**
     * 更新调整单状态
     */
    private void updateAdjustStatus(EstimateAdjustPO estimateAdjustPO,
                                  AuditResultEnum auditResult,
                                  ProcessUserDO processUserDO) {
        
        if (auditResult == AuditResultEnum.APPROVED) {
            estimateAdjustPO.setStatus(AUDIT_STATUS_APPROVED); // 审核通过状态
        } else if (auditResult == AuditResultEnum.REJECTED) {
            estimateAdjustPO.setStatus(AUDIT_STATUS_REJECTED); // 审核拒绝状态
        }
        
        estimateAdjustPO.update(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
            estimateRepository.updateEstimateAdjustPO(estimateAdjustPO);
    }
    
    /**
     * 同步旺铺数据
     */
    private void syncWpData(List<String> orgCodes,
                            EstimateAdjustDO estimateAdjustDO,
                            ProcessUserDO processUserDO, Long shipPeriodId) {
        
        orgCodes.forEach(orgCode -> {
            try {
                syncSingleOrgData(orgCode, estimateAdjustDO, processUserDO,shipPeriodId);
            } catch (Exception e) {
                log.error("同步组织[{}]旺铺数据失败", orgCode, e);
            }
        });
    }
    
        /**
     * 同步单个组织的数据到旺铺系统
     * 
     * @param orgCode 组织代码
     * @param estimateAdjustDO 预估调整数据
     * @param processUserDO 处理人信息
     * @param shipPeriodId 货期ID
     */
    private void syncSingleOrgData(String orgCode,
                                 EstimateAdjustDO estimateAdjustDO,
                                 ProcessUserDO processUserDO, Long shipPeriodId) {
        
        List<EstimateHistoryDTO> historyDTOS = estimateRepository.selectHistoryByOrgCode(
            estimateAdjustDO.getYearMonth(), orgCode);
        
        String organizationName = organizationMapper.getOrganizationName(orgCode);
        Integer businessGroupId = organizationMapper.getBusinessGroupById(orgCode);
        String productGroupCode = sfaBusinessGroupMapper.selectById(businessGroupId).getBusinessGroupCode();
        
        SaveSkuSaleModel saveSkuSaleModel = buildSaveSkuSaleModel(
            historyDTOS, estimateAdjustDO, processUserDO, organizationName, productGroupCode);
        
                estimateConnectorUtil.saveSkuSale(saveSkuSaleModel);

        // 将historyDTOS中的数据根据shipPeriodId过滤后，在根据sku做合并，合计estimateCount
        List<EstimateHistoryDTO> filteredHistoryDTOS = historyDTOS.stream()
            .filter(h -> h.getShipPeriodId().equals(shipPeriodId))
            .collect(Collectors.toList());

        Map<String, Long> skuCountMap = filteredHistoryDTOS.stream()
            .collect(Collectors.groupingBy(EstimateHistoryDTO::getSku, Collectors.summingLong(EstimateHistoryDTO::getEstimateCount)));
        
        // 构建SKU销售预估控制请求
        SkuOverSaleControlSaveWithPhaseRequest skuOverSaleControlSaveWithPhaseRequest = new SkuOverSaleControlSaveWithPhaseRequest();
        skuOverSaleControlSaveWithPhaseRequest.setCompanyName(organizationName);
        skuOverSaleControlSaveWithPhaseRequest.setEmployeeId(processUserDO.getEmployeeId());
        skuOverSaleControlSaveWithPhaseRequest.setEstimateMonth(estimateAdjustDO.getYearMonth());
        skuOverSaleControlSaveWithPhaseRequest.setProductGroupId(productGroupCode);
        skuOverSaleControlSaveWithPhaseRequest.setEstimatePhase(shipPeriodId.intValue());

        // 构建SKU列表
        List<SkuEstimateBoxNumberDTO> skuList = skuCountMap.entrySet().stream()
            .map(entry -> {
                SkuEstimateBoxNumberDTO dto = new SkuEstimateBoxNumberDTO();
                dto.setSku(entry.getKey());
                dto.setEstimateBoxNumber(entry.getValue().intValue());
                return dto;
            })
            .collect(Collectors.toList());
        skuOverSaleControlSaveWithPhaseRequest.setSkuList(skuList);

        // 保存商品销售预估基础数据
        estimateConnectorUtil.saveSkuOverSaleControlInfoWithPhase(skuOverSaleControlSaveWithPhaseRequest);
        
    }
    
    /**
     * 构建保存SKU销售模型
     */
    private SaveSkuSaleModel buildSaveSkuSaleModel(List<EstimateHistoryDTO> historyDTOS,
                                                 EstimateAdjustDO estimateAdjustDO,
                                                 ProcessUserDO processUserDO,
                                                 String organizationName,
                                                 String productGroupCode) {
        
        SaveSkuSaleModel saveSkuSaleModel = new SaveSkuSaleModel();
        saveSkuSaleModel.setCompanyName(organizationName);
        saveSkuSaleModel.setEmployeeId(processUserDO.getEmployeeId());
        saveSkuSaleModel.setEstimateMonth(estimateAdjustDO.getYearMonth());
        saveSkuSaleModel.setProductGroupId(productGroupCode);
        
        Map<String, Integer> skuCountMap = historyDTOS.stream()
            .collect(Collectors.groupingBy(
                EstimateHistoryDTO::getSku, 
                Collectors.summingInt(EstimateHistoryDTO::getEstimateCount)
            ));
        
        List<SkuModel> skuModels = skuCountMap.entrySet().stream()
            .map(entry -> {
                SkuModel skuModel = new SkuModel();
                skuModel.setSku(entry.getKey());
                skuModel.setEstimateBoxNumber(new BigDecimal(entry.getValue()));
                return skuModel;
            })
            .collect(Collectors.toList());
        
        saveSkuSaleModel.setSkuList(skuModels);
        return saveSkuSaleModel;
    }
    


    @Override
    @Transactional
    public void adjust(AdjustDO adjustDO, ProcessUserDO processUserDO) {
        // 获取调整物料单主表信息
        EstimateAdjustPO estimateAdjustPO = estimateRepository.selectAdjust(adjustDO.getYearMonth(),adjustDO.getShipPeriodId(),adjustDO.getSku(),RequestUtils.getBusinessGroup());
        if(Objects.isNull(estimateAdjustPO)){
            throw new ApplicationException("调整单主表信息获取失败");
        }

        Integer status = estimateAdjustPO.getStatus();
        if(status != EstimateAdjustStatusEnum.ADJUST_PROCESSING.getStatus()){
            throw new ApplicationException("已处理调整，请勿重复操作");
        }

        // 检查产销是否处理中
        boolean locked = redisUtil.isLocked(ESTIMATE_ADJUST_KEY, estimateAdjustPO.getAdjustId().toString());
        if(locked){
            throw new ApplicationException("产销处理中!");
        }

        List<AdjustDetailValue> adjustDetailList = adjustDO.getAdjustDetailList();

        List<EstimateAdjustDetailPO> adjustDetailPOS = estimateRepository.selectEstimateAdjustDetailByAdjustId(estimateAdjustPO.getAdjustId());
        adjustDetailPOS.forEach(e -> {
            Optional<AdjustDetailValue> adjustOptional = adjustDetailList.stream().filter(f -> f.getOrganizationId().equals(e.getOrganizationId()) && f.getType().equals(e.getType())).findFirst();
            if(adjustOptional.isPresent()){
                Integer finalAuditCount = adjustOptional.get().getFinalAuditCount();

                List<EstimateDetail> details = new ArrayList<>();

                Integer rawEstimateCount = Optional.ofNullable(e.getRawEstimateCount()).orElse(0);
                EstimateSkuPO estimateSkuPO = estimateSkuRepository.selectPriceBySku(e.getSku(), organizationMapper.getBusinessGroupById(e.getOrganizationId()));
                details.add(EstimateDetail.builder().sku(e.getSku()).currentCount(finalAuditCount).salePrice(estimateSkuPO.getThirdOrderPrice()).rawCount(rawEstimateCount).build());

                // 检查额度是否超管控
                EstimateCheckDO estimateCheckDO = EstimateCheckDO.builder().theYearMonth(estimateAdjustPO.getMonth())
                        .organizationId(e.getOrganizationId())
                        .adjustId(estimateAdjustPO.getAdjustId())
                        .estimateDetailList(details)
                        .build();
                estimateControlService.checkEstimateControl(estimateCheckDO);


                e.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());


                if(Objects.isNull(finalAuditCount)){
                    finalAuditCount = rawEstimateCount;
                }
                e.setAuditCount(finalAuditCount);
                e.setStatus(1);
                estimateRepository.updateEstimateAdjustDetail(e);
            }
        });



        // 如果全部完成择更新主表状态
        Optional<EstimateAdjustDetailPO> allFinishOptional = adjustDetailPOS.stream().filter(f -> f.getStatus() == 0).findFirst();
        if(!allFinishOptional.isPresent()){
            estimateAdjustPO.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
            estimateAdjustPO.setStatus(EstimateAdjustStatusEnum.ADJUST_FINISH.getStatus());
            estimateRepository.updateEstimateAdjustPO(estimateAdjustPO);
        }
    }

    @Override
    public IPage<EstimateAdjustVO> selectAdjust(EstimateAdjustSearchRequest estimateAdjustSearchRequest) {
        IPage<EstimateAdjustVO> page = new Page<EstimateAdjustVO>(estimateAdjustSearchRequest.getPage(),estimateAdjustSearchRequest.getRows());

        List<String> organizationIds = estimateAdjustSearchRequest.getOrganizationIds();
        if(!CollectionUtils.isEmpty(organizationIds)){
            String orgCode = organizationIds.get(0);
            estimateAdjustSearchRequest.setOrgType(organizationMapper.getOrganizationType(orgCode));
        }


        List<EstimateAdjustVO> list = estimateRepository.selectAreaAdjust(page,estimateAdjustSearchRequest);
        page.setRecords(list);
        return page;
    }

    @Override
    public EstimateAdjustDetailVO getEstimateAdjustDetail(AdjustDetailSearchRequest adjustDetailSearchRequest) {
        EstimateAdjustDetailVO estimateAdjustDetailVO = new EstimateAdjustDetailVO();

        EstimateAdjustPO estimateAdjustPO = estimateRepository.selectAdjustById(adjustDetailSearchRequest.getAdjustId());
        if(Objects.isNull(estimateAdjustPO)){
            throw new ApplicationException("调整单获取失败");
        }

        EstimateShipPeriodPO estimateShipPeriodPO = estimateSkuRepository.selectShipPeriodById(estimateAdjustPO.getShipPeriodId());
        if(Objects.isNull(estimateShipPeriodPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.SHIP_PERIOD_NOT_EXIST.getMsg());
        }


        String monthTitle = LocalDateTimeUtils.formatTime(LocalDate.parse(estimateAdjustPO.getMonth() + "-01").atStartOfDay(), "yyyy年MM月");


        String title = monthTitle + " " + estimateShipPeriodPO.getName()+ " " + "销售预估";
        estimateAdjustDetailVO.setTitle(title);





        // 设置主表状态
        estimateAdjustDetailVO.setStatus(estimateAdjustPO.getStatus());

        estimateAdjustDetailVO.setSku(estimateAdjustPO.getSku());
        EstimateSkuPO estimateSkuPO =  estimateSkuRepository.getSkuByCode(estimateAdjustPO.getSku(),RequestUtils.getBusinessGroup());
        if(Objects.nonNull(estimateSkuPO)){
            estimateAdjustDetailVO.setSkuName(estimateSkuPO.getSkuName());
            estimateAdjustDetailVO.setFlavor(estimateSkuPO.getFlavor());
            estimateAdjustDetailVO.setFullCaseSpec(estimateSkuPO.getFullCaseSpec());
            String moq = estimateSkuPO.getMoq();
            if(CommonUtil.StringUtils.isNotBlank(moq)){
                estimateAdjustDetailVO.setMoq(Integer.parseInt(moq));
            }else{
                estimateAdjustDetailVO.setMoq(0);
            }

        }



        // 获取预定单数
        String zbOrgCode = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
        List<SkuAdvancedOrder> advanceOrderList = Optional.ofNullable(estimateRepository.selectAdvancedOrderBox(zbOrgCode, "zb", Collections.singletonList(estimateAdjustPO.getSku()), estimateAdjustPO.getMonth())).orElse(new ArrayList<>());
        BigDecimal advanceOrderCount = BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(advanceOrderList)){
            advanceOrderCount = advanceOrderList.stream()
                    .findFirst()
                    .map(SkuAdvancedOrder::getAdvancedOrderBox)
                    .orElse(BigDecimal.ZERO);
        }


        // 获取明细
        adjustDetailSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> organizationIds = adjustDetailSearchRequest.getOrganizationIds();
        if(!CollectionUtils.isEmpty(organizationIds)){
            String organizationType = organizationMapper.getOrganizationType(organizationIds.get(0));
            adjustDetailSearchRequest.setOrgType(organizationType);
        }

        List<OrgAdjustVO> orgAdjustVOS = Optional.ofNullable(estimateRepository.selectAdjustDetail(adjustDetailSearchRequest)).orElse(new ArrayList<>());

        // 获取预定单数
        List<String> companyCodes = orgAdjustVOS.stream().map(OrgAdjustVO::getOrganizationId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(companyCodes)){
            List<EstimateSummaryDTO> list = new ArrayList<>();
            companyCodes.forEach(e -> {
                EstimateSummaryDTO estimateSummaryDTO = new EstimateSummaryDTO();
                estimateSummaryDTO.setOrganizationId(e);
                estimateSummaryDTO.setOrgType("company");
                estimateSummaryDTO.setSku(estimateAdjustPO.getSku());
                estimateSummaryDTO.setYearMonth(estimateAdjustPO.getMonth());
                list.add(estimateSummaryDTO);
            });

            List<SkuAdvancedOrder> skuAdvancedOrderList = Optional.ofNullable(estimateRepository.selectAdvancedOrderBoxByResult(list)).orElse(new ArrayList<>());

            orgAdjustVOS.forEach(e -> {
                Optional<SkuAdvancedOrder> advanceOptional = skuAdvancedOrderList.stream().filter(f -> f.getOrganizationId().equals(e.getOrganizationId())).findFirst();
                if(advanceOptional.isPresent()){
                    e.setAdvancedOrderCount(Optional.ofNullable(advanceOptional.get().getAdvancedOrderBox()).orElse(BigDecimal.ZERO).intValue());
                }else{
                    e.setAdvancedOrderCount(0);
                }

                Integer finalAuditCount = e.getFinalAuditCount();
                if(Objects.isNull(finalAuditCount)){
                    e.setFinalAuditCount(e.getRawEstimateCount());
                }

                Integer status = e.getStatus();
                Integer rawEstimateCount =  Optional.ofNullable(e.getRawEstimateCount()).orElse(0);
                Integer currentBox = rawEstimateCount;
                if(status == 1){
                    currentBox = Optional.ofNullable(finalAuditCount).orElse(0);
                }
                e.setDiff(finalAuditCount - rawEstimateCount);
                e.setTotal(currentBox + e.getAdvancedOrderCount());
            });
        }
        estimateAdjustDetailVO.setOrgAdjustVOS(orgAdjustVOS);

        // 获取已提报的数量
        BigDecimal currentMOQ = BigDecimal.ZERO;
        List<MOQ> moqList = estimateRepository.selectCurrentMOQ(Collections.singletonList(estimateAdjustPO.getSku()), estimateAdjustPO.getMonth(),companyCodes);
        if(!CollectionUtils.isEmpty(moqList)){
            currentMOQ = moqList.stream()
                    .findFirst()
                    .map(MOQ::getCurrentMOQ)
                    .orElse(BigDecimal.ZERO);
        }


        int summaryMoq = currentMOQ.intValue();

        summaryMoq = summaryMoq + advanceOrderCount.intValue();

        if(estimateAdjustDetailVO.getStatus() == EstimateAdjustStatusEnum.ADJUST_NOT_PRODUCT.getStatus()){
            summaryMoq = 0;
        }

        estimateAdjustDetailVO.setSummaryMoq(summaryMoq);

        estimateAdjustDetailVO.setDiff(estimateAdjustDetailVO.getMoq() - estimateAdjustDetailVO.getSummaryMoq());


        return estimateAdjustDetailVO;
    }

    @Override
    public void estimateApprovalExport(EstimateApprovalSearchRequest estimateApprovalSearchRequest,List<Integer> roleIds) {
        // 根据step 获取流程CODE
        String flowCode = EstimateConstants.COMPANY_FLOW_CODE;
        Integer step = estimateApprovalSearchRequest.getProcessStep();
        if(step == 20){
            flowCode = EstimateConstants.DEPARTMENT_FLOW_CODE;
        }else if(step == 10){
            flowCode = EstimateConstants.CEO_FLOW_CODE;
        }
        estimateApprovalSearchRequest.setFlowCode(flowCode);

        List<String> organizationIds = Optional.ofNullable(estimateApprovalSearchRequest.getOrganizationIds()).orElse(new ArrayList<>());
        if(organizationIds.stream().findFirst().isPresent()){
            String organizationType = organizationMapper.getOrganizationType(organizationIds.stream().findFirst().get());
            estimateApprovalSearchRequest.setOrganizationType(organizationType);
        }
        log.info("【select approval list】request:{}",estimateApprovalSearchRequest);


        List<EstimateApprovalDTO> list = estimateRepository.selectApprovalList(null,estimateApprovalSearchRequest);
        List<EstimateApprovalVO> estimateApprovalVOS = EstimateConvert.convertToVO(list, roleIds, estimateApprovalSearchRequest.getProcessStep());

        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "销售预估审核" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), EstimateApprovalVO.class, estimateApprovalVOS);
        assert response != null;
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public EstimatePermissionVO getEstimatePermission(String person) {
        EstimatePermissionVO estimatePermissionVO = new EstimatePermissionVO();
        estimatePermissionVO.setSearchPermission(true);

        String organizationType = RequestUtils.getLoginInfo().getOrganizationType();
        int order = OrganizationTypeEnum.getOrder(organizationType);
        if(order <=  OrganizationTypeEnum.DEPARTMENT.getOrder()){
            estimatePermissionVO.setDepartmentPermission(true);
        }
        if(order <=  OrganizationTypeEnum.COMPANY.getOrder()){
            estimatePermissionVO.setCompanyPermission(true);
        }
        if(order <=  OrganizationTypeEnum.VARE.getOrder()){
            estimatePermissionVO.setVareaPermission(true);
        }

        if(organizationType.equals("zb")){
            // 检查是否有产销角色
            Integer hasRole = roleEmployeeRelationMapper.checkHasRole(person, 37, RequestUtils.getBusinessGroup());
            if(hasRole >= 1){
                estimatePermissionVO.setConfigPermission(true);
                estimatePermissionVO.setProductionPermission(true);
            }
        }


        return estimatePermissionVO;
    }




    private EstimateAdjustPO createAdjust(EstimateAdjustDO estimateAdjustDO, ProcessUserDO processUserDO) {
        // 创建调整单
        EstimateAdjustPO estimateAdjustPO = estimateAdjustService.createMoqAudit(estimateAdjustDO.getYearMonth(),
                estimateAdjustDO.getShipPeriodId(),estimateAdjustDO.getSku(),processUserDO);

        // 初始化字表信息
        List<EstimateAdjustOrgDO> orgList = estimateAdjustDO.getOrgList();
        orgList.forEach(e -> {
            estimateAdjustService.initAdjustDetail(estimateAdjustPO.getAdjustId(),e.getOrganizationId(),estimateAdjustDO.getSku(),e.getType(),e.getRawEstimateCount(),processUserDO);
        });

        return estimateAdjustPO;
    }

    /**
     * 更新调整明细
     */
    private void updateAdjustDetail(Long adjustId, EstimateAdjustDO estimateAdjustDO, ProcessUserDO processUserDO) {
        // 获取调整明细列表
        List<EstimateAdjustDetailPO> adjustDetailList = estimateRepository.selectEstimateAdjustDetailByAdjustId(adjustId);
        
        if (CollectionUtils.isEmpty(adjustDetailList)) {
            log.warn("调整单[{}]未找到对应的调整明细", adjustId);
            return;
        }
        
        // 更新调整明细中的组织相关信息
        adjustDetailList.forEach(detail -> {
            detail.update(processUserDO.getEmployeeId(), processUserDO.getEmployeeName());
            estimateRepository.updateEstimateAdjustDetail(detail);
        });
        
        log.info("更新调整单[{}]明细完成，共更新{}条记录", adjustId, adjustDetailList.size());
    }

    private void synEstimate(List<EstimateApprovalDetailPO> approvalDetailPOList, BigDecimal auditPrice, EstimateApprovalPO estimateApprovalPO, ProcessUserDO processUserDO,int status) {

        EstimateModel estimateModel = new EstimateModel();
        estimateModel.setSaleEstimateNo(estimateApprovalPO.getSaleEstimateNo());
        estimateModel.setUpdator(processUserDO.getEmployeeName());
        estimateModel.setAuditAmount(auditPrice);
        estimateModel.setStatus(status);
        BigDecimal auditQuantity = approvalDetailPOList.stream().map(EstimateApprovalDetailPO::getAuditQuantity).filter(Objects::nonNull).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
        estimateModel.setAuditQuantity(auditQuantity);

        List<EstimateDetailModel> list = new ArrayList<>();
        approvalDetailPOList.stream().filter(f -> Objects.nonNull(f.getAuditQuantity()) && f.getAuditQuantity() > 0).forEach(e -> {
            EstimateDetailModel estimateDetailModel = new EstimateDetailModel();
            estimateDetailModel.setSku(e.getSku());
            estimateDetailModel.setAuditAmount(Optional.ofNullable(e.getSalePrice()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(Optional.ofNullable(e.getAuditQuantity()).orElse(0))));
            estimateDetailModel.setAuditQuantity(new BigDecimal(Optional.ofNullable(e.getAuditQuantity()).orElse(0)));
            list.add(estimateDetailModel);
        });
        estimateModel.setItems(list);


        estimateConnectorUtil.auditSalesEstimate(estimateModel);
    }

    private void saveHistory(EstimateApprovalPO estimateApprovalPO, List<EstimateApprovalDetailPO> approvalDetailPOList, ProcessUserDO processUserDO,int businessGroup,String storeName,Long shipPeriod) {
        // 根据货需月份获取已提报的记录
        List<EstimateApprovalDetailHistoryPO> estimateApprovalDetailHistoryPOS = Optional.ofNullable(estimateRepository.selectHistory(estimateApprovalPO.getMonth(),shipPeriod, estimateApprovalPO.getOrganizationId())).orElse(new ArrayList<>());

        Integer type = estimateApprovalPO.getType();

        estimateApprovalDetailHistoryPOS.forEach(e -> {
            Optional<EstimateApprovalDetailPO> skuOptional = approvalDetailPOList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();

            if(skuOptional.isPresent()){
                EstimateApprovalDetailPO estimateApprovalDetailPO = skuOptional.get();
                if(type == 1){
                    e.setEstimateQuantity(estimateApprovalDetailPO.getAuditQuantity());
                }else{
                    Integer appendQuantity = Optional.ofNullable(e.getAppendQuantity()).orElse(0);
                    int quantity = appendQuantity + estimateApprovalDetailPO.getAuditQuantity();
                    e.setAppendQuantity(quantity);
                }
                e.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
                estimateRepository.updateApprovalDetailHistory(e);
            }
        });

        // 新增历史
        approvalDetailPOList.forEach(e -> {
            Optional<EstimateApprovalDetailHistoryPO> skuOptional = estimateApprovalDetailHistoryPOS.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(!skuOptional.isPresent()){
                EstimateApprovalDetailHistoryPO estimateApprovalDetailHistoryPO = EstimateFactory.buildEstimateApprovalDetailHistory(e, estimateApprovalPO.getType(), estimateApprovalPO.getOrganizationId(), estimateApprovalPO.getMonth(), processUserDO);
                estimateApprovalDetailHistoryPO.setBusinessGroup(businessGroup);
                estimateApprovalDetailHistoryPO.setStoreName(storeName);
                estimateApprovalDetailHistoryPO.setShipPeriodId(shipPeriod);
                estimateRepository.saveApprovalDetailHistory(estimateApprovalDetailHistoryPO);
            }
        });

    }



    private void saveOrUpdateSkuList(List<EstimateApprovalDetailPO> approvalDetailPOList, List<EstimateApprovalDetailDO> skuList, Long approvalId, String organizationId,int type) {
        approvalDetailPOList.stream().filter(f -> f.getDeleteFlag() == 0).forEach(e -> {
            Optional<EstimateApprovalDetailDO> skuOptional = skuList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(skuOptional.isPresent()){
                EstimateApprovalDetailDO estimateApprovalDetailDO = skuOptional.get();
                if(type == 1){
                    e.setEstimateQuantity(estimateApprovalDetailDO.getAuditCount());
                }else{
                    e.setAuditQuantity(estimateApprovalDetailDO.getAuditCount());
                }

            }else{
                if(type == 1){
                    e.setEstimateQuantity(0);
                }else{
                    e.setAuditQuantity(0);
                }
            }
            e.setUpdateTime(LocalDateTime.now());
            estimateRepository.updateApprovalDetail(e);
        });


        // 检查是否有新增
        skuList.forEach(e -> {
            Optional<EstimateApprovalDetailPO> skuOptional = approvalDetailPOList.stream().filter(f -> f.getSku().equals(e.getSku()) && f.getDeleteFlag() == 0).findFirst();
            if(!skuOptional.isPresent()){
                // 插入操作
                EstimateApprovalDetailPO estimateApprovalDetailPO = new EstimateApprovalDetailPO();
                estimateApprovalDetailPO.setApprovalId(approvalId);
                estimateApprovalDetailPO.setSku(e.getSku());

                if(type == 1){
                    estimateApprovalDetailPO.setEstimateQuantity(e.getAuditCount());
                }else{
                    estimateApprovalDetailPO.setEstimateQuantity(0);
                    estimateApprovalDetailPO.setAuditQuantity(e.getAuditCount());
                }




                estimateApprovalDetailPO.setOrganizationId(organizationId);
                estimateApprovalDetailPO.setDeleteFlag(0);
                estimateApprovalDetailPO.setSalePrice(Optional.ofNullable(e.getSalePrice()).orElse(BigDecimal.ZERO));
                estimateApprovalDetailPO.setCreateTime(LocalDateTime.now());
                estimateApprovalDetailPO.setUpdateTime(LocalDateTime.now());
                estimateRepository.saveApprovalDetail(estimateApprovalDetailPO);
                approvalDetailPOList.add(estimateApprovalDetailPO);
            }
        });


    }

    private BigDecimal saveEstimateDetail(SaleEstimateDO saleEstimateBySaleEstimateNo, Long approvalId, String organizationId,Integer businessGroup) {
        List<SaleEstimateItemDO> itemEntityList = saleEstimateBySaleEstimateNo.getItemEntityList();
        AtomicReference<BigDecimal> totalPrice = new AtomicReference<>(BigDecimal.ZERO);

        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroup);
        QuerySkuRequest querySkuRequest = new QuerySkuRequest();
        querySkuRequest.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
        // 合伙人提报默认三期
        querySkuRequest.setSupplyPhaseFlag(3);
        List<SkuInfoDTO> skuInfoDTOS = Optional.ofNullable(estimateClient.querySkuInfo(querySkuRequest)).orElse(new ArrayList<>());


        List<EstimateApprovalDetailDO> skuList = new ArrayList<>();

        itemEntityList.stream().filter(f -> f.getQuantity().compareTo(BigDecimal.ZERO) > 0).forEach(e -> {
            // 查询盘价
            EstimateSkuPO estimateSkuPO = Optional.ofNullable(estimateSkuRepository.selectPriceBySku(e.getSku(),RequestUtils.getBusinessGroup())).orElse(new EstimateSkuPO());
            BigDecimal price = Optional.ofNullable(estimateSkuPO.getThirdOrderPrice()).orElse(BigDecimal.ZERO);

            EstimateApprovalDetailPO estimateApprovalDetailPO = EstimateFactory.buildEstimateApprovalDetail(e.getSku(), e.getQuantity().intValue(), price, approvalId, organizationId, e.getReason());
            estimateRepository.saveEstimateApprovalDetail(estimateApprovalDetailPO);

            totalPrice.set(totalPrice.get().add(price.multiply(e.getQuantity())));

            Optional<SkuInfoDTO> skuOptional = skuInfoDTOS.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(skuOptional.isPresent()){
                SkuInfoDTO skuInfoDTO = skuOptional.get();
                EstimateApprovalDetailDO estimateApprovalDetailDO = new EstimateApprovalDetailDO();
                BeanUtils.copyProperties(skuInfoDTO,estimateApprovalDetailDO);
                estimateApprovalDetailDO.setSalePrice(skuInfoDTO.getThirdOrderPrice());
                estimateApprovalDetailDO.setExpectListMonth(skuInfoDTO.getExpectListMonth());
                List<String> tagNameList = skuInfoDTO.getTagNameList();
                if(!CollectionUtils.isEmpty(tagNameList)){
                    estimateApprovalDetailDO.setTagNameList(String.join(",",tagNameList));
                }

                List<ActivityTagDTO> skuActivityTagList = Optional.ofNullable(skuInfoDTO.getSkuActivityTagList()).orElse(new ArrayList<>());

                List<String> activityTagNames = skuActivityTagList.stream().map(ActivityTagDTO::getActivityTagName).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(activityTagNames)){
                    estimateApprovalDetailDO.setActivityTags(String.join(",",activityTagNames));
                }


                List<String> detailImages = skuActivityTagList.stream().map(ActivityTagDTO::getDetailImage).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(detailImages)){
                    estimateApprovalDetailDO.setActivityImgUrl(String.join(",",detailImages));
                }

                skuList.add(estimateApprovalDetailDO);
            }

        });

        if(!CollectionUtils.isEmpty(skuList)){
            // 提报时检查sku，并更新sku信息
            ProcessUserDO processUserDO = new ProcessUserDO();
            processUserDO.setEmployeeId("ROOT");
            processUserDO.setEmployeeName("旺铺推送");
            estimateSkuService.saveOrUpdateSku(skuList, sfaBusinessGroupEntity.getId(),processUserDO);
        }


        return totalPrice.get();
    }


    private Long saveEstimateApproval(SaleEstimateDO saleEstimateBySaleEstimateNo, CeoEstimateApplyDO ceoEstimateApplyDO, EstimateSchedulePO estimateSchedulePO) {

        Long scheduleId = Optional.ofNullable(estimateSchedulePO.getScheduleId()).orElse(0L);
        Integer type = Optional.ofNullable(estimateSchedulePO.getType()).orElse(EstimateTypeEnum.ROUTINE.getType());


        EstimateApprovalPO estimateApprovalPO = EstimateFactory.buildEstimateApproval(saleEstimateBySaleEstimateNo.getSaleEstimateNo(), saleEstimateBySaleEstimateNo.getMonth(), ceoEstimateApplyDO.getApplyPositionId()
                , ceoEstimateApplyDO.getApplyOrganizationId(), ceoEstimateApplyDO.getApplyUserName(), EstimateSubmitEnum.SUBMIT.getType(), type, scheduleId,ceoEstimateApplyDO.getInstanceId());

        estimateRepository.saveEstimateApproval(estimateApprovalPO);
        return estimateApprovalPO.getApprovalId();
    }

}
