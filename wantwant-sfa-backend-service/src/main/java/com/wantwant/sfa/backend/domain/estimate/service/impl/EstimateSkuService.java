package com.wantwant.sfa.backend.domain.estimate.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.gexin.fastjson.JSON;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.*;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateErrorMsgEnum;
import com.wantwant.sfa.backend.domain.estimate.enums.EstimateTypeEnum;
import com.wantwant.sfa.backend.domain.estimate.mapper.EstimateBigTableMapper;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateSkuRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.*;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateSkuService;
import com.wantwant.sfa.backend.domain.estimate.service.factory.EstimateSkuFactory;
import com.wantwant.sfa.backend.domain.estimate.util.EstimateConstants;
import com.wantwant.sfa.backend.estimate.request.EstimateSkuExportRequest;
import com.wantwant.sfa.backend.estimate.vo.*;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.EstimateClient;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.model.ActivityTagDTO;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.model.SkuInfoDTO;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.request.QuerySkuRequest;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.model.estimate.EstimateModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/15/下午4:02
 */
@Service
@Slf4j
public class EstimateSkuService implements IEstimateSkuService {

    @Resource
    private IEstimateSkuRepository estimateSkuRepository;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private EstimateClient estimateClient;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private EstimateBigTableMapper estimateBigTableMapper;

    @Override
    @Transactional
    public EstimateSkuGroupDO saveSkuGroup(EstimateSkuGroupDO estimateSkuGroupDO, ProcessUserDO processUserDO) {
        log.info("【save sku group】DO:{}",estimateSkuGroupDO);

        // 检查物料组名称是否重复
        boolean isExist = estimateSkuRepository.checkGroupNameExist(estimateSkuGroupDO.getGroupName(),estimateSkuGroupDO.getBusinessGroup(),estimateSkuGroupDO.getGroupId());
        if(isExist){
            throw new ApplicationException(EstimateErrorMsgEnum.GROUP_NAME_DUPLICATION.getMsg());
        }

        // 更新sku信息
        log.info("【save sku group】step1: save sku");
//        List<EstimateSkuPO> estimateSkuPOS = saveOrUpdateSku(estimateSkuGroupDO.getSkuDOList(),estimateSkuGroupDO.getBusinessGroup(), processUserDO);
        log.info("【save sku group】step1: save sku finish");

        // 更新物料组信息
        log.info("【save sku group】step2: save sku group");
        Long groupId = saveOrUpdateSkuGroup(estimateSkuGroupDO.getGroupId(), estimateSkuGroupDO.getGroupName(),estimateSkuGroupDO.getShipPeriodId(), estimateSkuGroupDO.getBusinessGroup(), processUserDO);
        estimateSkuGroupDO.setGroupId(groupId);
        log.info("【save sku group】step2: save sku group finish");


        // 更新可销组织关系
        log.info("【save sku group】step3: save sku organization relation");
//        saveOrganizationRelation(estimateSkuPOS,estimateSkuGroupDO.getSkuDOList(),groupId,processUserDO);
        log.info("【save sku group】step2: save sku organization relation finish");

        return estimateSkuGroupDO;
    }

    @Override
    public List<EstimateSkuGroupVO> selectEstimateGroupByBusinessGroup(int businessGroup) {

        List<EstimateSkuGroupVO> list = estimateSkuRepository.selectEstimateGroupByBusinessGroup(businessGroup);
        return list;
    }

    @Override
    public void updateStatus(EstimateSkuGroupStatusDO estimateSkuGroupStatusDO, ProcessUserDO processUserDO) {
        log.info("【update sku group status】statsDO:{},processUserDO:{}",estimateSkuGroupStatusDO,processUserDO);
        EstimateSkuGroupPO estimateSkuGroupPO = estimateSkuRepository.selectSkuGroupById(estimateSkuGroupStatusDO.getGroupId());
        if(Objects.isNull(estimateSkuGroupPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.GROUP_ID_NOT_EXIST.getMsg());
        }
        estimateSkuGroupPO.setStatus(estimateSkuGroupStatusDO.getStatus());
        estimateSkuGroupPO.setUpdateUserId(processUserDO.getEmployeeId());
        estimateSkuGroupPO.setUpdateUserName(processUserDO.getEmployeeName());
        estimateSkuRepository.updateEstimateSkuGroup(estimateSkuGroupPO);
    }

    @Override
    public EstimateSkuGroupDetailVO selectSkuGroupDetail(int businessGroup, Long groupId) {
        log.info("【select sku group detail】businessGroup:{},groupId:{}",businessGroup,groupId);
        EstimateSkuGroupDetailVO estimateSkuGroupDetailVO = new EstimateSkuGroupDetailVO();

        EstimateSkuGroupPO estimateSkuGroupPO = estimateSkuRepository.selectSkuGroupById(groupId);
        if(Objects.isNull(estimateSkuGroupPO)){
            throw new ApplicationException(EstimateErrorMsgEnum.GROUP_ID_NOT_EXIST.getMsg());
        }
        estimateSkuGroupDetailVO.setShipPeriodId(estimateSkuGroupPO.getShipPeriodId());
        estimateSkuGroupDetailVO.setGroupId(groupId);
        estimateSkuGroupDetailVO.setShipPeriodId(estimateSkuGroupPO.getShipPeriodId());
        estimateSkuGroupDetailVO.setGroupName(estimateSkuGroupPO.getGroupName());

        // 获取物料组对应的sku信息
        List<EstimateSkuVO> skuVOList = estimateSkuRepository.selectSkuDetailByGroupId(groupId);
        if(!CollectionUtils.isEmpty(skuVOList)){
            skuVOList.forEach(e -> {
                Integer type = e.getType();
                if(type == 1){
                    e.setTypeStr("造旺常规品项目");
                }else if (type ==2){
                    e.setTypeStr("造旺综合品项");
                }else if(type == 3){
                    e.setTypeStr("集团经典品项");
                }

                String specifyOrgCodes = e.getSpecifyOrgCodes();
                if(StringUtils.isNotBlank(specifyOrgCodes)){
                    List<EstimateSkuOrgVO> orgVOS = new ArrayList<>();
                    Arrays.asList(specifyOrgCodes.split(Constants.COMMA)).stream().filter(f -> !f.equals(EstimateConstants.ALL)).forEach(o -> {
                        EstimateSkuOrgVO estimateSkuOrgVO = new EstimateSkuOrgVO();
                        estimateSkuOrgVO.setOrganizationId(o);
                        estimateSkuOrgVO.setOrganizationName(organizationMapper.getOrganizationName(o));
                        orgVOS.add(estimateSkuOrgVO);
                    });
                    e.setOrgVOS(orgVOS);
                    String companyStr = String.join(",", orgVOS.stream().map(EstimateSkuOrgVO::getOrganizationName).collect(Collectors.toList()));
                    e.setCompanyStr(companyStr);
                    e.setChecked(true);
                }
            });


        }
        estimateSkuGroupDetailVO.setSkuVOList(skuVOList);

        return estimateSkuGroupDetailVO;
    }

    @Override
    public List<EstimateSkuVO> catchSku(int businessGroup,Long shipPeriodId) {

        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroup);

        QuerySkuRequest querySkuRequest = new QuerySkuRequest();
        querySkuRequest.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
        querySkuRequest.setSupplyPhaseFlag(shipPeriodId.intValue());
        List<SkuInfoDTO> skuInfoDTOS = estimateClient.querySkuInfo(querySkuRequest);

        if(CollectionUtils.isEmpty(skuInfoDTOS)){
            return ListUtils.EMPTY_LIST;
        }

        List<EstimateSkuVO> list = new ArrayList<>();
        skuInfoDTOS.stream().forEach(e -> {
            EstimateSkuVO estimateSkuVO = new EstimateSkuVO();
            estimateSkuVO.setType(e.getSkuCategoryFlag());

            Integer type = e.getSkuCategoryFlag();
            if(type == 1){
                estimateSkuVO.setTypeStr("造旺常规品项目");
            }else if (type ==2){
                estimateSkuVO.setTypeStr("造旺综合品项");
            }else if(type == 3){
                estimateSkuVO.setTypeStr("集团经典品项");
            }
            estimateSkuVO.setLineName(e.getLineName());
            estimateSkuVO.setLineId(e.getLineId());
            estimateSkuVO.setSpu(e.getSpuId());
            estimateSkuVO.setSpuName(e.getSpuName());
            estimateSkuVO.setSku(e.getSku());
            estimateSkuVO.setSkuName(e.getSkuName());
            estimateSkuVO.setFlavor(e.getFlavor());
            estimateSkuVO.setFullCaseSpec(e.getSkuSpec());
            estimateSkuVO.setSalesPrices(Optional.ofNullable(e.getThirdOrderPrice()).orElse(BigDecimal.ZERO));
            if(Objects.nonNull(e.getShelfLife())){
                estimateSkuVO.setShelfLife(String.valueOf(e.getShelfLife()));
            }


            List<String> saleCompanyNameList = e.getSaleCompanyNameList();
            if(!CollectionUtils.isEmpty(saleCompanyNameList)){
                List<EstimateSkuOrgVO> orgVOS = new ArrayList<>();
                saleCompanyNameList.forEach(c -> {
                    EstimateSkuOrgVO estimateSkuOrgVO = new EstimateSkuOrgVO();
                    String organizationIdByName = organizationMapper.getOrganizationIdByName(c, 3, RequestUtils.getBusinessGroup());
                    estimateSkuOrgVO.setOrganizationName(c);
                    estimateSkuOrgVO.setOrganizationId(organizationIdByName);
                    orgVOS.add(estimateSkuOrgVO);
                });
                estimateSkuVO.setOrgVOS(orgVOS);
            }

            estimateSkuVO.setExpectListMonth(e.getExpectListMonth());
            estimateSkuVO.setWarnPercent(e.getWarnPercent());
            List<String> tagNameList = e.getTagNameList();
            if(!CollectionUtils.isEmpty(tagNameList)){
                estimateSkuVO.setTagName(String.join(",",tagNameList));
            }
            List<ActivityTagDTO> skuActivityTagList = e.getSkuActivityTagList();
            if(!CollectionUtils.isEmpty(skuActivityTagList)){
                List<String> activityImage = skuActivityTagList.stream().filter(f -> StringUtils.isNotBlank(f.getDetailImage())).map(ActivityTagDTO::getDetailImage).collect(Collectors.toList());
                estimateSkuVO.setActivityImageUrl(String.join(",",activityImage));

                List<String> activityDescription = skuActivityTagList.stream().filter(f -> StringUtils.isNotBlank(f.getActivityTagName())).map(ActivityTagDTO::getActivityTagName).collect(Collectors.toList());
                estimateSkuVO.setActivityDescription(String.join(",",activityDescription));
            }


            String moq = e.getMoq();
            boolean number = NumberUtil.isNumber(moq);
            if(!number){
                throw new ApplicationException("SKU MOQ异常");
            }
            estimateSkuVO.setMOQ(new BigDecimal(moq).intValue());


            list.add(estimateSkuVO);
        });


        return list;
    }

    @Override
    @Transactional
    public EstimateScheduleDO saveSchedule(EstimateScheduleDO estimateScheduleDO, ProcessUserDO processUserDO) {
        log.info("【save estimate schedule】estimateScheduleDO:{},processUserDO:{}",estimateScheduleDO,processUserDO);

        Long scheduleId = estimateScheduleDO.getScheduleId();

        // 插入组织
        List<EstimateOrganizationDO> estimateOrganizationDOList = Optional.ofNullable(estimateScheduleDO.getEstimateOrganizationDOList()).orElse(new ArrayList<>());

        // 检查是否有重复的时间
        boolean conflict = estimateSkuRepository.checkScheduleConflict(estimateScheduleDO.getStartDate(),estimateScheduleDO.getEndDate(),estimateScheduleDO.getTheYearMonth(),estimateScheduleDO.getScheduleId(),estimateScheduleDO.getBusinessGroup(),estimateScheduleDO.getShipPeriodId(),estimateOrganizationDOList);
        if(conflict){
            throw new ApplicationException(EstimateErrorMsgEnum.SCHEDULE_CONFLICT.getMsg());
        }

        // 结束日期不得晚于货需月的最后一天
        String theYearMonth = estimateScheduleDO.getTheYearMonth();
        LocalDate date = LocalDate.parse(theYearMonth + "-10").with(TemporalAdjusters.lastDayOfMonth());
        if(date.isBefore(estimateScheduleDO.getEndDate())){
            throw new ApplicationException(EstimateErrorMsgEnum.SCHEDULE_DATE_ERROR.getMsg());
        }

        EstimateSchedulePO estimateSchedulePO = EstimateSkuFactory.initEstimateSchedule(estimateScheduleDO,processUserDO);

        if(Objects.nonNull(scheduleId)){
            // 修改
            estimateSkuRepository.updateEstimateSchedule(estimateSchedulePO);
        }else{
            // 新增
            estimateSkuRepository.insertEstimateSchedule(estimateSchedulePO);
            estimateScheduleDO.setScheduleId(estimateSchedulePO.getScheduleId());
        }

        // 根据scheduleId删除历史
        log.info("【save estimate schedule】clear organization relation,scheduleId:{}",estimateSchedulePO.getScheduleId());
        estimateSkuRepository.clearScheduleOrganization(estimateSchedulePO.getScheduleId(),processUserDO);


        List<String> companyCodes = estimateOrganizationDOList.stream().map(EstimateOrganizationDO::getCompanyCode).collect(Collectors.toList());
        List<EstimateScheduleOrganizationRelationPO> list =  EstimateSkuFactory.initEstimateScheduleOrganizationList(companyCodes,estimateScheduleDO.getScheduleId(),processUserDO);
        estimateSkuRepository.saveEstimateOrganizationList(list);

        return estimateScheduleDO;
    }

    @Override
    public List<EstimateScheduleVO> selectSchedule(String yearMonth,Integer businessGroup) {
        log.info("【select estimate schedule】yearMonth:{},businessGroup:{}",yearMonth,businessGroup);

        List<EstimateScheduleVO> list = estimateSkuRepository.selectSchedule(yearMonth,businessGroup);

        if(!CollectionUtils.isEmpty(list)){
            LocalDate localDate = LocalDate.now();
            list.forEach(e -> {
                e.setTypeStr(EstimateTypeEnum.getTypeName(e.getType()));

                int days = localDate.compareTo(e.getEndDate());

                if(days <= 3){
                    e.setCanModify(true);
                }
            });
        }
        return list;
    }

    @Override
    public List<EstimateSkuGroupSelectVO> selectEstimateSkuGroup(int businessGroup) {
        List<EstimateSkuGroupVO> estimateSkuGroupVOS = estimateSkuRepository.selectEstimateGroupByBusinessGroup(businessGroup);

        if(CollectionUtils.isEmpty(estimateSkuGroupVOS)){
            return ListUtils.EMPTY_LIST;
        }
        List<EstimateSkuGroupSelectVO> list= new ArrayList<>();
        estimateSkuGroupVOS.forEach(e -> {
            EstimateSkuGroupSelectVO estimateSkuGroupVO = new EstimateSkuGroupSelectVO();
            estimateSkuGroupVO.setGroupId(e.getGroupId());
            estimateSkuGroupVO.setGroupName(e.getGroupName());
            list.add(estimateSkuGroupVO);
        });
        return list;
    }

    @Override
    public EstimateScheduleDetailVO selectScheduleDetail(Long scheduleId) {
        EstimateScheduleDetailVO estimateScheduleDetailVO = estimateSkuRepository.selectScheduleDetail(scheduleId);
        if(Objects.nonNull(estimateScheduleDetailVO)){
            estimateScheduleDetailVO.setTypeStr(EstimateTypeEnum.getTypeName(estimateScheduleDetailVO.getType()));
        }

        return estimateScheduleDetailVO;
    }

    @Override
    public List<EstimateOrganizationVO> selectEstimateOrg(EstimateOrgSearchDO estimateOrgSearchDO) {
        return estimateSkuRepository.selectEstimateOrg(estimateOrgSearchDO);
    }

    @Override
    public void exportSkuList(EstimateSkuExportRequest estimateSkuExportRequest) {
        List<EstimateSkuVO> skuVOList = estimateSkuExportRequest.getSkuVOList();
        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "物料组清单" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), EstimateSkuVO.class, skuVOList);
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }


    @Override
    public void setShipPeriod(EstimateShipPeriodDO estimateShipPeriodDO, ProcessUserDO processUserDO) {
        EstimateShipPeriodPO estimateShipPeriodPO = estimateSkuRepository.selectShipPeriodById(estimateShipPeriodDO.getId());
        if(Objects.isNull(estimateShipPeriodPO)){
            throw new ApplicationException("货需不存在");
        }

        Integer status = estimateShipPeriodDO.getStatus();
        if(Objects.nonNull(status)){
            estimateShipPeriodPO.setStatus(status);
        }

        String batchPeriod = estimateShipPeriodDO.getBatchPeriod();
        if(StringUtils.isNotBlank(batchPeriod)){
            estimateShipPeriodPO.setBatchPeriod(batchPeriod);
        }
        String deliveryDeadline = estimateShipPeriodDO.getDeliveryDeadline();
        if(StringUtils.isNotBlank(deliveryDeadline)){
            estimateShipPeriodPO.setDeliveryDeadline(deliveryDeadline);
        }
        estimateShipPeriodPO.setUpdateUserId(processUserDO.getEmployeeId());
        estimateShipPeriodPO.setUpdateUserName(processUserDO.getEmployeeName());
        estimateShipPeriodPO.setUpdateTime(LocalDateTime.now());
        estimateSkuRepository.updateShipPeriod(estimateShipPeriodPO);
    }

    @Override
    public List<ShipPeriodVO> selectShipPeriod(Integer status) {

        List<EstimateShipPeriodPO> list = estimateSkuRepository.selectShipPeriod(status);
        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }

        List<ShipPeriodVO> result = new ArrayList<>();
        list.forEach(e -> {
            ShipPeriodVO vo = new ShipPeriodVO();
            BeanUtils.copyProperties(e,vo);
            LocalDateTime updateTime = e.getUpdateTime();
            if(Objects.nonNull(updateTime)){
                vo.setUpdateTime(LocalDateTimeUtils.formatTime(updateTime,EstimateConstants.TIME_FORMAT));
            }

            result.add(vo);
        });
        return result;
    }

    @Override
    @Transactional
    public void saveOrUpdateSku(List<EstimateApprovalDetailDO> skuList,Integer businessGroup,ProcessUserDO processUserDO) {
        skuList.forEach(e -> {
            EstimateSkuPO estimateSkuPO = estimateSkuRepository.selectSkuByCode(e.getSku(),businessGroup);

            if(Objects.nonNull(estimateSkuPO)){
                estimateSkuPO.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
                BeanUtils.copyProperties(e,estimateSkuPO);
                estimateSkuPO.setTagName(e.getTagNameList());
                estimateSkuPO.setThirdOrderPrice(e.getSalePrice());
                estimateSkuPO.setBusinessGroup(businessGroup);
                estimateSkuPO.setActivityImageUrl(e.getActivityImgUrl());
                estimateSkuPO.setActivityDescription(e.getActivityTags());
                estimateSkuPO.setType(e.getSkuCategoryFlag());
                estimateSkuRepository.updateEstimateSku(estimateSkuPO);
            }else{
                estimateSkuPO = new EstimateSkuPO();
                estimateSkuPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
                BeanUtils.copyProperties(e,estimateSkuPO);
                estimateSkuPO.setThirdOrderPrice(e.getSalePrice());
                estimateSkuPO.setTagName(e.getTagNameList());
                estimateSkuPO.setBusinessGroup(businessGroup);
                estimateSkuPO.setActivityImageUrl(e.getActivityImgUrl());
                estimateSkuPO.setActivityDescription(e.getActivityTags());
                estimateSkuPO.setType(e.getSkuCategoryFlag());
                estimateSkuRepository.insertEstimateSku(estimateSkuPO);
            }
        });
    }


    private void saveOrganizationRelation(List<EstimateSkuPO> estimateSkuPOS, List<EstimateSkuDO> skuDOList, Long groupId, ProcessUserDO processUserDO) {
        // 根据groupId获取所有sku信息
        List<EstimateSkuOrganizationRelationPO> estimateSkuOrganizationRelationPOS = Optional.ofNullable(estimateSkuRepository.selectSkuRelationByGroupId(groupId)).orElse(new ArrayList<>());
        // 所有标记为删除
        if(!CollectionUtils.isEmpty(estimateSkuOrganizationRelationPOS)){
            estimateSkuRepository.deleteAllOrganizationRelationByGroupId(groupId,processUserDO);
        }


        estimateSkuPOS.stream().forEach(e -> {
            Optional<EstimateSkuDO> skuDOOptional = skuDOList.stream().filter(f -> f.getSku().equals(e.getSku())).findFirst();
            if(!skuDOOptional.isPresent()){
                throw new ApplicationException(EstimateErrorMsgEnum.SKU_ERROR.getMsg());
            }
            EstimateSkuDO estimateSkuDO = skuDOOptional.get();
            List<EstimateSkuOrgDO> estimateSkuOrgDOS = estimateSkuDO.getOrgVOS();
            if(!CollectionUtils.isEmpty(estimateSkuOrgDOS)){
                estimateSkuOrgDOS.forEach(s -> {
                    // 保存关系
                    estimateSkuRepository.saveOrganizationRelation(EstimateSkuFactory.initEstimateSkuOrganization(s.getOrganizationId(),organizationMapper.getOrganizationType(s.getOrganizationId()),e.getSkuId(),e.getSku(),groupId,processUserDO));
                });
            }else{
                estimateSkuRepository.saveOrganizationRelation(EstimateSkuFactory.initEstimateSkuOrganization(EstimateConstants.ALL,EstimateConstants.ALL,e.getSkuId(),e.getSku(),groupId,processUserDO));
            }
        });
    }


    private Long saveOrUpdateSkuGroup(Long groupId, String groupName, Long shipPeriodId, Integer businessGroup, ProcessUserDO processUserDO) {
        EstimateSkuGroupPO estimateSkuGroupPO = null;
        if(Objects.nonNull(groupId)){
            estimateSkuGroupPO = estimateSkuRepository.selectSkuGroupById(groupId);
            if(Objects.isNull(estimateSkuGroupPO)){
                throw new ApplicationException(EstimateErrorMsgEnum.GROUP_NAME_DUPLICATION.getMsg());
            }
            estimateSkuGroupPO.setGroupName(groupName);
            estimateSkuGroupPO.setBusinessGroup(businessGroup);
            estimateSkuGroupPO.setShipPeriodId(shipPeriodId);
            estimateSkuGroupPO.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
            estimateSkuRepository.updateEstimateSkuGroup(estimateSkuGroupPO);
        }else{
            estimateSkuGroupPO = EstimateSkuFactory.initEstimateSkuGroup(groupName, businessGroup,processUserDO);
            estimateSkuGroupPO.setShipPeriodId(shipPeriodId);
            estimateSkuRepository.insertEstimateSkuGroup(estimateSkuGroupPO);
        }

        return estimateSkuGroupPO.getGroupId();
    }


}
