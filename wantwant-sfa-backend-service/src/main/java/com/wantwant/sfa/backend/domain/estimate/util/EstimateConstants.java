package com.wantwant.sfa.backend.domain.estimate.util;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/上午8:22
 */
public class EstimateConstants {

    public static final String ALL = "all";
    public static final String CEO_FLOW_CODE = "TS00000002";
    public static final String DEPARTMENT_FLOW_CODE = "TS00000003";
    public static final String COMPANY_FLOW_CODE = "TS00000004";
    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String APPROVAL_TITLE = "{0}年{1}月 {2} 销售预估（ {3} ）";
    public static final String NOT_SUBMIT = "未提报";
    public static final String PASS = "已通过";
    public static final String REJECT = "已驳回";
    public static final String PROCESSING = "审核中";
    public static final String SUBMIT_LOCKED = "sfa:estimate:submit";
    public static final String OPERATION_LOCK = "sfa:estimate:operation";
    public static final String CEO_APPLY_LOCK = "sfa:estimate:ceo:apply";
}
