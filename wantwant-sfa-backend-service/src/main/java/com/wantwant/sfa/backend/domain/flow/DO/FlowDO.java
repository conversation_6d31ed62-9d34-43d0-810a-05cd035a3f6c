package com.wantwant.sfa.backend.domain.flow.DO;

import com.wantwant.sfa.backend.domain.flow.enums.AuditStrategyEnum;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:53
 */
@Data
@ToString
public class FlowDO {
    /** 流程code */
    private String flowCode;
    /** 审核类型 */
    private AuditStrategyEnum auditStrategyEnum;
    /** 组织类型 */
    private String organizationType;
    /** 角色ID */
    private Integer roleId;
    /** 工号 */
    private String employeeId;
    /** 创建人工号 */
    private String createUserId;
    /** 创建人名称 */
    private String createUserName;
    /** 处理人信息 */
    private FlowProcessUserDO flowProcessUserDO;


    // 设置处理人信息
    public void setProcessUser(Integer roleId,CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation,String organizationType,int businessGroup){
        AuditStrategyEnum auditStrategyEnum = this.getAuditStrategyEnum();
        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();
        flowProcessUserDO.setBusinessGroup(businessGroup);
        if(auditStrategyEnum.getId() == AuditStrategyEnum.POSITION_TYPE_STRATEGY.getId() || auditStrategyEnum.getId() == AuditStrategyEnum.EMP_ID_STRATEGY.getId()){
            flowProcessUserDO.setOrganizationId(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            flowProcessUserDO.setEmployeeName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
            flowProcessUserDO.setEmployeeId(ceoBusinessOrganizationPositionRelation.getEmployeeId());

            this.organizationType = organizationType;

        }else if(auditStrategyEnum.getId() == AuditStrategyEnum.ROLE_STRATEGY.getId()){
            flowProcessUserDO.setRoleId(roleId);
            this.roleId = roleId;
        }

        this.flowProcessUserDO = flowProcessUserDO;
    }
}
