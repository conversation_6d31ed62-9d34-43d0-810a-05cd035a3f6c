package com.wantwant.sfa.backend.domain.flow.DO;

import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午3:34
 */
@Data
@ToString
public class FlowPassDO {
    /** 实例ID */
    private Long instanceId;
    /** 备注 */
    private String comment;

    /** 当前流程处理人 */
    private FlowProcessUserDO currentProcessUserDO;

    /** 下级节点处理人 */
    private FlowProcessUserDO nextProcessUserDO;

    /** 下一节点步骤 */
    private Integer nextProcessStep;

    /** 是否跳过下一节点 */
    private boolean skipNextPoint;

    /** 审核信息 */
    private String extraInfo;

    private Integer businessGroup;

    /** 冗余字段 */
    private FlowRuleDO flowRuleDO;
}
