package com.wantwant.sfa.backend.domain.flow.DO;

import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午7:45
 */
@Data
@ToString
public class FlowRuleDO {
    /** 规则ID */
    private Long ruleId;
    /** 步骤 */
    private Integer step;
    /** 步骤名称 */
    private String processName;
    /** 审核策略 */
    private Integer auditStrategy;
    /** 驳回策略 */
    private Integer rejectStrategy;
    /** 跳过策略 */
    private Integer skipStrategy;
    /** 配置组织类型 */
    private String organizationType;
    /** 配置角色ID */
    private Integer roleId;
    /** 配置工号 */
    private String employeeId;
    /** 是否必须 */
    private Integer requireFlag;
}
