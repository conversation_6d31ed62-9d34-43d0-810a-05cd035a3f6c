package com.wantwant.sfa.backend.domain.flow.DO;

import com.wantwant.sfa.backend.domain.jobTransfer.DO.ChangeOrganizationDO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.ChangeSalaryDO;
import com.wantwant.sfa.backend.jobTransfer.request.ChangeSalaryRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/07/上午11:17
 */
@Data
public class JobTransferAuditDO {

    @ApiModelProperty("异动ID")
    @NotNull(message = "缺少异动ID")
    private Long transactionId;

    @ApiModelProperty("处理人工号")
    @NotBlank(message = "缺少处理人工号")
    private String person;

    private ChangeSalaryDO changeSalaryDO;

    @ApiModelProperty("异动后兼岗信息")
    private List<ChangeOrganizationDO> changePartTimeOrg;

    @ApiModelProperty("处理结果")
    private Integer result;

    @ApiModelProperty("备注")
    private String remark;

}
