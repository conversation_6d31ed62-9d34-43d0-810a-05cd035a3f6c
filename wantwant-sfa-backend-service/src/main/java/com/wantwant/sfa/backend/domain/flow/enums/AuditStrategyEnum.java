package com.wantwant.sfa.backend.domain.flow.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:54
 */

public enum AuditStrategyEnum {
    POSITION_TYPE_STRATEGY(10,"按岗位审核"),
    ROLE_STRATEGY(20,"按角色审核"),
    EMP_ID_STRATEGY(30,"按工号审核"),
    EXTERNAL_SYSTEM_STRATEGY(40,"外部系统审核"),
    ORG_TYPE_STRATEGY(50,"按组织审核");


    private Integer id;

    private String name;

    public static AuditStrategyEnum getStrategyEnumById(Integer auditStrategy) {
        AuditStrategyEnum[] values = AuditStrategyEnum.values();
        for(AuditStrategyEnum e : values){
            if(e.getId() == auditStrategy){
                return e;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    AuditStrategyEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }
}
