package com.wantwant.sfa.backend.domain.flow.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/上午11:00
 */
public enum InterviewProcessUserTypeEnum {
    SKIP(0,"不需审核"),
    SUPERIOR(1,"上级审核"),
    BOSS(2,"老板审核");

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    InterviewProcessUserTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
