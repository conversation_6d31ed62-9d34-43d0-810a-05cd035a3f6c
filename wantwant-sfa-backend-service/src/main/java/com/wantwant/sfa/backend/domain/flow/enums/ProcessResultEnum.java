package com.wantwant.sfa.backend.domain.flow.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午3:00
 */
public enum ProcessResultEnum {
    WAIT(0,"待处理"),
    PASS(1,"审核通过"),
    FAIL(2,"审核驳回"),
    CACHE(3,"暂存"),
    CLOSE(4," 关闭");

    private int result;

    private String name;

    public static String findNameByResult(Integer result) {
        ProcessResultEnum[] values = ProcessResultEnum.values();
        for(ProcessResultEnum e: values){
            if(e.getResult() == result){
                return e.getName();
            }
        }
        return null;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    ProcessResultEnum(int result, String name) {
        this.result = result;
        this.name = name;
    }
}
