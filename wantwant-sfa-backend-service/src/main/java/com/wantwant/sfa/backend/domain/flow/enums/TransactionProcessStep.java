package com.wantwant.sfa.backend.domain.flow.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/下午2:28
 */
public enum TransactionProcessStep {
    HR_PROCESS(1,"人资处理"),
    SUPERIOR(20,"上级主管处理"),
    HIGH_UP_SUPERIOR(21,"上级主管处理"),
    OPERATION(30,"运营审核"),
    BOSS(40,"老板审核");

    private int processStep;

    private String name;

    public int getProcessStep() {
        return processStep;
    }

    public void setProcessStep(int processStep) {
        this.processStep = processStep;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    TransactionProcessStep(int processStep, String name) {
        this.processStep = processStep;
        this.name = name;
    }
}
