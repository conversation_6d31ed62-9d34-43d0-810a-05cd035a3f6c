package com.wantwant.sfa.backend.domain.flow.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/下午2:31
 */
public enum TransactionResultEnum {
    PROCESSING(0,"待处理"),
    PASS(1,"审核通过"),
    FAIL(2,"审核失败");

    private int status;

    private String name;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    TransactionResultEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }
}
