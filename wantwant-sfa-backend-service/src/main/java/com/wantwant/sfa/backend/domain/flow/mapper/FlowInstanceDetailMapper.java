package com.wantwant.sfa.backend.domain.flow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.agent.vo.AgentVo;
import com.wantwant.sfa.backend.domain.flow.DO.FlowCurrentDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowDetailDO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:35
 */
public interface FlowInstanceDetailMapper extends BaseMapper<FlowInstanceDetailPO> {


    List<FlowCurrentDO> findInstanceId(@Param("processResult") Integer processResult, @Param("person") String person, @Param("roleIds") List<Integer> roleIds);

    List<FlowDetailDO> findDetailsByInstanceId(@Param("instanceId") Long instanceId);


    List<AgentVo> findAgent(@Param("person") String person, @Param("roleIds") List<Integer> roleIds,@Param("businessGroup") int businessGroup);


    void replaceProcessUserId(@Param("oldEmpId") String oldEmpId, @Param("empId") String empId);

    /**
     * 检查流程中是否包含特定组织
     *
     * @param instanceId
     * @param orgType
     * @return
     */
    Integer containsOrganizationType(@Param("instanceId") Long instanceId, @Param("orgType") String orgType);


    /**
     * 根据流程实例ID查询流程编码
     *
     * @param instanceId
     * @return
     */
    String selectFlowCodeByInstanceId(@Param("instanceId") Long instanceId);

}
