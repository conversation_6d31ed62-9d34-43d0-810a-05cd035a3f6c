package com.wantwant.sfa.backend.domain.flow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wantwant.sfa.backend.domain.flow.DO.FlowRuleDO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowRulePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:34
 */
public interface FlowRuleMapper extends BaseMapper<FlowRulePO> {

    List<FlowRulePO> findNextPointRule(@Param("instanceId") Long instanceId, @Param("processStep") Integer processStep);

    List<FlowRulePO> findCurrentRule(@Param("detailId") Long detailId, @Param("processStep") Integer processStep);

    /**
     * 获取最近一个节点的规则
     *
     * @param flowCode 流程code
     * @param organizationType 审核人组织类型
     * @param step 步骤
     * @return
     */
    FlowRuleDO findRule(@Param("flowCode") String flowCode, @Param("organizationType") String organizationType,@Param("step")int step);

    /**
     * 获取当前节点的下一个规则
     *
     * @param instanceId
     * @return
     */
    FlowRuleDO findCurrentNextRule(@Param("instanceId") Long instanceId);

    FlowRuleDO findCurrentRuleByInstanceId(@Param("instanceId") Long flowInstanceId);

    Integer findLastStep(@Param("instanceId") Long flowInstanceId);
}
