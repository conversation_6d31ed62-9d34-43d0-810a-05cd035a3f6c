package com.wantwant.sfa.backend.domain.flow.repository.facade;

import com.wantwant.sfa.backend.agent.vo.AgentVo;
import com.wantwant.sfa.backend.domain.flow.DO.*;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstancePO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowRulePO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午2:06
 */
public interface IFlowRepository {

    /**
     * 检查code是否存在
     *
     * @param flowCode
     * @return
     */
    Long checkFlowExistsByCode(String flowCode);

    /**
     * 根据审核人组织类型获取规则
     *
     * @param organizationType
     * @param flowId
     * @return
     */
    List<FlowRulePO> findByOrganizationType(String organizationType, Long flowId,Integer step);

    /**
     * 根据组织类型获取最小的步骤
     *
     * @param organizationType
     * @param flowId
     * @return
     */
    Integer findMinStepByOrganizationType(String organizationType, Long flowId);

    /**
     * 根据角色ID获取最小的步骤
     *
     * @param roleId
     * @param flowId
     * @return
     */
    Integer findMinStepByRoleId(Integer roleId, Long flowId);

    /**
     * 根据审核人角色获取规则
     *
     * @param roleId
     * @param flowId
     * @param roleStep
     * @return
     */
    List<FlowRulePO> findByRoleId(Integer roleId, Long flowId, Integer roleStep);

    /**
     * 根据工号获取最小的步骤
     *
     * @param employeeId
     * @param flowId
     * @return
     */
    Integer findMinStepByEmpId(String employeeId, Long flowId);

    /**
     * 根据工号获取规则
     *
     * @param employeeId
     * @param flowId
     * @param empIdStep
     * @return
     */
    List<FlowRulePO> findByEmpId(String employeeId, Long flowId, Integer empIdStep);

    /**
     * 创建流程实例
     *
     * @param initFlowInstance
     * @return
     */
    Long initInstance(FlowInstancePO initFlowInstance);

    /**
     * 创建流程明细
     *
     * @param initFlowInstanceDetail
     */
    Long initInstanceDetail(FlowInstanceDetailPO initFlowInstanceDetail);

    /**
     * 获取下个审核节点
     *
     * @param instanceId
     * @param processStep
     * @return
     */
    List<FlowRulePO> findNextPointRule(Long instanceId, Integer processStep);

    /**
     * 获取实例明细
     *
     * @param instanceId
     * @return
     */
    FlowInstancePO findInstanceById(Long instanceId);

    /**
     * 当前流程设置为完成
     *
     * @param flowInstancePO
     */
    void finishInstance(FlowInstancePO flowInstancePO);

    /**
     * 修改明细
     *
     * @param instanceDetail
     */
    void updateInstanceDetail(FlowInstanceDetailPO instanceDetail);

    void updateInstance(FlowInstancePO flowInstancePO);

    /**
     * 获取当前流程记录
     *
     * @param instanceId
     * @return
     */
    FlowInstanceDetailPO findInstanceDetail(Long instanceId);

    /**
     *
     *
     * @param detailId
     * @param processStep
     * @return
     */
    List<FlowRulePO> findCurrentRule(Long detailId, Integer processStep);

    /**
     * 获取规则
     *
     * @param flowCode
     * @param organizationType
     * @return
     */
    FlowRuleDO findRule(String flowCode, String organizationType,int step);

    /**
     * 获取当前节点的下一个规则
     *
     * @param instanceId
     * @return
     */
    FlowRuleDO findCurrentNextRule(Long instanceId);

    /**
     * 获取当前审核节点
     *
     * @param instanceId
     * @return
     */
    FlowInstanceDetailPO findCurrentFlow(Long instanceId);

    /**
     * 根据查询条件获取实例ID
     *
     * @param processResult
     * @param person
     * @param roleIds
     * @return
     */
    List<FlowCurrentDO> findInstanceId(Integer processResult, String person, List<Integer> roleIds);

    /**
     * 获取审核记录
     *
     * @param flowInstanceId
     * @return
     */
    List<FlowDetailDO> findDetailsByInstanceId(Long flowInstanceId);

    /**
     * 根据流程实例获取当前规则
     *
     * @param flowInstanceId
     * @return
     */
    FlowRuleDO findCurrentRuleByInstanceId(Long flowInstanceId);

    List<AgentVo> findAgent(String person, List<Integer> roleIds,int businessGroup);

    /**
     * 根据instanceId获取审核记录
     *
     * @param instanceId
     * @return
     */
    List<FlowInstanceDetailPO> getFlowDetailByInstanceId(Long instanceId);

    /**
     * 获取最后一步
     *
     * @param flowInstanceId
     * @return
     */
    Integer findLastStep(Long flowInstanceId);

    /**
     * 查询当前流程是否包含组织类型
     *
     * @param instanceId
     * @param orgType
     * @return
     */
    Integer containsOrganizationType(Long instanceId, String orgType);

    /**
     * 获取流程编码
     *
     * @param instanceId
     * @return
     */
    String selectFlowCodeByInstanceId(Long instanceId);

}
