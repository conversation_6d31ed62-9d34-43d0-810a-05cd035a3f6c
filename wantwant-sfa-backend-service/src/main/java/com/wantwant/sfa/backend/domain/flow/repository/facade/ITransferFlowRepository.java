package com.wantwant.sfa.backend.domain.flow.repository.facade;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessRecordEntity;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/上午11:45
 */
public interface ITransferFlowRepository {

    /**
     * 获取岗位类型
     *
     * @param employeeInfoId
     * @return
     */
    Integer selectPositionType(Integer employeeInfoId);

    /**
     * 保存异动流程
     *
     * @param sfaTransactionProcessEntity
     */
    void saveTransactionProcess(SfaTransactionProcessEntity sfaTransactionProcessEntity);

    /**
     * 保存异动明细
     *
     * @param sfaTransactionProcessRecordEntity
     */
    void saveTransactionProcessRecord(SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity);

    /**
     * 修改异动流程
     *
     * @param sfaTransactionProcessEntity
     */
    void updateTransactionProcess(SfaTransactionProcessEntity sfaTransactionProcessEntity);

    /**
     * 根据异动申请ID获取异动主流程
     *
     * @param transactionId
     * @return
     */
    SfaTransactionProcessEntity selectProcessByApplyId(Long transactionId);

    /**
     * 根据ID获取异动记录表
     *
     * @param transactionRecordId
     * @return
     */
    SfaTransactionProcessRecordEntity selectProcessRecordById(Long transactionRecordId);

    /**
     * 修改异动流程记录
     *
     * @param sfaTransactionProcessRecordEntity
     */
    void updateTransactionRecord(SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity);

    /**
     * 获取异动申请信息
     *
     * @param transactionId
     * @return
     */
    SfaTransactionApplyEntity selectTransactionApplyById(Long transactionId);

    /**
     * 保存任务
     *
     * @param sfaJobPositionTask
     */
    void insertJobPositionTask(SfaJobPositionTask sfaJobPositionTask);


}
