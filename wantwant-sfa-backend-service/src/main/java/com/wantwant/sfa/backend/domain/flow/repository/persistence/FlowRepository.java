package com.wantwant.sfa.backend.domain.flow.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.agent.vo.AgentVo;
import com.wantwant.sfa.backend.domain.flow.DO.FlowCurrentDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowDetailDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowRuleDO;
import com.wantwant.sfa.backend.domain.flow.enums.ProcessResultEnum;
import com.wantwant.sfa.backend.domain.flow.mapper.FlowDefinitionMapper;
import com.wantwant.sfa.backend.domain.flow.mapper.FlowInstanceDetailMapper;
import com.wantwant.sfa.backend.domain.flow.mapper.FlowInstanceMapper;
import com.wantwant.sfa.backend.domain.flow.mapper.FlowRuleMapper;
import com.wantwant.sfa.backend.domain.flow.repository.facade.IFlowRepository;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowDefinitionPO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstancePO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowRulePO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午2:06
 */
@Repository
public class FlowRepository implements IFlowRepository {

    @Resource
    private FlowDefinitionMapper flowDefinitionMapper;
    @Resource
    private FlowRuleMapper flowRuleMapper;
    @Resource
    private FlowInstanceMapper flowInstanceMapper;
    @Resource
    private FlowInstanceDetailMapper flowInstanceDetailMapper;

    @Override
    public Long checkFlowExistsByCode(String flowCode) {
        FlowDefinitionPO flowDefinitionPO = flowDefinitionMapper.selectOne(new LambdaQueryWrapper<FlowDefinitionPO>().eq(FlowDefinitionPO::getFlowCode, flowCode).eq(FlowDefinitionPO::getStatus, 1).eq(FlowDefinitionPO::getDeleteFlag, 0).last("limit 1"));
        if(Objects.isNull(flowDefinitionPO)){
            return null;
        }
        return flowDefinitionPO.getFlowId();
    }

    @Override
    public List<FlowRulePO> findByOrganizationType(String organizationType, Long flowId,Integer step) {
        List<FlowRulePO> flowRulePOS = flowRuleMapper.selectList(new LambdaQueryWrapper<FlowRulePO>().eq(FlowRulePO::getOrganizationType, organizationType)
                .eq(FlowRulePO::getStep,step).eq(FlowRulePO::getFlowId, flowId).eq(FlowRulePO::getDeleteFlag, 0));
        return flowRulePOS;
    }

    @Override
    public Integer findMinStepByOrganizationType(String organizationType, Long flowId) {
        FlowRulePO flowRulePO = flowRuleMapper.selectOne(new LambdaQueryWrapper<FlowRulePO>().eq(FlowRulePO::getOrganizationType, organizationType).eq(FlowRulePO::getFlowId, flowId).eq(FlowRulePO::getDeleteFlag, 0).orderByAsc(FlowRulePO::getStep).last("limit 1"));
        if(Objects.isNull(flowRulePO)){
            return null;
        }

        return flowRulePO.getStep();
    }

    @Override
    public Integer findMinStepByRoleId(Integer roleId, Long flowId) {
        FlowRulePO flowRulePO = flowRuleMapper.selectOne(new LambdaQueryWrapper<FlowRulePO>().eq(FlowRulePO::getRoleId, roleId).eq(FlowRulePO::getFlowId, flowId).eq(FlowRulePO::getDeleteFlag, 0).orderByAsc(FlowRulePO::getStep).last("limit 1"));
        if(Objects.isNull(flowRulePO)){
            return null;
        }

        return flowRulePO.getStep();
    }

    @Override
    public List<FlowRulePO> findByRoleId(Integer roleId, Long flowId, Integer step) {
        List<FlowRulePO> flowRulePOS = flowRuleMapper.selectList(new LambdaQueryWrapper<FlowRulePO>().eq(FlowRulePO::getRoleId, roleId)
                .eq(FlowRulePO::getStep,step).eq(FlowRulePO::getFlowId, flowId).eq(FlowRulePO::getDeleteFlag, 0));
        return flowRulePOS;
    }

    @Override
    public Integer findMinStepByEmpId(String employeeId, Long flowId) {
        FlowRulePO flowRulePO = flowRuleMapper.selectOne(new LambdaQueryWrapper<FlowRulePO>().eq(FlowRulePO::getEmployeeId, employeeId).eq(FlowRulePO::getFlowId, flowId).eq(FlowRulePO::getDeleteFlag, 0).orderByAsc(FlowRulePO::getStep).last("limit 1"));
        if(Objects.isNull(flowRulePO)){
            return null;
        }

        return flowRulePO.getStep();
    }

    @Override
    public List<FlowRulePO> findByEmpId(String employeeId, Long flowId, Integer step) {
        List<FlowRulePO> flowRulePOS = flowRuleMapper.selectList(new LambdaQueryWrapper<FlowRulePO>().eq(FlowRulePO::getEmployeeId, employeeId)
                .eq(FlowRulePO::getStep,step).eq(FlowRulePO::getFlowId, flowId).eq(FlowRulePO::getDeleteFlag, 0));
        return flowRulePOS;
    }

    @Override
    public Long initInstance(FlowInstancePO initFlowInstance) {
        flowInstanceMapper.insert(initFlowInstance);
        return initFlowInstance.getInstanceId();
    }

    @Override
    public Long initInstanceDetail(FlowInstanceDetailPO initFlowInstanceDetail) {
        flowInstanceDetailMapper.insert(initFlowInstanceDetail);
        return initFlowInstanceDetail.getDetailId();
    }


    @Override
    public FlowInstanceDetailPO findInstanceDetail(Long instanceId) {
        LambdaQueryWrapper<FlowInstanceDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowInstanceDetailPO::getInstanceId,instanceId);
        queryWrapper.eq(FlowInstanceDetailPO::getIsCurrent,1);
        queryWrapper.last("limit 1");
        FlowInstanceDetailPO flowInstanceDetailPO = flowInstanceDetailMapper.selectOne(queryWrapper);
        return flowInstanceDetailPO;
    }

    @Override
    public List<FlowRulePO> findCurrentRule(Long detailId, Integer processStep) {
        return flowRuleMapper.findCurrentRule(detailId,processStep);
    }

    @Override
    public FlowRuleDO findRule(String flowCode, String organizationType,int step) {
        return flowRuleMapper.findRule(flowCode,organizationType,step);
    }

    @Override
    public FlowRuleDO findCurrentNextRule(Long instanceId) {
        return flowRuleMapper.findCurrentNextRule(instanceId);
    }

    @Override
    public FlowInstanceDetailPO findCurrentFlow(Long instanceId) {
        return flowInstanceDetailMapper.selectOne(new LambdaQueryWrapper<FlowInstanceDetailPO>().eq(FlowInstanceDetailPO::getInstanceId,instanceId).eq(FlowInstanceDetailPO::getIsCurrent,1).last("limit 1"));
    }

    @Override
    public List<FlowCurrentDO> findInstanceId(Integer processResult, String person, List<Integer> roleIds) {
        return flowInstanceDetailMapper.findInstanceId(processResult,person,roleIds);
    }

    @Override
    public List<FlowDetailDO> findDetailsByInstanceId(Long instanceId) {
        return flowInstanceDetailMapper.findDetailsByInstanceId(instanceId);
    }

    @Override
    public FlowRuleDO findCurrentRuleByInstanceId(Long flowInstanceId) {
        return flowRuleMapper.findCurrentRuleByInstanceId(flowInstanceId);
    }

    @Override
    public List<AgentVo> findAgent(String person, List<Integer> roleIds,int businessGroup) {
        return flowInstanceDetailMapper.findAgent(person,roleIds,businessGroup);
    }

    @Override
    public List<FlowInstanceDetailPO> getFlowDetailByInstanceId(Long instanceId) {

        return flowInstanceDetailMapper.selectList(new LambdaQueryWrapper<FlowInstanceDetailPO>().eq(FlowInstanceDetailPO::getInstanceId,instanceId).eq(FlowInstanceDetailPO::getDeleteFlag,0)
                .orderByAsc(FlowInstanceDetailPO::getDetailId));

    }

    @Override
    public Integer findLastStep(Long flowInstanceId) {
        return flowRuleMapper.findLastStep(flowInstanceId);
    }

    @Override
    public Integer containsOrganizationType(Long instanceId, String orgType) {
        return flowInstanceDetailMapper.containsOrganizationType(instanceId,orgType);
    }

    @Override
    public String selectFlowCodeByInstanceId(Long instanceId) {
        return flowInstanceDetailMapper.selectFlowCodeByInstanceId(instanceId);
    }


    @Override
    public List<FlowRulePO> findNextPointRule(Long instanceId, Integer processStep) {
        return  flowRuleMapper.findNextPointRule(instanceId,processStep);
    }

    @Override
    public FlowInstancePO findInstanceById(Long instanceId) {
        return flowInstanceMapper.selectById(instanceId);
    }

    @Override
    public void finishInstance(FlowInstancePO flowInstancePO) {
        flowInstancePO.setResult(ProcessResultEnum.PASS.getResult());
        flowInstancePO.setUpdateTime(LocalDateTime.now());
        flowInstancePO.setCompleteTime(LocalDateTime.now());
        flowInstanceMapper.updateById(flowInstancePO);
    }

    @Override
    public void updateInstanceDetail(FlowInstanceDetailPO instanceDetail) {
        flowInstanceDetailMapper.updateById(instanceDetail);
    }

    @Override
    public void updateInstance(FlowInstancePO flowInstancePO) {
        flowInstanceMapper.updateById(flowInstancePO);
    }

}
