package com.wantwant.sfa.backend.domain.flow.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.domain.flow.repository.facade.ITransferFlowRepository;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaJobPositionTaskMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionApplyMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionProcessMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionProcessRecordMapper;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessRecordEntity;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/上午11:45
 */
@Repository
public class TransferFlowRepository implements ITransferFlowRepository {
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Resource
    private SfaTransactionProcessMapper sfaTransactionProcessMapper;

    @Resource
    private SfaTransactionProcessRecordMapper sfaTransactionProcessRecordMapper;

    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Resource
    private SfaTransactionApplyMapper sfaTransactionApplyMapper;
    @Resource
    private SfaJobPositionTaskMapper sfaJobPositionTaskMapper;

    @Override
    public Integer selectPositionType(Integer employeeInfoId) {
        SfaPositionRelationEntity sfaPositionRelationEntity = Optional.ofNullable(sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, employeeInfoId).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0).last("limit 1"))).orElse(new SfaPositionRelationEntity());

        return sfaPositionRelationEntity.getPositionTypeId();
    }

    @Override
    public void saveTransactionProcess(SfaTransactionProcessEntity sfaTransactionProcessEntity) {
        sfaTransactionProcessMapper.insert(sfaTransactionProcessEntity);
    }

    @Override
    public void saveTransactionProcessRecord(SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity) {
        sfaTransactionProcessRecordMapper.insert(sfaTransactionProcessRecordEntity);
    }

    @Override
    public void updateTransactionProcess(SfaTransactionProcessEntity sfaTransactionProcessEntity) {
        sfaTransactionProcessMapper.updateById(sfaTransactionProcessEntity);
    }

    @Override
    public SfaTransactionProcessEntity selectProcessByApplyId(Long transactionId) {
        return sfaTransactionProcessMapper.selectOne(new LambdaQueryWrapper<SfaTransactionProcessEntity>().eq(SfaTransactionProcessEntity::getTransactionApplyId,transactionId));
    }

    @Override
    public SfaTransactionProcessRecordEntity selectProcessRecordById(Long transactionRecordId) {
        return sfaTransactionProcessRecordMapper.selectById(transactionRecordId);
    }

    @Override
    public void updateTransactionRecord(SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity) {
        sfaTransactionProcessRecordMapper.updateById(sfaTransactionProcessRecordEntity);
    }

    @Override
    public SfaTransactionApplyEntity selectTransactionApplyById(Long transactionId) {
        return sfaTransactionApplyMapper.selectById(transactionId);
    }

    @Override
    public void insertJobPositionTask(SfaJobPositionTask sfaJobPositionTask) {
        sfaJobPositionTaskMapper.insert(sfaJobPositionTask);
    }


}
