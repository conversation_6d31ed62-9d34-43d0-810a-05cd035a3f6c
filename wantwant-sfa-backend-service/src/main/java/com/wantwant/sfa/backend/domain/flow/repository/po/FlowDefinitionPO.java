package com.wantwant.sfa.backend.domain.flow.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 流程定义
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@TableName("flow_definition")
@ApiModel(value = "FlowDefinition对象", description = "流程定义")
@Data
public class FlowDefinitionPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "flow_id", type = IdType.AUTO)
    private Long flowId;

    @ApiModelProperty("流程名称")
    private String flowName;

    @ApiModelProperty("版本")
    private String version;

    @ApiModelProperty("状态(1.启用 0.停用）")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否删除(1.是 0.否)")
    private Integer deleteFlag;

    private LocalDateTime createTime;

    @ApiModelProperty("创建人工号")
    private String createUserId;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人工号")
    private String updateUserId;

    @ApiModelProperty("修改人名称")
    private String updateUserName;

    @ApiModelProperty("是否开启待办提醒（1.是）")
    private Integer enableTodoReminder;

    @ApiModelProperty("待办事项名称")
    private String todoReminderName;

    @ApiModelProperty("待办事项跳转路由")
    private String todoReminderRoute;

    @ApiModelProperty("流程定义CODE")
    private String flowCode;

}
