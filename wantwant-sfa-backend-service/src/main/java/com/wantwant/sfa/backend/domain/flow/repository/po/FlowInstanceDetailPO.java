package com.wantwant.sfa.backend.domain.flow.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 流程实力明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@TableName("flow_instance_detail")
@ApiModel(value = "FlowInstanceDetail对象", description = "流程实力明细表")
@Data
public class FlowInstanceDetailPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("明细ID")
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Long detailId;

    @ApiModelProperty("流程实例ID")
    private Long instanceId;

    @ApiModelProperty("当前步骤")
    private Integer processStep;

    @ApiModelProperty("处理人工号")
    private String processUserId;

    @ApiModelProperty("处理人组织code")
    private String organizationId;

    @ApiModelProperty("处理人角色ID")
    private Integer processRoleId;

    @ApiModelProperty("上一步明细ID")
    private Long prevDetailId;

    @ApiModelProperty("下一步明细ID")
    private Long nextDetailId;

    @ApiModelProperty("额外信息json格式")
    private String extraInfo;

    @ApiModelProperty("当前处理结过(0.未处理 1.通过 2.驳回 3.暂存 4.关闭)")
    private Integer processResult;

    @ApiModelProperty("处理时间")
    private LocalDateTime processTime;

    @ApiModelProperty("评论")
    private String comment;

    @ApiModelProperty("是否当前流程(1.是 0.否)")
    private Integer isCurrent;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("是否外部系统审核(1.是）")
    private Integer externalProcess;

    private Integer businessGroup;

    private String processUserName;

}
