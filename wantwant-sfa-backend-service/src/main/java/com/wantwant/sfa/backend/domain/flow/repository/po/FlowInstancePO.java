package com.wantwant.sfa.backend.domain.flow.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 流程实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("flow_instance")
@ApiModel(value = "FlowInstance对象", description = "流程实例表")
public class FlowInstancePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("任务实例ID")
    @TableId(value = "instance_id", type = IdType.AUTO)
    private Long instanceId;

    @ApiModelProperty("当前流程步骤")
    private Integer processStep;

    @ApiModelProperty("当前流程处理结果(0.未处理 1.通过 2.驳回 3.暂存 4.关闭)")
    private Integer result;

    @ApiModelProperty("删除标志(1.是 )")
    private Integer deleteFlag;

    @ApiModelProperty("流程定义ID")
    private Long flowId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人工号")
    private String createUserId;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}
