package com.wantwant.sfa.backend.domain.flow.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 流程规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@TableName("flow_rule")
@ApiModel(value = "FlowRule对象", description = "流程规则")
@Data
public class FlowRulePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id", type = IdType.AUTO)
    private Long ruleId;

    @ApiModelProperty("审核步骤")
    private Integer step;

    @ApiModelProperty("flow_definition主键")
    private Long flowId;

    @ApiModelProperty("审核策略(10.按上级岗位审核  20.按角色审核  30.按工号审核  40.待外部系统处理)")
    private Integer auditStrategy;

    @ApiModelProperty("驳回策略(10.驳回关闭流程)")
    private Integer rejectStrategy;

    @ApiModelProperty("跳过策略(1.不跳过 2.缺岗跳过)")
    private Integer skipStrategy;

    @ApiModelProperty("版本号")
    private String version;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("按上级岗位审核-岗位类型")
    private String organizationType;

    @ApiModelProperty("按角色审核-role_id")
    private Long roleId;

    @ApiModelProperty("按工号审核-工号")
    private String employeeId;

}
