package com.wantwant.sfa.backend.domain.flow.service;

import com.wantwant.sfa.backend.agent.vo.AgentVo;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.*;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:48
 */
public interface IFlowService {

    /**
     * 生成流程
     *
     * @param flowDO
     */
    Long initFlow(FlowDO flowDO);
    /**
     * 审核通过
     *
     * @param flowPassDO
     * @return
     */
    Long pass(FlowPassDO flowPassDO);

    /**
     * 驳回流程
     *
     * @param flowRejectDO
     */
    void reject(FlowRejectDO flowRejectDO);

    /**
     * 获取规则
     *
     * @return
     */
    FlowRuleDO findRule(String flowCode,String organizationType,int step);

    /**
     * 根据instanceId获取下个节点的规则
     *
     * @param instanceId
     * @return
     */
    FlowRuleDO findCurrentNextRule(Long instanceId);

    /**
     * 获取当前审核节点
     *
     * @param instanceId
     * @return
     */
    FlowInstanceDetailPO findCurrentFlow(Long instanceId);

    /**
     * 根据查询条件获取流程实例ID
     *
     * @param processResult
     * @param person
     * @param roleIds
     * @return
     */
    List<FlowCurrentDO> findInstanceId(Integer processResult, String person, List<Integer> roleIds);

    /**
     * 根据流程实例ID获取审核记录
     *
     * @param flowInstanceId
     * @return
     */
    List<FlowDetailDO> findDetailsByInstanceId(Long flowInstanceId);

    /**
     * 获取当前流程规则信息
     *
     * @param flowInstanceId
     * @return
     */
    FlowRuleDO findCurrentRule(Long flowInstanceId);

    /**
     * 获取审核中的信息
     *
     * @param person
     * @param roleIds
     * @return
     */
    List<AgentVo> findAgent(String person, List<Integer> roleIds,int businessGroup);

    /**
     * 准备初始化流程的数据
     *
     * @param prepareInitFlow
     * @return
     */
    FlowDO prepareInitFlow(PrepareInitFlow prepareInitFlow);

    FlowPassDO preparePassFlow(Long instanceId, ProcessUserDO processUserDO);

    /**
     * 关闭流程
     *
     * @param flowRejectDO
     */
    void close(FlowRejectDO flowRejectDO);

    /**
     * 获取最后一步
     *
     * @param flowInstanceId
     * @return
     */
    Integer findLastStep(Long flowInstanceId);

    /**
     * 检查流程中是否包含特定组织
     *
     * @param instanceId
     * @param varea
     * @return
     */
    Integer containsOrganizationType(Long instanceId, String varea);
}
