package com.wantwant.sfa.backend.domain.flow.service;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.JobTransferAuditDO;
import com.wantwant.sfa.backend.domain.flow.enums.InterviewProcessUserTypeEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.JobTransferDO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;

import java.time.LocalDate;

/**
 * @Description: 人员异动用流程
 * @Auther: zhengxu
 * @Date: 2024/08/02/上午9:20
 */
public interface IJobTransferFlowService {

    /**
     * 获取建议执行日期
     *
     * @return
     */
    LocalDate getAdviceExecuteDate(SfaEmployeeInfoModel employeeInfoModel, Integer jobTransferType);

    /**
     * 初始化异动流程
     *
     * @return
     */
    Long initTransferProcess(JobTransferDO jobTransferDO, ProcessUserDO processUserDO);


    /**
     * 获取下级审核人类型
     *
     * @return
     */
    InterviewProcessUserTypeEnum getProcessTypeBySalaryId(Integer employeeInfoId, Integer salaryId,Integer positionId);

    /**
     * 异动流程审核
     *
     * @param jobTransferAudit
     * @param processUserDO
     */
    void audit(JobTransferAuditDO jobTransferAudit, ProcessUserDO processUserDO);

    /**
     * 人资处理完成
     *
     * @param transactionId
     * @param result
     * @param remark
     * @param processUserDO
     */
    void hrProcess(Long transactionId, Integer result, String remark, ProcessUserDO processUserDO);

    /**
     * 执行异动
     *
     * @param transactionId
     */
    void doTransaction(Long transactionId,ProcessUserDO processUserDO);

}
