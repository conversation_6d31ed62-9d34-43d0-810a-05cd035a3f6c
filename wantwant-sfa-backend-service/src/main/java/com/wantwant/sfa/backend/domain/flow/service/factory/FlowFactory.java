package com.wantwant.sfa.backend.domain.flow.service.factory;

import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.enums.ProcessResultEnum;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstancePO;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午2:59
 */
public class FlowFactory {

    public static FlowInstancePO initFlowInstance(Long definitionId,Integer processStep, ProcessResultEnum processResultEnum,String createUserId,String createUserName){
        FlowInstancePO flowInstancePO = new FlowInstancePO();
        flowInstancePO.setProcessStep(processStep);
        flowInstancePO.setResult(processResultEnum.getResult());
        flowInstancePO.setDeleteFlag(0);
        flowInstancePO.setFlowId(definitionId);
        flowInstancePO.setCreateTime(LocalDateTime.now());
        flowInstancePO.setCreateUserId(createUserId);
        flowInstancePO.setCreateUserName(createUserName);
        flowInstancePO.setUpdateTime(LocalDateTime.now());
        return flowInstancePO;
    }


    public static FlowInstanceDetailPO initFlowInstanceDetail(Long instanceId, Integer processStep, FlowProcessUserDO flowProcessUserDO,ProcessResultEnum processResultEnum,Long prevId){
        FlowInstanceDetailPO flowInstanceDetailPO = new FlowInstanceDetailPO();
        flowInstanceDetailPO.setInstanceId(instanceId);
        flowInstanceDetailPO.setProcessStep(processStep);
        flowInstanceDetailPO.setBusinessGroup(flowProcessUserDO.getBusinessGroup());
        flowInstanceDetailPO.setProcessUserId(flowProcessUserDO.getEmployeeId());
        flowInstanceDetailPO.setOrganizationId(flowProcessUserDO.getOrganizationId());
        flowInstanceDetailPO.setProcessRoleId(flowProcessUserDO.getRoleId());
        flowInstanceDetailPO.setProcessResult(processResultEnum.getResult());
        flowInstanceDetailPO.setPrevDetailId(prevId);
        return flowInstanceDetailPO;
    }
}
