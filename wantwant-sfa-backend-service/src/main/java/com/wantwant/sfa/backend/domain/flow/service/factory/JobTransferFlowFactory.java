package com.wantwant.sfa.backend.domain.flow.service.factory;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessRecordEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/下午2:03
 */
public class JobTransferFlowFactory {

    public static SfaTransactionProcessEntity initProcessEntity(Long transactionId,int processStep,int result, ProcessUserDO processUserDO){
        SfaTransactionProcessEntity sfaTransactionProcessEntity = new SfaTransactionProcessEntity();
        sfaTransactionProcessEntity.setTransactionApplyId(transactionId);
        sfaTransactionProcessEntity.setProcessStep(processStep);
        sfaTransactionProcessEntity.setProcessResult(result);
        sfaTransactionProcessEntity.setCreateTime(LocalDateTime.now());
        sfaTransactionProcessEntity.setCreateUserId(processUserDO.getEmployeeId());
        sfaTransactionProcessEntity.setCreateUserName(processUserDO.getEmployeeName());
        sfaTransactionProcessEntity.setUpdateTime(LocalDateTime.now());
        sfaTransactionProcessEntity.setUpdateUserId(processUserDO.getEmployeeId());
        sfaTransactionProcessEntity.setUpdateUserName(processUserDO.getEmployeeName());
        sfaTransactionProcessEntity.setDeleteFlag(0);
        return sfaTransactionProcessEntity;
    }

    public static SfaTransactionProcessRecordEntity initProcessRecordEntity(Long processId,int processStep,int result,
                                                                            String processUserId,String processUserName,Integer salaryId,
                                                                            ProcessUserDO processUserDO){
        SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity = new SfaTransactionProcessRecordEntity();
        sfaTransactionProcessRecordEntity.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        sfaTransactionProcessRecordEntity.setTransactionProcessId(processId);
        sfaTransactionProcessRecordEntity.setProcessType(processStep);
        sfaTransactionProcessRecordEntity.setProcessResult(result);
        sfaTransactionProcessRecordEntity.setProcessUserId(processUserId);
        sfaTransactionProcessRecordEntity.setProcessUserName(processUserName);
        sfaTransactionProcessRecordEntity.setSalaryStructureId(salaryId);
        return sfaTransactionProcessRecordEntity;
    }


    public static SfaTransactionProcessRecordEntity initHrPoint(Long prevId, Long processId, ProcessUserDO processUserDO,int processStep, int result) {
        SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity = new SfaTransactionProcessRecordEntity();
        sfaTransactionProcessRecordEntity.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        sfaTransactionProcessRecordEntity.setTransactionProcessId(processId);
        sfaTransactionProcessRecordEntity.setProcessType(processStep);
        sfaTransactionProcessRecordEntity.setProcessResult(result);
        sfaTransactionProcessRecordEntity.setPrevProcessId(prevId);
        return sfaTransactionProcessRecordEntity;

    }


    public static SfaJobPositionTask initPositionTask(Long transactionId, LocalDate adviceExecuteDate,ProcessUserDO processUserDO){
        SfaJobPositionTask sfaJobPositionTask = new SfaJobPositionTask();
        sfaJobPositionTask.setTransactionId(transactionId);
        sfaJobPositionTask.setExecuteDate(adviceExecuteDate);
        sfaJobPositionTask.setCreateTime(LocalDateTime.now());
        sfaJobPositionTask.setProcessUserId(processUserDO.getEmployeeId());
        sfaJobPositionTask.setProcessUserName(processUserDO.getEmployeeName());
        sfaJobPositionTask.setType(3);
        sfaJobPositionTask.setStatus(0);
        return sfaJobPositionTask;
    }
}
