package com.wantwant.sfa.backend.domain.flow.service.impl;

import com.wantwant.sfa.backend.domain.flow.mapper.FlowInstanceDetailMapper;
import com.wantwant.sfa.backend.domain.flow.service.IFlowEventService;
import com.wantwant.sfa.backend.mapper.afterSales.AfterSalesProcessMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayInfoMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.meeting.MeetingRecordMapper;
import com.wantwant.sfa.backend.mapper.review.SfaReviewReportMapper;
import com.wantwant.sfa.backend.mapper.workReport.DailyReportMapper;
import com.wantwant.sfa.backend.mapper.workReport.DailyReportReviewMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/14/上午10:16
 */
@Service
public class FlowEventService implements IFlowEventService {
    @Resource
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Resource
    private DisplayInfoMapper displayInfoMapper;
    @Resource
    private AfterSalesProcessMapper afterSalesProcessMapper;
    @Resource
    private FlowInstanceDetailMapper flowInstanceDetailMapper;
    @Resource
    private SfaReviewReportMapper sfaReviewReportMapper;
    @Resource
    private DailyReportMapper dailyReportMapper;
    @Resource
    private MeetingRecordMapper meetingRecordMapper;

    @Override
    @Transactional
    public void replaceEmpId(String oldEmpId, String empId) {
        // 替换面试申请表
        sfaInterviewProcessRecordMapper.replaceProcessUserId(oldEmpId,empId);

        // 替换特称审核表
        displayInfoMapper.replaceProcessUserId(oldEmpId,empId);

        // 替换售后审核
        afterSalesProcessMapper.replaceProcessUserId(oldEmpId,empId);

        // 替换流程表
        flowInstanceDetailMapper.replaceProcessUserId(oldEmpId,empId);

        // 替换月报查看审核
        sfaReviewReportMapper.replaceReviewEmpId(oldEmpId,empId);

        // 替换日报查看工号
        dailyReportMapper.replaceReviewEmpId(oldEmpId,empId);

        // 替换会议邀请人
        meetingRecordMapper.replaceRecordEmpId(oldEmpId,empId);
        // 替换会议主会人
        meetingRecordMapper.replaceLeadId(oldEmpId,empId);
    }
}
