package com.wantwant.sfa.backend.domain.flow.service.impl;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.agent.vo.AgentVo;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.domain.flow.DO.*;
import com.wantwant.sfa.backend.domain.flow.enums.AuditStrategyEnum;
import com.wantwant.sfa.backend.domain.flow.enums.ProcessResultEnum;
import com.wantwant.sfa.backend.domain.flow.repository.facade.IFlowRepository;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstanceDetailPO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowInstancePO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowRulePO;
import com.wantwant.sfa.backend.domain.flow.service.IFlowService;
import com.wantwant.sfa.backend.domain.flow.service.factory.FlowFactory;
import com.wantwant.sfa.backend.domain.flow.service.strategy.ProcessUserSelector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午2:01
 */
@Service
@Slf4j
public class FlowService implements IFlowService {

    @Resource
    private IFlowRepository flowRepository;
    @Resource
    private ProcessUserSelector processUserSelector;
    @Resource
    private IEmpService empService;

    @Override
    @Transactional
    public Long initFlow(FlowDO flowDO) {
        log.info("【init flow】DO:{}",flowDO);
        // 检查流程是否存在
        Long flowId = flowRepository.checkFlowExistsByCode(flowDO.getFlowCode());
        if(Objects.isNull(flowId)){
            throw new ApplicationException("流程定义不存在");
        }

        // 获取新创建的流程应该处于哪个节点
        List<FlowRulePO> rulePOList = getFlowRule(flowDO,flowId);
        if(CollectionUtils.isEmpty(rulePOList)){
            throw new ApplicationException("未配置流程规则");
        }

        int size = rulePOList.size();
        if(size == 1){
            FlowRulePO flowRulePO = rulePOList.stream().findFirst().get();
            // 生成流程实例
            Long instanceId = flowRepository.initInstance(FlowFactory.initFlowInstance(flowId, flowRulePO.getStep(), ProcessResultEnum.WAIT, flowDO.getCreateUserId(), flowDO.getCreateUserName()));
            if(Objects.isNull(instanceId)){
                throw new ApplicationException("流程实例创建失败");
            }

            // 生成流程实例明细
            flowRepository.initInstanceDetail(FlowFactory.initFlowInstanceDetail(instanceId, flowRulePO.getStep(),flowDO.getFlowProcessUserDO(),ProcessResultEnum.WAIT,null));

            // 返回流程实例ID
            return instanceId;
        }

        // TODO 单个节点多个流程

        return null;
    }



    @Override
    @Transactional
    public Long pass(FlowPassDO flowPassDO) {
        log.info("【flow pass】DO:{}",flowPassDO);
        // 根据当前操作人信息获取审核记录
        FlowInstanceDetailPO instanceDetail = flowRepository.findInstanceDetail(flowPassDO.getInstanceId());
        if(Objects.isNull(instanceDetail)){
            throw new ApplicationException("获取记录信息失败");
        }

        Integer processResult = instanceDetail.getProcessResult();
        if(processResult == ProcessResultEnum.PASS.getResult() || processResult == ProcessResultEnum.FAIL.getResult() || processResult == ProcessResultEnum.CLOSE.getResult()){
            throw new ApplicationException("流程已处理,请勿重复操作");
        }

        // 获取流程实例
        FlowInstancePO flowInstancePO = flowRepository.findInstanceById(instanceDetail.getInstanceId());
        if(Objects.isNull(flowInstancePO)){
            throw new ApplicationException("流程实例获取失败");
        }
        instanceDetail.setExtraInfo(flowPassDO.getExtraInfo());
        instanceDetail.setComment(flowPassDO.getComment());
        instanceDetail.setProcessUserId(flowPassDO.getCurrentProcessUserDO().getEmployeeId());
        instanceDetail.setProcessResult(ProcessResultEnum.PASS.getResult());
        instanceDetail.setProcessUserName(flowPassDO.getCurrentProcessUserDO().getEmployeeName());
        instanceDetail.setProcessTime(LocalDateTime.now());

        Integer nextProcessStep = flowPassDO.getNextProcessStep();
        if(Objects.isNull(nextProcessStep)){
            // 无下级节点直接结束流程
            flowRepository.finishInstance(flowInstancePO);
            // 当前流程明细设置为完成
            flowRepository.updateInstanceDetail(instanceDetail);
            return null;
        }


        flowInstancePO.setProcessStep(nextProcessStep);
        flowRepository.updateInstance(flowInstancePO);

        // 生成流程实例明细
        Long detailId = flowRepository.initInstanceDetail(FlowFactory.initFlowInstanceDetail(instanceDetail.getInstanceId(), flowPassDO.getNextProcessStep(), flowPassDO.getNextProcessUserDO(), ProcessResultEnum.WAIT,instanceDetail.getDetailId()));

        // 当前节点设置为非当前
        instanceDetail.setIsCurrent(0);
        instanceDetail.setNextDetailId(detailId);
        flowRepository.updateInstanceDetail(instanceDetail);

        return detailId;
    }

    @Override
    public void reject(FlowRejectDO flowRejectDO) {
        log.info("【flow reject】DO:{}",flowRejectDO);
        // 获取流程实例
        FlowInstancePO flowInstancePO = flowRepository.findInstanceById(flowRejectDO.getInstanceId());
        // 根据当前操作人信息获取审核记录
        FlowInstanceDetailPO instanceDetail = flowRepository.findInstanceDetail(flowRejectDO.getInstanceId());
        if(Objects.isNull(instanceDetail)){
            throw new ApplicationException("获取记录信息失败");
        }

        Integer processResult = instanceDetail.getProcessResult();
        if(processResult == ProcessResultEnum.PASS.getResult() || processResult == ProcessResultEnum.FAIL.getResult() || processResult == ProcessResultEnum.CLOSE.getResult()){
            throw new ApplicationException("流程已处理,请勿重复操作");
        }

        List<FlowRulePO> list = flowRepository.findCurrentRule(instanceDetail.getDetailId(),instanceDetail.getProcessStep());
        if(CollectionUtils.isEmpty(list)){
            throw new ApplicationException("流程规则获取失败");
        }

        int size = list.size();
        if(size == 1){
            FlowRulePO flowRulePO = list.stream().findFirst().get();
            Integer rejectStrategy = flowRulePO.getRejectStrategy();
            // 驳回关闭流程
            if(rejectStrategy == 10){
                flowInstancePO.setResult(ProcessResultEnum.FAIL.getResult());
                flowRepository.updateInstance(flowInstancePO);
                instanceDetail.setProcessResult(ProcessResultEnum.FAIL.getResult());
                instanceDetail.setProcessUserId(flowRejectDO.getCurrentProcessUserDO().getEmployeeId());
                instanceDetail.setOrganizationId(flowRejectDO.getCurrentProcessUserDO().getOrganizationId());
                instanceDetail.setComment(flowRejectDO.getComment());
                instanceDetail.setProcessTime(LocalDateTime.now());
                instanceDetail.setExtraInfo(flowRejectDO.getExtraInfo());
                instanceDetail.setProcessUserName(flowRejectDO.getCurrentProcessUserDO().getEmployeeName());
                flowRepository.updateInstanceDetail(instanceDetail);
            }
        }

        // TODO 单个节点多个流程

    }

    @Override
    public FlowRuleDO findRule(String flowCode, String organizationType,int step) {
        log.info("【flow find rule】flowCode:{},organizationType:{}",flowCode,organizationType);
        FlowRuleDO flowRuleDO = flowRepository.findRule(flowCode,organizationType,step);
        return flowRuleDO;
    }

    @Override
    public FlowRuleDO findCurrentNextRule(Long instanceId) {
        return flowRepository.findCurrentNextRule(instanceId);
    }

    @Override
    public FlowInstanceDetailPO findCurrentFlow(Long instanceId) {
        return flowRepository.findCurrentFlow(instanceId);
    }

    @Override
    public List<FlowCurrentDO> findInstanceId(Integer processResult, String person, List<Integer> roleIds) {
        return flowRepository.findInstanceId(processResult,person,roleIds);
    }

    @Override
    public List<FlowDetailDO> findDetailsByInstanceId(Long flowInstanceId) {
        return flowRepository.findDetailsByInstanceId(flowInstanceId);
    }

    @Override
    public FlowRuleDO findCurrentRule(Long flowInstanceId) {
        return flowRepository.findCurrentRuleByInstanceId(flowInstanceId);
    }

    @Override
    public List<AgentVo> findAgent(String person, List<Integer> roleIds,int businessGroup) {
        return flowRepository.findAgent(person,roleIds,businessGroup);
    }

    @Override
    public FlowDO prepareInitFlow(PrepareInitFlow prepareInitFlow) {
        // 获取规则
        FlowRuleDO rule = flowRepository.findRule(prepareInitFlow.getFlowCode(), prepareInitFlow.getAuditOrgType(), 0);

        if(Objects.isNull(rule)){
            throw new ApplicationException("流程规则未配置");
        }
        FlowDO flowDO = new FlowDO();
        flowDO.setFlowCode(prepareInitFlow.getFlowCode());
        flowDO.setAuditStrategyEnum(AuditStrategyEnum.getStrategyEnumById(rule.getAuditStrategy()));
        flowDO.setOrganizationType(prepareInitFlow.getAuditOrgType());
        flowDO.setRoleId(prepareInitFlow.getRoleId());

        FlowSelectUserDO flowSelectUserDO = new FlowSelectUserDO();
        flowSelectUserDO.setOrganizationId(prepareInitFlow.getOrganizationId());
        flowSelectUserDO.setAuditOrganizationType(prepareInitFlow.getAuditOrgType());
        flowSelectUserDO.setBusinessGroup(prepareInitFlow.getBusinessGroup());

        FlowProcessUserDO processUser = processUserSelector.findProcessUser(flowSelectUserDO, rule);
        flowDO.setFlowProcessUserDO(processUser);

        return flowDO;
    }

    @Override
    public FlowPassDO preparePassFlow(Long instanceId, ProcessUserDO processUserDO) {
        FlowPassDO flowPassDO = new FlowPassDO();
        flowPassDO.setInstanceId(instanceId);

        FlowProcessUserDO processUser = new FlowProcessUserDO();
        processUser.setEmployeeId(processUserDO.getEmployeeId());
        processUser.setEmployeeName(processUserDO.getEmployeeName());
        processUser.setOrganizationId(processUserDO.getOrganizationId());
        flowPassDO.setCurrentProcessUserDO(processUser);

        FlowInstanceDetailPO currentFlow = flowRepository.findCurrentFlow(instanceId);
        if(Objects.isNull(currentFlow)){
            throw new ApplicationException("当前流程获取失败");
        }
        flowPassDO.setBusinessGroup(currentFlow.getBusinessGroup());

        FlowSelectUserDO flowSelectUserDO = new FlowSelectUserDO();
        flowSelectUserDO.setOrganizationId(currentFlow.getOrganizationId());
        flowSelectUserDO.setBusinessGroup(processUserDO.getBusinessGroup());

        // 下一处理节点
        FlowRuleDO rule = flowRepository.findCurrentNextRule(instanceId);
        if(Objects.nonNull(rule)){
            FlowProcessUserDO nextProcessUser = processUserSelector.findProcessUser(flowSelectUserDO, rule);
            flowPassDO.setNextProcessUserDO(nextProcessUser);

            // 如果是按岗位审核
           if(rule.getAuditStrategy().equals(AuditStrategyEnum.POSITION_TYPE_STRATEGY.getId())){
               Integer skipStrategy = rule.getSkipStrategy();
               // 缺岗跳过，则寻找下个节点
               if(skipStrategy == 2){
                   // 获取流程编码
                   String flowCode = flowRepository.selectFlowCodeByInstanceId(instanceId);
                   if(StringUtils.isNotBlank(flowCode)){
                        throw new ApplicationException("流程获取失败");
                   }

                   FlowRuleDO newRule = findRule(flowCode, nextProcessUser.getOrganizationId(), rule.getStep());
                   flowPassDO.setNextProcessStep(newRule.getStep());
               }
           }else{
               flowPassDO.setNextProcessStep(rule.getStep());
           }
        }



        return flowPassDO;
    }

    @Override
    public void close(FlowRejectDO flowRejectDO) {
        log.info("【flow reject】DO:{}",flowRejectDO);
        // 获取流程实例
        FlowInstancePO flowInstancePO = flowRepository.findInstanceById(flowRejectDO.getInstanceId());
        // 根据当前操作人信息获取审核记录
        FlowInstanceDetailPO instanceDetail = flowRepository.findInstanceDetail(flowRejectDO.getInstanceId());
        if(Objects.isNull(instanceDetail)){
            throw new ApplicationException("获取记录信息失败");
        }

        Integer processResult = instanceDetail.getProcessResult();
        if(processResult == ProcessResultEnum.PASS.getResult() || processResult == ProcessResultEnum.FAIL.getResult() || processResult == ProcessResultEnum.CLOSE.getResult()){
            throw new ApplicationException("流程已处理,请勿重复操作");
        }

        List<FlowRulePO> list = flowRepository.findCurrentRule(instanceDetail.getDetailId(),instanceDetail.getProcessStep());
        if(CollectionUtils.isEmpty(list)){
            throw new ApplicationException("流程规则获取失败");
        }

        int size = list.size();
        if(size == 1){
            flowInstancePO.setResult(ProcessResultEnum.CLOSE.getResult());
            flowRepository.updateInstance(flowInstancePO);
            instanceDetail.setProcessResult(ProcessResultEnum.CLOSE.getResult());
            instanceDetail.setProcessUserId(flowRejectDO.getCurrentProcessUserDO().getEmployeeId());
            instanceDetail.setOrganizationId(flowRejectDO.getCurrentProcessUserDO().getOrganizationId());
            instanceDetail.setComment(flowRejectDO.getComment());
            instanceDetail.setProcessTime(LocalDateTime.now());
            instanceDetail.setExtraInfo(flowRejectDO.getExtraInfo());
            instanceDetail.setProcessUserName(flowRejectDO.getCurrentProcessUserDO().getEmployeeName());
            flowRepository.updateInstanceDetail(instanceDetail);
        }
    }

    @Override
    public Integer findLastStep(Long flowInstanceId) {
        return flowRepository.findLastStep(flowInstanceId);
    }

    @Override
    public Integer containsOrganizationType(Long instanceId, String varea) {
        return flowRepository.containsOrganizationType(instanceId,varea);
    }


    private List<FlowRulePO> getFlowRule(FlowDO flowDO, Long flowId) {
        AuditStrategyEnum auditStrategyEnum = flowDO.getAuditStrategyEnum();
        switch (auditStrategyEnum){
            case ORG_TYPE_STRATEGY :
            case POSITION_TYPE_STRATEGY:
                // 根据组织类型获取最小的一个步骤
                Integer orgStep = flowRepository.findMinStepByOrganizationType(flowDO.getOrganizationType(),flowId);
                if(Objects.isNull(orgStep)){
                    throw new ApplicationException("流程配置异常");
                }
                return flowRepository.findByOrganizationType(flowDO.getOrganizationType(),flowId,orgStep);
            case ROLE_STRATEGY:
                Integer roleStep = flowRepository.findMinStepByRoleId(flowDO.getRoleId(),flowId);
                if(Objects.isNull(roleStep)){
                    throw new ApplicationException("流程配置异常");
                }
                return flowRepository.findByRoleId(flowDO.getRoleId(),flowId,roleStep);
            case EMP_ID_STRATEGY:
                Integer empIdStep = flowRepository.findMinStepByEmpId(flowDO.getEmployeeId(),flowId);
                if(Objects.isNull(empIdStep)){
                    throw new ApplicationException("流程配置异常");
                }
                return flowRepository.findByEmpId(flowDO.getEmployeeId(),flowId,empIdStep);
            default: throw new ApplicationException("暂不支持的流程");
        }
    }



}
