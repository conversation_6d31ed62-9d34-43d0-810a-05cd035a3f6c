package com.wantwant.sfa.backend.domain.flow.service.impl;

import org.apache.commons.lang3.StringUtils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Grade工具类
 * 用于处理Grade字符串中数字的提取
 * 支持格式如：S01~S16, BD01~BD16等
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class GradeUtils {
    
    private static final Pattern DIGIT_PATTERN = Pattern.compile("\\d+");
    
    /**
     * 提取Grade中的数字部分
     * 取第一次数字最后一次出现的位置，往前截取到非数字位置，并转成Integer
     * 
     * @param grade 原始Grade字符串
     * @return 数字，默认为0
     */
    public static Integer extractGradeNumber(String grade) {
        if (StringUtils.isBlank(grade)) {
            return 0;
        }
        
        // 找到第一个数字的位置
        int firstDigitIndex = -1;
        for (int i = 0; i < grade.length(); i++) {
            if (Character.isDigit(grade.charAt(i))) {
                firstDigitIndex = i;
                break;
            }
        }
        
        if (firstDigitIndex == -1) {
            return 0;
        }
        
        // 找到最后一个数字的位置
        int lastDigitIndex = firstDigitIndex;
        for (int i = firstDigitIndex; i < grade.length(); i++) {
            if (Character.isDigit(grade.charAt(i))) {
                lastDigitIndex = i;
            } else {
                break;
            }
        }
        
        // 提取数字部分
        String numberStr = grade.substring(firstDigitIndex, lastDigitIndex + 1);
        try {
            return Integer.parseInt(numberStr);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 提取Grade中的数字部分并转换为Integer
     * 
     * @param grade 原始Grade字符串
     * @return 数字，默认为0
     */
    public static Integer extractGradeNumberAsInteger(String grade) {
        return extractGradeNumber(grade);
    }
    
    /**
     * 提取Grade中的最后一个数字序列
     * 
     * @param grade 原始Grade字符串
     * @return 提取的数字，如果没有数字则返回0
     */
    public static Integer extractLastGradeNumber(String grade) {
        if (StringUtils.isBlank(grade)) {
            return 0;
        }
        
        Matcher matcher = DIGIT_PATTERN.matcher(grade);
        String lastMatch = null;
        while (matcher.find()) {
            lastMatch = matcher.group();
        }
        
        if (lastMatch != null) {
            try {
                return Integer.parseInt(lastMatch);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        return 0;
    }
}
