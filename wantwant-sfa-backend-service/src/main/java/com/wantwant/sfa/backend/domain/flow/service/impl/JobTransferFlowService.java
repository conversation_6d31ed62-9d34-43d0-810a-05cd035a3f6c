package com.wantwant.sfa.backend.domain.flow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.JobTransferAuditDO;
import com.wantwant.sfa.backend.domain.flow.enums.InterviewProcessUserTypeEnum;
import com.wantwant.sfa.backend.domain.flow.enums.ProcessResultEnum;
import com.wantwant.sfa.backend.domain.flow.enums.TransactionProcessStep;
import com.wantwant.sfa.backend.domain.flow.enums.TransactionResultEnum;
import com.wantwant.sfa.backend.domain.flow.repository.facade.ITransferFlowRepository;
import com.wantwant.sfa.backend.domain.flow.service.IJobTransferFlowService;
import com.wantwant.sfa.backend.domain.flow.service.factory.JobTransferFlowFactory;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.ChangePositionTypeDO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.ChangeSalaryDO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.JobTransferDO;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.ChangeColumnEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferErrEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.repository.facade.IJobTransferRepository;
import com.wantwant.sfa.backend.domain.jobTransfer.service.selector.JobTransferSelector;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryStructureMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionActionMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryStructurePO;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionActionEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessRecordEntity;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import io.lettuce.core.TransactionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferEnum.CHANGE_SALARY;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/02/上午9:51
 */
@Service
@Slf4j
public class JobTransferFlowService implements IJobTransferFlowService {
    @Resource
    private ITransferFlowRepository transferFlowRepository;
    @Resource
    private EmployeeSalaryMapper employeeSalaryMapper;
    @Resource
    private EmployeeSalaryStructureMapper employeeSalaryStructureMapper;
    @Resource
    private IAuditService auditService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private IJobTransferRepository jobTransferRepository;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private JobTransferSelector jobTransferSelector;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public LocalDate getAdviceExecuteDate(SfaEmployeeInfoModel sfaEmployeeInfoModel, Integer jobTransferType) {

        JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(jobTransferType);
        switch (jobTransfer) {
            case CHANGE_SALARY:
                // 系统记录建议生效日期为上月26日（若早于入职日期则以入职日期为准）；
                LocalDate localDate = LocalDate.now().minusMonths(1L);
                LocalDate adviceDate = LocalDate.of(localDate.getYear(), localDate.getMonthValue(), 26);
                LocalDateTime onBoardTime = sfaEmployeeInfoModel.getOnboardTime();
                if (Objects.nonNull(onBoardTime)) {
                    if (onBoardTime.toLocalDate().isAfter(adviceDate)) {
                        return onBoardTime.toLocalDate();
                    }
                }
                return adviceDate;
            case CHANGE_SERVER_OBJ:
                return LocalDate.now();
            default: // 返回次月1号
                return LocalDate.now().plusMonths(1L).with(TemporalAdjusters.firstDayOfMonth());
        }

    }

    @Override
    @Transactional
    public Long initTransferProcess(JobTransferDO jobTransferDO, ProcessUserDO processUserDO) {
        log.info("【init transfer process】jobTransfer:{},processUser:{}", jobTransferDO, processUserDO);
        Integer type = jobTransferDO.getType();

        JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(type);

        String seniorHrEmployeeId = configMapper.getValueByCode("zw_senior_hr_employee_id");
        CeoBusinessOrganizationPositionRelation boss = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, seniorHrEmployeeId).eq(CeoBusinessOrganizationPositionRelation::getChannel, 3).last("limit 1"));

        switch (jobTransfer) {
            case CHANGE_SERVER_OBJ:
                initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PASS.getStatus(), processUserDO.getEmployeeId(), processUserDO.getEmployeeName(), null, processUserDO);
                break;

            case CHANGE_BD_POSITION:
                // 如果当前审核人是区域总监及已上岗位，直接到人资
                String organizationType = RequestUtils.getLoginInfo().getOrganizationType();
                int currentOrder = OrganizationTypeEnum.getOrder(organizationType);
                if(currentOrder <= OrganizationTypeEnum.COMPANY.getOrder()){

                    // 承揽与兼职之间异动，无需人资入职
                    List<SfaTransactionActionEntity> jobsTypeActionList = jobTransferRepository.selectActionByColumn(jobTransferDO.getTransactionId(), ChangeColumnEnum.JOBS_TYPE.getColumn());
                    if(CollectionUtils.isEmpty(jobsTypeActionList)){
                        throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                    }
                    SfaTransactionActionEntity sfaTransactionActionEntity = jobsTypeActionList.stream().findFirst().get();

                    String oldValue = sfaTransactionActionEntity.getOldValue();
                    String transactionValue = sfaTransactionActionEntity.getTransactionValue();
                    if("2".equals(oldValue) && "2".equals(transactionValue)){
                        initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PASS.getStatus(), processUserDO.getEmployeeId(), processUserDO.getEmployeeName(), 0, processUserDO);
                    }else{
                        return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.HR_PROCESS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), StringUtils.EMPTY, StringUtils.EMPTY, null, processUserDO);
                    }

                }else{
                    // 找到当前业务BD的主岗营业所
                    SelectAuditDto bdAuditDto = new SelectAuditDto();
                    bdAuditDto.setChannel(3);
                    // 通过employeeInfoId获取大区组织code
                    SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, jobTransferDO.getEmployeeInfoId()).eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.isNull(sfaPositionRelationEntity)) {
                        throw new ApplicationException("兼岗表信息错误");
                    }
                    String companyCode = sfaPositionRelationEntity.getCompanyCode();
                    if (StringUtils.isBlank(companyCode)) {
                        throw new ApplicationException("组织信息错误");
                    }
                    bdAuditDto.setCurrentOrganizationId(companyCode);
                    bdAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
                    bdAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
                    CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(bdAuditDto);

                    ChangeSalaryDO changeSalaryDO = Optional.ofNullable(jobTransferDO.getChangeSalaryDO()).orElse(new ChangeSalaryDO());

                    initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), auditPerson.getEmployeeId(), auditPerson.getEmployeeName(), changeSalaryDO.getId(), processUserDO);
                }

                break;
            case CHANGE_PART_TIME_POSITION:
                initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PASS.getStatus(), processUserDO.getEmployeeId(), processUserDO.getEmployeeName(), null, processUserDO);
                break;
            case CHANGE_SALARY:
                Integer positionType = transferFlowRepository.selectPositionType(jobTransferDO.getEmployeeInfoId());
                if (Objects.isNull(positionType)) {
                    throw new ApplicationException("岗位类型获取失败");
                }

                InterviewProcessUserTypeEnum processTypeBySalaryId = this.getProcessTypeBySalaryId(jobTransferDO.getEmployeeInfoId(), jobTransferDO.getChangeSalaryDO().getId(), positionType);
                if (processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.SKIP.getType()) {
                    return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PASS.getStatus(), processUserDO.getEmployeeId(), processUserDO.getEmployeeName(), jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                } else if (processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.SUPERIOR.getType()) {
                    // 只有区域经理是上级主管审核
                    SelectAuditDto selectAuditDto = new SelectAuditDto();
                    selectAuditDto.setChannel(3);
                    // 通过employeeInfoId获取大区组织code
                    SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, jobTransferDO.getEmployeeInfoId()).eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.isNull(sfaPositionRelationEntity)) {
                        throw new ApplicationException("兼岗表信息错误");
                    }
                    String vareaCode = sfaPositionRelationEntity.getVareaCode();
                    if (StringUtils.isBlank(vareaCode)) {
                        throw new ApplicationException("组织信息错误");
                    }
                    selectAuditDto.setCurrentOrganizationId(vareaCode);
                    selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
                    selectAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
                    CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);
                    initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), auditPerson.getEmployeeId(), auditPerson.getEmployeeName(), jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                } else if (processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.BOSS.getType()) {

                    // 老板审核
                    return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.BOSS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), boss.getEmployeeId(), boss.getEmployeeName(), jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                }
                break;
            case CHANGE_BUSINESS_GROUP:

                ChangePositionTypeDO changePositionTypeDO = jobTransferDO.getChangePositionTypeDO();
                if(Objects.isNull(changePositionTypeDO)){
                    throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                Integer positionTypeId = changePositionTypeDO.getPositionTypeId();

                if(positionTypeId == 12 || positionTypeId == 1){

                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, configMapper.getValueByCode("zw_senior_hr_employee_id")).eq(CeoBusinessOrganizationPositionRelation::getChannel,3).last("limit 1"));
                    return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.BOSS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), ceoBusinessOrganizationPositionRelation.getEmployeeId(), ceoBusinessOrganizationPositionRelation.getEmployeeName(), null, processUserDO);
                }else{
                    // 新的直属主管审核
                    SelectAuditDto selectAuditDto = new SelectAuditDto();
                    selectAuditDto.setChannel(3);
                    selectAuditDto.setBusinessGroup(jobTransferDO.getBusinessGroup());
                    selectAuditDto.setCurrentOrganizationId(organizationMapper.getOrganizationParentId(jobTransferDO.getChangeMainOrg().getOrganizationId()));
                    selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
                    CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);

                    return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), auditPerson.getEmployeeId(), auditPerson.getEmployeeName(), null, processUserDO);

                }

            case CHANGE_MAIN_POSITION:
                InterviewProcessUserTypeEnum processUserTypeEnum = this.getProcessTypeBySalaryId(jobTransferDO.getEmployeeInfoId(), jobTransferDO.getChangeSalaryDO().getId(), jobTransferDO.getChangePositionTypeDO().getPositionTypeId());
                // 薪资符合要求到人资处理
                if (processUserTypeEnum.getType() == InterviewProcessUserTypeEnum.SKIP.getType()) {
                    return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.HR_PROCESS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), StringUtils.EMPTY, StringUtils.EMPTY, jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                } else if (processUserTypeEnum.getType() == InterviewProcessUserTypeEnum.BOSS.getType()) {
                    return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.BOSS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), boss.getEmployeeId(), boss.getEmployeeName(), jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                } else if (processUserTypeEnum.getType() == InterviewProcessUserTypeEnum.SUPERIOR.getType()) {
                    // 直属主管审核
                    SelectAuditDto auditDto = new SelectAuditDto();
                    auditDto.setChannel(3);
                    auditDto.setBusinessGroup(jobTransferDO.getBusinessGroup());
                    auditDto.setCurrentOrganizationId(organizationMapper.getOrganizationParentId(jobTransferDO.getChangeMainOrg().getOrganizationId()));
                    auditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
                    CeoBusinessOrganizationPositionRelation auditPerson2 = auditService.chooseAuditPerson(auditDto);
                    if (auditPerson2.getEmployeeId().equals(processUserDO.getEmployeeId())) {
                        return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.HR_PROCESS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), StringUtils.EMPTY, StringUtils.EMPTY, jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                    } else {
                        return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.SUPERIOR.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), auditPerson2.getEmployeeId(), auditPerson2.getEmployeeName(), jobTransferDO.getChangeSalaryDO().getId(), processUserDO);
                    }
                }

                break;
            case CHANGE_COMPANY_WANT_WANT:
            case CHANGE_COMPANY:
                return initFinishProcess(jobTransferDO.getTransactionId(), TransactionProcessStep.HR_PROCESS.getProcessStep(), TransactionResultEnum.PROCESSING.getStatus(), StringUtils.EMPTY, StringUtils.EMPTY, null, processUserDO);
            case UNKNOWN:
                break;
        }

        return null;
    }



    private Long initFinishProcess(Long transactionId, int processStep, int processResult, String processUserId, String processUserName, Integer salaryId, ProcessUserDO processUserDO) {
        SfaTransactionProcessEntity sfaTransactionProcessEntity = JobTransferFlowFactory.initProcessEntity(transactionId, processStep, processResult, processUserDO);
        transferFlowRepository.saveTransactionProcess(sfaTransactionProcessEntity);
        SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity = JobTransferFlowFactory.initProcessRecordEntity(sfaTransactionProcessEntity.getId(), sfaTransactionProcessEntity.getProcessStep(), sfaTransactionProcessEntity.getProcessResult(), processUserId, processUserName, salaryId, processUserDO);
        transferFlowRepository.saveTransactionProcessRecord(sfaTransactionProcessRecordEntity);
        sfaTransactionProcessEntity.setTransactionRecordId(sfaTransactionProcessRecordEntity.getId());
        transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);
        return sfaTransactionProcessEntity.getId();
    }

    @Override
    public InterviewProcessUserTypeEnum getProcessTypeBySalaryId(Integer employeeInfoId, Integer salaryId, Integer transferPositionId) {
        // 获取原薪资方案
        EmployeeSalaryPO employeeSalaryPO = Optional.ofNullable(employeeSalaryMapper.selectSalaryByEmpId(employeeInfoId)).orElse(new EmployeeSalaryPO());

        // 获取新薪资方案
        EmployeeSalaryStructurePO employeeSalaryStructurePO = Optional.ofNullable(employeeSalaryStructureMapper.selectById(salaryId)).orElse(new EmployeeSalaryStructurePO());
        Integer grade = GradeUtils.extractGradeNumber(Optional.ofNullable(employeeSalaryStructurePO.getGrade()).orElse("S00"));
        Integer currentGrade = GradeUtils.extractGradeNumber(Optional.ofNullable(employeeSalaryPO.getSalaryLevel()).orElse("S00"));

        // 获取当前人员岗位类型

        log.info("【get process type by salary】oldSalaryGrade:{},transferSalaryGrade:{},positionTypeId:{}", currentGrade, grade, transferPositionId);

        // 当前薪资等级大于调整后薪资，不需处理
        if (grade <= currentGrade) {
            return InterviewProcessUserTypeEnum.SKIP;
        }

        // 战区大区的调整都到老板
        if (transferPositionId == 1 || transferPositionId == 12) {
            return InterviewProcessUserTypeEnum.BOSS;
        }


        // 省区/区域总监超过S07,事业部主管审核
        if (transferPositionId == 11 || transferPositionId == 2) {
            if (grade > 7) {
                return InterviewProcessUserTypeEnum.BOSS;
            }
        }

        // 区域经理超过S11,上级审核
        if (transferPositionId == 10) {
            if (grade > 11) {
                return InterviewProcessUserTypeEnum.SUPERIOR;
            }
        }

        // 无满足条件则不需要到人资，直接通过
        return InterviewProcessUserTypeEnum.SKIP;
    }

    @Override
    @Transactional
    public void audit(JobTransferAuditDO jobTransferAudit, ProcessUserDO processUserDO) {
        SfaTransactionApplyEntity sfaTransactionApplyEntity = transferFlowRepository.selectTransactionApplyById(jobTransferAudit.getTransactionId());

        // 获取异动流程主表
        SfaTransactionProcessEntity sfaTransactionProcessEntity = transferFlowRepository.selectProcessByApplyId(jobTransferAudit.getTransactionId());
        if (Objects.isNull(sfaTransactionProcessEntity)) {
            throw new ApplicationException("异动流程获取失败");
        }
        // 获取异动流程子表
        SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity = transferFlowRepository.selectProcessRecordById(sfaTransactionProcessEntity.getTransactionRecordId());
        if (Objects.isNull(sfaTransactionProcessRecordEntity)) {
            throw new ApplicationException("异动明细获取失败");
        }

        Integer processResult = sfaTransactionProcessRecordEntity.getProcessResult();
        if (processResult != 0) {
            throw new ApplicationException("异动明细获取失败");
        }

        Integer result = jobTransferAudit.getResult();


        sfaTransactionProcessRecordEntity.setProcessUserName(processUserDO.getEmployeeName());
        sfaTransactionProcessRecordEntity.setProcessUserId(processUserDO.getEmployeeId());
        sfaTransactionProcessRecordEntity.setComment(jobTransferAudit.getRemark());
        sfaTransactionProcessRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTransactionProcessRecordEntity.setProcessResult(result);

        // 驳回操作
        if (result == 2) {
            transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
            sfaTransactionProcessEntity.setProcessResult(result);
            transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);
            return;
        }


        Integer transactionType = sfaTransactionApplyEntity.getTransactionType();
        JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(transactionType);
        log.info("【job transfer audit】transactionType:{},jobTransfer:{}", transactionType, jobTransfer.getName());


        switch (jobTransfer) {
            // 仅兼岗，或仅薪资调整无需到人资
            case CHANGE_PART_TIME_POSITION:
            case CHANGE_SALARY:
                // 结束流程
                transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
                sfaTransactionProcessEntity.setProcessResult(result);
                transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);

                // 执行异动
                this.doTransaction(jobTransferAudit.getTransactionId(),processUserDO);



                break;
            case CHANGE_BUSINESS_GROUP:
                ChangeSalaryDO changeSalaryDO = jobTransferAudit.getChangeSalaryDO();
                SfaTransactionProcessRecordEntity nextRecord = null;
                String seniorHrEmployeeId = configMapper.getValueByCode("zw_senior_hr_employee_id");
                // 获取异动后岗位类型
                List<SfaTransactionActionEntity> sfaTransactionActionEntities = jobTransferRepository.selectActionByColumn(jobTransferAudit.getTransactionId(), ChangeColumnEnum.POSITION_TYPE.getColumn());
                if (CollectionUtils.isEmpty(sfaTransactionActionEntities)) {
                    throw new ApplicationException("异动数据异常");
                }
                SfaTransactionActionEntity sfaTransactionActionEntity = sfaTransactionActionEntities.stream().findFirst().get();
                String positionTypeId = sfaTransactionActionEntity.getTransactionValue();
                InterviewProcessUserTypeEnum processTypeBySalaryId = this.getProcessTypeBySalaryId(sfaTransactionApplyEntity.getEmployeeInfoId(), changeSalaryDO.getId(), Integer.parseInt(positionTypeId));
                if(processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.BOSS.getType() && seniorHrEmployeeId.equals(processUserDO.getEmployeeId())){
                    processTypeBySalaryId = InterviewProcessUserTypeEnum.SKIP;
                }


                // 不需要审核
                if (processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.SKIP.getType()) {
                    // 创建人资审核节点
                    nextRecord = JobTransferFlowFactory.initHrPoint(sfaTransactionProcessRecordEntity.getId(), sfaTransactionProcessEntity.getId(), processUserDO, 1, ProcessResultEnum.WAIT.getResult());
                    transferFlowRepository.saveTransactionProcessRecord(nextRecord);
                    sfaTransactionProcessRecordEntity.setNextProcessId(nextRecord.getId());
                    transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);

                } else if (processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.BOSS.getType()) {
                    nextRecord = JobTransferFlowFactory.initHrPoint(sfaTransactionProcessRecordEntity.getId(), sfaTransactionProcessEntity.getId(), processUserDO, 40, ProcessResultEnum.WAIT.getResult());
                    sfaTransactionProcessRecordEntity.setNextProcessId(nextRecord.getId());
                    transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, seniorHrEmployeeId).eq(CeoBusinessOrganizationPositionRelation::getChannel, 3).last("limit 1"));
                    nextRecord.setProcessUserId(ceoBusinessOrganizationPositionRelation.getEmployeeId());
                    nextRecord.setProcessUserName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
                    transferFlowRepository.saveTransactionProcessRecord(nextRecord);
                } else if (processTypeBySalaryId.getType() == InterviewProcessUserTypeEnum.SUPERIOR.getType()) {
                    // 如果当前操作人是大区或省区则直接跳过
                    Integer curPositionType = RequestUtils.getLoginInfo().getPositionTypeId();
                    if (curPositionType == 12 || curPositionType == 1) {
                        // 创建人资审核节点
                        nextRecord = JobTransferFlowFactory.initHrPoint(sfaTransactionProcessRecordEntity.getId(), sfaTransactionProcessEntity.getId(), processUserDO, 1, ProcessResultEnum.WAIT.getResult());
                        transferFlowRepository.saveTransactionProcessRecord(nextRecord);
                        sfaTransactionProcessRecordEntity.setNextProcessId(nextRecord.getId());
                        transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
                    } else {
                        List<SfaTransactionActionEntity> mainOrgCodes = jobTransferRepository.selectActionByColumn(jobTransferAudit.getTransactionId(), ChangeColumnEnum.MAIN_ORG_CODE.getColumn());
                        if (CollectionUtils.isEmpty(mainOrgCodes)) {
                            throw new ApplicationException("异动数据异常");
                        }
                        String orgCode = mainOrgCodes.stream().filter(f -> StringUtils.isNotBlank(f.getTransactionValue())).findFirst().get().getTransactionValue();
                        CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, orgCode).last("limit 1"));
                        // 获取直属上级
                        SelectAuditDto selectAuditDto = new SelectAuditDto();
                        selectAuditDto.setChannel(3);
                        // 通过employeeInfoId获取大区组织code
                        selectAuditDto.setCurrentOrganizationId(viewEntity.getVirtualAreaId());
                        selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
                        selectAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
                        CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);

                        nextRecord = JobTransferFlowFactory.initHrPoint(sfaTransactionProcessRecordEntity.getId(), sfaTransactionProcessEntity.getId(), processUserDO, 21, ProcessResultEnum.WAIT.getResult());
                        nextRecord.setProcessUserId(auditPerson.getEmployeeId());
                        nextRecord.setProcessUserName(auditPerson.getEmployeeName());
                        transferFlowRepository.saveTransactionProcessRecord(nextRecord);
                        transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
                    }
                }
                // 绑定流程
                sfaTransactionProcessEntity.setProcessStep(nextRecord.getProcessType());
                sfaTransactionProcessEntity.setProcessResult(nextRecord.getProcessResult());
                sfaTransactionProcessEntity.setTransactionRecordId(nextRecord.getId());
                transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);
                break;

            default: // 检查当前审核步骤
                Integer processStep = sfaTransactionProcessEntity.getProcessStep();
                if (processStep != 1) {
                    // 如果当前步骤不是人资，则创建人资审核节点
                    SfaTransactionProcessRecordEntity nextProcessRecord = JobTransferFlowFactory.initHrPoint(sfaTransactionProcessRecordEntity.getId(), sfaTransactionProcessEntity.getId(), processUserDO, 1, ProcessResultEnum.WAIT.getResult());
                    transferFlowRepository.saveTransactionProcessRecord(nextProcessRecord);

                    sfaTransactionProcessRecordEntity.setNextProcessId(nextProcessRecord.getId());
                    transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
                    // 绑定流程
                    sfaTransactionProcessEntity.setProcessStep(nextProcessRecord.getProcessType());
                    sfaTransactionProcessEntity.setProcessResult(nextProcessRecord.getProcessResult());
                    sfaTransactionProcessEntity.setTransactionRecordId(nextProcessRecord.getId());
                    transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);
                } else {
                    // 结束流程
                    transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);
                    sfaTransactionProcessEntity.setProcessResult(result);
                    transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);
                }
                break;

        }

    }

    @Override
    @Transactional
    public void hrProcess(Long transactionId, Integer result, String remark, ProcessUserDO processUserDO) {
        SfaTransactionApplyEntity sfaTransactionApplyEntity = transferFlowRepository.selectTransactionApplyById(transactionId);

        // 获取异动流程主表
        SfaTransactionProcessEntity sfaTransactionProcessEntity = transferFlowRepository.selectProcessByApplyId(transactionId);
        if (Objects.isNull(sfaTransactionProcessEntity)) {
            throw new ApplicationException("异动流程获取失败");
        }
        // 获取异动流程子表
        SfaTransactionProcessRecordEntity sfaTransactionProcessRecordEntity = transferFlowRepository.selectProcessRecordById(sfaTransactionProcessEntity.getTransactionRecordId());
        if (Objects.isNull(sfaTransactionProcessRecordEntity)) {
            throw new ApplicationException("异动明细获取失败");
        }

        Integer processResult = sfaTransactionProcessRecordEntity.getProcessResult();
        if (processResult != 0) {
            throw new ApplicationException("异动明细获取失败");
        }


        sfaTransactionProcessRecordEntity.setProcessUserName(processUserDO.getEmployeeName());
        sfaTransactionProcessRecordEntity.setProcessUserId(processUserDO.getEmployeeId());
        sfaTransactionProcessRecordEntity.setComment(remark);
        sfaTransactionProcessRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTransactionProcessRecordEntity.setProcessResult(result);
        transferFlowRepository.updateTransactionRecord(sfaTransactionProcessRecordEntity);

        sfaTransactionProcessEntity.setProcessResult(result);
        transferFlowRepository.updateTransactionProcess(sfaTransactionProcessEntity);

    }

    @Override
    @Transactional
    public void doTransaction(Long transactionId,ProcessUserDO processUserDO) {
        SfaTransactionApplyEntity sfaTransactionApplyEntity = transferFlowRepository.selectTransactionApplyById(transactionId);

        // 获取异动流程主表
        SfaTransactionProcessEntity sfaTransactionProcessEntity = transferFlowRepository.selectProcessByApplyId(transactionId);
        if (Objects.isNull(sfaTransactionProcessEntity)) {
            throw new ApplicationException("异动流程获取失败");
        }

        Integer processResult = sfaTransactionProcessEntity.getProcessResult();
        log.info("【do transaction】 result :{}",processResult);
        if(processResult != 1){
            return;
        }

        LocalDate adviceExecuteDate = sfaTransactionApplyEntity.getAdviceExecuteDate();
        LocalDate currentDate = LocalDate.now();
        log.info("【do transaction】 adviceExecuteDate :{}",adviceExecuteDate);

        if(adviceExecuteDate.isAfter(currentDate)){
            // 插入定时任务表
            SfaJobPositionTask sfaJobPositionTask = JobTransferFlowFactory.initPositionTask(sfaTransactionApplyEntity.getId(), sfaTransactionApplyEntity.getAdviceExecuteDate(), processUserDO);
            transferFlowRepository.insertJobPositionTask(sfaJobPositionTask);

        }else{
            JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(sfaTransactionApplyEntity.getTransactionType());
            jobTransferSelector.process(jobTransfer,sfaTransactionApplyEntity,processUserDO);
            jobTransferRepository.setActionExecuteTime(transactionId);
        }
    }
}
