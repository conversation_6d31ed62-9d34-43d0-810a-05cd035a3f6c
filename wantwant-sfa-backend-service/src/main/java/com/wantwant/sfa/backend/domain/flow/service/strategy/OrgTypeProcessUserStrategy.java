package com.wantwant.sfa.backend.domain.flow.service.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.domain.emp.DO.EmpDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowRuleDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowSelectUserDO;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowRulePO;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.BiFunction;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午3:45
 */
@Component
public class OrgTypeProcessUserStrategy implements BiFunction<FlowSelectUserDO, FlowRuleDO, FlowProcessUserDO> {

    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;

    @Override
    public FlowProcessUserDO apply(FlowSelectUserDO flowSelectUserDO, FlowRuleDO flowRuleDO) {

        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();


        CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                .eq(CeoBusinessOrganizationViewEntity::getOrganizationId, flowSelectUserDO.getOrganizationId()));

        String auditOrganizationType = flowSelectUserDO.getAuditOrganizationType();
        if("department".equals(auditOrganizationType)){
            flowProcessUserDO.setOrganizationId(viewEntity.getDepartmentId());
        }else if("company".equals(auditOrganizationType)){
            flowProcessUserDO.setOrganizationId(viewEntity.getOrgId2());
        }else if("province".equals(auditOrganizationType)){
            flowProcessUserDO.setOrganizationId(viewEntity.getProvinceId());
        }else if("varea".equals(auditOrganizationType)){
            flowProcessUserDO.setOrganizationId(viewEntity.getVirtualAreaId());
        }else if("area".equals(auditOrganizationType)){
            flowProcessUserDO.setOrganizationId(viewEntity.getOrgId3());
        }

        return flowProcessUserDO;
    }
}
