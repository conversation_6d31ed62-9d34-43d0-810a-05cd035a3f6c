package com.wantwant.sfa.backend.domain.flow.service.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowRuleDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowSelectUserDO;


import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.BiFunction;

@Component
public class PositionStrategy implements BiFunction<FlowSelectUserDO, FlowRuleDO, FlowProcessUserDO> {

    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private IAuditService auditService;
    @Resource
    private ConfigMapper configMapper;

    @Override
    public FlowProcessUserDO apply(FlowSelectUserDO flowSelectUserDO, FlowRuleDO flowRuleDO) {
        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();

        String organizationType = flowRuleDO.getOrganizationType();
        if(StringUtils.isBlank(organizationType)){
            throw new ApplicationException("组织类型异常");
        }
        // 获取当前组织id
        CeoBusinessOrganizationViewEntity ceoBusinessOrganizationViewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                .eq(CeoBusinessOrganizationViewEntity::getOrganizationId, flowSelectUserDO.getOrganizationId()));

        String currentOrgCode = StringUtils.EMPTY;
        if("zb".equals(organizationType)) {
            currentOrgCode = organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup());
        }else if("area".equals(organizationType)){
            currentOrgCode = ceoBusinessOrganizationViewEntity.getOrgId3();
        }else if("varea".equals(organizationType)){
            currentOrgCode = ceoBusinessOrganizationViewEntity.getVirtualAreaId();
        }else if("province".equals(organizationType)){
            currentOrgCode = ceoBusinessOrganizationViewEntity.getProvinceId();
        }else if("company".equals(organizationType)){
            currentOrgCode = ceoBusinessOrganizationViewEntity.getOrgId2();
        }else if("department".equals(organizationType)){
            currentOrgCode = ceoBusinessOrganizationViewEntity.getDepartmentId();
        }

        CeoBusinessOrganizationPositionRelation auditPerson = getAuditPersonOrgType(currentOrgCode);
        flowProcessUserDO.setOrganizationId(auditPerson.getOrganizationId());
        return flowProcessUserDO;
    }


    private CeoBusinessOrganizationPositionRelation getAuditPersonOrgType(String processOrganizationId) {

        String organizationType = organizationMapper.getOrganizationType(processOrganizationId);
        String parentOrgCode = StringUtils.EMPTY;
        if(organizationType.equals("zb")){

            return ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId,configMapper.getValueByCode("zw_hr_employee_id"))
                    .eq(CeoBusinessOrganizationPositionRelation::getChannel,3).eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup,RequestUtils.getBusinessGroup()).last("limit 1"));
        }else{
            parentOrgCode = organizationMapper.getOrganizationParentId(processOrganizationId);
        }


        SelectAuditDto selectAuditDto = new SelectAuditDto();
        selectAuditDto.setCurrentOrganizationId(parentOrgCode);
        selectAuditDto.setChannel(3);
        selectAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
        selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = auditService.chooseAuditPerson(selectAuditDto);
        if(ceoBusinessOrganizationPositionRelation.getEmployeeName().equals("代理督导")){
            ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                    new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId,configMapper.getValueByCode("zw_hr_employee_id"))
                            .eq(CeoBusinessOrganizationPositionRelation::getChannel,3).eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup,RequestUtils.getBusinessGroup()).last("limit 1")
            );
        }

        return ceoBusinessOrganizationPositionRelation;
    }
}
