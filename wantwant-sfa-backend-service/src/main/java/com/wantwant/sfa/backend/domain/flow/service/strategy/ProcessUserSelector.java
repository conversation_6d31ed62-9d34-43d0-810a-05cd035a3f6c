package com.wantwant.sfa.backend.domain.flow.service.strategy;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.domain.emp.DO.EmpDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowRuleDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowSelectUserDO;
import com.wantwant.sfa.backend.domain.flow.enums.AuditStrategyEnum;
import com.wantwant.sfa.backend.domain.flow.repository.po.FlowRulePO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;
import java.util.function.BiFunction;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/17/下午3:44
 */
@Component
public class ProcessUserSelector {

    @Resource
    private OrgTypeProcessUserStrategy orgTypeProcessUserStrategy;
    @Resource
    private RoleProcessUserStrategy roleProcessUserStrategy;
    @Resource
    private PositionStrategy positionStrategy;


    private final HashMap<AuditStrategyEnum, BiFunction<FlowSelectUserDO, FlowRuleDO, FlowProcessUserDO>> dispatchMapper = new HashMap<>();

    @PostConstruct
    public void init(){
        dispatchMapper.put(AuditStrategyEnum.ORG_TYPE_STRATEGY,orgTypeProcessUserStrategy);
        dispatchMapper.put(AuditStrategyEnum.POSITION_TYPE_STRATEGY,positionStrategy);
        dispatchMapper.put(AuditStrategyEnum.ROLE_STRATEGY,roleProcessUserStrategy);
    }


    public FlowProcessUserDO findProcessUser(FlowSelectUserDO flowSelectUserDO, FlowRuleDO flowRule){
        AuditStrategyEnum strategyEnumById = AuditStrategyEnum.getStrategyEnumById(flowRule.getAuditStrategy());
        BiFunction<FlowSelectUserDO, FlowRuleDO, FlowProcessUserDO> empDOFlowRulePOFlowProcessUserDOBiFunction = dispatchMapper.get(strategyEnumById);
        if(Objects.isNull(empDOFlowRulePOFlowProcessUserDOBiFunction)){
            throw new ApplicationException("流程配置错误，不支持的审核人选择器");
        }

        return empDOFlowRulePOFlowProcessUserDOBiFunction.apply(flowSelectUserDO,flowRule);
    }
}
