package com.wantwant.sfa.backend.domain.flow.service.strategy;

import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowRuleDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowSelectUserDO;
import org.springframework.stereotype.Component;

import java.util.function.BiFunction;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/20/下午9:48
 */
@Component
public class RoleProcessUserStrategy implements BiFunction<FlowSelectUserDO, FlowRuleDO, FlowProcessUserDO> {
    @Override
    public FlowProcessUserDO apply(FlowSelectUserDO flowSelectUserDO, FlowRuleDO flowRuleDO) {
        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();

        flowProcessUserDO.setRoleId(flowRuleDO.getRoleId());
        flowProcessUserDO.setBusinessGroup(flowSelectUserDO.getBusinessGroup());
        return flowProcessUserDO;
    }
}
