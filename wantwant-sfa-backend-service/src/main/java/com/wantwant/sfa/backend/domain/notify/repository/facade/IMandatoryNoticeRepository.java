package com.wantwant.sfa.backend.domain.notify.repository.facade;

import com.wantwant.sfa.backend.domain.notify.repository.po.MandatoryNoticePO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/下午3:39
 */
public interface IMandatoryNoticeRepository {

    void save(MandatoryNoticePO e);

    List<MandatoryNoticePO> findNoticeByEmpId(String empId);


    void read(Long id, String empId);
}
