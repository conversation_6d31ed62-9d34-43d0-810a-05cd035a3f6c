package com.wantwant.sfa.backend.domain.notify.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.domain.notify.mapper.MandatoryNoticeMapper;
import com.wantwant.sfa.backend.domain.notify.repository.facade.IMandatoryNoticeRepository;
import com.wantwant.sfa.backend.domain.notify.repository.po.MandatoryNoticePO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/下午3:40
 */
@Repository
public class MandatoryNoticeRepository implements IMandatoryNoticeRepository {
    @Resource
    private MandatoryNoticeMapper mandatoryNoticeMapper;

    @Override
    public void save(MandatoryNoticePO e) {
        mandatoryNoticeMapper.insert(e);
    }

    @Override
    public List<MandatoryNoticePO> findNoticeByEmpId(String empId) {
        return mandatoryNoticeMapper.selectList(new LambdaQueryWrapper<MandatoryNoticePO>().eq(MandatoryNoticePO::getEmpId,empId).eq(MandatoryNoticePO::getIsRead,0).eq(MandatoryNoticePO::getDeleteFlag,0));
    }

    @Override
    public void read(Long id, String empId) {
        MandatoryNoticePO readOperation = new MandatoryNoticePO();
        readOperation.setIsRead(1);
        readOperation.setReadTime(LocalDateTime.now());
        mandatoryNoticeMapper.update(readOperation,new LambdaQueryWrapper<MandatoryNoticePO>().eq(MandatoryNoticePO::getId,id));
    }
}
