package com.wantwant.sfa.backend.domain.notify.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 强制提醒
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@TableName("sfa_mandatory_notice")
@ApiModel(value = "SfaMandatoryNotice对象", description = "强制提醒")
@Data
public class MandatoryNoticePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("强制提醒类型(1.任务强制提醒)")
    private Integer type;

    @ApiModelProperty("强制提醒工号")
    private String empId;

    @ApiModelProperty("外部ID")
    private Long externalId;

    @ApiModelProperty("是否已读(0.未读 1.已读)")
    private Integer isRead;

    @ApiModelProperty("阅读时间")
    private LocalDateTime readTime;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;



}
