package com.wantwant.sfa.backend.domain.notify.service;

import com.wantwant.sfa.backend.domain.notify.DO.MandatoryNoticeDO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/下午3:34
 */
public interface IMandatoryNoticeService {
    /**
     * 创建强制提醒信息
     *
     * @param list
     */
    void createMandatoryNotice(List<MandatoryNoticeDO> list);

    /**
     * 根据工号获取强制提信息信息
     *
     * @param empId
     * @return
     */
    List<MandatoryNoticeDO> findNoticeByEmpId(String empId);

    /**
     * 读取
     *
     * @param id
     * @param empId
     */
    void read(Long id,String empId);
}
