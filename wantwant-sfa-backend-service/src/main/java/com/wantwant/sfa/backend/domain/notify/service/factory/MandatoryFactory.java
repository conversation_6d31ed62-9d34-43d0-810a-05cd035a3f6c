package com.wantwant.sfa.backend.domain.notify.service.factory;

import com.wantwant.sfa.backend.domain.notify.DO.MandatoryNoticeDO;
import com.wantwant.sfa.backend.domain.notify.repository.po.MandatoryNoticePO;
import com.wantwant.sfa.backend.util.BeanUtils;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/下午3:53
 */
public class MandatoryFactory {
    public static MandatoryNoticePO initMandatoryNotice(MandatoryNoticeDO mandatoryNoticeDO) {
        MandatoryNoticePO mandatoryNoticePO = new MandatoryNoticePO();
        BeanUtils.copyProperties(mandatoryNoticeDO,mandatoryNoticePO);
        return mandatoryNoticePO;
    }

    public static MandatoryNoticeDO convertNotice(MandatoryNoticePO mandatoryNoticePO) {
        MandatoryNoticeDO mandatoryNoticeDO = new MandatoryNoticeDO();
        BeanUtils.copyProperties(mandatoryNoticePO,mandatoryNoticeDO);
        return mandatoryNoticeDO;
    }
}
