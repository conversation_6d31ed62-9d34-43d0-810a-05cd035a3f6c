package com.wantwant.sfa.backend.domain.notify.service.impl;

import com.wantwant.sfa.backend.domain.notify.DO.MandatoryNoticeDO;
import com.wantwant.sfa.backend.domain.notify.repository.facade.IMandatoryNoticeRepository;
import com.wantwant.sfa.backend.domain.notify.repository.po.MandatoryNoticePO;
import com.wantwant.sfa.backend.domain.notify.service.IMandatoryNoticeService;
import com.wantwant.sfa.backend.domain.notify.service.factory.MandatoryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/下午3:34
 */
@Service
@Slf4j
public class MandatoryNoticeService implements IMandatoryNoticeService {

    @Resource
    private IMandatoryNoticeRepository mandatoryNoticeRepository;

    @Override
    @Transactional
    public void createMandatoryNotice(List<MandatoryNoticeDO> list) {
        log.info("【create mandatory notice】data:{}",list);

        if(CollectionUtils.isEmpty(list)){
            return;
        }

        list.stream().map(MandatoryFactory::initMandatoryNotice).collect(Collectors.toList()).forEach(e -> {
            mandatoryNoticeRepository.save(e);
        });

    }

    @Override
    public List<MandatoryNoticeDO> findNoticeByEmpId(String empId) {

        List<MandatoryNoticePO>  list = Optional.ofNullable(mandatoryNoticeRepository.findNoticeByEmpId(empId)).orElse(new ArrayList<>());


        return list.stream().map(MandatoryFactory::convertNotice).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void read(Long id, String empId) {
        mandatoryNoticeRepository.read(id,empId);
    }
}
