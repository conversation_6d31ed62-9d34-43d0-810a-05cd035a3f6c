package com.wantwant.sfa.backend.domain.notify.service.impl;

import com.wantwant.sfa.backend.domain.notify.DO.NotifyDO;
import com.wantwant.sfa.backend.domain.notify.service.INotifyPushService;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.NotifyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/07/上午9:07
 */
@Service
public class NotifyPushService implements INotifyPushService {

    @Resource
    private NotifyService notifyService;

    @Override
    @Transactional
    public void
    saveBatch(NotifyDO notifyDO) {
        List<NotifyPO> notifyPOS = new ArrayList<>();

        List<String> empIds = notifyDO.getEmpIds();
        empIds.stream().filter(f -> StringUtils.isNotBlank(f)).distinct().forEach(e -> {
            NotifyPO po = new NotifyPO();
            po.setTitle(notifyDO.getTitle());
            po.setType(notifyDO.getType());
            po.setContent(notifyDO.getContent());
            po.setCode(notifyDO.getCode());
            po.setEmployeeId(e);
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        });

        notifyService.saveBatch(notifyPOS);
    }
}
