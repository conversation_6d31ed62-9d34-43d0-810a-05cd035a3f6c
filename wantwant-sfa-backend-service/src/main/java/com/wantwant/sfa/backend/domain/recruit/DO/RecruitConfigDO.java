package com.wantwant.sfa.backend.domain.recruit.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:15
 */
@Data
public class RecruitConfigDO {
    @ApiModelProperty("配置ID")
    private Long id;
    @ApiModelProperty("产品组")
    @NotNull(message = "缺少产品组")
    private Integer businessGroup;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("限制岗位类型：5.区域经理 6.区域总监 7.战区督导 8.大区总监 9.省区总监")
    private List<Integer> restricts;
    @ApiModelProperty("有效开始日期")
    private String startValidDate;
    @ApiModelProperty("有效结束日期")
    private String endValidDate;
    @ApiModelProperty("不允许其他岗位兼此岗位:1.是")
    private Integer notAllowOtherPosition;
    @ApiModelProperty("不允许此岗兼任其他岗位:1.是")
    private Integer notAllowCurrentWithOther;
    @ApiModelProperty("备注")
    private String remark;
}
