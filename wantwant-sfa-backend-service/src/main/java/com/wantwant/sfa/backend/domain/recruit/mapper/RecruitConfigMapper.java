package com.wantwant.sfa.backend.domain.recruit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.recruit.repository.po.RecruitConfigPO;
import com.wantwant.sfa.backend.recruit.request.RecruitSearchRequest;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:47
 */
public interface RecruitConfigMapper extends BaseMapper<RecruitConfigPO> {

    /**
     * 查询列表
     *
     * @param page
     * @param recruitSearchRequest
     * @return
     */
    List<RecruitConfigVO> selectList(@Param("page") IPage<RecruitConfigVO> page, @Param("request") RecruitSearchRequest recruitSearchRequest);

    /**
     * 查询受限制ID
     *
     * @param businessGroup
     * @param organizationId
     * @param id
     * @return
     */
    Integer selectRestrictCount(@Param("businessGroup") Integer businessGroup, @Param("organizationId") String organizationId, @Param("id") Integer id);

    /**
     * 检查当前岗位是否可兼职其他岗位
     *
     * @param businessGroup
     * @param organizationId
     * @param id
     * @return
     */
    Integer checkNotAllowCurrentWithOther(@Param("businessGroup") Integer businessGroup, @Param("organizationId") String organizationId, @Param("id") Integer id);

    /**
     * 检查是否允许其他岗位兼此岗位
     *
     * @param businessGroup
     * @param organizationId
     * @param id
     * @return
     */
    Integer checkNotAllowOtherPosition(Integer businessGroup, String organizationId, Integer id);
}
