package com.wantwant.sfa.backend.domain.recruit.repository.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.recruit.repository.po.RecruitConfigPO;
import com.wantwant.sfa.backend.recruit.request.RecruitSearchRequest;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigVO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:17
 */
public interface IRecruitConfigRepository {
    /**
     * 插入数据
     *
     * @param recruitConfigPO
     */
    void insert(RecruitConfigPO recruitConfigPO);

    /**
     * 修改数据
     *
     * @param recruitConfigPO
     */
    void update(RecruitConfigPO recruitConfigPO);

    /**
     * 查询列表
     *
     * @param recruitSearchRequest
     * @return
     */
    List<RecruitConfigVO> select(IPage<RecruitConfigVO> page, RecruitSearchRequest recruitSearchRequest);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    RecruitConfigPO selectById(Long id);

    /**
     * 删除记录
     *
     * @param id
     * @param processUserDO
     */
    void deleteById(Long id, ProcessUserDO processUserDO);

    /**
     * 获取限制数量
     *
     * @param businessGroup
     * @param organizationId
     * @param id
     * @return
     */
    Integer selectRestrictCount(Integer businessGroup, String organizationId, Integer id);

    /**
     * 检查当前岗位是否可兼职其他岗位
     *
     * @param businessGroup
     * @param organizationId
     * @param id
     * @return
     */
    Integer checkNotAllowCurrentWithOther(Integer businessGroup, String organizationId, Integer id);

    /**
     * 检查是否允许其他岗位兼此岗位
     *
     * @param businessGroup
     * @param organizationId
     * @param id
     * @return
     */
    Integer checkNotAllowOtherPosition(Integer businessGroup, String organizationId, Integer id);



}
