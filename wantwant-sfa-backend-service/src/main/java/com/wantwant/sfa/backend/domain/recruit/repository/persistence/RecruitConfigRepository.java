package com.wantwant.sfa.backend.domain.recruit.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.recruit.mapper.RecruitConfigMapper;
import com.wantwant.sfa.backend.domain.recruit.repository.facade.IRecruitConfigRepository;
import com.wantwant.sfa.backend.domain.recruit.repository.po.RecruitConfigPO;
import com.wantwant.sfa.backend.recruit.request.RecruitSearchRequest;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigVO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:17
 */
@Repository
public class RecruitConfigRepository implements IRecruitConfigRepository {

    @Resource
    private RecruitConfigMapper recruitConfigMapper;

    @Override
    public void insert(RecruitConfigPO recruitConfigPO) {
        recruitConfigMapper.insert(recruitConfigPO);
    }

    @Override
    public void update(RecruitConfigPO recruitConfigPO) {
        recruitConfigMapper.updateById(recruitConfigPO);
    }

    @Override
    public List<RecruitConfigVO> select(IPage<RecruitConfigVO> page, RecruitSearchRequest recruitSearchRequest) {

        return recruitConfigMapper.selectList(page,recruitSearchRequest);
    }

    @Override
    public RecruitConfigPO selectById(Long id) {
        return recruitConfigMapper.selectById(id);
    }

    @Override
    public void deleteById(Long id, ProcessUserDO processUserDO) {
        RecruitConfigPO deleteOperate = new RecruitConfigPO();
        deleteOperate.setDeleteFlag(1);
        deleteOperate.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        recruitConfigMapper.update(deleteOperate,new LambdaQueryWrapper<RecruitConfigPO>().eq(RecruitConfigPO::getId,id));
    }

    @Override
    public Integer selectRestrictCount(Integer businessGroup, String organizationId, Integer id) {
        return recruitConfigMapper.selectRestrictCount(businessGroup,organizationId,id);
    }

    @Override
    public Integer checkNotAllowCurrentWithOther(Integer businessGroup, String organizationId, Integer id) {
        return recruitConfigMapper.checkNotAllowCurrentWithOther(businessGroup,organizationId,id);
    }

    @Override
    public Integer checkNotAllowOtherPosition(Integer businessGroup, String organizationId, Integer id) {
        return recruitConfigMapper.checkNotAllowOtherPosition(businessGroup,organizationId,id);
    }

}
