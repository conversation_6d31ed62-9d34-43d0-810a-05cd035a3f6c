package com.wantwant.sfa.backend.domain.recruit.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:20
 */
@TableName("sfa_recruit_config")
@ApiModel(value = "招聘限制", description = "招聘限制")
@Data
public class RecruitConfigPO extends CommonEntity {
    @ApiModelProperty("配置ID")
    private Long id;
    @ApiModelProperty("产品组")
    private Integer businessGroup;
    @ApiModelProperty("组织ID")
    private String organizationId;
    @ApiModelProperty("限制岗位类型：5.区域经理 6.区域总监 7.战区督导 8.大区总监 9.省区总监")
    private String restricts;
    @ApiModelProperty("有效开始日期")
    private String startValidDate;
    @ApiModelProperty("有效结束日期")
    private String endValidDate;
    @ApiModelProperty("不允许其他岗位兼此岗位:1.是")
    private Integer notAllowOtherPosition;
    @ApiModelProperty("不允许此岗兼任其他岗位:1.是")
    private Integer notAllowCurrentWithOther;
    @ApiModelProperty("备注")
    @TableField(value = "remark",strategy = FieldStrategy.IGNORED)
    private String remark;

}
