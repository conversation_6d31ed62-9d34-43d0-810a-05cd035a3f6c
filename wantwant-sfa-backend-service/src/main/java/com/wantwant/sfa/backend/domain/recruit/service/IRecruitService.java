package com.wantwant.sfa.backend.domain.recruit.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.recruit.DO.RecruitConfigDO;
import com.wantwant.sfa.backend.recruit.request.RecruitSearchRequest;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigDetailVO;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigVO;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:29
 */
public interface IRecruitService {

    /**
     * 保存配置
     *
     * @param recruitConfigDO
     * @return
     */
    Long saveConfig(RecruitConfigDO recruitConfigDO, ProcessUserDO processUserDO);


    /**
     * 查询列表
     *
     * @param recruitSearchRequest
     * @return
     */
    IPage<RecruitConfigVO> select(RecruitSearchRequest recruitSearchRequest);

    /**
     * 导出
     *
     * @param recruitSearchRequest
     */
    void export(RecruitSearchRequest recruitSearchRequest);

    /**
     * 查看设置明细
     *
     * @param id
     * @return
     */
    RecruitConfigDetailVO getRecruitConfig(Long id);

    /**
     * 删除操作
     *
     * @param id
     * @param processUserDO
     */
    void delete(Long id, ProcessUserDO processUserDO);


    /**
     * 检查岗位是否被限制
     *
     * @param organizationId
     * @param positionEnum
     * @return
     */
    boolean checkPositionRestrict(Integer businessGroup,String organizationId, PositionEnum positionEnum);

    /**
     * 检查岗位是否可兼职其他岗位
     *
     * @param businessGroup
     * @param organizationCode
     * @param positionEnum
     * @return
     */
    boolean checkNotAllowCurrentWithOther(Integer businessGroup, String organizationCode, PositionEnum positionEnum);


    /**
     * 检查允许其他岗位兼此岗
     *
     * @param businessGroupId
     * @param transactionValue
     * @param positionEnum
     * @return
     */
    boolean checkNotAllowOtherPosition(Integer businessGroupId, String transactionValue, PositionEnum positionEnum);


}
