package com.wantwant.sfa.backend.domain.recruit.service.factory;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.recruit.DO.RecruitConfigDO;
import com.wantwant.sfa.backend.domain.recruit.repository.po.RecruitConfigPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:36
 */
public class RecruitConfigFactory {

    public static RecruitConfigPO buildRecruitConfigPO(RecruitConfigDO recruitConfigDO, ProcessUserDO processUserDO){
        RecruitConfigPO recruitConfigPO = new RecruitConfigPO();
        BeanUtils.copyProperties(recruitConfigDO,recruitConfigPO);
        recruitConfigPO.setEndValidDate(recruitConfigDO.getEndValidDate()+"T23:59:59");
        if(Objects.isNull(recruitConfigDO.getId())){
            recruitConfigPO.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        }else{
            recruitConfigPO.update(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        }

        List<Integer> restricts = recruitConfigDO.getRestricts();
        if(!CollectionUtils.isEmpty(restricts)){
            recruitConfigPO.setRestricts(String.join(",",recruitConfigDO.getRestricts().stream().map(String::valueOf).collect(Collectors.toList())));
        }

        return recruitConfigPO;
    }
}
