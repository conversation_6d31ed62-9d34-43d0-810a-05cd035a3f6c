package com.wantwant.sfa.backend.domain.recruit.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.recruit.DO.RecruitConfigDO;
import com.wantwant.sfa.backend.domain.recruit.repository.facade.IRecruitConfigRepository;
import com.wantwant.sfa.backend.domain.recruit.repository.po.RecruitConfigPO;
import com.wantwant.sfa.backend.domain.recruit.service.IRecruitService;
import com.wantwant.sfa.backend.domain.recruit.service.factory.RecruitConfigFactory;
import com.wantwant.sfa.backend.recruit.request.RecruitSearchRequest;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigDetailVO;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigVO;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:31
 */
@Service
public class RecruitService implements IRecruitService {

    @Resource
    private IRecruitConfigRepository recruitConfigRepository;

    @Override
    @Transactional
    public Long saveConfig(RecruitConfigDO recruitConfigDO, ProcessUserDO processUserDO) {

        RecruitConfigPO recruitConfigPO = RecruitConfigFactory.buildRecruitConfigPO(recruitConfigDO, processUserDO);
        if(Objects.isNull(recruitConfigPO.getId())){
            recruitConfigRepository.insert(recruitConfigPO);
        }else{
            recruitConfigRepository.update(recruitConfigPO);
        }

        return recruitConfigPO.getId();
    }


    @Override
    public IPage<RecruitConfigVO> select(RecruitSearchRequest recruitSearchRequest) {
        IPage<RecruitConfigVO> page = new Page(recruitSearchRequest.getPage(),recruitSearchRequest.getRows());

        List<RecruitConfigVO> list = Optional.ofNullable(recruitConfigRepository.select(page,recruitSearchRequest)).orElse(new ArrayList<>());
        list = resultProcess(list);

        page.setRecords(list);
        return page;
    }

    private List<RecruitConfigVO> resultProcess(List<RecruitConfigVO> list) {
        if(CollectionUtils.isEmpty(list)){
            return list;
        }

        list.forEach(e -> {
            String restricts = e.getRestricts();
            if(StringUtils.isNotBlank(restricts)){
                List<PositionEnum> positionEnumList = Arrays.asList(restricts.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList())
                        .stream().map(PositionEnum::getEnumById).collect(Collectors.toList());

                if(!CollectionUtils.isEmpty(positionEnumList)){
                    e.setRestrictsStr(String.join(",",positionEnumList.stream().map(PositionEnum::getPositionName).collect(Collectors.toList())));
                }


                LocalDate startDate = LocalDate.parse(e.getStartValidDate());
                LocalDate endDate = LocalDate.parse(e.getEndValidDate());
                LocalDate now = LocalDate.now();
                if((startDate.isEqual(now) || startDate.isBefore(now)) && (endDate.isEqual(now) || endDate.isAfter(now))){
                    e.setStatus("已生效");
                }else{
                    e.setStatus("未生效");
                }
            }

        });
        return list;
    }

    @Override
    public void export(RecruitSearchRequest recruitSearchRequest) {
        List<RecruitConfigVO> list = Optional.ofNullable(recruitConfigRepository.select(null,recruitSearchRequest)).orElse(new ArrayList<>());
        list = resultProcess(list);


        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "招聘设置" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), RecruitConfigVO.class, list);
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public RecruitConfigDetailVO getRecruitConfig(Long id) {

        RecruitConfigPO recruitConfigPO = recruitConfigRepository.selectById(id);
        if(Objects.isNull(recruitConfigPO)){
            throw new ApplicationException("招聘设置不存在");
        }

        RecruitConfigDetailVO recruitConfigDetailVO = new RecruitConfigDetailVO();
        BeanUtils.copyProperties(recruitConfigPO,recruitConfigDetailVO);

        String restricts = recruitConfigPO.getRestricts();
        if(StringUtils.isNotBlank(restricts)){
            List<Integer> collect = Arrays.asList(restricts.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            recruitConfigDetailVO.setRestricts(collect);
        }

        return recruitConfigDetailVO;
    }

    @Override
    @Transactional
    public void delete(Long id, ProcessUserDO processUserDO) {
        recruitConfigRepository.deleteById(id,processUserDO);
    }


    @Override
    public boolean checkPositionRestrict(Integer businessGroup,String organizationId, PositionEnum positionEnum) {

        Integer count = recruitConfigRepository.selectRestrictCount(businessGroup,organizationId,positionEnum.getId());

        if(count > 0 ){
            return true;
        }
        return false;
    }

    @Override
    public boolean checkNotAllowCurrentWithOther(Integer businessGroup, String organizationId, PositionEnum positionEnum) {
        Integer count = recruitConfigRepository.checkNotAllowCurrentWithOther(businessGroup,organizationId,positionEnum.getId());

        if(count > 0 ){
            return true;
        }
        return false;
    }

    @Override
    public boolean checkNotAllowOtherPosition(Integer businessGroup, String organizationId, PositionEnum positionEnum) {
        Integer count = recruitConfigRepository.checkNotAllowOtherPosition(businessGroup,organizationId,positionEnum.getId());

        if(count > 0 ){
            return true;
        }
        return false;
    }
}
