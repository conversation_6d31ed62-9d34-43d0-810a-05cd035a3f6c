package com.wantwant.sfa.backend.domain.rpc.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2025/03/07/下午1:53
 */
@Data
@TableName("sfa_rpc_err_log")
public class RpcErrorLogPO {

    @TableId(value = "log_id", type = IdType.AUTO)
    @ApiModelProperty("日志ID")
    private Long logId;

    @TableField("api")
    @ApiModelProperty("接口名称")
    private String api;

    @TableField("method")
    @ApiModelProperty("方法名称")
    private String method;

    @TableField("request")
    @ApiModelProperty("请求参数")
    private String request;

    @TableField("response")
    @ApiModelProperty("响应结果")
    private String response;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
