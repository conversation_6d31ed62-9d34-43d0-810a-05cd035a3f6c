package com.wantwant.sfa.backend.domain.sms.repository.facade;

import com.wantwant.sfa.backend.domain.sms.repository.po.SmsVerificationsPO;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/上午10:16
 */
public interface ISmsVerificationRepository {
    /**
     * 标记无效
     *
     * @param mobile
     * @param type
     */
    void invalid(String mobile, Integer type);

    /**
     * 保存验证码
     *
     * @param smsVerificationsPO
     */
    void save(SmsVerificationsPO smsVerificationsPO);

    String getCode(Integer type, String mobile);
}
