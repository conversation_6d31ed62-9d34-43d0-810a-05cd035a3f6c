package com.wantwant.sfa.backend.domain.sms.repository.persistence;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.domain.sms.mapper.SmsVerificationMapper;
import com.wantwant.sfa.backend.domain.sms.repository.facade.ISmsVerificationRepository;
import com.wantwant.sfa.backend.domain.sms.repository.po.SmsVerificationsPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/上午10:16
 */
@Repository
@Slf4j
public class SmsVerificationRepository implements ISmsVerificationRepository {

    @Resource
    private SmsVerificationMapper smsVerificationMapper;

    @Override
    public void invalid(String mobile, Integer type) {
        SmsVerificationsPO smsVerificationsPO = new SmsVerificationsPO();
        smsVerificationsPO.setStatus(0);
        smsVerificationMapper.update(smsVerificationsPO,new LambdaQueryWrapper<SmsVerificationsPO>().eq(SmsVerificationsPO::getMobile,mobile)
                .eq(SmsVerificationsPO::getType,type).eq(SmsVerificationsPO::getDeleteFlag,0));
    }

    @Override
    public void save(SmsVerificationsPO smsVerificationsPO) {
        smsVerificationMapper.insert(smsVerificationsPO);
    }


    @Override
    public String getCode(Integer type, String mobile) {
        SmsVerificationsPO smsVerificationsPO = smsVerificationMapper.selectOne(new LambdaQueryWrapper<SmsVerificationsPO>().eq(SmsVerificationsPO::getMobile, mobile).eq(SmsVerificationsPO::getType, type)
                .ge(SmsVerificationsPO::getCreateTime, LocalDateTimeUtils.formatTime(LocalDateTime.now().minusMinutes(15L), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss))
                .eq(SmsVerificationsPO::getStatus, 1).eq(SmsVerificationsPO::getDeleteFlag, 0)
        );
        if(Objects.isNull(smsVerificationsPO)){
            return StringUtils.EMPTY;
        }
        return smsVerificationsPO.getCode();
    }


}
