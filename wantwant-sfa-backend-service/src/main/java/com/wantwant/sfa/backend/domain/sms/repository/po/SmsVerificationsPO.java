package com.wantwant.sfa.backend.domain.sms.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 短信验证表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@TableName("sfa_sms_verifications")
@ApiModel(value = "SfaSmsVerifications对象", description = "短信验证表")
@Data
public class SmsVerificationsPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sms_id", type = IdType.AUTO)
    private Long smsId;

    @ApiModelProperty("类型(1.旺金币发放验证)")
    private Integer type;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("验证code")
    private String code;

    @ApiModelProperty("状态(1.有效 0.无效)")
    private Integer status;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
