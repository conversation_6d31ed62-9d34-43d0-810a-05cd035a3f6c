package com.wantwant.sfa.backend.domain.sms.service;

import com.wantwant.sfa.backend.domain.sms.DO.InitVerificationCodeDO;
import com.wantwant.sfa.backend.domain.sms.DO.VerificationCodeVerifyDO;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/上午9:55
 */
public interface ISmsVerificationService {

    /**
     * 生成验证码
     *
     * @param initVerificationCodeDO
     */
    void initVerificationCode(InitVerificationCodeDO initVerificationCodeDO);


    boolean verify(VerificationCodeVerifyDO verificationCodeVerifyDO);
}
