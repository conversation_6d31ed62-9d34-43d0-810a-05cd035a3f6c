package com.wantwant.sfa.backend.domain.sms.service.factory;

import com.wantwant.sfa.backend.domain.sms.repository.po.SmsVerificationsPO;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/上午10:31
 */
public class SmsVerificationFactory {

    public static SmsVerificationsPO initVerificationPO(String mobile,String code,Integer type){
        SmsVerificationsPO smsVerificationsPO = new SmsVerificationsPO();
        smsVerificationsPO.setMobile(mobile);
        smsVerificationsPO.setCode(code);
        smsVerificationsPO.setType(type);
        smsVerificationsPO.setStatus(1);
        smsVerificationsPO.setDeleteFlag(0);
        smsVerificationsPO.setCreateTime(LocalDateTime.now());
        return smsVerificationsPO;
    }
}
