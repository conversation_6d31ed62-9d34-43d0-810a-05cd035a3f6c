package com.wantwant.sfa.backend.domain.thread.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 异步线程异常记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20
 */
@TableName("sfa_thread_err_record")
@ApiModel(value = "SfaThreadErrRecord对象", description = "异步线程异常记录")
@Data
public class ThreadErrRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("类型（1.扣罚)")
    private Integer type;

    @ApiModelProperty("参数")
    private String params;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("删除标志(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("异常信息")
    private String errMsg;

}
