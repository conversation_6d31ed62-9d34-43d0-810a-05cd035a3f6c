package com.wantwant.sfa.backend.domain.wallet.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/26/下午5:03
 */
@Data
public class ExpensesAdditionalDO {
    @ApiModelProperty("费用标签类型:1.常规费用 2.费用前置 3.历史遗留")
    private Integer expensesTag;

    @ApiModelProperty("关联对象类型，1.合伙人 2.客户")
    private Integer associateType;

    @ApiModelProperty("memberKey")
    private Long memberKey;

    @ApiModelProperty("客户ID")
    private String customerId;

    @ApiModelProperty("订单编号")
    private List<String> orderList;
}
