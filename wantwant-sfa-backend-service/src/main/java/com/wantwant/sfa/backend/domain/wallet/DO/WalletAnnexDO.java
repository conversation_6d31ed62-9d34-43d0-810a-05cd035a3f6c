package com.wantwant.sfa.backend.domain.wallet.DO;

import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.wallet.request.ApplyAnnexRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/22/下午10:55
 */
@Data
public class WalletAnnexDO {

    @ApiModelProperty("申请ID")
    private Long applyId;

    @ApiModelProperty("文件名称")
    private String name;

    @ApiModelProperty("文件类型")
    private String type;

    @ApiModelProperty("文件地址")
    private String url;


    public static WalletAnnexDO create(ApplyAnnexRequest request){
        WalletAnnexDO walletAnnexDO = new WalletAnnexDO();
        BeanUtils.copyProperties(request,walletAnnexDO);
        return walletAnnexDO;
    }
}
