package com.wantwant.sfa.backend.domain.wallet.DO;


import com.wantwant.sfa.backend.wallet.request.ApplyAnnexRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午8:46
 */
@Data
@ToString
public class WalletQuotaApplicationDO {

    @ApiModelProperty("类型(1.发放给组织 2.发放给个人)")
    private Integer applyType;

    @ApiModelProperty("费用支出方")
    private String expenditure;

    @ApiModelProperty("支出方组织CODE")
    private String expenditureOrganizationId;

    @ApiModelProperty("费用收入方")
    private String revenue;

    @ApiModelProperty("申请额度")
    private BigDecimal quota;

    @ApiModelProperty("费用支出币种类型")
    private Integer paymentWalletType;

    @ApiModelProperty("费用支出spuId")
    private String paymentSpuId;

    @ApiModelProperty("接受币种类型")
    private Integer acceptedWalletType;

    @ApiModelProperty("接受币种子类型")
    private String acceptedSpuId;

    @ApiModelProperty("接收人memberKey")
    private Long acceptedMemberKey;

    @ApiModelProperty("接收组织ID")
    private String acceptedOrganizationId;

    @ApiModelProperty("申请人信息")
    private WalletQuotaApplicationUserDO walletQuotaApplicationUserDO;

    @ApiModelProperty("费用用途")
    private String costPurpose;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否跨级操作")
    private boolean bypassHierarchy;

    @ApiModelProperty("流程实例ID")
    private Long flowInstanceId;

    @ApiModelProperty("附件")
    private List<ApplyAnnexRequest> annexRequestList;

    @ApiModelProperty("产品组")
    private Integer businessGroup;

    @ApiModelProperty("费用类型额外信息")
    private ExpensesAdditionalDO expensesAdditionalDO;
}
