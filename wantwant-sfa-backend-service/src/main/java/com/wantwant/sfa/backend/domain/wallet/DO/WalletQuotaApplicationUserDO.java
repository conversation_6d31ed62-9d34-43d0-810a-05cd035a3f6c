package com.wantwant.sfa.backend.domain.wallet.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午8:46
 */
@Data
public class WalletQuotaApplicationUserDO {

    @ApiModelProperty("申请人战区code")
    private String areaCode;

    @ApiModelProperty("申请人战区名称")
    private String areaName;

    @ApiModelProperty("申请人大区code")
    private String vareaCode;

    @ApiModelProperty("申请人大区名称")
    private String vareaName;

    @ApiModelProperty("申请人省区code")
    private String provinceCode;

    @ApiModelProperty("申请人省区名称")
    private String provinceName;

    @ApiModelProperty("申请人分公司code")
    private String companyCode;

    @ApiModelProperty("申请人分公司名称")
    private String companyName;

    @ApiModelProperty("申请人营业所code")
    private String departmentCode;

    @ApiModelProperty("申请人营业所名称")
    private String departmentName;

    @ApiModelProperty("申请人memberKey")
    private Long applyMemberKey;

    @ApiModelProperty("申请人工号")
    private String applyEmpId;

    @ApiModelProperty("申请人姓名")
    private String applyEmpName;

    @ApiModelProperty("申请人岗位类型")
    private String applyPositionType;

    @ApiModelProperty("申请人岗位ID")
    private String applyPositionId;


}
