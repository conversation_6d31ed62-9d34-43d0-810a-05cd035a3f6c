package com.wantwant.sfa.backend.domain.wallet.DO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午9:28
 */
@Data
@ApiModel("冻结旺金币")
@ToString
public class WalletQuotaLockDO {
    /** 冻结组织ID */
    private String organizationId;

    /** 币种类型 */
    private Integer walletType;

    @ApiModelProperty("申请额度")
    private BigDecimal quota;

    /** 费用支出spuId */
    private String paymentSpuId;

    @ApiModelProperty("费用支出方")
    private String expenditure;

    @ApiModelProperty("费用收入方")
    private String revenue;

    @ApiModelProperty("接收人memberKey")
    private Long acceptedMemberKey;

    @ApiModelProperty("接收组织ID")
    private String acceptedOrganizationId;

    @ApiModelProperty("处理人工号")
    private String processUserId;
    @ApiModelProperty("处理人姓名")
    private String processUserName;
    @ApiModelProperty("流程实例ID")
    private Long instanceId;
}
