package com.wantwant.sfa.backend.domain.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationPO;
import com.wantwant.sfa.backend.wallet.request.WalletQuotaApplySearchRequest;
import com.wantwant.sfa.backend.wallet.vo.ApplyHistoryVO;
import com.wantwant.sfa.backend.wallet.vo.WalletQuotaApplicationVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午9:13
 */
public interface WalletApplicationMapper extends BaseMapper<WantWalletApplicationPO> {


    List<WalletQuotaApplicationVo> search(@Param("page") IPage<WalletQuotaApplicationVo> page,
                                          @Param("request") WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, @Param("roleIds") List<Integer> roleIds);

    BigDecimal pendingQuota(@Param("applyType") Integer applyType, @Param("key") String key);


    BigDecimal searchPersonCurrentMonthIncome(@Param("memberKey") Long acceptedMemberKey, @Param("yearMonth") String yearMonth);

    List<ApplyHistoryVO> selectApplyHistory(@Param("applyType")Integer applyType,
                                            @Param("acceptedOrganizationId") String acceptedOrganizationId,
                                            @Param("acceptedMemberKey")Long acceptedMemberKey,
                                            @Param("applyId") Long applyId);
}
