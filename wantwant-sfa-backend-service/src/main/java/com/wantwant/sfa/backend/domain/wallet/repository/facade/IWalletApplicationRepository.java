package com.wantwant.sfa.backend.domain.wallet.repository.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.wallet.DO.WalletAnnexDO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateObjPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateOrderPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationPO;
import com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity;
import com.wantwant.sfa.backend.wallet.request.WalletQuotaApplySearchRequest;
import com.wantwant.sfa.backend.wallet.vo.ApplyHistoryVO;
import com.wantwant.sfa.backend.wallet.vo.WalletQuotaApplicationVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午9:06
 */
public interface IWalletApplicationRepository {

    Long save(WantWalletApplicationPO wantWalletApplicationPO);

    Long findWalletAccount(String organizationId, Integer walletType);

    WantWalletApplicationPO selectApplicationByInstanceId(Long instanceId);

    List<WantWalletLogEntity> selectLockedBy(Long instanceId);

    void addQuota(Long walletAccountId, Integer walletTypeId, BigDecimal total);

    List<WalletQuotaApplicationVo> search(@Param("page") IPage<WalletQuotaApplicationVo> page, @Param("request") WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, @Param("roleIds") List<Integer> roleIds);

    /**
     * 保存附件
     *
     * @param collect
     */
    void saveAnnex(List<WalletAnnexDO> collect);

    WantWalletApplicationPO selectApplicationById(Long applyId);

    BigDecimal searchPendingQuota(@Param("applyType") Integer applyType, @Param("key") String key);


    BigDecimal searchPersonCurrentMonthIncome(@Param("memberKey")Long acceptedMemberKey, @Param("yearMonth") String yearMonth);


    BigDecimal searchOrganizationCurrentMonthIncome(String acceptedOrganizationId, String substring);

    BigDecimal selectTotalIncome(String acceptedOrganizationId, LocalDate startDate,LocalDate endDate);


    WantWalletApplicationPO getApplyDetailByInstanceId(Long instanceId);


    String findWalletTypeNameById(Integer paymentWalletType);


    Long saveAssociateObj(WantWalletApplicationAssociateObjPO associateObj);

    void saveAssociateOrder(List<WantWalletApplicationAssociateOrderPO> associateOrder);

    List<ApplyHistoryVO> selectApplyHistory(Integer applyType, String acceptedOrganizationId, Long acceptedMemberKey, Long applyId);

    WantWalletApplicationAssociateObjPO selectAssociateObjByApplyId(Long applyId);

    List<String> selectAssociateOrderList(Long associateId);

    BigDecimal selectSurplus(String acceptedOrganizationId);


    BigDecimal selectOldInCome(String acceptedOrganizationId);
}
