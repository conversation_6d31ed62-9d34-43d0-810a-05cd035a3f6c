package com.wantwant.sfa.backend.domain.wallet.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.wallet.DO.WalletAnnexDO;
import com.wantwant.sfa.backend.domain.wallet.mapper.WalletApplicationMapper;
import com.wantwant.sfa.backend.domain.wallet.mapper.WalletApplyAnnexMapper;
import com.wantwant.sfa.backend.domain.wallet.mapper.WantWalletApplicationAssociateObjMapper;
import com.wantwant.sfa.backend.domain.wallet.mapper.WantWalletApplicationAssociateOrderMapper;
import com.wantwant.sfa.backend.domain.wallet.repository.facade.IWalletApplicationRepository;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAnnexPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateObjPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateOrderPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationPO;
import com.wantwant.sfa.backend.mapper.wallet.*;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.wallet.entity.*;
import com.wantwant.sfa.backend.wallet.enums.WalletLogTypeEnum;
import com.wantwant.sfa.backend.wallet.request.WalletQuotaApplySearchRequest;
import com.wantwant.sfa.backend.wallet.vo.ApplyHistoryVO;
import com.wantwant.sfa.backend.wallet.vo.WalletQuotaApplicationVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午9:07
 */
@Repository
public class WalletApplicationRepository implements IWalletApplicationRepository {
    @Resource
    private WalletApplicationMapper walletApplicationMapper;
    @Resource
    private WantWalletAccountMapper wantWalletAccountMapper;
    @Resource
    private WantWalletLogMapper wantWalletLogMapper;
    @Resource
    private WantWalletMapper wantWalletMapper;
    @Resource
    private WalletApplyAnnexMapper walletApplyAnnexMapper;
    @Resource
    private WantWalletTypeMapper wantWalletTypeMapper;
    @Resource
    private WantWalletApplicationAssociateObjMapper wantWalletApplicationAssociateObjMapper;
    @Resource
    private WantWalletApplicationAssociateOrderMapper wantWalletApplicationAssociateOrderMapper;


    @Override
    public Long save(WantWalletApplicationPO wantWalletApplicationPO) {

        walletApplicationMapper.insert(wantWalletApplicationPO);

        return wantWalletApplicationPO.getApplyId();
    }

    @Override
    public Long findWalletAccount(String organizationId, Integer walletType) {
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        if(Objects.isNull(wantWalletAccountEntity)){
            return null;
        }

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId())
                .eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        if(Objects.isNull(wantWalletEntity)){
            return null;
        }

        return wantWalletAccountEntity.getAccountId();
    }

    @Override
    public WantWalletApplicationPO selectApplicationByInstanceId(Long instanceId) {
        WantWalletApplicationPO wantWalletApplicationPO = walletApplicationMapper.selectOne(new LambdaQueryWrapper<WantWalletApplicationPO>().eq(WantWalletApplicationPO::getFlowInstanceId, instanceId).eq(WantWalletApplicationPO::getDeleteFlag, 0).last("limit 1"));
        return wantWalletApplicationPO;
    }

    @Override
    public List<WantWalletLogEntity> selectLockedBy(Long instanceId) {
        return wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getInstanceId,instanceId).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.LOCK.getCode()).eq(WantWalletLogEntity::getDeleteFlag,0));

    }

    @Override
    public void addQuota(Long walletAccountId, Integer walletTypeId, BigDecimal total) {
        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, walletAccountId).eq(WantWalletEntity::getWalletTypeId, walletTypeId).eq(WantWalletEntity::getDeleteFlag, 0).last("limit 1"));
        if(Objects.nonNull(wantWalletEntity)){
            wantWalletEntity.setSurplus(wantWalletEntity.getSurplus().add(total));
            wantWalletMapper.updateById(wantWalletEntity);
        }
    }



    @Override
    public List<WalletQuotaApplicationVo> search(IPage<WalletQuotaApplicationVo> page,  WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, List<Integer> roleIds) {
        List<WalletQuotaApplicationVo>  list = walletApplicationMapper.search(page,walletQuotaApplySearchRequest,roleIds);
        return list;
    }

    @Override
    public void saveAnnex(List<WalletAnnexDO> collect) {
        collect.forEach(e -> {
            WantWalletApplicationAnnexPO wantWalletApplicationAnnexPO = new WantWalletApplicationAnnexPO();
            BeanUtils.copyProperties(e,wantWalletApplicationAnnexPO);
            walletApplyAnnexMapper.insert(wantWalletApplicationAnnexPO);
        });
    }

    @Override
    public WantWalletApplicationPO selectApplicationById(Long applyId) {

        return walletApplicationMapper.selectById(applyId);

    }

    @Override
    public BigDecimal searchPendingQuota(Integer applyType, String key) {
        return walletApplicationMapper.pendingQuota(applyType,key);
    }

    @Override
    public BigDecimal searchPersonCurrentMonthIncome(Long acceptedMemberKey, String yearMonth) {
        return walletApplicationMapper.searchPersonCurrentMonthIncome(acceptedMemberKey,yearMonth);
    }

    @Override
    public BigDecimal searchOrganizationCurrentMonthIncome(String acceptedOrganizationId, String yearMonth) {
        return wantWalletLogMapper.selectIncome(acceptedOrganizationId,yearMonth,null,null);
    }

    @Override
    public BigDecimal selectTotalIncome(String acceptedOrganizationId, LocalDate startDate,LocalDate endDate) {
        return wantWalletLogMapper.selectIncome(acceptedOrganizationId,null,startDate,endDate);
    }

    @Override
    public WantWalletApplicationPO getApplyDetailByInstanceId(Long instanceId) {
        return walletApplicationMapper.selectOne(new LambdaQueryWrapper<WantWalletApplicationPO>().eq(WantWalletApplicationPO::getFlowInstanceId,instanceId).eq(WantWalletApplicationPO::getDeleteFlag,0).last("limit 1"));
    }

    @Override
    public String findWalletTypeNameById(Integer paymentWalletType) {
        WantWalletTypeEntity wantWalletTypeEntity = wantWalletTypeMapper.selectById(paymentWalletType);
        if(Objects.isNull(wantWalletTypeEntity)){
            return StringUtils.EMPTY;
        }
        return wantWalletTypeEntity.getWalletTypeName();
    }

    @Override
    public Long saveAssociateObj(WantWalletApplicationAssociateObjPO associateObj) {
        wantWalletApplicationAssociateObjMapper.insert(associateObj);
        return associateObj.getAssociateId();
    }

    @Override
    public void saveAssociateOrder(List<WantWalletApplicationAssociateOrderPO> associateOrder) {
        associateOrder.forEach(e -> {
            wantWalletApplicationAssociateOrderMapper.insert(e);
        });

    }

    @Override
    public List<ApplyHistoryVO> selectApplyHistory(Integer applyType, String acceptedOrganizationId, Long acceptedMemberKey, Long applyId) {
        return walletApplicationMapper.selectApplyHistory(applyType,acceptedOrganizationId,acceptedMemberKey,applyId);
    }

    @Override
    public WantWalletApplicationAssociateObjPO selectAssociateObjByApplyId(Long applyId) {
        return wantWalletApplicationAssociateObjMapper.selectOne(new LambdaQueryWrapper<WantWalletApplicationAssociateObjPO>()
                .eq(WantWalletApplicationAssociateObjPO::getApplyId,applyId).eq(WantWalletApplicationAssociateObjPO::getDeleteFlag,0).last("limit 1"));
    }

    @Override
    public List<String> selectAssociateOrderList(Long associateId) {
        List<WantWalletApplicationAssociateOrderPO> wantWalletApplicationAssociateOrderPOS = Optional.ofNullable(wantWalletApplicationAssociateOrderMapper.selectList(new LambdaQueryWrapper<WantWalletApplicationAssociateOrderPO>().eq(WantWalletApplicationAssociateOrderPO::getAssociateId, associateId).eq(WantWalletApplicationAssociateOrderPO::getDeleteFlag, 0))).orElse(new ArrayList<>());
        return wantWalletApplicationAssociateOrderPOS.stream().map(WantWalletApplicationAssociateOrderPO::getOrderNo).collect(Collectors.toList());
    }

    @Override
    public BigDecimal selectSurplus(String acceptedOrganizationId) {
        return wantWalletLogMapper.selectSurplus(acceptedOrganizationId);
    }

    @Override
    public BigDecimal selectOldInCome(String acceptedOrganizationId) {
        return wantWalletLogMapper.selectOldInCome(acceptedOrganizationId);
    }


}
