package com.wantwant.sfa.backend.domain.wallet.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@TableName("sfa_wallet_expense_rate_control")
@ApiModel(value = "SfaWalletExpenseRateControl对象", description = "")
@Data
public class WalletExpenseRateControlPO implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "control_id", type = IdType.AUTO)
    private Long controlId;

    @ApiModelProperty("组织CODE")
    private String organziationId;

    @ApiModelProperty("组织费用率管控")
    private BigDecimal organizationExpenseRate;

    @ApiModelProperty("合伙人费用率")
    private BigDecimal ceoExpenseRate;

    @ApiModelProperty("是否删除（1.是）")
    private Integer deleteFlag;

}
