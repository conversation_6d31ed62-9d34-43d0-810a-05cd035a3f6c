package com.wantwant.sfa.backend.domain.wallet.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 旺金币申请附件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
@TableName("sfa_want_wallet_application_annex")
@ApiModel(value = "SfaWantWalletApplicationAnnex对象", description = "旺金币申请附件")
@Data
public class WantWalletApplicationAnnexPO implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "file_id", type = IdType.AUTO)
    private Long fileId;

    @ApiModelProperty("sfa_want_wallet_application主键")
      private Long applyId;

    @ApiModelProperty("文件名称")
    private String name;

    @ApiModelProperty("文件类型")
    private String type;

    @ApiModelProperty("文件地址")
    private String url;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;




}
