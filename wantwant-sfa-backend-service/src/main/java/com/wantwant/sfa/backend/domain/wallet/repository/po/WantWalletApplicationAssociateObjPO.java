package com.wantwant.sfa.backend.domain.wallet.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 旺金币申请关联对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@TableName("sfa_want_wallet_application_associate_obj")
@ApiModel(value = "SfaWantWalletApplicationAssociateObj对象", description = "旺金币申请关联对象")
public class WantWalletApplicationAssociateObjPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "associate_id", type = IdType.AUTO)
    private Long associateId;

    @ApiModelProperty("申请IDa")
    private Long applyId;

    @ApiModelProperty("关联对象类型(1.合伙人 2.客户)")
    private Integer type;

    @ApiModelProperty("合伙人memberkey")
    private Long memberKey;

    @ApiModelProperty("客户ID")
    private String customerId;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;

}
