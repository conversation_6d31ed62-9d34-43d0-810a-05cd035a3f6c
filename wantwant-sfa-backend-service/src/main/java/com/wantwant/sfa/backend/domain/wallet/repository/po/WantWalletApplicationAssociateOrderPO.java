package com.wantwant.sfa.backend.domain.wallet.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 旺金币申请订单相关
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@TableName("sfa_want_wallet_application_associate_order")
@ApiModel(value = "SfaWantWalletApplicationAssociateOrder对象", description = "旺金币申请订单相关")
public class WantWalletApplicationAssociateOrderPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("sfa_want_wallet_application_associate_obj主键")
    private Long associateId;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("是否删除(1.是)")
    private Integer deleteFlag;



}
