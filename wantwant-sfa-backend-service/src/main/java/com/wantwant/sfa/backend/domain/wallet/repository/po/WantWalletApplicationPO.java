package com.wantwant.sfa.backend.domain.wallet.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 旺金币申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@TableName("sfa_want_wallet_application")
@ApiModel(value = "SfaWantWalletApplication对象", description = "旺金币申请表")
@Data
public class WantWalletApplicationPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "apply_id", type = IdType.AUTO)
    private Long applyId;

    @ApiModelProperty("类型(1.发放给组织 2.发放给个人)")
    private Integer applyType;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty("费用支出方")
    private String expenditure;

    @ApiModelProperty("支出方组织CODE")
    private String expenditureOrganizationId;

    @ApiModelProperty("费用收入方")
    private String revenue;

    @ApiModelProperty("申请额度")
    private BigDecimal quota;

    @ApiModelProperty("费用支出币种类型")
    private Integer paymentWalletType;

    @ApiModelProperty("费用支出spuId")
    private String paymentSpuId;

    @ApiModelProperty("接受币种类型")
    private Integer acceptedWalletType;

    @ApiModelProperty("接受币种子类型")
    private String acceptedSpuId;

    @ApiModelProperty("接收人memberKey")
    private Long acceptedMemberKey;

    @ApiModelProperty("接收组织ID")
    private String acceptedOrganizationId;

    @ApiModelProperty("申请人战区code")
    private String areaCode;

    @ApiModelProperty("申请人战区名称")
    private String areaName;

    @ApiModelProperty("申请人大区code")
    private String vareaCode;

    @ApiModelProperty("申请人大区名称")
    private String vareaName;

    @ApiModelProperty("申请人省区code")
    private String provinceCode;

    @ApiModelProperty("申请人省区名称")
    private String provinceName;

    @ApiModelProperty("申请人分公司code")
    private String companyCode;

    @ApiModelProperty("申请人分公司名称")
    private String companyName;

    @ApiModelProperty("申请人营业所code")
    private String departmentCode;

    @ApiModelProperty("申请人营业所名称")
    private String departmentName;

    @ApiModelProperty("费用用途")
    private String costPurpose;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("申请人memberKey")
    private Long applyMemberKey;

    @ApiModelProperty("申请人工号")
    private String applyEmpId;
    @ApiModelProperty("申请人姓名")
    private String applyEmpName;

    @ApiModelProperty("申请人岗位类型")
    private String applyPositionType;

    @ApiModelProperty("申请人positionId")
    private String applyPositionId;

    @ApiModelProperty("是否跨级操作")
    private boolean bypassHierarchy;

    @ApiModelProperty("流程实例ID")
    private Long flowInstanceId;

    @ApiModelProperty("是否删除")
    private Integer deleteFlag;

    private Integer businessGroup;

    private Integer expensesTag;
}
