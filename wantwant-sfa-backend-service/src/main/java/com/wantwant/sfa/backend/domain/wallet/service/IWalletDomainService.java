package com.wantwant.sfa.backend.domain.wallet.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowCurrentDO;
import com.wantwant.sfa.backend.domain.wallet.DO.*;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationPO;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.wallet.request.WalletQuotaApplySearchRequest;
import com.wantwant.sfa.backend.wallet.vo.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午8:38
 */
public interface IWalletDomainService {

    /**
     * 申请旺金币发放
     *
     * @param walletQuotaApplicationDO
     * @return
     */
    Long apply(WalletQuotaApplicationDO walletQuotaApplicationDO);

    /**
     * 锁定旺金币
     *
     * @param walletQuotaLockDO
     */
    void lock(WalletQuotaLockDO walletQuotaLockDO);

    /**
     * 将锁定的旺金币发放
     *
     * @param instanceId
     */
    void sendLocked(Long instanceId, ProcessUserDO processUserDO);

    /**
     * 检查是否超费用率
     *
     * @param instanceId
     * @return
     */
    boolean checkRatioOver(Long instanceId);

    /**
     * 释放锁
     *
     * @param walletLockReleaseDO
     */
    void LockRelease(WalletLockReleaseDO walletLockReleaseDO);

    /**
     * 分页查询
     *
     * @param walletQuotaApplySearchRequest
     * @param roleIds
     * @return
     */
    IPage<WalletQuotaApplicationVo> search( WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, List<Integer> roleIds);

    /**
     * 保存附件
     *
     * @param collect
     */
    void saveAnnex(List<WalletAnnexDO> collect);

    /**
     * 获取申请详情
     *
     * @param applyId
     * @return
     */
    WantWalletApplicationPO getApplyDetail(Long applyId);

    /**
     * 获取额度信息
     *
     * @param applyType
     * @param acceptedOrganizationId
     * @param acceptedMemberKey
     * @return
     */
    QuotaInfoVo findQuotaInfoVo(Integer applyType, String acceptedOrganizationId, Long acceptedMemberKey,String customerId, LocalDateTime applyTime);

    /**
     * 旺金币申请信息
     *
     * @param instanceId
     * @return
     */
    WantWalletApplicationPO getApplyDetailByInstanceId(Long instanceId);


    /**
     * 获取费用率
     *
     * @param organizationId
     * @param quota
     * @param acceptedKey
     * @return
     */
    WalletExpenseRateDO calculateExpenseRate(Integer applyType, String organizationId, BigDecimal quota, String acceptedKey,LocalDateTime applyTime);


    String findWalletTypeNameById(Integer paymentWalletType);

    void searchExport(WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, List<Integer> roleIds);

    void saveExpensesAdditional(Long applyId, ExpensesAdditionalDO expensesAdditionalDO);

    List<ApplyHistoryVO> selectApplyHistory(Integer applyType, String acceptedOrganizationId, Long acceptedMemberKey, Long applyId);

    /**
     * 获取关联对象
     *
     * @param applyId
     * @return
     */
    AssociateObjVO selectAssociateObj(Long applyId);

    OrganizationQuotaVO selectOrgYearVO(String acceptedOrganizationId);

    /**
     * 检查是否跨级审核
     *
     * @param instanceId
     * @return
     */
    boolean checkByPassHierarchy(Long instanceId);

}
