package com.wantwant.sfa.backend.domain.wallet.service.factory;

import com.wantwant.sfa.backend.domain.wallet.DO.ExpensesAdditionalDO;
import com.wantwant.sfa.backend.domain.wallet.DO.WalletQuotaApplicationDO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateObjPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateOrderPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationPO;
import com.wantwant.sfa.backend.util.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午9:04
 */
public class WalletFactory {


    public static WantWalletApplicationPO createApplication(WalletQuotaApplicationDO walletQuotaApplicationDO) {
        WantWalletApplicationPO wantWalletApplicationPO = new WantWalletApplicationPO();
        BeanUtils.copyProperties(walletQuotaApplicationDO,wantWalletApplicationPO);
        BeanUtils.copyProperties(walletQuotaApplicationDO.getWalletQuotaApplicationUserDO(),wantWalletApplicationPO);
        wantWalletApplicationPO.setBusinessGroup(walletQuotaApplicationDO.getBusinessGroup());
        wantWalletApplicationPO.setAcceptedMemberKey(walletQuotaApplicationDO.getAcceptedMemberKey());
        wantWalletApplicationPO.setApplyTime(LocalDateTime.now());
        ExpensesAdditionalDO expensesAdditionalDO = walletQuotaApplicationDO.getExpensesAdditionalDO();
        if(Objects.nonNull(expensesAdditionalDO)){
            wantWalletApplicationPO.setExpensesTag(expensesAdditionalDO.getExpensesTag());
        }


        return wantWalletApplicationPO;
    }

    public static WantWalletApplicationAssociateObjPO createAssociateObj(Long applyId, ExpensesAdditionalDO expensesAdditionalDO) {
        WantWalletApplicationAssociateObjPO wantWalletApplicationAssociateObjPO = new WantWalletApplicationAssociateObjPO();
        wantWalletApplicationAssociateObjPO.setType(expensesAdditionalDO.getAssociateType());
        wantWalletApplicationAssociateObjPO.setMemberKey( expensesAdditionalDO.getMemberKey());
        wantWalletApplicationAssociateObjPO.setCustomerId(expensesAdditionalDO.getCustomerId());
        wantWalletApplicationAssociateObjPO.setApplyId(applyId);
        wantWalletApplicationAssociateObjPO.setDeleteFlag(0);
        return wantWalletApplicationAssociateObjPO;
    }

    public static List<WantWalletApplicationAssociateOrderPO> createAssociateOrder(Long associateId, List<String> orderList) {
        List<WantWalletApplicationAssociateOrderPO> list = new ArrayList<>();
        orderList.forEach(e -> {
            WantWalletApplicationAssociateOrderPO wantWalletApplicationAssociateOrderPO = new WantWalletApplicationAssociateOrderPO();
            wantWalletApplicationAssociateOrderPO.setAssociateId(associateId);
            wantWalletApplicationAssociateOrderPO.setOrderNo(e);
            wantWalletApplicationAssociateOrderPO.setDeleteFlag(0);
            list.add(wantWalletApplicationAssociateOrderPO);
        });
        return list;
    }
}
