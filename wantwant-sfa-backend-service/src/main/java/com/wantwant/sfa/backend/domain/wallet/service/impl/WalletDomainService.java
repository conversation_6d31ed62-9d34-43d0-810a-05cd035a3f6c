package com.wantwant.sfa.backend.domain.wallet.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.GoldCoinTransactionGrantDetailRequest;
import com.wantwant.sfa.backend.activityQuota.model.GoldCoinTransactionGrantMemberRequest;
import com.wantwant.sfa.backend.activityQuota.model.GoldCoinTransactionGrantRequest;
import com.wantwant.sfa.backend.activityQuota.model.SPUInfoModel;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.enums.ProcessResultEnum;
import com.wantwant.sfa.backend.domain.wallet.DO.*;
import com.wantwant.sfa.backend.domain.wallet.repository.facade.IWalletApplicationRepository;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationAssociateObjPO;
import com.wantwant.sfa.backend.domain.wallet.repository.po.WantWalletApplicationPO;
import com.wantwant.sfa.backend.domain.wallet.service.IWalletDomainService;
import com.wantwant.sfa.backend.domain.wallet.service.factory.WalletFactory;
import com.wantwant.sfa.backend.entity.CustomerInfo;
import com.wantwant.sfa.backend.gold.enums.TransactionBusinessTypeEnum;
import com.wantwant.sfa.backend.interview.enums.PositionTypeEnum;
import com.wantwant.sfa.backend.mapper.CustomerInfoMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.order.AdsOrderItemDetailMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CalculateUtils;
import com.wantwant.sfa.backend.wallet.dto.*;
import com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity;
import com.wantwant.sfa.backend.wallet.enums.WalletLogTypeEnum;
import com.wantwant.sfa.backend.wallet.request.WalletQuotaApplySearchRequest;
import com.wantwant.sfa.backend.wallet.service.IWalletBigTableService;
import com.wantwant.sfa.backend.wallet.service.IWalletSearchService;
import com.wantwant.sfa.backend.wallet.service.IWalletService;
import com.wantwant.sfa.backend.wallet.service.impl.WalletApplicationService;
import com.wantwant.sfa.backend.wallet.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/20/上午8:49
 */
@Service
@Slf4j
public class WalletDomainService implements IWalletDomainService {

    @Resource
    private IWalletApplicationRepository walletApplicationRepository;
    @Resource
    private IWalletService walletService;
    @Resource
    private WalletApplicationService walletApplicationService;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private IWalletSearchService walletSearchService;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Resource
    private IWalletBigTableService walletBigTableService;
    @Resource
    private AdsOrderItemDetailMapper adsOrderItemDetailMapper;
    @Resource
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private OrganizationMapper organizationMapper;


    @Override
    @Transactional
    public Long apply(WalletQuotaApplicationDO walletQuotaApplicationDO) {
        log.info("【wallet quota apply】DO:{}",walletQuotaApplicationDO);

        Optional<WantWalletApplicationPO> wantWalletApplicationPO = Optional.ofNullable(walletQuotaApplicationDO).map(WalletFactory::createApplication);
        if(!wantWalletApplicationPO.isPresent()){
            throw new ApplicationException("创建额度申请信息数据异常");
        }

        return walletApplicationRepository.save(wantWalletApplicationPO.get());
    }

    @Override
    @Transactional
    public void lock(WalletQuotaLockDO walletQuotaLockDO) {
        log.info("【wallet quota lock】DO:{}",walletQuotaLockDO);
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        Long walletAccount = walletApplicationRepository.findWalletAccount(walletQuotaLockDO.getOrganizationId(), walletQuotaLockDO.getWalletType());
        if(Objects.isNull(walletAccount)){
            throw new ApplicationException("额度不存在");
        }
        walletUsedDTO.setWalletAccountId(walletAccount);
        walletUsedDTO.setInstanceId(walletQuotaLockDO.getInstanceId());
        walletUsedDTO.setWalletTypeId(walletQuotaLockDO.getWalletType());
        walletUsedDTO.setRevenue(walletQuotaLockDO.getRevenue());
        walletUsedDTO.setExpenditure(walletQuotaLockDO.getExpenditure());
        walletUsedDTO.setProcessUserId(walletQuotaLockDO.getProcessUserId());
        walletUsedDTO.setProcessUserName(walletQuotaLockDO.getProcessUserName());
        walletUsedDTO.setSubTypeId(walletQuotaLockDO.getPaymentSpuId());
        walletUsedDTO.setMemberKey(walletQuotaLockDO.getAcceptedMemberKey());
        walletUsedDTO.setQuota(walletQuotaLockDO.getQuota());
        walletUsedDTO.setLocked(true);
        walletService.used(walletUsedDTO);
    }

    @Override
    @Transactional
    public void sendLocked(Long instanceId, ProcessUserDO processUserDO) {
        // 根据实例ID获取到申请记录
        WantWalletApplicationPO wantWalletApplicationPO = walletApplicationRepository.selectApplicationByInstanceId(instanceId);
        if(Objects.isNull(wantWalletApplicationPO)){
            throw new ApplicationException("旺金币申请信息不存在");
        }

        // 获取到锁定的额度信息
        List<WantWalletLogEntity>  logEntities = walletApplicationRepository.selectLockedBy(instanceId);
        if(CollectionUtils.isEmpty(logEntities)){
            throw new ApplicationException("旺金币额度异常");
        }
        Integer applyType = wantWalletApplicationPO.getApplyType();

        // 发放给组织
        if(applyType == 1){
            // 先检查账户是否存在
            Long walletAccount = walletApplicationRepository.findWalletAccount(wantWalletApplicationPO.getAcceptedOrganizationId(), wantWalletApplicationPO.getAcceptedWalletType());
            // 创建账号
            if(Objects.isNull(walletAccount)){
                CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
                createWalletAccountDTO.setProcessUserName(processUserDO.getEmployeeName());
                createWalletAccountDTO.setProcessUserId(processUserDO.getEmployeeId());
                createWalletAccountDTO.setWalletType(wantWalletApplicationPO.getAcceptedWalletType());
                createWalletAccountDTO.setOrganizationId(wantWalletApplicationPO.getAcceptedOrganizationId());
                walletAccount = walletService.createAccount(createWalletAccountDTO);
            }
            WalletAddDTO walletAddDTO = new WalletAddDTO();
            walletAddDTO.setRemark(wantWalletApplicationPO.getRemark());
            walletAddDTO.setWalletAccountId(walletAccount);
            walletAddDTO.setWalletTypeId(wantWalletApplicationPO.getAcceptedWalletType());
            walletAddDTO.setExpenditure(wantWalletApplicationPO.getExpenditure());
            walletAddDTO.setRevenue(wantWalletApplicationPO.getRevenue());

            walletAddDTO.setProcessUserId(processUserDO.getEmployeeId());
            walletAddDTO.setProcessUserName(processUserDO.getEmployeeName());
            List<WalletAddDetailDTO> list = new ArrayList<>();
            logEntities.forEach(e -> {
                WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
                BeanUtils.copyProperties(e,walletAddDetailDTO);
                walletAddDetailDTO.setSubTypeId(wantWalletApplicationPO.getAcceptedSpuId());
                list.add(walletAddDetailDTO);
            });
            walletAddDTO.setDetailDTOList(list);
            // 发放给组织，调用额度增加方法
            walletService.add(walletAddDTO);
        }else{
            // 获取业务编码
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
            // 构建旺铺发放请求
            GoldCoinTransactionGrantRequest goldCoinTransactionGrantRequest = new GoldCoinTransactionGrantRequest();
            List<GoldCoinTransactionGrantMemberRequest> grantMemberList = new ArrayList<>();
            // 构建发放用户集合
            GoldCoinTransactionGrantMemberRequest goldCoinTransactionGrantMemberRequest = new GoldCoinTransactionGrantMemberRequest();
            goldCoinTransactionGrantMemberRequest.setMemberKey(String.valueOf(wantWalletApplicationPO.getAcceptedMemberKey()));
            goldCoinTransactionGrantMemberRequest.setCause(wantWalletApplicationPO.getCostPurpose());
            goldCoinTransactionGrantMemberRequest.setTransactionBusinessType(TransactionBusinessTypeEnum.SFA_BUSINESS_MANAGER_GRANT.getCode());

            goldCoinTransactionGrantMemberRequest.setOpUserId(wantWalletApplicationPO.getApplyEmpId());
            goldCoinTransactionGrantMemberRequest.setOpUserName(wantWalletApplicationPO.getApplyEmpName());


            List<String> orgPath = organizationMapper.selectOrgPath(wantWalletApplicationPO.getExpenditureOrganizationId());
            if(!CollectionUtils.isEmpty(orgPath)){
                String businessGroupName = sfaBusinessGroupEntity.getBusinessGroupName();
                String orgNames = String.join("-", orgPath);
                goldCoinTransactionGrantMemberRequest.setOpUserOrganization(businessGroupName+"-"+orgNames);
            }
            goldCoinTransactionGrantMemberRequest.setOpUserRole(wantWalletApplicationPO.getApplyPositionType());

            // 构建发放明细参数集合
            List<GoldCoinTransactionGrantDetailRequest> grantDetailList = new ArrayList<>();
            logEntities.forEach(e -> {
                GoldCoinTransactionGrantDetailRequest goldCoinTransactionGrantDetailRequest = new GoldCoinTransactionGrantDetailRequest();
                goldCoinTransactionGrantDetailRequest.setAmountSubType(wantWalletApplicationPO.getAcceptedWalletType());
                // 产品组币发放是，amountSubTypeId是产品组编码
                if(wantWalletApplicationPO.getAcceptedWalletType() == 1){
                    goldCoinTransactionGrantDetailRequest.setAmountSubTypeId(sfaBusinessGroupEntity.getBusinessGroupCode());
                }else{
                    goldCoinTransactionGrantDetailRequest.setAmountSubTypeId(wantWalletApplicationPO.getAcceptedSpuId());
                }
                goldCoinTransactionGrantDetailRequest.setAssociatedCode(String.valueOf(e.getLogId()));
                goldCoinTransactionGrantDetailRequest.setDepartmentCode(e.getDeptCode());
                goldCoinTransactionGrantDetailRequest.setApplyType(e.getApplyType());
                goldCoinTransactionGrantDetailRequest.setExpenditureAmountSubType(e.getWalletTypeId());
                Integer walletTypeId = e.getWalletTypeId();
                if(walletTypeId == 2){
                    goldCoinTransactionGrantDetailRequest.setExpenditureAmountSubTypeId(e.getSubTypeId());
                }else{
                    goldCoinTransactionGrantDetailRequest.setExpenditureAmountSubTypeId(sfaBusinessGroupEntity.getBusinessGroupCode());
                }
                goldCoinTransactionGrantDetailRequest.setExpenditureProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());

                goldCoinTransactionGrantDetailRequest.setGrantAmount(e.getQuota());
                goldCoinTransactionGrantDetailRequest.setMarginalCost(e.getBoundary());
                grantDetailList.add(goldCoinTransactionGrantDetailRequest);
            });

            goldCoinTransactionGrantMemberRequest.setGrantDetailList(grantDetailList);
            grantMemberList.add(goldCoinTransactionGrantMemberRequest);
            goldCoinTransactionGrantRequest.setGrantMemberList(grantMemberList);

            walletApplicationService.personalBatch(goldCoinTransactionGrantRequest);
        }


        // 获取锁定的类型，并将锁定类型改为发放类型
        walletService.changeLockedType(instanceId, WalletLogTypeEnum.USED.getCode());
    }

    @Override
    public boolean checkRatioOver(Long instanceId) {
        // 根据实例ID获取到申请记录
        WantWalletApplicationPO wantWalletApplicationPO = walletApplicationRepository.selectApplicationByInstanceId(instanceId);
        if(Objects.isNull(wantWalletApplicationPO)){
            throw new ApplicationException("旺金币申请信息不存在");
        }
        Integer applyType = wantWalletApplicationPO.getApplyType();

        if(applyType == 1){
            return walletSearchService.checkRatioOver(applyType,wantWalletApplicationPO.getQuota(),wantWalletApplicationPO.getAcceptedOrganizationId(),null);
        }else{
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, wantWalletApplicationPO.getAcceptedMemberKey()));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("合伙人获取失败");
            }
            SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                    .eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId())
                    .eq(SfaPositionRelationEntity::getBusinessGroup, wantWalletApplicationPO.getBusinessGroup())
                    .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                    .orderByDesc(SfaPositionRelationEntity::getId)
                    .last("limit 1")
            );
            if(Objects.isNull(sfaPositionRelationEntity)){
                throw new ApplicationException("合伙人岗位表获取失败");
            }

            return walletSearchService.checkRatioOver(applyType,wantWalletApplicationPO.getQuota(),sfaPositionRelationEntity.getDepartmentCode(),Long.valueOf(wantWalletApplicationPO.getAcceptedMemberKey()));
        }

    }

    @Override
    @Transactional
    public void LockRelease(WalletLockReleaseDO walletLockReleaseDO) {
        // 获取到锁定的额度信息
        List<WantWalletLogEntity>  logEntities = walletApplicationRepository.selectLockedBy(walletLockReleaseDO.getInstanceId());



        if(CollectionUtils.isEmpty(logEntities)){
            throw new ApplicationException("无可释放的额度");
        }

        // 账户主表增加额度
        BigDecimal total = logEntities.stream().map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 删除锁
        walletService.changeLockedType(walletLockReleaseDO.getInstanceId(), WalletLogTypeEnum.INCOME.getCode());


        WantWalletLogEntity wantWalletLogEntity = logEntities.stream().findFirst().get();


        walletApplicationRepository.addQuota(wantWalletLogEntity.getWalletAccountId(),wantWalletLogEntity.getWalletTypeId(),total);
    }

    @Override
    public IPage<WalletQuotaApplicationVo> search(WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, List<Integer> roleIds) {

        IPage<WalletQuotaApplicationVo> page = new Page<>(walletQuotaApplySearchRequest.getPage(), walletQuotaApplySearchRequest.getRows());

        walletQuotaApplySearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        List<WalletQuotaApplicationVo> list = walletApplicationRepository.search(page,walletQuotaApplySearchRequest,roleIds);

        if(!CollectionUtils.isEmpty(list)){

            List<SPUInfoModel> spuInfoModels = Optional.ofNullable(activityQuotaConnectorUtil.querySPUInfo()).orElse(new ArrayList<>());


            list.forEach(e -> {
                Integer processResult = e.getProcessResult();
                e.setStatus(ProcessResultEnum.findNameByResult(processResult));
                if(processResult == 0 &&  (walletQuotaApplySearchRequest.getPerson().equals(e.getProcessUserId())
                        || roleIds.stream().filter(f -> f.equals(e.getProcessRoleId())).findFirst().isPresent())){
                    e.setStatus("待审核");
                    e.setAudit(true);
                }

                if(processResult == 0 &&  !walletQuotaApplySearchRequest.getPerson().equals(e.getProcessUserId()) &&  !roleIds.stream().filter(f -> f.equals(e.getProcessRoleId())).findFirst().isPresent()){
                    e.setStatus("审核中");
                }


                String paymentSpuId = e.getPaymentSpuId();
                if(StringUtils.isNotBlank(paymentSpuId)){
                    Optional<SPUInfoModel> spuInfoModelOptional = spuInfoModels.stream().filter(f -> f.getSpuId().equals(paymentSpuId)).findFirst();
                    if(spuInfoModelOptional.isPresent()){
                        SPUInfoModel spuInfoModel = spuInfoModelOptional.get();
                        e.setPaymentSpuName(spuInfoModel.getSpuName());
                    }
                }

                String acceptedSpuId = e.getAcceptedSpuId();
                if(StringUtils.isNotBlank(acceptedSpuId)){
                    Optional<SPUInfoModel> spuInfoModelOptional = spuInfoModels.stream().filter(f -> f.getSpuId().equals(acceptedSpuId)).findFirst();
                    if(spuInfoModelOptional.isPresent()){
                        SPUInfoModel spuInfoModel = spuInfoModelOptional.get();
                        e.setAcceptedSpuName(spuInfoModel.getSpuName());
                    }
                }
            });
        }

        page.setRecords(list);
        return page;
    }

    @Override
    public void saveAnnex(List<WalletAnnexDO> collect) {
        walletApplicationRepository.saveAnnex(collect);
    }

    @Override
    public WantWalletApplicationPO getApplyDetail(Long applyId) {
        WantWalletApplicationPO walletApplicationPO = walletApplicationRepository.selectApplicationById(applyId);
        return walletApplicationPO;
    }

    @Override
    public QuotaInfoVo findQuotaInfoVo(Integer applyType, String acceptedOrganizationId, Long acceptedMemberKey,String customerId, LocalDateTime applyTime) {
        QuotaInfoVo quotaInfoVo = new QuotaInfoVo();

        // 发放给组织
        if(applyType == 1){
            BigDecimal pendingQuota = walletApplicationRepository.searchPendingQuota(applyType,acceptedOrganizationId);
            quotaInfoVo.setPendingQuota(pendingQuota);

//            LocalDate startDate = applyTime.toLocalDate().withMonth(4).withDayOfMonth(1);
//
//            int year = applyTime.toLocalDate().getYear();
//            int monthValue = applyTime.toLocalDate().getMonthValue();
//            if(year == startDate.getYear() && monthValue < 4){
//                startDate = startDate.minusYears(1L);
//            }
//            LocalDate endDate = startDate.plusYears(1L);
            LocalDate startDate = LocalDate.of( applyTime.toLocalDate().getYear(), 1, 1);
            LocalDate endDate = LocalDate.of(startDate.getYear()+1,1,1);

            BigDecimal currentMonthInCome = Optional.ofNullable(walletApplicationRepository.searchOrganizationCurrentMonthIncome(acceptedOrganizationId,applyTime.toLocalDate().toString().substring(0,7))).orElse(BigDecimal.ZERO);
            quotaInfoVo.setSendQuotaCurrentMonth(currentMonthInCome);
            BigDecimal income = Optional.ofNullable(walletApplicationRepository.selectTotalIncome(acceptedOrganizationId,startDate,endDate)).orElse(BigDecimal.ZERO);
            quotaInfoVo.setIncome(income);

            BigDecimal performance = Optional.ofNullable(walletBigTableService.findPerformance(startDate.toString().substring(0, 4), acceptedOrganizationId)).orElse(BigDecimal.ZERO);
            quotaInfoVo.setPerformance(performance);

        }else{
            BigDecimal pendingQuota = walletApplicationRepository.searchPendingQuota(applyType,String.valueOf(acceptedMemberKey));
            quotaInfoVo.setPendingQuota(pendingQuota);
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());

            PartnerQuotaDTO quotaRate = Optional.ofNullable(walletBigTableService.getQuotaRate(acceptedMemberKey,customerId,sfaBusinessGroupEntity.getBusinessGroupCode())).orElse(new PartnerQuotaDTO());

            quotaInfoVo.setCheckQuota(quotaRate.getCheckQuotaAmount());
            quotaInfoVo.setPerformance(quotaRate.getPerformance());
            quotaInfoVo.setIncome(quotaRate.getIncomeQuotaAmount());
            quotaInfoVo.setCheckQuotaRate(Optional.ofNullable(quotaRate.getCheckQuotaRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));

            // 查询本月已发放
            BigDecimal currentMonthIncome = Optional.ofNullable(walletApplicationRepository.searchPersonCurrentMonthIncome(acceptedMemberKey,applyTime.toLocalDate().toString().substring(0,7))).orElse(BigDecimal.ZERO);
            quotaInfoVo.setSendQuotaCurrentMonth(currentMonthIncome);
        }
        return quotaInfoVo;
    }

    @Override
    public WantWalletApplicationPO getApplyDetailByInstanceId(Long instanceId) {
        return walletApplicationRepository.getApplyDetailByInstanceId(instanceId);
    }

    @Override
    public WalletExpenseRateDO calculateExpenseRate(Integer applyType, String organizationId, BigDecimal quota, String acceptedKey,LocalDateTime applyTime) {
        WalletExpenseRateDO walletExpenseRateDO = new WalletExpenseRateDO();

        BigDecimal expenseRate = Optional.ofNullable(walletSearchService.getExpenseRate(applyType, acceptedKey, null, quota)).orElse(BigDecimal.ZERO);
        expenseRate = expenseRate.multiply(BigDecimal.TEN).multiply(BigDecimal.TEN);
        walletExpenseRateDO.setExpenseRate(expenseRate);

        if(applyType == 2){
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, acceptedKey));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("合伙人获取失败");
            }
            SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                    .eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId())
                    .eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                    .orderByDesc(SfaPositionRelationEntity::getId)
                    .last("limit 1")
            );
            if(Objects.isNull(sfaPositionRelationEntity)){
                throw new ApplicationException("合伙人岗位表获取失败");
            }

            acceptedKey = sfaPositionRelationEntity.getDepartmentCode();
        }


        BigDecimal ratioLimit = Optional.ofNullable(walletSearchService.findExpenseRateLimit(applyType,acceptedKey)).orElse(BigDecimal.ZERO);
        walletExpenseRateDO.setLimitExpenseRate(ratioLimit.multiply(new BigDecimal(100)));

        return walletExpenseRateDO;
    }

    @Override
    public String findWalletTypeNameById(Integer paymentWalletType) {
        return walletApplicationRepository.findWalletTypeNameById(paymentWalletType);
    }

    @Override
    public void searchExport(WalletQuotaApplySearchRequest walletQuotaApplySearchRequest, List<Integer> roleIds) {

        walletQuotaApplySearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        List<WalletQuotaApplicationVo> list = Optional.ofNullable(walletApplicationRepository.search(null,walletQuotaApplySearchRequest,roleIds)).orElse(new ArrayList<>());

        if(!CollectionUtils.isEmpty(list)){

            List<SPUInfoModel> spuInfoModels = Optional.ofNullable(activityQuotaConnectorUtil.querySPUInfo()).orElse(new ArrayList<>());


            list.forEach(e -> {
                Integer processResult = e.getProcessResult();
                e.setStatus(ProcessResultEnum.findNameByResult(processResult));
                if(processResult == 0 &&  (walletQuotaApplySearchRequest.getPerson().equals(e.getProcessUserId())
                        || roleIds.stream().filter(f -> f.equals(e.getProcessRoleId())).findFirst().isPresent())){
                    e.setStatus("待审核");
                    e.setAudit(true);
                }

                if(processResult == 0 &&  !walletQuotaApplySearchRequest.getPerson().equals(e.getProcessUserId()) &&  !roleIds.stream().filter(f -> f.equals(e.getProcessRoleId())).findFirst().isPresent()){
                    e.setStatus("审核中");
                }

                e.setPaymentWalletTypeStr(e.getPaymentWalletType());


                String paymentSpuId = e.getPaymentSpuId();
                if(StringUtils.isNotBlank(paymentSpuId)){
                    Optional<SPUInfoModel> spuInfoModelOptional = spuInfoModels.stream().filter(f -> f.getSpuId().equals(paymentSpuId)).findFirst();
                    if(spuInfoModelOptional.isPresent()){
                        SPUInfoModel spuInfoModel = spuInfoModelOptional.get();
                        e.setPaymentSpuName(spuInfoModel.getSpuName());
                        e.setPaymentWalletTypeStr(e.getPaymentWalletType() + "-" + spuInfoModel.getSpuName());
                    }
                }

                e.setAcceptedWalletTypeStr(e.getAcceptedWalletType());
                String acceptedSpuId = e.getAcceptedSpuId();
                if(StringUtils.isNotBlank(acceptedSpuId)){
                    Optional<SPUInfoModel> spuInfoModelOptional = spuInfoModels.stream().filter(f -> f.getSpuId().equals(acceptedSpuId)).findFirst();
                    if(spuInfoModelOptional.isPresent()){
                        SPUInfoModel spuInfoModel = spuInfoModelOptional.get();
                        e.setAcceptedSpuName(spuInfoModel.getSpuName());
                        e.setAcceptedWalletTypeStr(e.getAcceptedWalletType() + "-" + spuInfoModel.getSpuName());
                    }
                }
            });
        }

        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "旺金币审核" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), WalletQuotaApplicationVo.class, list);
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    @Transactional
    public void saveExpensesAdditional(Long applyId, ExpensesAdditionalDO expensesAdditionalDO) {
        Integer expensesTag = expensesAdditionalDO.getExpensesTag();

        if(expensesTag == 3){
            // 保存关联对象
            Long associateId = walletApplicationRepository.saveAssociateObj(WalletFactory.createAssociateObj(applyId, expensesAdditionalDO));

            List<String> orderList = expensesAdditionalDO.getOrderList();
            if(!CollectionUtils.isEmpty(orderList)){
                walletApplicationRepository.saveAssociateOrder(WalletFactory.createAssociateOrder(associateId,orderList));
            }
        }

    }

    @Override
    public List<ApplyHistoryVO> selectApplyHistory(Integer applyType, String acceptedOrganizationId, Long acceptedMemberKey, Long applyId) {
        return walletApplicationRepository.selectApplyHistory(applyType,acceptedOrganizationId,acceptedMemberKey,applyId);
    }

    @Override
    public AssociateObjVO selectAssociateObj(Long applyId) {
        WantWalletApplicationPO wantWalletApplicationPO = walletApplicationRepository.selectApplicationById(applyId);
        Integer applyType = wantWalletApplicationPO.getApplyType();
        // 接收方额度信息
        QuotaInfoVo quotaInfoVo = Optional.ofNullable(this.findQuotaInfoVo(wantWalletApplicationPO.getApplyType(), wantWalletApplicationPO.getAcceptedOrganizationId(), wantWalletApplicationPO.getAcceptedMemberKey(),null, wantWalletApplicationPO.getApplyTime())).orElse(new QuotaInfoVo());
        BigDecimal checkQuota =Optional.ofNullable(quotaInfoVo.getCheckQuota()).orElse(BigDecimal.ZERO);
        if(applyType == 1){
            checkQuota =Optional.ofNullable(quotaInfoVo.getIncome()).orElse(BigDecimal.ZERO);
        }

        // 根据申请ID获取关联对象信息
        WantWalletApplicationAssociateObjPO wantWalletApplicationAssociateObjPO = walletApplicationRepository.selectAssociateObjByApplyId(applyId);
        if(Objects.isNull(wantWalletApplicationAssociateObjPO)){
            return null;
        }
        AssociateObjVO associateObjVO = new AssociateObjVO();
        associateObjVO.setType(wantWalletApplicationAssociateObjPO.getType());
        Long memberKey = wantWalletApplicationAssociateObjPO.getMemberKey();
        String customerId = wantWalletApplicationAssociateObjPO.getCustomerId();
        QuotaInfoVo associateQuotaInfoVo = null;
        if(Objects.nonNull(memberKey)){
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, memberKey).last("limit 1"));
            if(Objects.nonNull(sfaEmployeeInfoModel)){
                associateObjVO.setEmpName(sfaEmployeeInfoModel.getEmployeeName());
            }

            associateQuotaInfoVo = Optional.ofNullable(this.findQuotaInfoVo(2, null,memberKey,null, wantWalletApplicationPO.getApplyTime())).orElse(new QuotaInfoVo());

        }

        else if(StringUtils.isNotBlank(customerId)){
            CustomerInfo customerInfo = customerInfoMapper.selectOne(new LambdaQueryWrapper<CustomerInfo>().eq(CustomerInfo::getCustomerId, customerId).eq(CustomerInfo::getDeleteFlag, 0).last("limit 1"));
            if(Objects.nonNull(customerInfo)){
                associateObjVO.setEmpName(customerInfo.getCustomerName());
            }
            associateQuotaInfoVo = Optional.ofNullable(this.findQuotaInfoVo(2, null,null,customerId, wantWalletApplicationPO.getApplyTime())).orElse(new QuotaInfoVo());
        }else{
            return null;
        }

        if(Objects.nonNull(associateQuotaInfoVo)){
            BigDecimal associatePerformance = Optional.ofNullable(associateQuotaInfoVo.getPerformance()).orElse(BigDecimal.ZERO);
            BigDecimal associateCheckQuota = Optional.ofNullable(associateQuotaInfoVo.getCheckQuota()).orElse(BigDecimal.ZERO);

            associateObjVO.setPerformance(associatePerformance);
            associateObjVO.setCheckQuota(associateCheckQuota);

            associateObjVO.setExpensesRatio(CalculateUtils.ratioPercent(associateCheckQuota,associatePerformance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : associatePerformance,4));

            // 计算总费用率：接收方业绩+关联对象业绩
            BigDecimal totalPerformance =associatePerformance.add(Optional.ofNullable(quotaInfoVo.getPerformance()).orElse(BigDecimal.ZERO));
            // 关联对象核销旺金币+接收方核销旺金币+本次发放
            BigDecimal totalCheckQuota = associateCheckQuota.add(checkQuota).add(Optional.ofNullable(wantWalletApplicationPO.getQuota()).orElse(BigDecimal.ZERO));
            associateObjVO.setTotalExpensesRatio(CalculateUtils.ratioPercent(totalCheckQuota,totalPerformance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : totalPerformance,4));
        }


        List<String> orderList = walletApplicationRepository.selectAssociateOrderList(wantWalletApplicationAssociateObjPO.getAssociateId());
        if(!CollectionUtils.isEmpty(orderList)){
            List<AssociateOrderVO> associateOrderVOList = adsOrderItemDetailMapper.selectByOrderList(orderList,RequestUtils.getBusinessGroup());
            associateObjVO.setAssociateOrderVOList(associateOrderVOList);
        }

        return associateObjVO;
    }

    @Override
    public OrganizationQuotaVO selectOrgYearVO(String acceptedOrganizationId) {
        OrganizationQuotaVO organizationQuotaVO = new OrganizationQuotaVO();
        // 获取组织剩余额度
        BigDecimal surplus = Optional.ofNullable(walletApplicationRepository.selectSurplus(acceptedOrganizationId)).orElse(BigDecimal.ZERO);
        organizationQuotaVO.setTotal(surplus);


        List<YearQuotaVO> yearQuotaVOList = new ArrayList<>();
        // 财年从2023年开始,2023年收入特殊处理
        BigDecimal oldInCome = Optional.ofNullable(walletApplicationRepository.selectOldInCome(acceptedOrganizationId)).orElse(BigDecimal.ZERO);
        LocalDate oldStartDate = LocalDate.parse("2023-12-01");
        LocalDate oldEndDate = oldStartDate.plusMonths(4L);
        BigDecimal oldInCome2 = Optional.ofNullable(walletApplicationRepository.selectTotalIncome(acceptedOrganizationId,oldStartDate.plusDays(1L),oldEndDate)).orElse(BigDecimal.ZERO);
        // 获取财年盘价
        BigDecimal oldPerformance =  Optional.ofNullable(walletBigTableService.selectPerformanceByMonthRange(acceptedOrganizationId,"2023-07","2024-01")).orElse(BigDecimal.ZERO);

        YearQuotaVO oldQuotaVo = new YearQuotaVO();
        oldQuotaVo.setYearMonth("2023");
        oldQuotaVo.setIncome(oldInCome.add(oldInCome2));
        oldQuotaVo.setPerformance(oldPerformance);
        oldQuotaVo.setExpensesRatio(CalculateUtils.ratioPercent(oldQuotaVo.getIncome(),oldPerformance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : oldPerformance,4));
        yearQuotaVOList.add(oldQuotaVo);

        int year = LocalDate.now().getYear();
        for(int i=2024;i <= year; i++){
            LocalDate startDate = LocalDate.parse(i+"-01-01");
            LocalDate endDate = startDate.plusYears(1L);
            BigDecimal income = Optional.ofNullable(walletApplicationRepository.selectTotalIncome(acceptedOrganizationId,startDate,endDate)).orElse(BigDecimal.ZERO);
            BigDecimal performance = Optional.ofNullable(walletBigTableService.findPerformance(i + "", acceptedOrganizationId)).orElse(BigDecimal.ZERO);
            YearQuotaVO yearQuotaVO = new YearQuotaVO();
            yearQuotaVO.setYearMonth(i+"");
            yearQuotaVO.setIncome(income);
            yearQuotaVO.setPerformance(performance);
            yearQuotaVO.setExpensesRatio(CalculateUtils.ratioPercent(yearQuotaVO.getIncome(),performance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : performance,4));
            yearQuotaVOList.add(yearQuotaVO);
        }

        // 增加合计
        if(!CollectionUtils.isEmpty(yearQuotaVOList)){
            BigDecimal totalIncome = yearQuotaVOList.stream().filter(f -> Objects.nonNull(f.getIncome())).map(YearQuotaVO::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalPerformance = yearQuotaVOList.stream().filter(f -> Objects.nonNull(f.getPerformance())).map(YearQuotaVO::getPerformance).reduce(BigDecimal.ZERO, BigDecimal::add);
            YearQuotaVO yearQuotaVO = new YearQuotaVO();
            yearQuotaVO.setYearMonth("合计");
            yearQuotaVO.setIncome(totalIncome);
            yearQuotaVO.setPerformance(totalPerformance);
            yearQuotaVO.setExpensesRatio(CalculateUtils.ratioPercent(yearQuotaVO.getIncome(),totalPerformance.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : totalPerformance,4));
            yearQuotaVOList.add(yearQuotaVO);

        }

        organizationQuotaVO.setYearQuotaVOList(yearQuotaVOList);

        return organizationQuotaVO;
    }

    @Override
    public boolean checkByPassHierarchy(Long instanceId) {

        // 根据实例ID获取到申请记录
        WantWalletApplicationPO wantWalletApplicationPO = walletApplicationRepository.selectApplicationByInstanceId(instanceId);
        if(Objects.isNull(wantWalletApplicationPO)){
            throw new ApplicationException("旺金币申请信息不存在");
        }


        return wantWalletApplicationPO.isBypassHierarchy();
    }


}
