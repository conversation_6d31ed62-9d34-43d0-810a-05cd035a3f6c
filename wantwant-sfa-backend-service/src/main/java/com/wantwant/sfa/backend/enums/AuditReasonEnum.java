package com.wantwant.sfa.backend.enums;

import lombok.Getter;

/**
 * 审核原因枚举
 */
@Getter
public enum AuditReasonEnum {
    
    NOT_DISPLAYED(2, "未陈列"),
    POOR_DISPLAY(3, "陈列差"),
    REPHOTOGRAPHED(4, "翻拍照片"),
    OTHER(6, "其他"),
    NONE(0, "无");

    private final Integer code;
    private final String description;

    AuditReasonEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据原因描述获取枚举
     */
    public static AuditReasonEnum getByDescription(String description) {
        if (description == null) {
            return NONE;
        }
        for (AuditReasonEnum reason : values()) {
            if (reason.getDescription().equals(description)) {
                return reason;
            }
        }
        return NONE;
    }

    /**
     * 根据原因代码获取枚举
     */
    public static AuditReasonEnum getByCode(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (AuditReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason;
            }
        }
        return NONE;
    }
} 