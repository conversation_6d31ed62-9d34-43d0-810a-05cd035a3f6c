package com.wantwant.sfa.backend.enums;

import com.wantwant.sfa.common.architecture.SpringContextHelper;
import com.wantwant.sfa.common.architecture.global.LocalizedText;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum CompleteExcelEnum implements ExcelBaseEnum {
    COMPLETE_EXPORT_DATE("sfa.backend.complete.export.date", "日期"),
    COMPLETE_AREA_NAME("sfa.backend.complete.area.name", "战区"),
    COMPLETE_VAREA_NAME("sfa.backend.complete.varea.name", "大区"),
    COMPLETE_PROVINCE_NAME("sfa.backend.complete.province.name", "省区"),
    COMPLETE_COMPANY_NAME("sfa.backend.complete.company.name", "分公司"),
    COMPLETE_DEPARTMENT_NAME("sfa.backend.complete.department.name", "营业所"),
    COMPLETE_POSITION_NAME("sfa.backend.complete.position.name", "岗位"),
    COMPLETE_EMPLOYEE_NAME("sfa.backend.complete.employee.name", "姓名"),
    COMPLETE_ONBOARD_TIME("sfa.backend.complete.onboard.time", "入职日期"),
    COMPLETE_COMPLETE_NUM("sfa.backend.complete.complete.num", "通关编号"),
    COMPLETE_COMPLETE_TIME_PERIOD("sfa.backend.complete.complete.time.period", "通关时间段"),
    COMPLETE_COMPLETE_TIME("sfa.backend.complete.complete.time", "业务打卡时间"),
    COMPLETE_COMPLETE_STATUS_NAME("sfa.backend.complete.complete.status.name", "通关状态"),
    COMPLETE_EMPLOYEE_STATUS_NAME("sfa.backend.complete.employee.status.name", "当时人员状态"),
    COMPLETE_AUDIT_STATUS_NAME("sfa.backend.complete.audit.status.name", "稽核状态"),
    COMPLETE_REASON("sfa.backend.complete.reason", "稽核异常原因"),
    COMPLETE_AUDIT_EMPLOYEE_NAME("sfa.backend.complete.audit.employee.name", "稽核人"),
    COMPLETE_BUSINESS_GROUP_NAME("sfa.backend.complete.business.group.name", "组别")
    ;
    private final String type;
    private final String desc;

    CompleteExcelEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getTextMsg() {
        try {
            return SpringContextHelper.getBean(LocalizedText.class).getValue(this.getType(),this.getDesc());
        } catch (Exception e) {
            log.error("获取国际化文本信息异常,{},{}",getType(),getDesc(),e);
            return this.getDesc();
        }
    }
}
