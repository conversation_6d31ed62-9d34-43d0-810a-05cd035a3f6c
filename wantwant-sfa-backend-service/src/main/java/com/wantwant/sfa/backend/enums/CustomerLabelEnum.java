package com.wantwant.sfa.backend.enums;

import com.wantwant.sfa.backend.annotation.Exclude;

public enum CustomerLabelEnum {
    /**
     * 已废弃
     */
    @Exclude
    OLDER("多笔用户","产生多笔下单"),
    @Exclude
    ORDERIN45("45天购买","不满足其他条件但45天内有下单"),
    @Exclude
    ORDEROVER45("45天无购买","不满足其他条件但45天内无下单"),
    @Exclude
    ACTIVELOST("活跃流失","活跃客户流失"),
    @Exclude
    LOYALTYLOST("忠诚流失","忠诚客户流失"),

    /**
     * 在使用
     */
    POTENTIAL("潜在客户","未开户成功或没有开户意向"),
    UNORDERED("未下单用户","开户但未下单"),
    NEWER("新用户","首单在本周期内下单的用户（不限金额）"),
    ACTIVE("活跃用户","统计日之前的两个周期内，每个周期下过一次订单"),
    LOYALTY("忠诚用户","统计日之前的三个周期内，每个周期下过一次订单。"),
    LOST ("流失用户","统计日之前的两个周期内，每个周期都未曾下过单的用户。"),
    SLEEP("其他沉睡","上个周期下过单，本个周期未下单。"),
    CALLBACK("召回用户","本周期内曾下过单的非忠诚非活跃非首单用户。"),
    ACTIVE_SLEEP("活跃沉睡",""),
    LOYALTY_SLEEP("忠诚沉睡",""),


    @Exclude
    OTHER("其他","都不符合任何标签，一般情况不会出现，仅供测试");



    private String info;
    private String reason;


    CustomerLabelEnum(String info,String reason){
        this.info = info;
        this.reason = reason;
    }


    public String getCode() {
        return this.name();
    }

    public String getReason(){
        return this.reason;
    }

    public String getInfo(){
        return this.info;
    }
}
