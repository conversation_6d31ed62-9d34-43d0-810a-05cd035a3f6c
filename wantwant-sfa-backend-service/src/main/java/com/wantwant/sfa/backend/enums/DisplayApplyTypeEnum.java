package com.wantwant.sfa.backend.enums;

import com.wantwant.commons.ex.ApplicationException;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/03/下午4:14
 */
public enum DisplayApplyTypeEnum {

    DISPLAY(1,"特陈额度"),
    DEPARTMENT_TYPE(10,"营业所旺金币"),
    COMPANY_TYPE(20,"分公司旺金币"),
    PROVINCE_TYPE(21,"省区旺金币"),
    VARE_TYPE(22,"大区旺金币"),
    AREA_TYPE(30,"战区旺金币");

    private int type;

    private String name;

    public static Integer findQuotaType(Integer ruleType, String orgType) {
        // 有规则默认特陈额度
        if(ruleType == 1){
            return DISPLAY.type;
        }
        // 0:合伙人申请,1:区域经理申请,2:区域总监申请,3:业务BD申请
        switch (orgType){
            case "area": return AREA_TYPE.type;
            case "varea": return VARE_TYPE.type;
            case "province": return PROVINCE_TYPE.type;
            case "company": return COMPANY_TYPE.type;
            case "department": return DEPARTMENT_TYPE.type;
            default: throw new ApplicationException("不支持的组织类型");
        }
    }

    public static DisplayApplyTypeEnum findQuota(Integer quotaType) {
        Optional<DisplayApplyTypeEnum> first = Arrays.asList(DisplayApplyTypeEnum.values()).stream().filter(f -> f.getType() == quotaType).findFirst();
        if(first.isPresent()){
            return first.get();
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    DisplayApplyTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
