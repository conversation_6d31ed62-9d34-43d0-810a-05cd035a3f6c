package com.wantwant.sfa.backend.enums;

public enum IndexReplayEnum {
  SALE("saleGoal_achievement_rate","业绩目标达成率", "saleGoalAchievementRate", "低于等于"),//小于 或者 等于
  AVGGMV("management_avg_gmv","管理岗人均业绩", "avgGmv", "低于"), //小于
  ONEREACH("personal_expenditure_rate","用人费用率", "oneReach", "高于等于"), //大于或者 等于
  FIVREACH("partner_off_rate","合伙人离职率", "FivReach", "高于"), //大于
  //TWOREACH("","总监在岗率", "twoReach", "高于"), //大于
  THRREACH("city_manager__on_job_rate","区域经理在岗率", "thrReach", "低于"), //小于
  FOURREACH("partner_onboard_rate","合伙人在岗率", "fouReach", "低于"); //小于


  IndexReplayEnum(String indicatorsFile, String indicatorsName, String fieldName, String symbolType) {
    this.indicatorsFile = indicatorsFile;
    this.indicatorsName = indicatorsName;
    this.fieldName = fieldName;
    this.symbolType = symbolType;
  }

  private String indicatorsFile;//指标字段名称
  private String indicatorsName;//指标名称
  private String fieldName;//字段名称
  private String symbolType;//颜色类型



  public static String getIndicatorsNameByIndicatorsFile(String indicatorsFile) {
    for (IndexReplayEnum value : IndexReplayEnum.values()) {
      if (value.indicatorsFile.equals(indicatorsFile)) {
        return value.indicatorsName;
      }
    }
    return null;
  }

  public static String getFieldNameByIndicatorsFile(String indicatorsFile) {
    for (IndexReplayEnum value : IndexReplayEnum.values()) {
      if (value.indicatorsFile.equals(indicatorsFile)) {
        return value.fieldName;
      }
    }
    return null;
  }

  public static String getSymbolByIndicatorsFile(String indicatorsFile) {
    for (IndexReplayEnum value : IndexReplayEnum.values()) {
      if (value.indicatorsFile.equals(indicatorsFile)) {
        return value.symbolType;
      }
    }
    return null;
  }


}
