package com.wantwant.sfa.backend.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.exam.service.IExamNotifyService;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.model.ExamNotifyModel;
import com.wantwant.sfa.backend.notify.model.TrainPenaltyModel;
import com.wantwant.sfa.backend.notify.template.impl.ExamNotifyContent;
import com.wantwant.sfa.backend.notify.template.impl.TrainPenaltyNotifyContent;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/05/上午9:20
 */
@Service
public class ExamNotifyService implements IExamNotifyService {

    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyContentMapper notifyContentMapper;

    private static final String CONTENT = "您好，【{0}】，需参加人员清单如下：";

    private static final String TITLE = "造旺学院【{0}】{1}通知";

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void sendMessage(Long memberKey, String title, String remark) {
        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", memberKey));
        if(Objects.isNull(sfaCustomer)){
            throw new ApplicationException("客户信息不存在");
        }
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectOne(new QueryWrapper<ApplyMemberPo>().eq("user_mobile", sfaCustomer.getMobileNumber()).orderByDesc("id").last("limit 1"));
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applyMemberPo.getId()));
        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("员工信息获取失败");
        }


        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", sfaCustomer.getPositionId()).eq("channel", 3));
        CeoBusinessOrganizationPositionRelation companyPosition = null;
        CeoBusinessOrganizationPositionRelation areaPosition = null;

        String areaName = StringUtils.EMPTY;
        String companyName = StringUtils.EMPTY;
        String departmentName = StringUtils.EMPTY;
        // 合伙人获取上上及
        if(ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 3){
            departmentName = organizationMapper.getOrganizationName(ceoBusinessOrganizationPositionRelation.getOrganizationParentId());
            // 分公司CODE
            String companyCode = organizationMapper.getOrganizationParentId(ceoBusinessOrganizationPositionRelation.getOrganizationParentId());
            companyName = organizationMapper.getOrganizationName(companyCode);
            companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",companyCode).eq("channel",3));
            String areaCode = organizationMapper.getOrganizationParentId(companyCode);
            areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",areaCode).eq("channel",3));
            areaName = organizationMapper.getOrganizationName(areaCode);
        }
        // 区域经理获取上级
        else if(ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 10){
            departmentName = organizationMapper.getOrganizationName(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            // 分公司CODE
            String companyCode = organizationMapper.getOrganizationParentId(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            companyName = organizationMapper.getOrganizationName(companyCode);
            companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",companyCode).eq("channel",3));
            String areaCode = organizationMapper.getOrganizationParentId(companyCode);
            areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",areaCode).eq("channel",3));
            areaName = organizationMapper.getOrganizationName(areaCode);
        }
        // 分公司总监
        else if(ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 2){
            companyName = organizationMapper.getOrganizationName(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            // 分公司CODE
            String areaCode = organizationMapper.getOrganizationParentId(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",areaCode).eq("channel",3));
            areaName = organizationMapper.getOrganizationName(areaCode);
        }else{
            throw new ApplicationException("暂不支持该合伙人发送通知");
        }

        if((Objects.isNull(companyPosition) || StringUtils.isBlank(companyPosition.getEmployeeId())) && (Objects.isNull(areaPosition) || StringUtils.isBlank(areaPosition.getEmployeeId()))){
            throw new ApplicationException("无可发送消息对象");
        }

        ExamNotifyModel model = new ExamNotifyModel();
        model.setDepartmentName(departmentName);
        model.setCompanyName(companyName);
        model.setAreaName(areaName);
        model.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        model.setMobile(sfaCustomer.getMobileNumber());
        model.setStatus(EmployeeStatus.findNameByType(sfaEmployeeInfoModel.getEmployeeStatus()));
        model.setPosition(PositionEnum.getPositionName(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition()));
        String employeeId = Objects.nonNull(companyPosition) && StringUtils.isNotBlank(companyPosition.getEmployeeId()) ? companyPosition.getEmployeeId() : areaPosition.getEmployeeId();
        model.setEmpId(employeeId);
        model.setRemark(remark);
        doSendMessage(model,title,MessageFormat.format(CONTENT,title));
    }


    private void doSendMessage(ExamNotifyModel model,String title,String content) {

        String localDate = LocalDate.now().toString();


        title = MessageFormat.format(TITLE, title, localDate);

        NotifyPO notifyPO = notifyService.saveNotify(2, 10, title, model.getEmpId(), content);

        ExamNotifyContent examNotifyContent = new ExamNotifyContent();

        model.setTemplateId(notifyPO.getTemplateId());
        NotifyContentEntity notifyContentEntity = examNotifyContent.buildNotifyContent(model);

        notifyContentMapper.insert(notifyContentEntity);
    }
}
