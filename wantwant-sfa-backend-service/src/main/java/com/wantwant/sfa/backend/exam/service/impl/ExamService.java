package com.wantwant.sfa.backend.exam.service.impl;

import com.wantwant.sfa.backend.exam.request.ExamNotifyRequest;
import com.wantwant.sfa.backend.exam.service.IExamNotifyService;
import com.wantwant.sfa.backend.exam.service.IExamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/05/上午9:16
 */
@Service
public class ExamService implements IExamService {

    @Autowired
    private IExamNotifyService examNotifyService;

    private String errMessage = "培训通知,memberKey:{0},错误信息{1}";

    @Override
    @Transactional
    public List<String> examNotify(ExamNotifyRequest request) {

        List<String> errMsg = new ArrayList<>();

        List<Long> memberKeyList = request.getMemberKeyList();
        memberKeyList.forEach(e -> {
            try{
                examNotifyService.sendMessage(e,request.getTitle(),request.getRemark());
            }catch(Exception ex){
                MessageFormat.format(errMessage,e.toString(),ex.getMessage());
            }
        });

        return errMsg;
    }
}
