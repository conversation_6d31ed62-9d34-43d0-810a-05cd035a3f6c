package com.wantwant.sfa.backend.gold.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午3:07
 */
@Data
public class GoldImportDto {
    @Excel(name = "标题")
    private String title;
    @Excel(name = "费用归属开始月份")
    private String startMonth;
    @Excel(name = "费用归属结束月份")
    private String endMonth;
    @Excel(name = "费用承担部门")
    private String deptName;
    @Excel(name = "业务员名称")
    private String ceoName;
    @Excel(name = "战区")
    private String areaName;
    @Excel(name = "大区")
    private String vareaName;
    @Excel(name = "省区")
    private String provinceName;
    @Excel(name = "分公司")
    private String companyName;
    @Excel(name = "营业所")
    private String branchName;
    @Excel(name = "业务员手机号")
    private String mobile;
    @Excel(name = "费用类型")
    private String expensesType;
    @Excel(name = "金额")
    private String amount;
    @Excel(name = "费用率(%)")
    private String expensesRate;
    @Excel(name = "边际定义(1.边际上 2.边际下)")
    private String boundary;
    @Excel(name = "spu支出")
    private String spuExpenditure;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "币种子类")
    private String coinsSubTypeName;

}
