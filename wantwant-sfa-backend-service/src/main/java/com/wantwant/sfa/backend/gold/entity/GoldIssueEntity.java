package com.wantwant.sfa.backend.gold.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@TableName("gold_issue")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GoldIssueEntity {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @TableField("the_year_mon")
    private String  theYearMon;

    @TableField("region_name")
    private  String regionName;

    @TableField("region_organization_id")
    private  String regionOrganizationId;

    @TableField("branch_name")
    private  String branchName;

    @TableField("branch_organization_id")
    private  String branchOrganizationId;

    @TableField("position_type_id")
    private  String positionTypeId;

    @TableField("employee_name")
    private  String employeeName;

    @TableField("onboard_time")
    private Date onboardTime;

    @TableField("display_limit")
    private  String displayLimit;

    @TableField("display_issue")
    private  String displayIssue;

    @TableField("display_occupied")
    private  String displayOccupied;

    @TableField("display_residue")
    private  String displayResidue;

    @TableField("display_expense_ratio")
    private  String displayExpenseRatio;

    @TableField("refund_limit")
    private  String refundLimit;

    @TableField("refund_issue")
    private  String refundIssue;

    @TableField("refund_reissue")
    private  String refundReissue;

    @TableField("refund_residue")
    private  String refundResidue;

}
