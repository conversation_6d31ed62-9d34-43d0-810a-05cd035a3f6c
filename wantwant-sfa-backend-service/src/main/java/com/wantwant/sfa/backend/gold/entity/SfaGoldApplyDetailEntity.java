package com.wantwant.sfa.backend.gold.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午4:22
 */
@Data
@TableName("sfa_gold_apply_detail")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SfaGoldApplyDetailEntity {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @TableField("batch_id")
    private Long batchId;

    @TableField("title")
    private String title;

    @TableField("month")
    private String month;

    @TableField("dept_name")
    private String deptName;

    @TableField("gold_type")
    private Integer goldType;

    @TableField("expenses_type")
    private String expenseType;

    @TableField("expenses_rate")
    private BigDecimal expensesRate;

    @TableField("position_id")
    private String positionId;

    @TableField("amount")
    private BigDecimal amount;

    @TableField("is_delete")
    private Integer isDelete;

    @TableField("remark")
    private String remark;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @TableField("err_msg")
    private String errMsg;

    @TableField("status")
    private Integer status;

    @TableField("start_month")
    private String startMonth;

    @TableField("end_month")
    private String endMonth;

    @TableField("boundary")
    private Integer boundary;

    @TableField("coins_type")
    private int coinsType;

    @TableField("coins_type_sub_id")
    private String coinsTypeSubId;

    @TableField("coins_type_sub_name")
    private String coinsTypeSubName;

    @TableField("member_key")
    private Long memberKey;

    @TableField("spu_expenditure")
    private String spuExpenditure;


    @TableField("spu_expenditure_ids")
    private String spuExpenditureIds;
}
