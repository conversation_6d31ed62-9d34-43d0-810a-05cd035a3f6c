package com.wantwant.sfa.backend.gold.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午3:38
 */
@Data
@TableName("sfa_gold_apply")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SfaGoldApplyEntity {
    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @TableField("month")
    private String month;

    @TableField("start_month")
    private String startMonth;

    @TableField("end_month")
    private String endMonth;

    @TableField("apply_type")
    private Integer applyType;

    @TableField("dept_name")
    private String deptName;

    @TableField("gold_type")
    private Integer goldType;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("create_user_name")
    private String createUserName;

    @TableField("create_user_dept_name")
    private String createUserDeptName;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("is_delete")
    private Integer isDelete;

    @TableField("import_type")
    private Integer importType;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("title")
    private String title;

    @TableField("business_group")
    private int businessGroup;

    @TableField("boundary")
    private int boundary;

    @TableField("coins_type")
    private int coinsType;
}
