package com.wantwant.sfa.backend.gold.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午4:47
 */
@Data
@TableName("sfa_gold_process")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SfaGoldProcessEntity {
    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @TableField("batch_id")
    private Long batchId;

    @TableField("process_type")
    private Integer processType;

    @TableField("process_record_id")
    private Long processRecordId;

    @TableField("process_result")
    private Integer ProcessResult;
}
