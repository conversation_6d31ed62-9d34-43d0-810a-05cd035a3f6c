package com.wantwant.sfa.backend.gold.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午4:55
 */
@Data
@TableName("sfa_gold_process_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SfaGoldProcessRecordEntity {
    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @TableField("process_user_id")
    private String processUserId;

    @TableField("process_id")
    private Long processId;

    @TableField("process_time")
    private LocalDateTime processTime;

    @TableField("process_type")
    private Integer processType;

    @TableField("process_result")
    private Integer processResult;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("prev_record_id")
    private Long prevRecordId;

    @TableField("next_record_id")
    private Long nextRecordId;

    @TableField("remark")
    private String remark;
}
