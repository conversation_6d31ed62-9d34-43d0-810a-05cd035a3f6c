package com.wantwant.sfa.backend.gold.enums;

import com.wantwant.sfa.backend.interview.enums.ProcessType;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午2:55
 */
public enum GoldProcessEnum {
    APPLY(1,"申请"),
    FINANCE(2,"财务审核"),
    FINAL(3,"最终审核"),
    UNDEFINED(-1,"未知");

    private Integer type;

    private String name;

    public static GoldProcessEnum findNext(int type, boolean jumpFinance){
        type ++;
        if(type == FINANCE.getType().intValue() && jumpFinance) {//当前非财务审核且需要跳过财务审核
            type ++;
        }
        int currentStep = 0;

        for(GoldProcessEnum goldProcessEnum: GoldProcessEnum.values()){
            if(goldProcessEnum.getType() == type){
                return goldProcessEnum;
            }
        }

        return UNDEFINED;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    GoldProcessEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
