package com.wantwant.sfa.backend.gold.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午2:57
 */
public enum GoldProcessResultEnum {
    PROCESS(0,"待处理"),
    PASS(1,"审核通过"),
    FAIL(2,"驳回");

    private Integer status;

    private String name;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    GoldProcessResultEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }
}
