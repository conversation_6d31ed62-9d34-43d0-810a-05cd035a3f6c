package com.wantwant.sfa.backend.gold.enums;

import lombok.Getter;

/**
 * 旺金币交易业务场景类型枚举
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Getter
public enum TransactionBusinessTypeEnum {

    /**
     * SFA-总部发放
     */
    SFA_HEAD_OFFICE_GRANT(12, "SFA-总部发放"),

    /**
     * SFA-业务主管发放
     */
    SFA_BUSINESS_MANAGER_GRANT(13, "SFA-业务主管发放"),

    /**
     * SFA-特陈活动费用发放
     */
    SFA_SPECIAL_ACTIVITY_GRANT(14, "SFA-特陈活动费用发放"),

    /**
     * SFA-条码费发放
     */
    SFA_BARCODE_FEE_GRANT(15, "SFA-条码费发放"),

    /**
     * SFA-售后补贴发放
     */
    SFA_AFTER_SALES_SUBSIDY_GRANT(16, "SFA-售后补贴发放"),

    /**
     * SFA-其它发放
     */
    SFA_OTHER_GRANT(17, "SFA-其它发放");

    /**
     * 业务场景编码
     */
    private final Integer code;

    /**
     * 业务场景描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code        业务场景编码
     * @param description 业务场景描述
     */
    TransactionBusinessTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举值
     *
     * @param code 业务场景编码
     * @return 对应的枚举值，如果没有找到则返回null
     */
    public static TransactionBusinessTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransactionBusinessTypeEnum typeEnum : TransactionBusinessTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 检查编码是否有效
     *
     * @param code 业务场景编码
     * @return 如果编码有效返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 使用示例：
     * <pre>
     * // 设置业务场景
     * GoldCoinTransactionGrantMemberRequest request = new GoldCoinTransactionGrantMemberRequest();
     * request.setTransactionBusinessTypeEnum(TransactionBusinessTypeEnum.SFA_HEAD_OFFICE_GRANT);
     * 
     * // 获取业务场景描述
     * TransactionBusinessTypeEnum typeEnum = TransactionBusinessTypeEnum.getByCode(12);
     * String description = typeEnum.getDescription(); // "SFA-总部发放"
     * 
     * // 验证编码有效性
     * boolean isValid = TransactionBusinessTypeEnum.isValidCode(12); // true
     * </pre>
     */
}
