package com.wantwant.sfa.backend.gold.model;

import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyDetailEntity;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/18/上午11:47
 */
@Data
public class GoldImportModel {

    private SfaGoldApplyDetailEntity detailEntity;

    private CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation;

    private Integer applyType;

    private BigDecimal quota;

    private String remark;

    private String deptCode;

    private String deptName;

    private String businessGroupCode;

    private String yearMonthStart;

    private String yearMonthEnd;

    private Integer marginalCost;

    private Integer amountSubType;

    private String amountSubTypeId;

    private String coinsTypeSubName;

    private Long memberKey;

    private String positionId;

    private String processUserName;

}
