package com.wantwant.sfa.backend.gold.service;

import com.wantwant.sfa.backend.gold.request.GoldApplyDetailUpdateRequest;
import com.wantwant.sfa.backend.gold.request.GoldApprovalRequest;
import com.wantwant.sfa.backend.gold.vo.GoldImportResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午3:27
 */
public interface IGoldService {
    /**
     * 旺金币导入
     *
     * @param file
     * @param person
     */
    List<String> upload(MultipartFile file,int type, String person);


    /**
     * 审核通过
     *
     * @param request
     */
    GoldImportResult approval(GoldApprovalRequest request);

    /**
     * 驳回
     *
     * @param request
     */
    void dismissed(GoldApprovalRequest request);

    /**
     * 删除明细
     *
     * @param applyDetailId
     */
    void deleteDetailById(Long applyDetailId);
}
