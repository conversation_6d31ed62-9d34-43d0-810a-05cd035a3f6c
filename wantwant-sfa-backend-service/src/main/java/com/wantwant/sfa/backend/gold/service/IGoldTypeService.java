package com.wantwant.sfa.backend.gold.service;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.gold.request.GoldCumulativeRequest;
import com.wantwant.sfa.backend.gold.vo.ExpenseVo;
import com.wantwant.sfa.backend.gold.vo.GoldCumulativeVo;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午3:28
 */
public interface IGoldTypeService {
    /**
     * 根据费用类型获取金币类型
     *
     * @param expensesType
     * @return
     */
    Integer selectGoldTypeByExpensesType(String expensesType);


    Integer selectExpensesTypeCodeByExpensesType(String expensesType);

    List<ExpenseVo> selectExpenseTypeByCoins(Integer coinsType);

    List<GoldCumulativeVo> selectGoldTypeService(GoldCumulativeRequest request);

    void exportCumulative(GoldCumulativeRequest request);
}
