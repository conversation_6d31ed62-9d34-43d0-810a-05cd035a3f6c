package com.wantwant.sfa.backend.gold.service.impl;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessEnum;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.gold.service.process.*;
import com.wantwant.sfa.backend.gold.vo.GoldImportErrorEmpVo;
import com.wantwant.sfa.backend.gold.vo.GoldImportResult;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldApplyDetailMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldApplyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午5:51
 */
@Component
@Slf4j
public class GoldProcessComponent {
    @Autowired
    private GoldPassProcess goldPassProcess;
    @Autowired
    private GoldDismissedProcess goldDismissedProcess;
    @Autowired
    private GoldReApplyProcess goldReApplyProcess;
    @Autowired
    private GoldFinalApplyProcess goldFinalApplyProcess;
    @Autowired
    private GoldActivityFinalApplyProcess goldActivityFinalApplyProcess;
    @Autowired
    private SfaGoldApplyMapper sfaGoldApplyMapper;
    @Autowired
    private SfaGoldApplyDetailMapper sfaGoldApplyDetailMapper;

    private static final Integer PASS_PROCESS = 1;
    private static final Integer DISMISSED_PROCESS = 2;
    private static final Integer RE_APPLY_PROCESS = 3;
    private static final Integer FINAL_PROCESS = 4;
    private static final Integer GOLD_ACTIVITZy_FINAL_APPLY_PROCESS = 5;

    private String messageTitle = "共计处理{0}条 已成功{1}条 未成功{2}条 ";

    private Map<Integer, Consumer> processMap = new HashMap<>();

    @PostConstruct
    public void init(){
        processMap.put(PASS_PROCESS,goldPassProcess);
        processMap.put(DISMISSED_PROCESS,goldDismissedProcess);
        processMap.put(RE_APPLY_PROCESS,goldReApplyProcess);
        processMap.put(FINAL_PROCESS,goldFinalApplyProcess);
        processMap.put(GOLD_ACTIVITZy_FINAL_APPLY_PROCESS,goldActivityFinalApplyProcess);
    }


    public GoldImportResult dispatch(GoldProcessDto dto){

        log.info("【旺金币审核请求分发】dto:{}",dto);

        if(dto.getResult() == GoldProcessResultEnum.FAIL.getStatus()){
            processMap.get(DISMISSED_PROCESS).accept(dto);
        }

        // 通过并且不是最终审核
        else if(dto.getResult() == GoldProcessResultEnum.PASS.getStatus()
                && dto.getProcessType() != GoldProcessEnum.FINAL.getType() && !dto.isReApply()){
            processMap.get(PASS_PROCESS).accept(dto);
        }

        // 驳回后重新提交
        else if(dto.isReApply()){
            processMap.get(RE_APPLY_PROCESS).accept(dto);
        }

        else if(dto.getResult() == GoldProcessResultEnum.PASS.getStatus()
                && dto.getProcessType() == GoldProcessEnum.FINAL.getType() && !dto.isReApply()){
            Long appId = dto.getAppId();

            SfaGoldApplyEntity sfaGoldApplyEntity = sfaGoldApplyMapper.selectById(appId);
            if(Objects.isNull(sfaGoldApplyEntity)){
                throw new ApplicationException("申请信息未找到");
            }
            Integer goldType = sfaGoldApplyEntity.getGoldType();
            if(goldType == 1){
                processMap.get(FINAL_PROCESS).accept(dto);
            }else if(goldType == 0){
                processMap.get(GOLD_ACTIVITZy_FINAL_APPLY_PROCESS).accept(dto);
            }else{
                throw new ApplicationException("未知流程");
            }


            GoldImportResult result = new GoldImportResult();
            // 成功条目数
            int successCount = sfaGoldApplyDetailMapper.selectSuccessCount(dto.getAppId(),1);
            // 失败条数
            int errorCount = sfaGoldApplyDetailMapper.selectSuccessCount(dto.getAppId(),2);

            String title = MessageFormat.format(messageTitle,successCount+errorCount,successCount,errorCount);
            if(errorCount > 0){
                // 失败条目数
                List<GoldImportErrorEmpVo> errList = sfaGoldApplyDetailMapper.selectErrorInfo(dto.getAppId());
                result.setSubTitle("已将失败信息告知导入人员");
                result.setErrList(errList);
            }
            result.setTitle(title);


            return result;


        }

        else{
            throw new ApplicationException("未知流程");
        }

        return null;
    }
}
