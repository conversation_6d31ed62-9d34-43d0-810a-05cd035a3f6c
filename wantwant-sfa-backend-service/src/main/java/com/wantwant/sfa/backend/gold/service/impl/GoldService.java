package com.wantwant.sfa.backend.gold.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.SPUInfoModel;
import com.wantwant.sfa.backend.activityQuota.service.ICostTypeService;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.gold.dto.GoldImportDto;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyDetailEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessRecordEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessEnum;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.gold.request.GoldApplyDetailUpdateRequest;
import com.wantwant.sfa.backend.gold.request.GoldApprovalRequest;
import com.wantwant.sfa.backend.gold.service.IGoldService;
import com.wantwant.sfa.backend.gold.vo.GoldImportResult;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldApplyDetailMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldApplyMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.partTime.PartTimePositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午3:05
 */
@Service
@Slf4j
public class GoldService implements IGoldService {
    @Autowired
    private GoldTypeService goldTypeService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaGoldApplyMapper sfaGoldApplyMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaGoldApplyDetailMapper sfaGoldApplyDetailMapper;
    @Autowired
    private SfaGoldProcessMapper sfaGoldProcessMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;
    @Autowired
    private GoldProcessComponent goldProcessComponent;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private ICostTypeService costTypeService;

    private String MSG_TEMPLATE = "第{0}行数据,{1}";
    @Autowired
    private ICheckCustomerService checkCustomerService;

    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Resource
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;



    @Override
    @Transactional
    public List<String> upload(MultipartFile file, int type,String person) {
        log.info("GoldService upload type:{}",type);
        if(type < 0 || type > 3) {
            throw new ApplicationException("币种类型传入错误!");
        }
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        // 获取审核人信息
        CeoBusinessOrganizationPositionRelation processUserInfo = checkCustomerService.getPersonInfo(person,loginInfo);

        ImportParams params = new ImportParams();
        List<GoldImportDto> list = new ArrayList<>();
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), GoldImportDto.class, params);
        } catch (Exception e) {
            log.error("【gold upload】导入失败", e);
            throw new ApplicationException("导入失败");
        }

        list = list.stream().filter(e -> StringUtils.isNotBlank(e.getStartMonth())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            throw new ApplicationException("无法获取导入信息");
        }

        GoldImportDto goldImportDto = list.get(0);

        // 检查费用类型是否唯一
        List<String> expensesTypeList = list.stream().map(GoldImportDto::getExpensesType).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        if(expensesTypeList.size() > 1){
            throw new ApplicationException("存在不同的费用类型无法导入");
        }

        List<String> boundaryList = list.stream().map(GoldImportDto::getBoundary).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        if(boundaryList.size() > 1){
            throw new ApplicationException("存在不同的边际类型无法导入");
        }

        Optional<GoldImportDto> first = list.stream().filter(f -> StringUtils.isBlank(f.getBoundary())).findFirst();
        if(first.isPresent()){
            throw new ApplicationException("边际类型必填写");
        }

        // 判断操作类型，有大区名称的就是组织类型
        int importType = StringUtils.isBlank(goldImportDto.getAreaName()) ? 1 : 2;

        Integer goldType = goldTypeService.selectGoldTypeByExpensesType(goldImportDto.getExpensesType());
        if (Objects.isNull(goldType)) {
            log.info("expensesType:{}", goldImportDto.getExpensesType());
            throw new ApplicationException("无法获取金币类型");
        }

        // 计算总金额
        BigDecimal totalAmount = list.stream().map(GoldImportDto::getAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 保存sfa_gold_apply
        SfaGoldApplyEntity sfaGoldApplyEntity = saveGoldApply(goldImportDto.getStartMonth(),goldImportDto.getEndMonth(), goldType, processUserInfo, importType,totalAmount,list.get(0).getTitle(),goldImportDto.getBoundary(), type);
        // 保存sfa_gold_apply_detail
        List<String> errList = saveGoldApplyDetail(list, sfaGoldApplyEntity, importType, processUserInfo, type);

        if(!CollectionUtils.isEmpty(errList)){
            sfaGoldApplyEntity.setIsDelete(1);
            sfaGoldApplyMapper.updateById(sfaGoldApplyEntity);
            return errList;
        }
        // 冗余字段到主表
        sfaGoldApplyMapper.updateById(sfaGoldApplyEntity);


        // 创建sfa_gold_process
        SfaGoldProcessEntity sfaGoldProcessEntity = saveSfaGoldProcess(sfaGoldApplyEntity, processUserInfo);
        // 创建sfa_gold_process_record
        SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity = saveSfaGoldProcessRecord(sfaGoldProcessEntity, processUserInfo);
        // 绑定sfa_gold_process_record与创建sfa_gold_process
        bindRelation(sfaGoldProcessEntity, sfaGoldProcessRecordEntity);

        return ListUtils.EMPTY_LIST;
    }


    @Override
    public GoldImportResult approval(GoldApprovalRequest request) {

        GoldProcessDto dto = new GoldProcessDto();
        dto.setAppId(request.getApplyId());
        dto.setPerson(request.getPerson());

        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", request.getApplyId()));
        if (Objects.isNull(sfaGoldProcessEntity)) {
            throw new ApplicationException("流程记录未找到");
        }

        // 主流程结果
        Integer processResult = sfaGoldProcessEntity.getProcessResult();
        if (processResult == GoldProcessResultEnum.FAIL.getStatus()) {
            dto.setReApply(true);
        }
        dto.setProcessType(sfaGoldProcessEntity.getProcessType());

        dto.setResult(GoldProcessResultEnum.PASS.getStatus());
        dto.setRemark(request.getRemark());

        return goldProcessComponent.dispatch(dto);

    }

    @Override
    public void dismissed(GoldApprovalRequest request) {
        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", request.getApplyId()));
        if (Objects.isNull(sfaGoldProcessEntity)) {
            throw new ApplicationException("流程记录未找到");
        }

        GoldProcessDto dto = new GoldProcessDto();
        dto.setAppId(request.getApplyId());
        dto.setPerson(request.getPerson());
        dto.setProcessType(sfaGoldProcessEntity.getProcessType());
        dto.setResult(GoldProcessResultEnum.FAIL.getStatus());
        dto.setRemark(request.getRemark());

        goldProcessComponent.dispatch(dto);
    }

    @Override
    public void deleteDetailById(Long applyDetailId) {
        SfaGoldApplyDetailEntity sfaGoldApplyDetailEntity = sfaGoldApplyDetailMapper.selectById(applyDetailId);
        if (Objects.isNull(sfaGoldApplyDetailEntity)) {
            throw new ApplicationException("申请明细获取失败");
        }

        sfaGoldApplyDetailEntity.setIsDelete(1);
        sfaGoldApplyDetailMapper.updateById(sfaGoldApplyDetailEntity);
    }


    private void bindRelation(SfaGoldProcessEntity sfaGoldProcessEntity, SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity) {
        sfaGoldProcessEntity.setProcessRecordId(sfaGoldProcessRecordEntity.getId());
        sfaGoldProcessMapper.updateById(sfaGoldProcessEntity);
    }

    private SfaGoldProcessRecordEntity saveSfaGoldProcessRecord(SfaGoldProcessEntity sfaGoldProcessEntity, CeoBusinessOrganizationPositionRelation processUserInfo) {
        SfaGoldProcessRecordEntity entity = new SfaGoldProcessRecordEntity();
        entity.setProcessUserId(processUserInfo.getEmployeeId());
        entity.setProcessType(GoldProcessEnum.APPLY.getType());
        entity.setProcessResult(GoldProcessResultEnum.PROCESS.getStatus());
        entity.setProcessId(sfaGoldProcessEntity.getId());
        entity.setCreateTime(LocalDateTime.now());
        sfaGoldProcessRecordMapper.insert(entity);
        return entity;
    }

    private SfaGoldProcessEntity saveSfaGoldProcess(SfaGoldApplyEntity sfaGoldApplyEntity, CeoBusinessOrganizationPositionRelation processUserInfo) {
        SfaGoldProcessEntity entity = new SfaGoldProcessEntity();
        entity.setBatchId(sfaGoldApplyEntity.getId());
        entity.setProcessType(GoldProcessEnum.APPLY.getType());
        entity.setProcessResult(GoldProcessResultEnum.PROCESS.getStatus());
        sfaGoldProcessMapper.insert(entity);
        return entity;
    }

    private List<String> saveGoldApplyDetail(List<GoldImportDto> list, SfaGoldApplyEntity sfaGoldApplyEntity, int importType, CeoBusinessOrganizationPositionRelation processUserInfo, int coinsType) {

        // 校验检查
        List<String> errList = checkImportInfo(list, importType);
        if (!CollectionUtils.isEmpty(errList)) {
            return errList;
        }
        // 导入造旺币发放信息
        doImport(list, sfaGoldApplyEntity, importType, processUserInfo, coinsType);

        return errList;
    }

    private void doImport(List<GoldImportDto> list, SfaGoldApplyEntity sfaGoldApplyEntity, int importType, CeoBusinessOrganizationPositionRelation processUserInfo, int coinsType) {
        //spu需要通过旺铺接口进行校验
        List<SPUInfoModel> spuInfoModelList = null;
        if(coinsType == 2) {
            spuInfoModelList =  activityQuotaConnectorUtil.querySPUInfo();
            if(CollectionUtils.isEmpty(spuInfoModelList)) {
                throw new ApplicationException("旺铺获取SPU信息失败");
            }
        }

        for (int i = 0; i < list.size(); i++) {
            SfaGoldApplyDetailEntity entity = new SfaGoldApplyDetailEntity();
            GoldImportDto e = list.get(i);
            if(coinsType == 2) {
               Optional<SPUInfoModel> option =  spuInfoModelList.stream().filter(s->s.getSpuName().equals(e.getCoinsSubTypeName())).findFirst();
                if(!option.isPresent()) {
                    throw new ApplicationException("SPU名称：" + e.getCoinsSubTypeName() + "查找失败");
                }
                entity.setCoinsTypeSubName(e.getCoinsSubTypeName());
                entity.setCoinsTypeSubId(option.get().getSpuId());
            }
            Integer applyType = goldTypeService.selectExpensesTypeCodeByExpensesType(e.getExpensesType());
            Integer type = goldTypeService.selectGoldTypeByExpensesType(e.getExpensesType());
            sfaGoldApplyEntity.setApplyType(applyType);


            String spuExpenditure = e.getSpuExpenditure();
            if(StringUtils.isNotBlank(spuExpenditure)){
                List<String> spuNames = Arrays.asList(spuExpenditure.split(","));
                List<String> spuIds = new ArrayList<>();
                List<SPUInfoModel> finalSpuInfoModelList = activityQuotaConnectorUtil.querySPUInfo();
                spuNames.forEach(s -> {
                    Optional<SPUInfoModel> first = finalSpuInfoModelList.stream().filter(f -> f.getSpuName().equals(s)).findFirst();
                    if(!first.isPresent()){
                        throw new ApplicationException("支出SPU：" + s + "查找失败");
                    }
                    spuIds.add(first.get().getSpuId());
                });
                entity.setSpuExpenditureIds(String.join(",",spuIds));

            }

            entity.setSpuExpenditure(e.getSpuExpenditure());
            entity.setCoinsType(coinsType);
            entity.setAmount(new BigDecimal(e.getAmount()));
            entity.setBatchId(sfaGoldApplyEntity.getId());
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreateUserId(processUserInfo.getEmployeeId());
            entity.setMonth(e.getStartMonth());
            entity.setGoldType(type);
            entity.setExpenseType(e.getExpensesType());
            entity.setIsDelete(0);
            entity.setRemark(e.getRemark());
            entity.setStatus(0);
            entity.setTitle(e.getTitle());
            entity.setStartMonth(e.getStartMonth());
            entity.setEndMonth(e.getEndMonth());
            String expensesRate = e.getExpensesRate();


            if(e.getBoundary().equals("1")){
                entity.setBoundary(1);
            }else{
                entity.setBoundary(2);
            }

            if(StringUtils.isNotBlank(expensesRate)){
                entity.setExpensesRate(new BigDecimal(expensesRate));
            }
            entity.setDeptName(e.getDeptName());


            // 检查部门是否正确
            DepartEntity deptEntity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("dept_name", e.getDeptName()).eq("delete_flag", 0));
            if(Objects.isNull(deptEntity)){
                throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2, "部门不存在"));
            }
            sfaGoldApplyEntity.setDeptName(e.getDeptName());

            // 检查费用类型和部门是否匹配
            try {
                costTypeService.checkApplyTypeDeptRelation(deptEntity.getDeptCode(),e.getExpensesType(),true);
            } catch (Exception ex) {
                throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2, ex.getMessage()));
            }

            // 业务
            if (importType == 1) {
                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>()
                        .eq("mobile", e.getMobile())
                        .in("employee_status", 1, 2)
                        .eq("employee_name", e.getCeoName())
                        .eq("channel", RequestUtils.getChannel())
                );

                if (Objects.isNull(sfaEmployeeInfoModel)) {
                    throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2, "员工离职"));
                }


                entity.setPositionId(sfaEmployeeInfoModel.getPositionId());
                entity.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
                entity.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
            }

            // 组织类型
            else if (importType == 2) {
                String areaName = e.getAreaName();
                String vareaName = e.getVareaName();
                String provinceName = e.getProvinceName();
                String companyName = e.getCompanyName();
                String departName = e.getBranchName();
                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
                if(StringUtils.isNotBlank(departName)) {
                    ceoBusinessOrganizationPositionRelation = selectPositionByOrganizationId(departName);
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                        throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2,  departName + "组织类型查找"));
                    }
                } else if(StringUtils.isNotBlank(companyName)) {
                    ceoBusinessOrganizationPositionRelation = selectPositionByOrganizationId(companyName);
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                        throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2,  companyName + "组织类型查找"));
                    }
                } else if(StringUtils.isNotBlank(provinceName)) {
                    ceoBusinessOrganizationPositionRelation = selectPositionByOrganizationId(provinceName);
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                        throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2,  provinceName + "组织类型查找"));
                    }
                } else if(StringUtils.isNotBlank(vareaName)) {
                    ceoBusinessOrganizationPositionRelation = selectPositionByOrganizationId(vareaName);
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                        throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2,  vareaName + "组织类型查找"));
                    }
                }else if (StringUtils.isNotBlank(areaName)) {
                    ceoBusinessOrganizationPositionRelation = selectPositionByOrganizationId(areaName);
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                        throw new ApplicationException(MessageFormat.format(MSG_TEMPLATE, i + 2,  areaName + "组织类型查找"));
                    }
                }
                entity.setPositionId(ceoBusinessOrganizationPositionRelation.getPositionId());
            }


            sfaGoldApplyDetailMapper.insert(entity);
        }
    }

    private List<String> checkImportInfo(List<GoldImportDto> list, int importType) {
        List<String> errMsgList = new ArrayList<>();

        AtomicReference<Integer> goldType = new AtomicReference<>();
        AtomicReference<String> deptName = new AtomicReference<>();


        for (int i = 0; i < list.size(); i++) {
            GoldImportDto e = list.get(i);
            Integer type = goldTypeService.selectExpensesTypeCodeByExpensesType(e.getExpensesType());
            if (Objects.isNull(goldType.get())) {
                goldType.set(type);
            } else {
//                if (!goldType.get().equals(type)) {
//                    errMsgList.add(MessageFormat.format(MSG_TEMPLATE, i + 2, "导入文件含有2种不同的金币类型"));
//                    continue;
//                }
            }


            String dpName = e.getDeptName();
            if (Objects.isNull(deptName.get())) {
                deptName.set(dpName);
            } else {
//                if (!deptName.get().equals(dpName)) {
//                    errMsgList.add(MessageFormat.format(MSG_TEMPLATE, i + 2, "导入文件含有2种不同的费用部门"));
//                    continue;
//                }
            }


            String month = e.getStartMonth();
            if (StringUtils.isBlank(month)) {
                errMsgList.add(MessageFormat.format(MSG_TEMPLATE, i + 2, "缺少日期"));
                continue;
            }

            if (Objects.isNull(goldType)) {
                errMsgList.add(MessageFormat.format(MSG_TEMPLATE, i + 2, "无法获取金币类型"));
                continue;
            }

            // 业务
            if (importType == 1) {
                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>()
                        .eq("mobile", e.getMobile())
                        .in("employee_status", 1, 2)
                        .eq("employee_name", e.getCeoName())
                        .eq("channel", RequestUtils.getChannel())
                );
                if (Objects.isNull(sfaEmployeeInfoModel)) {
                    errMsgList.add(MessageFormat.format(MSG_TEMPLATE, i + 2, "无法获取业务员信息"));
                    continue;
                }
            }
        }

        return errMsgList;
    }

    private CeoBusinessOrganizationPositionRelation selectPositionByOrganizationId(String orgName) {

        String organizationId = organizationMapper.getOrganizationIdByName(orgName, RequestUtils.getChannel(),RequestUtils.getBusinessGroup());
        if (StringUtils.isBlank(organizationId)) {
            throw new ApplicationException("组织信息获取失败");
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", organizationId)
                .eq("channel", RequestUtils.getChannel())
        );
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException("组织信息获取失败");
        }
        return ceoBusinessOrganizationPositionRelation;
    }


    private SfaGoldApplyEntity saveGoldApply(String startMonth, String endMonth, Integer goldType, CeoBusinessOrganizationPositionRelation processUserInfo,
                                             int importType, BigDecimal totalAmount, String title, String boundary, int coinsType) {
        SfaGoldApplyEntity sfaGoldApplyEntity = new SfaGoldApplyEntity();
        sfaGoldApplyEntity.setCreateTime(LocalDateTime.now());
        sfaGoldApplyEntity.setCreateUserId(processUserInfo.getEmployeeId());
        sfaGoldApplyEntity.setGoldType(goldType);
        sfaGoldApplyEntity.setTotalAmount(totalAmount);
        sfaGoldApplyEntity.setIsDelete(0);
        sfaGoldApplyEntity.setBusinessGroup(RequestUtils.getBusinessGroup());
        sfaGoldApplyEntity.setTitle(title);
        sfaGoldApplyEntity.setStartMonth(startMonth);
        sfaGoldApplyEntity.setEndMonth(endMonth);
        sfaGoldApplyEntity.setImportType(importType);
        sfaGoldApplyEntity.setCreateUserName(processUserInfo.getEmployeeName());
        // 获取操作人部门
        String departmentName = deptEmployeeRelationMapper.getDepartNameByEmployeeId(processUserInfo.getEmployeeId());
        sfaGoldApplyEntity.setCreateUserDeptName(departmentName);
        sfaGoldApplyEntity.setBoundary(Integer.parseInt(boundary));
        sfaGoldApplyEntity.setCoinsType(coinsType);
        sfaGoldApplyMapper.insert(sfaGoldApplyEntity);
        return sfaGoldApplyEntity;
    }

}
