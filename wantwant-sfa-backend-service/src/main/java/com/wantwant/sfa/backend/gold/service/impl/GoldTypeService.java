package com.wantwant.sfa.backend.gold.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.estimate.model.EstimateCompanyExportModel;
import com.wantwant.sfa.backend.gold.entity.GoldIssueEntity;
import com.wantwant.sfa.backend.gold.request.GoldCumulativeRequest;
import com.wantwant.sfa.backend.gold.service.IGoldTypeService;
import com.wantwant.sfa.backend.gold.vo.ExpenseVo;
import com.wantwant.sfa.backend.gold.vo.GoldCumulativeVo;
import com.wantwant.sfa.backend.mapper.gold.GoldIssueMapper;
import com.wantwant.sfa.backend.mapper.gold.GoldTypeMapper;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午3:24
 */
@Service
public class GoldTypeService implements IGoldTypeService {
    @Autowired
    private GoldTypeMapper goldTypeMapper;

    @Autowired
    private GoldIssueMapper goldIssueMapper;

    public Integer selectGoldTypeByExpensesType(String expensesType) {
        return goldTypeMapper.selectGoldTypeByExpensesType(expensesType);
    }

    @Override
    public Integer selectExpensesTypeCodeByExpensesType(String expensesType) {
        return goldTypeMapper.selectExpensesTypeCodeByExpensesType(expensesType);
    }

    @Override
    public List<ExpenseVo> selectExpenseTypeByCoins(Integer coinsType) {
        return goldTypeMapper.selectExpenseTypeByCoins(coinsType);
    }

    @Override
    public List<GoldCumulativeVo> selectGoldTypeService(GoldCumulativeRequest request) {
        List<GoldCumulativeVo> vos = new ArrayList<>();
        if(!request.getOrganizationId().equals("ZB_Z")){
             vos = goldIssueMapper.selectByOrganizationId(request);
            for (GoldCumulativeVo vo : vos) {
                if (vo.getPositionTypeId() == 1){
                    //查下级
                    vo.setNextList(goldIssueMapper.selectNextByOrganizationId(vo.getOrganizationId(), request.getYearMonth()));
                }
            }
        }else {
            vos = goldIssueMapper.selectByZb(request);
            //先查大区 通过大区查下级  最后总计
            if(ObjectUtils.isNull(request.getEmployeeName())){
                vos.add(goldIssueMapper.selectByTotal(request.getYearMonth()));
            }
            for (GoldCumulativeVo vo : vos) {
                vo.setNextList(goldIssueMapper.selectNextByOrganizationId(vo.getOrganizationId(), request.getYearMonth()));
            }
        }
        return vos;
    }

    @Override
    public void exportCumulative(GoldCumulativeRequest request) {
        List<GoldCumulativeVo> vos = new ArrayList<>();
        if(!request.getOrganizationId().equals("ZB_Z")){
            List<GoldCumulativeVo> goldCumulativeVos = goldIssueMapper.selectByOrganizationId(request);
            for (GoldCumulativeVo vo : goldCumulativeVos) {
                vos.add(vo);
                if (vo.getPositionTypeId() == 1){
                    vos.addAll(goldIssueMapper.selectNextByOrganizationId(vo.getOrganizationId(), request.getYearMonth()));
                }
            }
        }else {
            List<GoldCumulativeVo> goldCumulativeVos = goldIssueMapper.selectByZb(request); //大区
            for (GoldCumulativeVo vo : goldCumulativeVos) {
                vos.add(vo);
                vos.addAll(goldIssueMapper.selectNextByOrganizationId(vo.getOrganizationId(), request.getYearMonth()));
            }
            vos.add(goldIssueMapper.selectByTotal(request.getYearMonth()));
        }

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        String name = "旺金币额度发放导出" + sheetName;

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), GoldCumulativeVo.class, vos);

        response.setContentType("application/vnd.ms-excel");

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
                    .encode(name + ".xlsx"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }
}
