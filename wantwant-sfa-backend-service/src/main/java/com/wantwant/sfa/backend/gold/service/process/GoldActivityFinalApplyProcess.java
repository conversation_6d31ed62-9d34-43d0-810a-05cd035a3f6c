package com.wantwant.sfa.backend.gold.service.process;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.*;
import com.wantwant.sfa.backend.activityQuota.service.ActivityQuotaService;
import com.wantwant.sfa.backend.activityQuota.service.ICostTypeService;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.activityQuota.service.IQuotaSendService;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyDetailEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessRecordEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.gold.enums.TransactionBusinessTypeEnum;
import com.wantwant.sfa.backend.gold.model.GoldImportErrModel;
import com.wantwant.sfa.backend.gold.model.GoldImportJsonModel;
import com.wantwant.sfa.backend.gold.model.GoldImportModel;
import com.wantwant.sfa.backend.gold.model.GoldImportResultModel;
import com.wantwant.sfa.backend.gold.service.IGoldTypeService;
import com.wantwant.sfa.backend.interview.enums.PositionTypeEnum;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.activityQuota.ActivityQuotaMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.gold.*;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaCustomer;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.rabbitMQ.config.GoldImportErrMsgTopicRabbitConfig;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.wallet.dto.PersonalBatchDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletSendDTO;
import com.wantwant.sfa.backend.wallet.service.IWalletApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Consumer;

/**
 * @Description: 造旺币(试吃)最终审核
 * @Auther: zhengxu
 * @Date: 2022/03/23/下午3:06
 */
@Component
@Slf4j
public class GoldActivityFinalApplyProcess implements Consumer<GoldProcessDto> {
    @Autowired
    private SfaGoldProcessMapper sfaGoldProcessMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Autowired
    private SfaGoldApplyDetailMapper sfaGoldApplyDetailMapper;
    @Autowired
    private IGoldTypeService goldTypeService;
    @Autowired
    private SfaGoldApplyMapper sfaGoldApplyMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private GoldTypeMapper goldTypeMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private ICostTypeService costTypeService;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private IQuotaSendService quotaSendService;

    @Autowired
    private IWalletApplicationService iWalletApplicationService;


    private String messageTemplate = "您好，{0}，系统提醒您，你有一笔新的旺金币已入账，费用类型： {1}，数量：{2}，烦请进行查看，如数额异常请勿使用，及时联系发送方；";

    private String titleTemplate = "{0}旺金币到账信息";


    private String revenueMsg = "{0}-{1}-{2}";

    @Override
    @Transactional
    public void accept(GoldProcessDto goldProcessDto) {
        log.info("【造旺币最终审核通过】dto:{}",goldProcessDto);
        // 根据申请ID获取处理流程
        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", goldProcessDto.getAppId()));
        if(Objects.isNull(sfaGoldProcessEntity)){
            throw new ApplicationException("流程记录获取失败");
        }

        // 根据处理流程获取当前流程记录ID
        SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity = sfaGoldProcessRecordMapper.selectById(sfaGoldProcessEntity.getProcessRecordId());
        if(sfaGoldProcessRecordEntity.getProcessType().equals(goldProcessDto.getProcessType()) && sfaGoldProcessRecordEntity.getProcessResult() == GoldProcessResultEnum.PASS.getStatus()){
            throw new ApplicationException("请勿重复操作");
        }

        SfaGoldApplyEntity sfaGoldApplyEntity = sfaGoldApplyMapper.selectById(sfaGoldProcessEntity.getBatchId());
        if(Objects.isNull(sfaGoldApplyEntity)) {
            throw new ApplicationException("申请信息无法获取");
        }


        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = checkCustomerService.getPersonInfo(goldProcessDto.getPerson(),loginInfo);

        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("操作人信息获取失败");
        }

        // 修改当前流程的状态为通过
        sfaGoldProcessRecordEntity.setProcessResult(GoldProcessResultEnum.PASS.getStatus());
        sfaGoldProcessRecordEntity.setProcessUserId(goldProcessDto.getPerson());
        sfaGoldProcessRecordEntity.setRemark(goldProcessDto.getRemark());
        sfaGoldProcessRecordEntity.setProcessTime(LocalDateTime.now());
        sfaGoldProcessRecordMapper.updateById(sfaGoldProcessRecordEntity);

        // 修改流程主表的状态为通过
        sfaGoldProcessEntity.setProcessResult(GoldProcessResultEnum.PASS.getStatus());
        sfaGoldProcessMapper.updateById(sfaGoldProcessEntity);

        // 数据检查
        GoldImportResultModel goldImportResult = checkGoldApply(goldProcessDto,sfaGoldApplyEntity.getImportType(),sfaGoldApplyEntity.getBusinessGroup());

        List<GoldImportErrModel> errList = goldImportResult.getErrList();
        List<GoldImportModel> importList = goldImportResult.getImportList();

        // 标记错误记录
        if(!CollectionUtils.isEmpty(errList)){
            errList.forEach(e -> {
                SfaGoldApplyDetailEntity detailEntity = e.getDetailEntity();
                detailEntity.setStatus(2);
                detailEntity.setErrMsg(e.getErrMsg());
                sfaGoldApplyDetailMapper.updateById(detailEntity);
            });

            // 发送导入错误消息
            sendErrMsg(errList);
        }

        if(!CollectionUtils.isEmpty(importList)){


            if(sfaGoldApplyEntity.getImportType() == 1) {
                // 发放造旺币
//                send(importList, ceoBusinessOrganizationPositionRelation);
                sendV2ForPersonBatch(importList, sfaGoldApplyEntity);
            }
            // 组织导入追缴罚款
            else{
                // 设置额度
//                settingQuota(importList,sfaGoldApplyEntity.getImportType(),ceoBusinessOrganizationPositionRelation);

//                importList.forEach(e -> {
//                    penaltyService.pressPenalty(e.getCeoBusinessOrganizationPositionRelation().getOrganizationId(),e.getApplyType(),e.getQuota());
//                });
                sendV2ForOrganizationBatch(importList, ceoBusinessOrganizationPositionRelation);
            }

            // 发送成功标记
            importList.forEach(e -> {
                SfaGoldApplyDetailEntity detailEntity = e.getDetailEntity();
                detailEntity.setStatus(1);
                sfaGoldApplyDetailMapper.updateById(detailEntity);
            });
        }
    }

    private void sendErrMsg(List<GoldImportErrModel> errList) {

        List<GoldImportJsonModel> list = new ArrayList<>();

        errList.forEach(e -> {
            GoldImportJsonModel model = new GoldImportJsonModel();
            model.setEmployeeInfoId(e.getDetailEntity().getEmployeeInfoId());
            model.setApplyType(e.getDetailEntity().getExpenseType());
            model.setAmount(e.getDetailEntity().getAmount());
            model.setMonth(e.getDetailEntity().getMonth());
            model.setErrMsg(e.getErrMsg());
            model.setGoldDetailId(e.getDetailEntity().getId());
            list.add(model);
        });

        log.info("gold import err queue,exchange:{},queue:{},json:{}", GoldImportErrMsgTopicRabbitConfig.goldImportErrMsgExchange,GoldImportErrMsgTopicRabbitConfig.goldImportErrMsg, JSONObject.toJSONString(list));

        rabbitTemplate.convertAndSend(
                GoldImportErrMsgTopicRabbitConfig.goldImportErrMsgExchange,
                GoldImportErrMsgTopicRabbitConfig.goldImportErrMsg,
                JSONObject.toJSONString(list));
    }


    private GoldImportResultModel checkGoldApply(GoldProcessDto goldProcessDto,int importType,int businessGroup) {
        GoldImportResultModel goldImportResultModel = new GoldImportResultModel();
        // 获取造旺币导入明细
        List<SfaGoldApplyDetailEntity> sfaGoldApplyDetailEntities = sfaGoldApplyDetailMapper.selectList(new QueryWrapper<SfaGoldApplyDetailEntity>()
                .eq("batch_id", goldProcessDto.getAppId())
                .eq("is_delete", 0)
        );

        if(CollectionUtils.isEmpty(sfaGoldApplyDetailEntities)){
            goldImportResultModel.setErrList(ListUtils.EMPTY_LIST);
            goldImportResultModel.setImportList(ListUtils.EMPTY_LIST);
            return goldImportResultModel;
        }

        //spu需要通过旺铺接口进行校验
        List<SPUInfoModel> spuInfoModelList = null;
        int coinsType = sfaGoldApplyDetailEntities.get(0).getCoinsType();
        if(coinsType == 2) {
            spuInfoModelList =  activityQuotaConnectorUtil.querySPUInfo();
            if(CollectionUtils.isEmpty(spuInfoModelList)) {
                throw new ApplicationException("发放时，旺铺获取SPU信息校验失败");
            }
        }

        List<GoldImportErrModel> errList = new ArrayList<>();
        List<GoldImportModel> importList = new ArrayList<>();


        for(SfaGoldApplyDetailEntity entity : sfaGoldApplyDetailEntities){
            if(entity.getCoinsType() == 2) {
                Optional<SPUInfoModel> option =  spuInfoModelList.stream().filter(s->s.getSpuName().equals(entity.getCoinsTypeSubName())).findFirst();
                if(!option.isPresent()) {
                    throw new ApplicationException("SPU的id为：" + entity.getCoinsTypeSubName() + "查找失败");
                }
            }

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("position_id", entity.getPositionId())
                    .eq("channel", RequestUtils.getChannel())
            );

            // 费用类型检查
            Integer applyType = goldTypeService.selectExpensesTypeCodeByExpensesType(entity.getExpenseType());

            // 检查部门是否存在
            DepartEntity deptEntity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("dept_name", entity.getDeptName()).eq("delete_flag", 0));


            // 合伙人导入信息确认
            if(importType == 1){
                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(entity.getEmployeeInfoId());
                if(Objects.isNull(applyType)){
                    errList.add(instanceErrModel(sfaEmployeeInfoModel,entity,"错误的费用类型"));
                    continue;
                }

                // 员工表检查
                if(sfaEmployeeInfoModel.getEmployeeStatus() != 1 && sfaEmployeeInfoModel.getEmployeeStatus() != 2){
                    errList.add(instanceErrModel(sfaEmployeeInfoModel,entity,"员工已离职"));
                    continue;
                }


                // 客户信息表检查
//                SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>()
//                        .eq("position_id", sfaEmployeeInfoModel.getPositionId())
//                        .eq("mobile_number",sfaEmployeeInfoModel.getMobile())
//                        .eq("is_frozen", 0)
//                );
//                if(Objects.isNull(sfaCustomer)){
//                    errList.add(instanceErrModel(sfaEmployeeInfoModel,entity,"客户信息获取失败"));
//                    continue;
//                }

                if(Objects.isNull(deptEntity)){
                    errList.add(instanceErrModel(sfaEmployeeInfoModel,entity,"费用承担部门不能存在"));
                    continue;
                }else{
                    try {
                        costTypeService.checkApplyTypeDeptRelation(deptEntity.getDeptCode(),entity.getExpenseType(),false);
                    } catch (Exception e) {
                        errList.add(instanceErrModel(sfaEmployeeInfoModel,entity,e.getMessage()));
                    }
                }
            }


            // 检查结束
            GoldImportModel importModel = new GoldImportModel();
            importModel.setRemark(goldProcessDto.getRemark());
            importModel.setYearMonthStart(entity.getStartMonth());
            importModel.setYearMonthEnd(entity.getEndMonth());
            importModel.setDetailEntity(entity);
            importModel.setDeptCode(deptEntity.getDeptCode());
            importModel.setDeptName(deptEntity.getDeptName());
//            importModel.setIsCommonCoins(entity.getIsCommonCoins());
            importModel.setBusinessGroupCode(String.valueOf(businessGroup));
            importModel.setApplyType(applyType);
            importModel.setMarginalCost(entity.getBoundary() - 1);
            importModel.setCeoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation);
            importModel.setQuota(entity.getAmount());
            importModel.setAmountSubType(entity.getCoinsType());
            importModel.setAmountSubTypeId(entity.getCoinsTypeSubId());
            importModel.setCoinsTypeSubName(entity.getCoinsTypeSubName());
            importModel.setMemberKey(entity.getMemberKey());
            importModel.setPositionId(entity.getPositionId());
            importModel.setRemark(entity.getRemark());
            importList.add(importModel);
        }

        goldImportResultModel.setErrList(errList);
        goldImportResultModel.setImportList(importList);
        return goldImportResultModel;
    }

    private GoldImportErrModel instanceErrModel(SfaEmployeeInfoModel sfaEmployeeInfoModel,SfaGoldApplyDetailEntity entity,String msg) {
        GoldImportErrModel goldImportErrModel = new GoldImportErrModel();
        goldImportErrModel.setDetailEntity(entity);
        goldImportErrModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        goldImportErrModel.setMobile(sfaEmployeeInfoModel.getMobile());
        goldImportErrModel.setErrMsg(msg);
        return goldImportErrModel;
    }

    public void sendMessage(String positionId, Integer applyType, BigDecimal quota) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", positionId).eq("channel", 3));
        if(Objects.nonNull(ceoBusinessOrganizationPositionRelation) && StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())){
            String day = DateUtil.format(new Date(), "yyyy年-MM月-dd日");
            String title = MessageFormat.format(titleTemplate,day);

            String typeName = goldTypeMapper.selectTypeNameByCode(applyType);
            String message = MessageFormat.format(messageTemplate,day,typeName,quota.toString());


            notifyService.saveNotify(2, NotifyTemplateTypeEnum.SEND.getType(),title,ceoBusinessOrganizationPositionRelation.getEmployeeId(),message);
        }

    }


    private ActivityQuotaApplicationModel createActivityQuotaApplicationModel(Integer applyType, BigDecimal amount, String remark, SfaEmployeeInfoModel sfaEmployeeInfoModel, String organizationId, Integer memberKey, CeoBusinessOrganizationPositionRelation person) {
        ActivityQuotaApplicationModel model = new ActivityQuotaApplicationModel();
        model.setCode("0");
        model.setApplyTime(LocalDate.now());
        model.setActivityType(0);
        model.setApplyType(applyType);
        model.setPositionId(sfaEmployeeInfoModel.getPositionId());
        model.setOrganizationId(organizationId);
        model.setMemberKey(memberKey);
        model.setMobile(sfaEmployeeInfoModel.getMobile());
        model.setApplyQuota(amount);
        model.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        model.setAuditEmployeeId(person.getEmployeeId());
        model.setAuditEmployeeName(person.getEmployeeName());
        model.setAuditQuota(amount);
        model.setRemark(remark);
        model.setChannel(RequestUtils.getChannel());
        model.setStatus(1);
        model.setCreateTime(LocalDateTime.now());
        model.setBusinessGroup(RequestUtils.getBusinessGroup());
        model.setAuditTime(LocalDateTime.now());
        model.setType(2);
        return model;
    }

//    private void settingQuota(List<GoldImportModel> importList, Integer importType,CeoBusinessOrganizationPositionRelation person) {
//
//        LoginModel loginInfo = RequestUtils.getLoginInfo();
//
//        importList.forEach(e -> {
//            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = e.getCeoBusinessOrganizationPositionRelation();
//
//            SfaGoldApplyDetailEntity detailEntity = e.getDetailEntity();
//
//            // 获取活动主表信息
//            ActivityQuotaModel activityQuotaModel = activityQuotaMapper.selectOne(new QueryWrapper<ActivityQuotaModel>()
//                    .eq("activity_type", 0)
//                    .eq("activity_time", LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).toString())
//                    .eq("channel", RequestUtils.getChannel())
//                    .eq("business_group",e.getBusinessGroup())
//            );
//            if (Objects.isNull(activityQuotaModel)) {
//                throw new ApplicationException("活动主信息不存在");
//            }
//
//
//            QuotaLogAdditionalModel quotaLogAdditionalModel = new QuotaLogAdditionalModel();
//            quotaLogAdditionalModel.setExpenditure("总部");
//            quotaLogAdditionalModel.setDeptCode(e.getDeptCode());
//            quotaLogAdditionalModel.setDeptName(e.getDeptName());
//            quotaLogAdditionalModel.setLoginModel(loginInfo);
////            quotaLogAdditionalModel.setBoundary(e.getBoundary());
//            quotaLogAdditionalModel.setStartMonth(e.getYearMonthStart());
//            quotaLogAdditionalModel.setEndMonth(e.getYearMonthEnd());
//
//            String mobile = null;
//            if(importType == 2){
//                // 导入组织
//                quotaLogAdditionalModel.setRevenue(organizationMapper.getOrganizationName(ceoBusinessOrganizationPositionRelation.getOrganizationId()));
//                // 保存消息
//                if(StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())){
//                    sendMessage(ceoBusinessOrganizationPositionRelation.getPositionId(),e.getApplyType(),detailEntity.getAmount());
//                }
//            }else{
//                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(e.getDetailEntity().getEmployeeInfoId());
//                quotaLogAdditionalModel.setRevenue(MessageFormat.format(revenueMsg,sfaEmployeeInfoModel.getCompanyName(),sfaEmployeeInfoModel.getEmployeeName(),sfaEmployeeInfoModel.getMobile()));
//                mobile = sfaEmployeeInfoModel.getMobile();
//            }
//
//
//
//
//            log.info("【额度设置】organizationId:{},applyType:{},quota:{}",ceoBusinessOrganizationPositionRelation.getOrganizationId(),e.getApplyType(),e.getQuota());
//            activityQuotaService.recursionQuota(activityQuotaModel.getId(), ceoBusinessOrganizationPositionRelation.getOrganizationId(), e.getQuota(),
//                    LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).toString(), e.getApplyType(), BigDecimal.ZERO, person, importType == 1 ? 1 : 0, RequestUtils.getChannel(),false,true,mobile,quotaLogAdditionalModel);
//
//        });
//
//    }

    private void sendV2ForPersonBatch(List<GoldImportModel> importList, SfaGoldApplyEntity sfaGoldApplyEntity) {
        List<PersonalBatchDTO> retList = new ArrayList<>();
        List<SfaBusinessGroupEntity> sfaBusinessGroupEntities = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>().eq(SfaBusinessGroupEntity::getDeleteFlag, 0));



        GoldCoinTransactionGrantRequest goldCoinTransactionGrantRequest = new GoldCoinTransactionGrantRequest();

        List<GoldCoinTransactionGrantMemberRequest> grantMemberList = new ArrayList<>();


        importList.forEach(e->{
            GoldCoinTransactionGrantMemberRequest goldCoinTransactionGrantMemberRequest = new GoldCoinTransactionGrantMemberRequest();
            goldCoinTransactionGrantMemberRequest.setCause(e.getRemark());
            goldCoinTransactionGrantMemberRequest.setMemberKey(String.valueOf(e.getMemberKey()));
            goldCoinTransactionGrantMemberRequest.setOpUserName(sfaGoldApplyEntity.getCreateUserName());
            goldCoinTransactionGrantMemberRequest.setOpUserId(sfaGoldApplyEntity.getCreateUserId());

            goldCoinTransactionGrantMemberRequest.setOpUserOrganization("总部");
            goldCoinTransactionGrantMemberRequest.setOpUserRole(sfaGoldApplyEntity.getCreateUserDeptName());

            goldCoinTransactionGrantMemberRequest.setTransactionBusinessType(TransactionBusinessTypeEnum.SFA_HEAD_OFFICE_GRANT.getCode());

            List<GoldCoinTransactionGrantDetailRequest> grantDetailList = new ArrayList<>();
            GoldCoinTransactionGrantDetailRequest goldCoinTransactionGrantDetailRequest = new GoldCoinTransactionGrantDetailRequest();

            Integer amountSubType = e.getAmountSubType();
            goldCoinTransactionGrantDetailRequest.setAmountSubType(e.getAmountSubType());
            if(amountSubType == 1){
                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupEntities.stream().filter(f -> f.getId() == Integer.parseInt(e.getBusinessGroupCode())).findFirst().get();
                goldCoinTransactionGrantDetailRequest.setAmountSubTypeId(sfaBusinessGroupEntity.getBusinessGroupCode());
            }else{
                goldCoinTransactionGrantDetailRequest.setAmountSubTypeId(e.getAmountSubTypeId());
            }
            goldCoinTransactionGrantDetailRequest.setApplyType(e.getApplyType());
            goldCoinTransactionGrantDetailRequest.setDepartmentCode(e.getDeptCode());
            goldCoinTransactionGrantDetailRequest.setGrantAmount(e.getQuota());
            goldCoinTransactionGrantDetailRequest.setMarginalCost(e.getMarginalCost());
            grantDetailList.add(goldCoinTransactionGrantDetailRequest);
            goldCoinTransactionGrantMemberRequest.setGrantDetailList(grantDetailList);
            grantMemberList.add(goldCoinTransactionGrantMemberRequest);
        });
        goldCoinTransactionGrantRequest.setGrantMemberList(grantMemberList);


        iWalletApplicationService.personalBatch(goldCoinTransactionGrantRequest);
    }

    private void sendV2ForOrganizationBatch(List<GoldImportModel> importList, CeoBusinessOrganizationPositionRelation person) {

        List<SfaBusinessGroupEntity> sfaBusinessGroupEntities = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>().eq(SfaBusinessGroupEntity::getDeleteFlag, 0));



        importList.forEach(e->{
            WalletSendDTO dto = new WalletSendDTO();
            dto.setQuota(e.getQuota());
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupEntities.stream().filter(f -> f.getId() == Integer.parseInt(e.getBusinessGroupCode())).findFirst().get();
            dto.setBusinessGroup(sfaBusinessGroupEntity.getBusinessGroupCode());
            dto.setReceiverType(1);
            dto.setReceiverWalletType(e.getAmountSubType());
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", e.getPositionId()).eq("channel", 3));
            dto.setReceiverKey(ceoBusinessOrganizationPositionRelation.getOrganizationId());
            dto.setProcessUserId(person.getEmployeeId());
            dto.setProcessUserName(person.getEmployeeName());
            dto.setStartYearMonth(e.getYearMonthStart());
            dto.setEndYearMonth(e.getYearMonthEnd());
            dto.setRemark(e.getRemark());
            dto.setApplyType(e.getApplyType());
            dto.setSubTypeId(e.getAmountSubTypeId());
            dto.setBoundary(e.getMarginalCost());
            dto.setDeptCode(e.getDeptCode());
            dto.setDeptName(e.getDeptName());
            iWalletApplicationService.quickSend(dto);
        });
    }
//
//    private void send(List<GoldImportModel> importList, CeoBusinessOrganizationPositionRelation person) {
//
//        List<ActivityQuotaValidModel> list = new ArrayList<>();
//
//        importList.forEach(e -> {
//            ActivityQuotaValidModel model = new ActivityQuotaValidModel();
//            SfaGoldApplyDetailEntity detailEntity = e.getDetailEntity();
//
//            SfaPositionRelationEntity positionRelationEntity = sfaPositionRelationMapper.selectOne(new QueryWrapper<SfaPositionRelationEntity>()
//                    .eq("position_id", e.getCeoBusinessOrganizationPositionRelation().getPositionId())
//                    .orderByDesc("id")
//                    .last("limit 1")
//            );
//
//            if(Objects.isNull(positionRelationEntity)){
//                throw new ApplicationException("岗位表信息获取失败");
//            }
//
//
//
//            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(positionRelationEntity.getEmployeeInfoId());
//            if(Objects.isNull(sfaEmployeeInfoModel)){
//                throw new ApplicationException("员工信息获取失败");
//            }
//
//
//
//            model.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
//            model.setAmountType(0L);
//            model.setApplyType(e.getApplyType());
//            model.setAuditAmount(detailEntity.getAmount());
//            model.setSurplusAmount(detailEntity.getAmount());
//            model.setAuditor(person.getEmployeeId());
//            model.setAuditRemark(e.getRemark());
//
////            Integer isCommonCoins = e.getIsCommonCoins();
////            if(isCommonCoins == 0){
////                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
////                model.setAmountSubTypeId(sfaBusinessGroupEntity.getBusinessGroupCode());
////                model.setAmountSubType(1);
////            }else{
////                model.setAmountSubType(0);
////            }
//            if(e.getCoinsType() == 0) {
//                model.setAmountSubType(0);
//            }else if(e.getCoinsType() == 1) {
//                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
//                model.setAmountSubTypeId(sfaBusinessGroupEntity.getBusinessGroupCode());
//                model.setAmountSubType(1);
//            }else if(e.getCoinsType() == 2) {
//                model.setAmountSubTypeId(e.getCoinsTypeSubId());
//                model.setAmountSubType(2);
//            }else {
//                throw new ApplicationException("目前只支持通用币、组别币和SPU币");
//            }
//            model.setDepartmentCode(e.getDeptCode());
//            model.setPositionRelation(e.getCeoBusinessOrganizationPositionRelation());
//            model.setStatus(1);
//            Integer boundary = e.getBoundary();
//            if(Objects.nonNull(boundary)){
//                if(boundary == 1){
//                    model.setMarginalCost(0);
//                }else{
//                    model.setMarginalCost(1);
//                }
//            }
//            model.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
//            list.add(model);
//
//            model.setYearMonthStart(e.getYearMonthStart());
//            model.setYearMonthEnd(e.getYearMonthEnd());
//
//
//            // 创建活动申请
//            activityQuotaService.createNewApplyRequireNotAudit(createActivityQuotaApplicationModel(e.getApplyType(),e.getQuota(),e.getRemark(),sfaEmployeeInfoModel,
//                    e.getCeoBusinessOrganizationPositionRelation().getOrganizationId(),Integer.valueOf(sfaEmployeeInfoModel.getMemberKey().toString()),person));
//        });
//
//
//        Boolean isSuccess = activityQuotaConnectorUtil.batchGrant(list);
//        if(!isSuccess){
//            throw new ApplicationException("金币发放失败");
//        }
//
//        // 合伙人额度扣减
//        list.stream().filter(f -> f.getApplyType() == 14 || f.getApplyType() == 27).forEach(e -> {
//            // 合伙人额度增加
//            UpdateBatchSaleAmountModel model = new UpdateBatchSaleAmountModel();
//            model.setAmount(e.getAuditAmount());
//            model.setApplyType(e.getApplyType());
//            model.setFlag(1);
//            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
//            model.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
//            model.setYearMonthStart(e.getYearMonthStart());
//            model.setYearMonthEnd(e.getYearMonthEnd());
//            model.setCompanyName(e.getCompanyName());
//            activityQuotaConnectorUtil.updateBatchSaleAmountForSfa(model);
//        });
//    }


}
