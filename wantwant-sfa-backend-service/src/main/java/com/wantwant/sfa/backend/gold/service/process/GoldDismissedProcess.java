package com.wantwant.sfa.backend.gold.service.process;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessRecordEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/08/上午10:29
 */
@Component
@Slf4j
public class GoldDismissedProcess implements Consumer<GoldProcessDto> {

    @Autowired
    private SfaGoldProcessMapper sfaGoldProcessMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;

    @Override
    public void accept(GoldProcessDto goldProcessDto) {
        log.info("【旺金币审核驳回】dto:{}",goldProcessDto);
        // 根据申请ID获取处理流程
        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", goldProcessDto.getAppId()));
        if(Objects.isNull(sfaGoldProcessEntity)){
            throw new ApplicationException("流程记录获取失败");
        }

        // 根据处理流程获取当前流程记录ID
        SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity = sfaGoldProcessRecordMapper.selectById(sfaGoldProcessEntity.getProcessRecordId());
        if(sfaGoldProcessRecordEntity.getProcessType().equals(goldProcessDto.getProcessType())
                && sfaGoldProcessRecordEntity.getProcessResult() == GoldProcessResultEnum.FAIL.getStatus()){
            throw new ApplicationException("请勿重复操作");
        }

        // 设置当前流程为驳回
        sfaGoldProcessRecordEntity.setProcessResult(GoldProcessResultEnum.FAIL.getStatus());
        sfaGoldProcessRecordEntity.setProcessUserId(goldProcessDto.getPerson());
        sfaGoldProcessRecordEntity.setProcessTime(LocalDateTime.now());
        sfaGoldProcessRecordEntity.setRemark(goldProcessDto.getRemark());
        sfaGoldProcessRecordMapper.updateById(sfaGoldProcessRecordEntity);


        // 设置流程主表信息
        sfaGoldProcessEntity.setProcessResult(GoldProcessResultEnum.FAIL.getStatus());
        sfaGoldProcessMapper.updateById(sfaGoldProcessEntity);
    }
}
