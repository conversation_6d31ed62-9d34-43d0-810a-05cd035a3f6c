package com.wantwant.sfa.backend.gold.service.process;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.ActivityQuotaValidModel;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldApplyDetailEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessRecordEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.gold.service.IGoldTypeService;
import com.wantwant.sfa.backend.mapper.SfaCustomerMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldApplyDetailMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessRecordMapper;
import com.wantwant.sfa.backend.model.SfaCustomer;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/08/下午2:11
 */
@Component
@Slf4j
public class GoldFinalApplyProcess implements Consumer<GoldProcessDto> {

    @Autowired
    private SfaGoldProcessMapper sfaGoldProcessMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Autowired
    private SfaGoldApplyDetailMapper sfaGoldApplyDetailMapper;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private IGoldTypeService goldTypeService;

    @Override
    @Transactional
    public void accept(GoldProcessDto goldProcessDto) {
        log.info("【旺金币最终审核通过】dto:{}",goldProcessDto);
        // 根据申请ID获取处理流程
        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", goldProcessDto.getAppId()));
        if(Objects.isNull(sfaGoldProcessEntity)){
            throw new ApplicationException("流程记录获取失败");
        }

        // 根据处理流程获取当前流程记录ID
        SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity = sfaGoldProcessRecordMapper.selectById(sfaGoldProcessEntity.getProcessRecordId());
        if(sfaGoldProcessRecordEntity.getProcessType().equals(goldProcessDto.getProcessType()) && sfaGoldProcessRecordEntity.getProcessResult().equals(GoldProcessResultEnum.PASS.getStatus())){
            throw new ApplicationException("请勿重复操作");
        }

        // 修改当前流程的状态为通过
        sfaGoldProcessRecordEntity.setProcessResult(GoldProcessResultEnum.PASS.getStatus());
        sfaGoldProcessRecordEntity.setProcessUserId(goldProcessDto.getPerson());
        sfaGoldProcessRecordEntity.setRemark(goldProcessDto.getRemark());
        sfaGoldProcessRecordEntity.setProcessTime(LocalDateTime.now());
        sfaGoldProcessRecordMapper.updateById(sfaGoldProcessRecordEntity);

        // 修改流程主表的状态为通过
        sfaGoldProcessEntity.setProcessResult(GoldProcessResultEnum.PASS.getStatus());
        sfaGoldProcessMapper.updateById(sfaGoldProcessEntity);

        // 发放造旺币
        send(goldProcessDto);
    }

    private void send(GoldProcessDto goldProcessDto) {
        List<SfaGoldApplyDetailEntity> sfaGoldApplyDetailEntities = sfaGoldApplyDetailMapper.selectList(new QueryWrapper<SfaGoldApplyDetailEntity>()
                .eq("batch_id", goldProcessDto.getAppId())
                .eq("is_delete", 0)
        );
        if(CollectionUtils.isEmpty(sfaGoldApplyDetailEntities)){
            return;
        }

        List<ActivityQuotaValidModel> list = new ArrayList<>();
        sfaGoldApplyDetailEntities.forEach(e -> {
            ActivityQuotaValidModel model = new ActivityQuotaValidModel();
            SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>()
                    .eq("position_id", e.getPositionId())
                    .eq("is_frozen",0)
                    .eq("channel", RequestUtils.getChannel())
            );
            if(Objects.isNull(sfaCustomer)){
                throw new ApplicationException("合伙人信息获取失败");
            }


            Integer appyType = goldTypeService.selectExpensesTypeCodeByExpensesType(e.getExpenseType());
            if(Objects.isNull(appyType)){
                throw new ApplicationException("费用类型获取失败");
            }
            model.setMemberKey(sfaCustomer.getMemberKey());
            model.setAmountType(1L);
            model.setApplyType(appyType);
            model.setAuditAmount(e.getAmount());
            model.setSurplusAmount(e.getAmount());
            model.setAuditor(goldProcessDto.getPerson());
            model.setAuditRemark(goldProcessDto.getRemark());
            model.setStatus(1);
            list.add(model);
        });


        Boolean isSuccess = activityQuotaConnectorUtil.batchGrant(list);
        if(!isSuccess){
            throw new ApplicationException("金币发放失败");
        }

    }
}
