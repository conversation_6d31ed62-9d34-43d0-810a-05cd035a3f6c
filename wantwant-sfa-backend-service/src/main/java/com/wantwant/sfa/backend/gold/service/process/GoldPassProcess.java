package com.wantwant.sfa.backend.gold.service.process;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessRecordEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessEnum;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/08/上午9:49
 */
@Component
@Slf4j
public class GoldPassProcess implements Consumer<GoldProcessDto> {
    @Autowired
    private SfaGoldProcessMapper sfaGoldProcessMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;

    @Override
    public void accept(GoldProcessDto goldProcessDto) {
        log.info("【旺金币审核通过】dto:{}",goldProcessDto);
        // 根据申请ID获取处理流程
        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", goldProcessDto.getAppId()));
        if(Objects.isNull(sfaGoldProcessEntity)){
            throw new ApplicationException("流程记录获取失败");
        }
        // 根据处理流程获取当前流程记录ID
        SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity = sfaGoldProcessRecordMapper.selectById(sfaGoldProcessEntity.getProcessRecordId());
        if(!sfaGoldProcessRecordEntity.getProcessType().equals(goldProcessDto.getProcessType())){
            throw new ApplicationException("请勿重复操作");
        }
        // 修改当前流程状态
        sfaGoldProcessRecordEntity.setProcessResult(goldProcessDto.getResult());
        sfaGoldProcessRecordEntity.setProcessUserId(goldProcessDto.getPerson());
        sfaGoldProcessRecordEntity.setRemark(goldProcessDto.getRemark());
        sfaGoldProcessRecordEntity.setProcessTime(LocalDateTime.now());
        sfaGoldProcessRecordMapper.updateById(sfaGoldProcessRecordEntity);

        // 创建新的流程
        SfaGoldProcessRecordEntity nextRecord = createNextRecord(sfaGoldProcessRecordEntity);

        // 绑定流程记录之间的关系
        sfaGoldProcessRecordEntity.setNextRecordId(nextRecord.getId());
        sfaGoldProcessRecordMapper.updateById(sfaGoldProcessRecordEntity);

        // 修改流程信息
        sfaGoldProcessEntity.setProcessType(nextRecord.getProcessType());
        sfaGoldProcessEntity.setProcessRecordId(nextRecord.getId());
        sfaGoldProcessEntity.setProcessResult(nextRecord.getProcessResult());
        sfaGoldProcessMapper.updateById(sfaGoldProcessEntity);
    }

    private SfaGoldProcessRecordEntity createNextRecord(SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity) {
        SfaGoldProcessRecordEntity nextProcessRecordEntity = new SfaGoldProcessRecordEntity();
        nextProcessRecordEntity.setCreateTime(LocalDateTime.now());
        nextProcessRecordEntity.setProcessType(GoldProcessEnum.findNext(sfaGoldProcessRecordEntity.getProcessType(),true).getType());
        nextProcessRecordEntity.setProcessId(sfaGoldProcessRecordEntity.getProcessId());
        nextProcessRecordEntity.setPrevRecordId(sfaGoldProcessRecordEntity.getId());
        nextProcessRecordEntity.setProcessResult(GoldProcessResultEnum.PROCESS.getStatus());
        sfaGoldProcessRecordMapper.insert(nextProcessRecordEntity);
        return nextProcessRecordEntity;
    }
}
