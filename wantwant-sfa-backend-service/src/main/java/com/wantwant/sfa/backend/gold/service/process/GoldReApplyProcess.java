package com.wantwant.sfa.backend.gold.service.process;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.gold.dto.GoldProcessDto;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessEntity;
import com.wantwant.sfa.backend.gold.entity.SfaGoldProcessRecordEntity;
import com.wantwant.sfa.backend.gold.enums.GoldProcessEnum;
import com.wantwant.sfa.backend.gold.enums.GoldProcessResultEnum;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessMapper;
import com.wantwant.sfa.backend.mapper.gold.SfaGoldProcessRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/08/上午10:38
 */
@Component
@Slf4j
public class GoldReApplyProcess implements Consumer<GoldProcessDto> {
    @Autowired
    private SfaGoldProcessMapper sfaGoldProcessMapper;
    @Autowired
    private SfaGoldProcessRecordMapper sfaGoldProcessRecordMapper;


    @Override
    public void accept(GoldProcessDto goldProcessDto) {
        log.info("【旺金币审核重新提交】dto:{}",goldProcessDto);
        // 根据申请ID获取处理流程
        SfaGoldProcessEntity sfaGoldProcessEntity = sfaGoldProcessMapper.selectOne(new QueryWrapper<SfaGoldProcessEntity>().eq("batch_id", goldProcessDto.getAppId()));
        if(Objects.isNull(sfaGoldProcessEntity)){
            throw new ApplicationException("流程记录获取失败");
        }

        // 根据处理流程获取当前流程记录ID
        SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity = sfaGoldProcessRecordMapper.selectById(sfaGoldProcessEntity.getProcessRecordId());
        if(!sfaGoldProcessRecordEntity.getProcessType().equals(goldProcessDto.getProcessType())){
            throw new ApplicationException("请勿重复操作");
        }

        // 创建新的流程记录 processType = 1, processResult = 1
        SfaGoldProcessRecordEntity step1 = createNextRecord(sfaGoldProcessRecordEntity,GoldProcessEnum.APPLY.getType(),GoldProcessResultEnum.PASS.getStatus(),null,sfaGoldProcessRecordEntity.getId(),goldProcessDto.getPerson());
        // 设置当前流程的下一节点为step1
        sfaGoldProcessRecordEntity.setNextRecordId(step1.getId());
        sfaGoldProcessRecordMapper.updateById(sfaGoldProcessRecordEntity);
        // 创建新的流程记录 processType = 2, processResult = 0
        SfaGoldProcessRecordEntity step2 = createNextRecord(step1,GoldProcessEnum.findNext(GoldProcessEnum.FINANCE.getType(),true).getType(),GoldProcessResultEnum.PROCESS.getStatus(),null,step1.getId(),null);
        // 设置step1 的下级节点
        step1.setNextRecordId(step2.getId());
        sfaGoldProcessRecordMapper.updateById(step1);


        // 设置流程主表信息
        sfaGoldProcessEntity.setProcessRecordId(step2.getId());
        sfaGoldProcessEntity.setProcessResult(step2.getProcessResult());
        sfaGoldProcessEntity.setProcessType(step2.getProcessType());
        sfaGoldProcessMapper.updateById(sfaGoldProcessEntity);
    }




    private SfaGoldProcessRecordEntity createNextRecord(SfaGoldProcessRecordEntity sfaGoldProcessRecordEntity,int processType,int processResult, Long nextId, Long prevId, String person) {
        SfaGoldProcessRecordEntity nextProcessRecordEntity = new SfaGoldProcessRecordEntity();
        nextProcessRecordEntity.setCreateTime(LocalDateTime.now());
        nextProcessRecordEntity.setProcessType(processType);
        nextProcessRecordEntity.setProcessId(sfaGoldProcessRecordEntity.getProcessId());
        nextProcessRecordEntity.setPrevRecordId(prevId);
        nextProcessRecordEntity.setNextRecordId(nextId);
        nextProcessRecordEntity.setProcessUserId(person);
        if(StringUtils.isNotBlank(person)){
            nextProcessRecordEntity.setProcessTime(LocalDateTime.now());
        }
        nextProcessRecordEntity.setProcessResult(processResult);
        sfaGoldProcessRecordMapper.insert(nextProcessRecordEntity);
        return nextProcessRecordEntity;
    }
}
