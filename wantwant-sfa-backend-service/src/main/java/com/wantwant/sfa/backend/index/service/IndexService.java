package com.wantwant.sfa.backend.index.service;

import com.wantwant.sfa.backend.index.vo.DataOverviewVo;
import com.wantwant.sfa.backend.index.vo.NextAchievementVo;
import com.wantwant.sfa.backend.index.vo.PromiseVo;
import com.wantwant.sfa.backend.index.vo.UserInfoVo;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/26/上午9:20
 */
public interface IndexService {
    /**
     * 获取登陆人姓名
     *
     * @param person
     * @return
     */
    UserInfoVo getUserName(String person);


    /**
     * 获取承诺信息
     *
     * @param person
     * @param orgCode
     * @return
     */
    PromiseVo getPromise(String person, String orgCode);

    /**
     * 获取数据概览
     *
     * @param person
     * @param orgCode
     * @return
     */
    DataOverviewVo getDataOverview(String person, String orgCode);

    /**
     * 获取下属目标达成情况
     *
     * @param orgCode
     * @return
     */
    List<NextAchievementVo> getNextAchievement(String orgCode,String person,String orderField,String orderType);

}
