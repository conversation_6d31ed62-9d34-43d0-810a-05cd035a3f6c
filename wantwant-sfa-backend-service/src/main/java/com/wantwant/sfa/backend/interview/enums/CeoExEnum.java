package com.wantwant.sfa.backend.interview.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/17/上午10:35
 */
public enum CeoExEnum {
    // 0-全职合伙人、1-兼职合伙人 、2-造旺总监 、3-企业合伙人、4-区域经理 、5-承揽合伙人、6-战区督导、7-省区总监、8-全职业务BD、9-承揽业务BD、10-兼职业务BD、11-大区总监
    PART_TIME_CEO("兼职合伙人",1,2,1,1,"branch"),
    CEO("全职合伙人",1,1,1,0,"branch"),
    BUSINESS_CEO("企业合伙人",2,2,1,3,"branch"),
    CONTRACT_CEO("承揽合伙人",3,2,1,5,"branch"),
    CITY_MANAGER("区域经理",1,1,4,4,"department"),
    MANAGER("区域总监",1,1,2,2,"company"),
    AREA_MANAGER("战区督导",1,1,3,6,"area"),
    VAREA_MANAGER("大区总监",1,1,5,11,"varea"),
    PROVINCE_MANAGER("省区总监",1,1,6,7,"province"),
    BUSINESS_BD("全职业务BD",6,1,7,8,"branch"),
    BUSINESS_BD_CONTRACT("承揽业务BD",7,2,7,9,"branch"),
    BUSINESS_BD_PART_TIME("兼职业务BD",6,2,7,10,"branch"),
    DIRECT_BUSINESS_CEO("直营合伙人",8,2,1,50,"branch");

    private String name;

    private int ceoType;

    private int position;

    private int jobsType;

    private int ex1;

    private String organizationType;

    public static CeoExEnum findByEx1(Integer ex1) {
        CeoExEnum[] values = CeoExEnum.values();
        for(CeoExEnum ceoExEnum: values){
            if(ceoExEnum.getEx1() == ex1){
                return ceoExEnum;
            }
        }
        return null;
    }

    public static Integer findEx1ByPositionTypeId(Integer positionTypeId) {
        if(positionTypeId == 1){
            return AREA_MANAGER.getEx1();
        }else if(positionTypeId == 2){
            return MANAGER.getEx1();
        }else if(positionTypeId == 10){
            return CITY_MANAGER.getEx1();
        }else if(positionTypeId == 11){
            return PROVINCE_MANAGER.getEx1();
        }else if(positionTypeId == 12){
            return VAREA_MANAGER.getEx1();
        }else{
            return null;
        }
    }

    public static CeoExEnum findByPosition(Integer ceoType, Integer position, Integer jobsType) {
        CeoExEnum[] values = CeoExEnum.values();
        for(CeoExEnum ceoExEnum: values){
            if(ceoExEnum.getCeoType()== ceoType && ceoExEnum.getJobsType() == jobsType && ceoExEnum.getPosition() == position){
                return ceoExEnum;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCeoType() {
        return ceoType;
    }

    public void setCeoType(int ceoType) {
        this.ceoType = ceoType;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public int getJobsType() {
        return jobsType;
    }

    public void setJobsType(int jobsType) {
        this.jobsType = jobsType;
    }

    public int getEx1() {
        return ex1;
    }

    public void setEx1(int ex1) {
        this.ex1 = ex1;
    }

    public String getOrganizationType() {
        return organizationType;
    }

    public void setOrganizationType(String organizationType) {
        this.organizationType = organizationType;
    }

    CeoExEnum(String name, int ceoType, int jobsType, int position, int ex1,String organizationType) {
        this.name = name;
        this.ceoType = ceoType;
        this.position = position;
        this.jobsType = jobsType;
        this.ex1 = ex1;
        this.organizationType = organizationType;
    }
}
