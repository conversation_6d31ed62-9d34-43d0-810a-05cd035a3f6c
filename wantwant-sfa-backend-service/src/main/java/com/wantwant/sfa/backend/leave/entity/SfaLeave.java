package com.wantwant.sfa.backend.leave.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_leave")
@ApiModel(value = "SfaLeave对象", description = "请假表")
public class SfaLeave extends CommonEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String businessNum;

    private Integer leaveType;

    private Integer applyEmployeeInfoId;

    private Integer businessGroup;

    private LocalDateTime submitTime;

    private LocalDate attendanceStartDate;

    private LocalDate attendanceEndDate;

    private LocalDateTime leaveStartTime;

    private LocalDateTime leaveEndTime;

    private Integer leaveHours;

    private String wantLeaveNum;

    private String wantImage;

    private String leaveReason;

    private String appendix;

    private String appendixName;

    private String image;

    private String imageName;

    private Integer monthAlreadyLeaveHours;

    private Integer auditEmployeeInfoId;

    @TableField(exist = false)
    @ApiModelProperty("申请人工号")
    private String applyEmployeeId;

    @TableField(exist = false)
    @ApiModelProperty("申请人姓名")
    private String applyEmployeeName;

    @TableField(exist = false)
    @ApiModelProperty("审核人工号")
    private String auditEmployeeId;

    @TableField(exist = false)
    @ApiModelProperty("请假类型")
    private String leaveTypeName;

    private Integer leaveStatus;

}
