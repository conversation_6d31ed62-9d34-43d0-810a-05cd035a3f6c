package com.wantwant.sfa.backend.leave.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_leave_cancel_info")
@ApiModel(value="sfaLeaveCancelInfo", description="请假信息审核表")
public class SfaLeaveCancelInfo  implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value="id")
    private Long id;

    @TableField(value="business_num")
    private String businessNum;

    @TableField(value="business_num_origin")
    private String businessNumOrigin;

    @TableField(value="leave_cancel_type")
    private Integer  leaveCancelType;

    @TableField(value="member_key")
    private Long memberKey;

    @TableField(value="submit_time")
    private LocalDateTime submitTime;

    @TableField(value="attendance_start_date")
    private LocalDateTime attendanceStartDate;

    @TableField(value="attendance_end_date")
    private LocalDateTime attendanceEndDate;

    @TableField(value="leave_cancel_start_time")
    private LocalDateTime leaveCancelStartTime;

    @TableField(value="leave_cancel_end_time")
    private LocalDateTime leaveCancelEndTime;

    @TableField(value="leave_cancel_hours")
    private Integer  leaveCancelHours;

    @TableField(value="leave_cancel_reason")
    private String leaveCancelReason;

    @TableField(value="cancel_appendix")
    private String cancelAppendix;

    @TableField(value="leave_cancel_status")
    private Integer leaveCancelStatus;

    @TableField(value="month_already_leave_cancel_hours")
    private Integer  monthAlreadyLeaveCancelHours;

    @TableField(value="create_time")
    private LocalDateTime createTime;

    @TableField(value="update_time")
    private LocalDateTime updateTime;

    @TableField("delete_flag")
    private int deleteFlag;


}
