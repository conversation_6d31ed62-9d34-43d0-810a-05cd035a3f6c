package com.wantwant.sfa.backend.leave.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_leave_info_verify")
@ApiModel(value="SfaLeaveInfoVerify对象", description="请假信息审核表")
public class SfaLeaveInfoVerify  implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value="id")
    private Long id;

    @TableField(value="business_num")
    private String businessNum;

    @TableField(value="leave_type")
    private Integer  leaveType;

    @TableField(value="member_key")
    private Long memberKey;

    @TableField(value="submit_time")
    private LocalDateTime submitTime;

    @TableField(value="attendance_start_date")
    private LocalDateTime attendanceStartDate;

    @TableField(value="attendance_end_date")
    private LocalDateTime attendanceEndDate;

    @TableField(value="leave_start_time")
    private LocalDateTime leaveStartTime;

    @TableField(value="leave_end_time")
    private LocalDateTime leaveEndTime;

    @TableField(value="leave_hours")
    private Integer  leaveHours;

    @TableField(value="leave_reason")
    private String leaveReason;

    @TableField(value="appendix")
    private String appendix;


    @TableField(value="month_already_leave_hours")
    private Integer  monthAlreadyLeaveHours;

    @TableField(value="process_step")
    private Integer processStep;

    @TableField(value="result")
    private Integer result;

    @TableField(value="detail_id")
    private Long detailId;

    @TableField("max_step")
    private int maxStep;

    @TableField("cancel_status")
    private int cancelStatus;

    @TableField(value="create_time")
    private LocalDateTime createTime;

    @TableField(value="update_time")
    private LocalDateTime updateTime;

    @TableField("delete_flag")
    private int deleteFlag;


}
