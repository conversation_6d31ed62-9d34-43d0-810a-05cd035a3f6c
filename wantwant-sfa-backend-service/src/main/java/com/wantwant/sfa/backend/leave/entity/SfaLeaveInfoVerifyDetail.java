package com.wantwant.sfa.backend.leave.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_leave_info_verify_detail")
@ApiModel(value="SfaLeaveInfoVerifyDetail对象", description="请假信息审核详情表")
public class SfaLeaveInfoVerifyDetail {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value = "id")
    private Long id;

    @TableField(value = "verify_id")
    private Long verifyId;

    @TableField(value = "audit_position_id")
    private String auditPositionId;

    @TableField(value = "audit_user_name")
    private String auditUserName;

    @TableField(value = "prev_id")
    private Long prevId;

    @TableField(value = "next_id")
    private Long nextId;

    @TableField(value = "reason")
    private String reason;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "process_time")
    private LocalDateTime processTime;

    @TableField("delete_flag")
    private int deleteFlag;
}
