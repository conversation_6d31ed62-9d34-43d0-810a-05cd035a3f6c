package com.wantwant.sfa.backend.leave.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("sfa_leave_quota")
public class SfaLeaveQuotaEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    private Integer employeeInfoId;
    private Integer quotaYear;
    private Integer quotaType;
    private Integer quotaHours;
    private Integer quotaAlreadyHours;
    private LocalDate quotaStartDate;
    private LocalDate quotaEndDate;
    private Integer quotaStatus;

}
