package com.wantwant.sfa.backend.leave.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_leave_record")
@ApiModel(value = "SfaLeaveRecord对象", description = "请假履历表")
public class SfaLeaveRecord extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long leaveId;

    private String operatorEmployeeId;

    private Integer operatorType;

    private LocalDateTime operatorTime;

    private String operatorReason;
}
