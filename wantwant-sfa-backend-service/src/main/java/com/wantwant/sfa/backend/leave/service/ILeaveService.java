package com.wantwant.sfa.backend.leave.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.leave.request.LeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCancelInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveListRequest;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditRecordVo;
import com.wantwant.sfa.backend.leave.vo.LeaveDetailVo;
import com.wantwant.sfa.backend.leave.vo.LeaveListVo;

import java.util.List;

public interface ILeaveService {
    /*
    * @params request 申请申请信息
    */
    void employeeCommitLeaveInfo(LeaveCommitInfoRequest request);

    /*
     * @params request 申请申请信息
     */
    void employeeCancelLeave(List<LeaveCancelInfoRequest> list);



    IPage<LeaveListVo> getLeaveList(LeaveListRequest request);

    List<LeaveAuditRecordVo> getLeaveAuditRecord(String businessNum);

    void leaveAudit(LeaveAuditRequest request);

    /*
    * 销假信息详情
    */
    LeaveDetailVo leaveCancelDetailInfo(String businessNum);
}
