package com.wantwant.sfa.backend.leave.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.sfa.backend.leave.request.LeaveAuditPopupRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveDetailRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveListRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveRevocationRequest;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditPopupVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveCheckVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveListVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveRecordVo;

import java.util.List;

public interface INewLeaveService {

    LeaveAuditPopupVo leaveAuditPopup(LeaveAuditPopupRequest request);

    NewLeaveCheckVo leaveCheck(String person);

    void leaveCommit(NewLeaveCommitInfoRequest request);

    IPage<NewLeaveListVo> leaveList(NewLeaveListRequest request);

    NewLeaveListVo leaveDetail(NewLeaveDetailRequest request);

    List<NewLeaveRecordVo> leaveRecordList(String businessNum);

    void leaveAudit(NewLeaveAuditRequest request);

    void leaveRevocation(NewLeaveRevocationRequest request);

}
