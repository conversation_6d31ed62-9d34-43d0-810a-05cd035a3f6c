package com.wantwant.sfa.backend.leave.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveCancelInfo;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveInfoVerify;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveInfoVerifyDetail;
import com.wantwant.sfa.backend.leave.request.LeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCancelInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveListRequest;
import com.wantwant.sfa.backend.leave.service.ILeaveService;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditRecordVo;
import com.wantwant.sfa.backend.leave.vo.LeaveDetailVo;
import com.wantwant.sfa.backend.leave.vo.LeaveListVo;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.mapper.SfaCustomerMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveCancelInfoMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveInfoVerifyDetailMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveInfoVerifyMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaCustomer;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class LeaveService implements ILeaveService {

    public static final int ATTENDANCE_DAYS = 1;//考勤周期attendance_max_hours
    public static final int ATTENDANCE_MAX_HOURS = 90;//最大小时数


    @Autowired
    private SfaLeaveInfoVerifyMapper leaveInfoVerifyMapper;

    @Autowired
    private SfaLeaveInfoVerifyDetailMapper leaveInfoVerifyDetailMapper;

    @Autowired
    private SfaLeaveCancelInfoMapper leaveCancelInfoMapper;
    
    @Autowired
    private SettingsMapper settingsMapper;

    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;

    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper relationMapper;

    @Autowired
    private SfaEmployeeInfoMapper employeeInfoMapper;

    @Resource
    private ROOTConnectorUtil connectorUtil;

    @Autowired
    private ICheckCustomerService checkCustomerService;


    @Transactional
    @Override
    public void employeeCommitLeaveInfo(LeaveCommitInfoRequest request) {
        log.info("employeeCommitLeaveInfo request:{}",request);
        //申请单号是否重复
        SfaLeaveInfoVerify infoVerify =  leaveInfoVerifyMapper.selectOne(new QueryWrapper<SfaLeaveInfoVerify>().eq("business_num", request.getBusinessNum()).eq("delete_flag", 0));
        if (Objects.nonNull(infoVerify)) {
            throw new ApplicationException("申请单号重复");
        }
        LocalDateTime leaveStartTime = request.getLeaveStartTime();
        LocalDateTime leaveEndTime = request.getLeaveEndTime();
        if (leaveStartTime.compareTo(leaveEndTime) > 0) {
            throw new ApplicationException("请假开始日期大于结束日期");
        }
        ArrayList<LocalDateTime> list = getAttendanceDate(leaveStartTime, leaveEndTime);

        //以开始日期为模板，计算考勤周期(请假不能跨考勤周期)
        LocalDateTime attendanceStartDate = list.get(0);
        LocalDateTime attendanceEndDate = list.get(1);
        //当前申请人在考勤周期内累计申请的小时数,即为本周期内最后一次审批通过的时间(也可以随意获取一条，每条的本月已请假时间是一样的)
        infoVerify = leaveInfoVerifyMapper.getLastestApprovalRecord(attendanceStartDate, attendanceEndDate, request.getMemberKey());
        int monthAlreadyLeaveHours = 0;//当前周期本月已请假小时数
        if(Objects.nonNull(infoVerify)) {
            monthAlreadyLeaveHours = infoVerify.getMonthAlreadyLeaveHours();//已批准
        }
        int maxStep = 3;//默认3级审核
        String settingMaxHours =  settingsMapper.getSfaSettingsByCode("attendance_max_hours");
        int maxHours = ATTENDANCE_MAX_HOURS;
        if(settingMaxHours != null) {
            maxHours = Integer.valueOf(settingMaxHours);
        }
        if(monthAlreadyLeaveHours + request.getLeaveHours() >= maxHours) {
            maxStep = 4;
        }

        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", request.getMemberKey()));
        if(Objects.isNull(sfaCustomer)) {
            throw new ApplicationException("合伙人memberKey不存在");
        }
        //计算流程的审核步骤,默认区域区域经理(后面添加用4指定区域经理审核)
        int processStep = 4;
        CeoBusinessOrganizationPositionRelation relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", sfaCustomer.getPositionId()));
        if (Objects.isNull(relation) || relation.getPositionTypeId() != 3) {
            throw new ApplicationException("合伙人岗位信息不正确");
        }

        //查询上级(区域经理)
        relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", relation.getOrganizationParentId()));

        //区域经理缺岗的时候，直接区域总监
        if(relation.getEmployeeId() == null || CommonUtil.StringUtils.isEmpty(relation.getEmployeeId())) {
            processStep = 1;
            relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", relation.getOrganizationParentId()));
        }

        //区域总监缺岗的时候，直接区域
        if(relation.getEmployeeId() == null || CommonUtil.StringUtils.isEmpty(relation.getEmployeeId())) {
            processStep = 2;
            relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", relation.getOrganizationParentId()));
        }

        //大区缺岗时候，直接
        if(relation.getEmployeeId() == null || CommonUtil.StringUtils.isEmpty(relation.getEmployeeId())) {
            processStep = 3;
            maxStep = 4;
        }

        //以上的操作为了构建SfaLeaveInfoVerify
        SfaLeaveInfoVerify newLeaveInfoVerify  = new SfaLeaveInfoVerify();
        newLeaveInfoVerify.setBusinessNum(request.getBusinessNum());
        newLeaveInfoVerify.setLeaveType(request.getLeaveType());
        newLeaveInfoVerify.setMemberKey(request.getMemberKey());
        newLeaveInfoVerify.setAttendanceStartDate(attendanceStartDate);
        newLeaveInfoVerify.setAttendanceEndDate(attendanceEndDate);
        newLeaveInfoVerify.setLeaveStartTime(request.getLeaveStartTime());
        newLeaveInfoVerify.setLeaveEndTime(request.getLeaveEndTime());
        newLeaveInfoVerify.setLeaveHours(request.getLeaveHours());
        newLeaveInfoVerify.setMonthAlreadyLeaveHours(monthAlreadyLeaveHours);
        newLeaveInfoVerify.setProcessStep(processStep);
        newLeaveInfoVerify.setResult(0);
        newLeaveInfoVerify.setMaxStep(maxStep);
        newLeaveInfoVerify.setCreateTime(LocalDateTime.now());
        newLeaveInfoVerify.setSubmitTime(request.getSubmitTime());
        newLeaveInfoVerify.setLeaveReason(request.getLeaveReason());
        newLeaveInfoVerify.setAppendix(request.getAppendix());
        leaveInfoVerifyMapper.insert(newLeaveInfoVerify);

        //构建SfaLeaveInfoVerifyDetail
        createLeaveInfoVerifyDetailNode(newLeaveInfoVerify,relation);
    }

    @Transactional
    @Override
    public void employeeCancelLeave(List<LeaveCancelInfoRequest> list) {
        log.info("employeeCancelLeave list: {}",list);
        if(CollectionUtils.isEmpty(list)) {
            throw new ApplicationException("销假信息为空");
        }
        //插入表，且更新销假时长
        list.stream().forEach(e->{
            //申请单号是否重复
            SfaLeaveInfoVerify infoVerify =  leaveInfoVerifyMapper.selectOne(new QueryWrapper<SfaLeaveInfoVerify>().eq("business_num", e.getBusinessNumOrigin()).eq("member_key", e.getMemberKey()).eq("delete_flag", 0));
            if (Objects.isNull(infoVerify)) {
                throw new ApplicationException("申请单号不存在");
            }
            //判断是否已经销假
            SfaLeaveCancelInfo exsitCancelInfo = leaveCancelInfoMapper.selectOne(new QueryWrapper<SfaLeaveCancelInfo>().eq("business_num",e.getBusinessNum()).eq("delete_flag", 0));
            if (!Objects.isNull(exsitCancelInfo)) {
                throw new ApplicationException("请假单已被销假");
            }

            //销假的日期应该在请假范围之类
            LocalDateTime leaveCancelStartTime = e.getLeaveCancelStartTime();
            LocalDateTime leaveCancelEndTime = e.getLeaveCancelEndTime();
            if (leaveCancelStartTime.compareTo(leaveCancelEndTime) > 0) {
                throw new ApplicationException("销假开始日期大于结束日期");
            }
            if (leaveCancelStartTime.compareTo(infoVerify.getLeaveStartTime()) < 0) {
                throw new ApplicationException("销假开始日期小于请假开始日期");
            }
            if (leaveCancelEndTime.compareTo(infoVerify.getLeaveEndTime()) > 0) {
                throw new ApplicationException("销假结束日期大于请假结束日期");
            }
            LocalDateTime attendanceStartDate = infoVerify.getAttendanceStartDate();
            LocalDateTime attendanceEndDate = infoVerify.getAttendanceEndDate();
            //计算本周期销假时长
            SfaLeaveCancelInfo lastestLeaveCancelInfo = leaveCancelInfoMapper.getLastestCancelLeave(attendanceStartDate, attendanceEndDate, e.getMemberKey());
            int monthAlreadyCancelLeaveHours = 0;//当前周期本月已请假小时数
            if(Objects.nonNull(lastestLeaveCancelInfo)) {
                monthAlreadyCancelLeaveHours = lastestLeaveCancelInfo.getMonthAlreadyLeaveCancelHours();//已取消
            }
            //构建SfaLeaveCancelInfo插入表
            SfaLeaveCancelInfo leaveCancelInfo = new SfaLeaveCancelInfo();
            leaveCancelInfo.setBusinessNum(e.getBusinessNum());
            leaveCancelInfo.setBusinessNumOrigin(e.getBusinessNumOrigin());
            leaveCancelInfo.setLeaveCancelType(e.getLeaveCancelType());
            leaveCancelInfo.setMemberKey(e.getMemberKey());
            leaveCancelInfo.setSubmitTime(e.getSubmitTime());
            leaveCancelInfo.setAttendanceStartDate(infoVerify.getAttendanceStartDate());
            leaveCancelInfo.setAttendanceEndDate(infoVerify.getAttendanceEndDate());
            leaveCancelInfo.setLeaveCancelStartTime(e.getLeaveCancelStartTime());
            leaveCancelInfo.setLeaveCancelEndTime(e.getLeaveCancelEndTime());
            leaveCancelInfo.setLeaveCancelHours(e.getLeaveCancelHours());
            leaveCancelInfo.setLeaveCancelReason(e.getLeaveCancelReason());
            leaveCancelInfo.setCancelAppendix(e.getCancelAppendix());
            leaveCancelInfo.setLeaveCancelStatus(e.getLeaveCancelStatus());
            leaveCancelInfo.setMonthAlreadyLeaveCancelHours(e.getLeaveCancelHours() + monthAlreadyCancelLeaveHours);//历史加上本次的
            leaveCancelInfo.setCreateTime(LocalDateTime.now());
            leaveCancelInfoMapper.insert(leaveCancelInfo);

            //更新请假状态表的状态
            infoVerify.setCancelStatus(1);
            infoVerify.setUpdateTime(LocalDateTime.now());
            leaveInfoVerifyMapper.updateById(infoVerify);

            //更新合伙人本周期内的请假小时数
            //满足审批通过才会影响请假小时数
            if(infoVerify.getResult() == 1) {
                leaveInfoVerifyMapper.updateAttendanceMonthAlreadyLeaveHours(infoVerify.getAttendanceStartDate(), infoVerify.getAttendanceEndDate(), infoVerify.getMemberKey(), leaveCancelInfo.getLeaveCancelHours(), 2);
            }
        });
    }

    /*
    * 获取请假申请列表
    *
    */
    @Override
    public IPage<LeaveListVo> getLeaveList(LeaveListRequest request) {
        log.info("getLeaveList request:{}", request);
        //分公司和总督导返回所属，总部账号全部返回
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation relation = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);
        if(Objects.isNull(relation)) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        //判断是否稽核账号
        String leaveAccount = settingsMapper.getSfaSettingsByCode("leave_audit");
        int leaveAudit = leaveAccount != null && leaveAccount.contains(request.getPerson()) ? 1: 0;

        Page<LeaveListVo> page = new Page<>(request.getPage(), request.getRows());
        List<LeaveListVo> list = leaveInfoVerifyMapper.queryLeaveList(page, request, leaveAudit, relation.getPositionId());
        if(!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(e->{
                if(e.getLeaveCancelStatus() == 0 || e.getLeaveCancelStatus() == 2) {
                    if (request.getType() == null) {
                        //总督导只有在步骤1且待审核按钮才亮
                        if(relation.getPositionTypeId() == 1 && e.getProcessStep() == 2 && e.getResult() == 0) {
                            e.setBShowBtn(true);
                        }if(relation.getPositionTypeId() == 2 && e.getProcessStep() == 1 && e.getResult() == 0) {
                            e.setBShowBtn(true);
                        }
                        if(relation.getPositionTypeId() == 10 && e.getProcessStep() == 4 && e.getResult() == 0) {
                            e.setBShowBtn(true);
                        }
                    }else {
                        e.setBShowBtn(true);
                    }
                }
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public List<LeaveAuditRecordVo> getLeaveAuditRecord(String businessNum) {
        log.info("getLeaveAuditRecord:{}",businessNum);
        List<LeaveAuditRecordVo> list = new ArrayList<>();
        SfaLeaveInfoVerify leaveInfoVerify =  leaveInfoVerifyMapper.selectOne(new QueryWrapper<SfaLeaveInfoVerify>().eq("business_num",businessNum).eq("delete_flag",0));
        if(Objects.isNull(leaveInfoVerify)) {
            throw new ApplicationException("申请单号不存在");
        }
        SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", leaveInfoVerify.getMemberKey()));
        if(Objects.isNull(sfaCustomer)) {
            throw new ApplicationException("申请人标识不存在");
        }
        SfaEmployeeInfoModel employeeInfoModel = employeeInfoMapper.queryByPositionId(sfaCustomer.getPositionId());
        if(Objects.isNull(employeeInfoModel)) {
            throw new ApplicationException("员工信息不存在");
        }
        SfaLeaveCancelInfo leaveCancelInfo = leaveCancelInfoMapper.selectOne(new QueryWrapper<SfaLeaveCancelInfo>().eq("business_num_origin", leaveInfoVerify.getBusinessNum()).eq("delete_flag", 0));


        //构造申请记录
        LeaveAuditRecordVo leaveCommitRecord = new LeaveAuditRecordVo();
        String position = "-全职合伙人";
        if(employeeInfoModel.getPostType() != null && employeeInfoModel.getPostType() == 2) {
            position = "-兼职合伙人";
        }
        leaveCommitRecord.setTitle("提交申请");
        leaveCommitRecord.setContent(employeeInfoModel.getEmployeeName() + "-" + employeeInfoModel.getMobile() + position);
        leaveCommitRecord.setProcessTime(leaveInfoVerify.getSubmitTime());
        list.add(leaveCommitRecord);

        //审核流程
        List<SfaLeaveInfoVerifyDetail> leaveInfoVerifyDetailList = leaveInfoVerifyDetailMapper.selectList(new QueryWrapper<SfaLeaveInfoVerifyDetail>().eq("verify_id",leaveInfoVerify.getId()).eq("delete_flag", 0).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(leaveInfoVerifyDetailList)) {
            leaveInfoVerifyDetailList.forEach(e->{
                LeaveAuditRecordVo recordVo = new LeaveAuditRecordVo();
                if(leaveInfoVerify.getDetailId().equals(e.getId())) {
                    if(!Objects.isNull(leaveCancelInfo) && leaveCancelInfo.getLeaveCancelStatus() == 1 && leaveInfoVerify.getResult() == 0) {
                        LeaveAuditRecordVo cancelAuditRecordVo = new LeaveAuditRecordVo();
                        cancelAuditRecordVo.setTitle("撤回申请");
                        cancelAuditRecordVo.setContent(leaveCommitRecord.getContent());
                        cancelAuditRecordVo.setProcessTime(leaveInfoVerify.getUpdateTime());
                        cancelAuditRecordVo.setResult(3);//待审核中的整单撤销，展示已撤回
                        cancelAuditRecordVo.setReason(leaveCancelInfo.getLeaveCancelReason());
                        list.add(cancelAuditRecordVo);
                        return;
                    }else {
                        recordVo.setResult(leaveInfoVerify.getResult());
                    }
                }else {
                    recordVo.setResult(1);
                }
                recordVo.setProcessTime(e.getProcessTime());
                recordVo.setReason(e.getReason());
                recordVo.setTitle(getLeaveAuditTitle(e.getAuditPositionId(), leaveInfoVerify.getProcessStep(), employeeInfoModel));
                recordVo.setContent(getLeaveAuditContent(e.getAuditPositionId(), leaveInfoVerify.getProcessStep(), employeeInfoModel));
                list.add(recordVo);
            });
        }
        return list;
    }

    /*
    * 1、判断审核人是否有审核权限
    * 2、当前审核状态是否是待审核
    * 3、更新当前状态、新增节点
    */
    @Transactional
    @Override
    public void leaveAudit(LeaveAuditRequest request) {
        log.info("LeaveAuditRequest request:{}", request);

        if(request.getResult() != 1 && request.getResult() != 2) {
            throw new ApplicationException("审核状态传入不正确");
        }
        SfaLeaveInfoVerify leaveInfoVerify =  leaveInfoVerifyMapper.selectOne(new QueryWrapper<SfaLeaveInfoVerify>().eq("business_num",request.getBusinessNum()).eq("delete_flag",0));
        if(Objects.isNull(leaveInfoVerify)) {
            throw new ApplicationException("申请单号不存在");
        }
        if(leaveInfoVerify.getResult() != 0) {
            throw new ApplicationException("当前客户已审批，请勿重新提交");
        }
        SfaLeaveCancelInfo leaveCancelInfo = leaveCancelInfoMapper.selectOne(new QueryWrapper<SfaLeaveCancelInfo>().eq("business_num_origin", request.getBusinessNum()).eq("delete_flag", 0));
        if(!Objects.isNull(leaveCancelInfo) && leaveCancelInfo.getLeaveCancelStatus() == 1) {//整单销假已撤回
            throw new ApplicationException("申请单号已被撤回，无需审批");
        }
        SfaLeaveInfoVerifyDetail leaveInfoVerifyDetail = leaveInfoVerifyDetailMapper.selectOne(new QueryWrapper<SfaLeaveInfoVerifyDetail>().eq("verify_id", leaveInfoVerify.getId()).isNull("process_time").eq("delete_flag",0));
        if(Objects.isNull(leaveInfoVerifyDetail)) {
            throw new ApplicationException("待审核记录获取失败");
        }

        //操作人信息
        CeoBusinessOrganizationPositionRelation relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("employee_id", request.getPerson()).eq("channel", RequestUtils.getChannel()));
        if (Objects.isNull(relation) || relation.getPositionId() == null) {
            throw new ApplicationException("操作人工号不正确");
        }
        //操作人和当前审批是否匹配
        if(!((leaveInfoVerify.getProcessStep() == 1 && relation.getPositionTypeId() == 2) ||
                (leaveInfoVerify.getProcessStep() == 2 && relation.getPositionTypeId() == 1) ||
                (leaveInfoVerify.getProcessStep() == 3 && relation.getPositionTypeId() == 7) ||
                (leaveInfoVerify.getProcessStep() == 4 && relation.getPositionTypeId() == 10))) {
            throw new ApplicationException("审核人不正确");
        }

        //运营管理账号控制
        String operatorPositionId = null;
        String operatorUserName = null;
        if (leaveInfoVerify.getProcessStep() == 3) {//运营审核账号
            String leaveAudit = settingsMapper.getSfaSettingsByCode("leave_audit");
            boolean operatorAudit = leaveAudit != null && leaveAudit.contains(request.getPerson()) ? true : false;
            if (!operatorAudit) {
                throw new ApplicationException("无运营审核权限");
            }
            //获取稽核人员的工号和姓名
            operatorPositionId = relation.getPositionId();
            operatorUserName = relation.getEmployeeName();
        }

        log.info("请假审批流程==infoVerify:{},verifyDetail:{},{}",leaveInfoVerify.getProcessStep(), leaveInfoVerify.getResult());
        //1：如果是3次审核，则区域经理区域总监审核走此逻辑，如果是4次审核，则区域经理区域总监和总督导通过走此逻辑
        if(request.getResult() == 1 && ((leaveInfoVerify.getMaxStep() == 3 && leaveInfoVerify.getProcessStep() != 2)
                || (leaveInfoVerify.getMaxStep() == 4 && leaveInfoVerify.getProcessStep() != 3))) {//非最后一步的通过流程走此逻辑
            if(relation.getPositionId().equals(leaveInfoVerifyDetail.getAuditPositionId())) {
                //更新流程表
                leaveInfoVerify.setResult(0);
                leaveInfoVerify.setUpdateTime(LocalDateTime.now());
//                leaveInfoVerify.setProcessStep(leaveInfoVerify.getProcessStep() + 1);

                SfaLeaveInfoVerifyDetail nextInfoVerifyDetail = new SfaLeaveInfoVerifyDetail();
                nextInfoVerifyDetail.setVerifyId(leaveInfoVerifyDetail.getVerifyId());
                nextInfoVerifyDetail.setPrevId(leaveInfoVerifyDetail.getId());
                nextInfoVerifyDetail.setCreateTime(LocalDateTime.now());

                if(relation.getPositionTypeId() == 1) {//当前为大区审核
                    leaveInfoVerify.setProcessStep(3);
                }else {
                    CeoBusinessOrganizationPositionRelation parentRelation = getNextAuditPosition(relation);
                    if(Objects.isNull(parentRelation)) {
                        throw new ApplicationException("获取下个审核人信息失败");
                    }
                    if(parentRelation.getPositionTypeId() == 2) {//分公司
                        leaveInfoVerify.setProcessStep(1);
                        nextInfoVerifyDetail.setAuditPositionId(parentRelation.getPositionId());
                        nextInfoVerifyDetail.setAuditUserName(parentRelation.getEmployeeName());
                    }else if(parentRelation.getPositionTypeId() == 1) {//大区
                        if(!CommonUtil.StringUtils.isEmpty(parentRelation.getEmployeeId())) {
                            leaveInfoVerify.setProcessStep(2);
                            nextInfoVerifyDetail.setAuditPositionId(parentRelation.getPositionId());
                            nextInfoVerifyDetail.setAuditUserName(parentRelation.getEmployeeName());
                        }else {//如果大区没有工号，则直接到运营审核
                            leaveInfoVerify.setProcessStep(3);
                            leaveInfoVerify.setMaxStep(4);
                        }
                    }
                }
                leaveInfoVerifyDetailMapper.insert(nextInfoVerifyDetail);
                leaveInfoVerify.setDetailId(nextInfoVerifyDetail.getId());
                leaveInfoVerifyMapper.updateById(leaveInfoVerify);//更新流程表

                //更新前一个节点
                leaveInfoVerifyDetail.setNextId(nextInfoVerifyDetail.getId());
                leaveInfoVerifyDetail.setProcessTime(LocalDateTime.now());
                leaveInfoVerifyDetail.setReason(request.getReason());//暂定读此字段;
                leaveInfoVerifyDetailMapper.updateById(leaveInfoVerifyDetail);
            }else {
                throw new ApplicationException("审批人与操作人不符");
            }
        }else {//驳回或者通过的最后一步
            if(leaveInfoVerify.getProcessStep() == 3) {//更新审核记录表的audit_position_id和audit_user_name
                leaveInfoVerifyDetail.setAuditUserName(operatorUserName);
                leaveInfoVerifyDetail.setAuditPositionId(operatorPositionId);
            }
            leaveInfoVerifyDetail.setProcessTime(LocalDateTime.now());
            leaveInfoVerifyDetail.setReason(request.getReason());
            leaveInfoVerifyDetailMapper.updateById(leaveInfoVerifyDetail);//更新审核记录表

            leaveInfoVerify.setResult(request.getResult());//1是通过 2是驳回
            leaveInfoVerify.setUpdateTime(LocalDateTime.now());
            leaveInfoVerifyMapper.updateById(leaveInfoVerify);
            //更新本周期内，所有的提交记录
            if(request.getResult() == 1) {
                int leaveCancelHours = 0;
                if(!Objects.isNull(leaveCancelInfo) && leaveCancelInfo.getLeaveCancelStatus() == 2) {//部分销假
                    leaveCancelHours = leaveCancelInfo.getLeaveCancelHours();
                }
                leaveInfoVerifyMapper.updateAttendanceMonthAlreadyLeaveHours(leaveInfoVerify.getAttendanceStartDate(), leaveInfoVerify.getAttendanceEndDate(), leaveInfoVerify.getMemberKey(), leaveInfoVerify.getLeaveHours() - leaveCancelHours, 1);
                String settingMaxHours =  settingsMapper.getSfaSettingsByCode("attendance_max_hours");
                int maxHours = ATTENDANCE_MAX_HOURS;
                if(settingMaxHours != null) {
                    maxHours = Integer.valueOf(settingMaxHours);
                }
                leaveInfoVerifyMapper.updatePengingAuditProcessStep(leaveInfoVerify.getAttendanceStartDate(), leaveInfoVerify.getAttendanceEndDate(),leaveInfoVerify.getMemberKey(), maxHours);
            }
            //调用旺铺接口,构建参数
            Map<String, String> map = new HashMap<>();
            map.put("businessNum", request.getBusinessNum());
            map.put("examineState", String.valueOf(request.getResult()));
            map.put("examineTime", leaveInfoVerifyDetail.getProcessTime().format(DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)));
            map.put("rejectReason", request.getReason());
            map.put("operator", leaveInfoVerifyDetail.getAuditUserName());
            connectorUtil.leaveAudit(map);
        }
    }

    /*
     * 获取下个审核人
     *
     */
    private CeoBusinessOrganizationPositionRelation getNextAuditPosition(CeoBusinessOrganizationPositionRelation currentPosition) {
        if(!Objects.isNull(currentPosition) && Objects.isNull(currentPosition.getOrganizationParentId())) {
            return currentPosition;
        }
        CeoBusinessOrganizationPositionRelation parentRelation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", currentPosition.getOrganizationParentId()));
        if(Objects.isNull(parentRelation)) {
            throw new ApplicationException("组织id查找失败");
        }
        if(!CommonUtil.StringUtils.isEmpty(parentRelation.getEmployeeId())){
            return parentRelation;
        }else {
            return getNextAuditPosition(parentRelation);
        }
    }


    /*
    * 根据请假单号查询销假信息
    *
    */
    @Override
    public LeaveDetailVo leaveCancelDetailInfo(String businessNum) {
        log.info("leaveCancelDetailInfo {}", businessNum);
        SfaLeaveCancelInfo leaveCancelInfo = leaveCancelInfoMapper.selectOne(new QueryWrapper<SfaLeaveCancelInfo>().eq("business_num_origin", businessNum).eq("delete_flag", 0));
        if(Objects.isNull(leaveCancelInfo)) {
            return null;
        }
        LeaveDetailVo leaveDetailVo = new LeaveDetailVo();
        leaveDetailVo.setLeaveCancelbusinessNum(leaveCancelInfo.getBusinessNum());
        leaveDetailVo.setLeaveCancelType(leaveCancelInfo.getLeaveCancelType());
        leaveDetailVo.setLeaveCancelStatus(leaveCancelInfo.getLeaveCancelStatus());
        leaveDetailVo.setLeaveCancelStartTime(leaveCancelInfo.getLeaveCancelStartTime());
        leaveDetailVo.setLeaveCancelEndTime(leaveCancelInfo.getLeaveCancelEndTime());
        leaveDetailVo.setLeaveCancelHours(leaveCancelInfo.getLeaveCancelHours());
        leaveDetailVo.setLeaveCancelReason(leaveCancelInfo.getLeaveCancelReason());
        return leaveDetailVo;
    }

    /*
     * 返回类似：西南区-南充分-区域总监-缪波
     * 西南区-督导-严寒冰
     * 总部-稽核
     */
    private String getLeaveAuditTitle(String position, int processStep, SfaEmployeeInfoModel employeeInfoModel) {
        if(Objects.isNull(position)) {//只有总督导的待审核无岗位信息
            return "总部-稽核";
        }else {
            CeoBusinessOrganizationPositionRelation relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", position));
            if(Objects.isNull(relation)) {
                return null;
            }
            if(relation.getPositionTypeId() == 1) {
                return employeeInfoModel.getAreaName() + "-督导-" + relation.getEmployeeName();
            }else if(relation.getPositionTypeId() == 2) {
                return employeeInfoModel.getAreaName() + "-" + employeeInfoModel.getCompanyName() + "-区域总监-" + relation.getEmployeeName();
            }else if(relation.getPositionTypeId() == 7) {
                return "总部-稽核-" + relation.getEmployeeName();
            }else if(relation.getPositionTypeId() == 10) {
                return employeeInfoModel.getAreaName() + "-" + employeeInfoModel.getCompanyName() + "-区域总监-" + "-区域经理-" + relation.getEmployeeName();
            }
            return "";
        }
    }

    /*
     * 返回类似：（00424998）
     * 类似工号
     *
     */
    private String getLeaveAuditContent(String position, int processStep, SfaEmployeeInfoModel employeeInfoModel) {
        if(Objects.isNull(position)) {
            return null;
        }else {
            CeoBusinessOrganizationPositionRelation relation = relationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("position_id", position));
            if(Objects.isNull(relation)) {
                return null;
            }
            return "(" + relation.getEmployeeId() + ")";
        }
    }
    /*
    * 根据请假申请表，创建审核记录表
    */
    private void createLeaveInfoVerifyDetailNode(SfaLeaveInfoVerify leaveInfoVerify, CeoBusinessOrganizationPositionRelation relation) {
        SfaLeaveInfoVerifyDetail leaveInfoVerifyDetail = new SfaLeaveInfoVerifyDetail();
        leaveInfoVerifyDetail.setVerifyId(leaveInfoVerify.getId());
        if(leaveInfoVerify.getProcessStep() != 3) {
            leaveInfoVerifyDetail.setAuditPositionId(relation.getPositionId());
            leaveInfoVerifyDetail.setAuditUserName(relation.getEmployeeName());
        }
        leaveInfoVerifyDetail.setCreateTime(LocalDateTime.now());
        leaveInfoVerifyDetailMapper.insert(leaveInfoVerifyDetail);
        leaveInfoVerify.setDetailId(leaveInfoVerifyDetail.getId());
        leaveInfoVerifyMapper.updateById(leaveInfoVerify);
    }

    /*
     * 根据请假日期返回所在的考勤周期
     */
    private ArrayList<LocalDateTime> getAttendanceDate(LocalDateTime leaveStartTime, LocalDateTime leaveEndTime) {
        ArrayList<LocalDateTime> list = new ArrayList<>();
        LocalDateTime attendanceStartDate = null;
        LocalDateTime attendanceEndDate = null;

        attendanceStartDate = LocalDateTime.of(leaveStartTime.getYear(), leaveStartTime.getMonth(),ATTENDANCE_DAYS, 0, 0,0);
        if(leaveStartTime.getMonth() == Month.DECEMBER) {
            attendanceEndDate = attendanceStartDate.withYear(attendanceStartDate.getYear() + 1).withMonth(1);
        }else {
            attendanceEndDate = attendanceStartDate.withMonth(attendanceStartDate.getMonthValue() + 1);
        }
        list.add(attendanceStartDate);
        list.add(attendanceEndDate);
        return list;
    }




}
