package com.wantwant.sfa.backend.map.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_map_realtime_positioning")
@ApiModel(value = "SfaMapRealtimePositioning对象", description = "动态定位表")
public class SfaMapRealtimePositioning extends CommonEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private LocalDate positioningDate;

    private LocalDateTime positioningTime;

    private String employeeId;

    private Integer employeeInfoId;

    private String province;

    private String city;

    private String district;

    private String address;

    private String longitude;

    private String latitude;

}
