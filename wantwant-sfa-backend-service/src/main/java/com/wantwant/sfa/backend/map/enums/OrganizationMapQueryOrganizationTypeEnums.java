package com.wantwant.sfa.backend.map.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.map.enums
 * @Description:
 * @Date: 2024/11/21 9:06
 */
@Getter
@AllArgsConstructor
public enum OrganizationMapQueryOrganizationTypeEnums {
    AREA("area","按照战区展示"),
    VAREA("varea","按照大区展示"),
    PROVINCE("province","按照省区展示"),
    COMPANY("company","按照分公司展示"),
    DEPARTMENT("department","按照营业所展示"),
    ;
    private String queryType;
    private String queryDesc;
}
