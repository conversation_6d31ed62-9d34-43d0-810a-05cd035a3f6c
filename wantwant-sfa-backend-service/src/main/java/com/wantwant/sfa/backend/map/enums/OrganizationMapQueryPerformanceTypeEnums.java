package com.wantwant.sfa.backend.map.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.map.enums
 * @Description:
 * @Date: 2024/11/21 9:07
 */
@Getter
@AllArgsConstructor
public enum OrganizationMapQueryPerformanceTypeEnums {

    PERFORMANCE("performance","按照业绩展示"),
    LINE("line","按照line展示"),
    SPU("spu","按照spu展示"),
    SKU("sku","按照sku展示"),
    ;
    private String queryType;
    private String queryDesc;
}
