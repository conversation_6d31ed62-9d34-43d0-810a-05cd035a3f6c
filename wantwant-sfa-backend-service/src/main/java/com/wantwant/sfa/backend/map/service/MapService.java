package com.wantwant.sfa.backend.map.service;

import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.map.request.MapListRequest;
import com.wantwant.sfa.backend.map.request.PeriodMapListRequest;
import com.wantwant.sfa.backend.map.request.RealtimePositioningCommitRequest;
import com.wantwant.sfa.backend.map.request.RealtimePositioningListRequest;
import com.wantwant.sfa.backend.map.vo.MapListVo;
import com.wantwant.sfa.backend.map.vo.RealtimePositioningVo;

import java.util.List;

public interface MapService {

    List<MapListVo> attendanceList(MapListRequest request);

    List<MapListVo> attendanceListForPeriod(PeriodMapListRequest request);

    List<MapListVo> attendanceListForSome(MapListRequest request);

    void realtimePositioningCommit(RealtimePositioningCommitRequest request);

    List<RealtimePositioningVo> realtimePositioningList(RealtimePositioningListRequest request);

}
