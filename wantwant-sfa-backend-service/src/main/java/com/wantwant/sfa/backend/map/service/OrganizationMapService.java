package com.wantwant.sfa.backend.map.service;

import com.wantwant.sfa.backend.map.request.OrganizationMapAchievementRequest;
import com.wantwant.sfa.backend.map.request.OrganizationMapRequest;
import com.wantwant.sfa.backend.map.vo.OrganizationMapAchievementCompareVo;
import com.wantwant.sfa.backend.map.vo.OrganizationMapAchievementDistributionVo;
import com.wantwant.sfa.backend.map.vo.OrganizationMapProductVo;
import com.wantwant.sfa.backend.map.vo.OrganizationMapVo;

import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.map.service
 * @Description:
 * @Date: 2024/11/20 18:20
 */
public interface OrganizationMapService {
    OrganizationMapVo getOrganizationMap(OrganizationMapRequest request);

    List<OrganizationMapProductVo> getOrganizationMapProductInfo(String queryType);

    List<OrganizationMapAchievementCompareVo> getOrganizationMapAchievementCompare(OrganizationMapAchievementRequest request);

    OrganizationMapAchievementDistributionVo getOrganizationMapAchievementDistribution(OrganizationMapAchievementRequest request);
}
