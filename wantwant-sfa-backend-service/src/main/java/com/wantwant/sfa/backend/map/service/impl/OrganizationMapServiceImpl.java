package com.wantwant.sfa.backend.map.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.dict.entity.SfaDictCode;
import com.wantwant.sfa.backend.dict.service.impl.DictCodeServiceImpl;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.enums.RankingListPositionEnums;
import com.wantwant.sfa.backend.map.enums.OrganizationMapQueryOrganizationTypeEnums;
import com.wantwant.sfa.backend.map.enums.OrganizationMapQueryPerformanceTypeEnums;
import com.wantwant.sfa.backend.map.request.OrganizationMapAchievementRequest;
import com.wantwant.sfa.backend.map.request.OrganizationMapRequest;
import com.wantwant.sfa.backend.map.request.RealtimePositioningListRequest;
import com.wantwant.sfa.backend.map.service.OrganizationMapService;
import com.wantwant.sfa.backend.map.vo.*;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.map.OrganizationMapMapper;
import com.wantwant.sfa.backend.mapper.map.SfaMapRealtimePositioningMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.organization.vo.OrganizationAllChildInfoVo;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.DynamicDataGroupingUtils;
import com.wantwant.sfa.backend.util.RealTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.map.service.impl
 * @Description:
 * @Date: 2024/11/20 18:26
 */
@Service
@Slf4j
public class OrganizationMapServiceImpl implements OrganizationMapService {

    @Resource
    private OrganizationMapMapper organizationMapMapper;

    @Resource
    private DictCodeServiceImpl dictCodeServiceImpl;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;

    @Resource
    private RealTimeUtils realTimeUtils;

    @Resource
    private SfaMapRealtimePositioningMapper sfaMapRealtimePositioningMapper;

    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    /**
     * 岗位都展示兼岗数据，以主岗的岗位类型层级为标准
     * 总部--展示战区层级
     * 战区--展示战区下所有大区层级
     * 大区--展示大区下所有分公司
     * 分公司--展示分公司下所有的营业所
     * 营业所--展示营业所
     * @param request
     * @return
     */
    @Override
    public OrganizationMapVo getOrganizationMap(OrganizationMapRequest request) {
        log.info("getOrganizationMap request=[{}]",request);

        OrganizationMapVo organizationMapVo = new OrganizationMapVo();
        request.setNearMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<OrganizationMapPerformanceVo> mapVos;
        //如果岗位id为空 则展示默认数据
        if(StringUtils.isBlank(request.getOrganizationId())&&CollectionUtil.isEmpty(request.getOrganizationIds())){
            if(RankingListPositionEnums.ZB.getOrganizationType().equals(RequestUtils.getLoginInfo().getOrganizationType())){
                request.setOrganizationType(RankingListPositionEnums.ZB.getOrganizationType());
            }else {
                //非总部查询登陆人员当前产品组所有岗位数据
                List<SfaPositionRelationEntity> positionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                            .eq(SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroup())
                            .eq(SfaPositionRelationEntity::getEmpId, request.getEmployeeId())
                            .eq(SfaPositionRelationEntity::getStatus,1)
                            .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                            .ge(SfaPositionRelationEntity::getEndValidDate, LocalDateTime.now())
                            .orderByAsc(SfaPositionRelationEntity::getPartTime)
                );
                //岗位信息查询失败直接返回空
                if(CollectionUtil.isEmpty(positionRelationList)){
                    return organizationMapVo;
                }
                List<String> organizationIds = positionRelationList.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());
                request.setOrganizationIds(organizationIds);
                //取第一位的组织作为组织类型
                request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationIds().get(0)));
            }
        }else if(CollectionUtil.isNotEmpty(request.getOrganizationIds())){
            //补充组织类型
            request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationIds().get(0)));
            //判断展示是否是自己的下级  若是下级则不用改变 若是上级则查当前自己的平级
            if(RankingListPositionEnums.compareAndGetInterval(request.getOrganizationType(), request.getQueryOrganizationType())>0){
                request.setQueryOrganizationType(request.getOrganizationType());
            }
        }else {
            request.setOrganizationType(organizationMapper.getOrganizationType(request.getOrganizationId()));
        }
        //补充数据
        if(StringUtils.isNotBlank(request.getOrganizationId())&&CollectionUtil.isEmpty(request.getOrganizationIds())){
            request.setOrganizationIds(Arrays.asList(request.getOrganizationId()));
        }
        //如果无查询类型 则默认查业绩层级
        if(StringUtils.isBlank(request.getQueryPerformanceType())){
            request.setQueryPerformanceType(OrganizationMapQueryPerformanceTypeEnums.PERFORMANCE.getQueryType());
        }
        //如果无查询层级 则默认查询自己下一级
        if(StringUtils.isBlank(request.getQueryOrganizationType())){
            if(RankingListPositionEnums.ZB.getOrganizationType().equals(request.getOrganizationType())){
                request.setQueryOrganizationType(OrganizationMapQueryOrganizationTypeEnums.AREA.getQueryType());
            }else if(RankingListPositionEnums.AREA.getOrganizationType().equals(request.getOrganizationType())){
                request.setQueryOrganizationType(OrganizationMapQueryOrganizationTypeEnums.VAREA.getQueryType());
            }else if(RankingListPositionEnums.V_AREA.getOrganizationType().equals(request.getOrganizationType())){
                request.setQueryOrganizationType(OrganizationMapQueryOrganizationTypeEnums.PROVINCE.getQueryType());
            }else if(RankingListPositionEnums.PROVINCE.getOrganizationType().equals(request.getOrganizationType())){
                request.setQueryOrganizationType(OrganizationMapQueryOrganizationTypeEnums.COMPANY.getQueryType());
            }else if(RankingListPositionEnums.COMPANY.getOrganizationType().equals(request.getOrganizationType())){
                request.setQueryOrganizationType(OrganizationMapQueryOrganizationTypeEnums.DEPARTMENT.getQueryType());
            }else if(RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(request.getOrganizationType())){
                request.setQueryOrganizationType(OrganizationMapQueryOrganizationTypeEnums.DEPARTMENT.getQueryType());
            }
        }
        //参数校验
        validatedRequest(request);
        //岗位类型必须 包括 查询的组织类型 否则直接返回空
        if(RankingListPositionEnums.compareAndGetInterval(request.getOrganizationType(), request.getQueryOrganizationType())>0){
            return organizationMapVo;
        }
        //根据条件展示数据
        if(OrganizationMapQueryPerformanceTypeEnums.PERFORMANCE.getQueryType().equals(request.getQueryPerformanceType())){
            //根据业绩展示
            mapVos = organizationMapMapper.queryPerformanceOrganizationMapInfo(request);
        }else {
            //根据line spu sku
            mapVos = organizationMapMapper.queryProductOrganizationMapInfo(request);
        }
        /**
         * 配置岗位对应的区域
         * 战区-省
         * 大区-省
         * 省区-城市
         * 分公司 -城市
         * 营业所-区/县级(颗粒图暂不支持改为)--城市
         */
        //市辖区地域code替换
        List<SfaDictCode> specialRegionList = dictCodeServiceImpl.getListByClassCd(DictCodeConstants.CLASSCD_SPEICAL_REGION);
        Map<String, SfaDictCode> specialRegionInfoMap;
        //地区转换
        Map<String, String> specialRegionMap;
        if(CollectionUtil.isNotEmpty(specialRegionList)){
            specialRegionInfoMap = specialRegionList.stream().collect(Collectors.toMap(SfaDictCode::getItemValue, Function.identity(),(x,y)->x));
            specialRegionMap = specialRegionList.stream().collect(Collectors.toMap(SfaDictCode::getItemValue, SfaDictCode::getItemContent,(x,y)->x));
        }else {
            specialRegionMap = Maps.newHashMap();
            specialRegionInfoMap = Maps.newHashMap();
        }
        if(CollectionUtil.isNotEmpty(mapVos)){
            //热力图查询
            List<DynamicDataGroupingUtils.DataItem> dataItemList = mapVos.stream().map(mapVo -> {
                DynamicDataGroupingUtils.DataItem dataItem = new DynamicDataGroupingUtils.DataItem();
                dataItem.setId(mapVo.getOrganizationId());
                if("population".equals(request.getSortName())){
                    dataItem.setValue(mapVo.getPopulation());
                }else if("populationPerformanceAvg".equals(request.getSortName())){
                    dataItem.setValue(mapVo.getPopulationPerformanceAvg());
                }else {
                    dataItem.setValue(mapVo.getPerformance());
                }
                return dataItem;
            }).collect(Collectors.toList());
            //热力值分组数据处理结果
            DynamicDataGroupingUtils.DynamicDataGroupingResultDto resultDto = DynamicDataGroupingUtils.dynamicBucketGrouping(dataItemList);
            Map<String, Integer> dataItemsMap;
            if(Objects.nonNull(resultDto)){
                //热力值分组数据处理结果-分组区间数据
                List<DynamicDataGroupingUtils.IntervalRangeWithGroup> intervalRangesWithGroup = resultDto.getIntervalRangeWithGroupList();
                List<OrganizationMapIntervalRangeWithGroupVo> rangeWithGroupVos = BeanUtil.copyToList(intervalRangesWithGroup, OrganizationMapIntervalRangeWithGroupVo.class);
                organizationMapVo.setRangeWithGroupVos(rangeWithGroupVos);
                //热力值分组数据处理结果-数据标识结果
                List<DynamicDataGroupingUtils.DataItem> dataItems = resultDto.getDataItemList();
                dataItemsMap = dataItems.stream().collect(Collectors.toMap(DynamicDataGroupingUtils.DataItem::getId, DynamicDataGroupingUtils.DataItem::getGroupCode));
            } else {
                dataItemsMap = new HashMap<>();
            }

            //动态定位查询--查询每个人最新的一条数据
            List<String> employeeIds = mapVos.stream().filter(mapVo->StringUtils.isNotBlank(mapVo.getEmployeeId())).map(OrganizationMapPerformanceVo::getEmployeeId).collect(Collectors.toList());
            Map<String,RealtimePositioningVo> realtimePositioningVoMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(employeeIds)){
                RealtimePositioningListRequest realtimePositioningListRequest = new RealtimePositioningListRequest();
                realtimePositioningListRequest.setEmployeeIds(employeeIds);
                realtimePositioningListRequest.setPositioningStartDate(LocalDate.now());
                realtimePositioningListRequest.setPositioningEndDate(LocalDate.now());
                List<RealtimePositioningVo> realtimePositioningVos = sfaMapRealtimePositioningMapper.selectNewestRealtimePositioningAllList(realtimePositioningListRequest);
                if(CollectionUtil.isNotEmpty(realtimePositioningVos)){
                    realtimePositioningVoMap = realtimePositioningVos.stream().collect(Collectors.toMap(RealtimePositioningVo::getOrganizationId,Function.identity(),(x1, x2) -> x1.getPositioningTime().isBefore(x2.getPositioningTime()) ? x2 : x1));
                }
            }
            //区域查询
            List<OrganizationMapRegionVo> organizationMapRegionVos = organizationMapMapper.queryOrganizationMapRegionInfo(request);
            Map<String, OrganizationMapRegionVo> mapRegionVoMap = organizationMapRegionVos.stream().collect(Collectors.toMap(OrganizationMapRegionVo::getOrganizationId, Function.identity()));
            Map<String, RealtimePositioningVo> finalRealtimePositioningVoMap = realtimePositioningVoMap;
            Map<String, String> finalSpecialRegionMap = specialRegionMap;
            mapVos.stream().forEach(mapVo->{
                OrganizationMapRegionVo mapRegionVo = mapRegionVoMap.get(mapVo.getOrganizationId());
                String regionCodes = mapRegionVo.getRegionCodes();
                if(StringUtils.isNotBlank(regionCodes)){
                    List<String> regionList = Arrays.asList(StringUtils.split(mapRegionVo.getRegionCodes(), ","));
                    regionList = regionList.stream().map(region-> {
                        String result = StringUtils.rightPad(region, 6, "0");
                        String replaceResult = finalSpecialRegionMap.get(result);
                        if(StringUtils.isNotBlank(replaceResult)){
                            return replaceResult;
                        }
                        return result;
                    }).distinct().collect(Collectors.toList());
                    for (int i = 0; i < regionList.size(); i++) {
                        String districtCode = regionList.get(i);
                        if (specialRegionMap.containsKey(districtCode)) {
                            regionList.set(i, specialRegionMap.get(districtCode));
                        }
                    }
                    mapVo.setRegionCodes(regionList);
                }
                RealtimePositioningVo positioningVo = finalRealtimePositioningVoMap.get(mapVo.getOrganizationId());
                if(Objects.nonNull(positioningVo)){
                    mapVo.setLatitude(positioningVo.getLatitude());
                    mapVo.setLongitude(positioningVo.getLongitude());
                }
                mapVo.setGroupCode(dataItemsMap.get(mapVo.getOrganizationId()));
            });
        }else {
            //补充空数据
            organizationMapVo.setRangeWithGroupVos(CollectionUtil.newArrayList());
        }
        /**
         * 12.9直接返回当前组织下级组织信息
         */
        organizationMapVo.setMapOutLineGroupVos(generateMapOutLineGroup(request,mapVos));
        /**
         * 查询各个地区的数据
         * 战区-省
         * 大区-省
         * 省区-城市
         * 分公司 -城市
         * 营业所-区/县级(颗粒图暂不支持改为)--城市
         */
        List<OrganizationMarketDetailPerformanceMapVo> marketDetailPerformanceMapVos = organizationMapMapper.queryMarketDetailPerformanceOrganizationMapInfo(request);
        if(CollectionUtil.isNotEmpty(marketDetailPerformanceMapVos)){
            Map<String, SfaDictCode> finalSpecialRegionInfoMap = specialRegionInfoMap;
            marketDetailPerformanceMapVos.stream().forEach(marketDetailPerformanceMapVo->{
                SfaDictCode regionInfo = finalSpecialRegionInfoMap.get(marketDetailPerformanceMapVo.getMarketCode());
                if(Objects.nonNull(regionInfo)){
                    marketDetailPerformanceMapVo.setMarketCode(regionInfo.getItemContent());
                    marketDetailPerformanceMapVo.setMarketName(regionInfo.getItemInfo());
                }
            });
        }
        organizationMapVo.setMarketDetailPerformanceMapVos(marketDetailPerformanceMapVos);
        organizationMapVo.setMapPerformanceVos(mapVos);
        return organizationMapVo;
    }

    /**
     * 地图轮廓——新增分类 先按照大区分类 再按照groupCode分类
     * 1、大区及以下 按照大区汇总
     * 2、战区直接不分类
     * @param mapVos
     * @return
     */
    private List<OrganizationMapOutLineGroupVo> generateMapOutLineGroup(OrganizationMapRequest request,List<OrganizationMapPerformanceVo> mapVos){
        if(RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(request.getOrganizationType())){
            return CollectionUtil.newArrayList();
        }else {
            List<OrganizationAllChildInfoVo> organizationIdTimeZones;
            if(RankingListPositionEnums.ZB.getOrganizationType().equals(request.getOrganizationType())){
                organizationIdTimeZones = organizationMapper.getBusinessGroupOrganizationIds(request.getBusinessGroup(),RankingListPositionEnums.AREA.getOrganizationType());
            }else {
                organizationIdTimeZones = organizationMapper.getAllChildrenOrganizationIdTimeZones(request.getOrganizationIds());
            }
            if(CollectionUtil.isNotEmpty(organizationIdTimeZones)){
                return organizationIdTimeZones.stream().map(p -> {
                    OrganizationMapOutLineGroupVo mapOutLineGroupVo = new OrganizationMapOutLineGroupVo();
                    mapOutLineGroupVo.setOrganizationId(p.getRegions());
                    mapOutLineGroupVo.setOrganizationName(p.getTitle());
                    return mapOutLineGroupVo;
                }).collect(Collectors.toList());
            }
            return CollectionUtil.newArrayList();
        }


//        if(CollectionUtil.isEmpty(mapVos)){
//            return null;
//        }
        //直接转换
//        if(OrganizationMapQueryOrganizationTypeEnums.AREA.getQueryType().equals(request.getQueryOrganizationType())|| OrganizationMapQueryOrganizationTypeEnums.VAREA.getQueryType().equals(request.getQueryOrganizationType())){
//            return mapVos.stream().map(mapVo->{
//                OrganizationMapOutLineGroupVo mapOutLineGroupVo = new OrganizationMapOutLineGroupVo();
//                mapOutLineGroupVo.setOrganizationId(mapVo.getOrganizationId());
//                mapOutLineGroupVo.setOrganizationName(mapVo.getOrganizationName());
//                List<MapOutLineGroupRegionVo> groupRegionVos = new ArrayList<>(1);
//                MapOutLineGroupRegionVo groupRegionVo = new MapOutLineGroupRegionVo();
//                groupRegionVo.setGroupCode(mapVo.getGroupCode());
//                groupRegionVo.setRegionCodes(mapVo.getRegionCodes());
//                groupRegionVos.add(groupRegionVo);
//                mapOutLineGroupVo.setGroupRegionVos(groupRegionVos);
//                return mapOutLineGroupVo;
//            }).collect(Collectors.toList());
//        }else {
//            List<OrganizationMapOutLineGroupVo> mapOutLineGroupVos = new ArrayList<>();
//            //首先按照大区分组
//            Map<String, List<OrganizationMapPerformanceVo>> groupByVAreaIdMap = mapVos.stream().collect(Collectors.groupingBy(OrganizationMapPerformanceVo::getVareaId));
//            for(Map.Entry<String, List<OrganizationMapPerformanceVo>> entry:groupByVAreaIdMap.entrySet()){
//                String vareaId = entry.getKey();
//                OrganizationMapOutLineGroupVo mapOutLineGroupVo = new OrganizationMapOutLineGroupVo();
//                mapOutLineGroupVo.setOrganizationId(vareaId);
//                List<OrganizationMapPerformanceVo> mapPerformanceVos = groupByVAreaIdMap.get(vareaId);
//                mapOutLineGroupVo.setOrganizationName(mapPerformanceVos.get(0).getVareaName());
//                //按照groupCode分组
//                Map<Integer, List<OrganizationMapPerformanceVo>> groupByGroupCodeMap = mapPerformanceVos.stream().collect(Collectors.groupingBy(OrganizationMapPerformanceVo::getGroupCode));
//                List<MapOutLineGroupRegionVo> groupRegionVos = new ArrayList<>();
//                for(Map.Entry<Integer, List<OrganizationMapPerformanceVo>> groupCodeEntry:groupByGroupCodeMap.entrySet()){
//                    MapOutLineGroupRegionVo groupRegionVo = new MapOutLineGroupRegionVo();
//                    groupRegionVo.setGroupCode(groupCodeEntry.getKey());
//                    List<String> regionCodes = new ArrayList<>();
//                    groupCodeEntry.getValue().forEach(e->regionCodes.addAll(e.getRegionCodes()));
//                    groupRegionVo.setRegionCodes(regionCodes);
//                    groupRegionVos.add(groupRegionVo);
//                }
//                mapOutLineGroupVo.setGroupRegionVos(groupRegionVos);
//                mapOutLineGroupVos.add(mapOutLineGroupVo);
//            }
//            return mapOutLineGroupVos;
//        }
    }

    private void validatedRequest(OrganizationMapRequest request){
        //选择sku/spu/line时必须有具体的id
        if(!OrganizationMapQueryPerformanceTypeEnums.PERFORMANCE.getQueryType().equals(request.getQueryPerformanceType())
            && StringUtils.isBlank(request.getProductInfoId())
        ){
            throw new ApplicationException("按照产品信息查询时必须选择具体的产品信息");
        }
    }
    @Override
    public List<OrganizationMapProductVo> getOrganizationMapProductInfo(String queryType) {
        //直接拿总部的数据最新月份数据
        return organizationMapMapper.getOrganizationMapProductInfo(queryType, LocalDate.now().toString().substring(0,7),RequestUtils.getBusinessGroup());
    }

    @Override
    public List<OrganizationMapAchievementCompareVo> getOrganizationMapAchievementCompare(OrganizationMapAchievementRequest request) {
        log.info("getOrganizationMapAchievementCompare request:{}", request);
        setRequest(request);
        List<OrganizationMapAchievementCompareVo> list = organizationMapMapper.queryOrganizationMapAchievementCompare(request);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(vo -> {
                String receiverProvinceName = vo.getName();
                if (StringUtils.isNotBlank(receiverProvinceName)) {
                    int endIndex = receiverProvinceName.contains("内蒙古") || receiverProvinceName.contains("黑龙江") ? 3 : 2;
                    vo.setName(receiverProvinceName.substring(0, endIndex));
                }
            });
        }
        return list;
    }

    @Override
    public OrganizationMapAchievementDistributionVo getOrganizationMapAchievementDistribution(OrganizationMapAchievementRequest request) {
        log.info("getOrganizationMapAchievementDistribution request:{}", request);
        setRequest(request);

        OrganizationMapAchievementDistributionVo achievementDistributionVo = new OrganizationMapAchievementDistributionVo();

        List<OrganizationMapAchievementDistributionRegionVo> provinceList = new ArrayList<>();

        List<OrganizationMapAchievementDistributionRegionVo> regionList = organizationMapMapper.queryOrganizationMapAchievementDistributionRegion(request);
        if (CollectionUtil.isNotEmpty(regionList)) {
            if (OrganizationTypeEnum.ZB.getOrganizationType().equals(request.getOrganizationType())) {
                OrganizationMapAchievementDistributionRegionVo province = new OrganizationMapAchievementDistributionRegionVo();
                province.setReceiverProvinceName("全国");
                provinceList.add(province);
            }
            Map<String, List<OrganizationMapAchievementDistributionRegionVo>> regionMap = regionList.stream().collect(Collectors.groupingBy(OrganizationMapAchievementDistributionRegionVo::getReceiverProvinceId));
            regionMap.forEach((receiverProvinceId, receiverCityList) -> {
                OrganizationMapAchievementDistributionRegionVo province = new OrganizationMapAchievementDistributionRegionVo();
                province.setReceiverProvinceId(receiverProvinceId);
                if (CollectionUtil.isNotEmpty(receiverCityList)) {
                    province.setReceiverProvinceName(receiverCityList.get(0).getReceiverProvinceName());
                    String receiverProvinceName = province.getReceiverProvinceName();
                    if (StringUtils.isNotBlank(receiverProvinceName)) {
                        int endIndex = receiverProvinceName.contains("内蒙古") || receiverProvinceName.contains("黑龙江") ? 3 : 2;
                        province.setReceiverProvinceName(receiverProvinceName.substring(0, endIndex));
                    }
                    List<OrganizationMapAchievementDistributionCityVo> cityList = new ArrayList<>();
                    BeanUtils.copyProperties(receiverCityList, cityList, OrganizationMapAchievementDistributionRegionVo.class, OrganizationMapAchievementDistributionCityVo.class);
                    province.setCityList(cityList);
                }
                provinceList.add(province);
            });
            achievementDistributionVo.setProvinceList(provinceList);

            if (StringUtils.isBlank(request.getReceiverProvinceId())) {
                if (OrganizationTypeEnum.ZB.getOrganizationType().equals(request.getOrganizationType())) {
//                request.setReceiverProvinceId(regionList.get(0).getReceiverProvinceId());
                } else if (OrganizationTypeEnum.AREA.getOrganizationType().equals(request.getOrganizationType())) {
                    request.setReceiverProvinceId(regionList.get(0).getReceiverProvinceId());
                } else if (OrganizationTypeEnum.VARE.getOrganizationType().equals(request.getOrganizationType())) {
                    request.setReceiverProvinceId(regionList.get(0).getReceiverProvinceId());
                } else if (OrganizationTypeEnum.PROVINCE.getOrganizationType().equals(request.getOrganizationType())) {
                    request.setReceiverProvinceId(regionList.get(0).getReceiverProvinceId());
                } else if (OrganizationTypeEnum.COMPANY.getOrganizationType().equals(request.getOrganizationType())) {
                    request.setReceiverProvinceId(regionList.get(0).getReceiverProvinceId());
                    request.setReceiverCityId(regionList.get(0).getReceiverCityId());
                } else if (OrganizationTypeEnum.DEPARTMENT.getOrganizationType().equals(request.getOrganizationType())) {
                    request.setReceiverProvinceId(regionList.get(0).getReceiverProvinceId());
                    request.setReceiverCityId(regionList.get(0).getReceiverCityId());
                }
            }
            List<OrganizationMapAchievementDistributionPerformanceVo> fullPerformanceList = new ArrayList<>();
            List<OrganizationMapAchievementDistributionPerformanceVo> performanceList = organizationMapMapper.queryOrganizationMapAchievementDistributionPerformance(request);
            Map<String, OrganizationMapAchievementDistributionPerformanceVo> performanceMap;
            if (CollectionUtil.isNotEmpty(performanceList)) {
                performanceMap = performanceList.stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationMapAchievementDistributionPerformanceVo::getTheYearMonth,
                                        v -> v,
                                        (existingValue, newValue) -> existingValue // 保留第一个出现的值
                                )
                        );
            } else {
                performanceMap = new HashMap<>();
            }
            request.getYearMonthList().forEach(yearMonth -> {
                if (performanceMap.containsKey(yearMonth)) {
                    fullPerformanceList.add(performanceMap.get(yearMonth));
                } else {
                    OrganizationMapAchievementDistributionPerformanceVo performance = new OrganizationMapAchievementDistributionPerformanceVo();
                    performance.setTheYearMonth(yearMonth);
                    fullPerformanceList.add(performance);
                }
            });
            achievementDistributionVo.setPerformanceList(fullPerformanceList);
        }
        return achievementDistributionVo;
    }

    private void setRequest(OrganizationMapAchievementRequest request) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        log.info("request loginInfo:[{}] ", loginInfo);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
        }
        request.setOrganizationIds(personOrganizationIds);
        // 非总部人员，限定范围，本人及以下组织
//        if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
//            request.setOrganizationIds(personOrganizationIds);
//        }

        request.setOrganizationType(loginInfo.getOrganizationType());

        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
            );
                if (Objects.isNull(ceoBusinessOrganizationEntity)) {
                throw new ApplicationException("组织ID不存在");
            }
            request.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());
        }

        if (RankingListPositionEnums.ZB.getOrganizationType().equals(request.getOrganizationType())) {
            request.setQueryRegionType("province");
        } else if (RankingListPositionEnums.AREA.getOrganizationType().equals(request.getOrganizationType())) {
            request.setQueryRegionType("province");
        } else if (RankingListPositionEnums.V_AREA.getOrganizationType().equals(request.getOrganizationType())) {
            request.setQueryRegionType("province");
        } else if (RankingListPositionEnums.PROVINCE.getOrganizationType().equals(request.getOrganizationType())) {
            request.setQueryRegionType("city");
        } else if (RankingListPositionEnums.COMPANY.getOrganizationType().equals(request.getOrganizationType())) {
            request.setQueryRegionType("district");
        } else if (RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(request.getOrganizationType())) {
            request.setQueryRegionType("district");
        } else {
            request.setQueryRegionType("district");
        }

        request.setYearMonthList(realTimeUtils.queryYearMonthList(request.getDateTypeId(), request.getYearMonth()));

    }
}
