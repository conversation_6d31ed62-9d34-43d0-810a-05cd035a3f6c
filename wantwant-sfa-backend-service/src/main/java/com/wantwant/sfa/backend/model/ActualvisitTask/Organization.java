package com.wantwant.sfa.backend.model.ActualvisitTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Organization {

    @ApiModelProperty(value = "岗位ID")
    private String posId;

    @ApiModelProperty(value = "业务员id")
    private String employeeId;

    @ApiModelProperty(value = "业务员姓名")
    private String employeeName;

    @ApiModelProperty(value = "大区id")
    private String areaCode;

    @ApiModelProperty(value = "大区")
    private String areaName;

    @ApiModelProperty(value = "分公司id")
    private String branchCode;

    @ApiModelProperty(value = "分公司")
    private String branchName;

    @ApiModelProperty(value = "营业所ID")
    private String officeCode;

    @ApiModelProperty(value = "营业所ID")
    private String officeName;
}
