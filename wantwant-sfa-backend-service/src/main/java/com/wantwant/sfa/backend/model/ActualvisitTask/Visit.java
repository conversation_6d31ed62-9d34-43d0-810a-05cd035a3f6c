package com.wantwant.sfa.backend.model.ActualvisitTask;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 拜访记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_visit")
@ApiModel(value = "SfaVisit对象", description = "拜访记录表")
public class Visit extends Model<Visit> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("member_id")
    private String memberId;

    @ApiModelProperty(value = "member_id对应姓名")
    @TableField("employee_name")
    private String employeeName;

    @TableField("customer_id")
    private Integer customerId;

    @TableField("customer_name")
    private String customerName;

    @ApiModelProperty(value = "0:未知/n1：男性/n2：女性")
    @TableField("gender")
    private Integer gender;

    @ApiModelProperty(value = "1：是学代/n 2：是终端")
    @TableField("customer_type")
    private Integer customerType;

    @ApiModelProperty(value = "客户子类型")
    @TableField("customer_subtype")
    private Integer customerSubtype;

    @ApiModelProperty(value = "0：潜在/n 1：正式")
    @TableField("is_potential")
    private Integer isPotential;

    @ApiModelProperty(value = "客户手机号")
    @TableField("customer_mobile")
    private String customerMobile;

    @ApiModelProperty(value = "身份证号")
    @TableField("id_card")
    private String idCard;

    @ApiModelProperty(value = "身份证姓名")
    @TableField("id_card_name")
    private String idCardName;

    @TableField("id_card_image_name")
    private String idCardImageName;

    @TableField("id_card_image_url")
    private String idCardImageUrl;

    @ApiModelProperty(value = "0: 待拜访 \n1: 拜访中\n2: 已拜访\n3:未拜访\n4:开户驳回重新提交\n5:特陈驳回重新提交\n6:区域经理取消的拜访计划\n-1:取消拜访 7：已拜访为结束 8: 开户驳回重新提交为结束 9:特称驳回重新提交为结束")
    @TableField("visit_status")
    private Integer visitStatus;

    @ApiModelProperty(value = "1：首次拜访\n2：再次拜访\n3：异常拜访")
    @TableField("visit_type")
    private Integer visitType;

    @TableField("visit_time")
    private LocalDateTime visitTime;

    @TableField("visit_end_time")
    private LocalDateTime visitEndTime;

    @ApiModelProperty(value = "拜访目的")
    @TableField("goal_type_code")
    private String goalTypeCode;

    @ApiModelProperty(value = "拜访异常 0：正常 其他数字为不正常")
    @TableField("abnormal")
    private String abnormal;

    @TableField("result")
    private String result;

    @TableField("remark")
    private String remark;

    @TableField("cancel_time")
    private LocalDateTime cancelTime;

    @TableField("cancel_reason")
    private String cancelReason;

    @TableField("store_name")
    private String storeName;

    @TableField("store_image_name")
    private String storeImageName;

    @TableField("store_image_url")
    private String storeImageUrl;

    @ApiModelProperty(value = "营业执照号")
    @TableField("store_license")
    private String storeLicense;

    @ApiModelProperty(value = "营业执照照片")
    @TableField("store_license_image")
    private String storeLicenseImage;

    @ApiModelProperty(value = "营业执照图片链接")
    @TableField("store_license_image_url")
    private String storeLicenseImageUrl;

    @ApiModelProperty(value = "营业执照图片名称")
    @TableField("store_license_image_name")
    private String storeLicenseImageName;

    @TableField("store_inner_picture")
    private String storeInnerPicture;

    @TableField("store_inner_image_url")
    private String storeInnerImageUrl;

    @TableField("store_inner_image_name")
    private String storeInnerImageName;

    @TableField("school_name")
    private String schoolName;

    @TableField("school_image_name")
    private String schoolImageName;

    @TableField("school_inner_image")
    private String schoolInnerImage;

    @TableField("school_inner_image_url")
    private String schoolInnerImageUrl;

    @TableField("school_inner_image_name")
    private String schoolInnerImageName;

    @TableField("school_image_url")
    private String schoolImageUrl;

    @ApiModelProperty(value = "学生证图片链接")
    @TableField("student_card_url")
    private String studentCardUrl;

    @ApiModelProperty(value = "学生证图片名称")
    @TableField("student_card_name")
    private String studentCardName;

    @TableField("verification_code")
    private String verificationCode;

    @TableField("province")
    private String province;

    @TableField("city")
    private String city;

    @TableField("district")
    private String district;

    @TableField("street")
    private String street;

    @TableField("longitude")
    private String longitude;

    @TableField("latitude")
    private String latitude;

    @ApiModelProperty(value = "是否有下单 0：没有下单/1：有下单")
    @TableField("is_order")
    private Integer isOrder;

    @ApiModelProperty(value = "0：当场开户失败\n1：当场开户成功")
    @TableField("is_convert")
    private Integer isConvert;

    @TableField("convert_time")
    private LocalDateTime convertTime;

    @TableField("intention_level")
    private Integer intentionLevel;

    @ApiModelProperty(value = "是否重新提交驳回特陈 0:特陈驳回未提交 1:特陈驳回提交")
    @TableField("special_resubmit_status")
    private Integer specialResubmitStatus;

    @TableField("visit_count")
    private Integer visitCount;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @ApiModelProperty(value = "拜访计划创建人")
    @TableField("visit_plan_creator")
    private String visitPlanCreator;

    @ApiModelProperty(value = "0:非拜访计划,1:自己的计划,2:区域经理安排的拜访计划,3:区域经理取消的拜访计划未查看,4:区域经理取消的拜访计划已查看,5大区主管设置的拜访计划,6:大区主管取消的拜访计划未查看,7:大区主管取消的拜访计划已查看")
    @TableField("is_plan")
    private Integer isPlan;

    @ApiModelProperty(value = "上一次拜访的时间")
    @TableField("last_time")
    private LocalDateTime lastTime;

    @ApiModelProperty(value = "当场下单金额")
    @TableField("spot_order")
    private BigDecimal spotOrder;

    @ApiModelProperty(value = "是否有活动：0：无，1：有")
    @TableField("is_activity")
    private Integer isActivity;

    @TableField("eat_activity_image_url")
    private String eatActivityImageUrl;

    @TableField("eat_activity_image_name")
    private String eatActivityImageName;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("update_person")
    private String updatePerson;

//    @ApiModelProperty(value = "同步考勤列表是否上传队列：0-否 1-是")
//    private Integer isAccumulatedToAttendance;

    @ApiModelProperty(value = "性别：0-男 1-女")
    @TableField("sex")
    private Integer sex;

    @ApiModelProperty(value = "学生证号")
    @TableField("student_card_number")
    private String studentCardNumber;

    @ApiModelProperty(value = "是否可以重复提交 0不可以重复提交 1可以重复提交")
    @TableField("is_repetition")
    private Integer isRepetition;

    @ApiModelProperty(value = "审核状态,0未审核,1审核通过,2驳回,3关闭")
    @TableField("is_verified")
    private Integer isVerified;

    @ApiModelProperty(value = "学生证有效期")
    @TableField("student_card_validity")
    private String studentCardValidity;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
