package com.wantwant.sfa.backend.model;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-08-19
 */
@TableName("sfa_apply_member_addition")
@Data
@ToString
public class ApplyMemberAdditionPo extends Model<ApplyMemberAdditionPo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("apply_id")
    private Integer applyId;
    
    @TableField("province")
    private String province;

    @TableField("city")
    private String city;


    @TableField("district")
    private String district;

    @TableField("street")
    private String street;

    @TableField("longitude")
    private String longitude;

    @TableField("latitude")
    private String latitude;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("is_delete")
    private int isDelete;

}
