package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@TableName("sfa_arrival_reminder")
@Data
@ToString
public class ArrivalReminder extends Model<ArrivalReminder> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableId(value = "channel_id")
    private String channelId;


    @TableId(value = "channel_name")
    private String channelName;


    @TableId(value = "sku")
    private String sku;

    @TableId(value = "flavor")
    private String flavor;

    @TableId(value = "sku_spec")
    private String skuSpec;

    @TableId(value = "sku_name")
    private String skuName;

    @TableId(value = "organization_id")
    private String organizationId;

    @TableId(value = "employee_id")
    private String employeeId;

    @TableId(value = "business_group")
    private String businessGroup;


    @TableId(value = "is_delete")
    private Integer isDelete;

    @TableId("creat_time")
    private LocalDateTime creatTime;

    @TableId("push_time")
    private LocalDateTime pushTime;
}
