package com.wantwant.sfa.backend.model;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Assessment {
	
    @ApiModelProperty(value = "组织ID")
	String organizationId;
    @ApiModelProperty(value = "组织名称")
	String organizationName;    
    @ApiModelProperty(value = "组织类型")
	String organizationType;
    @ApiModelProperty(value = "考核数量")
	BigDecimal assessmentCount;
}
