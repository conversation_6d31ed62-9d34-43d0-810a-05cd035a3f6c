package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ceo_business_organization_position_relation")
@ApiModel(value = "CeoBusinessOrganizationPositionRelation对象", description = "")
@ToString
public class CeoBusinessOrganizationPositionRelation extends Model<CeoBusinessOrganizationPositionRelation> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    @TableField("organization_id")
    private String organizationId;

    @ApiModelProperty(value = "岗位ID")
    @TableField("position_id")
    private String positionId;

    @ApiModelProperty(value = "岗位类型")
    @TableField("position_type_id")
    private Integer positionTypeId;

    @ApiModelProperty(value = "负责人ID 员工工号")
    @TableField(value="employee_id",strategy = FieldStrategy.IGNORED)
    private String employeeId;

    @ApiModelProperty(value = "负责人姓名")
    @TableField(value="employee_name",strategy = FieldStrategy.IGNORED)
    private String employeeName;

    @ApiModelProperty(value = "组织上级ID")
    @TableField("organization_parent_id")
    private String organizationParentId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("updated_person")
    private String updatedPerson;

    @TableField("ordinary_image_name")
    private String ordinaryImageName;

    @TableField("ordinary_url")
    private String ordinaryUrl;

    @TableField("student_image_name")
    private String studentImageName;

    @TableField("student_url")
    private String studentUrl;

    @TableField("terminal_image_name")
    private String terminalImageName;

    @TableField("terminal_url")
    private String terminalUrl;

    @ApiModelProperty(value = "入职时间")
    @TableField(value="onboard_time",strategy = FieldStrategy.IGNORED)
    private LocalDateTime onboardTime;

    @ApiModelProperty(value = "离职时间")
    @TableField(value="off_time",strategy = FieldStrategy.IGNORED)
    private LocalDateTime offTime;

    @TableField("channel")
    private int channel;
    
    @ApiModelProperty(value = "试岗开始时间")
    @TableField(value="probation_start_time",strategy = FieldStrategy.IGNORED)
    private LocalDateTime probationStartTime;

    @ApiModelProperty(value = "试岗结束时间")
    @TableField(value="probation_end_time",strategy = FieldStrategy.IGNORED)
    private LocalDateTime probationEndTime;

    @ApiModelProperty(value = "组别sfa_production_group表的主键")
    @TableField("business_group")
    private Integer businessGroup;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
