package com.wantwant.sfa.backend.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
* <AUTHOR>
* @description: //模块目的、功能描述
* @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
* @Time 2020-12-28 15:04:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ceo_business_organization_position_relation_task")
@ApiModel(value = "CeoBusinessOrganizationPositionRelationTask对象", description = "")
public class CeoBusinessOrganizationPositionRelationTask extends Model<CeoBusinessOrganizationPositionRelationTask> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    @TableField("organization_id")
    private String organizationId;

    @ApiModelProperty(value = "岗位ID")
    @TableField("position_id")
    private String positionId;

    @ApiModelProperty(value = "岗位类型")
    @TableField("position_type_id")
    private Integer positionTypeId;

    @ApiModelProperty(value = "负责人ID 员工工号")
    @TableField("employee_id")
    private String employeeId;

    @ApiModelProperty(value = "负责人姓名")
    @TableField("employee_name")
    private String employeeName;

    @ApiModelProperty(value = "组织上级ID")
    @TableField("organization_parent_id")
    private String organizationParentId;

    @ApiModelProperty(value = "变更任务类型：1入职，2离职")
    @TableField("change_type")
    private String changeType;

    @ApiModelProperty(value = "变更任务类型：1入职，2离职")
    @TableField("change_date")
    private LocalDate changeDate;

    @ApiModelProperty(value = "岗位变更执行时间")
    @TableField("executed_date")
    private LocalDateTime executed_date;

    @ApiModelProperty(value = "0:未执行,1:已执行,2：已取消")
    @TableField("is_execute")
    private Integer isExecute;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @TableField("create_person_name")
    private String createPersonName;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("updated_person")
    private String updatedPerson;

    @TableField("updated_person_name")
    private String updatedPersonName;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
