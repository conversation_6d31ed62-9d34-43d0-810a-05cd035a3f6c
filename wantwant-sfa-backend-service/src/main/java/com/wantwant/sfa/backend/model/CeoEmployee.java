package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ceo_employee")
@ApiModel(value = "CeoEmployee对象", description = "")
public class CeoEmployee extends Model<CeoEmployee> {

    private static final long serialVersionUID = 1L;

    @TableId("memberKey")
    @TableField("memberKey")
    private Long memberKey;

    @TableField("empId")
    private String empId;

    @TableField("name")
    private String name;

    @TableField("mail")
    private String mail;

    @TableField("mobile")
    private String mobile;

    @TableField("onboardDate")
    private String onboardDate;

    @TableField("createDate")
    private LocalDate createDate;

    @TableField("updateDate")
    private LocalDate updateDate;


    @Override
    protected Serializable pkVal() {
        return this.memberKey;
    }

}
