package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ceo_terminal_member")
@ApiModel(value = "CeoTerminalMember对象", description = "")
public class CeoTerminalMember extends Model<CeoTerminalMember> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "`key`", type = IdType.AUTO)
    private Integer key;

    @ApiModelProperty(value = "终端姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "storeName")
    private String storeName;

    @ApiModelProperty(value = "门店图片URL")
    private String storeImageUrl;//店门

    @ApiModelProperty(value = "证件门店名称")
    private String cerStoreName;

    @ApiModelProperty(value = "客户类型: 2终端 4特通")
    @TableField("customerType")
    private Integer customerType;

    @ApiModelProperty(value = "客户子类型与custoemr_type一起用，特通4--1  特通--餐饮 4--2 特通--KTV 4--3 特通--其他")
    @TableField("customerSubtype")
    private Integer customerSubtype;

    @ApiModelProperty(value = "客户ID")
    @TableField("customerId")
    private Integer customerId;

    @ApiModelProperty(value = "终端手机号")
    @TableField("mobileNumber")
    private String mobileNumber;

    @ApiModelProperty(value = "身份证号码")
    @TableField("IDCard")
    private String IDCard;

    @ApiModelProperty(value = "省份")
    @TableField("province")
    private String province;

    @ApiModelProperty(value = "城市")
    @TableField("city")
    private String city;

    @ApiModelProperty(value = "区")
    @TableField("district")
    private String district;

    @ApiModelProperty(value = "详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty(value = "收货-省")
    @TableField("receive_province")
    private String receiveProvince;

    @ApiModelProperty(value = "收货-市")
    @TableField("receive_city")
    private String receiveCity;

    @ApiModelProperty(value = "收货-区域")
    @TableField("receive_district")
    private String receiveDistrict;

    @ApiModelProperty(value = "收货-街道")
    @TableField("receive_street")
    private String receiveStreet;

    @ApiModelProperty(value = "身份证正面图片链接")
    @TableField("IDCardFrontImageUrl")
    private String IDCardFrontImageUrl;

    @ApiModelProperty(value = "身份证反面图片链接")
    @TableField("IDCardBackImageUrl")
    private String IDCardBackImageUrl;

    @ApiModelProperty(value = "营业执照图片链接")
    @TableField("lisenceImageUrl")
    private String lisenceImageUrl;

    @ApiModelProperty(value = "身份证正面图片名称")
    @TableField("IDCardFrontImageName")
    private String IDCardFrontImageName;

    @ApiModelProperty(value = "身份证反面图片名称")
    @TableField("IDCardBackImageName")
    private String IDCardBackImageName;

    @ApiModelProperty(value = "营业执照图片名称")
    @TableField("lisenceImageName")
    private String lisenceImageName;

    @ApiModelProperty(value = "营业执照号")
    @TableField("license")
    private String license;

    @ApiModelProperty(value = "审核次数")
    @TableField("verifiedCount")
    private Integer verifiedCount;

    @ApiModelProperty(value = "审核员工")
    @TableField("employeeID")
    private String employeeID;

    @ApiModelProperty(value = "0是不可重复提交,1可重复提交")
    @TableField("isRepetition")
    private Integer isRepetition;

    @ApiModelProperty(value = "审核状态 0,未审核,1,审核通过,2,驳回")
    @TableField("isVerified")
    private Integer isVerified;

    @ApiModelProperty(value = "申请时间")
    @TableField("createdAt")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "审核时间")
    @TableField("verifiedAt")
    private LocalDateTime verifiedAt;

    @TableField("closeSuggestions")
    private String closeSuggestions;

    @TableField("dismissedSuggestions")
    private String dismissedSuggestions;

    @ApiModelProperty(value = "岗位code")
    @TableField("posID")
    private String posID;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("updated_person")
    private String updatedPerson;


    @ApiModelProperty(value = "拜访ID")
    @TableField("visit_id")
    private Integer visitId;

    @TableField("verifyId")
    private Integer verifyId;

    @TableField("source")
    private Integer source;


    @Override
    protected Serializable pkVal() {
        return this.key;
    }

}
