package com.wantwant.sfa.backend.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Customer {
	
    @ApiModelProperty("大区")
    private String area;  
    
    @ApiModelProperty("分公司")
    private String company;  
    
    @ApiModelProperty("营业所")
    private String branch;  
    
    @ApiModelProperty("1:已有，0：潜在")
    private String isPotential;  
    
    @ApiModelProperty("客户类型 1：学代,2：终端,3：旺粉,4：特通'")
    private String customerType;  
    
    @ApiModelProperty("特通4--1  特通--餐饮 4--2 特通--KTV 4--3 特通--其他")
    private String customerSubtype;  
    
    @ApiModelProperty("冻结状态 0:未冻结 1:冻结")
    private Integer isFrozen;      
    
    @ApiModelProperty("审核状态 0 未审核,1 审核通过,2 驳回'")
    private String isVerified;      
    
    @ApiModelProperty("提交状态 '0是不可重复提交,1可重复提交'")
    private String isRepetition;      
    
    @ApiModelProperty("客户编号")
    private String customerId;  
    
    @ApiModelProperty("客户名称")
    private String customerName;  
    
    @ApiModelProperty("门店名称")
    private String storeName; 
    
    @ApiModelProperty("证件门店名称")
    private String cerStoreName;   
    
    @ApiModelProperty("门店照片")
    private String storeImageUrl;  
    
    @ApiModelProperty("门店地址（省）")
    private String province;  
    
    @ApiModelProperty("门店地址（市）")
    private String city;  
    
    @ApiModelProperty("门店地址（区）")
    private String district;  
    
    @ApiModelProperty("门店地址（街道）")
    private String street;  
    
    @ApiModelProperty("收货 门店地址（省）")
    private String receiveProvince;  
    
    @ApiModelProperty("收货 门店地址（市）")
    private String receiveCity;  
    
    @ApiModelProperty("收货 门店地址（区）")
    private String receiveDistrict;  
    
    @ApiModelProperty("收货 门店地址（街道）")
    private String receiveStreet;
    @ApiModelProperty("手机号")
    private String customerMobile; 
    
    @ApiModelProperty("专员工号")
    private String employeeId; 
    
    @ApiModelProperty("专员姓名")
    private String employeeName;  
    
    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date requestDate; 
    
    @ApiModelProperty("证件类型")
    private String licenseType; 
    
    @ApiModelProperty("证件号码")
    private String license;  
    
    @ApiModelProperty("岗位编号")
    private String positionId;  
    
    @ApiModelProperty("证件图片")
    private String lisenceImageName;  
    
    @ApiModelProperty("身份证号")
    private String iDCard;   
    
    @ApiModelProperty("拜访ID")
    private String visitId; 
    
    @ApiModelProperty("身份证正面图片")
    private String iDCardFrontImage;  
    
    @ApiModelProperty("学生证图片")
    private String studentCardUrl;  
    
    @ApiModelProperty("学生证有效期")
    private String studentCardValidity;   
    
    @ApiModelProperty("学生证号")
    private String studentCardNumber;  
    
    @ApiModelProperty("操作人")
    private String person;  
    
    @ApiModelProperty("操作人姓名")
    private String personName;


    @ApiModelProperty("memberKey")
    private Integer memberKey;

    @ApiModelProperty("注册来源 0：sfa-app 1：旺铺-h5")
    private String source;
}
