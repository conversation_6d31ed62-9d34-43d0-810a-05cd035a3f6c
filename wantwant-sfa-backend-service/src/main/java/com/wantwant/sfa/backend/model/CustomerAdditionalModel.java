package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/08/31/下午1:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer_additional")
@ApiModel(value = "SfaCustomerAdditional对象", description = "客户表附属信息")
public class CustomerAdditionalModel extends Model<CustomerAdditionalModel> {
    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Integer customerId;

    @ApiModelProperty(value = "组合")
    @TableField("combine")
    private String combine;

    @ApiModelProperty(value = "终端覆盖数")
    @TableField("terminal_covert_count")
    private Long terminalCovertCount;

    @ApiModelProperty(value = "渠道")
    @TableField("channel")
    private Integer channel;

    @ApiModelProperty(value = "状态")
    @TableField("`status`")
    private Integer status;


}
