package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer_check")
@ApiModel(value = "CustomerCheck对象", description = "")
public class CustomerCheck extends Model<CustomerCheck> {

    @ApiModelProperty(value ="id")
    @TableField("id")
    String id;

    @ApiModelProperty(value ="申请id")
    @TableField("request_id")
    String requestId;

    @ApiModelProperty(value ="大区")
    @TableField("region")
    String region;

    @ApiModelProperty(value ="分公司")
    @TableField("filiale")
    String filiale;

    @ApiModelProperty(value ="申请人")
    @TableField("applicant")
    String applicant;

    @ApiModelProperty(value ="申请人ID 员工工号")
    @TableField("employee_id")
    String employeeId;

    @ApiModelProperty(value ="审核时间")
    @TableField("check_time")
    String checkTime;

    @ApiModelProperty(value ="审核意见")
    @TableField("check_reason")
    String checkReason;

    @ApiModelProperty(value ="审核状态 0:同意 1：拒绝")
    @TableField("check_state")
    Integer checkState;


    @ApiModelProperty(value = "审核人姓名")
    @TableField("auditor")
    String auditor;
}
