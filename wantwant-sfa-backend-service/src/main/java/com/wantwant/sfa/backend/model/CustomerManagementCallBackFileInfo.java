package com.wantwant.sfa.backend.model;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer_center_callback_file_info")
@ApiModel(value = "客户中心-客户列表-客户详情-回访记录-文件信息", description = "")
@ToString
public class CustomerManagementCallBackFileInfo extends Model<CustomerManagementCallBackFileInfo>{

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value = "id")
    private Long id;

    @TableField(value = "record_id")
    private Long recordId;

    @ApiModelProperty(value = "文件url")
    @TableField(value = "file_url")
    private String fileUrl;

    @ApiModelProperty(value = "文件url")
    @TableField(value = "file_name")
    private String fileName;

    @ApiModelProperty(value = "删除标志(0.否 1.是)")
    @TableField(value = "delete_flag")
    private int deleteFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
