package com.wantwant.sfa.backend.model;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer_center_callback_info")
@ApiModel(value = "客户中心-客户列表-客户详情-回访记录", description = "")
@ToString
public class CustomerManagementCallBackInfo extends Model<CustomerManagementCallBackInfo>{

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value = "id")
    private Long id;

    @ApiModelProperty(value = "客户ID")
    @TableField(value = "customer_id")
    private String customerId;

    @ApiModelProperty(value = "员工ID")
    @TableField(value = "employee_id")
    private String employeeId;

    @ApiModelProperty(value = "员工姓名")
    @TableField(value = "employee_name")
    private String employeeName;

    @ApiModelProperty(value = "记录类型(1.回访、2.跟进)")
    @TableField(value = "record_type")
    private int recordType;

    @ApiModelProperty(value = "回访类型(1.资料修改、2.满意度、3.关闭帐户 0.跟进)")
    @TableField(value = "callback_type")
    private int callBackType;

    @ApiModelProperty(value = "记录日期")
    @TableField(value = "callback_date")
    private Date callBackDate;

    @ApiModelProperty(value = "文件url")
    @TableField(value = "file_url")
    private String fileUrl;

    @ApiModelProperty(value = "文件url")
    @TableField(value = "file_name")
    private String fileName;

    @ApiModelProperty(value = "跟进回访信息的关联关系:record id")
    @TableField(value = "parent_callback_relation")
    private Long parentCallBackRelation;

    @ApiModelProperty(value = "记录信息(回访信息/跟进信息)")
    @TableField(value = "record_info")
    private String recordInfo;

    @ApiModelProperty(value = "删除标志(0.否 1.是)")
    @TableField(value = "delete_flag")
    private int deleteFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
