package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：客户拜访信息
 * @Author： chen
 * @Date 2022/5/19
 */
@Data
@ApiModel(value = "VisitInfo对象", description = "客户拜访信息表")
@TableName("customer_visit_info")
public class CustomerVisitInfoModel {

    @ApiModelProperty(value = "id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "`customer_id`")
    private String customerId;

    @ApiModelProperty(value = "被拜访人")
    @TableField(value = "`visit_type`")
    private Integer visitType;

    @ApiModelProperty(value = "被拜访人")
    @TableField(value = "`customerType`")
    private Integer customerType;

    @TableField(value = "`partner_member_key`")
    @ApiModelProperty("合伙人memberKey")
    private String partnerMemberKey;

    @TableField(value = "`store_image_url`")
    @ApiModelProperty("门店图片url")
    private String storeImageUrl;

    @TableField(value = "'in_store_image_url'")
    @ApiModelProperty("店内图片url")
    private String inStoreImageUrl;

    @TableField(value = "'store_items_flag'")
    @ApiModelProperty("店内品项|经销品项：0-无造旺品项、1-有造旺品项")
    private Integer storeItemsFlag;

    @TableField(value = "'cooperate_inclination'")
    @ApiModelProperty("合作意愿：0-暂无合作意愿/后续不再购买造旺产品、1-有合作意愿/会继续购买造旺产品")
    private Integer cooperateInclination;

    @TableField(value = "'longitude'")
    @ApiModelProperty("经度")
    private String longitude;

    @TableField(value = "'latitude'")
    @ApiModelProperty("纬度")
    private String latitude;

    @TableField(value = "`is_inventory_check`")
    @ApiModelProperty("是否库存盘点 1-是  0-否")
    private Integer isInventoryCheck;

    @TableField(value = "`auditStatus`")
    @ApiModelProperty("稽核状态 0-正常  1-异常")
    private Integer auditStatus;

    @ApiModelProperty("稽核原因")
    private String auditReason;

    @ApiModelProperty("稽核时间")
    private String auditTime;

    @ApiModelProperty("备注")
    private String remark;



    @TableField(value = "`time_cost`")
    @ApiModelProperty("拜访用时")
    private String timeCost;

    @TableField(value = "`start_time`")
    @ApiModelProperty("开始时间")
    private String startTime;

    @TableField(value = "`end_time`")
    @ApiModelProperty("结束时间")
    private String endTime;

    @TableField(value = "`delete_flag`")
    @ApiModelProperty("删除标识 0-有效  1-无效")
    private Integer delete_flag;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新人")
    private String updator;

    @TableField(value = "`create_time`")
    @ApiModelProperty("创建时间")
    private String create_time;

    @TableField(value = "`update_time`")
    @ApiModelProperty("更新时间")
    private String update_time;


}
