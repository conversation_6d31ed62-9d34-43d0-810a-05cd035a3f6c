package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/10/26/下午3:01
 */
@Data
@TableName("sfa_department")
@ApiModel(value = "department对象", description = "")
public class DepartmentModel {

    @ApiModelProperty(value = "id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "`department_code`")
    private String departmentCode;
    @TableField(value = "`department_name`")
    private String departmentName;
    @TableField(value = "`department_type`")
    private Integer departmentType;
    @TableField(value = "`status`")
    private Integer status;

}
