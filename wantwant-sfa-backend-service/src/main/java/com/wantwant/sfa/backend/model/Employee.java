package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class Employee {

	
	@ApiModelProperty("大区")
	String area;
	
	@ApiModelProperty("大区组织ID")
	String areaOrganizationId;

	String varea;

	String vareaOrganziationId;

	String province;

	String provinceOrganizationId;
	
	@ApiModelProperty("分公司")
	String company;
	
	@ApiModelProperty("分公司组织ID")
	String companyOrganizationId;

	String department;

	String departmentOrganizationId;
	
	@ApiModelProperty("营业所")
	String branch;
	
	@ApiModelProperty("营业所组织ID")
	String branchOrganizationId;	
	
	@ApiModelProperty(value = "业务姓名")
	String employeeName;	
	
	@ApiModelProperty(value = "工号")
	String employeeId;
	
	@ApiModelProperty(value = "入职时间")
	Date createDate;
	
	@ApiModelProperty(value = "总业绩")
	String sumPerformance;	
	
	@ApiModelProperty(value = "本月业绩")
	String monthPerformance;
	
	@ApiModelProperty(value = "岗位ID")
	String positionId;
	
	@ApiModelProperty(value = "组织Id")
	String organizationId;
	
	@ApiModelProperty("组织名称")
	String organizationName;
	
	@ApiModelProperty("组织类型")
	String organizationType;

	@ApiModelProperty("试岗时间")
	String probationStartTime;

	Integer partTime;

	private Long memberKey;


	public String getOrganizationName() {
		if(organizationId.equals(branchOrganizationId)) {
			return branch;
		}else if (organizationId.equals(departmentOrganizationId)){
			return department;
		}else if(organizationId.equals(companyOrganizationId)){
			return company;
		}else if(organizationId.equals(provinceOrganizationId)){
			return province;
		}else if(organizationId.equals(vareaOrganziationId)){
			return varea;
		}else {
			return area;
		}
	}

	
	@ApiModelProperty(value = "岗位类型")
	int positionTypeId;
	
	@ApiModelProperty(value = "操作人")
	String person;


}
