package com.wantwant.sfa.backend.model;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * [主推品]合伙人目标业绩
 *
 * @since 2025-03-21
 */
@Data
@ToString
@TableName("sfa_employee_goal_detail")
public class EmployeeGoalDetailPo extends Model<EmployeeGoalDetailPo> {

	private static final long serialVersionUID = 5520986048618368807L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@TableField("main_id")
	private Integer mainId;

	@TableField("goal_id")
	private Integer goalId;

	@TableField("position_id")
	private String positionId;

	/**
	 * 时间范围(0:月,1:季度)
	 */
	@TableField("`range`")
	private Integer range;

	/**
	 * 生效年
	 */
	@TableField("`year`")
	private Integer year;

	/**
	 * 生效季度
	 */
	@TableField("`quarter`")
	private Integer quarter;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 业绩目标
	*/
	@TableField("sale_goal")
	private BigDecimal saleGoal;

	@TableField("member_key")
	private String memberKey;

	@TableField("employee_info_id")
	private Integer employeeInfoId;

	@TableField("business_group_id")
	private Integer businessGroupId;

	@TableField("push_status")
	private Integer pushStatus;


	private Integer goalStatus;

	@TableField("on_job_time")
	private LocalDate onJobTime;

	@TableField("product_name")
	private String productName;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("created_name")
	private String createdName;

	@TableField("updated_by")
	private String updatedBy;

	@TableField("updated_name")
	private String updatedName;

	/** 0:未删除 1:删除 */
	@TableField("is_delete")
	private Integer isDelete;

}
