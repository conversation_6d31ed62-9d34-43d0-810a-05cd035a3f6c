package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合伙人目标业绩分配历史
 *
 * @since 2022-08-26
 */
@Data
@TableName("sfa_employee_goal_log")
public class EmployeeGoalLogPO extends Model<EmployeeGoalLogPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_employee_goal.id
	*/
	@TableField("goal_id")
	private Integer goalId;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 修改属性
	*/
	@TableField("modify_attribute")
	private String modifyAttribute;

	/**
	* 修改值
	*/
	@TableField("modify_value")
	private String modifyValue;

	private Integer goalStatus;

	private String reason;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 创建人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
