package com.wantwant.sfa.backend.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合伙人目标业绩
 *
 * @since 2022-02-18
 */
@Data
@ToString
@TableName("sfa_employee_goal")
public class EmployeeGoalPo extends Model<EmployeeGoalPo> {

	private static final long serialVersionUID = -2185303597977779080L;

	@TableId(value = "id")
	private Integer id;

	@TableField("position_id")
	private String positionId;

	/**
	 * 时间范围(0:月,1:季度)
	 */
	@TableField("`range`")
	private Integer range;

	/**
	 * 生效年
	 */
	@TableField("`year`")
	private Integer year;

	/**
	 * 生效季度
	 */
	@TableField("`quarter`")
	private Integer quarter;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 业绩目标
	*/
	@TableField("sale_goal")
	private BigDecimal saleGoal;

	private Integer goalStatus;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("created_name")
	private String createdName;

	@TableField("updated_by")
	private String updatedBy;

	@TableField("updated_name")
	private String updatedName;

	/** 0:未删除 1:删除 */
	@TableField("is_delete")
	private Integer isDelete;

}
