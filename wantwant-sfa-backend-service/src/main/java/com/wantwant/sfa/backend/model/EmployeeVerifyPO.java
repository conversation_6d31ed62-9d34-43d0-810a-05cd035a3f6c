package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 品项利润合伙人核实
 *
 * @since 2023-02-09
 */
@Data
@TableName("sfa_employee_verify")
public class EmployeeVerifyPO extends Model<EmployeeVerifyPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	@TableField("the_year_mon")
	private String theYearMon;

	/**
	* sfa_employee_info.id
	*/
	@TableField("employee_info_id")
	private Integer employeeInfoId;

	/**
	* 核实状态(0:未核实,1:正常,2:异常)
	*/
	@TableField("status")
	private Integer status;

	/**
	* 创建人
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 更新人
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;
}
