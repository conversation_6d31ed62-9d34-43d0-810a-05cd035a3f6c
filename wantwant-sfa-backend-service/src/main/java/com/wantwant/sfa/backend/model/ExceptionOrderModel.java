package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 异常订单列表
 * @Auther: zhengxu
 * @Date: 2021/08/17/上午9:34
 */
@Data
public class ExceptionOrderModel {
    @ApiModelProperty("异常品项")
    private String item;
    @ApiModelProperty("大区")
    private String area;
    @ApiModelProperty("分公司")
    private String branch;
    @ApiModelProperty("专员")
    private String employeeName;
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("支付时间")
    private Date payTime;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("客户编码")
    private Long memberKey;
    @ApiModelProperty("收货人姓名")
    private String receiveName;
    @ApiModelProperty("手机号")
    private String mobileNumber;
    @ApiModelProperty("支付方式")
    private String payType;
    @ApiModelProperty("订单来源")
    private String source;
    @ApiModelProperty("处理状态")
    private String processStatus;
    @ApiModelProperty("处理结果")
    private String processResult;
    @ApiModelProperty("处理备注")
    private String remark;
    @ApiModelProperty("订单交易金额")
    private String rmbTransactionAmount;
    @ApiModelProperty("订单状态")
    private String orderStatus;
}
