package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_free_gift_bang_stick_record")
@ApiModel(value = "sfaFreeGiftBangStickRecord对象", description = "")
public class FreeGiftBangStickRecord extends Model<FreeGiftBangStickRecord> {
  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @TableField("customer_id")
  private Integer customerId;

  @TableField("employee_id")
  private String employeeId;

  @TableField("visit_id")
  private Integer visitId;

  @ApiModelProperty(value = "库存单位")
  @TableField("sku")
  private String sku;

  @ApiModelProperty(value = "商品图片")
  @TableField("img_url")
  private String imgUrl;

  @ApiModelProperty(value = "商品名称")
  @TableField("name")
  private String name;

  @ApiModelProperty(value = "口味")
  @TableField("flavour")
  private String flavour;

  @ApiModelProperty(value = "规格")
  @TableField("spec")
  private String spec;

  @ApiModelProperty(value = "赠品图片")
  @TableField(exist = false)
  private List<GiftUrlVo> giftUrl;

  @ApiModelProperty(value = "赠品数量")
  @TableField("amount")
  private Integer amount;

  @ApiModelProperty(value = "是否下单 0，无 1，有")
  @TableField("intention")
  private Integer intention;

  @TableField("create_time")
  private LocalDateTime createTime;

  @TableField("update_time")
  private LocalDateTime updateTime;

  @TableField("is_delete")
  private Integer isDelete;
}
