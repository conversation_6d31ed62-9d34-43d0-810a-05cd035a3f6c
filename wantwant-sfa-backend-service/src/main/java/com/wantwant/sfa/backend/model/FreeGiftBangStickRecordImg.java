package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_free_gift_bang_stick_record_img")
@ApiModel(value = "sfaFreeGiftBangStickRecordImg对象", description = "")
public class FreeGiftBangStickRecordImg extends Model<FreeGiftBangStickRecordImg> {
  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @ApiModelProperty(value = "商品图片")
  @TableField("gift_url")
  private String giftUrl;

  @TableField("gift_record_id")
  private Integer giftRecordId;

  @TableField("create_time")
  private LocalDateTime createTime;

  @TableField("update_time")
  private LocalDateTime updateTime;

  @TableField("is_delete")
  private Integer isDelete;
}
