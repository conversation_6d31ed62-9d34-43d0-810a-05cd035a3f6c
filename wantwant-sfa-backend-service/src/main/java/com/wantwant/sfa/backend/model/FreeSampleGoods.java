package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_free_sample_goods")
@ApiModel(value = "SfaFreeSampleGoods对象", description = "")
public class FreeSampleGoods extends Model<FreeSampleGoods> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @TableField("sku")
  private String sku;

  @ApiModelProperty(value = "商品名称")
  @TableField("name")
  private String name;

  @ApiModelProperty(value = "口味")
  @TableField("flavour")
  private String flavour;

  @ApiModelProperty(value = "规格")
  @TableField("spec")
  private String spec;

  @TableField("img_name")
  private String imgName;

  @ApiModelProperty(value = "商品图片")
  @TableField("img_url")
  private String imgUrl;

  @TableField("create_time")
  private LocalDateTime createTime;

  @TableField("update_time")
  private LocalDateTime updateTime;

  @TableField("is_delete")
  private Integer isDelete;


  @Override
  protected Serializable pkVal() {
    return this.id;
  }

}
