package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_free_sample_record")
@ApiModel(value = "SfaFreeSampleRecord对象", description = "")
public class FreeSampleRecord extends Model<FreeSampleRecord> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @TableField("customer_id")
  private Integer customerId;

  @TableField("employee_id")
  private String employeeId;

  @TableField("visit_id")
  private Integer visitId;

  @TableField("sku")
  private String sku;

  @ApiModelProperty(value = "商品名称")
  @TableField("name")
  private String name;


  @ApiModelProperty(value = "ptKey")
  @TableField("pt_key")
  private Integer ptKey;




  @ApiModelProperty(value = "口味")
  @TableField("flavour")
  private String flavour;

  @ApiModelProperty(value = "规格")
  @TableField("spec")
  private String spec;

  @ApiModelProperty(value = "商品图片")
  @TableField("img_url")
  private String imgUrl;


  @ApiModelProperty(value = "商品图片")
  @TableField("img_name")
  private String imgName;

  @ApiModelProperty(value = "试吃数量")
  @TableField("amount")
  private Integer amount;

  @ApiModelProperty(value = "下单意向 0，无 1，有")
  @TableField("intention")
  private Integer intention;

  @ApiModelProperty(value = "价格评价 0 高 1 适中 2一般")
  @TableField("price_evaluation")
  private Integer priceEvaluation;

  @ApiModelProperty(value = "口味评价 0 好 1 一般 2 差")
  @TableField("taste_evaluation")
  private Integer tasteEvaluation;

  @TableField("pic_name")
  private String picName;

  @TableField("pic_url")
  private String picUrl;

  @ApiModelProperty(value = "其他评价")
  @TableField("comment")
  private String comment;

  @ApiModelProperty(value = "试吃评价")
  @TableField("taste_comment")
  private String tasteComment;



  @TableField("create_time")
  private LocalDateTime createTime;

  @TableField("update_time")
  private LocalDateTime updateTime;

  @TableField("is_delete")
  private Integer isDelete;


  @Override
  protected Serializable pkVal() {
    return this.id;
  }

}
