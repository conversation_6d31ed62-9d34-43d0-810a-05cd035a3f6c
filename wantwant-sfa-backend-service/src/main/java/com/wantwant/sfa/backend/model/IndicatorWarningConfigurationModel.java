package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仓储导入表
 *
 * @since 2022-06-22
 */
@Data
@TableName("indicator_warning_configuration")
public class IndicatorWarningConfigurationModel extends Model<IndicatorWarningConfigurationModel> {
	private static final long serialVersionUID = 1L;

	/**
	* id
	*/
	@TableId(value = "id")
	private Integer id;

	/**
	* 指标等级
	*/
	@TableField("indicators_level")
	private Integer indicatorsLevel;

	/**
	* 指标名称
	*/
	@TableField("indicators_name")
	private String indicatorsName;

	/**
	* 指标字段
	*/
	@TableField("indicators_field")
	private String indicatorsField;

	/**
	 * 指标字段
	 */
	@TableField("indicators_color_field")
	private String indicatorsColorField;

	/**
	* 指标表字段
	*/
	@TableField("indicators_table_field")
	private String indicatorsTableField;

	/**
	* 预警值
	*/
	@TableField("warning_value")
	private String warningValue;

	/**
	* 单位
	*/
	@TableField("unit")
	private String unit;

	/**
	 * 计算逻辑
	 */
	@TableField("calculate_logic")
	private String calculateLogic;

	/** 
	 * 标色规则
	 */
	@TableField("color_rules")
	private String colorRules;


	@ApiModelProperty(value = "创建时间")
	@TableField("create_time")
	private LocalDateTime createdTime;

	@ApiModelProperty(value = "创建人")
	@TableField("create_people")
	private String createPeople;

	@ApiModelProperty(value = "更新时间")
	@TableField("update_time")
	private LocalDateTime updatedTime;

	@ApiModelProperty(value = "更新人")
	@TableField("update_people")
	private String updatepeople;

	@ApiModelProperty(value = "是否删除")
	@TableField("is_delete")
	private Integer isDelete;

}
