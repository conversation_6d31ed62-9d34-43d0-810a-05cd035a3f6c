package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("inventory_production_batch_detail")
@ApiModel(value = "InventoryProductionBatchDetail对象", description = "")
public class InventoryProductionBatchDetail extends Model<InventoryProductionBatchDetail> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "货品外部编码")
    @TableField("sku")
    private String sku;

    @ApiModelProperty(value = "平台总库存")
    @TableField("inventory_total")
    private Integer inventoryTotal;

    @ApiModelProperty(value = "平台锁定库存")
    @TableField("inventory_lock")
    private Integer inventoryLock;

    @ApiModelProperty(value = "平台占用")
    @TableField("inventory_occupy")
    private Integer inventoryOccupy;

    @ApiModelProperty(value = "平台可用库存")
    @TableField("inventory_available")
    private Integer inventoryAvailable;

    @ApiModelProperty(value = "批次售卖数量-做统计使用")
    @TableField("sold")
    private Integer sold;

    @ApiModelProperty(value = "批次号")
    @TableField("batch_mgmt_number")
    private String batchMgmtNumber;

    @ApiModelProperty(value = "生产日期")
    @TableField("production_date")
    private LocalDateTime productionDate;

    @ApiModelProperty(value = "到期时间")
    @TableField("expire_date")
    private LocalDateTime expireDate;

    @ApiModelProperty(value = "sku货物编码")
    @TableField("goods_batch_code")
    private String goodsBatchCode;

    @ApiModelProperty(value = "wms总库存")
    @TableField("wms_inventory_total")
    private Integer wmsInventoryTotal;

    @ApiModelProperty(value = "wms占用库存")
    @TableField("wms_inventory_occupy")
    private Integer wmsInventoryOccupy;

    @ApiModelProperty(value = "wms锁定库存")
    @TableField("wms_inventory_lock")
    private Integer wmsInventoryLock;

    @ApiModelProperty(value = "货品EAN码")
    @TableField("goods_barcode")
    private String goodsBarcode;

    @ApiModelProperty(value = "仓库编码")
    @TableField("out_ware_code")
    private String outWareCode;

    @ApiModelProperty(value = "货主id")
    @TableField("customer_id")
    private String customerId;

    @ApiModelProperty(value = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "状态 0-有效 1-无效")
    @TableField("delete_flag")
    private Boolean deleteFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
