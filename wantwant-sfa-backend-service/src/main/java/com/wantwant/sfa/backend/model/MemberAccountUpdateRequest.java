package com.wantwant.sfa.backend.model;

import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@Data
@ToString
public class MemberAccountUpdateRequest {

    @ApiModelProperty(value = "memberKey")
    @NotNull(message = "参数错误：memberKey不能为空")
    private Long memberKey;

    @ApiModelProperty(value = "工作类型：0-全职、1-兼职、2-造旺总监、3-企业合伙人、4-区域经理")
    @NotBlank(message = "参数错误：合伙人工作类型不能为空")
    private String ex1;

    @ApiModelProperty(value = "是否试岗：是 - 1、否 - 0")
    @NotBlank(message = "参数错误：合伙人试岗类型不能为空")
    private String ex2;

    @ApiModelProperty("实际入职日期  yyyy-mm-dd hh:mm:ss")
    private String realInductionDate;

    @ApiModelProperty("最后操作日期  yyyy-mm-dd hh:mm:ss")
    private String processDate;

    @ApiModelProperty("异动时间：yyyy-mm-dd hh:mm:ss")
    private String variationTime;

    @ApiModelProperty(value = "试岗开始日期，ex1=4 且 ex2=1 时必传")
    private String trialStartTime;

    @ApiModelProperty(value = "产品组信息")
    @NotEmpty.List(@NotEmpty(message = "参数错误：合伙人产品组信息不能为空"))
    @Size(min = 1,message = "参数错误：合伙人产品组信息不能为空")
    private List<MemberProductGroupInfoVo> memberProductGroupInfos;


    public void setEx1(Integer ceoType,Integer jobsType,Integer position){
        int positionId = PositionEnum.getPositionId(ceoType, jobsType, position);
        if(positionId == PositionEnum.CEO.getId()){
            this.ex1 = "0";
        }
        if(positionId == PositionEnum.PART_TIME_CEO.getId()){
            this.ex1 = "1";
        }
        if(positionId == PositionEnum.MANAGER.getId()){
            this.ex1 = "2";
        }
        if(positionId == PositionEnum.BUSINESS_CEO.getId()){
            this.ex1 = "3";
        }
        if(positionId == PositionEnum.CITY_MANAGER.getId()){
            this.ex1 = "4";
        }
        if(positionId == PositionEnum.CONTRACT_CEO.getId()){
            this.ex1 = "5";
        }
        if(positionId == PositionEnum.AREA_MANAGER.getId()){
            this.ex1 = "6";
        }
        if(positionId == PositionEnum.PROVINCE_MANAGER.getId()){
            this.ex1 = "7";
        }
        if(positionId == PositionEnum.BUSINESS_BD.getId()){
            this.ex1 = "8";
        }
        if(positionId == PositionEnum.BUSINESS_BD_CONTRACT.getId()){
            this.ex1 = "9";
        }
        if(positionId == PositionEnum.BUSINESS_BD_PART_TIME.getId()){
            this.ex1 = "10";
        }
        if(positionId == PositionEnum.VAREA_MANAGER.getId()){
            this.ex1 = "11";
        }
    }

    public void setEx2(Integer employeeStatus){
        if(employeeStatus == 1){
            this.ex2 = "1";
        }else{
            this.ex2 = "0";
        }
    }
}