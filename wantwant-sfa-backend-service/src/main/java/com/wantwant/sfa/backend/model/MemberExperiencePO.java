package com.wantwant.sfa.backend.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 应聘者工作经历
 *
 * @since 2021-12-23
 */
@Data
@TableName("sfa_member_experience")
public class MemberExperiencePO extends Model<MemberExperiencePO> {

	private static final long serialVersionUID = 5702685112736551067L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_apply_member.id
	*/
	@TableField("application_id")
	private Integer applicationId;

	/**
	* 起始时间
	*/
	@TableField("start_date")
	private LocalDate startDate;

	/**
	* 终止日期
	*/
	@TableField("end_date")
	private LocalDate endDate;

	/**
	* 公司
	*/
	@TableField("company")
	private String company;

	/**
	* 职位
	*/
	@TableField("position")
	private String position;

	/**
	* 团队规模
	*/
	@TableField("team_size")
	private Integer teamSize;

	/**
	* 备注
	*/
	@TableField("remark")
	private String remark;

	/**
	* 创建时间
	*/
	@TableField("create_at")
	private LocalDateTime createAt;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

	@TableField("industry")
	private String industry;

	@TableField("production_category")
	private String productionCategory;
}
