package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class MemberProductGroupInfoVo {

    @ApiModelProperty(value = "17832843",notes = "用户唯一id")
    private Long memberKey;

    @ApiModelProperty(value = "产品组ID")
    private String productGroupId;

    @ApiModelProperty(value = "大类ID信息")
    private List<String> categoryIds;

    @ApiModelProperty(value = "所选sku")
    private List<String> skuList;

    @ApiModelProperty(value = "小标市场信息")
    private List<String> smallMarketIds;

    @ApiModelProperty(value = "造旺渠道：2-造旺")
    private Integer businessCode = 2;

    @ApiModelProperty(value = "LB",notes = "战区code")
    private String areaCode;

    @ApiModelProperty(value = "两北区",notes = "大区code")
    private String area;

    @ApiModelProperty(value = "LB",notes = "大区code")
    private String regionCode;

    @ApiModelProperty(value = "两北区",notes = "大区")
    private String region;

    @ApiModelProperty(value = "AH",notes = "省区code")
    private String provinceCode;

    @ApiModelProperty(value = "安徽省",notes = "省区")
    private String provinceArea;

    @ApiModelProperty(value = "CX3",notes = "分公司code")
    private String companyCode;

    @ApiModelProperty(value = "保定分",notes = "分公司")
    private String company;

    @ApiModelProperty(value = "2412",notes = "营业所code")
    private String branchCode;

    @ApiModelProperty(value = "河间所",notes = "营业所")
    private String branch;

    @ApiModelProperty(value = "河北省",notes = "省")
    private String province;

    @ApiModelProperty(value = "沧州市",notes = "市")
    private String city;

    @ApiModelProperty(value = "任丘市",notes = "县区")
    private String district;

    @ApiModelProperty(value = "某某街道",notes = "街道地址")
    private String street;

    @ApiModelProperty(value = "岗位性质：0-主岗、1-兼岗",notes = "岗位性质：0-主岗、1-兼岗")
    private Integer jobNature = 0;

    @ApiModelProperty("工作类型：0-全职、1-兼职、2-造旺总监、3-企业合伙人、4-区域经理")
    private String ex1;

    @ApiModelProperty("是否试岗：是 - 1、否 - 0")
    private String ex2;

    @ApiModelProperty(value = "签约公司")
    private String signingCompany;

    @ApiModelProperty(value = "承揽标识 0-否 1-是")
    private Integer contractJobFlag;

    @ApiModelProperty(value = "入岗时间:yyyy-MM-dd HH:mm:ss")
    private String entryTime;

    @ApiModelProperty(value = "承揽公司名称")
    private String contractJobCompanyName;

}