package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ApiModel
@ToString
public class MemberSmallMarketUpdateRequest {

    @ApiModelProperty(value = "用户memberKey")
    private Long memberKey;

    @ApiModelProperty(value = "产品组ID")
    private String productGroupId;

    @ApiModelProperty(value = "小标市场ID")
    private String smallMarketId;

}