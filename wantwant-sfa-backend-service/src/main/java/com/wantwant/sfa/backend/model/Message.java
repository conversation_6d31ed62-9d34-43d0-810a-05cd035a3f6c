package com.wantwant.sfa.backend.model;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Message {

    Integer id;

    @ApiModelProperty("模板表ID")
    Integer templateId;

    @ApiModelProperty("类型：101系统消息，102版本升级，201订单付款，202订单完成，203特陈提交，204特陈审核，205特陈驳回，301客户开户提交，302客户审核，303客户驳回")
    Integer type;

    @ApiModelProperty("标题")
    String title;

    @ApiModelProperty("消息描述")
    String message;

    @ApiModelProperty("图片")
    String img;

    @ApiModelProperty("额外参数")
    String jsonData;

    @ApiModelProperty(value = "消息标签：0 常见消息，1 新品上架，2 活动通知，3 政策通知")
    private int label;

    @ApiModelProperty(value = "弹窗提示：0 不提示，1 提示")
    private int isPrompt;

    @ApiModelProperty(value = "启动页提示：0 不提示，1 提示")
    private int isStartup;

    @ApiModelProperty("额外参数对象")
    JSONObject jsonObject;

    @ApiModelProperty("业务员")
    String createEmployeeId;

    @ApiModelProperty("业务员姓名")
    String createEmployeeName;

    @ApiModelProperty(value = "失效时间")
    private Date endDate;

    @ApiModelProperty(value = "置顶标签 0:未置顶 1:置顶")
    private Integer isTop;

    @ApiModelProperty("消息模板")
    MessageTemplate messageTemplate;


    List<MessageSendRecord> messageSendRecordList;

}
