package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/25/上午9:03
 */
@Data
@ToString
public class MessageInfoModel {

    private String creator;

    private String enclosure;

    private List<MessageRelationModel> relationModes;

    private String messageContent;

    private String messageImages;

    private String messageLabel;

    private String messageTitle;

    private Integer messageType;

    private Integer messageViewType;

    private Integer pushFlag;

    private String summary;

    private Integer reminderFlag;

    @ApiModelProperty(value = "消息类别 0-公告类 1-政策类")
    private Integer messageCategoryType;
}
