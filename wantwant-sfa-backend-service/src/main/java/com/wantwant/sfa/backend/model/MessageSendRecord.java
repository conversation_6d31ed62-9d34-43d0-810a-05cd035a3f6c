package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MessageSendRecord {

	Integer id;
	
	@ApiModelProperty("消息表ID")
	Integer messageId;
	
	@ApiModelProperty("接受app类型：0 所有,1 安卓,2 ios")
	int appType;

    @ApiModelProperty(value = "弹窗提示：0 不提示，1 提示")
    int isPrompt;

	@ApiModelProperty("状态: 0未发放,1未读,2已读")
	Integer status;
	
	@ApiModelProperty("状态: 0未查看,1收到,2不懂")
	Integer result;
	
	@ApiModelProperty("不懂回复")
	String reason;
	
	@ApiModelProperty("原收件人工号")
	String originalEmployeeId;	
	
	@ApiModelProperty("业务员工号")
	String employeeId;
	
	@ApiModelProperty("业务员姓名")
	String employeeName;
	
	@ApiModelProperty("岗位ID")
	String positionId;
	
	@ApiModelProperty("组织Id")
	String organizationId;	
	
	@ApiModelProperty("组织Name")
	String organizationName;
	
	@ApiModelProperty("大区")
	String area;
	
	@ApiModelProperty("大区组织ID")
	String areaOrganizationId;
	
	@ApiModelProperty("分公司")
	String company;
	
	@ApiModelProperty("分公司组织ID")
	String companyOrganizationId;

	String department;

	String departmentOrganizationId;
	
	@ApiModelProperty("营业所")
	String branch;
	
	@ApiModelProperty("营业所组织ID")
	String branchOrganizationId;
	
	@ApiModelProperty("职位：1：大区，2：分公司，3：营业所")
	Integer positionType;
	
	@ApiModelProperty("个推任务ID")
	String getuiContentId;
	
	@ApiModelProperty("个推推送状态")
	String getuiResult;
	
	@ApiModelProperty("个推title")
	String getuiTitle;
	
	@ApiModelProperty("个推消息体")
	String getuiMessage;
	
	@ApiModelProperty("创建人")
	String createEmployeeId;
	
	@ApiModelProperty("创建人姓名")
	String createEmployeeName;
	
	@ApiModelProperty("创建人组织id")
	String createOrganizationId;
	
	@ApiModelProperty("创建人组织名称")
	String createOrganizationName;

	@ApiModelProperty("员工表主键")
	Integer employeeInfoId;

	@ApiModelProperty("类型")
	Integer messageType;


	int terminal = 2;
	
	Message message;
}
