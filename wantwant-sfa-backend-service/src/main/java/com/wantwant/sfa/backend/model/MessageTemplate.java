package com.wantwant.sfa.backend.model;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MessageTemplate {
	
	Integer id;
	
	@ApiModelProperty("标题")
	String title;
	
	@ApiModelProperty("内容")
	String content;
	
	@ApiModelProperty("附件")
	String enclosure;
	
	@ApiModelProperty("状态: 0草稿,1提交")
	List<String> enclosureList;
	
	@ApiModelProperty("业务员姓名")
	int status;
	
	@ApiModelProperty("创建人id")
	String createEmployeeId;
	
	@ApiModelProperty("创建人姓名")
	String createEmployeeName;
}
