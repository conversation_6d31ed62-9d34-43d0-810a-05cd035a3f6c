package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_mobile_captcha")
@ApiModel(value = "SfaMobileCaptcha对象", description = "")
public class MobileCaptcha extends Model<MobileCaptcha> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("mobile")
    private String mobile;

    @ApiModelProperty(value = "验证码")
    @TableField("code")
    private String code;

    @TableField("is_effective")
    private Integer isEffective;

    @ApiModelProperty(value = "生效起始时间")
    @TableField("effective_time")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "失效时间")
    @TableField("expiration_time")
    private LocalDateTime expirationTime;

    @TableField("is_delete")
    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
