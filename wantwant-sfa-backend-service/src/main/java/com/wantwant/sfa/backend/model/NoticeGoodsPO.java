package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("sfa_notice_goods")
public class NoticeGoodsPO extends Model<NoticeGoodsPO>{

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    @TableField(value = "channel_id")
    private String channelId;

    @TableField(value = "channel_name")
    private String channelName;

    @TableField(value = "sku")
    private String sku;

    @TableField(value = "sku_name")
    private String skuName;

    @TableField(value = "batch_code")
    private String batchCode;

    @TableField(value = "put_on_shelves_nums")
    private String putOnShelvesNums;

    @TableField(value = "available_for_sale_nums")
    private String availableForSale;
    @TableField("create_time")
    private LocalDateTime createTime;
}
