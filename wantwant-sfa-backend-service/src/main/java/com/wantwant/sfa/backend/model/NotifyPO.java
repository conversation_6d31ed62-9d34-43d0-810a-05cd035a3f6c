package com.wantwant.sfa.backend.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 通知表
 *
 * @since 2022-03-07
 */
@Data
@TableName("sfa_notify")
public class NotifyPO extends Model<NotifyPO> {

	private static final long serialVersionUID = 7115647051123590378L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	/**
	* 标题
	*/
	@TableField("title")
	private String title;

	/**
	* 类型(1:系统任务,2:提醒消息,3.系统提醒)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 内容
	*/
	@TableField("content")
	private String content;

	/**
	* 状态(0:未读,1:已读)
	*/
	@TableField("status")
	private Integer status;

	/** 
	 * 操作(1:合伙人目标设置,2:经销商授权管理)
	 */
	@TableField("code")
	private String code;

	/**
	* 通知人
	*/
	@TableField("employee_id")
	private String employeeId;

	/**
	* 创建人
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	* 更新时间
	*/
	@TableField("update_time")
	private LocalDateTime updateTime;

	/**
	* 备注
	*/
	@TableField("remark")
	private String remark;


	@TableField("template_id")
	private Long templateId;

	/** 
	 * 是否收藏消息(0:取消,1:收藏)
	 */
	@TableField("is_favorite")
	private Integer isFavorite;

	/** 
	 * 收藏/取消时间
	 */
	@TableField("favorite_time")
	private LocalDateTime favoriteTime;

	/**
	 * 查看时间
	 */
	@TableField("see_time")
	private LocalDateTime seeTime;

	/** 
	 * 周报期数
	 */
	@TableField("cycle")
	private Integer cycle;
	
	/** 
	 * 周报开始时间
	 */
	@TableField("start_date")
	private LocalDate startDate;

	/** 
	 * 周报结束时间
	 */
	@TableField("end_date")
	private LocalDate endDate;

	/** 
	 * 组织ID
	 */
	@TableField("organization_id")
	private String organizationId;

	/** 
	 * sfa_work_report.id
	 */
	@TableId(value = "work_id")
	private Long workId;

}
