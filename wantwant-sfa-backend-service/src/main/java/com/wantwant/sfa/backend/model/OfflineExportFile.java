package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_offline_export_file")
@ApiModel(value = "SfaOfflineExportFile对象", description = "")
public class OfflineExportFile extends Model<OfflineExportFile> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "唯一Code")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "操作人ID")
    @TableField("operator_id")
    private String operatorId;

    @ApiModelProperty(value = "操作人姓名")
    @TableField("operator_name")
    private String operatorName;

    @ApiModelProperty(value = "导出操作名称")
    @TableField("export_name")
    private String exportName;

    @ApiModelProperty(value = "文件名")
    @TableField("file_name")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    @TableField("file_dir")
    private String fileDir;

    @ApiModelProperty(value = "文件大小")
    @TableField("file_size")
    private String fileSize;

    @ApiModelProperty(value = "请求beanName")
    @TableField("request_bean_name")
    private String requestBeanName;


    @ApiModelProperty(value = "请求方法名")
    @TableField("request_method")
    private String requestMethod;

    @ApiModelProperty(value = "请求参数")
    @TableField("request_param")
    private String requestParam;

    @ApiModelProperty(value = "请求参数完整类名")
    @TableField("request_param_class_name")
    private String requestParamClassName;


    @ApiModelProperty(value = "状态 0初始化，1成功，-1失败")
    @TableField("status")
    private Integer status;


    @ApiModelProperty(value = "失败原因")
    @TableField("fail_reason")
    private String failReason;

    @ApiModelProperty(value = "是否删除")
    @TableField("is_delete")
    private Boolean isDelete;

    @TableField("request_time")
    private LocalDateTime requestTime;

    @TableField("complete_time")
    private LocalDateTime completeTime;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("download_time")
    private LocalDateTime downloadTime;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
