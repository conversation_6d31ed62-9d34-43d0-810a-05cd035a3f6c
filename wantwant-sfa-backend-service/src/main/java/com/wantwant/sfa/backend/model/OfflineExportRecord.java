package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_offline_export_record")
@ApiModel(value = "SfaOfflineExportRecord对象", description = "")
public class OfflineExportRecord extends Model<OfflineExportRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("downloader_id")
    private String downloaderId;

    @TableField("downloader_name")
    private String downloaderName;

    @TableField("download_code")
    private String downloadCode;

    @TableField("download_time")
    private LocalDateTime downloadTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
