package com.wantwant.sfa.backend.model;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/07/10/上午9:30
 */
@Data
@ToString
public class OpenAccountModel {

    @NotBlank(message = "参数错误：手机号不能为空")
    @ApiModelProperty(value = "手机号")
    private String mobileNumber;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "头像")
    private String imageName;

    @ApiModelProperty(value = "性别：男-M、女-F")
    private String gender;

    @ApiModelProperty(value = "岗位性质：0-主岗、1-兼岗")
    private Integer jobNature = 0;

    @ApiModelProperty(value = "生日")
    private String dob;

    @ApiModelProperty(value = "入职日期")
    private String workDate;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @NotBlank(message = "参数错误：合伙人工作类型不能为空")
    @ApiModelProperty(value = "工作类型：0-全职、1-兼职、2-造旺总监、3-企业合伙人、4-区域经理、8-全职业务BD、9-承揽业务BD、10-兼职业务BD")
    private String ex1;

    @NotBlank(message = "参数错误：合伙人试岗类型不能为空")
    @ApiModelProperty(value = "是否试岗：是 - 1、否 - 0")
    private String ex2;

    @ApiModelProperty("实际入职日期  yyyy-mm-dd hh:mm:ss")
    private String realInductionDate;

    @ApiModelProperty("最后操作日期  yyyy-mm-dd hh:mm:ss")
    private String processDate;

    @ApiModelProperty(value = "地址信息")
    List<SfaInsertOrUpdateAddressRequest> addressRequests;

    @NotEmpty.List(@NotEmpty(message = "参数错误：合伙人产品组信息不能为空"))
    @Size(min = 1,message = "参数错误：合伙人产品组信息不能为空")
    @ApiModelProperty(value = "产品组信息")
    List<MemberProductGroupInfoVo> memberProductGroupInfos;


    @ApiModelProperty("试岗开始时间")
    private String trialStartTime;

    /**
     * 设置基础数据
     *
     * @param mobileNumber
     * @param name
     * @param gender
     * @param companyName
     */
    public void setBaseInfo(String mobileNumber,String name,Integer gender,String companyName,String birthday,String imageName){
        this.mobileNumber = mobileNumber;
        this.name = name;
        if(Objects.nonNull(gender) && gender == 1){
            this.gender = "M";
        }else{
            this.gender = "F";
        }
        this.imageName = imageName;
        this.companyName = companyName;
        if(StringUtils.isNotBlank(birthday)){
            LocalDate bd = LocalDate.parse(birthday);
            this.dob = LocalDateTimeUtils.formatTime(bd.atStartOfDay(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
        }
        this.dob = dob;
    }

    public void setEx1(Integer ceoType,Integer jobsType,Integer position){
        int positionId = PositionEnum.getPositionId(ceoType, jobsType, position);
        if(positionId == PositionEnum.CEO.getId()){
            this.ex1 = "0";
        } else if(positionId == PositionEnum.PART_TIME_CEO.getId()){
            this.ex1 = "1";
        } else if(positionId == PositionEnum.MANAGER.getId()){
            this.ex1 = "2";
        } else if(positionId == PositionEnum.BUSINESS_CEO.getId()){
            this.ex1 = "3";
        } else if(positionId == PositionEnum.CITY_MANAGER.getId()){
            this.ex1 = "4";
        } else if(positionId == PositionEnum.CONTRACT_CEO.getId()){
            this.ex1 = "5";
        } else if(positionId == PositionEnum.BUSINESS_BD.getId()){
            this.ex1 = "8";
        } else if(positionId == PositionEnum.BUSINESS_BD_CONTRACT.getId()){
            this.ex1 = "9";
        } else if(positionId == PositionEnum.BUSINESS_BD_PART_TIME.getId()){
            this.ex1 = "10";
        } else if(positionId == PositionEnum.AREA_MANAGER.getId()){
            this.ex1 = "6";
        } else if(positionId == PositionEnum.PROVINCE_MANAGER.getId()){
            this.ex1 = "7";
        } else if( positionId == PositionEnum.VAREA_MANAGER.getId()){
            this.ex1 = "11";
        }else if( positionId == PositionEnum.CUSTOMER_DIRECTOR.getId()){
            this.ex1 = "13";
        }
    }

    public void setEx2(Integer employeeStatus){
        if(employeeStatus == 1){
           this.ex2 = "1";
        }else{
            this.ex2 = "0";
        }
    }

    public void setRealInductionDate(String realInductionDate){
        this.realInductionDate = realInductionDate;
        this.setProcessDate(LocalDateTimeUtils.formatNow(LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
    }



}
