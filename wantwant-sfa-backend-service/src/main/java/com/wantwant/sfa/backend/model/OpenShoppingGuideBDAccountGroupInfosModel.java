package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Auther: zhangpengpeng
 * @Date: 2024/07/03
 */
@Data
@ToString
public class OpenShoppingGuideBDAccountGroupInfosModel {

    @ApiModelProperty(value = "产品组ID")
    private String productGroupId;

    @ApiModelProperty(value = "LB", notes = "战区code")
    private String areaCode;

    @ApiModelProperty(value = "两北区", notes = "大区code")
    private String area;

    @ApiModelProperty(value = "LB", notes = "大区code")
    private String regionCode;

    @ApiModelProperty(value = "两北区", notes = "大区")
    private String region;

    @ApiModelProperty(value = "AH", notes = "省区code")
    private String provinceCode;

    @ApiModelProperty(value = "安徽省", notes = "省区")
    private String provinceArea;

    @ApiModelProperty(value = "CX3", notes = "分公司code")
    private String companyCode;

    @ApiModelProperty(value = "保定分", notes = "分公司")
    private String company;

    @ApiModelProperty(value = "2412", notes = "营业所code")
    private String branchCode;

    @ApiModelProperty(value = "河间所", notes = "营业所")
    private String branch;

}
