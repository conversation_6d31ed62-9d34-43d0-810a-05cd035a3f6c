package com.wantwant.sfa.backend.model;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhangpengpeng
 * @Date: 2024/07/03
 */
@Data
@ToString
public class OpenShoppingGuideBDAccountModel {

    @ApiModelProperty(value = "手机号")
    private String mobileNumber;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别：男-M、女-F")
    private String gender;

    @ApiModelProperty(value = "生日")
    private String dob;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作人工号")
    private String operator;

    @ApiModelProperty(value = "身份：12-短促导购BD、14-长促导购BD",example = "开发")
    private String ex;

    @ApiModelProperty(value = "产品组组织信息")
    List<OpenShoppingGuideBDAccountGroupInfosModel> groupInfos;


    /**
     * 设置基础数据
     */
    public void setBaseInfo(String mobile, String name, Integer gender, LocalDate birthday, String remark, String person) {
        this.mobileNumber = mobile;
        this.name = name;
        if (Objects.nonNull(gender) && gender == 1) {
            this.gender = "M";
        } else {
            this.gender = "F";
        }
        if (Objects.nonNull(birthday)) {
            this.dob = LocalDateTimeUtils.formatTime(birthday.atStartOfDay(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
        }
        this.remark = remark;
        this.operator = person;
    }

}
