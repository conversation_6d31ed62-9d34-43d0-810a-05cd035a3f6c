package com.wantwant.sfa.backend.model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Author: luxiaoyin
 * @Date: 2020/8/12
 * @Package: com.wantwant.sfa.backend.model
 */
@Data
public class OrderDailyStats {
    private Integer year;

    private Integer month;

    private Integer day;

    private LocalDateTime proAt;

    private BigDecimal amount;

    private BigDecimal saleAmount;

}
