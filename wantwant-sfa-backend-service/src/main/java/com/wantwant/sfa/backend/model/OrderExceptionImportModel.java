package com.wantwant.sfa.backend.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Description: 导入异常订单Model
 * @Auther: zhengxu
 * @Date: 2021/08/11/下午3:41
 */
@Data
@ApiModel(value = "SfaGoal导入对象", description = "")
public class OrderExceptionImportModel {
    @Excel(name = "订单号")
    private String orderNo;
    @Excel(name = "处理结果")
    private String process;
    @Excel(name = "处理备注")
    private String remark;
}
