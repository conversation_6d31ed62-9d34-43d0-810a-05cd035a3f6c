package com.wantwant.sfa.backend.model;

import lombok.Data;

/**
 * @Description: 异常订单信息用Model。
 * @Auther: zhengxu
 * @Date: 2021/08/16/上午10:35
 */
@Data
public class OrderExceptionModel {
    /** 订单号 */
    private String orderNo;
    /** 删除标记 */
    private int isDelete;
    /** 大区Id */
    private String areaId;
    /** 大区名称 */
    private String areaName;
    /** 分公司Id */
    private String branchId;
    /** 分公司名称 */
    private String branchName;
    /** 店铺名称 */
    private String storeName;
    /** 员工ID */
    private String employeeId;
    /** 员工名称 */
    private String employeeName;
    /** 月份 */
    private String month;
    /** 异常状态 */
    private int exception;
    /** 所属渠道 */
    private int channel;
    /** 处理结果 */
    private int status;
    /** 处理备注 */
    private String remark;

}
