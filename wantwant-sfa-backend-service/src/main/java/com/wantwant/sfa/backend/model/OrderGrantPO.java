package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单发放记录
 *
 * @since 2022-06-06
 */
@Data
@TableName("sfa_order_grant")
public class OrderGrantPO extends Model<OrderGrantPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* ceo_order_header.code
	*/
	@TableField("code")
	private String code;

	/**
	* 发放金额
	*/
	@TableField("amount")
	private BigDecimal amount;

	/**
	* 状态(0:未发放,1:部分发放,2:全部发放)
	*/
	@TableField("state")
	private Integer state;

	/**
	* 创建人
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	* 更新时间
	*/
	@TableField("update_time")
	private LocalDateTime updateTime;

}
