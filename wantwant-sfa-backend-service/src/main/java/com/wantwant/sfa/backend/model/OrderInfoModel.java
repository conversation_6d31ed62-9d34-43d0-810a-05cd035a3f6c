package com.wantwant.sfa.backend.model;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 订单详情用Model。
 * @Auther: zhengxu
 * @Date: 2021/08/17/下午6:10
 */
@Data
public class OrderInfoModel {
    /** 客户ID */
    private Integer customerId;
    /** 岗位ID */
    private String positionId;
    /** 客户类型 */
    private String customerType;
    /** 是否潜在客户 */
    private Integer isPotential;
    /** 岗位ID */
    private Integer isVerified;
    /** 来源 */
    private String channel;
    /** 是否审核 */
    private String storeChannel;
    /** 是否确认售后 */
    private Integer isManualReceived;
    /** 客户编码*/
    private Long memberKey;
    /** 手机号 */
    private String mobileNumber;
    /** 订单号 */
    private String orderNo;
    /** 订单状态 */
    private String status;
    /** 订单类型 */
    private Integer orderType;
    /** 创建时间 */
    private Date placeAt;
    /** 处理时间 */
    private Date processingAt;
    /** 发货时间 */
    private Date deliveringAt;
    /** 收货时间*/
    private Date receivedAt;
    /** 完成时间 */
    private Date completeAt;
    /** 收件人省 */
    private String receiverProvince;
    /** 收件人市 */
    private String receiverCity;
    /** 收件人区 */
    private String receiverDistrict;
    /** 收件人街道 */
    private String receiverStreet;
    /** 收件人姓名 */
    private String receiverName;
    /** 收件人手机号 */
    private String receiverMobileNumber;
    /** 售后服务原因 */
    private String serviceReason;
    /** 售后服务描述 */
    private String serviceDescription;
    /** 交易金额 */
    private Float rmbTransactionAmount;
    /** 配送费 */
    private Float deliveryFee;
    /** 折扣描述 */
    private String discountDescription;
    /** 折扣金额 */
    private Float discountAmount;
    /** 满减金额 */
    private Float fullReductionAmount;
    /** 发票类型 */
    private Integer invoiceType;

    private Float itemsTotal;
    /** 总价 */
    private Float grandTotal;
    /** 商品划线价合计 */
    private Float origTotal;

    /** 是否开票 */
    private Integer isInvoice;
    /** 发票标题 */
    private String invoiceTitle;
    /** 发票收件人姓名 */
    private String invoiceReceiverName;
    /** 发票收件人手机号 */
    private String invoiceReceiverMobileNumber;
    /** 发票收件人地址省 */
    private String invoiceReceiverProvince;
    /** 发票收件人地址市 */
    private String invoiceReceiverCity;
    /** 发票收件人地址区 */
    private String invoiceReceiverDistrict;
    /** 发票收件人地址街道 */
    private String invoiceReceiverStreet;
    /** 开票银行 */
    private String invoiceIssuedBank;
    /** 开票银行行号 */
    private String invoiceBankAcctNumber;
    /** 开票公司省 */
    private String invoiceCompanyProvince;
    /** 开票公司市 */
    private String invoiceCompanyCity;
    /** 开票公司区 */
    private String invoiceCompanyDistrict;
    /** 开票公司街道 */
    private String invoiceCompanyStreet;
    /** 开票公司点好 */
    private String invoiceCompanyPhone;
    /** 开票税号 */
    private String invoiceTaxId;
    /** 优惠券折扣金额 */
    private Float couponDiscountAmount;
    /** 员工Id */
    private String empid;
    /** 支付状态 */
    private String rmbTransactionStatus;
    private String statusOrder;
}
