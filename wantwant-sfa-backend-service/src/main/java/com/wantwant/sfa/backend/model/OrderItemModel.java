package com.wantwant.sfa.backend.model;

import lombok.Data;

/**
 * @Description: 订单项目Model。
 * @Auther: zhengxu
 * @Date: 2021/08/17/下午7:12
 */
@Data
public class OrderItemModel {
    /** 订单号 */
    private String orderNo;
    /** sku */
    private String sku;
    /** 商品名称 */
    private String skuName;
    /** 商品数量 */
    private Integer quantity;
    /** 口味 */
    private String flavour;
    /** 规格 */
    private String spec;
    /** 退款金额 */
    private Float retailSubtotal;
    /** 订单金额 */
    private Float orderItemPrice;
    /** ceo名称 */
    private String ceoName;
    /** 是否赠品 */
    private Integer isGiveaway;

    private String productTemplateCode;
}
