package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 招聘需求
 *
 * @since 2022-03-10
 */
@Data
@TableName("sfa_recruitment_needs")
public class RecruitmentNeedsPO extends Model<RecruitmentNeedsPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 分公司组织ID
	*/
	@TableField("company_organization_id")
	private String companyOrganizationId;

	/**
	* 报名岗位(1:造旺合伙人,2:造旺区域总监)
	*/
	@TableField("position")
	private Integer position;

	/**
	* 省
	*/
	@TableField("province_name")
	private String provinceName;

	/**
	* 市
	*/
	@TableField(value = "city_name",strategy = FieldStrategy.IGNORED)
	private String cityName;

	/**
	* 区县
	*/
	@TableField(value = "district_name",strategy = FieldStrategy.IGNORED)
	private String districtName;

	/**
	* 招聘要求
	*/
	@TableField("requirement")
	private String requirement;

	/**
	* 招聘人数
	*/
	@TableField("recruit_num")
	private Integer recruitNum;

	/**
	* 入职人数
	*/
	@TableField("onboarding_num")
	private Integer onboardingNum;

	/**
	* 创建人
	*/
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 更新人
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	* 状态(1:停止招聘)
	*/
	@TableField("status")
	private Integer status;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
