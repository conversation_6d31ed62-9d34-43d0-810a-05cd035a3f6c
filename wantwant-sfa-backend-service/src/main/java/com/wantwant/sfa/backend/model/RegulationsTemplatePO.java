package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("sfa_regulations_template")
public class RegulationsTemplatePO extends Model<RegulationsTemplatePO> {

    private static final long serialVersionUID = 5702685112736551067L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("title")
    private String title;

    @TableField("message")
    private String message;

    @TableField("version")
    private String version;

    @TableField("link")
    private Integer link;

    @TableField("content")
    private String content;

    @TableField("webContent")
    private String webContent;

    @TableField("status")
    private Integer status;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_employee_id")
    private String createEmployeeId;

    @TableField("create_employee_name")
    private String createEmployeeName;


}
