package com.wantwant.sfa.backend.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * @Description: 修复小标市场与人员关系
 * @Auther: zhengxu
 * @Date: 2021/11/03/上午9:07
 */
@Data
@ApiModel(value = "修复小标市场与人员关系用Model")
public class RepairSmallMarketModel {
    @Excel(name = "岗位编号")
    private String positionId;
    @Excel(name = "大区")
    private String areaName;
    @Excel(name = "分公司")
    private String companyName;
    @Excel(name = "营业所")
    private String branchName;
    @Excel(name ="专员工号")
    private String employeeId;
    @Excel(name ="专员姓名")
    private String employeeName;
    @Excel(name ="小标编号")
    private String smallMarketCode;
    @Excel(name ="操作人")
    private String person;
    @Excel(name ="入职时间")
    private String onboardTime;
    @TableField("属性")
    private String postType;
}
