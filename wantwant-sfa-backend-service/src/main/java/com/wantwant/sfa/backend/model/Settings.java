package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_settings")
@ApiModel(value = "SfaSettings对象", description = "")
public class Settings extends Model<Settings> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "配置项")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "值")
    @TableField("value")
    private String value;

    @ApiModelProperty(value = "是否失效")
    @TableField("failure")
    private Boolean failure;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("update_person")
    private String updatePerson;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
