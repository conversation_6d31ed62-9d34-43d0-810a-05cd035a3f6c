package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_bonus_evaluation")
@ApiModel(value = "奖金评定更新表对象", description = "")
public class SfaBonusEvaluationModel {

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @ApiModelProperty(value = "考核月份")
  @TableField("assessment_time")
  private String assessmentTime;

  @ApiModelProperty(value = "组织id")
  @TableField("organiztaion_id")
  private String organiztaionId;

  @ApiModelProperty(value = "员工信息id")
  @TableField("employee_info_id")
  private Integer employeeInfoId;

  @ApiModelProperty(value = "业绩达成奖")
  @TableField("actual_achievement_award")
  private Double actualAchievementAward;

  @ApiModelProperty(value = "客户成交奖")
  @TableField("actual_customer_transaction_award")
  private Double actualCustomerTransactionAward;

  @ApiModelProperty(value = "目标达成奖")
  @TableField("actual_goal_achievement_award")
  private Double actualGoalAchievementAward;

  @ApiModelProperty(value = "人效奖")
  @TableField("actual_human_effect_award")
  private Double actualHumanEffectAward;

  @ApiModelProperty(value = "是否提交（0.否;1.是）")
  @TableField("is_submit")
  private Integer isSubmit;

  @ApiModelProperty(value = "组织类型(0.分公司;1.合伙人)")
  @TableField("organiztaion_type")
  private Integer organiztaionType;

  @ApiModelProperty(value = "创建人id")
  @TableField("create_id")
  private String createId;

  @ApiModelProperty(value = "创建时间")
  @TableField("create_time")
  private LocalDateTime createTime;

  @ApiModelProperty(value = "更新人id")
  @TableField("update_id")
  private String updateId;

  @ApiModelProperty(value = "更新时间")
  @TableField("update_time")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "0-默认 1-删除")
  @TableField("delete_flag")
  private Integer deleteFlag;
}
