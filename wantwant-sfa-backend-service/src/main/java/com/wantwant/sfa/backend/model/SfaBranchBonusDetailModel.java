package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_branch_bonus_detail")
@ApiModel(value = "区域总监奖金评定表对象", description = "")
public class SfaBranchBonusDetailModel {

  @TableField("organization_id")
  @ApiModelProperty(name = "组织id", allowableValues = "组织id")
  private String organizationId;

  @TableField("comapany_region_id")
  @ApiModelProperty(name = "大区组织ID", allowableValues = "大区组织ID")
  private String comapanyRegionId;

  @TableField("comapany_region_name")
  @ApiModelProperty(name = "大区组织名称", allowableValues = "大区组织名称")
  private String comapanyRegionName;

  @TableField("comapany_branch_id")
  @ApiModelProperty(name = "分公司组织ID", allowableValues = "分公司组织ID")
  private String comapanyBranchId;

  @TableField("comapany_branch_name")
  @ApiModelProperty(name = "分公司组织名称", allowableValues = "分公司组织名称")
  private String comapanyBranchName;

  @TableField("company_assessment_classification")
  @ApiModelProperty(name = "考核分类", allowableValues = "考核分类")
  private String companyAssessmentClassification;

  @TableField("manager_name")
  @ApiModelProperty(name = "区域总监姓名", allowableValues = "区域总监姓名")
  private String managerName;

  @TableField("employee_id")
  @ApiModelProperty(name = "区域经理员工工号", allowableValues = "区域经理员工工号")
  private String employeeId;

  @TableField("manager_employee_info_id")
  @ApiModelProperty(name = "employee_info表id", allowableValues = "employee_info表id")
  private Integer managerEmployeeEnfoId;

  @TableField("manager_onboard_date")
  @ApiModelProperty(name = "入职日期", allowableValues = "入职日期")
  private LocalDateTime managerOnboardDate;

  @TableField("manager_off_date")
  @ApiModelProperty(name = "离职日期", allowableValues = "离职日期")
  private LocalDateTime managerOffDate;

  @TableField("manager_attendance_days")
  @ApiModelProperty(name = "应出勤天数", allowableValues = "应出勤天数")
  private Integer managerAttendanceDays;

  @TableField("manager_actual_attendance_days")
  @ApiModelProperty(name = "实际出勤天数", allowableValues = "实际出勤天数")
  private Integer managerActualAttendanceDays;

  @TableField("manager_performance")
  @ApiModelProperty(name = "盘价业绩", allowableValues = "盘价业绩")
  private Double managerPerformance;

  @TableField("manager_goal")
  @ApiModelProperty(name = "业绩目标", allowableValues = "业绩目标")
  private Double managerGoal;

  @TableField("manager_goal_achievement_rate")
  @ApiModelProperty(name = "目标达成率", allowableValues = "目标达成率")
  private Double managerGoalAchievementRate;

  @TableField("manager_human_effect")
  @ApiModelProperty(name = "人效", allowableValues = "人效")
  private Integer managerHumanEffect;

  @TableField("manager_standard_salary")
  @ApiModelProperty(name = "设定标准底薪", allowableValues = "设定标准底薪")
  private Double managerStandardSalary;

  @TableField("manager_fixed_reward")
  @ApiModelProperty(name = "设定固定奖励", allowableValues = "设定固定奖励")
  private Double managerFixedReward;

  @TableField("goal_achievement_award")
  @ApiModelProperty(name = "设定目标达成奖", allowableValues = "设定目标达成奖")
  private Double goalAchievementAward;

  @TableField("human_effect_award")
  @ApiModelProperty(name = "设定人效奖", allowableValues = "设定人效奖")
  private Double humanEffectAward;

  @TableField("salary_cap")
  @ApiModelProperty(name = "设定薪资上限", allowableValues = "设定薪资上限")
  private Double salaryCap;

  @TableField("actual_attendance_salary")
  @ApiModelProperty(name = "实际出勤底薪", allowableValues = "实际出勤底薪")
  private Double actualAttendanceSalary;

  @TableField("actual_fixed_reward")
  @ApiModelProperty(name = "实际固定奖励", allowableValues = "实际固定奖励")
  private Double actualFixedReward;

  @TableField("actual_goal_achievement_award")
  @ApiModelProperty(name = "实际目标达成奖", allowableValues = "实际目标达成奖")
  private Double actualGoalAchievementAward;

  @TableField("actual_human_effect_award")
  @ApiModelProperty(name = "实际人效奖", allowableValues = "实际人效奖")
  private Double actualHumanEffectAward;

  @TableField("total_award")
  @ApiModelProperty(name = "奖金合计", allowableValues = "奖金合计")
  private Double totalAward;

  @TableField("total_salary")
  @ApiModelProperty(name = "薪资合计", allowableValues = "奖金合计")
  private Double totalSalary;

  @TableField("etl_date")
  @ApiModelProperty(name = "数据更新时间", allowableValues = "数据更新时间")
  private LocalDateTime etlDate;
}
