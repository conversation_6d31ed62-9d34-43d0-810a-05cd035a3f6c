package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_branch_performance_evaluation")
@ApiModel(value = "合伙人绩效评定表", description = "")
public class SfaBranchPerformanceEvaluationModel {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private String organizationId;

    @ApiModelProperty(value = "大区组织ID")
    @TableField("area_id")
    private String areaId;

    @ApiModelProperty(value = "大区组织名称")
    @TableField("area_name")
    private String areaName;

    @ApiModelProperty(value = "分公司组织ID")
    @TableField("compnay_id")
    private String compnayId;

    @ApiModelProperty(value = "分公司组织名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "memberkey")
    @TableField("memberkey")
    private String memberkey;

    @ApiModelProperty(value = "employee_info表id")
    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "入职日期")
    @TableField("onboard_date")
    private LocalDateTime onboardDate;

    @ApiModelProperty(value = "离职日期")
    @TableField("off_date")
    private LocalDateTime offDate;

    @ApiModelProperty(value = "绩效包")
    @TableField("performance_package")
    private BigDecimal performancePackage;

    @ApiModelProperty(value = "盘价业绩")
    @TableField("all_items_price_performance")
    private BigDecimal allItemsPricePerformance;

    @ApiModelProperty(value = "发放比例")
    @TableField("all_items_issue_proportion")
    private BigDecimal allItemsIssueProportion;

    @ApiModelProperty(value = "发放金额")
    @TableField("all_items_issue_amount")
    private BigDecimal allItemsIssueAmount;

    @ApiModelProperty(value = "评分")
    @TableField("score")
    private Integer score;

    @ApiModelProperty(value = "发放金额")
    @TableField("issue_amount")
    private BigDecimal issueAmount;

    @ApiModelProperty(value = "绩效奖金合计")
    @TableField("performance_bonus_combined")
    private BigDecimal performanceBonusCombined;

    @ApiModelProperty(value = "考核月份")
    @TableField("assessment_month")
    private String assessmentMonth;

    @ApiModelProperty(value = "数据更新时间")
    @TableField("etl_date")
    private String etlDate;

}
