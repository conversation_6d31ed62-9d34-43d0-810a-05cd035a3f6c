package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_contract_enclosure_url")
@ApiModel(value = "合同附件url对象", description = "")
public class SfaContractEnclosureUrlModel {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "供应商合同ID")
    @TableField("supplier_contract_id")
    private Integer supplierContractId;

    @ApiModelProperty(value = "文件地址")
    @TableField("url")
    private String url;

    @ApiModelProperty(value = "文件名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "供应商代码")
    @TableField("supplier_code")
    private String supplierCode;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;

}
