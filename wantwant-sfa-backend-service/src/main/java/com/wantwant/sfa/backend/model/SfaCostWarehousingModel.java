package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_cost_warehousing")
@ApiModel(value = "sfaCostWarehousing对象", description = "")
public class SfaCostWarehousingModel extends Model<SfaCostWarehousingModel> {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("date")
    private String date;

    @TableField("warehousing_id")
    private Integer warehousingId;

    @TableField("warehousing_name")
    private String warehousingName;

    @TableField("travel_expenses")
    private BigDecimal travelExpenses;

    @TableField("publishing")
    private BigDecimal publishing;

    @TableField("procurement")
    private BigDecimal procurement;

    @TableField("other")
    private BigDecimal other;

    @TableField("excel_id")
    private Integer excelId;
}
