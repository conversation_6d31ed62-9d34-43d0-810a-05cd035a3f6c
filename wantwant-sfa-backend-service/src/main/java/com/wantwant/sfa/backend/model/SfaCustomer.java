package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer")
@ApiModel(value = "SfaCustomer对象", description = "客户表")
public class SfaCustomer extends Model<SfaCustomer> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "岗位ID")
    @TableField("position_id")
    private String positionId;

    @TableField("member_id")
    private String memberId;

    @TableField("customer_name")
    private String customerName;

    @ApiModelProperty(value = "0:未知/n1：男性/n2：女性")
    @TableField("gender")
    private Integer gender;

    @ApiModelProperty(value = "1：学代\n2：终端\n3：旺粉\n4：特通")
    @TableField("customer_type")
    private Integer customerType;

    @ApiModelProperty(value = "客户子类型与custoemr_type一起用，特通4--1  特通--餐饮 4--2 特通--KTV 4--3 特通--其他")
    @TableField("customer_subtype")
    private Integer customerSubtype;

    @TableField("customer_label")
    private String customerLabel;


    @ApiModelProperty(value = "0：潜在/n 1：正式")
    @TableField("is_potential")
    private Integer isPotential;

    @ApiModelProperty(value = "0是不可重复提交,1可重复提交")
    @TableField("is_repetition")
    private Integer isRepetition;

    @ApiModelProperty(value = "审核状态 0,未审核,1,审核通过,2,驳回")
    @TableField("is_verified")
    private Integer isVerified;

    @TableField("mobile_number")
    private String mobileNumber;

    @ApiModelProperty(value = "身份证号")
    @TableField("id_card")
    private String idCard;

    @ApiModelProperty(value = "身份证正面图片链接")
    @TableField("id_card_front_image_url")
    private String idCardFrontImageUrl;

    @ApiModelProperty(value = "身份证反面图片链接")
    @TableField("id_card_back_image_url")
    private String idCardBackImageUrl;

    @ApiModelProperty(value = "身份证正面图片名称")
    @TableField("id_card_front_image_name")
    private String idCardFrontImageName;

    @ApiModelProperty(value = "身份证反面图片名称")
    @TableField("id_card_back_image_name")
    private String idCardBackImageName;

    @TableField("store_name")
    private String storeName;

    @TableField("store_image_name")
    private String storeImageName;

    @TableField("store_image_url")
    private String storeImageUrl;

    @ApiModelProperty(value = "营业执照号")
    @TableField("store_license")
    private String storeLicense;

    @ApiModelProperty(value = "营业执照图片链接")
    @TableField("store_lisence_image_url")
    private String storeLisenceImageUrl;

    @ApiModelProperty(value = "营业执照图片名称")
    @TableField("store_lisence_image_name")
    private String storeLisenceImageName;

    @TableField("school_name")
    private String schoolName;

    @TableField("school_image_name")
    private String schoolImageName;

    @TableField("school_image_url")
    private String schoolImageUrl;

    @ApiModelProperty(value = "学生证图片链接")
    @TableField("student_card_url")
    private String studentCardUrl;

    @ApiModelProperty(value = "学生证图片名称")
    @TableField("student_card_name")
    private String studentCardName;

    @TableField("province")
    private String province;

    @TableField("city")
    private String city;

    @TableField("district")
    private String district;

    @TableField("street")
    private String street;

    @TableField("longitude")
    private Double longitude;

    @TableField("latitude")
    private Double latitude;

    @ApiModelProperty(value = "最后拜访时间")
    @TableField("visit_last_time")
    private LocalDateTime visitLastTime;

    @TableField("visit_count")
    private Integer visitCount;

    @ApiModelProperty(value = "需要重新提交特陈的个数")
    @TableField("special_display_count")
    private Integer specialDisplayCount;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @TableField("updated_person")
    private String updatedPerson;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("no_visit_count")
    private Integer noVisitCount;

    @TableField("cancel_visit_count")
    private Integer cancelVisitCount;

    @ApiModelProperty(value = "性别：0-男 1-女")
    @TableField("sex")
    private Integer sex;

    @ApiModelProperty(value = "学生证号")
    @TableField("student_card_number")
    private String studentCardNumber;

    @ApiModelProperty(value = "学生证有效期")
    @TableField("student_card_validity")
    private String studentCardValidity;

    @TableField("memberKey")
    private Long memberKey;

    @ApiModelProperty(value = "潜在memberKey")
    @TableField("potential_member_key")
    private String potentialMemberKey;

    @ApiModelProperty(value = "0: 未删除, 1: 已删除")
    @TableField("deleteFlag")
    private Integer deleteFlag;

    @ApiModelProperty(value = "客户累计业绩")
    @TableField("customerAmounts")
    private Integer customerAmounts;

    @ApiModelProperty(value = "客户累计订单")
    @TableField("customerOrders")
    private Integer customerOrders;

    @ApiModelProperty(value = "冻结状态 0:未冻结 1:冻结")
    @TableField("is_frozen")
    private Integer isFrozen;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
