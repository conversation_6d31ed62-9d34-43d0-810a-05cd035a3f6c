package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_department_performance_evaluation")
@ApiModel(value = "区域经理绩效评定表", description = "")
public class SfaDepartmentPerformanceEvaluationModel {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private String organizationId;

    @ApiModelProperty(value = "大区组织ID")
    @TableField("area_id")
    private String areaId;

    @ApiModelProperty(value = "大区组织名称")
    @TableField("area_name")
    private String areaName;

    @ApiModelProperty(value = "分公司组织ID")
    @TableField("compnay_id")
    private String compnayId;

    @ApiModelProperty(value = "分公司组织名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "区域经理组织ID")
    @TableField("department_id")
    private String departmentId;

    @ApiModelProperty(value = "区域经理组织名称")
    @TableField("department_name")
    private String departmentName;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "memberkey")
    @TableField("memberkey")
    private String memberkey;

    @ApiModelProperty(value = "手机号")
    @TableField("mobile")
    private String mobile;

    @ApiModelProperty(value = "身份证")
    @TableField("identity_card")
    private String identityCard;

    @ApiModelProperty(value = "employee_info表id")
    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "入职日期")
    @TableField("onboard_date")
    private LocalDateTime onboardDate;

    @ApiModelProperty(value = "离职日期")
    @TableField("off_date")
    private LocalDateTime offDate;

    @ApiModelProperty(value = "绩效包")
    @TableField("performance_package")
    private BigDecimal performancePackage;

    @ApiModelProperty(value = "盘价业绩")
    @TableField("all_items_price_performance")
    private BigDecimal allItemsPricePerformance;

    @ApiModelProperty(value = "发放比例")
    @TableField("all_items_issue_proportion")
    private BigDecimal allItemsIssueProportion;

    @ApiModelProperty(value = "发放金额")
    @TableField("all_items_issue_amount")
    private BigDecimal allItemsIssueAmount;

    /**
     * 业绩目标达成
     */

    @ApiModelProperty(value = "盘价业绩(业绩目标达成)")
    @TableField("target_price_performance")
    private BigDecimal targetPricePerformance;

    @ApiModelProperty(value = "人口目标(业绩目标达成)")
    @TableField("target_population_target")
    private BigDecimal targetPopulationTarget;

    @ApiModelProperty(value = "达成率(业绩目标达成)")
    @TableField("target_reach_rate")
    private BigDecimal targetReachRate;

    @ApiModelProperty(value = "发放系数(业绩目标达成)")
    @TableField("target_issue_coefficient")
    private BigDecimal targetIssueCoefficient;

    @ApiModelProperty(value = "发放金额(业绩目标达成)")
    @TableField("target_issue_amount")
    private BigDecimal targetIssueAmount;

    /**
     * 人均业绩
     */

    @ApiModelProperty(value = "人均业绩(人均业绩)")
    @TableField("per_capita_results")
    private BigDecimal perCapitaResults;

    @ApiModelProperty(value = "发放比例(人均业绩)")
    @TableField("per_capita_distribution_proportion")
    private BigDecimal perCapitaDistributionProportion;

    @ApiModelProperty(value = "发放金额(人均业绩)")
    @TableField("per_capita_distribution_amount")
    private BigDecimal perCapitaDistributionAmount;

    /**
     *  在岗率目标达成
     */
    @ApiModelProperty(value = "达成率(在岗率目标达成)")
    @TableField("on_jobreach_rate")
    private BigDecimal onJobreachRate;

    @ApiModelProperty(value = "发放系数(在岗率目标达成)")
    @TableField("on_jobissue_coefficient")
    private BigDecimal onJobissueCoefficient;

    @ApiModelProperty(value = "发放金额(在岗率目标达成)")
    @TableField("on_job_issue_amount")
    private BigDecimal onJobIssueAmount;

    /**
     *  B类品销售预估达成
     */

    @ApiModelProperty(value = "实际业绩(B类品销售预估达成)")
    @TableField("bclass_actual_performance")
    private BigDecimal bclassActualPerformance;

    @ApiModelProperty(value = "销售预估(B类品销售预估达成)")
    @TableField("bclass_sales_forecast")
    private BigDecimal bclassSalesForecast;

    @ApiModelProperty(value = "达成率(B类品销售预估达成)")
    @TableField("bclass_reach_rate")
    private BigDecimal bclassReachRate;

    @ApiModelProperty(value = "发放比例(B类品销售预估达成)")
    @TableField("bclass_distribution_proportion")
    private BigDecimal bclassDistributionProportion;

    @ApiModelProperty(value = "发放金额(B类品销售预估达成)")
    @TableField("bclass_distribution_amount")
    private BigDecimal bclassDistributionAmount;

    @ApiModelProperty(value = "评分(主管评分)")
    @TableField("score")
    private Integer score;

    @ApiModelProperty(value = "发放金额(主管评分)")
    @TableField("issue_amount")
    private BigDecimal issueAmount;

    @ApiModelProperty(value = "绩效奖金合计")
    @TableField("performance_bonus_combined")
    private BigDecimal performanceBonusCombined;

    @ApiModelProperty(value = "考核月份")
    @TableField("assessment_month")
    private String assessmentMonth;

    @ApiModelProperty(value = "薪资方案")
    @TableField("salary_plan")
    private String salaryPlan;

    @ApiModelProperty(value = "数据更新时间")
    @TableField("etl_date")
    private String etlDate;


    @ApiModelProperty(value = "直接利润率")
    @TableField("direct_profit_rate")
    private BigDecimal directProfitRate;

    @ApiModelProperty(value = "系数(直接利润率)")
    @TableField("direct_profit_rate_coefficient")
    private BigDecimal directProfitRateCoefficient;

    @ApiModelProperty(value = "奖金(直接利润率)")
    @TableField("direct_profit_rate_bonus")
    private BigDecimal directProfitRateBonus;

    @ApiModelProperty(value = "经销商开发数(过程指标)")
    @TableField("dealer_development_course")
    private BigDecimal dealerDevelopmentCourse;

    @ApiModelProperty(value = "优质陈列执行数(过程指标)")
    @TableField("quality_display_execution_course")
    private BigDecimal qualityDisplayExecutionCourse;

    @ApiModelProperty(value = "新增终端焦点数(过程指标)")
    @TableField("terminal_focus_number_course")
    private BigDecimal terminalFocusNumberCourse;

    @ApiModelProperty(value = "奖金(过程指标)")
    @TableField("fund_course")
    private BigDecimal fundCourse;

    @ApiModelProperty(value = "业务组ID")
    @TableField("business_group")
    private Integer businessGroup;

}
