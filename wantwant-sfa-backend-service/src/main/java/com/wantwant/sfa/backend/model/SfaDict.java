package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
* <AUTHOR>
* @description: //模块目的、功能描述
* @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
* @Time 2021-1-25 18:27:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_dict")
@ApiModel(value = "SfaDict对象", description = "")
public class SfaDict extends Model<SfaDict> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型")
    @TableField("type")
    private String type;
    
    @ApiModelProperty(value = "配置项")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "值")
    @TableField("value")
    private String value;

    @ApiModelProperty(value = "是否失效")
    @TableField("failure")
    private Boolean failure;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("update_person")
    private String updatePerson;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
