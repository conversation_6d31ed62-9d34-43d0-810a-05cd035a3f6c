package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/10/23/下午12:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_employee_rehire_black_list")
@ApiModel(value = "sfa_employee_rehire_black_list对象", description = "离职黑名单")
public class SfaEmployeeBlackListModel {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(name="sfa_employee_info的主键")
    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @TableField("reason")
    @ApiModelProperty(name="返聘理由")
    private String reason;

    @ApiModelProperty(name="创建日期")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(name="更新日期")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("create_person")
    @ApiModelProperty(name="创建人id")
    private String createPerson;

    @TableField("update_person")
    @ApiModelProperty(name="创建人姓名")
    private String updatePerson;

    @ApiModelProperty(value = "删除标识 0-有效 1-无效")
    @TableField("delete_flag")
    private Integer deleteFlag;


}
