package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/10/23/下午12:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_employee_rehire")
@ApiModel(value = "sfa_employee_rehire对象", description = "离职返聘")
public class SfaEmployeeRehireModel {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(name="sfa_employee_info的主键")
    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @TableField("employee_name")
    @ApiModelProperty(name="员工姓名")
    private String employeeName;

    @TableField("application_id")
    @ApiModelProperty(name="申请ID")
    private Integer applicationId;

    @TableField("`type`")
    private Integer type;

    @TableField("gender")
    @ApiModelProperty(name="员工性别",allowableValues="0.未知 1.男 2.女")
    private Integer gender;

    @TableField("mobile")
    @ApiModelProperty(name="手机号")
    private String mobile;

    @TableField("employee_status")
    @ApiModelProperty(name="员工状态")
    private Integer employeeStatus;

    @TableField("position_id")
    @ApiModelProperty(name="岗位ID",allowableValues="岗位ID")
    private String positionId;

    @ApiModelProperty(name="主岗事业部",allowableValues="1.旺旺在职 2.第三方兼职")
    @TableField("affiliation")
    private Integer affiliation;


    @ApiModelProperty(name="岗位类型",allowableValues="1.全职 2.兼职")
    @TableField("post_type")
    private Integer postType;


    @ApiModelProperty(name="入职日期")
    @TableField("onboard_time")
    private Date onboardTime;



    /**
     * 入职公司
     */
    @TableField("joining_company")
    private String joiningCompany;


    @ApiModelProperty(name="大区CODE")
    @TableField("area_code")
    private String areaCode;
    @ApiModelProperty(name="大区名称")
    @TableField("area_name")
    private String areaName;
    @TableField("company_code")
    @ApiModelProperty(name="分公司CODE")
    private String companyCode;
    @TableField("company_name")
    @ApiModelProperty(name="分公司名称")
    private String companyName;
    @TableField("branch_code")
    @ApiModelProperty(name="营业所CODE")
    private String branchCode;
    @TableField("branch_name")
    @ApiModelProperty(name="营业所Name")
    private String branchName;
    @TableField("department_code")
    @ApiModelProperty(name="营业所code")
    private String departmentCode;
    @TableField("department_name")
    @ApiModelProperty(name="营业所名字")
    private String departmentName;

    @TableField("channel")
    @ApiModelProperty(name="渠道",allowableValues="1.sfa 2.旺江山 3.造旺")
    private Integer channel;


    @TableField("province")
    @ApiModelProperty(name="省")
    private String province;
    @TableField("city")
    @ApiModelProperty(name="市")
    private String city;
    @TableField("district")
    @ApiModelProperty(name="区")
    private String district;



    @ApiModelProperty(name="试岗日期")
    @TableField("probation_time")
    private String probationTime;


    @TableField(value="contract_company",strategy = FieldStrategy.IGNORED)
    private String contractCompany;

    @ApiModelProperty(name="离职日期")
    @TableField("off_time")
    private Date offTime;

    @TableField("reason")
    @ApiModelProperty(name="返聘理由")
    private String reason;

    @ApiModelProperty(name="创建日期")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(name="更新日期")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("create_person")
    @ApiModelProperty(name="创建人id")
    private String createPerson;

    @TableField("update_person")
    @ApiModelProperty(name="创建人姓名")
    private String updatePerson;

    @ApiModelProperty(value = "删除标识 0-有效 1-无效")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @TableField("old_position")
    private Integer oldPosition;
    @TableField("transaction_position")
    private Integer transactionPosition;

    @TableField("member_key")
    private Long memberKey;

    @TableField("transaction_id")
    private Long transactionId;

}
