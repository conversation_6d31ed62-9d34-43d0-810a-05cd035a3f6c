package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_entrepreneurship_subsidies_apply")
@ApiModel(value = "企业合伙人创业补贴申请表", description = "")
public class SfaEntrepreneurshipSubsidiesApplyModel {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "申请编号")
    @TableField("apply_id")
    private String applyId;

    @ApiModelProperty(value = "创业补贴发放id")
    @TableField("subsidies_issue_id")
    private Integer subsidiesIssueId;

    @ApiModelProperty(value = "申请时间")
    @TableField("apply_time")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "申请补贴年月")
    @TableField("apply_subsidies_time")
    private String applySubsidiesTime;

    @ApiModelProperty(value = "合伙人memberkey")
    @TableField("partner_memberkey")
    private String partnerMemberkey;

    @ApiModelProperty(value = "发放方式(1.现金;2.旺金币)")
    @TableField("issue_way")
    private Integer issueWay;

    @ApiModelProperty(value = "发票图片")
    @TableField("invoice_url")
    private String invoiceUrl;

    @ApiModelProperty(value = "盘价金额")
    @TableField("enterprise_amount")
    private BigDecimal enterpriseAmount;

    @ApiModelProperty(value = "应发金额(补贴金额)")
    @TableField("subsidies_amount")
    private BigDecimal subsidiesAmount;

    @ApiModelProperty(value = "状态结果(0.待审核;1.待发放;2.已发放;3.已驳回)")
    private Integer status;

    @ApiModelProperty(value = "是否批量审核(1.否;2.是)")
    @TableField("is_batch_issue")
    private Integer isBatchIssue;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_name")
    private String updateName;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;

}
