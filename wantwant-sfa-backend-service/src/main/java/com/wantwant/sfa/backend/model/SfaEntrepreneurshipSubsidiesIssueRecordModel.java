package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_entrepreneurship_subsidies_issue_record")
@ApiModel(value = "企业合伙人创业补贴发放表", description = "")
public class SfaEntrepreneurshipSubsidiesIssueRecordModel {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "创业补贴申请id")
    @TableField("subsidies_apply_id")
    private Integer subsidiesApplyId;

    @ApiModelProperty(value = "上一级id")
    @TableField("prove_subsidies_id")
    private Integer proveSubsidiesId;

    @ApiModelProperty(value = "下一级id")
    @TableField("next_subsidies_id")
    private Integer nextSubsidiesId;

    @ApiModelProperty(value = "备注")
    @TableField("note")
    private String note;

    @ApiModelProperty(value = "处理类型(0.审核;1.发放)")
    @TableField("process_type")
    private Integer processType;

    @ApiModelProperty(value = "处理结果(0.待审批;1.通过;2.不通过;)")
    @TableField("process_result")
    private Integer processResult;

    @ApiModelProperty(value = "处理人")
    @TableField("process_person")
    private String processPerson;

    @ApiModelProperty(value = "处理时间")
    @TableField("process_time")
    private LocalDateTime processTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;

}
