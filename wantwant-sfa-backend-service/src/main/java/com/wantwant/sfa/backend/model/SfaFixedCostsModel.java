package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_fixed_costs")
@ApiModel(value = "固定成本表对象", description = "")
public class SfaFixedCostsModel {

    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "月份")
    @TableField("month")
    private String month;

    @ApiModelProperty(value = "仓储导入id")
    @TableField("warehousing_id")
    private Integer warehousingId;

    /**
     * 仓库(1:造旺长沙,2:造旺济南,3:造旺隆昌,4:造旺沈阳)
     */
    @TableField("warehouse")
    private Integer warehouse;

    @ApiModelProperty(value = "供应商代码")
    @TableField("supplier_code")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    @TableField("supplier_name")
    private String supplierName;

    @ApiModelProperty(value = "费用大项(1盘价;2租赁;3集团分摊;4临时工;5水电;6耗材;7费用率)")
    @TableField("cost_categories")
    private Integer costCategories;

    @ApiModelProperty(value = "费用小项(1仓租费用;2栈板租赁;3网络费用;4叉车租赁;5正式工费用;6部门分摊;7临时工费用;8装卸工费用;9水电费;10纸箱耗材;11固定成本合计)")
    @TableField("cost_minor_item")
    private Integer costMinorItem;

    /**
     * 预提费用
     */
    @ApiModelProperty(value = "预提费用")
    @TableField("accrued_expenses_changsha")
    private BigDecimal accruedExpensesChangsha;

    /**
     * 实际费用
     */
    @ApiModelProperty(value = "实际费用")
    @TableField("actual_cost_changsha")
    private BigDecimal actualCostChangsha;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_people_name")
    private String createPeopleName;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_people")
    private String updatePeople;


    @ApiModelProperty(value = "修改人姓名")
    @TableField("update_people_name")
    private String updatePeopleName;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否覆盖(0:否,1:是)
     */
    @TableField("is_cover")
    private Integer isCover;

}
