package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SfaInsertOrUpdateAddressRequest {

    private Long memberKey;

    private String addressCode;

    private String tag;

    @ApiModelProperty(value = "省")
    private String province;

    private Integer isDefault;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String district;

    @ApiModelProperty(value = "乡镇街道")
    private String street;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号")
    private String receiverMobileNumber;

    @ApiModelProperty(value = "性别 1男 0女", required = true, example = "", position = 0)
    private Integer receiverGender;

    @ApiModelProperty(value = "地址来源  1: c端  2: 造旺")
    private int addressSource;

}