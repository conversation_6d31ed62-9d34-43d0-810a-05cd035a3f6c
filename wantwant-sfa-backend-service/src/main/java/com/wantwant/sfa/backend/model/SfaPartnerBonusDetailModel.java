package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_partner_bonus_detail")
@ApiModel(value = "合伙人奖金评定表对象", description = "")
public class SfaPartnerBonusDetailModel {

  @TableField("organization_id")
  @ApiModelProperty(name = "组织id", allowableValues = "组织id")
  private Integer organizationId;

  @TableField("comapany_region_id")
  @ApiModelProperty(name = "大区组织ID", allowableValues = "大区组织ID")
  private String comapanyRegionId;

  @TableField("comapany_region_name")
  @ApiModelProperty(name = "大区组织名称", allowableValues = "大区组织名称")
  private String comapanyRegionName;

  @TableField("comapany_branch_id")
  @ApiModelProperty(name = "分公司组织ID", allowableValues = "分公司组织ID")
  private String comapanyBranchId;

  @TableField("comapany_branch_name")
  @ApiModelProperty(name = "分公司组织名称", allowableValues = "分公司组织名称")
  private String comapanyBranchName;

  @TableField("company_assessment_classification")
  @ApiModelProperty(name = "考核分类", allowableValues = "考核分类")
  private String companyAssessmentClassification;

  @TableField("partner_name")
  @ApiModelProperty(name = "合伙人姓名", allowableValues = "合伙人姓名")
  private String partnerName;

  @TableField("partner_memberkey")
  @ApiModelProperty(name = "合伙人memberkey", allowableValues = "合伙人memberkey")
  private String partnerMemberkey;

  @TableField("partner_employee_info_id")
  @ApiModelProperty(name = "employee_info表id", allowableValues = "employee_info表id")
  private Integer partnerEmployeeInfoId;

  @TableField("partner_salary_scheme")
  @ApiModelProperty(name = "合伙人薪资方案", allowableValues = "合伙人薪资方案")
  private String partnerSalaryScheme;

  @TableField("partner_onboard_date")
  @ApiModelProperty(name = "合伙人入职日期", allowableValues = "合伙人入职日期")
  private LocalDateTime partnerOnboardDate;

  @TableField("partner_off_date")
  @ApiModelProperty(name = "合伙人离职日期", allowableValues = "合伙人离职日期")
  private LocalDateTime partnerOffDate;

  @TableField("partner_attendance_days")
  @ApiModelProperty(name = "应出勤天数", allowableValues = "应出勤天数")
  private String partnerAttendanceDays;

  @TableField("partner_actual_attendance_days")
  @ApiModelProperty(name = "实际出勤天数", allowableValues = "实际出勤天数")
  private int partnerActualAttendanceDays;

  @TableField("partner_performance")
  @ApiModelProperty(name = "盘价业绩", allowableValues = "盘价业绩")
  private Double partnerPerformance;

  @TableField("customer_transactions_num")
  @ApiModelProperty(name = "建档客户成交数", allowableValues = "建档客户成交数")
  private Integer customerTransactionsNum;

  @TableField("partner_standard_salary")
  @ApiModelProperty(name = "标准底薪", allowableValues = "标准底薪")
  private Double partnerStandardSalary;

  @TableField("aim_achievement_award")
  @ApiModelProperty(name = "设定业绩达成奖", allowableValues = "设定业绩达成奖")
  private Double aimAchievementAward;

  @TableField("aim_customer_transaction_award")
  @ApiModelProperty(name = "设定客户成交奖", allowableValues = "设定客户成交奖")
  private Double aimCustomerTransactionAward;

  @TableField("bonus_cap")
  @ApiModelProperty(name = "奖金上限", allowableValues = "奖金上限")
  private Double bonusCap;

  @TableField("attendance_salary")
  @ApiModelProperty(name = "出勤底薪", allowableValues = "出勤底薪")
  private Double attendanceSalary;

  @TableField("product_commission_amount")
  @ApiModelProperty(name = "品相利润", allowableValues = "品相利润")
  private Double productCommissionAmount;

  @TableField("actual_achievement_award")
  @ApiModelProperty(name = "实际业绩达成奖", allowableValues = "实际业绩达成奖")
  private Double actualAchievementAward;

  @TableField("actual_customer_transaction_award")
  @ApiModelProperty(name = "实际客户成交奖", allowableValues = "实际客户成交奖")
  private Double actualCustomerTransactionAward;

  @TableField("total_award")
  @ApiModelProperty(name = "奖金合计", allowableValues = "奖金合计")
  private Double totalAward;

  @TableField("total_salary")
  @ApiModelProperty(name = "薪资合计", allowableValues = "薪资合计")
  private Double totalSalary;

  @TableField("assessment_time")
  @ApiModelProperty(name = "考核月份", allowableValues = "考核月份")
  private String assessmentTime;
}
