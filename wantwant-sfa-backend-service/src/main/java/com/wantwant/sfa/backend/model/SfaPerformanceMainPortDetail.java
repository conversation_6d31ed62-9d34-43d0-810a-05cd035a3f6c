package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_performance_main_port_detail")
@ApiModel(value = "绩效评定主推口明细表", description = "")
public class SfaPerformanceMainPortDetail {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "岗位类型")
    @TableField("position_type")
    private String positionId;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private String organizationId;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "memberkey")
    @TableField("memberkey")
    private String memberkey;

    @ApiModelProperty(value = "employee_info表id")
    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "考核月份")
    @TableField("assessment_month")
    private String assessmentMonth;

    @ApiModelProperty(value = "主推品名称")
    @TableField("main_product_name")
    private String mainProductName;

    @ApiModelProperty(value = "实际业绩")
    @TableField("actual_results")
    private BigDecimal actualResults;

    @ApiModelProperty(value = "目标业绩")
    @TableField("target_results")
    private BigDecimal targetResults;

    @ApiModelProperty(value = "达成率")
    @TableField("reach_rate")
    private BigDecimal reachRate;

    @ApiModelProperty(value = "发放系数")
    @TableField("issue_coefficient")
    private BigDecimal issueCoefficient;

    @ApiModelProperty(value = "发放金额")
    @TableField("issue_amount")
    private BigDecimal issueAmount;

    @ApiModelProperty(value = "数据更新时间")
    @TableField("etl_date")
    private String etlDate;

}
