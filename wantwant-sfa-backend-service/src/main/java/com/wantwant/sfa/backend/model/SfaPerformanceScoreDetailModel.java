package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_performance_score_detail")
@ApiModel(value = "绩效评分明细表", description = "")
public class SfaPerformanceScoreDetailModel {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "岗位类型")
    @TableField("position_type")
    private Integer positionType;

    @ApiModelProperty(value = "考核月份")
    @TableField("assessment_month")
    private String assessmentMonth;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private String organizationId;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "employee_info表id")
    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "是否汰换(0.否;1.是)")
    @TableField("is_replacing")
    private Integer isReplacint;

    @ApiModelProperty(value = "评分")
    @TableField("score")
    private Integer score;

    @ApiModelProperty(value = "备注")
    @TableField(value = "note",fill = FieldFill.UPDATE)
    private String note;

    @ApiModelProperty(value = "状态(0.失效;1.生效)")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除")
    @TableField("is_delete")
    private Integer isDelete;

    @ApiModelProperty(value = "业务组ID")
    @TableField("business_group")
    private Integer businessGroup;

    @ApiModelProperty(value = "是否考核季度(0.否；1.是)")
    @TableField("is_quarter")
    private int isQuarter;

}
