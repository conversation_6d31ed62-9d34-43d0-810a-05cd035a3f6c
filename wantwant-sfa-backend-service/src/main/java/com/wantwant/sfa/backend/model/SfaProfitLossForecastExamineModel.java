package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_profitloss_forecast_examine")
@ApiModel(value = "损益线下预估审核表", description = "")
public class SfaProfitLossForecastExamineModel {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "部门code")
    @TableField("section_code")
    private String sectionCode;

    @ApiModelProperty(value = "部门")
    @TableField("section")
    private String section;

    @ApiModelProperty(value = "费用归属code")
    @TableField("expense_ownership_code")
    private String expenseOwnershipCode;

    @ApiModelProperty(value = "费用归属组别")
    @TableField("expense_ownership_group")
    private String expenseOwnershipGroup;

    @ApiModelProperty(value = "费用归属开始月份")
    @TableField("expense_ownership_start")
    private String expenseOwnershipStart;

    @ApiModelProperty(value = "费用归属结束月份")
    @TableField("expense_ownership_end")
    private String expenseOwnershipEnd;

    @ApiModelProperty(value = "旺金币费用code")
    @TableField("golden_coin_code")
    private String goldenCoinCode;

    @ApiModelProperty(value = "旺金币费用类型")
    @TableField("golden_coin_type")
    private String goldenCoinType;

    @ApiModelProperty(value = "边际定义(1.边际上,2.边际下)")
    @TableField("marginal_definition")
    private Integer marginalDefinition;

    @ApiModelProperty(value = "费用预估")
    @TableField("expense_forecast")
    private String expenseForecast;

    @ApiModelProperty(value = "预估逻辑")
    @TableField("forecast_logic")
    private String forecastLogic;

    @ApiModelProperty(value = "审核状态(0.未审核;1.通过;2.驳回)")
    @TableField("audit_status")
    private int auditStatus;

    @ApiModelProperty(value = "审核意见")
    @TableField("audit_opinion")
    private String auditOpinion;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除")
    @TableField("is_delete")
    private Integer isDelete;
}
