package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_referral_bonus_standard")
@ApiModel(value = "推荐奖金达标表", description = "")
public class SfaReferralBonusStandard {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "推荐人姓名")
    @TableField("superior_name")
    private String superiorName;

    @ApiModelProperty(value = "推荐人手机号")
    @TableField("superior_mobile")
    private String superiorMobile;

    @ApiModelProperty(value = "推荐人工号")
    @TableField("superior_employ_id")
    private String superiorEmployId;

    @ApiModelProperty(value = "推荐人岗位")
    @TableField("superior_position_name")
    private String superiorPositionName;

    @ApiModelProperty(value = "被推荐人姓名")
    @TableField("employee_name")
    private String employeeName;

    @ApiModelProperty(value = "被推荐人工号")
    @TableField("employee_id")
    private String employeeId;

    @ApiModelProperty(value = "被推荐人手机号")
    @TableField("user_mobile")
    private String userMobile;

    @ApiModelProperty(value = "被推荐人empId")
    @TableField("referral_empId")
    private Integer referralEmpId;

    @ApiModelProperty(value = "被推荐人大区")
    @TableField("region_name")
    private String regionName;

    @ApiModelProperty(value = "被推荐人分公司")
    @TableField("branch_co_name")
    private String branchCoName;

    @ApiModelProperty(value = "入职日期")
    @TableField("onboard_time")
    private LocalDateTime onboardTime;

    @ApiModelProperty(value = "入职岗位")
    @TableField("position_name")
    private String positionName;

    @ApiModelProperty(value = "岗位属性")
    @TableField("post_type")
    private String postType;

    @ApiModelProperty(value = "在岗状态")
    @TableField("employee_status")
    private String employeeStatus;

    @ApiModelProperty(value = "在岗天数")
    @TableField("onboard_days")
    private Integer onboardDays;

    @ApiModelProperty(value = "达标日期")
    @TableField("standard_date")
    private String standardDate;

    @ApiModelProperty(value = "第1个月业绩")
    @TableField("first_month_performance")
    private BigDecimal firstMonthPerformance;

    @ApiModelProperty(value = "第2个月业绩")
    @TableField("second_month_performance")
    private BigDecimal secondMonthPerformance;

    @ApiModelProperty(value = "第3个月业绩")
    @TableField("third_month_performance")
    private BigDecimal thirdMonthPerformance;

    @ApiModelProperty(value = "第4个月业绩")
    @TableField("fourth_month_performance")
    private BigDecimal fourthMonthPerformance;

    @ApiModelProperty(value = "第5个月业绩")
    @TableField("fifth_month_performance")
    private BigDecimal fifth_month_performance;

    @ApiModelProperty(value = "第6个月业绩")
    @TableField("sixth_month_performance")
    private BigDecimal sixth_month_performance;

    @ApiModelProperty(value = "奖励方案")
    @TableField("reward_plan")
    private String rewardPlan;

    @ApiModelProperty(value = "奖励类型")
    @TableField("reward_type")
    private String rewardType;

    @ApiModelProperty(value = "奖励金额")
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "奖励说明")
    @TableField("reward_instructions")
    private String rewardInstructions;
}
