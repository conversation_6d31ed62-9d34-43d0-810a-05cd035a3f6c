package com.wantwant.sfa.backend.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_referral_bonus_standard_issue_record")
@ApiModel(value = "推荐奖金达标发放表", description = "")
public class SfaReferralBonusStandardIssueRecord {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "推荐人姓名")
    @TableField("superior_name")
    private String type;

    @ApiModelProperty(value = "推荐人手机号")
    @TableField("superior_mobile")
    private String superiorMobile;

    @ApiModelProperty(value = "推荐人工号")
    @TableField("superior_employ_id")
    private String superiorEmployId;

    @ApiModelProperty(value = "被推荐人姓名")
    @TableField("employee_name")
    private String employeeName;

    @ApiModelProperty(value = "被推荐人工号")
    @TableField("employee_id")
    private String employeeId;

    @ApiModelProperty(value = "被推荐人手机号")
    @TableField("user_mobile")
    private String userMobile;

    @ApiModelProperty(value = "被推荐人empId")
    @TableField("referral_empId")
    private Integer referralEmpId;

    @ApiModelProperty(value = "达标日期")
    @TableField("standard_date")
    private String standardDate;

    @ApiModelProperty(value = "奖励类型")
    @TableField("reward_type")
    private String rewardType;

    @ApiModelProperty(value = "奖励金额")
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "状态(0.未申请;1.已取消;2.待复核;3.待审批;4.已驳回;5.发放成功;6.发放失败;7.发放中)")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "备注(取消/驳回/发放原因)")
    @TableField("note")
    private String note;

    @ApiModelProperty(value = "申请时间")
    @TableField("apply_date")
    private LocalDateTime applyDate;

    @ApiModelProperty(value = "发放时间")
    @TableField("issue_date")
    private LocalDateTime issueDate;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_name")
    private String updateName;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;


}
