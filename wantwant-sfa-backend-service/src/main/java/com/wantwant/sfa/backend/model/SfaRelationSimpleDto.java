package com.wantwant.sfa.backend.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class SfaRelationSimpleDto {
    private String organizationId;
    private String organizationName;
    private String organizationType;
    private String positionId;
    private String employeeId;
    private String employeeName;
    @JSONField(format = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime onboardTime;
    @JSONField(format = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime offTime;
}
