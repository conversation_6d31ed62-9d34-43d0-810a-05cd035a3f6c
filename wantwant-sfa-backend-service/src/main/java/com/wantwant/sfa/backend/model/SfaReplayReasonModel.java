package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> rong hua
 * @description: //sfa规则设置表
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源 @Time 2021-4-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_replay_reason_message")
@ApiModel(value = "SfaReplayReasonModel对象", description = "sfa_replay_reason_message表")
public class SfaReplayReasonModel extends Model<SfaReplayReasonModel> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer replayReasonId;

  @ApiModelProperty(value = "分支原因上级Id")
  @TableField("parent_id")
  private Integer parentId;

  @ApiModelProperty(value = "报表id")
  @TableField("report_id")
  private Integer reportId;

  @ApiModelProperty(value = "复盘类型: 1、基本数据概括；2、团队达成")
  @TableField("replay_reason_type")
  private Integer replayReasonType;

  @ApiModelProperty(value = "原因指标类型: 1、目标达成率；2、用人费用")
  @TableField("reason_index_type")
  private Integer reasonIndexType;

  @ApiModelProperty(value = "组织id")
  @TableField("organization_id")
  private String organizationId;

  @ApiModelProperty(value = "组织名称")
  @TableField("organization_name")
  private String organizationName;

  @ApiModelProperty(value = "月份")
  @TableField("month")
  private String month;

  @ApiModelProperty(value = "描述")
  @TableField("reason")
  private String reason;

  @ApiModelProperty(value = "描述(百分比)")
  @TableField("reason_percent")
  private String reasonPercent;

  @ApiModelProperty(value = "顺序")
  @TableField("reason_order")
  private Integer reasonOrder;

  @ApiModelProperty(value = "创建人")
  @TableField("create_people")
  private String createPeople;

  @ApiModelProperty(value = "创建时间")
  @TableField("create_time")
  private LocalDateTime createTime;

  @ApiModelProperty(value = "修改人")
  @TableField("update_people")
  private String updatePeople;

  @ApiModelProperty(value = "修改时间")
  @TableField("update_time")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "是否删除(0.否;1.是)")
  @TableField("is_delete")
  private Integer deleteFlag;

}
