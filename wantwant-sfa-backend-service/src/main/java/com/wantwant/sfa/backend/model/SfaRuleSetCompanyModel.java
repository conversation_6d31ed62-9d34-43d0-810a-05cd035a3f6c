package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> rong hua
 * @description: //sfa规则设置表
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源 @Time 2021-4-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_rule_set_company")
@ApiModel(value = "sfaRuleSetCompany对象", description = "sfa_rule_set_company表")
public class SfaRuleSetCompanyModel extends Model<SfaRuleSetCompanyModel> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @ApiModelProperty(value = "关联规则id")
  @TableField("rule_set_id")
  private Integer ruleSetId;

  @ApiModelProperty(value = "分公司id")
  @TableField("company_id")
  private String companyId;

  @ApiModelProperty(value = "分公司名称")
  @TableField("company_name")
  private String companyName;

  @ApiModelProperty(value = "创建人id")
  @TableField("create_id")
  private String createId;

  @ApiModelProperty(value = "创建时间")
  @TableField("create_time")
  private LocalDateTime createTime;

  @ApiModelProperty(value = "更新人id")
  @TableField("update_id")
  private String updateId;

  @ApiModelProperty(value = "更新时间")
  @TableField("update_time")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "是否删除")
  @TableField("delete_flag")
  private Integer deleteFlag;
}
