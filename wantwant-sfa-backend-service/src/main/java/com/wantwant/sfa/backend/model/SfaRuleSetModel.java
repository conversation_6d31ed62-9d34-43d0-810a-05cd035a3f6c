package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> rong hua
 * @description: //sfa规则设置表
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源 @Time 2021-4-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_rule_set")
@ApiModel(value = "sfaRuleSete对象", description = "sfa_rule_set表")
public class SfaRuleSetModel extends Model<SfaRuleSetModel> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @ApiModelProperty(value = "规则类型(0.考核;1.扣款)")
  @TableField("rule_type")
  private Integer ruleType;

  @ApiModelProperty(value = "规则名称")
  @TableField("rule_name")
  private String ruleName;

  @ApiModelProperty(value = "规则范围条件类型")
  @TableField("rule_range_type")
  private Integer ruleRangeType;

  @ApiModelProperty(value = "规则范围运算类型")
  @TableField("rule_range_operation_type")
  private Integer ruleRangeOperationType;

  @ApiModelProperty(value = "规则范围是否是数值")
  @TableField("rule_range_is_numerical")
  private Integer ruleRangeIsNumerical;

  @ApiModelProperty(value = "规则范围阀值")
  @TableField("rule_range_threshold")
  private Integer ruleRangeThreshold;

  @ApiModelProperty(value = "规则指标类型")
  @TableField("rule_indicators_type")
  private Integer ruleIndicatorsType;

  @ApiModelProperty(value = "规则指标运算类型")
  @TableField("rule_indicators_operation_type")
  private Integer ruleIndicatorsOperationType;

  @ApiModelProperty(value = "规则指标是否是数值")
  @TableField("rule_indicators_is_numerical")
  private Integer ruleIndicatorsIsNumerical;

  @ApiModelProperty(value = "规则指标阀值")
  @TableField("rule_indicators_threshold")
  private Double ruleIndicatorsThreshold;

  @ApiModelProperty(value = "执行状态")
  @TableField("perform_state")
  private Integer performState;

  @ApiModelProperty(value = "执行扣款")
  @TableField("perform_deductions")
  private Double performDeductions;

  @ApiModelProperty(value = "创建人组织id")
  @TableField("create_organiztaion_id")
  private String createOrganiztaionId;

  @ApiModelProperty(value = "创建人id")
  @TableField("create_id")
  private String createId;

  @ApiModelProperty(value = "创建时间")
  @TableField("create_time")
  private LocalDateTime createTime;

  @ApiModelProperty(value = "更新人id")
  @TableField("update_id")
  private String updateId;

  @ApiModelProperty(value = "更新时间")
  @TableField("update_time")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "是否删除")
  @TableField("delete_flag")
  private Integer deleteFlag;
}
