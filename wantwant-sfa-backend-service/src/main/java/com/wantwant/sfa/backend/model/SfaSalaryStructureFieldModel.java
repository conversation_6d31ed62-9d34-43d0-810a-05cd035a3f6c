package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_salary_structure_field")
@ApiModel(value = "薪资结构字段配置表", description = "")
public class SfaSalaryStructureFieldModel extends Model<SfaSalaryStructureFieldModel> {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "主表ID")
    @TableField("structure_id")
    private Integer structureId;

    @ApiModelProperty(value = "列名称")
    @TableField("column_name")
    private String columnName;

    @ApiModelProperty(value = "父列ID")
    @TableField("structure_field_id")
    private Integer structureFieldId;

    @ApiModelProperty(value = "列顺序")
    @TableField("column_order")
    private Integer columnOrder;

    @ApiModelProperty(value = "是否固定(0.否;1.是)")
    @TableField("is_fixed")
    private Integer isFixed;

    @ApiModelProperty(value = "列宽")
    @TableField("column_width")
    private Integer columnWidth;

    @ApiModelProperty(value = "最小列宽")
    @TableField("minimum_column_width")
    private Integer minimumColumnWidth;

    @ApiModelProperty(value = "是否显示(0.否;1.是)")
    @TableField("is_show")
    private Integer isShow;

    @ApiModelProperty(value = "外部字段名称")
    @TableField("external_field_name")
    private String externalFieldName;

    @ApiModelProperty(value = "创建日期")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;
}
