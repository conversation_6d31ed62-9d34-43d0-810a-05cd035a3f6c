package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_salary_structure")
@ApiModel(value = "SfaSalaryStructure对象", description = "")
public class SfaSalaryStructureModel extends Model<SfaSalaryStructureModel> {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "岗位类型(1.大区;2.分公司;3.营业所,4.区域经理)")
    @TableField("post_type")
    private Integer postType;

    @ApiModelProperty(value = "时间范围")
    @TableField("time_scope")
    private String timeScope;

    @ApiModelProperty(value = "生效日期")
    @TableField("start_date")
    private Integer startDate;

    @ApiModelProperty(value = "失效日期")
    @TableField("end_date")
    private Integer endDate;

    @ApiModelProperty(value = "创建日期")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;
}
