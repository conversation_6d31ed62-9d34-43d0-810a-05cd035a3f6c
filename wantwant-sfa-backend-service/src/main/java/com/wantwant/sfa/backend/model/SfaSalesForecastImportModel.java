package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_sales_forecast_import")
@ApiModel(value = "sfaSalesForecastImportModel对象", description = "")
public class SfaSalesForecastImportModel extends Model<SfaSalesForecastImportModel> {

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @ApiModelProperty(value = "月份")
  @TableField("date")
  private String date;

  @ApiModelProperty(value = "SKU")
  @TableField("sku")
  private String sku;

  @ApiModelProperty(value = "在库+在途周转天数")
  @TableField("median_Margin")
  private Double medianargin;

  @ApiModelProperty(value = "1期提报")
  @TableField("first_report")
  private Double firstReport;

  @ApiModelProperty(value = "3期提报")
  @TableField("third_report")
  private Double thirdReport;

  @ApiModelProperty(value = "3期调整量")
  @TableField("third_adjust")
  private Double thirdAdjust;

  @ApiModelProperty(value = "4期调整量")
  @TableField("fourth_adjust")
  private Double fourthAdjust;

  @ApiModelProperty(value = "1期开单")
  @TableField("first_billing")
  private Double firstBilling;

  @ApiModelProperty(value = "2期开单")
  @TableField("second_billing")
  private Double secondBilling;

  @ApiModelProperty(value = "3期开单")
  @TableField("third_billing")
  private Double thirdBilling;

  @ApiModelProperty(value = "4期开单")
  @TableField("four_billing")
  private Double fourBilling;

  @ApiModelProperty(value = "开单总量")
  @TableField("billing_total")
  private Double billingTotal;

  @ApiModelProperty(value = "开单差异量")
  @TableField("billing_difference")
  private Double billingDifference;

  @ApiModelProperty(value = "无法供货")
  @TableField("unable_supply")
  private Double unableSupply;

  @ApiModelProperty(value = "实际供货")
  @TableField("actual_supply")
  private Double actualSupply;

  @ApiModelProperty(value = "已入库")
  @TableField("warehoused")
  private Double warehoused;

  @ApiModelProperty(value = "借货调拨")
  @TableField("allot")
  private Double allot;

  @ApiModelProperty(value = "调拨在途量截止至今")
  @TableField("allot_transfer")
  private Double allotTransfer;

  @ApiModelProperty(value = "调拔已入库")
  @TableField("allot_in_storage")
  private Double allotInStorage;

  @ApiModelProperty(value = "仓库类型(1.济南;2.长沙;3.隆昌;4.沈阳)")
  @TableField("warehouse_type")
  private Integer warehouseType;

  @ApiModelProperty(value = "创建人")
  @TableField("create_people")
  private String createPeople;

  @ApiModelProperty(value = "创建时间")
  @TableField("create_time")
  private LocalDateTime createTime;

  @ApiModelProperty(value = "更新人")
  @TableField("update_people")
  private String updatePeople;

  @ApiModelProperty(value = "更新时间")
  @TableField("update_time")
  private LocalDateTime updateTime;

  @ApiModelProperty(value = "是否删除")
  @TableField("is_delete")
  private Integer isDelete;
}
