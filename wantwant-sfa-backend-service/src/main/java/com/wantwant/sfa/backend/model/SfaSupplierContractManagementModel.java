package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_supplier_contract_management")
@ApiModel(value = "供应商合同管理表对象", description = "")
public class SfaSupplierContractManagementModel {


    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @ApiModelProperty(value = "供应商代码")
    @TableField("supplier_code")
    private String supplierCode;

    @ApiModelProperty(value = "供应商类型(0.运输;1.仓储;2.劳务)")
    @TableField("supplier_type")
    private Integer supplierType;

    @ApiModelProperty(value = "供应商名称")
    @TableField("supplier_name")
    private String supplierName;

    @ApiModelProperty(value = "二级供应商代码")
    @TableField("supplier_code_two")
    private String supplierCodeTwo;

    @ApiModelProperty(value = "二级供应商名称")
    @TableField("supplier_name_two")
    private String supplierNameTwo;

    @ApiModelProperty(value = "开户账号")
    @TableField("account_no")
    private String accountNo;

    @ApiModelProperty(value = "开户行")
    @TableField("bank_of_deposit")
    private String bankOfDeposit;

    @ApiModelProperty(value = "合同开始日期")
    @TableField("contract_start_time")
    private LocalDate contractStartTime;

    @ApiModelProperty(value = "合同结束日期")
    @TableField("contract_end_time")
    private LocalDate contractEndTime;

    @ApiModelProperty(value = "合同到期提醒（天）")
    @TableField("expiration_reminder_day")
    private Integer expirationReminderDay;

    @ApiModelProperty(value = "说明")
    @TableField(value = "`explain`",fill = FieldFill.UPDATE)
    private String explain;

    @ApiModelProperty(value = "运输模式")
    @TableField(value = "`transport_mode`")
    private String transportMode;

    @ApiModelProperty(value = "当前状态(0.已生效;1.已过期)")
    @TableField("state")
    private Integer state;

    @ApiModelProperty(value = "创建人")
    @TableField("create_people")
    private String createPeople;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("update_people")
    private String updatePeople;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除(0.否;1.是)")
    @TableField("is_delete")
    private Integer isDelete;
}
