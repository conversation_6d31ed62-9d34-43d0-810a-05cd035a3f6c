package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_warehousing")
@ApiModel(value = "sfaWarehousing对象", description = "")
public class SfaWarehousingModel extends Model<SfaWarehousingModel> {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("date")
    private String date;

    @TableField("warehousing_id")
    private Integer warehousingId;

    @TableField("warehousing_name")
    private String warehousingName;

    @TableField("storage_rental_fee_total_day")
    private BigDecimal storageRentalFeeTotalDay;

    @TableField("group_share_storage_day")
    private BigDecimal groupShareStorageDay;

    @TableField("loading_unloading")
    private BigDecimal loadingUnloading;

    @TableField("picking")
    private BigDecimal picking;

    @TableField("other")
    private BigDecimal other;

    @TableField("hydropowert")
    private BigDecimal hydropowert;

    @TableField("carton_cost")
    private BigDecimal cartonCost;

    @TableField("excel_id")
    private Integer excelId;
}
