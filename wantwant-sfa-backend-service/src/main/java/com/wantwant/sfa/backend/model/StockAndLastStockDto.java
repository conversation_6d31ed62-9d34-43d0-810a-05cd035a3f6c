package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: luxiaoyin
 * @Date: 2020/4/15
 * @Package: com.wantwant.sfa.backend.model
 */
@Data
public class StockAndLastStockDto {
  private Integer id;

  private String sku;

  private String batch;

  private LocalDateTime productionDate;

  private LocalDateTime expireDate;

  private String name;

  private String imgUrl;
  private String imgKey;

  private Integer ptKey;

  private String flavour;

  private String spec;

  private Integer amount;

  private String oldSku;

  private String oldBatch;

  private Integer oldAmount;

  private Integer lastDigestedAmount;
}
