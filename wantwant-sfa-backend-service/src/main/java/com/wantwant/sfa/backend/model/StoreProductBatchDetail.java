package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("store_product_batch_detail")
@ApiModel(value = "StoreProductBatchDetail对象", description = "")
public class StoreProductBatchDetail extends Model<StoreProductBatchDetail> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "`key`", type = IdType.AUTO)
  private Integer key;

  @TableField("sku")
  private String sku;

  @ApiModelProperty(value = "产品类型 1:零售 2:箱购")
  @TableField("type")
  private Integer type;

  @ApiModelProperty(value = "库存")
  @TableField("inventory")
  private Integer inventory;

  @ApiModelProperty(value = "批次售卖数量")
  @TableField("sold")
  private Integer sold;

  @ApiModelProperty(value = "批次号")
  @TableField("batch_mgmt_number")
  private String batchMgmtNumber;

  @ApiModelProperty(value = "批次效期编码")
  @TableField("goods_batch_code")
  private String goodsBatchCode;

  @ApiModelProperty(value = "wms 库存总数")
  @TableField("wms_inventory_total")
  private Integer wmsInventoryTotal;

  @ApiModelProperty(value = "wms 占用库存")
  @TableField("wms_inventory_occupy")
  private Integer wmsInventoryOccupy;

  @ApiModelProperty(value = "wms 锁住库存")
  @TableField("wms_inventory_lock")
  private Integer wmsInventoryLock;

  @ApiModelProperty(value = "wms 初始库存")
  @TableField("wms_inventory_initial_occpuy")
  private Integer wmsInventoryInitialOccpuy;

  @ApiModelProperty(value = "库存总数")
  @TableField("inventory_total")
  private Integer inventoryTotal;

  @ApiModelProperty(value = "锁住库存")
  @TableField("inventory_lock")
  private Integer inventoryLock;

  @ApiModelProperty(value = "已使用库存")
  @TableField("inventory_occupy")
  private Integer inventoryOccupy;

  @TableField("updatedAt")
  private LocalDateTime updatedAt;

  @ApiModelProperty(value = "批次的插入时间")
  @TableField("createAt")
  private LocalDateTime createAt;

  @TableField("version")
  private Integer version;

  @ApiModelProperty(value = "生产日期")
  @TableField("production_date")
  private LocalDateTime productionDate;

  @ApiModelProperty(value = "过期时间")
  @TableField("expire_date")
  private LocalDateTime expireDate;

  @ApiModelProperty(value = "该批次是否有效:0、有效;1、无效")
  @TableField("status")
  private Boolean status;


  @Override
  protected Serializable pkVal() {
    return this.key;
  }

}
