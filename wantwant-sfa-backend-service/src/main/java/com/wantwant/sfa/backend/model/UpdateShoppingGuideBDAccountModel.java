package com.wantwant.sfa.backend.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhangpengpeng
 * @Date: 2024/07/03
 */
@Data
@ToString
public class UpdateShoppingGuideBDAccountModel extends OpenShoppingGuideBDAccountModel {

    @ApiModelProperty(value = "memberKey")
    private Long memberKey;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "更新的字段数组：0-更新状态、1-更新备注、2-更新组织、3-更新姓名、4-更新手机号")
    List<Integer> updateTypes;


}
