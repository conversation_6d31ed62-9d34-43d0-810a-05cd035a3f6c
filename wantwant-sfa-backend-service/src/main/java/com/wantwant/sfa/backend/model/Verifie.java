package com.wantwant.sfa.backend.model;

import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Verifie {
    @ApiModelProperty("表单编号")
    private String verifieId;  
    
    @ApiModelProperty("申请时间")
    private Date requestAt; 
    
    @ApiModelProperty("审核时间")
    private Date verifiedAt; 
    
    @ApiModelProperty("表单状态 0 未审核,1 审核通过,2 驳回")
    private String isVerified;  
        
    @ApiModelProperty("驳回意见")
    private List<String> dismissedSuggestions;      
    
    @ApiModelProperty("处理意见")
    private String verifieSuggestions; 
	
	@ApiModelProperty(value = "岗位ID")
	String positionId;
	
	@ApiModelProperty(value = "业务姓名")
	String employeeName;
	
	@ApiModelProperty("组织名称")
	String organizationName;	

    @ApiModelProperty("审核记录")
    private List<Customer> customerList;  
        
    @ApiModelProperty("操作人")
    private String person; 
    
    @ApiModelProperty("操作人名称")
    private String personName;  
    
}
