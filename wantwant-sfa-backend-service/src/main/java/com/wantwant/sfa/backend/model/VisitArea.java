package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2020/11/12 16:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SfaVisitArea对象", description = "推荐区域设置表")
@TableName("sfa_visit_area")
public class VisitArea {

    private Integer id;

    @ApiModelProperty("业务员id")
    private String memberId;

    @ApiModelProperty("设置时间")
    private LocalDate setTime;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;
}
