package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 拜访目的类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_visit_goal_type")
@ApiModel(value = "VisitGoalType对象", description = "拜访目的类型表")
public class VisitGoalType extends Model<VisitGoalType> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("type_code")
    private Integer typeCode;

    @TableField("customer_type")
    private Integer customerType;

    @TableField("is_potential")
    private Integer isPotential;

    @TableField("name")
    private String name;

    @TableField("is_active")
    private Integer isActive;

    @TableField("sort")
    private Integer sort;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_person")
    private String createPerson;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("update_person")
    private String updatePerson;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
