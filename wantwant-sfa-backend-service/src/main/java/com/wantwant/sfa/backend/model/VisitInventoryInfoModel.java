package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description：拜访-库存盘点
 * @Author： chen
 * @Date 2022/5/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "VisitInventoryInfoModel对象", description = "拜访-库存盘点表")
@TableName("customer_visit_inventory_info")
public class VisitInventoryInfoModel  extends Model<VisitInventoryInfoModel> {

    @ApiModelProperty(value = "id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "`visit_id`")
    private String visitId;

    @TableField(value = "`customer_id`")
    private String customerId;

    @TableField(value = "`partner_member_key`")
    @ApiModelProperty("合伙人memberKey")
    private String partnerMemberKey;



    @TableField(value = "`sku`")
    @ApiModelProperty("产品编码")
    private String sku;

    @TableField(value = "`sku_name`")
    @ApiModelProperty("产品名称")
    private String skuName;

    @TableField(value = "`single_pack_unit`")
    @ApiModelProperty("单位")
    private String singlePackUnit;

    @TableField(value = "`box_num`")
    @ApiModelProperty("箱数")
    private String boxNum;

    @TableField(value = "`unit_num`")
    @ApiModelProperty("单位数量")
    private String unitNum;

    @TableField(value = "`sku_image_url`")
    @ApiModelProperty("sku图片url")
    private String skuImageUrl;

    @TableField(value = "`quantity`")
    @ApiModelProperty("数量(箱购)")
    private String quantity;

    @TableField(value = "`is_snapshot`")
    @ApiModelProperty("是否快照:1_是、0_否")
    private String isSnapshot;

    @TableField(value = "`delete_flag`")
    @ApiModelProperty("删除标识 0-有效  1-无效")
    private String deleteFlag;

    @TableField(value = "`creator`")
    @ApiModelProperty("创建人")
    private String creator;

    @TableField(value = "`updator`")
    @ApiModelProperty("更新人")
    private String updator;

    @TableField(value = "`create_time`")
    @ApiModelProperty("创建时间")
    private String createTime;

    @TableField(value = "`update_time`")
    @ApiModelProperty("更新时间")
    private String updateTime;

}
