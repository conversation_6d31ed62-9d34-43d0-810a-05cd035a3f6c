package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 核心指标预警员工快照(每周)
 *
 * @since 2023-04-24
 */
@Data
@TableName("sfa_weekly_employee_snapshot")
public class WeeklyEmployeeSnapshotPO extends Model<WeeklyEmployeeSnapshotPO> {

	@TableId(value = "id")
	private Integer id;

	/**
	* 员工姓名
	*/
	@TableField("employee_name")
	private String employeeName;

	/**
	* 手机号
	*/
	@TableField("mobile")
	private String mobile;

	/**
	* 状态(1.试岗,2.入职)
	*/
	@TableField("state")
	private Integer state;

	/**
	* 所属大区CODE
	*/
	@TableField("area_code")
	private String areaCode;

	/**
	* 所属大区名称
	*/
	@TableField("area_name")
	private String areaName;

	/**
	* 所属分公司CODE
	*/
	@TableField("company_code")
	private String companyCode;

	/**
	* 所属分公司名称
	*/
	@TableField("company_name")
	private String companyName;

	/**
	* 区域经理(所)code
	*/
	@TableField("department_code")
	private String departmentCode;

	/**
	* 区域经理(所)名称
	*/
	@TableField("department_name")
	private String departmentName;

	@TableField("branch_code")
	private String branchCode;

	@TableField("branch_name")
	private String branchName;

	@TableField("province_id")
	private String provinceId;

	@TableField("province_name")
	private String provinceName;

	@TableField("virtual_area_id")
	private String virtualAreaId;

	@TableField("virtual_area_name")
	private String virtualAreaName;

	@TableField(value = "`business_group`")
	private Integer businessGroup;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
