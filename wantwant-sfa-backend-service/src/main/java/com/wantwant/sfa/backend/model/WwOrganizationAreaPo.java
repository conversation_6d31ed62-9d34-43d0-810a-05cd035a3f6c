package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 爱旺旺大区分公司地区划分
 *
 * @since 2021-11-08
 */
@Data
@TableName("sfa_wworganization_area")
public class WwOrganizationAreaPo extends Model<WwOrganizationAreaPo> {

	private static final long serialVersionUID = 4749034531451928280L;

	@TableId(value = "id")
	private Long id;

	/**
	* 省
	*/
	@TableField("province_code")
	private String provinceCode;

	@TableField("province_name")
	private String provinceName;

	/**
	* 市
	*/
	@TableField("city_code")
	private String cityCode;

	@TableField("city_name")
	private String cityName;

	/**
	* 区县
	*/
	@TableField("district_code")
	private String districtCode;

	@TableField("district_name")
	private String districtName;

	/**
	 * 街道
	 */
	@TableField("street_code")
	private String streetCode;

	@TableField("street_name")
	private String streetName;


	/**
	* 大区ID
	*/
	@TableField("area_organization_id")
	private String areaOrganizationId;

	/**
	* 大区
	*/
	@TableField("area_organization_name")
	private String areaOrganizationName;

	/**
	* 分公司ID
	*/
	@TableField("company_organization_id")
	private String companyOrganizationId;

	/**
	* 分公司
	*/
	@TableField("company_organization_name")
	private String companyOrganizationName;

	/**
	* 营业所ID
	*/
	@TableField("branch_organization_id")
	private String branchOrganizationId;

	/**
	* 营业所
	*/
	@TableField("branch_organization_name")
	private String branchOrganizationName;
	/**
	 * 分公司ID(冲刺组)
	 */
	@TableField("ex_company_organization_id")
	private String exCompanyOrganizationId;

	/**
	 * 分公司名称(冲刺组)
	 */
	@TableField("ex_company_organization_name")
	private String exCompanyOrganizationName;

}
