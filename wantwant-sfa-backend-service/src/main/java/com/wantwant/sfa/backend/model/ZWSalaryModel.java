package com.wantwant.sfa.backend.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_salary")
@ApiModel(value = "SfaSalary对象", description = "")
public class ZWSalaryModel extends Model<ZWSalaryModel> {

  @TableId(value = "id", type = IdType.AUTO)
  @TableField("id")
  private Integer id;

  @TableField("organization_id")
  private String organizationId;

  @TableField("organization_name")
  private String organizationName;

  @TableField("salary_data")
  private Integer salaryData;

  @TableField("standard_salary")
  private Integer standardSalary;

  @TableField("organization_type")
  private Integer organizationType;

  @TableField("is_delete")
  private Integer isDelete;
}
