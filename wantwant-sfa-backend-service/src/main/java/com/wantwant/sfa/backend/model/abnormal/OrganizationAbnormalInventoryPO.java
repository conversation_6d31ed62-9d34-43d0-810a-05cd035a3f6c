package com.wantwant.sfa.backend.model.abnormal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 分公司异常库存
 *
 * @since 2023-08-21
 */
@Data
@TableName("sfa_organization_abnormal_inventory")
public class OrganizationAbnormalInventoryPO extends Model<OrganizationAbnormalInventoryPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_organization_abnormal_inventory_process.id
	*/
	@TableField("p_id")
	private Long pId;

	/**
	* 异常库存月份
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 组织name
	*/
	@TableField("organization_name")
	private String organizationName;

	/**
	* 申请时间
	*/
	@TableField("apply_time")
	private LocalDateTime applyTime;

	/**
	* 渠道ID
	*/
	@TableField("channel_id")
	private String channelId;

	/**
	* 渠道名称
	*/
	@TableField("channel_name")
	private String channelName;

	/**
	* 申请处理库存
	*/
	@TableField("total_inventory")
	private Integer totalInventory;

	/**
	* 申请处理货值
	*/
	@TableField("total_value")
	private BigDecimal totalValue;

	/**
	* 扣罚旺金币
	*/
	@TableField("total_deduction_want")
	private BigDecimal totalDeductionWant;

	/**
	* 审核状态(0:审批中,1:已通过,2:驳回)
	*/
	@TableField("status")
	private Integer status;

	/**
	* 是否主推品(0:否,1:是)
	*/
	@TableField("main_flag")
	private Integer mainFlag;

	@TableField("created_by")
	private String createdBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
