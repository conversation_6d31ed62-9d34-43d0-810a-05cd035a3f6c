package com.wantwant.sfa.backend.model.abnormal;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分公司异常库存审批
 *
 * @since 2023-08-21
 */
@Data
@TableName("sfa_organization_abnormal_inventory_process")
public class OrganizationAbnormalInventoryProcessPO extends Model<OrganizationAbnormalInventoryProcessPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id",type = IdType.INPUT)
	private Long id;

	/**
	* sfa_organization_abnormal_inventory.id
	*/
	@TableField("a_id")
	private Integer aId;

	/**
	* 经办部门
	*/
	@TableField("dept_name")
	private String deptName;

	/**
	* 经办部门CODE
	*/
	@TableField("dept_code")
	private String deptCode;

	/**
	* 经办人
	*/
	@TableField("review_id")
	private String reviewId;

	/**
	* 经办人名称
	*/
	@TableField("review_name")
	private String reviewName;

	/**
	* 审批时间
	*/
	@TableField("review_time")
	private LocalDateTime reviewTime;

	/**
	* 审批节点(1.总督导,2.非主推品-平台运营,3.主推品-商企,4.单位主管)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 审批结果(1.待审核,2.审核通过,3.审核驳回)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	* 审批内容
	*/
	@TableField("comment")
	private String comment;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableField("is_delete")
	private Integer isDelete;
}
