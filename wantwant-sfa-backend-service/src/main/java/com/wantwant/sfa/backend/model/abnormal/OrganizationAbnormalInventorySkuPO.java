package com.wantwant.sfa.backend.model.abnormal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 分公司异常库存sku维度
 *
 * @since 2023-08-21
 */
@Data
@TableName("sfa_organization_abnormal_inventory_sku")
public class OrganizationAbnormalInventorySkuPO extends Model<OrganizationAbnormalInventorySkuPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_organization_abnormal_inventory.id
	*/
	@TableField("a_id")
	private Integer aId;

	/**
	* 异常库存月份
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* sku编码
	*/
	@TableField("sku")
	private String sku;

	/**
	* sku全称
	*/
	@TableField("sku_name")
	private String skuName;

	/**
	* 本次申请处理箱数
	*/
	@TableField("inventory")
	private Integer inventory;

	/**
	* 本次申请处理货值
	*/
	@TableField("value")
	private BigDecimal value;

	/**
	* 申请原因
	*/
	@TableField("description")
	private String description;

	/**
	* 扣罚旺金币
	*/
	@TableField("deduction_want")
	private BigDecimal deductionWant;

	/**
	* 组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 组织name
	*/
	@TableField("organization_name")
	private String organizationName;

	/**
	* 渠道ID
	*/
	@TableField("channel_id")
	private String channelId;

	/**
	* 渠道名称
	*/
	@TableField("channel_name")
	private String channelName;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableField("is_delete")
	private Integer isDelete;


}
