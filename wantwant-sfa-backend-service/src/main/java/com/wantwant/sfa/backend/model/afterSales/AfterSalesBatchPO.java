package com.wantwant.sfa.backend.model.afterSales;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后单批次集合
 *
 * @since 2022-11-22
 */
@Data
@TableName("sfa_after_sales_batch")
public class AfterSalesBatchPO extends Model<AfterSalesBatchPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_after_sales_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* sfa_after_sales_detail.id
	*/
	@TableField("detail_id")
	private Integer detailId;

	/**
	* 详细售后单信息id
	*/
	@TableField("detail_no")
	private String detailNo;

	/**
	* 申请箱数
	*/
	@TableField("apply_box_quantity")
	private String applyBoxQuantity;

	/**
	 * 客服审批箱数
	 */
	@TableField("box_quantity")
	private String boxQuantity;

	/**
	* 申请最小规格数
	*/
	@TableField("apply_min_pack")
	private String applyMinPack;

	/**
	 * 客服审批最小规格数
	 */
	@TableField("min_pack")
	private String minPack;

	/**
	* 申请最小规格数合计
	*/
	@TableField("apply_min_pack_total")
	private String applyMinPackTotal;

	/**
	 * 客服审批最小规格数合计
	 */
	@TableField("min_pack_total")
	private String minPackTotal;

	/**
	* 临期己退
	*/
	@TableField("apply_temporary_withdrawal")
	private BigDecimal applyTemporaryWithdrawal;

	/**
	 * 客服审批临期己退
	 */
	@TableField("temporary_withdrawal")
	private BigDecimal temporaryWithdrawal;

	/**
	* 常规己退
	*/
	@TableField("apply_regular_returned")
	private BigDecimal applyRegularReturned;

	/**
	 * 客服审批常规己退
	 */
	@TableField("regular_returned")
	private BigDecimal regularReturned;

	/**
	* 订单号
	*/
	@TableField("order_code")
	private String orderCode;

	/**
	* 规格实付金额
	*/
	@TableField("specification_paid_amount")
	private BigDecimal specificationPaidAmount;

	/**
	* 退款金额
	*/
	@TableField("apply_refund_amount")
	private BigDecimal applyRefundAmount;

	/**
	 * 客服审批退款金额
	 */
	@TableField("refund_amount")
	private BigDecimal refundAmount;

	/**
	* 子售后单状态
	*/
	@TableField("apply_order_status")
	private String applyOrderStatus;

	/**
	 * 客服审批子售后单状态
	 * 1:售后中,3:售后成功,4:拒绝退款
	 */
	@TableField("order_status")
	private String orderStatus;

	/**
	 * 箱规
	 */
	@TableField("pack_spec")
	private String packSpec;

	/**
	* 单位数
	*/
	@TableField("unit_num")
	private String unitNum;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
