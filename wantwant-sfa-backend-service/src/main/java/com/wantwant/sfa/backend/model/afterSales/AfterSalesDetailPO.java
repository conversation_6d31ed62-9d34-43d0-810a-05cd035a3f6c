package com.wantwant.sfa.backend.model.afterSales;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 详细售后单信息
 *
 * @since 2022-11-22
 */
@Data
@TableName("sfa_after_sales_detail")
public class AfterSalesDetailPO extends Model<AfterSalesDetailPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_after_sales_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* sku单支供货价
	*/
	@TableField("single_supply_price")
	private BigDecimal singleSupplyPrice;

	/**
	* 生产批次
	*/
	@TableField("batch")
	private String batch;

	/**
	* 退款数量合计
	*/
	@TableField("apply_refunds_number_total")
	private String applyRefundsNumberTotal;

	/**
	 * 客服审批退款数量合计
	 */
	@TableField("refunds_number_total")
	private String refundsNumberTotal;

	/**
	* 退货金额合计
	*/
	@TableField("apply_refund_amount_total")
	private BigDecimal applyRefundAmountTotal;

	/**
	 * 客服审批退货金额合计
	 */
	@TableField("refund_amount_total")
	private BigDecimal refundAmountTotal;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;


}
