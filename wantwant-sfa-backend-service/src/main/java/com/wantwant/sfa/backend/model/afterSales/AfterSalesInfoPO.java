package com.wantwant.sfa.backend.model.afterSales;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 申请售后单信息
 *
 * @since 2022-11-22
 */
@Data
@TableName("sfa_after_sales_info")
public class AfterSalesInfoPO extends Model<AfterSalesInfoPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 临期售后编号
	*/
	@TableField("application_no")
	private String applicationNo;

	/**
	* 合伙人memberKey
	*/
	@TableField("member_key")
	private String memberKey;

	/** 
	 * 所属业务memberKey:默认member_key,申请来源客户取grant_account
	 */
	@TableField("partner_member_key")
	private String partnerMemberKey;

	/**
	* 合伙人姓名
	*/
	@TableField("customer_name")
	private String customerName;

	/**
	* 产品编号
	*/
	@TableField("sku")
	private String sku;

	/**
	* 产品名称
	*/
	@TableField("sku_name")
	private String skuName;

	/**
	* 产品口味
	*/
	@TableField("flavour")
	private String flavour;

	/**
	* 最小可售后单位
	*/
	@TableField("min_unit")
	private String minUnit;

	/**
	* 申请退款数量合计
	*/
	@TableField("apply_refunds_number_total")
	private String applyRefundsNumberTotal;

	/**
	 * 客服审批合计退款数量
	 */
	@TableField("refunds_number_total")
	private String refundsNumberTotal;

	/**
	* 申请退款金额合计
	*/
	@TableField("apply_refund_amount_total")
	private BigDecimal applyRefundAmountTotal;

	/**
	 * 客服审批退款金额合计
	 */
	@TableField("refund_amount_total")
	private BigDecimal refundAmountTotal;

	/**
	* 异常情况说明
	*/
	@TableField("abnormal_description")
	private String abnormalDescription;

	/** 
	 * 售后凭证(多图,拼接) 
	 */
	@TableField("image_url")
	private String imageUrl;

	/**
	 * 申请时间
	 */
	@TableField(value = "apply_time")
	private LocalDateTime applyTime;


	/**
	 * 申请人工号
	 */
	@TableField(value = "applicant")
	private String applicant;

	/**
	 * 申请来源(0:合伙人,1:客服,2:区域经理,3.业务BD)
	 */
	@TableField(value = "source")
	private Integer source;

	/**
	 * 提报人
	 */
	@TableField(value = "submitter")
	private String submitter;

	/**
	 * 提报人工号
	 */
	@TableField(value = "submitter_id")
	private String submitterId;

	/**
	 * 旺金币被发放人账号
	 */
	@TableField(value = "grant_account")
	private String grantAccount;

	/**
	 * 旺金币被发放人姓名
	 */
	@TableField(value = "grant_name")
	private String grantName;

	/**
	 * 产品组编号
	 */
	@TableField("product_group_id")
	private String productGroupId;

	/** 
	 * 营业所 
	 */
	@TableField("branch_code")
	private String branchCode;

	@TableField("branch_name")
	private String branchName;

	/**
	 * 使用分公司旺金币(0:不使用,1:使用)
	 */
	@TableField("use_company_flag")
	private Integer useCompanyFlag;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/** 
	 * 发放类型(1:个人额度,2:分公司旺金币,3:战区旺金币,4:特殊品项补贴)
	 */
	@TableField("quota_type")
	private Integer quotaType;

	@TableField("spu_id")
	private String spuId;

	@TableField("spu_name")
	private String spuName;

	/**
	 * 实际发放陈列费用币种code(1:产品组币,2:spu币)
	 */
    @TableField("use_fee_type_code")
    private Integer useFeeTypeCode;

    /**
     * 实际发放币种子类
     */
    @TableField("use_fee_sub_type")
    private String useFeeSubType;

    /**
     * 实际发放币种子类code
     */
    @TableField("use_fee_sub_type_code")
    private String useFeeSubTypeCode;

}
