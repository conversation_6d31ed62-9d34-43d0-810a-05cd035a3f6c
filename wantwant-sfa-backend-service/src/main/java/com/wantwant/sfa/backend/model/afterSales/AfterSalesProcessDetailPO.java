package com.wantwant.sfa.backend.model.afterSales;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 售后审批流程详情
 *
 * @since 2022-11-22
 */
@Data
@TableName("sfa_after_sales_process_detail")
public class AfterSalesProcessDetailPO extends Model<AfterSalesProcessDetailPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id",type = IdType.INPUT)
	private Long id;

	/**
	* sfa_after_sales_process.id
	*/
	@TableField("process_id")
	private Integer processId;

	/**
	* 处理类型(1.区域经理审核,2.总监审核,3.客服审核)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 处理结果(1.待审核,2.审核通过,3.审核驳回,4.撤回)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	* 审核人工号
	*/
//	@TableField(value = "reviewer_id",strategy= FieldStrategy.IGNORED)
	private String reviewerId;

	/**
	* 审核人名称
	*/
//	@TableField(value = "reviewer_name",strategy= FieldStrategy.IGNORED)
	private String reviewerName;

	/**
	* 审核时间
	*/
	@TableField("reviewer_time")
	private LocalDateTime reviewerTime;

	/**
	* 审核人所属组织
	*/
//	@TableField(value = "organization_id",strategy= FieldStrategy.IGNORED)
	private String organizationId;

	/**
	* 审批内容
	*/
	@TableField("comment")
	private String comment;

	/**
	* 上次审核id
	*/
	@TableField("prev_process_id")
	private Long prevProcessId;

	/**
	* 下次审核id
	*/
	@TableField("next_process_id")
	private Long nextProcessId;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;


	@TableField(exist = false)
	private Integer employeeInfoId;

}
