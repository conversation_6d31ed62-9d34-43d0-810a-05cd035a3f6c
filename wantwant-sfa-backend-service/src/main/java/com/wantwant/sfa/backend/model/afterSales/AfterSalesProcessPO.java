package com.wantwant.sfa.backend.model.afterSales;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 售后审批流程
 *
 * @since 2022-11-22
 */
@Data
@TableName("sfa_after_sales_process")
public class AfterSalesProcessPO extends Model<AfterSalesProcessPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_after_sales_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* sfa_after_sales_process_detail.id
	*/
	@TableField("detail_id")
	private Long detailId;

	/**
	* 处理类型(1.区域经理审核,2.总监审核,3.客服审核,4.撤回)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 处理结果(1.待审核,2.审核通过,3.审核驳回,4.撤回)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	* 状态(0:启用,1:关闭)
	*/
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

}
