package com.wantwant.sfa.backend.model.attendanceTask;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_attendance_v2")
@ApiModel(value = "SfaAttendance对象", description = "打卡记录表")
public class Attendance extends Model<Attendance> {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  @TableField("employee_info_id")
  private Integer employeeInfoId;

  @TableField("calendar_date")
  private LocalDate calendarDate;

  @TableField("calendar_year")
  private Integer calendarYear;

  @TableField("calendar_month")
  private Integer calendarMonth;

  @TableField("calendar_day")
  private Integer calendarDay;

  @TableField("work_day_type")
  private Integer workDayType;

  @TableField("company")
  private String company;

  @TableField("employee_id")
  private String employeeId;

  @ApiModelProperty(value = "员工姓名")
  @TableField("employee_name")
  private String employeeName;

  @TableField("attendance_standard_time")
  private LocalDateTime attendanceStandardTime;

  @ApiModelProperty(value = "0：拜访打卡\\n1：上班打卡\\n2：下班打卡\\n3：通关打卡")
  @TableField("attendance_type")
  private Integer attendanceType;

  @ApiModelProperty(value = "0：正常 1：异常")
  @TableField("attendance_status")
  private Integer attendanceStatus;

  @ApiModelProperty(value = "1:未打卡 2迟到 3 无效打卡")
  @TableField("attendance_execption_type")
  private Integer attendanceExecptionType;

  @TableField("sign_up_pic_url")
  private String signUpPicUrl;

  @TableField("face_similar_score")
  private BigDecimal faceSimilarScore;

  @TableField("attendance_time")
  private LocalDateTime attendanceTime;

  @TableField("province")
  private String province;

  @TableField("city")
  private String city;

  @TableField("district")
  private String district;

  @TableField("street")
  private String street;

  @TableField("longitude")
  private String longitude;

  @TableField("latitude")
  private String latitude;

  @TableField("pic_url")
  private String picUrl;

  @TableField("pic_name")
  private String picName;

  @TableField("create_time")
  private LocalDateTime createTime;

  @TableField("create_person")
  private String createPerson;

  @TableField("updated_time")
  private LocalDateTime updatedTime;

  @TableField("update_person")
  private String updatePerson;

  @ApiModelProperty(value = "人脸识别照片")
  @TableField("face_url")
  private String faceUrl;

  @ApiModelProperty(value = "主管 0:未稽核 1:正常 2:异常")
  @TableField("business_audit_status")
  private Integer businessAuditStatus;

  @ApiModelProperty(value = "稽核原因 1(居家打卡) 2(代打卡)  3(电子设备打卡)")
  @TableField("business_audit_reason")
  private Integer businessAuditReason;

  @ApiModelProperty(value = "操作工号")
  @TableField("business_audit_person")
  private String businessAuditPerson;

  @ApiModelProperty(value = "操作姓名")
  @TableField("business_audit_name")
  private String businessAuditName;

  @ApiModelProperty(value = "总部主管 0:未稽核 1:正常 2:异常")
  @TableField("zb_audit_status")
  private Integer zbAuditStatus;

  @ApiModelProperty(value = "总部稽核原因 1(居家打卡) 2(代打卡)  3(电子设备打卡)")
  @TableField("zb_audit_reason")
  private Integer  zbAuditReason;

  @ApiModelProperty(value = "总部操作工号")
  @TableField("zb_audit_person")
  private String zbAuditPerson;

  @ApiModelProperty(value = "总部操作姓名")
  @TableField("zb_audit_name")
  private String zbAuditName;

  @ApiModelProperty(value = "0 手动 1是定时任务生成")
  @TableField("create_type")
  private int createType;

  @ApiModelProperty(value = "逻辑删除")
  @TableField("delete_flag")
  private int deleteFlag;

  @TableField(exist = false)
  @ApiModelProperty("是否出差")
  private Boolean isTrip;

  @TableField(exist = false)
  @ApiModelProperty("打卡地址")
  private String address;

  @Override
  protected Serializable pkVal() {
    return this.id;
  }
}
