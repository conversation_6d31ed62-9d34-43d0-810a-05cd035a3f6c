package com.wantwant.sfa.backend.model.attendanceTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CeoOrderHeader {

    private long key;
    private String code;
    private long memberKey;
    private String channel;

    @ApiModelProperty("订单状态")
    private String status;

    @ApiModelProperty("提交时间")
    private Date placedAt;

    @ApiModelProperty("处理时间(付款时间)")
    private Date processingAt;

    @ApiModelProperty("配送时间")
    private Date deliveringAt;

    @ApiModelProperty("收货时间")
    private Date receivedAt;

    @ApiModelProperty("完成时间")
    private Date completeAt;

    @ApiModelProperty("取消时间")
    private Date cancelAt;

    @ApiModelProperty("售后时间")
    private Date serviceAt;

    @ApiModelProperty("旺币交易金額")
    private BigDecimal wbTransactionAmount;

    @ApiModelProperty("旺币交易状态")
    private String wbTransactionStatus;

    @ApiModelProperty("人民币交易金額")
    private BigDecimal rmbTransactionAmount;

    @ApiModelProperty("人民币交易状态")
    private String rmbTransactionStatus;

    @ApiModelProperty("")
    private long weightTotal;
    private double deliveryFee;
    private double itemsTotal;
    private String discountDescription;
    private double discountAmount;
    private double grandTotal;
    private long itemCount;
    private long itemQuantity;
    private String receiverProvince;
    private String receiverCity;
    private String receiverDistrict;
    private String receiverStreet;

    @ApiModelProperty("收货者")
    private String receiverName;
    private long receiverGender;
    private String receiverMobileNumber;
    private long invoiceType;
    private String invoiceTitle;
    private String noteMember;
    private String noteInternal;
    private LocalDateTime updatedAt;
    private String cancelReason;
    private String serviceReason;
    private String serviceDescription;
    private long invoiceNormal;
    private String invoiceTaxId;
    private String invoiceReceiverName;
    private String invoiceReceiverMobileNumber;
    private String invoiceReceiverProvince;
    private String invoiceReceiverCity;
    private String invoiceReceiverDistrict;
    private String invoiceReceiverStreet;
    private String invoiceIssuedBank;
    private String invoiceBankAcctNumber;
    private String invoiceCompanyProvince;
    private String invoiceCompanyCity;
    private String invoiceCompanyDistrict;
    private String invoiceCompanyStreet;
    private String invoiceCompanyPhone;
    private String invitationCode;
    private long orderType;
    private String source;
    private double parent1CommissionRate;
    private double parent1CommissionAmount;
    private double parent2CommissionRate;
    private double parent2CommissionAmount;
    private double sfaauditrate;
    private double sfaauditcommissionamount;
    private double manualreceiverate;
    private double manualreceivecommissionamount;
    private double productCommissionAmount;
    private long isInvoice;
    private LocalDateTime updateInvoiceAt;
    private String couponCode;
    private String couponDiscountDescription;
    private double couponDiscountAmount;
    private String freeDescription;
    private String freeProductSku;
    private double origTotal;


    @ApiModelProperty("订单删除状态")
    private long isDelete;
    private long isReward;
    private java.sql.Timestamp rewardAt;
    private String remark;
    private long issfaauditcommissionrewarded;
    private long issfaaudited;
    private LocalDateTime sfaauditcommissionrewardedAt;


    @ApiModelProperty("0:未确认收货 1：确认收货")
    private long isManualReceived;
    private long ismanualreceivecommissionrewarded;
    private LocalDateTime manualreceivecommissionrewardedAt;
    private long isAccumulatedToVisit;
    private long isAccumulatedCancelToVisit;
    private long isAccumulatedToAttendance;
    private long isAccumulatedCancelToAttendance;
    private long isCreateDefaultStockOut;
    private LocalDateTime createDefaultStockOutAt;
    private String wxPrepayId;
    private String adid;
    private String applicationSource;
    private long shareMode;
    private long ceoKey;
    private String addressCode;
    private String addressTag;
    private long contentType;
    private long type;
    private String platform;
    private String invoiceCode;
    private String invoicetag;
    private long cancelStatus;
    private long isIncreasedPerformance;
    private LocalDateTime increasedPerformanceAt;
    private long isDecreasedPerformance;
    private LocalDateTime decreasedPerformanceAt;
    private long isAccumulatedItemDetailToItemSku;
    private double experience;
    private double parent1Experience;
    private double parent2Experience;
    private long isExperienceReward;
    private LocalDateTime experienceRewardedAt;
    private long points;
    private String pointstxnstatus;
    private long isPointRewarded;
    private LocalDateTime pointRewardedAt;
    private long pointAmount;
    private long isAccumulatedExperience;
    private long isAccumulatedPoint;
    private long isPoint;
    private double pointDeductionRmbAmount;
    private double pointDeductionRate;

}
