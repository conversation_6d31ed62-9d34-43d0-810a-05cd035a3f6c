package com.wantwant.sfa.backend.model.automaticVisitTask;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2020/9/16 14:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer_level")
@ApiModel(value = "sfa_customer_level对象", description = "客户分类等级表")
public class CustomerLevel {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("customer_id")
    private Integer customerId;

    @ApiModelProperty("等级")
    private Integer level;
}
