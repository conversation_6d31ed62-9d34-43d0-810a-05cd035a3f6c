package com.wantwant.sfa.backend.model.automaticVisitTask;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020/9/8 14:13
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_customer_location_code")
@ApiModel(value = "sfa_customer_location_code对象", description = "客户经纬度编码表")
public class LocationCode extends Model<LocationCode> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("customer_id")
    private Integer customerId;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("地理位置编码")
    @TableField("location_code")
    private String locationCode;

}
