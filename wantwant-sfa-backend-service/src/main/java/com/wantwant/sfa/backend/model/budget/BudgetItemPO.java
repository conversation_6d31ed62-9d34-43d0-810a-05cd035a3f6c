package com.wantwant.sfa.backend.model.budget;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预算项目
 *
 * @since 2023-02-27
 */
@Data
@TableName("sfa_budget_item")
public class BudgetItemPO extends Model<BudgetItemPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 项目编号
	*/
	@TableField("item_code")
	private String itemCode;

	/**
	* 项目名称
	*/
	@TableField("item_name")
	private String itemName;

	/** 
	 * 项目组成 
	 */
	@TableField("item_design")
	private String itemDesign;

	/**
	* 项目备注
	*/
	@TableField("item_comment")
	private String itemComment;

	/**
	 * 项目地址
	 */
	@TableField("item_address")
	private String itemAddress;

	/**
	 * 项目标识  项目标识(仓储(1.仓库运费；2.仓库费用))
	 */
	@TableField("item_type")
	private int itemType;


	/** 
	 * 级别(1:一级,2:二级) 
	 */
	@TableField("level")
	private Integer level;

	/** 
	 * 父id
	 */
	@TableField("parent_id")
	private Integer parentId;

	/** 
	 * 背景色
	 */
	@TableField("bg_color")
	private String bgColor;

	/** 
	 * 缩进数量
	 */
	@TableField("tab")
	private Integer tab;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/** 
	 * 创建人
	 */
	@TableField("created_by")
	private String createdBy;

	/** 
	 * 修改人
	 */
	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;


}
