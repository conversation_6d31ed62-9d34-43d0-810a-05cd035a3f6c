package com.wantwant.sfa.backend.model.budget;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 预算总览
 *
 * @since 2023-02-27
 */
@Data
@TableName("sfa_budget_overview")
public class BudgetOverviewPO extends Model<BudgetOverviewPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 时间范围(0:月,1:年)
	*/
	@TableField("range")
	private Integer range;

	/**
	* 项目编号
	*/
	@TableField("item_code")
	private String itemCode;

	/**
	* 预计金额
	*/
	@TableField("estimate_amount")
	private BigDecimal estimateAmount;

	/**
	* 预计占比
	*/
	@TableField("estimate_rate")
	private BigDecimal estimateRate;

	/**
	* 达成金额
	*/
	@TableField("achieved_amount")
	private BigDecimal achievedAmount;

	/**
	* 达成占比
	*/
	@TableField("achieved_rate")
	private BigDecimal achievedRate;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	@TableField("created_by")
	private String createdBy;

	/**
	 * 修改人
	 */
	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
