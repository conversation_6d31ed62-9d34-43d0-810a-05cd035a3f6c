package com.wantwant.sfa.backend.model.businessTrip;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 出差申请费用明细
 *
 * @since 2023-09-13
 */
@Data
@TableName("sfa_business_trip_cost")
public class BusinessTripCostPO extends Model<BusinessTripCostPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "cost_id")
	private Integer costId;

	/**
	* sfa_business_trip.trip_id
	*/
	@TableField("trip_id")
	private Integer tripId;

	/**
	* 费用类型
	*/
	@TableField("cost_type")
	private String costType;

	/**
	* 费用类型子项
	*/
	@TableField("cost_item_type")
	private String costItemType;

	/**
	* sfa_business_trip_itinerary.itinerary_id
	*/
	@TableField("itinerary_id")
	private Integer itineraryId;

	/**
	* 行程
	*/
	@TableField("itinerary")
	private String itinerary;

	/**
	* 金额
	*/
	@TableField("amount")
	private BigDecimal amount;

	/**
	* 备注
	*/
	@TableField("remark")
	private String remark;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
