package com.wantwant.sfa.backend.model.businessTrip;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 出差申请审批
 *
 * @since 2023-09-13
 */
@Data
@TableName("sfa_business_trip_file")
public class BusinessTripFilePO extends Model<BusinessTripFilePO> {
	private static final long serialVersionUID = 1L;

	@TableId("id")
	private Integer id;

	/**
	* 外键
	*/
	@TableField("w_id")
	private Integer wId;

	/**
	* 类型(1:出差单,2:申请单)
	*/
	@TableField("type")
	private Integer type;

	@TableField("name")
	private String name;

	@TableField("url")
	private String url;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
