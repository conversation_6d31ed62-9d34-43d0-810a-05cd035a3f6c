package com.wantwant.sfa.backend.model.businessTrip;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 出差申请行程明细
 *
 * @since 2023-09-13
 */
@Data
@TableName("sfa_business_trip_itinerary")
public class BusinessTripItineraryPO extends Model<BusinessTripItineraryPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "itinerary_id")
	private Integer itineraryId;

	/**
	* sfa_business_trip.trip_id
	*/
	@TableField("trip_id")
	private Integer tripId;

	/**
	* 交通类型
	*/
	@TableField("traffic_type")
	private String trafficType;

	/**
	* 出发城市
	*/
	@TableField("departure_city")
	private String departureCity;

	/**
	* 到达城市
	*/
	@TableField("arrival_city")
	private String arrivalCity;

	/**
	* 出发日期
	*/
	@TableField("departure_date")
	private LocalDate departureDate;

	/**
	* 备注
	*/
	@TableField("remark")
	private String remark;

	/**
	* 第几天
	*/
	@TableField("day")
	private Integer day;

	/**
	* 费用类型(0:交通,1:酒店)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 酒店名称及地点
	*/
	@TableField("consumer")
	private String consumer;

	/**
	* 入住城市
	*/
	@TableField("staying_city")
	private String stayingCity;

	/**
	* 住宿标准
	*/
	@TableField("standard")
	private String standard;


	/**
	* 入住日期
	*/
	@TableField("staying_date")
	private LocalDate stayingDate;

	/**
	* 退房日期
	*/
	@TableField("out_date")
	private LocalDate outDate;

	/**
	* 入住第几天
	*/
	@TableField("staying_day")
	private Integer stayingDay;

	/**
	* 退房第几天
	*/
	@TableField("departure_day")
	private Integer departureDay;

	/**
	* 几晚
	*/
	@TableField("nights")
	private Integer nights;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
