package com.wantwant.sfa.backend.model.calendar;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_calendar_config")
public class CanlendarConfigPO extends Model<CanlendarConfigPO> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("role_id")
    private Integer roleId;

    @TableField("title")
    private String title;

    @TableField("content")
    private String content;

    @TableField("type")
    private Integer type;

    @TableField("start_date")
    private LocalDateTime startDate;

    @TableField("end_date")
    private LocalDateTime endDate;

    @TableField("date_desc")
    private String dateDesc;

    @TableField("resource_id")
    private Integer resourceId;

    @TableField("extra_employee_ids")
    private String extraEmployeeIds;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("create_employee_id")
    private String createEmployeeId;

    @TableField("create_employee_name")
    private String createEmployeeName;

    @TableField("delete_flag")
    private int deleteFlag;

    @TableField("business_group")
    private Integer businessGroup;
}
