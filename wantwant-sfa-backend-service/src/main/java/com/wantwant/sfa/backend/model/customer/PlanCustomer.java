package com.wantwant.sfa.backend.model.customer;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PlanCustomer {
    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");

    @ApiModelProperty("客户编号")
    private String customerId;  
    
    @ApiModelProperty("冻结状态 0:未冻结 1:冻结")
    private Integer isFrozen;
    
    @ApiModelProperty("客户经纬度")
    private List<String> location;  
    
    @ApiModelProperty("门店名称")
    private String storeName;  

    @ApiModelProperty("学校名称")
    private String schoolName;  

    @ApiModelProperty("省")
    private String province;  

    @ApiModelProperty("市")
    private String city;  

    @ApiModelProperty("区")
    private String district;  

    @ApiModelProperty("街道")
    private String street;  

    
    @ApiModelProperty("客户类型:1：学代,2：终端,3：旺粉,4：特通,5：经销商,6：二批")
    private String customerType;
    
    @ApiModelProperty("客户子类型:特通4--1  特通--餐饮 4--2 特通--KTV 4--3 特通--其他 4–4 特通—网吧 4–5 特通—健身所  4–6 特通—企业")
    private String customerSubtype;  
    
    @ApiModelProperty("客户状态:1正式,0潜在")
    private String potential;  
    
    @ApiModelProperty("提交状态 '0是不可重复提交,1可重复提交'")
    private String isRepetition;   
    
    @ApiModelProperty("审核状态 0 未审核,1 审核通过,2 驳回'")
    private String isVerified;   
    
    @ApiModelProperty("复购:1一月未复购，2从未下过单,0其他")
    private String order;  
    
    @ApiModelProperty("拜访:1一周未拜访，2一月未拜访,0其他")
    private String visit;  
    
    @ApiModelProperty("满意:1满意，2一般,3差，0差")
    private String desire;  
    
    @ApiModelProperty("经度")
    private String longitude;
    
    @ApiModelProperty("纬度")
    private String latitude;
    
    private String index;  
    
    private String memberKey;  
    
    private String processingLastAt;  
    
    private String visitLastTime;  
    
    private Integer intentionLevel;  
    
    private String isPlan;

    public String getOrder() {
    	//潜在客户则复购
    	if(!"1".equals(potential)  ) {
    		return "0";
    	}
    	if(StringUtils.isBlank(processingLastAt)) {
    		return "2";
    	}
    	LocalDate date = LocalDate.parse(processingLastAt,fmt);
    	//相隔天数
    	long day = date.until(LocalDate.now(), ChronoUnit.DAYS);
    	return day > 30 ? "1" : "0";
    }
    
    public String getDesire() {
    	//已有客户则满意度为0
    	if(!"0".equals(potential) || intentionLevel == null ) {
    		return "0";
    	}
    	return intentionLevel >= 4 ? "1" : intentionLevel >= 2 ? "2" : intentionLevel == 1 ? "3" : "0";
    }
    
    public String getVisit() {
    	
    	if(StringUtils.isBlank(visitLastTime)) {
    		return "0";
    	}
    	LocalDate date = LocalDate.parse(visitLastTime,fmt);
		this.visitLastTime = date.toString();
    	//相隔天数
    	long day = date.until(LocalDate.now(), ChronoUnit.DAYS);
    	return day > 30 ? "2" : day > 7 ? "1" : "0";
    }
    
    public List<String> getLocation() {
    	location = new ArrayList<String>();
    	
    	if(StringUtils.isNotBlank(longitude) && StringUtils.isNotBlank(latitude)) {
    		location.add(longitude);
    		location.add(latitude);
    	}
    		
    	return location;
    }
    
    public String getIndex() {
    	return "0";
    }
    

        
}
