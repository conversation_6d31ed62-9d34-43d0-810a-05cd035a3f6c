package com.wantwant.sfa.backend.model.customerTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TaskCustomer {

    @ApiModelProperty("客户id")
    private int customerId;

    @ApiModelProperty("业务员id")
    private String memberId;

    @ApiModelProperty("0：潜在 1：正式")
    private int isPotential;

    @ApiModelProperty("memberKey")
    private Integer memberKey;

    @ApiModelProperty("开发时间")
    private LocalDateTime requestTime;

    @ApiModelProperty("审核状态 0：未审核 1：审核通过 2：驳回")
    private int isVerified;

    List<TaskCustomerOrder> orderList;

}
