package com.wantwant.sfa.backend.model.customerTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TaskCustomerOrder {

    @ApiModelProperty("订单号")
    private String key;

    @ApiModelProperty("订单编号")
    private String code;

    @ApiModelProperty("memberKey")
    private Integer memberKey;

    @ApiModelProperty("订单状态")
    private String status;

    @ApiModelProperty("完成时间")
    private Date completeAt;

    @ApiModelProperty("付款时间")
    private Date processingAt;

    @ApiModelProperty("取消时间")
    private Date cancelAt;

    @ApiModelProperty("人民币交易金額")
    private BigDecimal rmbTransactionAmount;

    @ApiModelProperty("人民币交易状态")
    private String rmbTransactionStatus;
}
