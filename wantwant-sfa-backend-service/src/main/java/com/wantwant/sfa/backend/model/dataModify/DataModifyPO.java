package com.wantwant.sfa.backend.model.dataModify;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资料修改信息
 *
 * @since 2022-06-08
 */
@Data
@TableName("sfa_data_modify")
public class DataModifyPO extends Model<DataModifyPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	 * 申请编号
	 */
	@TableField("application_no")
	private String applicationNo;

	/**
	* 业务memberKey
	*/
	@TableField("member_key")
	private String memberKey;

	/**
	* 客户ID/合伙人memberKey
	*/
	@TableField("user_id")
	private String userId;

	/**
	* 客户/合伙人名称
	*/
	@TableField("user_name")
	private String userName;

	/**
	* 客户/合伙人手机号
	*/
	@TableField("user_phone")
	private String userPhone;

	/**
	* 修改属性
	*/
	@TableField("modify_attribute")
	private String modifyAttribute;

	/**
	* 修改前属性
	*/
	@TableField("before_attribute")
	private String beforeAttribute;

	/**
	* 修改后属性
	*/
	@TableField("after_attribute")
	private String afterAttribute;

	/**
	* 修改原因
	*/
	@TableField("modify_reason")
	private String modifyReason;

	/**
	* 图片地址
	*/
	@TableField("pic_url")
	private String picUrl;

	/**
	* 修改类型(1:客户,2:BD,3:合伙人)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 分类(1:意向客户,2:BD,3:企业合伙人,4:全职合伙人,5:兼职合伙人,6:区域总监)
	*/
	@TableField("subclass")
	private String subclass;

	/**
	 * 组别
	 */
	@TableField("business_group")
	private String businessGroup;

	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField("update_time")
	private LocalDateTime updateTime;


	private String organizationId;
}
