package com.wantwant.sfa.backend.model.dataModify;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资料修改审批流程详情
 *
 * @since 2022-06-08
 */
@Data
@TableName("sfa_modify_process_detail")
public class ModifyProcessDetailPO extends Model<ModifyProcessDetailPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id",type = IdType.INPUT)
	private Long id;

	/**
	* sfa_modify_process.id
	*/
	@TableField("process_id")
	private Integer processId;

	/**
	* 处理类型(1.总监审核,2.大区审核,3.运营审核)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 处理结果(1.待审核,2.审核通过,3.审核驳回)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	* 审核人工号
	*/
//	@TableField(value = "reviewer_id",strategy= FieldStrategy.IGNORED)
	private String reviewerId;

	/**
	* 审核人名称
	*/
//	@TableField(value = "reviewer_name",strategy= FieldStrategy.IGNORED)
	private String reviewerName;

	/**
	* 审核时间
	*/
	@TableField("reviewer_time")
	private LocalDateTime reviewerTime;

	/**
	* 审核人所属组织
	*/
//	@TableField(value = "organization_id",strategy= FieldStrategy.IGNORED)
	private String organizationId;

	/**
	* 审批内容
	*/
	@TableField("comment")
	private String comment;

	/**
	* 上次审核id
	*/
	@TableField("prev_process_id")
	private Long prevProcessId;

	/**
	* 下次审核id
	*/
	@TableField("next_process_id")
	private Long nextProcessId;

	/**
	* 创建时间
	*/
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;


	@TableField(exist = false)
	private Integer employeeInfoId;

}
