package com.wantwant.sfa.backend.model.dataModify;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资料修改审批流程
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Data
@TableName("sfa_modify_process")
public class ModifyProcessPO extends Model<ModifyProcessPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_data_modify.id
	*/
	@TableField("application_no")
	private String applicationNo;

	/**
	* sfa_display_process_detail.id
	*/
	@TableField("detail_id")
	private Long detailId;

	/**
	* 处理类型(1.总监审核,2.大区审核,3.运营审核)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 处理结果(1.待审核,2.审核通过,3.审核驳回)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	* 状态(0:启用,1:关闭)
	*/
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

}
