package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 特陈明细历史
 *
 * @since 2022-09-19
 */
@Data
@TableName("sfa_display_detail_history")
public class DisplayDetailHistoryPO extends Model<DisplayDetailHistoryPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_display_process_detail.id
	*/
	@TableField("detail_id")
	private Long detailId;

	/** 
	 * sfa_display_info.id
	 */
	@TableField("info_id")
	private Integer infoId;

	/**
	* 产品组
	*/
	@TableField("production_group")
	private String productionGroup;

	/**
	* 要求排面数
	*/
	@TableField("required_quantity")
	private String requiredQuantity;

	/**
	* 陈列具体要求
	*/
	@TableField("specific_requirements")
	private String specificRequirements;

	/**
	* 陈列位置要求
	*/
	@TableField("location_requirements")
	private String locationRequirements;

	/**
	* 陈列最高费用
	*/
	@TableField("max_fee")
	private BigDecimal maxFee;

	/**
	* 陈列标准
	*/
	@TableField("standard")
	private String standard;

	/**
	* 申请陈列费用
	*/
	@TableField("apply_quota")
	private BigDecimal applyQuota;

	/**
	 * 区域经理建议额度
	 */
	@TableField("dept_item_quota")
	private BigDecimal deptItemQuota;

	/**
	* 陈列形式
	*/
	@TableField("display_form")
	private String displayForm;

	/**
	* 陈列示例图片
	*/
	@TableField("sample_picture_url")
	private String samplePictureUrl;

	/**
	* 陈列近景图片
	*/
	@TableField("near_picture_url")
	private String nearPictureUrl;

	/**
	* 陈列远景图片
	*/
	@TableField("far_picture_url")
	private String farPictureUrl;

	/**
	 * 区域经理-陈列近景图片
	 */
	@TableField("department_near_picture_url")
	private String departmentNearPictureUrl;

	/**
	 * 区域经理-陈列远景图片
	 */
	@TableField("department_far_picture_url")
	private String departmentFarPictureUrl;

	/**
	 * 现场稽核-陈列近景图片
	 */
	@TableField("check_near_picture_url")
	private String checkNearPictureUrl;

	/**
	 * 现场稽核-陈列远景图片
	 */
	@TableField("check_far_picture_url")
	private String checkFarPictureUrl;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 合伙人异常(0:未稽核,1:正常,2:异常)
	 */
	@TableField("partner_anomaly")
	private Integer partnerAnomaly;

	/**
	 * 区域经理异常(0:未稽核,1:正常,2:异常)
	 */
	@TableField("department_anomaly")
	private Integer departmentAnomaly;

	/**
	 * 合伙人异常原因
	 */
	@TableField("partner_anomaly_reason")
	private String partnerAnomalyReason;

	/**
	 * 区域经理异常原因
	 */
	@TableField("department_anomaly_reason")
	private String departmentAnomalyReason;


	/**
	 * 实际发放币种子类
	 */
	@TableField("use_fee_sub_type")
	private String useFeeSubType;

	/**
	 * 实际发放陈列费用币种code(0:通用币,1:产品组币,2:spu币)
	 */
	@TableField("use_fee_type_code")
	private Integer useFeeTypeCode;

	/**
	 * 实际发放币种子类code
	 */
	@TableField("use_fee_sub_type_code")
	private String useFeeSubTypeCode;


}
