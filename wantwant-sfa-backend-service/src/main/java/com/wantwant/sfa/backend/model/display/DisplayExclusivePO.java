package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 特陈专属助陈品
 *
 * @since 2023-05-09
 */
@Data
@TableName("sfa_display_exclusive")
public class DisplayExclusivePO extends Model<DisplayExclusivePO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_display_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* sfa_display_detail.id
	*/
	@TableField("d_id")
	private Integer dId;

	/**
	* 活动名称
	*/
	@TableField("name")
	private String name;

	/**
	* 图片地址
	*/
	@TableField("pic")
	private String pic;

	@TableField("sku")
	private String sku;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
