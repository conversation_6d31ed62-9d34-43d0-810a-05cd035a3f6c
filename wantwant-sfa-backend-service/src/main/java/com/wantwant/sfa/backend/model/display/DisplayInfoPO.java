package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 特陈信息
 *
 * @since 2022-05-18
 */
@Data
@TableName("sfa_display_info")
public class DisplayInfoPO extends Model<DisplayInfoPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 申请编号
	*/
	@TableField("application_no")
	private String applicationNo;

	/**
	* 客户编号
	*/
	@TableField("customer_id")
	private String customerId;

	/**
	* 客户姓名
	*/
	@TableField("customer_name")
	private String customerName;

	/**
	 * 陈列客户编号
	 */
	@TableField("display_customer_no")
	private String displayCustomerNo;

	/**
	 * 陈列客户姓名
	 */
	@TableField("display_customer_name")
	private String displayCustomerName;

	/**
	 * 陈列客户类型
	 */
	@TableField("display_customer_type")
	private Integer displayCustomerType;

	/**
	* 营业执照名称
	*/
	@TableField("listed_person_name")
	private String listedPersonName;

	/**
	 * 营业执照照片
	 */
	@TableField("license_image_url")
	private String licenseImageUrl;

	/**
	* 陈列客户电话
	*/
	@TableField("listed_person_phone")
	private String listedPersonPhone;

	/**
	 * 门店名称
	 */
	@TableField("store_name")
	private String storeName;

	/**
	 * 门店地址
	 */
	@TableField("store_address")
	private String storeAddress;

	/**
	 * 门店面积
	 */
	@TableField("store_area")
	private String storeArea;

	/**
	 * 门店照片
	 */
	@TableField("store_image_url")
	private String storeImageUrl;

	/**
	 * 门店头照
	 */
	@TableField("store_head_image_url")
	private String storeHeadImageUrl;

	/**
	 * 到店门头照
	 */
	@TableField("arrival_store_head_image_url")
	private String arrivalStoreHeadImageUrl;

	/**
	* 陈列形式
	*/
	@TableField("display_form")
	private String displayForm;

	/**
	* 所属业务memberKey
	*/
	@TableField("partner_member_key")
	private Integer partnerMemberKey;

	/**
	* 核准费用
	*/
	@TableField("quota")
	private BigDecimal quota;

	/**
	 * 区域经理建议额度
	 */
	@TableField("dept_quota")
	private BigDecimal deptQuota;

	/**
	* 客服驳回次数
	*/
	@TableField("reject_number")
	private Integer rejectNumber;

	/**
	 * 最近一次驳回人员
	 */
	@TableField("reject_name")
	private String rejectName;

	/**
	 * 最近一次驳回原因
	 */
	@TableField("reject_reason")
	private String rejectReason;

	/**
	* 示例图片(废弃)
	*/
	@TableField("sample_picture_url")
	private String samplePictureUrl;

	/**
	* 陈列近景图片(废弃)
	*/
	@TableField("near_picture_url")
	private String nearPictureUrl;

	/**
	* 陈列远景图片(废弃)
	*/
	@TableField("far_picture_url")
	private String farPictureUrl;

	/*20221207-7.2.0-杨志慧 start*/

	/**
	 * 是否发放(0:未发放,1:审批通过待发放,2:发放成功,3:发放失败)
	 */
	@TableField("is_grant")
	private Integer isGrant;

	/**
	 * 发放失败原因
	 */
	@TableField(value = "grant_failure_reason",strategy = FieldStrategy.IGNORED)
	private String grantFailureReason;

	/**
	 * 未到营运稽核记为未稽核
	 * 营运审核通过为正常
	 * 营运标记异常记为异常
	 * 营运驳回记为未稽核
	 *
	 * 营运稽核状态(0:未稽核,1:正常,2:异常)
	 */
	@TableField("audit_status")
	private Integer auditStatus;

	/** 
	 * 营运稽核时间
	 */
	@TableField(value = "audit_time",strategy = FieldStrategy.IGNORED)
	private LocalDateTime auditTime;

	/** 
	 * 合伙人异常(0:正常,1:异常)
	 */
	@TableField(value = "partner_anomaly",strategy = FieldStrategy.IGNORED)
	private Integer partnerAnomaly;

	/** 
	 * 区域经理异常(0:正常,1:异常)
	 */
	@TableField(value = "department_anomaly",strategy = FieldStrategy.IGNORED)
	private Integer departmentAnomaly;

	/** 
	 * 异常原因
	 */
	@TableField(value = "anomaly_reason",strategy = FieldStrategy.IGNORED)
	private String anomalyReason;

	/**
	 * 经度
	 */
	@TableField("longitude")
	private String longitude;

	/**
	 * 纬度
	 */
	@TableField("latitude")
	private String latitude;

	/**
	 * 区域经理-上传合照
	 */
	@TableField(value = "department_image_url",strategy = FieldStrategy.IGNORED)
	private String departmentImageUrl;

	/**
	 * 区域经理-通关密码
	 */
	@TableField(value = "pass_code",strategy = FieldStrategy.IGNORED)
	private String passCode;

	/*20221207-7.2.0-杨志慧 end*/

	/** 
	 * 现场稽核-上传合照
	 */
	@TableField(value = "check_image_url")
	private String checkImageUrl;

	/** 
	 * 现场稽核状态(0:未稽核,1:正常,2:异常)
	 */
	@TableField("check_status")
	private Integer checkStatus;

	/**
	 * 现场稽核人员
	 */
	@TableField("check_reviewer")
	private String checkReviewer;

	/** 
	 * 现场稽核时间
	 */
	@TableField("check_time")
	private LocalDateTime checkTime;

	/** 
	 * 城市等级
	 */
	@TableField("city_level")
	private String cityLevel;

	/** 
	 * 现场稽核原因 
	 */
	@TableField("check_failure_reason")
	private String checkFailureReason;

	/**
	 * 产品组编号
	 */
	@TableField("product_group_id")
	private String productGroupId;

	/**
	 * 营业所
	 */
	@TableField("branch_code")
	private String branchCode;

	@TableField("branch_name")
	private String branchName;

	/**
	* 创建时间
	*/
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 申请类型(0:合伙人申请,1:区域经理申请,2:区域总监申请,3:业务BD申请)
	 */
	@TableField("apply_type")
	private Integer applyType;

	/**
	 * 代申请人memberKey
	 */
	@TableField("apply_member_key")
	private Integer applyMemberKey;

	/** 
	 * 发放类型
	 * 1:特陈额度
	 * 2:分公司旺金币
	 * 3:战区旺金币
	 */
	@TableField("quota_type")
	private Integer quotaType;

	/**
	 * 活动规则(1:有规则,2:无规则)
	 */
	@TableField("rule_type")
	private Integer ruleType;

	/**
	 * 组织code
	 */
	private String organizationId;
	/**
	 * 组织类型
	 */
	private String organizationType;
	/**
	 * 组织名称
	 */
	private String organizationName;

	@ApiModelProperty(value = "陈列客户渠道类型  1-直营渠道，2-传统渠道")
	private Integer channelType;

	@ApiModelProperty(value = "陈列客户门店类型  1-社区、2-校园、3-餐饮、4-写字楼/商圈、5-交通站点、6-休闲场所、7-标超、8-量贩、9-零食系统")
	private Integer salesroomType;

	@ApiModelProperty(value = "费用签收单图片")
	private String expenseReceiptImages;

	@ApiModelProperty(value = "协议开始时间")
	private LocalDate displayAgreementStartDate;

	@ApiModelProperty(value = "协议结束时间")
	private LocalDate displayAgreementEndDate;


}
