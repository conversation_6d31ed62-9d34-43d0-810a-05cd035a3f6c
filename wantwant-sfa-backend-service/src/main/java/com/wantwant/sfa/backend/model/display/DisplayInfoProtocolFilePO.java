package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 特称协议文件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@TableName("sfa_display_info_protocol_file")
@ApiModel(value = "SfaDisplayInfoProtocolFile对象", description = "特称协议文件")
@Data
public class DisplayInfoProtocolFilePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "file_id", type = IdType.AUTO)
    private Long fileId;

    @ApiModelProperty("类型(1.图片 2.文件)")
    private Integer type;

    @ApiModelProperty("文件url")
    private String url;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("sfa_display_info主键")
    private Integer infoId;

    @ApiModelProperty("是否删除(1.是)")
    @TableLogic(value = "0",delval = "1")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
