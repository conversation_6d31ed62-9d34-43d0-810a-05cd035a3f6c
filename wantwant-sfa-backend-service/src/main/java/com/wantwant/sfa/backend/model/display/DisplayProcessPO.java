package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 特陈审批流程
 *
 * @since 2022-05-18
 */
@Data
@TableName("sfa_display_process")
public class DisplayProcessPO extends Model<DisplayProcessPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_display_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* sfa_display_process_detail.id
	*/
	@TableField("detail_id")
	private Long detailId;

	/**
	* 处理类型(1.总监审核,2.督导审核,3.客服审核,4.业务支持审核,5.取消申请)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 处理结果(1.待审核,2.审核通过,3.审核驳回,4.关闭)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	 * 状态(0:启用,1:关闭)
	 */
	@TableField("state")
	private Integer state;

	/**
	* 区域经理建议额度
	*/
	@TableField("quota")
	private BigDecimal quota;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

}
