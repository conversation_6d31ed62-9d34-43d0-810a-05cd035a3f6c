package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 特陈规则活动形式明细
 *
 * @since 2023-05-05
 */
@Data
@TableName("sfa_display_rule_detail")
public class DisplayRuleDetailPO extends Model<DisplayRuleDetailPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id",type = IdType.INPUT)
	private Long id;

	/** 
	 * sfa_display_rule.id
	 */
	@TableField("r_id")
	private Long rId;

	/**
	* 活动编号(act_display_activity.id)
	*/
	@TableField("act_id")
	private Integer actId;

	/** 
	 * 活动明细编号(act_display_activity_detail.id)
	 */
	@TableField("act_detail_id")
	private Integer actDetailId;

	/**
	* 陈列形式
	*/
	@TableField("display_type")
	private String displayType;

	/**
	 * 产品组编号
	 */
	@TableField("product_group_id")
	private String productGroupId;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0",delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 陈列费用币种
	 */
	@TableField("fee_type")
	private String feeType;

	/**
	 * 陈列费用币种code
	 * 0-通用币；1-产品组币 ；2-spu币
	 */
	@TableField("fee_type_code")
	private Integer feeTypeCode;

	/**
	 * 币种子类
	 */
	@TableField("fee_sub_type")
	private String feeSubType;

	/**
	 * 币种子类code
	 */
	@TableField("fee_sub_type_code")
	private String feeSubTypeCode;

}
