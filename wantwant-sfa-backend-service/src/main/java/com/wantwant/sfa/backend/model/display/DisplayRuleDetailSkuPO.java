package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 特陈规则活动sku明细
 *
 * @since 2023-05-05
 */
@Data
@TableName("sfa_display_rule_detail_sku")
public class DisplayRuleDetailSkuPO extends Model<DisplayRuleDetailSkuPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	 * sfa_display_rule.id
	 */
	@TableField("r_id")
	private Long rId;

	/** 
	 * sfa_display_rule_detail.id
	 */
	@TableField("d_id")
	private Long dId;

	/**
	* 活动编号
	*/
	@TableField("act_id")
	private Integer actId;

	/**
	* 活动形式明细ID
	*/
	@TableField("act_detail_id")
	private Integer actDetailId;

	/**
	* sku
	*/
	@TableField("sku")
	private String sku;

	/**
	* sku名称
	*/
	@TableField("sku_name")
	private String skuName;

	/**
	* 口味
	*/
	@TableField("flavour")
	private String flavour;

	/**
	* 规则
	*/
	@TableField("spec")
	private String spec;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0",delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
