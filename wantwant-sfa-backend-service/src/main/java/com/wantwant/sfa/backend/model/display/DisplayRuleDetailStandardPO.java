package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 特陈规则陈列标准明细
 *
 * @since 2023-05-05
 */
@Data
@TableName("sfa_display_rule_detail_standard")
public class DisplayRuleDetailStandardPO extends Model<DisplayRuleDetailStandardPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	 * sfa_display_rule.id
	 */
	@TableField("r_id")
	private Long rId;

	/**
	 * sfa_display_rule_detail.id
	 */
	@TableField("d_id")
	private Long dId;

	/**
	* 活动编号
	*/
	@TableField("act_id")
	private Integer actId;

	/**
	* 活动形式明细ID
	*/
	@TableField("act_detail_id")
	private Integer actDetailId;

	/**
	* 陈列标准(0:小型,1:中型,2:大型)
	*/
	@TableField("standard_type")
	private Integer standardType;

	/**
	* 陈列费用(一线城市)
	*/
	@TableField("display_fee")
	private BigDecimal displayFee;

	/**
	* 陈列费用(二线城市)
	*/
	@TableField("display_fee_two")
	private BigDecimal displayFeeTwo;

	/**
	* 陈列费用(三线城市)
	*/
	@TableField("display_fee_three")
	private BigDecimal displayFeeThree;

	/**
	* 修改陈列费用(一线城市)
	*/
	@TableField("modify_display_fee")
	private BigDecimal modifyDisplayFee;

	/**
	* 修改陈列费用(二线城市)
	*/
	@TableField("modify_display_fee_two")
	private BigDecimal modifyDisplayFeeTwo;

	/**
	* 修改陈列费用(三线城市)
	*/
	@TableField("modify_display_fee_three")
	private BigDecimal modifyDisplayFeeThree;

	/**
	* 陈列具体要求
	*/
	@TableField("display_require")
	private String displayRequire;

	/**
	* 陈列位置
	*/
	@TableField("display_position")
	private String displayPosition;

	/**
	* 陈列排面数
	*/
	@TableField("display_rows")
	private String displayRows;

	/** 
	 * 状态(1:禁用)
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0",delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
