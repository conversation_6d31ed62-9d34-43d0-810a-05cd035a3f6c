package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 特陈规则信息
 *
 * @since 2023-05-05
 */
@Data
@TableName("sfa_display_rule")
public class DisplayRulePO extends Model<DisplayRulePO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id",type = IdType.INPUT)
	private Long id;

	/**
	* 活动编号
	*/
	@TableField("act_id")
	private Integer actId;

	/**
	* 组织ID
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 活动名称
	*/
	@TableField("display_name")
	private String displayName;

	/**
	* 活动开始时间
	*/
	@TableField("start_time")
	private LocalDateTime startTime;

	/**
	* 活动结束时间
	*/
	@TableField("end_time")
	private LocalDateTime endTime;

	/**
	* 活动规则
	*/
	@TableField("act_rule")
	private String actRule;

	/**
	* 状态(0:未申请,1:审核中,2:通过,3:驳回)
	*/
	@TableField("status")
	private Integer status;

	/**
	* 申请人工号
	*/
	@TableField("apply_id")
	private String applyId;

	/**
	* 申请人名称
	*/
	@TableField("apply_name")
	private String applyName;

	/**
	* 申请时间
	*/
	@TableField("apply_time")
	private LocalDateTime applyTime;

	/**
	* 审核人工号
	*/
	@TableField("reviewer_id")
	private String reviewerId;

	/**
	* 审核人名称
	*/
	@TableField("reviewer_name")
	private String reviewerName;

	/**
	* 审核时间
	*/
	@TableField(value = "reviewer_time",strategy = FieldStrategy.IGNORED)
	private LocalDateTime reviewerTime;

	/**
	* 审核人所属组织
	*/
	@TableField("reviewer_organization_id")
	private String reviewerOrganizationId;

	/**
	* 审批意见/备注
	*/
	@TableField(value = "comment",strategy = FieldStrategy.IGNORED)
	private String comment;

	/**
	* 活动渠道
	*/
	@TableField("channel_id")
	private String channelId;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0",delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
