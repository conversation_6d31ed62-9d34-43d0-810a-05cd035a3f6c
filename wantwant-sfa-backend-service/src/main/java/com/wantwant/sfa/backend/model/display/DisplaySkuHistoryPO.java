package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 特陈sku信息历史
 *
 * @since 2022-07-19
 */
@Data
@TableName("sfa_display_sku_history")
public class DisplaySkuHistoryPO extends Model<DisplaySkuHistoryPO> {

	private static final long serialVersionUID = 3276449265788017816L;

	@TableId(value = "id")
	private Integer id;

	/** 
	 * sfa_display_process_detail.id
	 */
	@TableId(value = "detail_id")
	private Long detailId;

	/**
	 * sfa_display_info.id
	 */
	@TableField("info_id")
	private Integer infoId;

	/**
	 * sfa_display_detail.id
	 */
	@TableField("d_id")
	private Integer dId;

	/**
	* 陈列形式
	*/
	@TableField("display_form")
	private String displayForm;

	/**
	* 陈列近景图片
	*/
	@TableField("near_picture_url")
	private String nearPictureUrl;

	/**
	* 陈列远景图片
	*/
	@TableField("far_picture_url")
	private String farPictureUrl;

	/**
	* sku编码
	*/
	@TableField("sku")
	private String sku;

	/**
	 * 商品名称
	 */
	@TableField("sku_name")
	private String skuName;

	/**
	* 生产线名称
	*/
	@TableField("line_name")
	private String lineName;

	/**
	* 口味
	*/
	@TableField("flavour")
	private String flavour;

	/**
	* 箱规
	*/
	@TableField("pack_spec")
	private String packSpec;

	/**
	* 商品图片
	*/
	@TableField("sku_image_url")
	private String skuImageUrl;

	/** 
	 * 纯奶费用:按照修改额度/提报带有纯牛奶陈列组的sku数，平摊计算纯牛奶的额度
	 */
	@TableField("milk_quota")
	private BigDecimal milkQuota;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0",delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * spu编号
	 */
	@TableField("spu_id")
	private String spuId;

	/**
	 * spu名称
	 */
	@TableField("spu_name")
	private String spuName;

}
