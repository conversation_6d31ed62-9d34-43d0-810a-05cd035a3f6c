package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 特称预警值设定
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-23
 */
@TableName("sfa_display_warning_config")
@ApiModel(value = "SfaDisplayWarningConfig对象", description = "特称预警值设定")
@Data
public class DisplayWarningConfigPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "config_id", type = IdType.AUTO)
    private Long configId;

    @ApiModelProperty("预警比例")
    private BigDecimal rate;

    @ApiModelProperty("产品组")
    private Integer businessGroup;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("删除标志(1.是)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}
