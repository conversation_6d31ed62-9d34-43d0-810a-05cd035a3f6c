package com.wantwant.sfa.backend.model.display;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 接口日志信息
 *
 * @since 2022-07-28
 */
@Data
@TableName("sfa_interface_log")
public class InterfaceLogPO extends Model<InterfaceLogPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	 * 标识
	 */
	@TableId(value = "apply_id")
	private String applyId;

	/**
	* 类型(1:特陈发起,2:特陈取消,3:资料修改发起,4:问题反馈发起,5:造旺报名,6.sap订单发起,7.保存售后单,8.售后单撤回,9.客服审批,10:问题反馈评分,11:陈列规则,12:问题反馈复议)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 入参
	*/
	@TableField("input")
	private String input;

	/**
	* 出参
	*/
	@TableField("output")
	private String output;

	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	public InterfaceLogPO() {
	}

	public InterfaceLogPO(String applyId, Integer type, String input, String output, LocalDateTime createTime) {
		this.applyId = applyId;
		this.type = type;
		this.input = input;
		this.output = output;
		this.createTime = createTime;
	}
}
