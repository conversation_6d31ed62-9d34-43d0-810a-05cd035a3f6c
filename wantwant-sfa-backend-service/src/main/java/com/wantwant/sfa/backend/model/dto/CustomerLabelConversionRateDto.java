package com.wantwant.sfa.backend.model.dto;

import com.wantwant.sfa.backend.realData.anno.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 2021-08-11 Gu
 *
 * <p>客户标签转化率
 */
@Data
public class CustomerLabelConversionRateDto {

  @ApiModelProperty(value = "潜在客户")
  @ListName(value = "potential")
  @VisitCustomerNum(value = "potentialVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "potentialVisitCustomerNumLastMonth")
  @TransAmountActual(value = "potentialTransAmountActual")
  @TransAmountActualLastMonth(value = "potentialTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "potentialOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "potentialOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "potentialNotVisit")
  @NotVisitLastMonth(value = "potentialNotVisitLastMonth")
  @MonthlyCustomers(value = "potentiaMonthlyCustomers")
  @LastMonthlyCustomers(value = "potentiaLastMonthlyCustomers")
  private BigDecimal potentialVisitCustomerNum; /*潜在客户 拜访客户数*/

  @ApiModelProperty(value = "潜在客户 上月拜访客户数")
  private BigDecimal potentialVisitCustomerNumLastMonth; /*潜在客户 拜访客户数*/

  @ApiModelProperty(value = "潜在客户  下单金额")
  private BigDecimal potentialTransAmountActual;

  @ApiModelProperty(value = "潜在客户  上月下单金额")
  private BigDecimal potentialTransAmountActualLastMonth;

  @ApiModelProperty(value = "潜在客户 下单客户数（有效交易客户数） ")
  private BigDecimal potentialOrderNumberOfCustomers;

  @ApiModelProperty(value = "潜在客户 上月下单客户数（有效交易客户数） ")
  private BigDecimal potentialOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "潜在客户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal potentialNotVisit;

  @ApiModelProperty(value = "潜在客户 上月自主下单客户数(未拜访交易客户数)")
  private BigDecimal potentialNotVisitLastMonth;

  @ApiModelProperty(value = "潜在客户 上月末客户数")
  private BigDecimal potentiaMonthlyCustomers;

  @ApiModelProperty(value = "潜在客户 上上月末客户数")
  private BigDecimal potentiaLastMonthlyCustomers;

  @ApiModelProperty(value = "未下单用户")
  @ListName(value = "unordered")
  @VisitCustomerNum(value = "unorderedVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "unorderedVisitCustomerNumLastMonth")
  @TransAmountActual(value = "unorderedTransAmountActual")
  @TransAmountActualLastMonth(value = "unorderedTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "unorderedOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "unorderedOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "unorderedNotVisit")
  @NotVisitLastMonth(value = "unorderedNotVisitLastMonth")
  @MonthlyCustomers(value = "unorderedMonthlyCustomers")
  @LastMonthlyCustomers(value = "unorderedLastMonthlyCustomers")

  /*未下单用户 拜访客户数*/
  private BigDecimal unorderedVisitCustomerNum;

  @ApiModelProperty(value = "未下单用户 上月末客户数")
  private BigDecimal unorderedVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "未下单用户 上上月末客户数")
  private BigDecimal unorderedTransAmountActual;

  @ApiModelProperty(value = "未下单用户  下单客户数（有效交易客户数） ")
  private BigDecimal unorderedTransAmountActualLastMonth;

  @ApiModelProperty(value = "未下单用户   下单金额")
  private BigDecimal unorderedOrderNumberOfCustomers;

  @ApiModelProperty(value = "未下单用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal unorderedOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "未下单用户   下单金额")
  private BigDecimal unorderedNotVisit;

  @ApiModelProperty(value = "未下单用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal unorderedNotVisitLastMonth;

  @ApiModelProperty(value = "未下单用户 上月末客户数")
  private BigDecimal unorderedMonthlyCustomers;

  @ApiModelProperty(value = "未下单用户 上上月末客户数")
  private BigDecimal unorderedLastMonthlyCustomers;

  @ApiModelProperty(value = "新用户")
  @ListName(value = "newer")
  @VisitCustomerNum(value = "newerVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "newerVisitCustomerNumLastMonth")
  @TransAmountActual(value = "newerTransAmountActual")
  @TransAmountActualLastMonth(value = "newerTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "newerOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "newerOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "newerNotVisit")
  @NotVisitLastMonth(value = "newerNotVisitLastMonth")
  @MonthlyCustomers(value = "newerMonthlyCustomers")
  @LastMonthlyCustomers(value = "newerLastMonthlyCustomers")
  /*新用户 拜访客户数*/
  private BigDecimal newerVisitCustomerNum;

  @ApiModelProperty(value = "新用户 上月末客户数")
  private BigDecimal newerVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "新用户 上上月末客户数")
  private BigDecimal newerTransAmountActual;

  @ApiModelProperty(value = "新用户  下单客户数（有效交易客户数） ")
  private BigDecimal newerTransAmountActualLastMonth;

  @ApiModelProperty(value = "新用户  下单金额 ")
  private BigDecimal newerOrderNumberOfCustomers;

  @ApiModelProperty(value = "新用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal newerOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "新用户 客单价")
  private BigDecimal newerNotVisit;

  @ApiModelProperty(value = "新用户 自主下单占比")
  private BigDecimal newerNotVisitLastMonth;

  @ApiModelProperty(value = "新用户 上月末客户数")
  private BigDecimal newerMonthlyCustomers;

  @ApiModelProperty(value = "新用户 上上月末客户数")
  private BigDecimal newerLastMonthlyCustomers;

  @ApiModelProperty(value = "活跃用户")
  @ListName(value = "active")
  @VisitCustomerNum(value = "activeVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "activeVisitCustomerNumLastMonth")
  @TransAmountActual(value = "activeTransAmountActual")
  @TransAmountActualLastMonth(value = "activeTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "activeOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "activeOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "activeNotVisit")
  @NotVisitLastMonth(value = "activeNotVisitLastMonth")
  @MonthlyCustomers(value = "activeMonthlyCustomers")
  @LastMonthlyCustomers(value = "activeLastMonthlyCustomers")
  /* 活跃用户 拜访客户数*/
  private BigDecimal activeVisitCustomerNum;

  @ApiModelProperty(value = "活跃用户 上月末客户数")
  private BigDecimal activeVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "活跃用户 上上月末客户数")
  private BigDecimal activeTransAmountActual;

  @ApiModelProperty(value = "活跃用户  下单客户数（有效交易客户数） ")
  private BigDecimal activeTransAmountActualLastMonth;

  @ApiModelProperty(value = "活跃用户   下单金额")
  private BigDecimal activeOrderNumberOfCustomers;

  @ApiModelProperty(value = "活跃用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal activeOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "活跃用户 客单价")
  private BigDecimal activeNotVisit;

  @ApiModelProperty(value = "活跃用户 自主下单占比")
  private BigDecimal activeNotVisitLastMonth;

  @ApiModelProperty(value = "活跃用户 上月末客户数")
  private BigDecimal activeMonthlyCustomers;

  @ApiModelProperty(value = "活跃用户 上上月末客户数")
  private BigDecimal activeLastMonthlyCustomers;

  @ApiModelProperty(value = "忠诚用户")
  @ListName(value = "loyalty")
  @VisitCustomerNum(value = "loyaltyVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "loyaltyVisitCustomerNumLastMonth")
  @TransAmountActual(value = "loyaltyTransAmountActual")
  @TransAmountActualLastMonth(value = "loyaltyTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "loyaltyOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "loyaltyOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "loyaltyNotVisit")
  @NotVisitLastMonth(value = "loyaltyNotVisitLastMonth")
  @MonthlyCustomers(value = "loyaltyMonthlyCustomers")
  @LastMonthlyCustomers(value = "loyaltyLastMonthlyCustomers")
  /* 忠诚用户 拜访客户数*/
  private BigDecimal loyaltyVisitCustomerNum;

  @ApiModelProperty(value = "忠诚用户 上月末客户数")
  private BigDecimal loyaltyVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "忠诚用户 上上月末客户数")
  private BigDecimal loyaltyTransAmountActual;

  @ApiModelProperty(value = "忠诚用户  下单客户数（有效交易客户数） ")
  private BigDecimal loyaltyTransAmountActualLastMonth;

  @ApiModelProperty(value = "忠诚用户   下单金额")
  private BigDecimal loyaltyOrderNumberOfCustomers;

  @ApiModelProperty(value = "忠诚用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal loyaltyOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "忠诚用户 客单价")
  private BigDecimal loyaltyNotVisit;

  @ApiModelProperty(value = "忠诚用户 自主下单占比")
  private BigDecimal loyaltyNotVisitLastMonth;

  @ApiModelProperty(value = "忠诚用户 上月末客户数")
  private BigDecimal loyaltyMonthlyCustomers;

  @ApiModelProperty(value = "忠诚用户 上上月末客户数")
  private BigDecimal loyaltyLastMonthlyCustomers;

  @ApiModelProperty(value = "流失用户")
  @ListName(value = "lost")
  @VisitCustomerNum(value = "lostVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "lostVisitCustomerNumLastMonth")
  @TransAmountActual(value = "lostTransAmountActual")
  @TransAmountActualLastMonth(value = "lostTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "lostOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "lostOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "lostNotVisit")
  @NotVisitLastMonth(value = "lostNotVisitLastMonth")
  @MonthlyCustomers(value = "lostMonthlyCustomers")
  @LastMonthlyCustomers(value = "lostLastMonthlyCustomers")

  /* 流失用户 拜访客户数*/
  private BigDecimal lostVisitCustomerNum;

  @ApiModelProperty(value = "流失用户 上月末客户数")
  private BigDecimal lostVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "流失用户 上上月末客户数")
  private BigDecimal lostTransAmountActual;

  @ApiModelProperty(value = "流失用户  下单客户数（有效交易客户数） ")
  private BigDecimal lostTransAmountActualLastMonth;

  @ApiModelProperty(value = "流失用户   下单金额")
  private BigDecimal lostOrderNumberOfCustomers;

  @ApiModelProperty(value = "流失用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal lostOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "流失用户 客单价")
  private BigDecimal lostNotVisit;

  @ApiModelProperty(value = "流失用户 自主下单占比")
  private BigDecimal lostNotVisitLastMonth;

  @ApiModelProperty(value = "流失用户 上月末客户数")
  private BigDecimal lostMonthlyCustomers;

  @ApiModelProperty(value = "流失用户 上上月末客户数")
  private BigDecimal lostLastMonthlyCustomers;

  @ApiModelProperty(value = "其他沉睡")
  @ListName(value = "sleep")
  @VisitCustomerNum(value = "sleepVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "sleepVisitCustomerNumLastMonth")
  @TransAmountActual(value = "sleepTransAmountActual")
  @TransAmountActualLastMonth(value = "sleepTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "sleepOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "sleepOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "sleepNotVisit")
  @NotVisitLastMonth(value = "sleepNotVisitLastMonth")
  @MonthlyCustomers(value = "sleepMonthlyCustomers")
  @LastMonthlyCustomers(value = "sleepLastMonthlyCustomers")

  /* 其他沉睡 拜访客户数*/
  private BigDecimal sleepVisitCustomerNum;

  @ApiModelProperty(value = "其他沉睡 上月末客户数")
  private BigDecimal sleepVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "其他沉睡 上上月末客户数")
  private BigDecimal sleepTransAmountActual;

  @ApiModelProperty(value = "其他沉睡  下单客户数（有效交易客户数） ")
  private BigDecimal sleepTransAmountActualLastMonth;

  @ApiModelProperty(value = "其他沉睡   下单金额")
  private BigDecimal sleepOrderNumberOfCustomers;

  @ApiModelProperty(value = "其他沉睡 自主下单客户数(未拜访交易客户数)")
  private BigDecimal sleepOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "其他沉睡 客单价")
  private BigDecimal sleepNotVisit;

  @ApiModelProperty(value = "其他沉睡 自主下单占比")
  private BigDecimal sleepNotVisitLastMonth;

  @ApiModelProperty(value = "其他沉睡 上月末客户数")
  private BigDecimal sleepMonthlyCustomers;

  @ApiModelProperty(value = "其他沉睡 上上月末客户数")
  private BigDecimal sleepLastMonthlyCustomers;

  @ApiModelProperty(value = "召回用户")
  @ListName(value = "callback")
  @VisitCustomerNum(value = "callbackVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "callbackVisitCustomerNumLastMonth")
  @TransAmountActual(value = "callbackTransAmountActual")
  @TransAmountActualLastMonth(value = "callbackpTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "callbackOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "callbackOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "callbackNotVisit")
  @NotVisitLastMonth(value = "callbackNotVisitLastMonth")
  @MonthlyCustomers(value = "callbackMonthlyCustomers")
  @LastMonthlyCustomers(value = "callbackLastMonthlyCustomers")

  /* 召回用户 拜访客户数*/
  private BigDecimal callbackVisitCustomerNum;

  @ApiModelProperty(value = "召回用户 上月末客户数")
  private BigDecimal callbackVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "召回用户 上上月末客户数")
  private BigDecimal callbackTransAmountActual;

  @ApiModelProperty(value = "召回用户  下单客户数（有效交易客户数） ")
  private BigDecimal callbackpTransAmountActualLastMonth;

  @ApiModelProperty(value = "召回用户   下单金额")
  private BigDecimal callbackOrderNumberOfCustomers;

  @ApiModelProperty(value = "召回用户 自主下单客户数(未拜访交易客户数)")
  private BigDecimal callbackOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "召回用户 客单价")
  private BigDecimal callbackNotVisit;

  @ApiModelProperty(value = "召回用户 自主下单占比")
  private BigDecimal callbackNotVisitLastMonth;

  @ApiModelProperty(value = "召回用户 上月末客户数")
  private BigDecimal callbackMonthlyCustomers;

  @ApiModelProperty(value = "召回用户 上上月末客户数")
  private BigDecimal callbackLastMonthlyCustomers;

  @ApiModelProperty(value = "活跃沉睡")
  @ListName(value = "activeSleep")
  @VisitCustomerNum(value = "activeSleepVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "activeSleepVisitCustomerNumLastMonth")
  @TransAmountActual(value = "activeSleepTransAmountActual")
  @TransAmountActualLastMonth(value = "activeSleepTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "activeSleepOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "activeSleepOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "activeSleepNotVisit")
  @NotVisitLastMonth(value = "activeSleepNotVisitLastMonth")
  @MonthlyCustomers(value = "activeSleepMonthlyCustomers")
  @LastMonthlyCustomers(value = "activeSleepLastMonthlyCustomers")

  /* 活跃沉睡 拜访客户数*/
  private BigDecimal activeSleepVisitCustomerNum;

  @ApiModelProperty(value = "活跃沉睡 上月末客户数")
  private BigDecimal activeSleepVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "活跃沉睡 上上月末客户数")
  private BigDecimal activeSleepTransAmountActual;

  @ApiModelProperty(value = "活跃沉睡  下单客户数（有效交易客户数） ")
  private BigDecimal activeSleepTransAmountActualLastMonth;

  @ApiModelProperty(value = "活跃沉睡   下单金额")
  private BigDecimal activeSleepOrderNumberOfCustomers;

  @ApiModelProperty(value = "活跃沉睡 自主下单客户数(未拜访交易客户数)")
  private BigDecimal activeSleepOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "活跃沉睡 客单价")
  private BigDecimal activeSleepNotVisit;

  @ApiModelProperty(value = "活跃沉睡 自主下单占比")
  private BigDecimal activeSleepNotVisitLastMonth;

  @ApiModelProperty(value = "活跃沉睡 上月末客户数")
  private BigDecimal activeSleepMonthlyCustomers;

  @ApiModelProperty(value = "活跃沉睡 上上月末客户数")
  private BigDecimal activeSleepLastMonthlyCustomers;

  @ApiModelProperty(value = "忠诚沉睡")
  @ListName(value = "loyaltySleep")
  @VisitCustomerNum(value = "loyaltySleepVisitCustomerNum")
  @VisitCustomerNumLastMonth(value = "loyaltySleepVisitCustomerNumLastMonth")
  @TransAmountActual(value = "loyaltySleepTransAmountActual")
  @TransAmountActualLastMonth(value = "loyaltySleepTransAmountActualLastMonth")
  @OrderNumberOfCustomers(value = "loyaltySleepOrderNumberOfCustomers")
  @OrderNumberOfCustomersLastMonth(value = "loyaltySleepOrderNumberOfCustomersLastMonth")
  @NotVisit(value = "loyaltySleepNotVisit")
  @NotVisitLastMonth(value = "loyaltySleepNotVisitLastMonth")
  @MonthlyCustomers(value = "loyaltySleepMonthlyCustomers")
  @LastMonthlyCustomers(value = "loyaltySleepLastMonthlyCustomers")

  /*忠诚沉睡 拜访客户数*/
  private BigDecimal loyaltySleepVisitCustomerNum;

  @ApiModelProperty(value = "忠诚沉睡 上月末客户数")
  private BigDecimal loyaltySleepVisitCustomerNumLastMonth;

  @ApiModelProperty(value = "忠诚沉睡 上上月末客户数")
  private BigDecimal loyaltySleepTransAmountActual;

  @ApiModelProperty(value = "忠诚沉睡  下单客户数（有效交易客户数） ")
  private BigDecimal loyaltySleepTransAmountActualLastMonth;

  @ApiModelProperty(value = "忠诚沉睡   下单金额")
  private BigDecimal loyaltySleepOrderNumberOfCustomers;

  @ApiModelProperty(value = "忠诚沉睡 自主下单客户数(未拜访交易客户数)")
  private BigDecimal loyaltySleepOrderNumberOfCustomersLastMonth;

  @ApiModelProperty(value = "忠诚沉睡 客单价")
  private BigDecimal loyaltySleepNotVisit;

  @ApiModelProperty(value = "忠诚沉睡 自主下单占比")
  private BigDecimal loyaltySleepNotVisitLastMonth;

  @ApiModelProperty(value = "忠诚沉睡 上月末客户数")
  private BigDecimal loyaltySleepMonthlyCustomers;

  @ApiModelProperty(value = "忠诚沉睡 上上月末客户数")
  private BigDecimal loyaltySleepLastMonthlyCustomers;
}
