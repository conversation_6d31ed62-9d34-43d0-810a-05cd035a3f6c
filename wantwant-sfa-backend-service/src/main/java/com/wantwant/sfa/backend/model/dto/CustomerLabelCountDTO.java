package com.wantwant.sfa.backend.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 客户标签数
 *
 * <AUTHOR>
 * @date 2021-07-01 18:22
 * @version 1.0
 */
@Data
public class CustomerLabelCountDTO implements Serializable {

  private static final long serialVersionUID = -1095844873467505151L;
  /** 组织id */
  private String organizationId;

  /** 组织名称 */
  private String organizationName;

  /** 组织名称 */
  private String organizationType;

  /** 潜在客户 */
  private int potential;

  /** 未下单用户 */
  private int unordered;

  /** 新用户 */
  private int newer;

  /** 活跃用户 */
  private int active;

  /** 忠诚用户 */
  private int loyalty;

  /** 流失用户 */
  private int lost;

  /** 其他沉睡 */
  private int sleep;

  /** 召回用户 */
  private int callback;

  /** 活跃沉睡 */
  private int activeSleep;

  /** 忠诚沉睡 */
  private int loyaltySleep;
}
