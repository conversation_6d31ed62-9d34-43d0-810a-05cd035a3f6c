package com.wantwant.sfa.backend.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrganizationDataDto implements Serializable {

  @ApiModelProperty(value = "组织名称")
  private String organizationName;

  @ApiModelProperty(value = "组织id")
  private String organizationId;

  @ApiModelProperty(value = "组织类型")
  private String organizationType;

  @ApiModelProperty(value = "业务员名称")
  private String employeeName;

  @ApiModelProperty(value = "业务员工号")
  private String employeeId;

  @ApiModelProperty(value = "本期销售金额")
  private BigDecimal transAmountActual;

  @ApiModelProperty(value = "上期金额")
  private BigDecimal lastTransAmountActual;

  @ApiModelProperty(value = "订单数量")
  private Integer orderNum;

  @ApiModelProperty(value = "顾客数量")
  private Integer customerNumber;

  @ApiModelProperty(value = "客户单价")
  private BigDecimal customerUnitPrice;

  public BigDecimal getCustomerUnitPrice() {
    if (customerNumber == 0 || transAmountActual.compareTo(BigDecimal.ZERO) <= 0) {
      return BigDecimal.ZERO;
    } else {
      return transAmountActual.divide(new BigDecimal(customerNumber), 2, BigDecimal.ROUND_HALF_UP);
    }
  }

  @ApiModelProperty(value = "销售金额目标")
  private BigDecimal transAmountTagret;

  @ApiModelProperty(value = "销售金额达成")
  private BigDecimal transAmountRate;

  @ApiModelProperty(value = "月环比增长率")
  private BigDecimal growth;

  @ApiModelProperty(value = "上月今日金额")
  private BigDecimal lastMonthTodayTransAmountActual;

  @ApiModelProperty(value = "在岗人数")
  private Integer onDutyNum;

  @ApiModelProperty(value = "在岗人数 日报")
  private Integer onGuardNumDaily;

  @ApiModelProperty("考核人数")
  private Integer examineNumbers;

  @ApiModelProperty(value = "交易客户数")
  private Long dealCustomerCountActual; /*汇总*/

  @ApiModelProperty(value = "人均交易客户数")
  private BigDecimal avgDealCustomerCount;

  @ApiModelProperty(value = "客户交易目标") /*原有*/
  private BigDecimal transactionCustomerTarget;

  @ApiModelProperty(value = "人均客户交易目标")
  private BigDecimal avgTransactionCustomerTarget;

  @ApiModelProperty(value = "首单客户数")
  private Long firstOrderCustomerCountActual; /*汇总*/

  @ApiModelProperty(value = "月首单客户数")
  private Long firstOrderCustomerCountMonthActual;

  @ApiModelProperty(value = "日首单客户数")
  private Long firstOrderCustomerCountTodayActual;

  @ApiModelProperty(value = "人均首单客户数")
  private BigDecimal avgFirstOrderCustomerCount;

  @ApiModelProperty(value = "首单客户数目标")
  private BigDecimal firstOrderCustomerCountTarget;

  @ApiModelProperty(value = "人均首单客户数目标")
  private BigDecimal avgFirstOrderCustomerCountTarget;

  @ApiModelProperty(value = "本月拜访达成数")
  private Long visitCountActual; /*汇总*/

  @ApiModelProperty(value = "人均拜访客户数")
  private BigDecimal avgVisitCount;

  @ApiModelProperty(value = "拜访客户数目标")
  private BigDecimal visitCountTarget;

  @ApiModelProperty(value = "人均拜访客户数目标")
  private BigDecimal avgVisitCountTarget;

  @ApiModelProperty(value = "本月开发客户数")
  private Long newCustomerCountActual; /*汇总*/

  @ApiModelProperty(value = "人均开发客户数")
  private BigDecimal avgNewCustomerCount;

  @ApiModelProperty(value = "开发客户数目标")
  private BigDecimal newCustomerCountTarget;

  @ApiModelProperty(value = "人均开发客户数目标")
  private BigDecimal avgNewCustomerCountTarget;

  @ApiModelProperty(value = "业绩达成")
  private BigDecimal results; /*汇总*/

  @ApiModelProperty(value = "人均业绩达成")
  private BigDecimal avgResults;

  @ApiModelProperty(value = "业绩目标") /*原有*/
  private BigDecimal transAmountTarget;

  @ApiModelProperty(value = "人均业绩目标")
  private BigDecimal avgTransAmountTarget;

  @ApiModelProperty(value = "考勤Id")
  private Integer attendanceId;

  @ApiModelProperty(value = "考勤状态")
  private Integer attendanceStatus;

  @ApiModelProperty(value = "个人金额")
  private BigDecimal personalTransAmountActual;

  @ApiModelProperty(value = "终端金额")
  private BigDecimal terminalTransAmountActual;

  @ApiModelProperty(value = "批发金额")
  private BigDecimal wholesaleTransAmountActual;

  @ApiModelProperty(value = "特通金额")
  private BigDecimal specialTransAmountActual;

  @ApiModelProperty(value = "潜在客户")
  private Long potential; /*汇总*/

  @ApiModelProperty(value = "未下单用户")
  private Long unordered; /*汇总*/

  @ApiModelProperty(value = "新用户")
  private Long newer; /*汇总*/

  @ApiModelProperty(value = "活跃用户")
  private Long active; /*汇总*/

  @ApiModelProperty(value = "忠诚用户")
  private Long loyalty; /*汇总*/

  @ApiModelProperty(value = "流失用户")
  private Long lost; /*汇总*/

  @ApiModelProperty(value = "其他沉睡")
  private Long sleep; /*汇总*/

  @ApiModelProperty(value = "召回用户")
  private Long callback; /*汇总*/

  @ApiModelProperty(value = "活跃沉睡")
  private Long activeSleep; /*汇总*/

  @ApiModelProperty(value = "忠诚沉睡")
  private Long loyaltySleep; /*汇总*/

  @ApiModelProperty(value = "潜在客户交易金额")
  private BigDecimal potentialTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "未下单用户金额")
  private BigDecimal unorderedTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "新用户金额")
  private BigDecimal newerTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "活跃用户金额")
  private BigDecimal activeTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "忠诚用户金额")
  private BigDecimal loyaltyTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "流失用户金额")
  private BigDecimal lostTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "其他沉睡金额")
  private BigDecimal sleepTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "召回用户金额")
  private BigDecimal callbackTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "活跃沉睡金额")
  private BigDecimal activeSleepTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "忠诚沉睡金额")
  private BigDecimal loyaltySleepTransAmountActual; /*汇总*/

  @ApiModelProperty(value = "客户目标数")
  private BigDecimal customerTarget;

  @ApiModelProperty(value = "客户达成率")
  private BigDecimal customerRate;

  @ApiModelProperty(value = "新入职人数")
  private Integer newRecruitsNumber;

  public static void main(String[] args) {
    Long l = 500L;
    Integer i = 11;
    Integer j = (int) (l / i);
    System.out.println("l/i = " + l / i);
    System.out.println("j = " + j);
  }
}
