package com.wantwant.sfa.backend.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: luxiaoyin
 * @Date: 2020/6/23
 * @Package: com.wantwant.sfa.backend.model.dto
 */
@Data
public class VisitListDTO {

    private String employeeName;

    private String visitEmployeeName;

    private Integer customerId;

    private LocalDateTime visitTime;

    private LocalDateTime visitEndTime;

    private Integer isPotential;

    private Integer isVerified;

    private String organizationName3;

    private String organizationName2;

    private String organizationName1;

    private Integer isActivity;

    private Integer sex;

    private Integer customerType;

    private Integer customerSubType;

    private Double currentMoney;

    private String memberId;


    private String customerName;

    private String storeName;

    private String schoolName;

    private String positionId;

    private String visitStayTime;

    private String visitType;


    private String goalTypeCode;

    private Integer isConvert;

    private Integer visitId;

    private String customerMobile;

    private String mobileNumber;

    private String result;

    private String remark;

    private Double toDayAmounts;
}
