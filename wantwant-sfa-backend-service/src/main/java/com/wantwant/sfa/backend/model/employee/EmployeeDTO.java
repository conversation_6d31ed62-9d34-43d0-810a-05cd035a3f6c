package com.wantwant.sfa.backend.model.employee;

import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import lombok.Data;

@Data
public class EmployeeDTO {
    /** sfa_employee_info表主键 */
    private Integer employeeInfoId;
    /** 在职状态 */
    private Integer employeeStatus;
    /** 面试表ID */
    private Integer interviewRecordId;
    /** 在职状态描述 */
    private String employeeStatusStr;
    /** 岗位类型ID */
    private Integer positionTypeId;
    /** memberKey */
    private Long memberKey;


    public String getEmployeeStatusStr(){
        return EmployeeStatus.findNameByType(this.employeeStatus);
    }
}
