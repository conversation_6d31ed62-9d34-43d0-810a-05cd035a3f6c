package com.wantwant.sfa.backend.model.employee;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeOnboardInfo {
    private String posId;
    private String employeeId;
    private String employeeName;
    private LocalDateTime onboardTime;
}
