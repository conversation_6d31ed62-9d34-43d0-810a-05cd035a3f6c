package com.wantwant.sfa.backend.model.estimate;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/29/下午2:19
 */
@Data
@ToString
public class EstimateModel {
    /** 销售预估单号 */
    private String saleEstimateNo;
    /** 审核状态 0:待审核  1:审核中 2:审核通过 3:驳回 4:取消 */
    private Integer status;
    /** 审批额度 */
    private BigDecimal auditAmount;
    /** 审批数量 */
    private BigDecimal auditQuantity;
    /** 驳回原因 */
    private String remark;
    /** 操作人 */
    private String updator;
    /** 预估单明细 */
    private List<EstimateDetailModel> items;
}
