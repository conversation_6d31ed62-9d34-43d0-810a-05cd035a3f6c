package com.wantwant.sfa.backend.model.estimate;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/30/上午11:00
 */
@Data
public class EstimatePositionModel {
    /** 申请主表ID */
    private Integer approvalId;
    /** 申请记录ID */
    private Integer recordId;
    /** 物料名称 */
    private String skuName;
    /** sku编码 */
    private String sku;
    /** 产品线 */
    private String lineName;
    /** 上市月份 */
    private String expectListMonth;
    /** 供货价 */
    private BigDecimal supplyPrice;
    /** 申请月份 */
    private String month;
    /** 类型 */
    private Integer estimateType;
    /** 预估数量 */
    private BigDecimal estimateQuantity;
    /** 预估金额 */
    private BigDecimal estimatePrice;
    /** 核对价格 */
    private BigDecimal auditQuantity;
    /** 核对价格 */
    private BigDecimal auditPrice;
    /** 申请人岗位ID */
    private String applyPositionId;
    /** pt编码 */
    private String ptKey;
    /** 审核状态 */
    private Integer result;
    /** 员工名称 */
    private String employeeName;
    /** 组织名称 */
    private String organizationName;
}
