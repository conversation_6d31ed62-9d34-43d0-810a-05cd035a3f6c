package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报销申请所有文件记录
 *
 * @since 2023-09-18
 */
@Data
@TableName("sfa_expense_apply_file_info")
public class ExpenseApplyFileInfoPO extends Model<ExpenseApplyFileInfoPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Long id;

	/**
	* sfa_expense_apply.apply_id
	*/
	@TableField("apply_id")
	private Integer applyId;


	/**
	* 关联类型(1.餐补、2.出行、3住宿、4内部聚餐)
	*/
	@TableField("relation_type")
	private String relationType;

	/**
	 * 关联子类型(1.票据、2.导航)
	 */
	@TableField("sub_relation_type")
	private String subRelationType;

	/**
	* 关联id
	*/
	@TableField("relation_id")
	private Integer relationId;

	/**
	 *文件url
	 */
	@TableField("file_url")
	private String url;
	/**
	 *文件名称
	 */
	@TableField("file_name")
	private String name;
	/**
	 *文件类型
	 */
	@TableField("file_type")
	private String type;
	/**
	 * 票据信息
	 */
	@TableField("invoice_info")
	private String invoiceInfo;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
    @TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
