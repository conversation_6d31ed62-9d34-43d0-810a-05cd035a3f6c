package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报销申请住宿明细
 *
 * @since 2023-09-18
 */
@Data
@TableName("sfa_expense_apply_hotel")
public class ExpenseApplyHotelPO extends Model<ExpenseApplyHotelPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "hotel_id")
	private Integer hotelId;

	/**
	* sfa_expense_apply.trip_id
	*/
	@TableField("apply_id")
	private Integer applyId;

	/**
	* 费用类型
	*/
	@TableField("hotel_type")
	private String hotelType;

	/**
	* 入住城市
	*/
	@TableField("staying_city")
	private String stayingCity;

	/**
	* 酒店名称及地点
	*/
	@TableField("consumer")
	private String consumer;

	/**
	* 住宿标准
	*/
	@TableField("standard")
	private String standard;

	/**
	* 金额
	*/
	@TableField("hotel_amount")
	private BigDecimal hotelAmount;

	/**
	* 入住日期
	*/
	@TableField("staying_date")
	private LocalDate stayingDate;

	/**
	* 退房日期
	*/
	@TableField("out_date")
	private LocalDate outDate;

	/**
	* 几晚
	*/
	@TableField("nights")
	private Integer nights;

	/**
	* 事由说明
	*/
	@TableField("hotel_remark")
	private String hotelRemark;

	/**
	* 发票号码
	*/
	@TableField("hotel_invoice")
	private String hotelInvoice;

	/**
	* 附件
	*/
	@TableField("hotel_file_url")
	private String hotelFileUrl;

	/**
	* 票据图片
	*/
	@TableField("hotel_pic_url")
	private String hotelPicUrl;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(1:删除)
     */
	@TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}
