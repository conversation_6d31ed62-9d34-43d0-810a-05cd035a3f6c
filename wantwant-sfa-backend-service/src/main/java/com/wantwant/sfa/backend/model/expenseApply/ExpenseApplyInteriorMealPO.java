package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报销申请内部聚餐明细
 *
 * @since 2023-09-18
 */
@Data
@TableName("sfa_expense_apply_interior_meal")
public class ExpenseApplyInteriorMealPO extends Model<ExpenseApplyInteriorMealPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "interior_meal_id")
	private Integer interiorMealId;

	/**
	* sfa_expense_apply.apply_id
	*/
	@TableField("apply_id")
	private Integer applyId;

	/**
	 * 关联会议id
	 */
	@TableField("info_id")
	private Integer infoId;

	/**
	* 费用类型
	*/
	@TableField("interior_meal_type")
	private String interiorMealType;

	/**
	* 发生日期
	*/
	@TableField("interior_meal_date")
	private LocalDate interiorMealDate;

	/**
	* 金额
	*/
	@TableField("interior_meal_amount")
	private BigDecimal interiorMealAmount;

	/**
	 * 预估金额
	 */
	@TableField("estimate_price")
	private BigDecimal estimatePrice;
	/**
	 * 可报销人数
	 */
	@TableField("participant_count")
	private Integer participantCount;
	/**
	 * 会议类型-小类
	 */
	@TableField("subclass")
	private String subclass;
	/**
	* 发生城市
	*/
	@TableField("interior_meal_city")
	private String interiorMealCity;

	/**
	* 餐补标准
	*/
	@TableField("interior_meal_standard")
	private String interiorMealStandard;

	/**
	 * 发票号
	 */
	@TableField("interior_meal_invoice")
	private String interiorMealInvoice;

	/**
	* 事由说明
	*/
	@TableField("interior_meal_remark")
	private String interiorMealRemark;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
    @TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
