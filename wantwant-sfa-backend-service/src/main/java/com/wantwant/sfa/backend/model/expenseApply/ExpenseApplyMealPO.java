package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报销申请餐补明细
 *
 * @since 2023-09-18
 */
@Data
@TableName("sfa_expense_apply_meal")
public class ExpenseApplyMealPO extends Model<ExpenseApplyMealPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "meal_id")
	private Integer mealId;

	/**
	* sfa_expense_apply.apply_id
	*/
	@TableField("apply_id")
	private Integer applyId;


	/**
	* 费用类型
	*/
	@TableField("meal_type")
	private String mealType;

	/**
	* 发生日期
	*/
	@TableField("meal_date")
	private LocalDate mealDate;

	/**
	* 金额
	*/
	@TableField("meal_amount")
	private BigDecimal mealAmount;

	/**
	* 发生城市
	*/
	@TableField("meal_city")
	private String mealCity;

	/**
	* 餐补标准
	*/
	@TableField("meal_standard")
	private String mealStandard;

	/**
	* 事由说明
	*/
	@TableField("meal_remark")
	private String mealRemark;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
    @TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
