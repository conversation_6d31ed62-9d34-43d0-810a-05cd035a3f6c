package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报销申请
 *
 */
@Data
@TableName("sfa_expense_apply")
public class ExpenseApplyPO extends Model<ExpenseApplyPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "apply_id")
	private Integer applyId;

	/**
	* sfa_business_trip.id
	*/
	@TableField("trip_id")
	private Integer tripId;

	/**
	* sfa_expense_apply_process.id
	*/
	@TableField(value = "process_id",strategy = FieldStrategy.IGNORED)
	private Long processId;

	/**
	* 开始时间
	*/
	@TableField("start_date")
	private LocalDate startDate;

	/**
	* 结束时间
	*/
	@TableField("end_date")
	private LocalDate endDate;

	/**
	* 出差天数
	*/
	@TableField("days")
	private Integer days;

	/**
	* 出发地点
	*/
	@TableField("departure_position")
	private String departurePosition;

	/**
	* 出差地点
	*/
	@TableField("business_position")
	private String businessPosition;

	/**
	* 出差类型
	*/
	@TableField("business_type")
	private String businessType;

	/**
	* 报销类别
	*/
	@TableField("expense_type")
	private String expenseType;

	/**
	* 出差目的
	*/
	@TableField("business_purpose")
	private String businessPurpose;

	/**
	* 出差事由
	*/
	@TableField("business_reasons")
	private String businessReasons;

	/**
	* 出差报告
	*/
	@TableField("business_report")
	private String businessReport;

	/**
	* 相关附件
	*/
	@TableField("file_url")
	private String fileUrl;

	/**
	* 审批状态(0:编辑中,1:审批中,2:已通过,3:已驳回,4:撤回)
	*/
	@TableField("status")
	private Integer status;

	/**
	* 组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 总金额
	*/
	@TableField("total_amount")
	private BigDecimal totalAmount;

	/**
	 * 餐补总金额
	 */
	@TableField("total_meal_amount")
	private BigDecimal totalMealAmount;

	/**
	 * 内部聚餐总金额
	 */
	@TableField("total_interior_meal_amount")
	private BigDecimal totalInteriorMealAmount;

	/**
	 * 内部聚餐预估总金额
	 */
	@TableField("total_interior_meal_estimate_amount")
	private BigDecimal totalInteriorMealEstimateAmount;

	/**
	 * 住宿总金额
	 */
	@TableField("total_hotel_amount")
	private BigDecimal totalHotelAmount;

	/**
	 * 出行信息总金额
	 */
	@TableField("total_travel_amount")
	private BigDecimal totalTravelAmount;

	/**
	* 岗位类型(1:总督导,2:区域总监,10:区域经理,12:大区总监)
	*/
	@TableField("position_type")
	private Integer positionType;

	/**
	* 职等
	*/
	@TableField("grade_level")
	private String gradeLevel;

	@TableField("created_by")
	private String createdBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	 * sfa_employee_info.id
	 */
	@TableField("proxy_by")
	private String proxyBy;

	/**
	 *
	 * 代理人名称
	 */
	@TableField("proxy_name")
	private String proxyName;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

	/** 
	 * 身份证号
	 */
	@TableField("id_card_no")
	private String idCardNo;

	/** 
	 * 预留手机号
	 */
	@TableField("mobile")
	private String mobile;

	/** 
	 * 银行卡号
	 */
	@TableField("bank_no")
	private String bankNo;

	/**
	 * 当前单子的员工号
	 */
	@TableField("employee_id")
	private String employeeId;
	

}
