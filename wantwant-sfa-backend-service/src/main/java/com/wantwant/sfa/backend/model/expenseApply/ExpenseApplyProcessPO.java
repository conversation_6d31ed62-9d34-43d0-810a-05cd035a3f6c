package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报销申请审批
 *
 * @since 2023-09-18
 */
@Data
@TableName("sfa_expense_apply_process")
public class ExpenseApplyProcessPO extends Model<ExpenseApplyProcessPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "process_id",type = IdType.INPUT)
	private Long processId;

	/**
	* sfa_expense_apply.trip_id
	*/
	@TableField("apply_id")
	private Integer applyId;

	/**
	* 审核人工号
	*/
	@TableField("reviewer_id")
	private String reviewerId;

	/**
	* 审核人名称
	*/
	@TableField("reviewer_name")
	private String reviewerName;

	/**
	* 审核人岗位
	*/
	@TableField("reviewer_position")
	private String reviewerPosition;

	/**
	* 审核时间
	*/
	@TableField("reviewer_time")
	private LocalDateTime reviewerTime;

	/**
	* 审核人所属组织
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 审批节点(1:总监,2:省区总监,3:大区总监,4:总督导,5:leo,6:财务管理课)
	 * 区域经理:(1:区域总监,2:省区总监,3:大区总监,4:总督导,6:财务管理课)
	 * 总监:(2:省区总监,3:大区总监,4:总督导,6:财务管理课)
	 * 省区总监:(3:大区总监,4:总督导,6:财务管理课)
	 * 大区总监:(4:总督导,6:财务管理课)
	 * 总督导:(5:leo,6:财务管理课)
	*/
	@TableField("process_type")
	private Integer processType;

	/**
	* 审批结果(1:待审核,2:审核通过,3:审核驳回,4:撤回)
	*/
	@TableField("process_result")
	private Integer processResult;

	/**
	* 审批内容
	*/
	@TableField("comment")
	private String comment;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
