package com.wantwant.sfa.backend.model.expenseApply;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报销申请出行明细
 *
 * @since 2023-09-18
 */
@Data
@TableName("sfa_expense_apply_travel")
public class ExpenseApplyTravelPO extends Model<ExpenseApplyTravelPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "travel_id")
	private Integer travelId;

	/**
	* sfa_expense_apply.trip_id
	*/
	@TableField("apply_id")
	private Integer applyId;

	/**
	* 费用类型
	*/
	@TableField("travel_type")
	private String travelType;

	/**
	* 发生日期
	*/
	@TableField("travel_date")
	private LocalDate travelDate;


	/**
	 * 公里数
	 */
	@TableField("distance_kil")
	private BigDecimal distanceKil;

	/**
	 * 单价
	 */
	@TableField("unit_price")
	private BigDecimal unitPrice;

	/**
	* 金额
	*/
	@TableField("travel_amount")
	private BigDecimal travelAmount;

	/**
	* 出发城市
	*/
	@TableField("departure_city")
	private String departureCity;

	/**
	* 到达城市
	*/
	@TableField("arrival_city")
	private String arrivalCity;

	/** 
	 * 出发时间
	 */
	@TableField("departure_time")
	private LocalDateTime departureTime;

	/**
	 * 到达时间
	 */
	@TableField("arrival_time")
	private LocalDateTime arrivalTime;

	/**
	* 事由说明
	*/
	@TableField("travel_remark")
	private String travelRemark;

	/**
	* 发票号码
	*/
	@TableField("travel_invoice")
	private String travelInvoice;

	/**
	* 附件
	*/
	@TableField("travel_file_url")
	private String travelFileUrl;

	/**
	* 票据图片
	*/
	@TableField("travel_pic_url")
	private String travelPicUrl;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;
}
