package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 问题反馈信息
 *
 * @since 2022-09-06
 */
@Data
@TableName("sfa_feedback_info")
public class FeedbackInfoPO extends Model<FeedbackInfoPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 编号
	*/
	@TableField("application_no")
	private String applicationNo;

	/**
	* 用户memberKey
	*/
	@TableField("member_key")
	private String memberKey;

	/**
	 * 问题大类
	 */
	@TableField("category")
	private String category;

	/**
	 * 问题小类
	 */
	@TableField("subclass")
	private String subclass;


	/**
	* 问题描述
	*/
	@TableField("description")
	private String description;

	/** 
	 * 订单编号
	 */
	@TableField("order_code")
	private String orderCode;

	/**
	* 提交时间
	*/
	@TableField("feedback_time")
	private LocalDateTime feedbackTime;

	/**
	* 状态(0:未处理,1:处理中,2:已结案,3:问题无效驳回)
	*/
	@TableField("status")
	private Integer status;

	/** 
	 * sfa_feedback_review.id
	 */
	@TableField("r_id")
	private Integer rId;

	/**
	 * 满意度评分(最新)
	 */
	@TableField("satisfaction_score")
	private Integer satisfactionScore;

	/**
	 * 详细评价(最新)
	 */
	@TableField("detailed_evaluation")
	private String detailedEvaluation;

	/** 
	 * 评分时间(最新)
	 */
	@TableField("score_time")
	private LocalDateTime scoreTime;

	/** 
	 * 时效时间
	 */
	@TableField("expiration_time")
	private LocalDateTime expirationTime;

	/** 
	 * 结案工作小时数
	 */
	@TableField("hours")
	private Long hours;

	/** 
	 * 业务分类(0:问题,1:投诉)
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 产品组编号
	 */
	@TableField("product_group_id")
	private String productGroupId;

	/**
	 * 产品组名称
	 */
	@TableField("product_group_name")
	private String productGroupName;

	/**
	* 创建时间
	*/
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 来源(0:旺铺发起,1:sfaApp发起)
	 */
	@TableField("source")
	private Integer source;

	/** 
	 * 员工工号
	 */
	@TableField("employee_id")
	private String employeeId;

	/**
	 * 奖励旺金币
	 */
	@TableField("reward_want")
	private BigDecimal rewardWant;

	/**
	 * 当前组织
	 */
	@TableField("organization_id")
	private String organizationId;

	/** 
	 * 流程类型(0:常规流程,1:直达总部)
	 */
	@TableField("process_type")
	private Integer processType;

	/** 
	 * 是否复议(0:首次,1:复议) 
	 */
	@TableField("repeat_flag")
	private Integer repeatFlag;

	/** 
	 * 复议提交时间
	 */
	@TableField("repeat_time")
	private LocalDateTime repeatTime;

	/** 
	 * 复议描述
	 */
	@TableField("repeat_description")
	private String repeatDescription;



}
