package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题反馈点赞
 *
 * @since 2022-12-28
 */
@Data
@TableName("sfa_feedback_like")
public class FeedbackLikePO extends Model<FeedbackLikePO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 编号
	*/
	@TableField("application_no")
	private String applicationNo;

	/**
	* 员工工号
	*/
	@TableField("employee_id")
	private String employeeId;

	/**
	* 员工姓名
	*/
	@TableField("employee_name")
	private String employeeName;

	/**
	* 所属组织
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 岗位
	*/
	@TableField("position")
	private String position;

	/** 
	 * 类型(0:点赞,1:关注)
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(delval = "1")
	@TableField("is_delete")
	private Integer isDelete;


}
