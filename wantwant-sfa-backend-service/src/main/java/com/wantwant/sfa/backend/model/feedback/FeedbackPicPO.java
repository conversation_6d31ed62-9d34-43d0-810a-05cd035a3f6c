package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题反馈信息图片
 *
 * @since 2022-09-06
 */
@Data
@TableName("sfa_feedback_pic")
public class FeedbackPicPO extends Model<FeedbackPicPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_feedback_info.id
	*/
	@TableField("f_id")
	private Integer fId;

	/**
	* 类型(1:反馈,2:回复)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 图片地址
	*/
	@TableField("pic_url")
	private String picUrl;

	/**
	* 创建时间
	*/
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(delval = "1")
	@TableField("is_delete")
	private Integer isDelete;


	/**
	 * 是否复议(0:首次,1:复议)
	 */
	@TableField("repeat_flag")
	private Integer repeatFlag;

	/**
	 * 文件类型(0:图片,1:附件)
	 */
	@TableField("file_type")
	private Integer fileType;

}
