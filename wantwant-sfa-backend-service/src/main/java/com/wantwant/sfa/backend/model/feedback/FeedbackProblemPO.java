package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题类型
 *
 * @since 2022-12-28
 */
@Data
@TableName("sfa_feedback_problem")
public class FeedbackProblemPO extends Model<FeedbackProblemPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 问题类型
	*/
	@TableField("name")
	private String name;

	/**
	* 父id
	*/
	@TableField("parent_id")
	private Integer parentId;

	/**
	* 分类(1:一级,2:二级)
	*/
	@TableField("level")
	private Integer level;

	/**
	* 接单部门或岗位
	*/
	@TableField("position")
	private String position;

	/**
	* 协助部门CODE
	*/
	@TableField("dept_code")
	private String deptCode;

	/** 
	 * 业务分类(0:问题,1:投诉)
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
