package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题反馈质检配置
 *
 * @since 2023-07-19
 */
@Data
@TableName("sfa_feedback_quality_config")
public class FeedbackQualityConfigPO extends Model<FeedbackQualityConfigPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 类别
	*/
	@ApiModelProperty("类别")
	@TableField("type")
	private String type;

	/**
	* 问题类型
	*/
	@ApiModelProperty("问题类型")
	@TableField("name")
	private String name;

	/**
	* 扣分
	*/
	@ApiModelProperty("扣分")
	@TableField("base_score")
	private Integer baseScore;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
