package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题反馈质检明细
 *
 * @since 2023-07-19
 */
@Data
@TableName("sfa_feedback_quality_detail")
public class FeedbackQualityDetailPO extends Model<FeedbackQualityDetailPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_feedback_quality.id
	*/
	@TableField("q_id")
	private Integer qId;

	/**
	* sfa_feedback_quality_config.id
	*/
	@TableField("c_id")
	private Integer cId;

	/**
	* 扣分
	*/
	@TableField("deduction")
	private Integer deduction;

	/**
	* 原因备注
	*/
	@TableField("remark")
	private String remark;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
