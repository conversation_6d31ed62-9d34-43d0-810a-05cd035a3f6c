package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题反馈质检
 *
 * @since 2023-07-19
 */
@Data
@TableName("sfa_feedback_quality")
public class FeedbackQualityPO extends Model<FeedbackQualityPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_feedback_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* sfa_feedback_review.id
	*/
	@TableField("r_id")
	private Integer rId;

	/**
	* 质检状态(0:未质检,1:质检规范,2:质检不规范)
	*/
	@TableField("quality_status")
	private Integer qualityStatus;

	/**
	* 质检得分
	*/
	@TableField("score")
	private Integer score;

	/**
	* 质检人
	*/
	@TableField("quality_id")
	private String qualityId;

	/**
	* 质检人名称
	*/
	@TableField("quality_name")
	private String qualityName;

	/**
	* 质检时间
	*/
	@TableField("quality_time")
	private LocalDateTime qualityTime;

	/**
	* 扣分项(备注):扣分
	*/
	@TableField("notes")
	private String notes;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
