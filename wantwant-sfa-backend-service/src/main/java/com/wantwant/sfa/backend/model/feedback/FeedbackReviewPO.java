package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 问题反馈会办
 *
 * @since 2022-09-06
 */
@Data
@TableName("sfa_feedback_review")
public class FeedbackReviewPO extends Model<FeedbackReviewPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 编号
	*/
	@TableField("application_no")
	private String applicationNo;

	/** 
	 * 流水号
	 */
	@TableField("serial")
	private Integer serial;

	/** 
	 * 经办部门
	 */
	@TableField("dept_name")
	private String deptName;

	/**
	* 经办部门CODE
	*/
	@TableField("dept_code")
	private String deptCode;

	/**
	 * 经办人工号
	 */
	@TableField(value = "reviewer_id",strategy = FieldStrategy.IGNORED )
	private String reviewerId;

	/**
	 * 经办人名称
	 */
	@TableField(value = "reviewer_name",strategy = FieldStrategy.IGNORED)
	private String reviewerName;

	/**
	* 回复时间
	*/
	@TableField("review_time")
	private LocalDateTime reviewTime;

	/**
	* 回复意见
	*/
	@TableField("review_result")
	private String reviewResult;

	/**
	 * 时效时间
	 */
	@TableField("expiration_time")
	private LocalDateTime expirationTime;

	/**
	 * 结案工作小时数
	 */
	@TableField("hours")
	private Long hours;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

	/** 
	 * 复议标识(0:首次,1:复议) 
	 */
	@TableField("repeat_flag")
	private Integer repeatFlag;

	/** 
	 * 满意度评分
	 */
	@TableField("satisfaction_score")
	private Integer satisfactionScore;

	/**
	 * 详细评价
	 */
	@TableField("detailed_evaluation")
	private String detailedEvaluation;

	/**
	 * 评分时间
	 */
	@TableField("score_time")
	private LocalDateTime scoreTime;

	/** 
	 * 处理方式(1:转办,2:结案,3:驳回,4:退回) 
	 */
	@TableField("review_type")
	private Integer reviewType;
	

	public static void main(String[] args) {
		LocalDateTime dateTime = LocalDateTime.of(2023, 01, 12, 17, 39, 37);
		LocalDateTime dateTime1 = LocalDateTime.of(2023, 01, 13, 0, 0, 0);
		Long hours = Duration.between(dateTime, dateTime1).toHours();
		System.out.println(hours);
	}
}
