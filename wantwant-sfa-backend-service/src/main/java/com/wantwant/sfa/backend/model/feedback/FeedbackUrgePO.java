package com.wantwant.sfa.backend.model.feedback;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题反馈催单
 *
 * @since 2023-04-04
 */
@Data
@TableName("sfa_feedback_urge")
public class FeedbackUrgePO extends Model<FeedbackUrgePO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 编号
	*/
	@TableField("application_no")
	private String applicationNo;

	/**
	* 催单时间
	*/
	@TableField("urge_time")
	private LocalDateTime urgeTime;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
