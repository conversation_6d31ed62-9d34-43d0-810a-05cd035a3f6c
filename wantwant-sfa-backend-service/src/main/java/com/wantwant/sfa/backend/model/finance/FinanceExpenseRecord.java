package com.wantwant.sfa.backend.model.finance;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_finance_expense_record")
@ApiModel(value="sfa_finance_expense_record", description="财务报销记录")
public class FinanceExpenseRecord {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value="id")
    private Integer id;

    @ApiModelProperty(value = "大区")
    @Excel(name = "大区",replace = {"-_null"})
    @TableField(value="area_name")
    private String areaName;

    @ApiModelProperty(value = "分公司")
    @Excel(name = "分公司",replace = {"-_null"})
    @TableField(value="company_name")
    private String companyName;

    @ApiModelProperty(value = "营业所")
    @Excel(name = "营业所",replace = {"-_null"})
    @TableField(value="department_name")
    private String departmentName;

    @ApiModelProperty(value = "人员ID")
//    @Excel(name = "人员ID",replace = {"-_null"})
    @TableField(value="employee_info_id")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名",replace = {"-_null"})
    @TableField(value="employee_name")
    private String employeeName;

    @ApiModelProperty(value = "性别")
    @Excel(name = "性别",replace = {"-_null"})
    @TableField(value="gender")
    private String gender;

    @ApiModelProperty(value = "入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @Excel(name = "入职日期",replace = {"-_null"})
    @TableField("onboard_date")
    private LocalDateTime onboardDate;

    @ApiModelProperty(value = "岗位")
    @Excel(name = "岗位",replace = {"-_null"})
    @TableField("position_type")
    private String positionType;

    @ApiModelProperty(value = "职等")
    @Excel(name = "职等",replace = {"-_null"})
    @TableField("position_level")
    private String positionLevel;

    @ApiModelProperty(value = "财务记账月份")
    @Excel(name = "财务记账月份",replace = {"-_null"})
    @TableField("finance_month")
    private String financeMonth;

    @ApiModelProperty(value = "出差开始日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @Excel(name = "出差开始日期",replace = {"-_null"})
    @TableField("travel_start_date")
    private LocalDateTime travelStartDate;

    @ApiModelProperty(value = "出差结束日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @Excel(name = "出差结束日期",replace = {"-_null"})
    @TableField("travel_end_date")
    private LocalDateTime travelEndDate;

    @ApiModelProperty(value = "公出、出差申请单单号")
    @Excel(name = "公出、出差申请单单号",replace = {"-_null"})
    @TableField("apply_order_no")
    private String applyOrderNo;


    @ApiModelProperty(value = "费用类型")
    @Excel(name = "费用类型",replace = {"-_null"})
    @TableField("cost_type")
    private String costType;

    @ApiModelProperty(value = "出发城市")
    @Excel(name = "出发城市",replace = {"-_null"})
    @TableField("travel_origin_address")
    private String travelOriginAddress;

    @ApiModelProperty(value = "到达城市")
    @Excel(name = "到达城市",replace = {"-_null"})
    @TableField("travel_dest_address")
    private String travelDestAddress;

    @ApiModelProperty(value = "公里数")
    @Excel(name = "公里数",replace = {"-_null"})
    @TableField(value="kilometres")
    private Integer kilometres;

    @ApiModelProperty(value = "费用摘要-出差目的")
    @Excel(name = "费用摘要-出差目的",replace = {"-_null"})
    @TableField("cost_desc")
    private String costDesc;

    @ApiModelProperty(value = "城市间-火车费用")
    @Excel(name = "城市间-火车费用",replace = {"-_null"})
    @TableField("train_cost")
    private BigDecimal trainCost;

    @ApiModelProperty(value = "城市间-汽车费用")
    @Excel(name = "城市间-汽车费用",replace = {"-_null"})
    @TableField("bus_cost")
    private BigDecimal busCost;

    @ApiModelProperty(value = "城市间-飞机费用")
    @Excel(name = "城市间-飞机费用",replace = {"-_null"})
    @TableField("plane_cost")
    private BigDecimal planeCost;

    @ApiModelProperty(value = "城市间-出租车费")
    @Excel(name = "城市间-出租车费",replace = {"-_null"})
    @TableField("taxi_cost")
    private BigDecimal taxiCost;

    @ApiModelProperty(value = "城市间-油票")
    @Excel(name = "城市间-油票",replace = {"-_null"})
    @TableField("oil_ticket")
    private BigDecimal oilTicket;

    @ApiModelProperty(value = "城市间-油费-元/公里")
    @Excel(name = "城市间-油费-元/公里",replace = {"-_null"})
    @TableField("oil_cost_per_kilo")
    private BigDecimal oilCostPerKilo;

    @ApiModelProperty(value = "城市间-过路费")
    @Excel(name = "城市间-过路费",replace = {"-_null"})
    @TableField("toll_fee")
    private BigDecimal tollFee;

    @ApiModelProperty(value = "城市内-出租车费")
    @Excel(name = "城市内-出租车费",replace = {"-_null"})
    @TableField("city_inside_taxi_cost")
    private BigDecimal cityInsideTaxiCost;

    @ApiModelProperty(value = "城市内-公交/地铁费用")
    @Excel(name = "城市内公交/地铁费用",replace = {"-_null"})
    @TableField("city_inside_bus_cost")
    private BigDecimal cityInsideBusCost;

    @ApiModelProperty(value = "城市内-油票")
    @Excel(name = "城市内-油票",replace = {"-_null"})
    @TableField("city_inside_oil_ticket")
    private BigDecimal cityInsideOilTicket;

    @ApiModelProperty(value = "城市内-油费-元/公里")
    @Excel(name = "城市内-油费-元/公里",replace = {"-_null"})
    @TableField("city_inside_oil_cost_per_kilo")
    private BigDecimal cityInsideOilCostPerKilo;

    @ApiModelProperty(value = "城市内-过路费")
    @Excel(name = "城市内-过路费",replace = {"-_null"})
    @TableField("city_inside_toll_fee")
    private BigDecimal cityInsideTollFee;

    @ApiModelProperty(value = "发票号")
    @Excel(name = "发票号",replace = {"-_null"})
    @TableField("invoice_no")
    private String invoiceNo;

    @ApiModelProperty(value = "小计")
    @Excel(name = "小计",replace = {"-_null"})
    @TableField("total")
    private BigDecimal total;

    @ApiModelProperty(value = "住宿标准")
    @Excel(name = "住宿标准",replace = {"-_null"})
    @TableField("accommodation_standrad_fee")
    private String accommodationStandradFee;

    @ApiModelProperty(value = "住宿费")
    @Excel(name = "住宿费",replace = {"-_null"})
    @TableField("accommodation_fee")
    private BigDecimal accommodationFee;

    @ApiModelProperty(value = "餐费")
    @Excel(name = "餐费",replace = {"-_null"})
    @TableField("meals_fee")
    private BigDecimal mealsFee;

    @ApiModelProperty(value = "费用小计")
    @Excel(name = "费用小计",replace = {"-_null"})
    @TableField("total_cost")
    private BigDecimal totalCost;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注",replace = {"-_null"})
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "逻辑删除")
    @TableField("delete_flag")
    private int deleteFlag;



























}
