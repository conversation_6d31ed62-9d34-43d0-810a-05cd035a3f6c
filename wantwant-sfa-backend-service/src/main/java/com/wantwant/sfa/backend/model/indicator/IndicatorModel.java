package com.wantwant.sfa.backend.model.indicator;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 核心指标新增修改DTO.
 * @Auther: zhengxu
 * @Date: 2021/07/14/上午11:01
 */
@Data
@ApiModel(value = "核心指标model")
public class IndicatorModel extends PageParam {
    @ApiModelProperty("指标ID")
    private Integer indicatorId;
    @ApiModelProperty("大区ID")
    private String regionId;
    @ApiModelProperty("大区名称")
    private String regionName;
    @ApiModelProperty("核心指标")
    private String indicatorList;
    @ApiModelProperty("有效开始时间")
    private Date validStartTime;
    @ApiModelProperty("操作人工号")
    private String personId;
    @ApiModelProperty("操作人姓名")
    private String personName;
    @ApiModelProperty("状态")
    private Integer status;
}
