package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 主推品目标
 *
 * @since 2022-08-19
 */
@Data
@TableName("sfa_main_product")
public class MainProductPO extends Model<MainProductPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 主推品名称
	*/
	@TableField("product_name")
	private String productName;

	/**
	* 所属大类sfa_production_group.id
	*/
	@TableField("product_group")
	private Integer productGroup;

	/**
	* 每箱单价(废弃)
	*/
	@TableField("box_price")
	private BigDecimal boxPrice;

	/**
	* 每合伙人目标箱数(废弃)
	*/
	@TableField("employee_goal_box")
	private Integer employeeGoalBox;

	/**
	* 每合伙人目标金额(箱数*单价)(废弃)
	*/
	@TableField("employee_goal_amount")
	private BigDecimal employeeGoalAmount;

	/** 
	 * 总目标
	 */
	@TableField("amount")
	private BigDecimal amount;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
