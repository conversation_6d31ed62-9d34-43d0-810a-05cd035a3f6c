package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 分公司主推品目标
 *
 * @since 2022-08-22
 */
@Data
@TableName("sfa_organization_product_goal")
public class OrganizationProductGoalPO extends Model<OrganizationProductGoalPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	@TableField("business_group_id")
	private Integer businessGroupId;

	/**
	* 分公司code
	*/
	@TableField("company_organization_id")
	private String companyOrganizationId;

	/**
	* 分公司name
	*/
	@TableField("company_name")
	private String companyName;

	/**
	* sfa_main_product.id
	*/
	@TableField("product_id")
	private Integer productId;

	/**
	* 目标箱数(废弃)
	*/
	@TableField("goal_box")
	private Integer goalBox;

	/**
	 * 目标金额
	 */
	@TableField("goal_amount")
	private BigDecimal goalAmount;

	/**
	 * 目标金额
	 */
	@TableField("zb_goal_amount")
	private BigDecimal zbGoalAmount;

	/**
	 * 是否标杆分公司
	 */
	@TableField("is_company")
	private Integer isCompany;

	/**
	 * 标杆金额
	 */
	@TableField("sample_amount")
	private BigDecimal sampleAmount;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

}
