package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 组织主推品目标设置记录
 *
 * @since 2023-03-23
 */
@Data
@TableName("sfa_organization_product_record")
public class OrganizationProductRecordPO extends Model<OrganizationProductRecordPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/** 
	 * 是否确认(0:未确认,1:已确认)
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 创建人
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 修改人
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
