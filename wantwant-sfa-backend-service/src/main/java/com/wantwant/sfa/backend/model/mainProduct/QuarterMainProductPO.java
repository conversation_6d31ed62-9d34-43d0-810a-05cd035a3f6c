package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 季度主推品目标
 *
 * @since 2023-06-13
 */
@Data
@TableName("sfa_quarter_main_product")
public class QuarterMainProductPO extends Model<QuarterMainProductPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效年
	*/
	@TableField("year")
	private Integer year;

	/**
	* 生效季度
	*/
	@TableField("quarter")
	private Integer quarter;

	/** 
	 * 主推品名称 
	 */
	@TableField("product_name")
	private String productName;

	/** 
	 * 业务组sfa_business_group.id
	 */
	@TableField("business_group_id")
	private Integer businessGroupId;

	/**
	* 所属大类sfa_production_group.id
	*/
	@TableField("product_group")
	private Integer productGroup;

	/**
	* 总目标
	*/
	@TableField("amount")
	private BigDecimal amount;

	/**
	 * 绩效占比
	 */
	@TableField("amount_rate")
	private BigDecimal amountRate;

	/**
	 * 目标范围（org-组织/partner-合伙人)
	 */
	@TableField("target_scope")
	private String targetScope;

	/**
	 * 目标规则（min-设置最低目标/max-设置总目标)
	 */
	@TableField("target_rule")
	private String targetRule;

	/**
	 * 目标类型（quarter-季度目标/month-月度目标）
	 */
	@TableField("target_type")
	private String targetType;

	/**
	 * 最低目标
	 */
	@TableField("min_target")
	private BigDecimal minTarget;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

}
