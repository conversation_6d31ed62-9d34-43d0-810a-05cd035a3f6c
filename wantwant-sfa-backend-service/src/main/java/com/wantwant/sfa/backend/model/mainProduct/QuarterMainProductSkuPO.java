package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 季度主推品目标sku
 *
 * @since 2023-06-14
 */
@Data
@TableName("sfa_quarter_main_product_sku")
public class QuarterMainProductSkuPO extends Model<QuarterMainProductSkuPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sfa_quarter_main_product.id
	*/
	@TableField("product_id")
	private Integer productId;

	/**
	* sku
	*/
	@TableField("sku")
	private String sku;

	/**
	* sku名称
	*/
	@TableField("sku_name")
	private String skuName;

	/**
	* 整箱规格
	*/
	@TableField("full_case_spec")
	private String fullCaseSpec;

	/**
	* 口味
	*/
	@TableField("flavor")
	private String flavor;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

}
