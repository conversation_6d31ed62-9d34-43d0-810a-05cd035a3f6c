package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 季度组织主推品目标
 *
 * @since 2023-06-13
 */
@Data
@TableName("sfa_quarter_organization_product_goal")
public class QuarterOrganizationProductGoalPO extends Model<QuarterOrganizationProductGoalPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效年
	*/
	@TableField("year")
	private Integer year;

	/**
	* 生效季度
	*/
	@TableField("quarter")
	private Integer quarter;

	/**
	* 组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 组织name
	*/
	@TableField("organization_name")
	private String organizationName;

	/**
	* 业务组id
	*/
	@TableField("business_group_id")
	private Integer businessGroupId;

	/**
	* sfa_quarter_main_product.id
	*/
	@TableField("product_id")
	private Integer productId;

	/**
	* 目标金额
	*/
	@TableField("goal_amount")
	private BigDecimal goalAmount;

	/**
	* 是否标杆分公司(0:否,1:是)
	*/
	@TableField("is_company")
	private Integer isCompany;

	/**
	* 标杆金额
	*/
	@TableField("sample_amount")
	private BigDecimal sampleAmount;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

}
