package com.wantwant.sfa.backend.model.mainProduct;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 季度组织主推品目标设置记录
 *
 * @since 2023-06-14
 */
@Data
@TableName("sfa_quarter_organization_product_record")
public class QuarterOrganizationProductRecordPO extends Model<QuarterOrganizationProductRecordPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效年
	*/
	@TableField("year")
	private Integer year;

	/**
	 * 生效季度
	 */
	@TableField("quarter")
	private Integer quarter;

	/** 
	 * 业务组sfa_business_group.id
	 */
	@TableField("business_group_id")
	private Integer businessGroupId;

	/** 
	 * 是否确认(0:未确认,1:已确认)
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 创建人
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 修改人
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

}
