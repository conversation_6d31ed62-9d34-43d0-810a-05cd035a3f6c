package com.wantwant.sfa.backend.model.marketAndPersonnel;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("sfa_company_achievement_rule_detail")
public class CompanyAchievementRuleDetailPO extends Model<CompanyAchievementRuleDetailPO> {

	private static final long serialVersionUID = 6235294982850608073L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 规则ID(sfa_company_achievement_rule.id)
	*/
	@TableField("rule_id")
	private Integer ruleId;

	/**
	* 指标
	*/
	@TableField("`index`")
	private String index;

	/**
	* 界限(1:含下限不含上限,2:含上限不含下限)
	*/
	@TableField("definition")
	private Integer definition;

	/**
	* 数值类型(1:数字,100:百分比)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 开始范围
	*/
	@TableField("start_range")
	private BigDecimal startRange;

	/**
	* 结束范围
	*/
	@TableField("end_range")
	private BigDecimal endRange;

	/**
	* 比例
	*/
	@TableField("ratio")
	private BigDecimal ratio;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
