package com.wantwant.sfa.backend.model.marketAndPersonnel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 分公司总监合伙人绩效适用分公司
 *
 * @date 4/19/22 4:25 PM
 * @version 1.0
 */
@Data
@TableName("sfa_company_achievement_rule_organization")
public class CompanyAchievementRuleOrganizationPO extends Model<CompanyAchievementRuleOrganizationPO> {

	private static final long serialVersionUID = 7262325281348968092L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 规则ID(sfa_company_achievement_rule.id)
	*/
	@TableField("rule_id")
	private Integer ruleId;

	/**
	* 适用分公司
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
