package com.wantwant.sfa.backend.model.marketAndPersonnel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 分公司绩效规则
 *
 * @date 4/19/22 2:32 PM
 * @version 1.0
 */
@Data
@TableName("sfa_company_achievement_rule")
public class CompanyAchievementRulePO extends Model<CompanyAchievementRulePO> {

	private static final long serialVersionUID = -4336000487766299128L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 岗位(1:合伙人,2:总监)
	*/
	@TableField("position")
	private Integer position;

	/**
	* 规则名称
	*/
	@TableField("`name`")
	private String name;

	/**
	* 规则简介
	*/
	@TableField("`describe`")
	private String describe;

	/**
	* A指标:目标达成率(考核占比)
	*/
	@TableField("a_index")
	private BigDecimal aIndex;

	/**
	* B指标:人效(考核占比)
	*/
	@TableField("b_index")
	private BigDecimal bIndex;

	/**
	* 开始时间
	*/
	@TableField("start_date")
	private LocalDate startDate;

	/**
	 * 结束时间
	 */
	@TableField("end_date")
	private LocalDate endDate;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

}
