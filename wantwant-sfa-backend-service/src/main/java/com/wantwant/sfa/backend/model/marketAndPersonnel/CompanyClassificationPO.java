package com.wantwant.sfa.backend.model.marketAndPersonnel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 分公司考核分类
 *
 * @since 2022-04-18
 */
@Data
@ToString
@TableName("sfa_company_classification")
public class CompanyClassificationPO extends Model<CompanyClassificationPO> {

	private static final long serialVersionUID = 2619689922959295704L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 分公司组织ID
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 生效时间
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	/**
	* 考核分类(A,B,C)
	*/
	@TableField("classification")
	private String classification;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
