package com.wantwant.sfa.backend.model.marketAndPersonnel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合伙人薪资方案
 *
 * @date 4/20/22 11:24 AM
 * @version 1.0
 */
@Data
@TableName("sfa_employee_salary")
@ToString
public class EmployeeSalaryPO extends Model<EmployeeSalaryPO> {

	private static final long serialVersionUID = -6018765558100176494L;

	@TableId(value = "id")
	private Integer id;

	/**
	 * 薪资结构ID(sfa_employee_salary_structure.id)
	 */
	@TableField(value = "structure_id")
	private Integer structureId;

	/** 
	 * 岗位(1:合伙人,2:总监)
	 */
	@TableField(value = "position")
	private Integer position;

	/**
	* 分公司组织ID
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 员工ID(sfa_employee_info.id)
	*/
	@TableField("employee_info_id")
	private Integer employeeInfoId;

	/**
	* 合伙人姓名
	*/
	@TableField("employee_name")
	private String employeeName;

	/**
	 * 薪资方案
	 * 1-底薪+奖金，自动匹配分公司标准底薪和标准绩效奖金包；
	 * 2-仅有底薪，取导入的底薪、奖金为0；
	 * 3-签约旺旺，取导入的底薪、奖金为0；
	 * 4-兼职，底薪及奖金均为0
	 * 5-企业合伙人
	 * 6-承揽
	 * 7-区域经理
	 * 8-总监
	*/
	@TableField("salary_describe")
	private String salaryDescribe;

	/**
	 * 薪资方案等级AB
	 */
	@TableField("salary_level")
	private String salaryLevel;

	/**
	* 合伙人底薪
	*/
	@TableField("employee_base_salary")
	private BigDecimal employeeBaseSalary;

	/** 
	 * 合伙人奖金
	 */
	@TableField("employee_bonus")
	private BigDecimal employeeBonus;

	/**
	 * 合伙人岗位津贴
	 */
	@TableField("employee_allowance")
	private BigDecimal employeeAllowance;

	/** 
	 * 签约公司 
	 */
	@TableField("contract_company")
	private String contractCompany;

	/** 
	 * 代支付公司
	 */
	@TableField("payment_company")
	private String paymentCompany;

	/**
	* 开始时间
	*/
	@TableField("start_date")
	private LocalDate startDate;

	/**
	* 结束时间
	*/
	@TableField("end_date")
	private LocalDate endDate;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	private BigDecimal fullRiskFee;

	private BigDecimal socialSecurityBase;

	private BigDecimal travelExpenses;

	private Integer paymentType;
}
