package com.wantwant.sfa.backend.model.marketAndPersonnel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合伙人薪资结构
 *
 * @date 4/20/22 11:27 AM
 * @version 1.0
 */
@Data
@TableName("sfa_employee_salary_structure")
public class EmployeeSalaryStructurePO extends Model<EmployeeSalaryStructurePO> {

	private static final long serialVersionUID = 8414866805903378681L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 分公司组织ID
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 标准底薪
	*/
	@TableField("base_salary")
	private BigDecimal baseSalary;

	/**
	* 标准奖金包
	*/
	@TableField("bonus")
	private BigDecimal bonus;

	/**
	 * 社保基数
	 */
	@TableField("social_security_base")
	private BigDecimal socialSecurityBase;

	/** 
	 * 岗位津贴
	 */
	@TableField("allowance")
	private BigDecimal allowance;

	@TableField("travel_expenses")
	private BigDecimal travelExpenses;

	/**
	 * 岗位(101:全职合伙人,102:承揽合伙人,201:区域总监,401:区域经理)
	 */
	@TableField("position")
	private Integer position;

	/**
	 * 等级
	 */
	@TableField("grade")
	private String grade;

	/**
	* 开始时间
	*/
	@TableField("start_date")
	private LocalDate startDate;

	/**
	* 结束时间
	*/
	@TableField("end_date")
	private LocalDate endDate;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;


	private BigDecimal fullRiskFee;
}
