package com.wantwant.sfa.backend.model.marketAndPersonnel;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_company_manager_salary")
@ApiModel(value="sfa_company_manager_salary", description="总监薪资表")
public class ManagerSalaryModel {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField(value="id")
    private Integer id;

    @ApiModelProperty(value = "薪资月份")
    @Excel(name = "薪资月份",replace = {"-_null"})
    @TableField(value="month")
    private String month;

    @ApiModelProperty(value = "岗位")
    @Excel(name = "岗位",replace = {"-_null"})
    @TableField(value="position_type_name")
    private String positionTypeName;

    @ApiModelProperty(value = "分公司")
//    @Excel(name = "分公司",replace = {"-_null"})
    @TableField(value="company")
    private String company;

    @Excel(name = "memberKey",replace = {"-_null"})
    @TableField(value = "member_key")
    private Long memberKey;

    @ApiModelProperty(value = "工号")
    @Excel(name = "工号",replace = {"-_null"})
    @TableField(value="employee_id")
    private String  employeeId;

    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名",replace = {"-_null"})
    @TableField(value="employee_name")
    private String employeeName;

    @ApiModelProperty(value = "身份证号")
    @Excel(name = "身份证号",replace = {"-_null"})
    @TableField(value="card_number")
    private String cardNumber;

    @ApiModelProperty(value = "核定薪资")
    @Excel(name = "核定薪资",replace = {"-_null"})
    @TableField(value="approved_salary")
    private BigDecimal approvedSalary;

    @ApiModelProperty(value = "岗位津贴")
    @Excel(name = "岗位津贴",replace = {"-_null"})
    @TableField(value="post_allowance")
    private BigDecimal postAllowance;

    @ApiModelProperty(value = "绩效奖金")
    @Excel(name = "绩效奖金",replace = {"-_null"})
    @TableField(value="achievement_bonus")
    private BigDecimal achievementBonus;

    @ApiModelProperty(value = "补发")
    @Excel(name = "补发",replace = {"-_null"})
    @TableField(value="responsibility_award")
    private BigDecimal responsibilityAward;

    @ApiModelProperty(value = "补扣")
    @Excel(name = "补扣",replace = {"-_null"})
    @TableField(value="supplementary_deduction")
    private BigDecimal supplementaryDeduction;

    @ApiModelProperty(value = "其它津贴合计")
    @Excel(name = "其它津贴合计",replace = {"-_null"})
    @TableField(value="other_allowance_total")
    private BigDecimal otherAllowanceTotal;

    @ApiModelProperty(value = "补贴合计")
    @Excel(name = "补贴合计",replace = {"-_null"})
    @TableField(value="other_payables_total")
    private BigDecimal otherPayablesTotal;

    @ApiModelProperty(value = "扣款合计")
    @Excel(name = "扣款合计",replace = {"-_null"})
    @TableField(value="deduction_other")
    private BigDecimal deductionOther;

    @ApiModelProperty(value = "应付薪资")
    @Excel(name = "应付薪资",replace = {"-_null"})
    @TableField(value="salaries_payable")
    private BigDecimal salariesPayable;

    @ApiModelProperty(value = "税后扣款合计")
    @Excel(name = "税后扣款合计",replace = {"-_null"})
    @TableField(value="after_tax_deduction_total")
    private BigDecimal  afterTaxDeductionTotal;

    @ApiModelProperty(value = "企业承担社保公积金")
    @Excel(name = "企业承担社保公积金",replace = {"-_null"})
    @TableField(value="enterprise_social_fund")
    private BigDecimal enterpriseSocialFund;

    @ApiModelProperty(value = "代扣社保")
    @Excel(name = "个人社保",replace = {"-_null"})
    @TableField(value="withhold_social")
    private BigDecimal withholdSocial;

    @ApiModelProperty(value = "代扣公积金")
    @Excel(name = "个人公积金",replace = {"-_null"})
    @TableField(value="withhold_fund")
    private BigDecimal withholdFund;

    @ApiModelProperty(value = "代扣个税")
    @Excel(name = "个人所得税",replace = {"-_null"})
    @TableField(value="withhold_tax")
    private BigDecimal withholdTax;

    @ApiModelProperty(value = "实发薪资")
    @Excel(name = "实发薪资",replace = {"-_null"})
    @TableField(value="salaries_actual")
    private BigDecimal salariesActual;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注",replace = {"-_null"})
    @TableField(value="remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @Excel(name = "创建时间",replace = {"-_null"})
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    @TableField(value="create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人",replace = {"-_null"})
    @TableField(value="create_person")
    private String createPerson;

    @TableField(value="update_time")
    private LocalDateTime updateTime;

    @TableField(value="update_person")
    private String updatePerson;

    @TableField("delete_flag")
    private int deleteFlag;

}
