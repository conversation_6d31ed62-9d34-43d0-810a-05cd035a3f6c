package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 会议批注
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@TableName("sfa_meeting_annotation")
@ApiModel(value = "SfaMeetingAnnotation对象", description = "会议批注")
@Data
public class MeetingAnnotationPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "annotation_id", type = IdType.AUTO)
    private Long annotationId;

    @ApiModelProperty("类型(1.周会承诺 2.承诺分配)")
    private Integer type;

    @ApiModelProperty("关联外键")
    private Long foreignKey;

    @ApiModelProperty("批注")
    private String annotation;




}
