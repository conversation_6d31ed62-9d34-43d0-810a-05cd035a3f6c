package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 会议稽核
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@TableName("sfa_meeting_audit")
@ApiModel(value = "SfaMeetingAudit对象", description = "会议稽核")
@Data
public class MeetingAuditPO implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "audit_id", type = IdType.AUTO)
    private Long auditId;

    @ApiModelProperty("sfa_meeting_info主键")
    private Integer infoId;

    @ApiModelProperty("类型(0.未稽核 1.人员稽核 2.会议照片)")
    private Integer type;

    @ApiModelProperty("稽核状态:1.正常 2.异常")
    private Integer auditResult;

    @ApiModelProperty("异常原因")
    private String reason;

    @ApiModelProperty("sfa_meeting_record主键")
    private Integer recordId;

    @ApiModelProperty("稽核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("稽核人员工号")
    private String auditUserId;

    @ApiModelProperty("稽核人员名称")
    private String auditUserName;

    @ApiModelProperty("是否删除")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}
