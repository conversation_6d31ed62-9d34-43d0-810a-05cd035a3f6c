package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会议附件表
 *
 * @since 2024-02-21
 */
@Data
@TableName("sfa_meeting_file")
public class MeetingFilePO extends Model<MeetingFilePO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 外键
	*/
	@TableField("w_id")
	private Integer wId;

	/**
	* 类型(1:会议信息附件,2:会议照片,3:会议纪要附件)
	*/
	@TableField("type")
	private Integer type;

	/**
	* 附件链接
	*/
	@TableField("url")
	private String url;

	/**
	* 附件名称
	*/
	@TableField("name")
	private String name;

	/**
	* 附件格式
	*/
	@TableField("format")
	private String format;

	/**
	* 文件大小(单位kb)
	*/
	@TableField("size")
	private Integer size;

	/**
	* 创建人工号
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 创建人名称
	*/
	@TableField("create_name")
	private String createName;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

}
