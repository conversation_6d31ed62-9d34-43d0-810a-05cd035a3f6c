package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线下会议信息
 *
 * @since 2024-02-21
 */
@Data
@TableName("sfa_meeting_info")
public class MeetingInfoPO extends Model<MeetingInfoPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "info_id")
	private Integer infoId;

	/**
	* 会议类型大类
	*/
	@TableField("category")
	private String category;

	/**
	* 会议类型小类
	*/
	@TableField("subclass")
	private String subclass;

	/**
	* 组织ID
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 会议开始时间
	*/
	@TableField("start_time")
	private LocalDateTime startTime;

	/**
	* 会议结束时间
	*/
	@TableField("end_time")
	private LocalDateTime endTime;

	/**
	* 会议主题
	*/
	@TableField("topic")
	private String topic;

	/**
	* 会议方式
	*/
	@TableField("mode")
	private String mode;

	/**
	* 线上会议链接
	*/
	@TableField(value="link",strategy = FieldStrategy.IGNORED)
	private String link;

	/**
	* 会议地址-省
	*/
	@TableField(value="province",strategy = FieldStrategy.IGNORED)
	private String province;

	/**
	* 会议地址-市
	*/
	@TableField(value="city",strategy = FieldStrategy.IGNORED)
	private String city;

	/**
	* 会议地址-区
	*/
	@TableField(value="district",strategy = FieldStrategy.IGNORED)
	private String district;

	/**
	* 会议地址-街道
	*/
	@TableField(value="street",strategy = FieldStrategy.IGNORED)
	private String street;

	/**
	* 会议地址-详细地址
	*/
	@TableField(value="address",strategy = FieldStrategy.IGNORED)
	private String address;

	/**
	 * 经度
	 */
	@TableField(value="longitude",strategy = FieldStrategy.IGNORED)
	private String longitude;

	/**
	 * 纬度
	 */
	@TableField(value="latitude",strategy = FieldStrategy.IGNORED)
	private String latitude;

	/**
	 * 会议纪要负责人岗位
	 */
	@TableField("leader_position_id")
	private String leaderPositionId;

	/**
	* 会议纪要负责人工号
	*/
	@TableField("leader_id")
	private String leaderId;

	/**
	* 会议纪要负责人名称
	*/
	@TableField("leader_name")
	private String leaderName;

	/**
	* 会议文件
	*/
	@TableField("papers")
	private String papers;

	/**
	* 备注
	*/
	@TableField("remark")
	private String remark;

	/**
	* 业务组(逗号分隔)
	*/
	@TableField("business_groups")
	private String businessGroups;

	/**
	* 战区CODE(逗号分隔)
	*/
	@TableField("area_codes")
	private String areaCodes;

	/**
	* 大区CODE(逗号分隔)
	*/
	@TableField("varea_codes")
	private String vareaCodes;

	/**
	* 省区CODE(逗号分隔)
	*/
	@TableField("province_codes")
	private String provinceCodes;

	/**
	* 分公司CODE(逗号分隔)
	*/
	@TableField("company_codes")
	private String companyCodes;

	/**
	* 营业所CODE(逗号分隔)
	*/
	@TableField("department_codes")
	private String departmentCodes;

	/**
 	* 接收人角色(1.总部,5.区域总监,6.总督导,7.区域经理,9.大区总监,10.省区总监)
	*/
	@TableField("roles")
	private String roles;

	/**
	* 权限组ID(sfa_permission_employee.id)
	*/
	@TableField("permission_group_id")
	private String permissionGroupId;

	/**
	* 会议状态(0:未开始,1:已取消,2:已完成,3:终止)
	 * 已完成(提交会议纪要)
	 * 终止(00441211可操作)
	*/
	@TableField("status")
	private Integer status;

	/**
	* 发起人工号
	*/
	@TableField("create_by")
	private String createBy;

	/**
	* 发起人名称
	*/
	@TableField("create_name")
	private String createName;

	/**
	* 修改人工号
	*/
	@TableField("update_by")
	private String updateBy;

	/**
	* 修改人名称
	*/
	@TableField("update_name")
	private String updateName;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	@TableField("actual_start_time")
	private LocalDateTime actualStartTime;
	@TableField("actual_end_time")
	private LocalDateTime actualEndTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

	@TableField(value="address_codes",fill = FieldFill.INSERT_UPDATE)
	private String addressCodes;

	@TableField("version")
	private String version;


	private String startEarlyReason;

	private Integer tag;

	private Integer auditStatus;


}
