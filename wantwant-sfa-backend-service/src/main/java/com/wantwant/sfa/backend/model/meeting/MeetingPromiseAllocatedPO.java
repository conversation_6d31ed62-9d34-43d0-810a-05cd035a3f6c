package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 业绩承诺分配
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@TableName("sfa_meeting_promise_allocated")
@ApiModel(value = "SfaMeetingPromiseAllocated对象", description = "业绩承诺分配")
@Data
public class MeetingPromiseAllocatedPO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "allocated_id", type = IdType.AUTO)
    private Long allocatedId;

    @ApiModelProperty("会议承诺ID")
    private Long promiseId;

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty("客户名称")
    private String employeeName;

    @ApiModelProperty("类型(1.合伙人 2.未建档 3.无法确定)")
    private Integer type;

    @ApiModelProperty("合伙人memberKey")
    private Long memberKey;

    @ApiModelProperty("业绩承诺")
    private BigDecimal promisePerformance;




}
