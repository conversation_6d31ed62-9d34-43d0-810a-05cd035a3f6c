package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 会议承诺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@TableName("sfa_meeting_promise")
@Data
@ApiModel(value = "SfaMeetingPromise对象", description = "会议承诺")
public class MeetingPromisePO extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "promise_id", type = IdType.AUTO)
    private Long promiseId;

    @ApiModelProperty("sfa_meeting_info主键")
    private Integer infoId;

    @ApiModelProperty("组织ID")
    private String organizationId;

    @ApiModelProperty("产品组ID")
    private Integer businessGroup;

    @ApiModelProperty("年")
    private String year;

    @ApiModelProperty("月")
    private String month;

    @ApiModelProperty("第几周")
    private Integer weeks;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("承诺业绩")
    private BigDecimal promisePerformance;



}
