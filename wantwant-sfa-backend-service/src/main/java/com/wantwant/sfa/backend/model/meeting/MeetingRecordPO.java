package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下会议人员
 *
 * @since 2024-02-21
 */
@Data
@TableName("sfa_meeting_record")
public class MeetingRecordPO extends Model<MeetingRecordPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "record_id")
	private Integer recordId;

	/**
	* sfa_meeting_info.id
	*/
	@TableField("info_id")
	private Integer infoId;

	/** 
	 * 身份标识
	 * 0:参会人(打卡)
	 * 1:生成的上级b(无需打卡)
	 *
	 */
	@TableField("role_flag")
	private Integer roleFlag;

	/**
	* 参会人id
	*/
	@TableField("employee_id")
	private String employeeId;

	/**
	* 参会人名称
	*/
	@TableField("employee_name")
	private String employeeName;

	/**
	* 参会人组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 参会人组织name
	*/
	@TableField("organization_name")
	private String organizationName;

	/**
	* 接收状态(0:未接收,1:确认,2:拒绝)
	*/
	@TableField("receive_status")
	private Integer receiveStatus;

	/**
	 * 拒绝原因
	 */
	@TableField(value = "reject_reasons",strategy=FieldStrategy.IGNORED)
	private String rejectReasons;

	/**
	* 签到状态(0:未完成,1:已完成)
	*/
	@TableField("check_status")
	private Integer checkStatus;

	/**
	* 签到图片
	*/
	@TableField("check_pic")
	private String checkPic;

	@TableField("sign_up_pic_url")
	private String signUpPicUrl;

	@TableField("face_similar_score")
	private BigDecimal faceSimilarScore;

	/**
	* 签到时间
	*/
	@TableField("check_time")
	private LocalDateTime checkTime;

	/**
	* 当前作业问题
	*/
	@TableField("problem")
	private String problem;

	/**
	* 改善意见
	*/
	@TableField("suggestions")
	private String suggestions;

	/**
	* 主管要求
	*/
	@TableField("requirements")
	private String requirements;

	/**
	* 本人承诺
	*/
	@TableField("commitment")
	private String commitment;

	/**
	* 其他
	*/
	@TableField("others")
	private String others;

	/**
	* 批注
	*/
	@TableField("notes")
	private String notes;

	/**
	* 个推推送状态(0:未发,1:已发)
	*/
	@TableField("getui_status")
	private Integer getuiStatus;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;


	@TableField("sales_commitment_promise")
	private BigDecimal salesCommitmentPromise;

	@TableField("transaction_customer_count_promise")
	private BigDecimal transactionCustomerCountPromise;

	@TableField("customer_order_price_promise")
	private BigDecimal customerOrderPricePromise;

	private Integer businessGroup;

	@TableField(value = "question",strategy = FieldStrategy.IGNORED)
	private String question;
	@TableField(value = "solution",strategy = FieldStrategy.IGNORED)
	private String solution;

	private String longitude;

	private String latitude;

	private String province;

	private String city;

	private String district;

	private String street;

	private Long memberKey;


	@ApiModelProperty("是否可报销")
	private Integer reimbursed;

	@ApiModelProperty("是否主岗岗位")
	private Integer primaryPosition;

	@ApiModelProperty("是否迟到")
	private Integer lateFlag;
}
