package com.wantwant.sfa.backend.model.meeting;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线下会议会议纪要
 *
 * @since 2024-02-21
 */
@Data
@TableName("sfa_meeting_summary")
public class MeetingSummaryPO extends Model<MeetingSummaryPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "summary_id")
	private Integer summaryId;

	/**
	* sfa_meeting_info.info_id
	*/
	@TableField("info_id")
	private Integer infoId;

	/**
	* 标题
	*/
	@TableField("title")
	private String title;

	/**
	* 正文内容
	*/
	@TableField("content")
	private String content;

	/**
	* 统一要求
	*/
	@TableField("uniform_requirements")
	private String uniformRequirements;

	/**
	* 问题反馈
	*/
	@TableField("problem_feedback")
	private String problemFeedback;

	@TableField("summary")
	private String summary;

	/**
	* 提交状态(0:暂存,1:提交)
	*/
	@TableField("commit_status")
	private Integer commitStatus;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 是否删除(1:删除)
	 */
	@TableLogic(value = "0", delval = "1")
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 提交时间
	 */
	private LocalDateTime commitTime;

}
