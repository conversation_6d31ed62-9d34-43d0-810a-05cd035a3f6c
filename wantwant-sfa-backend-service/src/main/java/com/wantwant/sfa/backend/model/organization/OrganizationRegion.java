package com.wantwant.sfa.backend.model.organization;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrganizationRegion {

    
    @ApiModelProperty(value = "业务名称",required = true)
    private String employeeName; 
   
    @ApiModelProperty(value = "组织ID",required = true)
    private String organizationId; 
    
    @ApiModelProperty(value = "组织类型",required = true)
    private String organizationType; 
    
    @ApiModelProperty(value = "岗位Id",required = true)
    private String positionId; 
    
    @ApiModelProperty(value = "组织名称",required = true)
    private String organizationName; 
    
    @ApiModelProperty(value = "业绩金额目标",required = true) 
    private BigDecimal saleGoal;
    
    @ApiModelProperty(value = "月业绩金额",required = true) 
    private BigDecimal monthPerformance;
    
    @ApiModelProperty(value = "达成",required = true) 
    private BigDecimal attainment;    
    
    @ApiModelProperty(value = "职位配置",required = true) 
    private int positionNum;
    
    @ApiModelProperty(value = "在职人员数量",required = true) 
    private int onboardNum;    
    
    @ApiModelProperty(value = "组织区域",required = true)
    private List<Region> regionList; 
  
}
