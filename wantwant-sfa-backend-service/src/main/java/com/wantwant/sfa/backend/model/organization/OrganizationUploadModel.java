package com.wantwant.sfa.backend.model.organization;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Description: 导入组织用model。
 * @Auther: zhengxu
 * @Date: 2021/09/28/上午9:38
 */
@Data
@ApiModel(value = "组织信息", description = "")
public class OrganizationUploadModel {
    @Excel(name = "大区")
    private String areaName;
    @Excel(name = "分公司")
    private String companyName;
    @Excel(name = "营业所")
    private String branchName;
    @Excel(name = "造旺CEO编制数")
    private String positionCount;
}
