package com.wantwant.sfa.backend.model.organization;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Region {
    
	private int RegionId;

	@ApiModelProperty(value = "区域类型")
	private String administrationName;      
    
	@ApiModelProperty(value = "区域名称(详细名称)")
	private String areaName;      
    
	@ApiModelProperty(value = "区域Id")
	private String code;      
    
	@ApiModelProperty(value = "颜色")
	private String color;      
    
	@ApiModelProperty(value = "区域名称")
	private String currentName;      
   
	//前端要求，必须要存在一个单独的对象,来放4个值
	@ApiModelProperty(value = "区域经纬度")
	private Center center;      

	
}
