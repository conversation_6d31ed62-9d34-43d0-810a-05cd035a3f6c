package com.wantwant.sfa.backend.model.organizationGoal;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 登录验证码发送表
 *
 * @since 2024-06-27
 */
@Data
@TableName("sfa_captcha_code")
public class CaptchaCodePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("mobile")
    private String mobile;

    @TableField("code")
    private String code;

    @TableField("type")
    private Integer type;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("delete_flag")
    private Integer deleteFlag;

    @TableField("end_valid_date")
    private LocalDateTime endValidDate;

}
