package com.wantwant.sfa.backend.model.organizationGoal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.wantwant.sfa.backend.authorization.util.EncodeUtil;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 登录用户表
 *
 * @since 2022-11-07
 */
@Data
@TableName("sfa_login_user")
public class LoginUserPO extends Model<LoginUserPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 账号
	*/
	@TableField("account")
	private String account;

	/**
	* 密码
	*/
	@TableField("password")
	private String password;

	/**
	* sfa_employee_info.id
	*/
	@TableField("emp_id")
	private Integer empId;

	/**
	* 状态(0:正常,1:停用)
	*/
	@TableField("status")
	private Integer status;


	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(0.否;1.是)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	public boolean isPasswordEquals(String password) {
		return EncodeUtil.encodeMD5(password).equals(this.password);
	}

}
