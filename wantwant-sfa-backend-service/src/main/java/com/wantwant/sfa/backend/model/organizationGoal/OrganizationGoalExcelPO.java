package com.wantwant.sfa.backend.model.organizationGoal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 组织目标导入excel
 *
 * @since 2022-08-23
 */
@Data
@TableName("sfa_organization_goal_excel")
public class OrganizationGoalExcelPO extends Model<OrganizationGoalExcelPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效年月
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;


	private Integer businessGroupId;

	/** 
	 * 开始日期 
	 */
	@TableField("start_date")
	private LocalDate startDate;

	/** 
	 * 分公司目标截止日期 
	 */
	@TableField("company_end_date")
	private LocalDate companyEndDate;

	/** 
	 * 营业所目标截止日期
	 */
	@TableField("department_end_date")
	private LocalDate departmentEndDate;

	/**
	 * 是否确认(0:未确认,1:已确认)
	 */
	@TableField("state")
	private Integer state;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
