package com.wantwant.sfa.backend.model.organizationGoal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 组织目标
 *
 * @since 2022-08-23
 */
@Data
@TableName("sfa_organization_goal")
public class OrganizationGoalPO extends Model<OrganizationGoalPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效年月
	*/
	@TableField("effective_date")
	private LocalDate effectiveDate;

	@TableField("business_group_id")
	private Integer businessGroupId;

	/**
	* sfa_organization_goal_excel.id
	*/
	@TableField("excel_id")
	private Integer excelId;

	/**
	* 组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 组织name
	*/
	@TableField("organization_name")
	private String organizationName;

	/**
	* 全品项业绩目标
	*/
	@TableField("trans_amount")
	private BigDecimal transAmount;

	/**
	* 合伙人在岗目标
	*/
	@TableField("partner_onjob")
	private Integer partnerOnjob;

	/**
	* 合伙人编制
	*/
	@TableField("partner_num")
	private Integer partnerNum;

	/**
	 * 合伙人管控数量
	 */
	@TableField("partner_controlled_num")
	private Integer partnerControlledNum;

	/**
	 * 区域经理编制
	 */
	@TableField("department_num")
	private Integer departmentNum;

	/** 
	 * 区域经理在岗目标
	 */
	@TableField("department_onjob")
	private Integer departmentOnjob;

	/**
	 * 区域经理管控数量
	 */
	@TableField("department_controlled_num")
	private Integer departmentControlledNum;

	/**
	* 销售预估金额
	*/
	@TableField("estimate_price")
	private BigDecimal estimatePrice;

	/**
	* 人均业绩预警
	*/
	@TableField("capita_sale_alerted")
	private BigDecimal capitaSaleAlerted;

	/**
	* 在岗率预警
	*/
	@TableField("onjob_rate_alerted")
	private BigDecimal onjobRateAlerted;

	/**
	* 离职率预警
	*/
	@TableField("offjob_rate_alerted")
	private BigDecimal offjobRateAlerted;

	/**
	* 库存盘点率预警
	*/
	@TableField("inventorycheck_rate_alerted")
	private BigDecimal inventorycheckRateAlerted;

	/**
	* 小标市场覆盖率预警
	*/
	@TableField("smallmarketcoverage_rate_alerted")
	private BigDecimal smallmarketcoverageRateAlerted;

	/**
	* 小标战略目标达成率预警
	*/
	@TableField("smallmarket_rate_alerted")
	private BigDecimal smallmarketRateAlerted;

	/**
	* 合伙人0开单率
	*/
	@TableField("partner_order0_rate")
	private BigDecimal partnerOrder0Rate;

	/** 
	 * 全品项目标(全品项目标导入)
	 */
	@TableField("trans_amount_import")
	private BigDecimal transAmountImport;

	/** 
	 * 乳品目标(全品项目标导入)
	 */
	@TableField("dairy_amount_import")
	private BigDecimal dairyAmountImport;

	/** 
	 * 饮品目标(全品项目标导入)
	 */
	@TableField("beverage_amount_import")
	private BigDecimal beverageAmountImport;

	/** 
	 * 是否确认(0:未确认,1:已确认) 
	 */
	@TableField("state")
	private Integer state;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	@TableField("created_time")
	private LocalDateTime createdTime;

	@TableField("updated_time")
	private LocalDateTime updatedTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/** 
	 * 区域总监-编制
	 */
	@TableField("company_num")
	private Integer companyNum;

	/** 
	 * 区域总监-在岗目标数量
	 */
	@TableField("company_onjob")
	private Integer companyOnjob;

	/** 
	 * 区域总监-招聘管控数量
	 */
	@TableField("company_controlled_num")
	private Integer companyControlledNum;

	/**
	 * 人均业绩目标
	 */
	@TableField("itemsSupplyTotal_cm_avg_target")
	private BigDecimal itemsSupplyTotalCmAvgTarget;

	/**
	 * 人均业绩预警
	 */
	@TableField("itemsSupplyTotal_cm_avg_warning")
	private BigDecimal itemsSupplyTotalCmAvgWarning;


	/**
	 * 合伙人0开单率目标
	 */
	@TableField("partner_0_order_rate_target")
	private BigDecimal partner0OrderRateTarget;

	/**
	 * 合伙人0开单率预警
	 */
	@TableField("partner_0_order_rate_warning")
	private BigDecimal partner0OrderRateWarning;

	/**
	 * 小标市场覆盖率目标
	 */
	@TableField("small_market_coverage_rate_target")
	private BigDecimal smallMarketCoverageRateTarget;

	/**
	 * 小标市场覆盖率预警
	 */
	@TableField("small_market_coverage_rate_target_warning")
	private BigDecimal smallMarketCoverageRateWarning;

	/**
	 * 小标战略目标达成率目标
	 */
	@TableField("small_market_strategic_goals_achievement_rate_target")
	private BigDecimal smallMarketStrategicGoalsAchievementRateTarget;

	/**
	 * 小标战略目标达成率预警
	 */
	@TableField("small_market_strategic_goals_achievement_rate_warning")
	private BigDecimal smallMarketStrategicGoalsAchievementRateWarning;

	/**
	 * 在岗率目标
	 */
	@TableField("partner_onboard_rate_target")
	private BigDecimal partnerOnboardRateTarget;

	/**
	 * 在岗率预警
	 */
	@TableField("partner_onboard_rate_warning")
	private BigDecimal partnerOnboardRateWarning;

	/**
	 * 离职率目标
	 */
	@TableField("partner_off_rate_target")
	private BigDecimal partnerOffRateTarget;

	/**
	 * 离职率预警
	 */
	@TableField("partner_off_rate_warning")
	private BigDecimal partnerOffRateWarning;

	/**
	 * 库存盘点率目标
	 */
	@TableField("inventory_check_rate_target")
	private BigDecimal inventoryCheckRateTarget;

	/**
	 * 库存盘点率预警
	 */
	@TableField("inventory_check_rate_warning")
	private BigDecimal inventoryCheckRateWarning;

	/**
	 * 主推品优酪乳业绩达成率目标
	 */
	@TableField("mp_youlaoru_achievement_rate_target")
	private BigDecimal mpYoulaoruAchievementRateTarget;

	/**
	 * 主推品优酪乳业绩达成率预警
	 */
	@TableField("mp_youlaoru_achievement_rate_warning")
	private BigDecimal mpYoulaoruAchievementRateWarning;

	/**
	 * 交易合伙人数目标
	 */
	@TableField("trading_partners_nums_target")
	private BigDecimal tradingPartnersNumsTarget;

	/**
	 * 交易合伙人数预警
	 */
	@TableField("trading_partners_nums_warning")
	private BigDecimal tradingPartnersNumsWarning;

	/**
	 * 人均业绩目标达成率目标
	 */
	@TableField("itemsSupplyTotal_cm_avg_achievement_rate_target")
	private BigDecimal itemsSupplyTotalCmAvgAchievementRateTarget;

	/**
	 * 人均业绩目标达成率预警
	 */
	@TableField("itemsSupplyTotal_cm_avg_achievement_rate_warning")
	private BigDecimal itemsSupplyTotalCmAvgAchievementRateWarning;

	/**
	 * 次月复购率目标
	 */
	@TableField("next_month_re_purchase_rate_target")
	private BigDecimal nextMonthRePurchaseRateTarget;

	/**
	 * 次月复购率预警
	 */
	@TableField("next_month_re_purchase_rate_warning")
	private BigDecimal nextMonthRePurchaseRateWarning;


	/**
	 * 合伙人在岗人数目标
	 */
	@TableField("partners_on_job_nums_target")
	private BigDecimal partnersOnJobNumsTarget;

	/**
	 * 合伙人在岗人数预警
	 */
	@TableField("partners_on_job_nums_warning")
	private BigDecimal partnersOnJobNumsWarning;

	/**
	 * 合伙人编制在岗率目标
	 */
	@TableField("partner_onboard_headcount_rate_target")
	private BigDecimal partnerOnboardHeadcountRateTarget;

	/**
	 * 合伙人编制在岗率预警
	 */
	@TableField("partner_onboard_headcount_rate_warning")
	private BigDecimal partnerOnboardHeadcountRateWarning;

	/**
	 * 在岗人数同比增长率目标
	 */
	@TableField("partners_on_job_nums_yoy_target")
	private BigDecimal partnersOnJobNumsYoyTarget;

	/**
	 * 在岗人数同比增长率预警
	 */
	@TableField("partners_on_job_nums_yoy_warning")
	private BigDecimal partnersOnJobNumsYoyWarning;

	/**
	 * 应有市场达成率目标
	 */
	@TableField("due_market_achievement_rate_target")
	private BigDecimal dueMarketAchievementRateTarget;

	/**
	 * 应有市场达成率预警
	 */
	@TableField("due_market_achievement_rate_warning")
	private BigDecimal dueMarketAchievementRateWarning;

	/**
	 * 合伙人净增加人数目标
	 */
	@TableField("partners_increase_number_target")
	private BigDecimal partnersIncreaseNumberTarget;

	/**
	 * 合伙人净增加人数预警
	 */
	@TableField("partners_increase_number_warning")
	private BigDecimal partnersIncreaseNumberWarning;

	/**
	 * 合伙人面试率目标
	 */
	@TableField("partners_interview_rate_target")
	private BigDecimal partnersInterviewRateTarget;

	/**
	 * 合伙人面试率预警
	 */
	@TableField("partners_interview_rate_warning")
	private BigDecimal partnersInterviewRateWarning;

	/**
	 * 合伙人面试通过率目标
	 */
	@TableField("partners_interview_pass_rate_target")
	private BigDecimal partnersInterviewPassRateTarget;

	/**
	 * 合伙人面试通过率预警
	 */
	@TableField("partners_interview_pass_rate_warning")
	private BigDecimal partnersInterviewPassRateWarning;

	/**
	 * 合伙人试岗通过率目标
	 */
	@TableField("partners_trial_pass_rate_target")
	private BigDecimal partnersTrialPassRateTarget;

	/**
	 * 合伙人试岗通过率预警
	 */
	@TableField("partners_trial_pass_rate_warning")
	private BigDecimal partnersTrialPassRateWarning;

	/**
	 * 在岗人均业绩(全职+承揽)目标
	 */
	@TableField(exist = false)
	private BigDecimal itemsSupplyTotalCmAvgFullTimeContractTarget;

	/**
	 * 在岗人均业绩(全职+承揽)预警
	 */
	@TableField(exist = false)
	private BigDecimal itemsSupplyTotalCmAvgFullTimeContractWarning;

	/**
	 * 乳品业绩占比目标
	 */
	@TableField("dairy_performance_rate_target")
	private BigDecimal dairyPerformanceRateTarget;

	/**
	 * 乳品业绩占比预警
	 */
	@TableField("dairy_performance_rate_warning")
	private BigDecimal dairyPerformanceRateWarning;

	/**
	 * 休闲业绩占比
	 */
	@TableField("leisure_performance_rate_target")
	private BigDecimal leisurePerformanceRateTarget;

	/**
	 * 休闲业绩占比预警
	 */
	@TableField("leisure_performance_rate_warning")
	private BigDecimal leisurePerformanceRateWarning;

	/**
	 * 区域经理在岗率目标
	 */
	@TableField("city_manager__on_job_rate_target")
	private BigDecimal cityManagerOnJobRateTarget;

	/**
	 * 区域经理在岗率预警
	 */
	@TableField("city_manager__on_job_rate_warning")
	private BigDecimal cityManagerOnJobRateWarning;

	/**
	 * 区域经理面试率目标
	 */
	@TableField("city_manager_interview_rate_target")
	private BigDecimal cityManagerInterviewRateTarget;

	/**
	 * 区域经理面试率预警
	 */
	@TableField("city_manager_interview_rate_warning")
	private BigDecimal cityManagerInterviewRateWarning;

	/**
	 * 区域经理面试入职率目标
	 */
	@TableField("city_manager_interview_entry_rate_target")
	private BigDecimal cityManagerInterviewEntryRateTarget;

	/**
	 * 区域经理面试入职率预警
	 */
	@TableField("city_manager_interview_entry_rate_warning")
	private BigDecimal cityManagerInterviewEntryRateWarning;

	/**
	 * 区域经理离职率目标
	 */
	@TableField("city_manager_off_rate_target")
	private BigDecimal cityManagerOffRateTarget;

	/**
	 * 区域经理离职率预警
	 */
	@TableField("city_manager_off_rate_warning")
	private BigDecimal cityManagerOffRateWarning;

	@TableField("business_bd_controlled_num")
	private Integer businessBdControlledNum;

	//20240329新增指标目标设置的预警目标开始

	@TableField("customers_num")
	private Integer customersNum;

	@TableField("trade_customer_num")
	private Integer tradeCustomerNum;

	@TableField("customer_unit_price")
	private Integer customerUnitPrice;

	@TableField("management_position_on_job_num")
	private Integer managementPositionOnJobNum;

	@TableField("management_position_unit_price")
	private Integer managementPositionUnitPrice;
	//20240329新增指标目标设置的预警目标结束

}
