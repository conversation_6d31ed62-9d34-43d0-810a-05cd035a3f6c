package com.wantwant.sfa.backend.model.organizationGoal;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 季度组织目标导入excel
 *
 * @since 2023-06-13
 */
@Data
@TableName("sfa_quarter_organization_goal_excel")
public class QuarterOrganizationGoalExcelPO extends Model<QuarterOrganizationGoalExcelPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 生效年
	*/
	@TableField("year")
	private Integer year;

	/**
	* 生效季度
	*/
	@TableField("quarter")
	private Integer quarter;

	@TableField("business_group_id")
	private Integer businessGroupId;

	/**
	* 每月目标截止日期
	*/
	@TableField("day")
	private Integer day;

	/**
	* 是否确认(0:未确认,1:已确认)
	*/
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

}
