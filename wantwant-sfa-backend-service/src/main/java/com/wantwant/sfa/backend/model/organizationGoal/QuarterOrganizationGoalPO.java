package com.wantwant.sfa.backend.model.organizationGoal;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 季度组织目标
 *
 * @since 2023-06-13
 */
@Data
@TableName("sfa_quarter_organization_goal")
public class QuarterOrganizationGoalPO extends Model<QuarterOrganizationGoalPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 年
	*/
	@TableField("year")
	private Integer year;

	/**
	* 季度
	*/
	@TableField("quarter")
	private Integer quarter;

	/**
	* sfa_quarter_organization_goal_excel.id
	*/
	@TableField("excel_id")
	private Integer excelId;

	@TableField("business_group_id")
	private Integer businessGroupId;

	/**
	* 组织id
	*/
	@TableField("organization_id")
	private String organizationId;

	/**
	* 组织name
	*/
	@TableField("organization_name")
	private String organizationName;

	/**
	* 季度目标
	*/
	@TableField("quarter_trans_amount")
	private BigDecimal quarterTransAmount;

	@TableField("customers_num")
	private Integer customersNum;

	@TableField("trade_customer_num")
	private Integer tradeCustomerNum;

	@TableField("customer_unit_price")
	private Integer customerUnitPrice;

	@TableField("management_position_on_job_num")
	private Integer managementPositionOnJobNum;

	@TableField("management_position_unit_price")
	private Integer managementPositionUnitPrice;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	@TableField("created_by")
	private String createdBy;

	@TableField("updated_by")
	private String updatedBy;

	/**
	* 创建人名称
	*/
	@TableField("created_name")
	private String createdName;

	/**
	* 修改人名称
	*/
	@TableField("updated_name")
	private String updatedName;

	/**
	* 是否删除(1:删除)
	*/
	@TableLogic
	@TableField("is_delete")
	private Integer isDelete;

}
