package com.wantwant.sfa.backend.model.param;

import com.wantwant.sfa.backend.annotation.Exclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @Author: luxiaoyin
 * @Date: 2020/6/23
 * @Package: com.wantwant.sfa.backend.visit.request
 */
@Data
@Slf4j
public class VisitListParam {
    @ApiModelProperty(value = "开始时间")
    private String beginTime;

//    public String getBeginTime(){
//        if(StringUtils.isNotBlank(this.beginTime)) {
//            return beginTime+" 00:00:00";
//        }
//        return this.beginTime;
//    }
//
//
//    public String getEndTime(){
//        if(StringUtils.isNotBlank(this.endTime)) {
//            return endTime+" 23:59:59";
//        }
//        return this.endTime;
//    }

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户查询")
    private String message;

    @ApiModelProperty(value = "拜访ID")
    private Integer visitId;

    @ApiModelProperty(value = "业务查询")
    private String business;

    @Exclude
    @ApiModelProperty(value = "大区")
    private List<String> area;

    @Exclude
    @ApiModelProperty(value = "分公司")
    private String organization_name2;

    @Exclude
    @ApiModelProperty(value = "营业所")
    private String organization_name1;

    @Exclude
    private String orgSql;

    @ApiModelProperty(value = "组织关系类型")
    private Integer orgType;

    @ApiModelProperty(value = "客户类型")
    private String customertype;

    @ApiModelProperty(value = "潜在或正式")
    private String ispotential;

    @ApiModelProperty(value = "当场开户")
    private String isconvert;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "子类型")
    private String customerSubType;

    private String checking;

    @ApiModelProperty(value = "当场开户")
    private String isActivity;


    public boolean isNull() {
        if (StringUtils.isNotBlank(this.orgSql)) {
            return false;
        }
        boolean isLog = true;
        Field[] fields = this.getClass().getDeclaredFields();

        for (Field field : fields) {
            if (null != field.getDeclaredAnnotation(Exclude.class)) {
                continue;
            }
            if(isLog && Logger.class.equals(field.getType())){
                isLog = false;
                continue;
            }
            if (String.class.equals(field.getType())) {
                try {
                    if (StringUtils.isNotBlank((String) field.get(this))) {
                        return false;
                    }
                } catch (IllegalAccessException e) {
                    log.error("isNull error", e);
                }
            } else {
                try {
                    if (null != field.get(this)) {
                        return false;
                    }
                } catch (IllegalAccessException e) {
                    log.error("isNull error", e);
                }
            }
        }
        return true;
    }
}
