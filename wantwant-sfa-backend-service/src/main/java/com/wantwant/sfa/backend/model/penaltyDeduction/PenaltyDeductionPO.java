package com.wantwant.sfa.backend.model.penaltyDeduction;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("sfa_penalty_deduction")
public class PenaltyDeductionPO extends Model<PenaltyDeductionPO> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    @TableField("the_year_month")
    private String theYearMonth;

    @TableField("employee_info_id")
    private Integer employeeInfoId;

    @TableField("employee_name")
    private String employeeName;

    @TableField("memberkey")
    private String memberkey;

    @TableField("idCard_num")
    private String idCardNum;

    /**
     * 岗位津贴
     */
    @TableField("post_allowance")
    private BigDecimal postAllowance;

    /** 
     * 绩效奖金 
     */
    @TableField("total_bonus")
    private BigDecimal totalBonus;

    /** 
     * 岗位津贴实扣金额 = 上月拜访,考核扣罚 + 本月拜访,考核扣罚
     */
    @TableField("post_allowance_deduction")
    private BigDecimal postAllowanceDeduction;

    /** 
     * 主管绩效实扣金额
     */
    @TableField("total_bonus_deduction")
    private BigDecimal totalBonusDeduction;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除(1:删除)
     */
    @TableLogic(delval = "1")
    @TableField("is_delete")
    private Integer isDelete;

    /** 
     *  岗位类型
     */
    @TableField("position_name")
    private String positionName;

    /** 
     *  岗位排序
     */
    @TableField("post_sort")
    private Integer postSort;

    /** 
     * 当前组织id 
     */
    @TableField("organization_id")
    private String organizationId;

    /** 
     * 战区组织
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 战区组织id
     */
    @TableField("area_id")
    private String areaId;

    /**
     *  大区组织名称
     */
    @TableField("varea_name")
    private String vareaName;

    /**
     *  大区组织id
     */
    @TableField("varea_id")
    private String vareaId;

    /**
     *  省区组织名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     *  省区组织id
     */
    @TableField("province_id")
    private String provinceId;

    /**
     * 分公司组织名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 分公司组织id
     */
    @TableField("company_id")
    private String companyId;

    /**
     * 分公司组织名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     *  营业所组织id
     */
    @TableField("department_id")
    private String departmentId;

    @TableField("joining_company_name")
    private String joiningCompanyName;

}
