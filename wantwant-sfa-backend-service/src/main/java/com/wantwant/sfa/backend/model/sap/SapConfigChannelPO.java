package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * sap库存地点转换配置表
 *
 * @since 2022-11-28
 */
@Data
@TableName("sap_config_channel")
public class SapConfigChannelPO extends Model<SapConfigChannelPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 订单渠道
	*/
	@TableField("channel_code")
	private String channelCode;

	/**
	* SAP物料发货工厂
	*/
	@TableField("werks")
	private String werks;

	/**
	* SAP库存仓别
	*/
	@TableField("lgort")
	private String lgort;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
