package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * sap售送达方转换配置表
 *
 * @since 2022-11-28
 */
@Data
@TableName("sap_config_member")
public class SapConfigMemberPO extends Model<SapConfigMemberPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 客户编码
	*/
	@TableField("memberKey")
	private String memberKey;

	/**
	* 合伙人企业名称
	*/
	@TableField("partner_company_name")
	private String partnerCompanyName;

	/**
	* 售达方
	*/
	@TableField("kunnr")
	private String kunnr;

	/**
	* 送达方
	*/
	@TableField("kunnr1")
	private String kunnr1;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;
}
