package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * sap订单类型转换配置表
 *
 * @since 2022-11-28
 */
@Data
@TableName("sap_config_order_type")
public class SapConfigOrderTypePO extends Model<SapConfigOrderTypePO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 造旺订单类型
	*/
	@TableField("order_type_code")
	private String orderTypeCode;

	/**
	* SAP订单类型
	*/
	@TableField("auart")
	private String auart;

	/**
	* SAP销售组织
	*/
	@TableField("vkorg")
	private String vkorg;

	/**
	* SAP分销渠道
	*/
	@TableField("vtweg")
	private String vtweg;

	/**
	* SAP产品组
	*/
	@TableField("spart")
	private String spart;

	/**
	* SAP售达方
	*/
	@TableField("kunnr")
	private String kunnr;

	/**
	* SAP送达方
	*/
	@TableField("kunnr1")
	private String kunnr1;

	/**
	* SAP订单原因
	*/
	@TableField("augru")
	private String augru;

	/**
	* SAP成本中心
	*/
	@TableField("kostl")
	private String kostl;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
