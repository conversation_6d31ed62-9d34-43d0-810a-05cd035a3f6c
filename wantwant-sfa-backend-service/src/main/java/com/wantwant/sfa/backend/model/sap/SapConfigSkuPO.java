package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * sap物料信息转换配置表
 *
 * @since 2022-11-28
 */
@Data
@TableName("sap_config_sku")
public class SapConfigSkuPO extends Model<SapConfigSkuPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* sku
	*/
	@TableField("sku")
	private String sku;

	/**
	 * sap_sku
	 */
	@TableField("sap_sku")
	private String sapSku;

	/**
	* SKU描述
	*/
	@TableField("sku_name")
	private String skuName;

	/** 
	 * 基本单位
	 */
	@TableField("base_unit")
	private String baseUnit;

	@TableField("sales_unit")
	private String salesUnit;

	/**
	 * 划算系数
	 */
	@TableField("scale_factor")
	private int scaleFactor;


	/**
	* 物料大类
	*/
	@TableField("material_category")
	private String materialCategory;

	/**
	 * 样赠
	 */
	@TableField("is_free")
	private int isFree;



	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 使用销售单位
	 */
	@TableField("using_sales_unit")
	private Integer usingSalesUnit;

}
