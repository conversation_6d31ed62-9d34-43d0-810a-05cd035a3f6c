package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * sap合并订单头
 *
 * @since 2022-11-28
 */
@Data
@TableName("sap_merge_order_header")
public class SapMergeOrderHeaderPO extends Model<SapMergeOrderHeaderPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@TableId(value = "work_id")
	private Long workId;

	/**
	* 采购订单编号(YYMMDD+三位流水号)
	*/
	@TableField("bstnk")
	private String bstnk;

	/**
	* SAP订单类型
	*/
	@TableField("auart")
	private String auart;//SH02

	/**
	* 销售组织
	*/
	@TableField("vkorg")
	private String vkorg;//SH03

	/**
	* 分销渠道
	*/
	@TableField("vtweg")
	private String vtweg;//SH04

	/**
	* 产品组
	*/
	@TableField("spart")
	private String spart;//SH05

	/**
	* 售达方
	*/
	@TableField("kunnr")
	private String kunnr;//SH06

	/**
	* 送达方
	*/
	@TableField("kunnr1")
	private String kunnr1;//SH07

	/**
	* 订单日期
	*/
	@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@TableField("audat")
	private LocalDateTime audat;

	/**
	* 定价日期
	*/
	@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@TableField("prsdt")
	private LocalDateTime prsdt;

	/**
	* 请求交货日期
	*/
	@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@TableField("vdatu")
	private LocalDateTime vdatu;

	/**
	* 订单原因
	*/
	@TableField("augru")
	private String augru;//SH11

	/**
	* 成本中心
	*/
	@TableField("kostl")
	private String kostl;//SH12

	/**
	 * SAP库存仓别
	 */
	@TableField("lgort")
	private String lgort;//SH12

	/**
	 * 现金支付金额
	 */
	@TableField("cash_payment")
	private BigDecimal cashPayment;

	/**
	 * 造旺订单id合并
	 */
//	@TableField("order_header_id_merge")
	@TableField(exist = false)
	private String orderHeaderIdMerge;//SH12

	/**
	* 促销执行案号
	*/
	@TableField("tmpId")
	private String tmpId;

	/**
	* 文本注释
	*/
	@TableField("textId")
	private String textId;

	/**
	* 传输SAP状态(0:待处理,1:成功,2:失败)
	*/
	@TableField("status")
	private Integer status;

	@TableField("msg")
	private String msg;

	/**
	* SAP返回销售订单号
	*/
	@TableField("vbeln")
	private String vbeln;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 同步sap的日期
	 */
	@JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@TableField("sync_date")
	private LocalDateTime syncDate;


	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	@TableField(exist = false)
	List<SapMergeOrderLinePO> orderDetailVoList;

}
