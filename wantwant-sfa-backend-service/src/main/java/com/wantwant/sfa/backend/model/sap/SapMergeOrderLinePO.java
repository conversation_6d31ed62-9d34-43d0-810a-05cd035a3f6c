package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * sap合并订单行
 *
 * @since 2022-11-28
 */
@Data
@TableName("sap_merge_order_line")
public class SapMergeOrderLinePO extends Model<SapMergeOrderLinePO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	/**
	* 采购订单编号
	*/
	@TableField("bstnk")
	private String bstnk;

	/**
	* 合并订单行号(每个订单从1开始的流水号)
	*/
	@TableField("posnr")
	private String posnr;

	/**
	* 物料编码
	*/
	@TableField("matnr")
	private String matnr;

	/**
	* 订单数量(旺铺数量/箱规，3位小数)
	*/
	@TableField("kwmeng")
	private BigDecimal kwmeng;

	/**
	* 单位(CAR)
	*/
	@TableField("meins")
	private String meins;//SL05

	/**
	* 发货工厂
	*/
	@TableField("werks")
	private String werks;//SL06

	/**
	* 仓别
	*/
	@TableField("lgort")
	private String lgort;//SL07

	/**
	* 开单单价
	*/
	@TableField("price")
	private BigDecimal price;

	/**
	* 开单金额(旺铺实际售卖金额SUM)
	*/
	@TableField("cmpre")
	private BigDecimal cmpre;

	/**
	* 促销执行案号
	*/
	@TableField("tmpId")
	private String tmpId;

	/**
	* 备注
	*/
	@TableField("textId")
	private String textId;

	/**
	 * 盘价金额
	 */
	@TableField("listed_amount")
	private BigDecimal listedAmount;

	/**
	* 盘价业绩
	*/
	@TableField("performance_amount")
	private BigDecimal performanceAmount;

	/**
	* 折后分摊金额
	*/
	@TableField("discount_amount")
	private BigDecimal discountAmount;

	/**
	* 现金支付金额
	*/
	@TableField("cash_payment")
	private BigDecimal cashPayment;

	/**
	* 旺金币支付金额
	*/
	@TableField("wang_payment")
	private BigDecimal wangPayment;

	/**
	 * 造旺订单id合并
	 */
//	@TableField("order_header_id_merge")
	@TableField(exist = false)
	private String orderHeaderIdMerge;//SH12

	/**
	 * 造旺订单id合并
	 */
	@TableField("order_line_id_merge")
	private String orderLineIdMerge;//SH12


	/**
	* 传输SAP状态(0:失败,1:成功,2:待处理)
	*/
	@TableField("status")
	private Integer status;

	/**
	* SAP返回信息
	*/
	@TableField(value = "msg",strategy = FieldStrategy.IGNORED)
	private String msg;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time",fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
