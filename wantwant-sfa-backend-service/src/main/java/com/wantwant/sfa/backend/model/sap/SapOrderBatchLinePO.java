package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 订单批次行
 *
 * @since 2022-09-21
 */
@Data
@TableName("sap_order_batch_line")
public class SapOrderBatchLinePO extends Model<SapOrderBatchLinePO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 订单编号
	*/
	@TableField("code")
	private String code;

	/**
	* 订单行号
	*/
	@TableField("line_code")
	private String lineCode;

	/**
	* 批次
	*/
	@TableField("batch")
	private String batch;

	/**
	* 批次数量
	*/
	@TableField("batch_quantity")
	private Integer batchQuantity;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
