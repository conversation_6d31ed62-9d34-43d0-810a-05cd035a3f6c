package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单支付行
 *
 * @since 2022-09-21
 */
@Data
@TableName("sap_order_cost_line")
public class SapOrderCostLinePO extends Model<SapOrderCostLinePO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 订单编号
	*/
	@TableField("code")
	private String code;

	/**
	* 订单行号
	*/
	@TableField("line_code")
	private String lineCode;

	/**
	* 旺金币发放金额
	*/
	@TableField("wang_amount")
	private BigDecimal wangAmount;

	/**
	 * 旺金币费用类型
	 */
	@TableField("cost_type")
	private String costType;

	/**
	 * 旺金币费用归属部门
	 */
	@TableField("department")
	private String department;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
