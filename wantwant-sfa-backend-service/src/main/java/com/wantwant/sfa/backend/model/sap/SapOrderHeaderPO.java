package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 订单头
 *
 * @since 2022-09-21
 */
@Data
@TableName("sap_order_header")
public class SapOrderHeaderPO extends Model<SapOrderHeaderPO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 订单编号
	*/
	@TableField("code")
	private String code;


	/**
	 * 订单组
	 */
	@TableField("order_group_flag")
	private Integer orderGroupFlag;

	/**
	* 订单类型-代码
	*/
	@TableField("order_type_code")
	private String orderTypeCode;

	@TableField("order_type_name")
	private String orderTypeName;

	/**
	* 渠道
	*/
	@TableField("channel_code")
	private String channelCode;

	@TableField("channel_name")
	private String channelName;

	/**
	* 下单时间
	*/
	@TableField("placed_at")
	private LocalDateTime placedAt;

	/**
	* 付款时间
	*/
	@TableField("processing_at")
	private LocalDateTime processingAt;

	/**
	* 配送时间
	*/
	@TableField("delivering_at")
	private LocalDateTime deliveringAt;

	/**
	 * 客户编码
	 */
	@TableField("memberKey")
	private String memberKey;

	/**
	 * 运费
	 */
	@TableField("delivery_fee")
	private BigDecimal deliveryFee;

	/**
	 * 在线交易信息
	 */
	@TableField("method")
	private String method;

	/**
	 * 交易流水号
	 */
	@TableField("transaction_code")
	private String transactionCode;

	/**
	 * 合单号
	 */
	@TableField("parent_id")
	private String parentId;

	/** 
	 * 大区id
	 */
	@TableField("area_organization_id")
	private String areaOrganizationId;

	/** 
	 * 大区name
	 */
	@TableField("area_organization_name")
	private String areaOrganizationName;

	/**
	 * 分公司id
	 */
	@TableField("company_organization_id")
	private String companyOrganizationId;

	/**
	 * 分公司name
	 */
	@TableField("company_organization_name")
	private String companyOrganizationName;

	@TableField("department_id")
	private String departmentId;

	@TableField("department_name")
	private String departmentName;

	/** 
	 * sfa_employee_info.id
	 */
	@TableField("emp_id")
	private Integer empId;

	@TableField("emp_name")
	private String empName;

	@TableField("emp_mobile")
	private String empMobile;

	/**
	 * sfa_customer.id
	 */
	@TableField("customer_id")
	private String customerId;

	/**
	 * 合伙人类型(企业合伙人,兼职合伙人,全职合伙人,承揽合伙人)
	 */
	@TableField("position_type")
	private String positionType;

	/** 
	 * 合伙人企业名称(sfa_apply_member.partner_company_name)
	 */
	@TableField("partner_company_name")
	private String partnerCompanyName;

	/**
	 * 盘价金额合计
	 */
	@TableField("standard_amount_total")
	private BigDecimal standardAmountTotal;

	/**
	 * 现金支付金额合计
	 */
	@TableField("cash_payment_total")
	private BigDecimal cashPaymentTotal;

	/**
	 * 旺金币支付金额合计
	 */
	@TableField("wang_payment_total")
	private BigDecimal wangPaymentTotal;

	/**
	 * 订单状态
	 */
	@TableField("order_status")
	private int orderStatus;


	/**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
