package com.wantwant.sfa.backend.model.sap;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单行
 *
 * @since 2022-09-21
 */
@Data
@TableName("sap_order_line")
public class SapOrderLinePO extends Model<SapOrderLinePO> {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	 * 订单编号
	 */
	@TableField("code")
	private String code;

	/**
	* 订单行号
	*/
	@TableField("line_code")
	private String lineCode;

	/**
	* sku
	*/
	@TableField("sku")
	private String sku;

	@TableField("sku_name")
	private String skuName;

	/**
	* sku数量
	*/
	@TableField("quantity")
	private Integer quantity;

	/**
	* sku箱规
	*/
	@TableField("full_case_num")
	private Integer fullCaseNum;

	/**
	* 标准整箱供货价-总督导
	*/
	@TableField("supply_price_region")
	private BigDecimal supplyPriceRegion;

	/**
	* 标准整箱供货价-总监
	*/
	@TableField("supply_price_branch")
	private BigDecimal supplyPriceBranch;

	/**
	* 三阶价
	*/
	@TableField("supply_price_biz")
	private BigDecimal supplyPriceBiz;

	/**
	* 应售供货单价
	*/
	@TableField("supply_unit_price")
	private BigDecimal supplyUnitPrice;

	/**
	* 实际售卖单价
	*/
	@TableField("retail_unit_price")
	private BigDecimal retailUnitPrice;

	/**
	* 应售供货金额
	*/
	@TableField("supply_subtotal")
	private BigDecimal supplySubtotal;

	/**
	* 实际售卖金额
	*/
	@TableField("retail_subtotal")
	private BigDecimal retailSubtotal;

	/**
	 * 现金支付金额
	 */
	@TableField("cash_payment")
	private BigDecimal cashPayment;

	/**
	 * 旺金币支付金额
	 */
	@TableField("wang_payment")
	private BigDecimal wangPayment;

	/**
	 * sku层折扣小计
	 */
	@TableField("discount_total")
	private BigDecimal discountTotal;

	/**
	 * 商品类型
	 */
	@TableField("attribute")
	private String attribute;

	/**
	 * 赠品促销形式
	 */
	@TableField("promotion_type")
	private String promotionType;

	/**
	 * 活动id
	 */
	@TableField("activity_id")
	private String activityId;

	/**
	 * 行项目类型
	 */
	@TableField("line_type")
	private Integer lineType;

	/**
	 * 盘价金额
	 */
	@TableField("listed_amount")
	private BigDecimal listedAmount;

	/** 
	 * 折后分摊金额
	 */
	@TableField("discount_amount")
	private BigDecimal discountAmount;

	/**
	 * 状态（0-未处理；1-预检查；2-处理中；3-已完成）
	 */
	@TableField("line_status")
	private Integer lineStatus;

	/**
	 * 合并订单编号
	 */
	@TableField("merge_code")
	private String mergeCode;

	/**
	 * 合并订单行号
	 */
	@TableField("merge_line_code")
	private String mergeLineCode;

	/**
	 * SAP凭证号
	 */
	@TableField("sap_code")
	private String sapCode;

	/** 
	 * SAP返回状态
	 */
	@TableField("sap_status")
	private String sapStatus;

	/** 
	 * SAP返回凭证
	 */
	@TableField("sap_certificate")
	private String sapCertificate;

	/**
	 * SAP返回文本
	 */
	@TableField("sap_text")
	private String sapText;

	/**
	* 是否删除(1:删除)
	*/
	@TableField("is_delete")
	private Integer isDelete;

}
