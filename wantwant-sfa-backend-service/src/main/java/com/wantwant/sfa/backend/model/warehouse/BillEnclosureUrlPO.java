package com.wantwant.sfa.backend.model.warehouse;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仓储附件url
 *
 * @since 2022-06-22
 */
@Data
@TableName("sfa_bill_enclosure_url")
public class BillEnclosureUrlPO extends Model<BillEnclosureUrlPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 固定成本id
	*/
	@TableField("fixed_costs_id")
	private Integer fixedCostsId;

	/**
	* 文件地址
	*/
	@TableField("url")
	private String url;

	/**
	* 文件名称
	*/
	@TableField("name")
	private String name;

	/**
	* 创建人
	*/
	@TableField("create_people")
	private String createPeople;

	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField("update_people")
	private String updatePeople;

	/**
	* 修改时间
	*/
	@TableField("update_time")
	private LocalDateTime updateTime;

	/**
	* 是否删除(0.否;1.是)
	*/
	@TableField("is_delete")
	private Integer isDelete;


}
