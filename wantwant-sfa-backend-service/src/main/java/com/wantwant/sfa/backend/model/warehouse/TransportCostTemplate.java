package com.wantwant.sfa.backend.model.warehouse;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("sfa_transport_cost_template")
public class TransportCostTemplate extends Model<TransportCostTemplate> {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    /**
     * 仓储导入id
     */
    @TableField("warehousing_id")
    private Integer warehousingId;

    /**
     * 合单号
     */
    @TableField("order_no")
    private String orderNo;


    /**
     * 系统运费金额
     */
    @TableField("sys_trans_amount")
    private BigDecimal sysTransAmount;

    /**
     * 账单运费金额
     */
    @TableField("bill_trans_amount")
    private BigDecimal billTransAmount;

    /**
     * 差异说明
     */
    @TableField("`explain`")
    private String explain;

    /**
     * 创建人
     */
    @TableField("create_people")
    private String createPeople;


    /**
     * 创建人姓名
     */
    @TableField("create_people_name")
    private String createPeopleName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_people")
    private String updatePeople;


    /**
     * 修改姓名
     */
    @TableField("update_people_name")
    private String updatePeopleName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除(0.否;1.是)
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否覆盖(0:否,1:是)
     */
    @TableField("is_cover")
    private Integer isCover;

}
