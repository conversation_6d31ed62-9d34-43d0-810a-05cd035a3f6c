package com.wantwant.sfa.backend.model.warehouse;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 运输价格管理
 *
 * @since 2022-06-22
 */
@Data
@TableName("sfa_transport_price_management_style")
public class TransportPriceManagementPO extends Model<TransportPriceManagementPO> {
	private static final long serialVersionUID = 1L;

	@TableId(value = "id")
	private Integer id;

	/**
	* 仓储导入id
	*/
	@TableField("warehousing_id")
	private Integer warehousingId;

	/**
	 * 协议的起始日期
	 */
	@TableField("start_date")
	private LocalDate startDate;

	/**
	 * 协议的结束日期
	 */
	@TableField("end_date")
	private LocalDate endDate;


	/**
	* 始发省
	*/
	@TableField("origin")
	private String origin;

	/**
	* 目的省
	*/
	@TableField("destination")
	private String destination;

	/**
	 * 目的市
	 */
	@TableField("destination_city")
	private String destinationCity;

	/**
	* 公司名称
	*/
	@TableField("corporate_name")
	private String corporateName;

	/**
	* 未税最低收费(元)
	*/
	@TableField("untaxed_minimum_charge")
	private BigDecimal untaxedMinimumCharge;

	/**
	* 未税单价(元/公司)
	*/
	@TableField("untaxed_unit_price")
	private BigDecimal untaxedUnitPrice;

	/**
	* 时效(天)
	*/
	@TableField("prescription")
	private BigDecimal prescription;

	/**
	* 税率
	*/
	@TableField("tax_rate")
	private BigDecimal taxRate;

	/**
	 * 重泡比
	 */
	@TableField("weight_rate")
	private BigDecimal weightRate;

	/**
	* 说明
	*/
	@TableField("`explain`")
	private String explain;

	/**
	* 创建人
	*/
	@TableField("create_people")
	private String createPeople;

	/**
	 * 创建人姓名
	 */
	@TableField("create_people_name")
	private String createPeopleName;


	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField("update_people")
	private String updatePeople;

	/**
	 * 修改姓名
	 */
	@TableField("update_people_name")
	private String updatePeopleName;


	/**
	* 修改时间
	*/
	@TableField("update_time")
	private LocalDateTime updateTime;

	/**
	* 是否删除(0.否;1.是)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 是否覆盖(0:否,1:是)
	 */
	@TableField("is_cover")
	private Integer isCover;

}
