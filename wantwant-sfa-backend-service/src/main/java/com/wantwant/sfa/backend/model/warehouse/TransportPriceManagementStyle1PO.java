package com.wantwant.sfa.backend.model.warehouse;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("sfa_transport_price_management_style1")
public class TransportPriceManagementStyle1PO extends Model<TransportPriceManagementStyle1PO> {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    /**
     * 仓储导入id
     */
    @TableField("warehousing_id")
    private Integer warehousingId;

    /**
     * 协议的起始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 协议的结束日期
     */
    @TableField("end_date")
    private LocalDate endDate;


    /**
     * 始发省
     */
    @TableField("origin")
    private String origin;

    /**
     * 目的省
     */
    @TableField("destination")
    private String destination;

    /**
     * 目的市
     */
    @TableField("destination_city")
    private String destinationCity;

    /**
     * 公司名称
     */
    @TableField("corporate_name")
    private String corporateName;

    /**
     * 零担运费-每吨/元
     */
    @TableField("ld_price_per_ton")
    private BigDecimal ldPricePerTon;

    /**
     * 零担运费-每方/元
     */
    @TableField("ld_price_per_cubic_meter")
    private BigDecimal ldPricePerCubicMeter;

    /**
     * 零但时效(天)
     */
    @TableField("ld_prescription")
    private BigDecimal ldPrescription;

    /**
     * 零但提送货费-每单/元
     */
    @TableField("ld_price_per_delivery_cost")
    private BigDecimal ldPricePerDeliveryCost;

    /**
     * 整车运费-4.2米车型
     */
    @TableField("zc_price_style1")
    private BigDecimal zcPriceStyle1;

    /**
     * 整车运费-6.8米车型
     */
    @TableField("zc_price_style2")
    private BigDecimal zcPriceStyle2;

    /**
     * 整车运费-9.6米车型
     */
    @TableField("zc_price_style3")
    private BigDecimal zcPriceStyle3;

    /**
     * 整车运费-7.6米车型
     */
    @TableField("zc_price_style4")
    private BigDecimal zcPriceStyle4;

    /**
     * 整车运费-13.5米车型
     */
    @TableField("zc_price_style5")
    private BigDecimal zcPriceStyle5;

    /**
     * 整车运费-17.5米车型
     */
    @TableField("zc_price_style6")
    private BigDecimal zcPriceStyle6;

    /**
     * 整车时效(天)
     */
    @TableField("zc_prescription")
    private BigDecimal zcPrescription;

    /**
     * 卸货费-每吨/元
     */
    @TableField("xh_price_per_ton")
    private BigDecimal xhPricePerTon;

    /**
     * 卸货费-每方/元
     */
    @TableField("xh_price_per_cubic_meter")
    private BigDecimal xhPricePerCubicMeter;

    /**
     * 回单-每单/元
     */
    @TableField("back_order")
    private BigDecimal backOrder;

    /**
     * 入库费每箱/元
     */
    @TableField("entry_store_cost")
    private BigDecimal entryStoreCost;

    /**
     * 重泡比
     */
    @TableField("weight_rate")
    private BigDecimal weightRate;

    /**
     * 时效(天)
     */
    @TableField("prescription")
    private BigDecimal prescription;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 说明
     */
    @TableField("`explain`")
    private String explain;

    /**
     * 创建人
     */
    @TableField("create_people")
    private String createPeople;


    /**
     * 创建人姓名
     */
    @TableField("create_people_name")
    private String createPeopleName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_people")
    private String updatePeople;


    /**
     * 修改姓名
     */
    @TableField("update_people_name")
    private String updatePeopleName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除(0.否;1.是)
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否覆盖(0:否,1:是)
     */
    @TableField("is_cover")
    private Integer isCover;
}
