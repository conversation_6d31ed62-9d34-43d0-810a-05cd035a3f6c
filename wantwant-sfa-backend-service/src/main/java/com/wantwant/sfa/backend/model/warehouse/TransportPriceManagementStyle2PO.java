package com.wantwant.sfa.backend.model.warehouse;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("sfa_transport_price_management_style2")
public class TransportPriceManagementStyle2PO extends Model<TransportPriceManagementStyle2PO> {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    /**
     * 仓储导入id
     */
    @TableField("warehousing_id")
    private Integer warehousingId;

    /**
     * 协议的起始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 协议的结束日期
     */
    @TableField("end_date")
    private LocalDate endDate;


    /**
     * 始发省
     */
    @TableField("origin")
    private String origin;

    /**
     * 目的省
     */
    @TableField("destination")
    private String destination;


    /**
     * 公司名称
     */
    @TableField("corporate_name")
    private String corporateName;

    /**
     * 二级供应商名称
     */
    @TableField("second_supply_name")
    private String secondSupplyName;


    /**
     * 二级供应商代码
     */
    @TableField("second_supply_code")
    private String secondSupplyCode;


    /**
     * 起步重量0.5公斤
     */
    @TableField("kilo_half")
    private BigDecimal kiloHalf;

    /**
     * 起步重量1公斤
     */
    @TableField("kilo_one")
    private BigDecimal kiloOne;

    /**
     * 起步重量2公斤
     */
    @TableField("kilo_two")
    private BigDecimal kiloTwo;

    /**
     * 起步重量3公斤
     */
    @TableField("kilo_tree")
    private BigDecimal kiloTree;


    /**
     * 起步重量4公斤
     */
    @TableField("kilo_four")
    private BigDecimal kiloFour;

    /**
     * 起步重量5公斤
     */
    @TableField("kilo_five")
    private BigDecimal kiloFive;

    /**
     * 续重元/公斤3kg以上首重按1kg起计算
     */
    @TableField("kilo_apend_per_three")
    private BigDecimal kiloApendPerThree;

    /**
     * 续重
     */
    @TableField("append_weight")
    private BigDecimal appendWeight;

    /**
     * 重泡比
     */
    @TableField("weight_ratio")
    private BigDecimal weightRatio;

    /**
     * 时效(天)
     */
    @TableField("prescription")
    private BigDecimal prescription;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 说明
     */
    @TableField("`explain`")
    private String explain;

    /**
     * 创建人
     */
    @TableField("create_people")
    private String createPeople;


    /**
     * 创建人姓名
     */
    @TableField("create_people_name")
    private String createPeopleName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_people")
    private String updatePeople;

    /**
     * 修改姓名
     */
    @TableField("update_people_name")
    private String updatePeopleName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除(0.否;1.是)
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否覆盖(0:否,1:是)
     */
    @TableField("is_cover")
    private Integer isCover;
}
