package com.wantwant.sfa.backend.model.warehouse;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仓储导入表
 *
 * @since 2022-06-22
 */
@Data
@TableName("sfa_warehousing_import_apply")
public class WarehousingImportApplyPO extends Model<WarehousingImportApplyPO> {
	private static final long serialVersionUID = 1L;

	/**
	* 仓储导入ID
	*/
	@TableId(value = "id")
	private Integer id;

	/**
	* 月份
	*/
	@TableField("month")
	private String month;

	/**
	* 导入类型(1.固定成本 2.快运价格 3.公路运输价格 4.快递价格 5.账单运费 ))
	*/
	@TableField("import_type")
	private Integer importType;

	/**
	* 创建人
	*/
	@TableField("create_people")
	private String createPeople;

	/**
	 * 创建人姓名
	 */
	@TableField("create_people_name")
	private String createPeopleName;

	/**
	* 创建时间
	*/
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField("update_people")
	private String updatePeople;

	/**
	 * 修改人姓名
	 */
	@TableField("update_people_name")
	private String updatePeopleName;


	/**
	* 修改时间
	*/
	@TableField("update_time")
	private LocalDateTime updateTime;

	/**
	 * 状态(0:待审核,1:已生效,2:已驳回)
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 审核人姓名
	 */
	@TableField("reviewer_name")
	private String reviewerName;

	/** 
	 * 审核人工号
	 */
	@TableField("reviewer_id")
	private String reviewerId;

	/** 
	 * 审核时间
	 */
	@TableField("reviewer_time")
	private LocalDateTime reviewerTime;
	
	/**
	 * 审批内容
	 */
	@TableField("comment")
	private String comment;


	/**
	 * 是否继承(0.否;1.是)
	 */
	@TableField("is_inheritance")
	private Integer isInheritance;


	/**
	* 是否删除(0.否;1.是)
	*/
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 是否覆盖(0:否,1:是)
	 */
	@TableField("is_cover")
	private Integer isCover;

}
