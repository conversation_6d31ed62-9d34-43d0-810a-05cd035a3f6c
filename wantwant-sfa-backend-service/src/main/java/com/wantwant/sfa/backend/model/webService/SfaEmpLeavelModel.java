package com.wantwant.sfa.backend.model.webService;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/09/23/上午10:16
 */
@Data
@TableName("wp_employee_leave")
public class SfaEmpLeavelModel {
    @ApiModelProperty(value = "id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "员工工号")
    @TableField("emp_id")
    private String empId;

    @ApiModelProperty(value = "员工姓名")
    @TableField("emp_name")
    private String empName;

    @ApiModelProperty(value = "假别")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "请假开始日期")
    @TableField("start_date")
    private String startDate;

    @ApiModelProperty(value = "请假开始时间")
    @TableField("start_time")
    private String startTime;

    @ApiModelProperty(value = "请假开始日期")
    @TableField("end_date")
    private String endDate;

    @ApiModelProperty(value = "请假开始时间")
    @TableField("end_time")
    private String endTime;

    @ApiModelProperty(value = "请假时长")
    @TableField("leave_time")
    private Float leaveTime;
}
