package com.wantwant.sfa.backend.model.webService;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 爱旺旺员工同步用model。
 * @Auther: zhengxu
 * @Date: 2021/09/23/上午9:36
 */
@Data
@TableName("wp_employee")
public class SfaEmpModel {

    @ApiModelProperty(value = "员工工号")
    @TableField("emp_id")
    private String empId;
    @ApiModelProperty(value = "员工姓名")
    @TableField("emp_name")
    private String empName;
    @ApiModelProperty(value = "组织6级")
    @TableField("jb")
    private String jb;
    @ApiModelProperty(value = "组织7级")
    @TableField("ksqr")
    private String ksqr;
    @ApiModelProperty(value = "组织8级")
    @TableField("kssj")
    private String kssj;
    @ApiModelProperty(value = "状态")
    @TableField("status")
    private int status;
    @ApiModelProperty(value = "入职日期")
    @TableField("emp_on_board_date")
    private String empOnBoardDate;
    @ApiModelProperty(value = "离职日期")
    @TableField("emp_term_date")
    private String empTermDate;
}
