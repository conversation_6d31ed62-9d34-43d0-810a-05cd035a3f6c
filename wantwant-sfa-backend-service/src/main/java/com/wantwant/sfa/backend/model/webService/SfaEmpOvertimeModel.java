package com.wantwant.sfa.backend.model.webService;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/09/23/上午11:22
 */
@Data
@TableName("wp_employee_overtime")
public class SfaEmpOvertimeModel {
    @ApiModelProperty(value = "id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "员工工号")
    @TableField("emp_id")
    private String empId;

    @ApiModelProperty(value = "员工姓名")
    @TableField("emp_name")
    private String empName;


    @ApiModelProperty(value = "加班开始日期")
    @TableField("start_date")
    private String startDate;

    @ApiModelProperty(value = "加班开始时间")
    @TableField("start_time")
    private String startTime;

    @ApiModelProperty(value = "加班开始日期")
    @TableField("end_date")
    private String endDate;

    @ApiModelProperty(value = "加班开始时间")
    @TableField("end_time")
    private String endTime;

    @ApiModelProperty(value = "加班时长")
    @TableField("over_time")
    private Float overTime;
}
