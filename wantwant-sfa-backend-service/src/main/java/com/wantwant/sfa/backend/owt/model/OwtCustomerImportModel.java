package com.wantwant.sfa.backend.owt.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Description: 123专案导入客户用Model。
 * @Auther: zhengxu
 * @Date: 2021/08/31/上午9:15
 */
@Data
@ApiModel(value = "123专案客户导入对象", description = "")
public class OwtCustomerImportModel {
    @Excel(name = "大区")
    private String areaName;
    @Excel(name = "分公司")
    private String companyName;
    @Excel(name = "营业所")
    private String branchName;
    @Excel(name="客户名称")
    private String customerName;
    @Excel(name = "客户类型")
    private String customerType;
    @Excel(name = "性别")
    private String sex;
    @Excel(name = "客户地址")
    private String address;
    @Excel(name = "省")
    private String province;
    @Excel(name = "市")
    private String city;
    @Excel(name = "区")
    private String district;
    @Excel(name = "街道")
    private String street;
    @Excel(name = "门店地址")
    private String storeName;
    @Excel(name = "手机号")
    private String mobile;
    @Excel(name = "渠道")
    private String channel;
    @Excel(name = "小标市场名称")
    private String smallMarketName;
    @Excel(name = "小标市场代码")
    private String smallMarketCode;
    @Excel(name = "组合")
    private String combine;
    @Excel(name = "终端覆盖数")
    private String terminalCovertCount;
    @Excel(name = "专员ID")
    private String employeeId;
    @Excel(name = "专员名称")
    private String employeeName;
    /** 岗位ID */
    private String positionId;
    /** 客户ID */
    private Integer customerId;
    /** 旺铺会员号 */
    private String memberKey;


}
