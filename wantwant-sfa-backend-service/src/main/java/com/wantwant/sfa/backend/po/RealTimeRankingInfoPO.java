package com.wantwant.sfa.backend.po;

import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.po
 * @Description:
 * @Date: 2024/5/24 13:12
 */
@Data
public class RealTimeRankingInfoPO {
    /**
     * 排名类型：
     *  region 区域
     *  national 全国
     */
    private String rankingType;
    //区域id
    private String organizationId;
    @ApiModelProperty("人员的memberKey")
    private Long memberKey;
    //造旺本品业绩
    private BigDecimal zwProductPerformance;
    //造旺本品业绩同比
    private BigDecimal zwProductPerformanceYearRatio;
    //造旺本品业绩环比
    private BigDecimal zwProductPerformanceRatio;
    //业绩目标达成率
    private BigDecimal performanceAchievementRate;

    /**
     * queryType = 1 管理岗人均业绩
     */

    @ApiModelProperty(value = "管理岗人均业绩")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPerPerformance;

    @ApiModelProperty(value = "管理岗在职人数")
    @PerformanceValue(serialNumber = "329")
    private Integer managementOnJobNum;
    /**
     * queryType = 2 管理岗人均交易客户数
     */
    @ApiModelProperty(value = "管理岗人均交易客户数")
    @PerformanceValue(serialNumber = "486")
    private BigDecimal managementPositionCustomerNum;

    @ApiModelProperty(value = "客单价")
    @PerformanceValue(serialNumber = "37")
    private BigDecimal perCustomer;

}
