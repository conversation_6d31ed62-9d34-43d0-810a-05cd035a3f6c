package com.wantwant.sfa.backend.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/下午6:40
 */
@Data
@ToString
public class TaskAssignModifyDTO {

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("截止日期")
    private LocalDateTime deadline;

    @ApiModelProperty("操作人姓名")
    private String processUserName;

    @ApiModelProperty("操作人工号")
    private String processUserId;

    @ApiModelProperty("主办人")
    private TaskAssignDTO mainProcessUser;

    @ApiModelProperty("协办人")
    private List<TaskAssignDTO> assistedProcessUsers;

    @ApiModelProperty("抄送人")
    private List<TaskAssignDTO> ccProcessUsers;

    private String remark;
}
