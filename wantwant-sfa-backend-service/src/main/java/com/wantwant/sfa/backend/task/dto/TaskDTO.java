package com.wantwant.sfa.backend.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.time.LocalDate;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午9:39
 */
@Data
@ToString
public class TaskDTO {

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("关联任务ID")
    private Long contextTask;

    @ApiModelProperty("任务性质(1.长期 2.短期)")
    private Integer taskNature;

    @ApiModelProperty("任务类型(1.交办任务 2.个人任务 3.部门任务)")
    private Integer taskType;

    @ApiModelProperty("组织划分:1.后勤、2.市场、3.业务、4.产研")
    private Integer divisionType;

    @ApiModelProperty("任务大类(1.开源 2.截流)")
    private Integer category;

    @ApiModelProperty("任务子类(1.后勤销售 2.造旺APP 3.SFA产品功能 4.产品功能)")
    private Integer taskSubType;

    @ApiModelProperty("任务来源(1.常规 2.双周会 3.季度会议 4.月会)")
    private Integer taskSource;

    @ApiModelProperty("任务来源选择其他时填写")
    private String taskSourceOther;

    private String deptCode;

    @ApiModelProperty("任务内容")
    private String content;

    @ApiModelProperty("任务背景")
    private String background;

    @ApiModelProperty("周报月报名称")
    private String reportTitle;

    @ApiModelProperty("任务目的")
    private String purpose;

    @ApiModelProperty("任务优先级(1.低 2.中 3.高 4.极高)")
    private Integer priority;

    @ApiModelProperty("任务价值")
    private String worth;

    @ApiModelProperty("办理截止时间")
    private LocalDateTime deadline;

    @ApiModelProperty("附件")
    private String annex;

    @ApiModelProperty("主办人")
    private TaskAssignDTO mainProcessUser;

    @ApiModelProperty("协办人")
    private List<TaskAssignDTO> assistedProcessUsers;

    @ApiModelProperty("抄送人")
    private List<TaskAssignDTO> ccProcessUsers;

    @ApiModelProperty("是否提交:1.提交 0.否")
    private Integer submit;

    private String createUserName;

    private String createUserId;

    @ApiModelProperty("是否转移:1.是")
    private Boolean transfer;

    @ApiModelProperty("紧急程度")
    private String urgencyLevel;
    @ApiModelProperty("紧急原因")
    @Size(max = 500,message = "紧急原因过长")
    private String urgencyReason;

    @ApiModelProperty("关联外键")
    private Long fKey;
    @ApiModelProperty("类型:1.周报 2.月报")
    private Integer type;
    @ApiModelProperty("问题ID")
    private Long issueId;
}
