package com.wantwant.sfa.backend.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/10/下午3:02
 */
@Data
@ToString
public class TaskSituationDTO {

    private Long taskId;

    private String processUserId;

    private String processUserName;

    private String situation;

    private Integer requireCallback;

    @ApiModelProperty("附件")
    private String appendix;

    private LocalDateTime expectedFinishDate;
}
