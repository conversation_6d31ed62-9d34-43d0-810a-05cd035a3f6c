package com.wantwant.sfa.backend.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/10/16/下午1:52
 */
@Data
public class TaskTraceDTO {

    @ApiModelProperty("traceId")
    private Long traceId;

    private Long taskId;

    @ApiModelProperty("任务实力ID")
    private Long instanceId;

    @ApiModelProperty("任务类型(1.交办任务 2.个人任务 3.部门任务)")
    private Integer taskType;

    private Integer taskSubType;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("提交人")
    private String assignUserName;

    private Integer assignType;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("紧急程度")
    private String urgencyLevel;

    @ApiModelProperty("任务大类(1.开源 2.截流)")
    private Integer category;

    @ApiModelProperty("任务来源(1.常规 2.双周会 3.季度会议 4.月会 5.点对点交办 6.其他)")
    private Integer taskSource;
    @ApiModelProperty("任务来源选择其他时填写")
    private Integer taskSourceOther;

    @ApiModelProperty("任务性质(1.长期 2.短期)")
    private Integer taskNature;

    @ApiModelProperty("任务内容")
    private String content;

    @ApiModelProperty("任务优先级：1.低 2.中 3.高 4.极高")
    private Integer priority;

    @ApiModelProperty("任务价值")
    private String worth;

    private LocalDateTime publishTime;

    private LocalDateTime deadline;

    private Integer status;

    private LocalDateTime refreshTime;

    private LocalDateTime expectedFinishDate;

    private Integer auditStatus;

    private String situation;

    private Integer requireCallback;

    private LocalDateTime updateTime;
}
