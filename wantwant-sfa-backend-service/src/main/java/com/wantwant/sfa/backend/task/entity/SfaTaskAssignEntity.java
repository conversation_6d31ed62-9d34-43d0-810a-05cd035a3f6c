package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@TableName("sfa_task_assign")
@ApiModel(value = "SfaTaskAssign对象", description = "")
@Data
public class SfaTaskAssignEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "assign_id", type = IdType.AUTO)
    private Long assignId;

    @ApiModelProperty("指派人工号")
    private String assignUserId;

    @ApiModelProperty("指派人名称")
    private String assignUserName;

    @ApiModelProperty("指派人部门CODE")
    private String assignDeptCode;
    @ApiModelProperty("指派人部门名称")
    private String assignDeptName;

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("指派类型(1.主办 2.协办 3.抄送)")
    private Integer assignType;

    @ApiModelProperty("状态(0.无效 1.有效)")
    private Integer status;

}
