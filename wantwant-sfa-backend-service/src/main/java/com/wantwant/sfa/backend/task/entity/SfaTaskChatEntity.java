package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-07
 */
@TableName("sfa_task_chat")
@ApiModel(value = "SfaTaskChat对象", description = "")
@Data
public class SfaTaskChatEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "chat_id", type = IdType.AUTO)
    private Long chatId;

    @ApiModelProperty("sfa_task_log主键")
    private Long logId;

    @ApiModelProperty("父级ID")
    private Long parentId;

    @ApiModelProperty("操作人工号")
    private String employeeId;

    @ApiModelProperty("操作人名称")
    private String employeeName;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("是否删除(1.是）")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}
