package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import com.wantwant.sfa.backend.util.ComparableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@TableName("sfa_task")
@ApiModel(value = "SfaTask对象", description = "")
@Data
public class SfaTaskEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    @ApiModelProperty("任务名称")
    @ComparableField("任务名称")
    private String taskName;

    @ApiModelProperty("是否挂起:1.是 0.否")
    @ComparableField("是否挂起")
    private Integer suspend = 0;

    @ApiModelProperty("关联任务ID")
    @TableField(value = "context_task",strategy = FieldStrategy.IGNORED)
    @ComparableField("关联任务ID")
    private Long contextTask;

    @ApiModelProperty("组织划分:1.后勤、2.市场、3.业务、4.产研")
    @ComparableField(value = "组织划分",replace = {"1_后勤","2_市场","3_业务","4_产研"})
    private Integer divisionType;

    @ApiModelProperty("任务类型(1.交办任务 2.个人任务)")
    private Integer taskType;

    @ApiModelProperty("任务大类(1.开源 2.截流)")
    @ComparableField(value = "任务大类",replace = {"1_开源","2_截流"})
    private Integer category;

    @ApiModelProperty("任务内容")
    @ComparableField("任务内容")
    private String content;

    @ApiModelProperty("任务背景")
    @ComparableField("任务背景")
    private String background;

    @ApiModelProperty("周报月报名称")
    @ComparableField("周报月报名称")
    private String reportTitle;

    @ApiModelProperty("任务目的")
    @ComparableField("任务目的")
    private String purpose;

    @ApiModelProperty("任务优先级(1.低 2.中 3.高 4.极高)")
    @ComparableField(value = "任务优先级",replace = {"1_低","2_中","3_高","4_极高"})
    private Integer priority;

    @ApiModelProperty("任务价值")
    @ComparableField("任务价值")
    private String worth;

    @ApiModelProperty("办理截止时间")
    @ComparableField("办理截止时间")
    private LocalDateTime deadline;

    @ApiModelProperty("状态(0.无效 10.草稿 20.待发布 30.待签收 40.进行中 50.送审 60.办结 80.关闭)")
    private Integer status;

    @ApiModelProperty("附件")
    @ComparableField("附件")
    private String annex;

    @ApiModelProperty("任务子类(1.后勤销售 2.造旺APP产品功能 3.SFA产品功能 4.产品功能)")
    @ComparableField(value = "任务子类",replace = {"1_后勤","2_销售","3_造旺APP产品功能","4_SFA产品功能'"})
    private Integer taskSubType;

    @ApiModelProperty("任务来源(1.常规 2.双周会 3.季度会议 4.月会)")
    @ComparableField(value = "任务来源",replace = {"1_常规","2_双周会","3_季度会议","4_月会","5_点对点交办","6_其他"})
    private Integer taskSource;

    @ApiModelProperty("任务来源选择其他时填写")
    @ComparableField("任务来源")
    private String taskSourceOther;


    @ApiModelProperty("部门任务所属部门code")
    @ComparableField("部门任务所属部门")
    private String deptCode;


    @ApiModelProperty("任务性质(1.长期 2.短期)")
    @ComparableField(value = "任务性质",replace = {"1_长期","2_短期"})
    private Integer taskNature;

    @ApiModelProperty("发布时间")
    @ComparableField("发布时间")
    private LocalDateTime publishTime;


    private String realCreateUserId;

    private String realCreateUserName;


    @TableField(value = "task_tag",strategy = FieldStrategy.IGNORED)
    private String taskTag;

    @ComparableField("紧急程度")
    private String urgencyLevel;
    @ComparableField("备注原因")
    private String urgencyReason;
}
