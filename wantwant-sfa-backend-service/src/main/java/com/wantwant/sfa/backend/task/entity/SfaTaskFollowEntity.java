package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@TableName("sfa_task_follow")
@ApiModel(value = "SfaTaskFollow对象", description = "")
@Data
public class SfaTaskFollowEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "follow_id", type = IdType.AUTO)
    private Long followId;

    @ApiModelProperty("sfa_task主键")
    private Long taskId;

    @ApiModelProperty("关注人工号")
    private String employeeId;

    @ApiModelProperty("删除标志(1.删除 )")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}
