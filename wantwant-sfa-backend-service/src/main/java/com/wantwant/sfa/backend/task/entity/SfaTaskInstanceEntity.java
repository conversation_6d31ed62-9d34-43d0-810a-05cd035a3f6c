package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@TableName("sfa_task_instance")
@ApiModel(value = "SfaTaskInstance对象", description = "")
@Data
public class SfaTaskInstanceEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "process_id", type = IdType.AUTO)
    private Long processId;

    @ApiModelProperty("sfa_task主键")
    private Long taskId;

    @ApiModelProperty("任务明细ID")
    private Long recordId;

    @ApiModelProperty("任务步骤(10.发布审核 20.签收 30.送审)")
    private Integer processStep;

    @ApiModelProperty("处理结果(0.待处理 1.处理完成 2.驳回 4.关闭)")
    private Integer processResult;

    @ApiModelProperty("状态(0.无效 10.草稿 20.待发布 30.待签收 40.进行中 50.送审 60.办结 80.关闭)")
    private Integer status;

}
