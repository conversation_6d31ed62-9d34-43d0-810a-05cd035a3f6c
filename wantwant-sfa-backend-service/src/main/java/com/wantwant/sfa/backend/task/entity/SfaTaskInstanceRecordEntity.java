package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@TableName("sfa_task_instance_record")
@ApiModel(value = "SfaTaskInstanceRecord对象", description = "")
@Data
public class SfaTaskInstanceRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    @ApiModelProperty("任务步骤(10.发布审核 20.签收 30.送审)")
    private Integer processStep;

    @ApiModelProperty("处理结果(0.待处理 1.处理完成 2.驳回 4.关闭)")
    private Integer processResult;

    @ApiModelProperty("处理人工号")
    private String processUserId;

    @ApiModelProperty("处理人姓名")
    private String processUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("处理时间")
    private LocalDateTime processTime;

    @ApiModelProperty("状态（0.无效 1.有效)")
    private Integer status;

    @ApiModelProperty("删除标志(0.未删除 1.删除)")
    private Integer deleteFlag;

    @ApiModelProperty("上一步ID")
    private Long prevRecord;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("下一步ID")
    @TableField(value = "next_record",strategy = FieldStrategy.IGNORED)
    private Long nextRecord;

}
