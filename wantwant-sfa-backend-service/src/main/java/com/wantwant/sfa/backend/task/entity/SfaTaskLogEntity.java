package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@TableName("sfa_task_log")
@ApiModel(value = "SfaTaskLog对象", description = "")
@Data
public class SfaTaskLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;

    @ApiModelProperty("sfa_task主键")
    private Long taskId;

    @ApiModelProperty("类型(1.发布任务 2.确认发布 3.签收 4.提交结果 5.送审 6.确认办结 7.添加交办人 8.挂起 9.催办)")
    private Integer type;

    @ApiModelProperty("创建人工号")
    private String createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("操作对象")
    private String processObj;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("删除标志(1.删除)")
    private Integer deleteFlag;

    @ApiModelProperty("状态(0.无效 1.有效)")
    private Integer status;

    @ApiModelProperty("差异")
    private String diff;


    private Long traceId;


    public void init(String processUserId, String processUserName) {
        this.createUserId = processUserId;
        this.createUserName = processUserName;
        this.deleteFlag = 0;
        this.status = 1;
        this.createTime = LocalDateTime.now();
    }
}
