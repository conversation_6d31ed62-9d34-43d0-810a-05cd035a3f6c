package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 交办任务会议预约表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@TableName("sfa_task_meeting")
@ApiModel(value = "SfaTaskMeeting对象", description = "交办任务会议预约表")
@Data
public class SfaTaskMeetingEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("sfa_task主键")
    private Long taskId;

    @ApiModelProperty("sfa_meeting_info主键")
    private Long infoId;

    @ApiModelProperty("建议开会日期")
    private String meetingSuggestionTime;

}
