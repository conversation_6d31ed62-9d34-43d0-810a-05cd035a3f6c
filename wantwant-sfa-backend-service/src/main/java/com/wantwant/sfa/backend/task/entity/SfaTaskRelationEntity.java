package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 外部任务关联
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@TableName("sfa_task_relation")
@ApiModel(value = "SfaTaskRelation对象", description = "外部任务关联")
@Data
public class SfaTaskRelationEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "relation_id", type = IdType.AUTO)
    private Long relationId;

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("外部主键(周报，月报主键)")
    private Long fKey;

    @ApiModelProperty("类型(1.周报 2.月报)")
    private Integer type;

    @ApiModelProperty("问题ID")
    private Long issueId;

}
