package com.wantwant.sfa.backend.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.wantwant.sfa.backend.common.entity.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@TableName("sfa_task_situation_trace")
@ApiModel(value = "SfaTaskSituationTrace对象", description = "")
@Data
public class SfaTaskSituationTraceEntity extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "trace_id", type = IdType.AUTO)
    private Long traceId;

    private String empId;

    @ApiModelProperty("sfa_task主键")
    private Long taskId;

    @ApiModelProperty("办理情况")
    private String situation;

    @ApiModelProperty("预计完成时间")
    private LocalDateTime expectedFinishDate;

    @ApiModelProperty("状态(0.无效 1.有效)")
    private Integer status;

    @ApiModelProperty("是否需要回复")
    private Integer requireCallback;

    @ApiModelProperty("回复内容")
    private String callback;

    private LocalDateTime callbackTime;

    @ApiModelProperty("审核人工号")
    private String auditEmpId;

    @ApiModelProperty("审核人名称")
    private String auditEmpName;

    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty("审核状态（0.未审核 1.通过 2.驳回）")
    private Integer auditStatus;

    @ApiModelProperty("审核备注")
    private String auditComment;

    @ApiModelProperty("附件")
    private String appendix;

    private Integer isCurrent;
}
