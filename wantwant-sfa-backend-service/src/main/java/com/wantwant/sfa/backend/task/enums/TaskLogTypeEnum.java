package com.wantwant.sfa.backend.task.enums;

import lombok.AllArgsConstructor;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午8:46
 */
@AllArgsConstructor
public enum TaskLogTypeEnum {
    PUSH_TASK(1,"发布任务"),
    CONFIRM_PUSH(2,"确认发布"),
    SIGN(3,"签收"),
    SUBMIT_RESULT(4,"提交结果"),
    READY_AUDIT(5,"送审"),
    FINISH(6,"确认办结"),
    MODIFY_ASSIGN(7,"修改协办人"),
    SUSPEND(8,"挂起"),
    REMINDER(9,"催办"),
    CLOSED(10,"关闭"),
    UN_SUSPEND(11,"取消挂起"),
    REDONE(12,"重办"),
    MODIFY_DEADLINE(13,"修改截止日期"),
    AUDIT_PUSH(14,"发布审核"),
    REFUSE(15,"驳回"),
    LAUNCH_MEETING(16,"发起会议"),
    AUDIT_MEETING(17,"审核"),
    MODIFY_MAIN_ASSIN(18,"修改主办人"),
    CREATE_MEETING(19,"创建会议"),
    MODIFY(20,"修改任务");
    // 1.发布任务 2.确认发布 3.签收 4.提交结果 5.送审 6.确认办结 7.添加交办人 8.挂起 9.催办 10.关闭

    private int type;

    private String name;

    public static String getTypeName(Integer type) {
        TaskLogTypeEnum[] values = TaskLogTypeEnum.values();
        for(TaskLogTypeEnum e: values){
            if(e.getType() == type){
                return e.getName();
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
