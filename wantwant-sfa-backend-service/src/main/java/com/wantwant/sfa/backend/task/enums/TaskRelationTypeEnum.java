package com.wantwant.sfa.backend.task.enums;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/21/上午9:49
 */
public enum TaskRelationTypeEnum {
    WEEK_REPORT(1,"周报"),
    MONTH_REPORT(2,"月报");

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    TaskRelationTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
}
