package com.wantwant.sfa.backend.task.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/14/下午8:51
 */
@Data
public class TaskModel {

    private Long taskId;

    private String taskName;

    private String taskTag;

    private Integer taskNature;

    private Integer taskType;

    private Integer priority;

    private Integer status;

    private Integer weekRefresh;

    private Integer taskSubType;

    private Integer suspend;

    private String createUserName;

    private LocalDateTime createTime;

    private LocalDateTime deadline;

    private LocalDateTime lastModifyTime;

    private LocalDateTime updateTime;

    private LocalDateTime publishTime;

    private String tempName;


    private String urgencyLevel;
}
