package com.wantwant.sfa.backend.task.service;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.taskManagement.request.*;
import com.wantwant.sfa.backend.taskManagement.vo.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午7:15
 */
public interface ITaskApplication {
    /**
     * 创建任务
     *
     * @param createTaskRequest
     */
    void createTask(CreateTaskRequest createTaskRequest);

    /**
     * 修改任务
     *
     * @param updateTaskRequest
     */
    void updateTask(UpdateTaskRequest updateTaskRequest);

    /**
     * 获取指派人信息
     *
     * @return
     */
    List<AssignVo> getAssign(String employeeName);

    /**
     * 获取任务情况
     *
     * @param taskId
     * @return
     */
    List<TaskSituationVo> getSituation(Long taskId);

    /**
     * 获取任务操作记录
     *
     * @param taskId
     * @return
     */
    List<TaskLogVo> getTaskLog(Long taskId);

    /**
     * 获取任务明细
     *
     * @param taskId
     * @return
     */
    TaskDetailVo getDetail(Long taskId,String person);

    /**
     * 任务推送
     *
     * @param request
     */
    void publish(TaskOperatorRequest request);

    /**
     * 任务回退
     *
     * @param request
     */
    void revert(TaskOperatorRequest request);

    /**
     * 任务签收
     *
     * @param request
     */
    void sign(TaskOperatorRequest request);

    /**
     * 修改指派人
     *
     * @param request
     */
    void modifyAssign(TaskAssignModifyRequest request);

    /**
     * 状态提交
     *
     * @param taskSituationSubmitRequest
     */
    void submitSituation(TaskSituationSubmitRequest taskSituationSubmitRequest);

    /**
     * 任务完成
     *
     * @param request
     */
    void complete(TaskCompleteRequest request);

    /**
     * 任务完结
     *
     * @param request
     */
    void finish(TaskOperatorRequest request);

    /**
     * 重办
     *
     * @param request
     */
    void redo(TaskRedoRequest request);

    /**
     * 驳回
     *
     * @param request
     */
    void refuse(TaskRefuseRequest request);

    /**
     * 挂起任务
     *
     * @param request
     */
    void suspend(TaskSuspendRequest request);

    /**
     * 关闭任务
     *
     * @param request
     */
    void close(TaskOperatorRequest request);

    /**
     * 根据任务名称获取任务
     *
     * @param taskName
     * @return
     */
    List<TaskSampleVo> searchTaskByName(String taskName);

    /**
     * 任务查询
     *
     * @param taskSelectRequest
     * @return
     */
    Page<TaskVo> selectList(TaskSelectRequest taskSelectRequest);

    void downloadList(TaskSelectRequest request, HttpServletRequest req, HttpServletResponse res);

    /**
     * 任务信息查询
     *
     * @param person
     * @return
     */
    TaskInfoVo taskInfo(String person);

    /**
     * 修改截止日期
     *
     * @param modifyDeadlineRequest
     */
    void modifyDeadline(ModifyDeadlineRequest modifyDeadlineRequest);

    /**
     * 删除任务
     *
     * @param request
     */
    void delete(TaskOperatorRequest request);

    /**
     * 关注任务
     *
     * @param taskFollowRequest
     */
    void follow(TaskFollowRequest taskFollowRequest);

    /**
     * 任务进度追踪
     *
     * @param request
     * @return
     */
    Page<TaskTraceVo> selectTaskTraceAudit(TaskTraceAuditSearchRequest request);

    void callback(TraceCallBackRequest traceCallBackRequest);

    void audit(TraceAuditRequest traceAuditRequest);

    void taskTraceExport(TaskTraceAuditSearchRequest request);

    Boolean auditPermission(String person);

    void batchFinish(TaskBatchCommand command);

    void batchPublish(TaskBatchCommand command);

    /**
     * 发起会议
     *
     * @param launchMeetingRequest
     */
    void launchMeeting(LaunchMeetingRequest launchMeetingRequest);

    /**
     * 待确认会议审核
     *
     * @param taskAuditRequest
     */
    void meetingAudit(TaskAuditRequest taskAuditRequest);

    /**
     * 确认完结
     *
     * @param taskAuditRequest
     */
    void meetingAuditFinish(TaskAuditRequest taskAuditRequest);

    /**
     * 修改任务关联
     *
     * @param taskContextModifyRequest
     */
    void taskContextModify(TaskContextModifyRequest taskContextModifyRequest);

    /**
     * 催办任务
     *
     * @param taskAuditRequest
     */
    void urge(TaskAuditRequest taskAuditRequest);

    /**
     * 批量催办
     *
     * @param command
     */
    void batchUrge(TaskBatchCommand command);

    void modifyTask(UpdateTaskRequest updateTaskRequest);


}
