package com.wantwant.sfa.backend.task.service;

import com.wantwant.sfa.backend.taskManagement.request.TaskChatDelRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatEditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskLogReplyRequest;
import com.wantwant.sfa.backend.taskManagement.vo.TaskChatReplyVo;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/07/下午2:08
 */
public interface ITaskChatService {
    /**
     * 回复
     *
     * @param taskLogReplyRequest
     */
    void reply(TaskLogReplyRequest taskLogReplyRequest);

    /**
     * 获取回复内容
     *
     * @param logId
     * @return
     */
    List<TaskChatReplyVo> getReply(Long logId,String person);

    /**
     * 删除回复内容
     *
     * @param taskChatDelRequest
     */
    void deleteReply(TaskChatDelRequest taskChatDelRequest);

    /**
     * 编辑回复内容
     *
     * @param taskChatEditRequest
     */
    void edit(TaskChatEditRequest taskChatEditRequest);

}
