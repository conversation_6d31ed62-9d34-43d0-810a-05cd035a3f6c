package com.wantwant.sfa.backend.task.service;

import com.wantwant.sfa.backend.task.dto.TaskCompleteDTO;
import com.wantwant.sfa.backend.task.dto.TaskCustomerDTO;
import com.wantwant.sfa.backend.task.dto.TaskFollowDTO;
import com.wantwant.sfa.backend.task.dto.TaskSituationDTO;
import com.wantwant.sfa.backend.taskManagement.request.TaskOperatorRequest;
import com.wantwant.sfa.backend.taskManagement.request.TraceAuditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TraceCallBackRequest;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/10/上午10:09
 */
public interface ITaskCustomerService {

    /**
     * 任务签收
     *
     * @param taskCustomerDTO
     */
    void sign(TaskCustomerDTO taskCustomerDTO);

    /**
     * 提交完成情况
     *
     * @param taskSituationDTO
     */
    void situationSubmit(TaskSituationDTO taskSituationDTO);

    /**
     * 完成并送审核
     */
    void complete(TaskCompleteDTO taskCompleteDTO);

    /**
     * 关注任务
     *
     * @param taskFollowDTO
     */
    void follow(TaskFollowDTO taskFollowDTO);

    /**
     * 删除任务
     *
     * @param request
     */
    void delete(TaskOperatorRequest request);

    void callback(TraceCallBackRequest traceCallBackRequest);

    void audit(TraceAuditRequest traceAuditRequest);


}
