package com.wantwant.sfa.backend.task.service;


import com.wantwant.sfa.backend.task.dto.TaskAssignDTO;
import com.wantwant.sfa.backend.taskManagement.request.LaunchMeetingRequest;
import com.wantwant.sfa.backend.taskManagement.request.ModifyDeadlineRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskAuditRequest;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午11:40
 */
public interface ITaskProcessService {
    /**
     * 创建流程
     *
     * @param taskId
     * @param taskType
     * @param createUserId
     * @param createUserName
     */
    void initProcess(Long taskId,Integer taskType,String createUserId,String createUserName);

    /**
     * 更换流程处理人
     *
     * @param taskId
     * @param
     */
    void changeProcessUser(Long taskId, TaskAssignDTO mainProcessUser);


    /**
     * 挂起任务
     *
     * @param taskId
     * @param
     */
    void suspend(Long taskId,Integer suspend,String remark, String processUserId,String processUserName);


    /**
     * 关闭任务
     *
     * @param taskId
     * @param
     */
    void closed(Long taskId,String remark, String processUserId,String processUserName);

    /**
     * 修改截止日期
     *
     * @param modifyDeadlineRequest
     */
    void modifyDeadline(ModifyDeadlineRequest modifyDeadlineRequest);

    /**
     * 发起会议s
     *
     * @param launchMeetingRequest
     */
    void launchMeeting(LaunchMeetingRequest launchMeetingRequest);

    /**
     * 待会议确认审核
     *
     * @param taskAuditRequest
     */
    void meetingAudit(TaskAuditRequest taskAuditRequest);

    /**
     * 发起会议确认完结
     *
     * @param taskAuditRequest
     */
    void meetingAuditFinish(TaskAuditRequest taskAuditRequest);

}
