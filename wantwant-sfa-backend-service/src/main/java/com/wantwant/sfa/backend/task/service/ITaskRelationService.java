package com.wantwant.sfa.backend.task.service;

import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.task.dto.TaskIssueRelationDTO;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/21/上午9:01
 */
public interface ITaskRelationService {
    /**
     * 保存任务关系
     *
     * @param taskId
     * @param fKey
     * @param type
     * @param issueId
     * @param processUserDO
     */
    void saveRelation(Long taskId,Long fKey, Integer type, Long issueId, ProcessUserDO processUserDO);

    /**
     * 查询任务关联
     *
     * @param fKey
     * @param type
     * @return
     */
    List<TaskIssueRelationDTO> selectTask(Long fKey, Integer type);
}
