package com.wantwant.sfa.backend.task.service;

import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.taskManagement.request.TaskSelectRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskTraceAuditSearchRequest;
import com.wantwant.sfa.backend.taskManagement.vo.*;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/14/上午10:23
 */
public interface ITaskSearchService {

    /**
     * 获取任务情况
     *
     * @param taskId
     * @return
     */
    List<TaskSituationVo> getSituation(Long taskId);

    /**
     * 获取任务记录
     *
     * @param taskId
     * @return
     */
    List<TaskLogVo> getTaskLog(Long taskId);

    /**
     * 获取任务明细
     *
     * @param taskId
     * @return
     */
    TaskDetailVo getDetail(Long taskId,String person);

    /**
     * 根据任务名称获取任务名称
     *
     * @param taskName
     * @return
     */
    List<TaskSampleVo> searchTaskByName(String taskName);

    /**
     * 任务查询
     *
     * @param taskSelectRequest
     * @return
     */
    Page<TaskVo> selectList(TaskSelectRequest taskSelectRequest);

    void downloadList(TaskSelectRequest request, HttpServletRequest req, HttpServletResponse res);

    /**
     * 获取任务信息
     *
     * @param person
     * @return
     */
    TaskInfoVo taskInfo(String person);

    /**
     * 获取有主管的上级部门
     *
     * @param deptId
     * @return
     */
    String getDeptLeadId(Integer deptId);

    /**
     * 任务进度追踪审核列表
     *
     * @param request
     * @return
     */
    Page<TaskTraceVo> selectTaskTraceAudit(TaskTraceAuditSearchRequest request);

    void taskTraceExport(TaskTraceAuditSearchRequest request);

    Boolean auditPermission(String person);
}
