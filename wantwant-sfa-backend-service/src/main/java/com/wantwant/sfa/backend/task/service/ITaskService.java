package com.wantwant.sfa.backend.task.service;

import com.wantwant.sfa.backend.task.dto.TaskAssignModifyDTO;
import com.wantwant.sfa.backend.task.dto.TaskDTO;
import com.wantwant.sfa.backend.taskManagement.request.TaskAuditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskContextModifyRequest;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午9:38
 */
public interface ITaskService {
    /**
     * 保存任务
     *
     * @param taskDTO
     */
    void createTask(TaskDTO taskDTO);



    /**
     * 修改任务
     *
     * @param taskDTO
     */
    void updateTask(TaskDTO taskDTO);

    /**
     * 修改任务指派人
     *
     * @param taskAssignModifyDTO
     */
    void modifyAssign(TaskAssignModifyDTO taskAssignModifyDTO);

    /**
     * 修改任务关联
     *
     * @param taskContextModifyRequest
     */
    void taskContextModify(TaskContextModifyRequest taskContextModifyRequest);

    /**
     * 催办任务
     *
     * @param taskAuditRequest
     */
    void urge(TaskAuditRequest taskAuditRequest);

    void modifyTask(TaskDTO taskDTO);

}
