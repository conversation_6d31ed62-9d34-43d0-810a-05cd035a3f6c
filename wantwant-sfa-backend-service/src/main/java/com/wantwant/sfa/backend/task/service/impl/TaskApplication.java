package com.wantwant.sfa.backend.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.task.dto.*;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.service.*;
import com.wantwant.sfa.backend.taskManagement.request.*;
import com.wantwant.sfa.backend.taskManagement.vo.*;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static io.lettuce.core.GeoArgs.Unit.m;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午7:15
 */
@Service
public class TaskApplication implements ITaskApplication {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private ITaskInfrastructureService taskInfrastructureService;
    @Autowired
    private ITaskSearchService taskSearchService;
    @Autowired
    private ITaskPublishService taskPublishService;
    @Autowired
    private ITaskCustomerService taskCustomerService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ITaskAuditService taskAuditService;
    @Autowired
    private ITaskProcessService taskProcessService;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private RedisUtil redisUtil;

    private String submitSituationLock = "task:submit:";

    @Override
    @Transactional
    public void createTask(CreateTaskRequest createTaskRequest) {

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(createTaskRequest.getPerson(), RequestUtils.getLoginInfo());

        TaskDTO taskDTO = new TaskDTO();
        BeanUtils.copyProperties(createTaskRequest,taskDTO);
        taskDTO.setDeadline(createTaskRequest.getDeadline().atStartOfDay());
        taskDTO.setCreateUserName(personInfo.getEmployeeName());
        taskDTO.setCreateUserId(personInfo.getEmployeeId());


        List<TaskAnnexRequest> annex = createTaskRequest.getAnnex();
        if(!CollectionUtils.isEmpty(annex)){
            List<String>urls = new ArrayList<>();
            annex.forEach(e -> {
                urls.add(e.getName()+"$$"+e.getUrl());
            });
            taskDTO.setAnnex(String.join(",",urls));
        }
        TaskAssignRequest mainProcessUser = createTaskRequest.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            BeanUtils.copyProperties(mainProcessUser,taskAssignDTO);
            taskDTO.setMainProcessUser(taskAssignDTO);
        }

        int taskType = createTaskRequest.getTaskType();
        if(taskType == 2 ){
            String deptCode = createTaskRequest.getDeptCode();
            if(StringUtils.isBlank(deptCode)){
                throw new ApplicationException("个人任务必须选择部门");
            }
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpName(personInfo.getEmployeeName());
            taskAssignDTO.setEmpId(personInfo.getEmployeeId());
            taskAssignDTO.setDeptCode(deptCode);
            DepartEntity entity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("dept_code", deptCode).eq("delete_flag", 0).eq("status", 1));
            if(Objects.isNull(entity)){
                throw new ApplicationException("获取部门失败");
            }
            taskAssignDTO.setDeptName(entity.getDeptName());
            taskDTO.setMainProcessUser(taskAssignDTO);


        }
        if(taskType == 3){
            String deptCode = createTaskRequest.getDeptCode();
            if(StringUtils.isBlank(deptCode)){
                throw new ApplicationException("部门任务必须选择部门");
            }
        }

        List<TaskAssignRequest> assistedProcessUsers = createTaskRequest.getAssistedProcessUsers();
        if(!CollectionUtils.isEmpty(assistedProcessUsers)){
            List<TaskAssignDTO> assignDTOS = new ArrayList<>();
            assistedProcessUsers.forEach(e -> {
                TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
                BeanUtils.copyProperties(e,taskAssignDTO);
                assignDTOS.add(taskAssignDTO);
            });
            taskDTO.setAssistedProcessUsers(assignDTOS);
        }


        List<TaskAssignRequest> ccProcessUsers = createTaskRequest.getCcProcessUsers();
        if(!CollectionUtils.isEmpty(ccProcessUsers)){
            List<TaskAssignDTO> assignDTOS = new ArrayList<>();
            ccProcessUsers.forEach(e -> {
                TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
                BeanUtils.copyProperties(e,taskAssignDTO);
                assignDTOS.add(taskAssignDTO);
            });
            taskDTO.setCcProcessUsers(assignDTOS);
        }

        taskService.createTask(taskDTO);
    }

    @Override
    @Transactional
    public void updateTask(UpdateTaskRequest updateTaskRequest) {

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(updateTaskRequest.getPerson(), RequestUtils.getLoginInfo());

        TaskDTO taskDTO = createTaskDTO(updateTaskRequest, personInfo);

        taskService.updateTask(taskDTO);
    }

    private TaskDTO createTaskDTO(UpdateTaskRequest updateTaskRequest, CeoBusinessOrganizationPositionRelation personInfo) {
        TaskDTO taskDTO = new TaskDTO();
        BeanUtils.copyProperties(updateTaskRequest,taskDTO);
        taskDTO.setDeadline(updateTaskRequest.getDeadline().atStartOfDay());
        taskDTO.setCreateUserName(personInfo.getEmployeeName());
        taskDTO.setCreateUserId(personInfo.getEmployeeId());

        TaskAssignRequest mainProcessUser = updateTaskRequest.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            BeanUtils.copyProperties(mainProcessUser,taskAssignDTO);
            taskDTO.setMainProcessUser(taskAssignDTO);
        }

        int taskType = updateTaskRequest.getTaskType();
        if(taskType == 2){
            String deptCode = updateTaskRequest.getDeptCode();
            if(StringUtils.isBlank(deptCode)){
                throw new ApplicationException("个人任务必须选择部门");
            }
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpName(personInfo.getEmployeeName());
            taskAssignDTO.setEmpId(personInfo.getEmployeeId());
            taskAssignDTO.setDeptCode(deptCode);
            DepartEntity entity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("dept_code", deptCode).eq("delete_flag", 0).eq("status", 1));
            if(Objects.isNull(entity)){
                throw new ApplicationException("获取部门失败");
            }
            taskAssignDTO.setDeptName(entity.getDeptName());
            taskDTO.setMainProcessUser(taskAssignDTO);
        }

        List<TaskAnnexRequest> annex = updateTaskRequest.getAnnex();
        if(!CollectionUtils.isEmpty(annex)){
            List<String>urls = new ArrayList<>();
            annex.forEach(e -> {
                urls.add(e.getName()+"$$"+e.getUrl());
            });
            taskDTO.setAnnex(String.join(",",urls));
        }


        List<TaskAssignRequest> assistedProcessUsers = updateTaskRequest.getAssistedProcessUsers();
        if(!CollectionUtils.isEmpty(assistedProcessUsers)){
            List<TaskAssignDTO> assignDTOS = new ArrayList<>();
            assistedProcessUsers.forEach(e -> {
                TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
                BeanUtils.copyProperties(e,taskAssignDTO);
                assignDTOS.add(taskAssignDTO);
            });
            taskDTO.setAssistedProcessUsers(assignDTOS);
        }


        List<TaskAssignRequest> ccProcessUsers = updateTaskRequest.getCcProcessUsers();
        if(!CollectionUtils.isEmpty(ccProcessUsers)){
            List<TaskAssignDTO> assignDTOS = new ArrayList<>();
            ccProcessUsers.forEach(e -> {
                TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
                BeanUtils.copyProperties(e,taskAssignDTO);
                assignDTOS.add(taskAssignDTO);
            });
            taskDTO.setCcProcessUsers(assignDTOS);
        }
        return taskDTO;
    }


    @Override
    public void modifyTask(UpdateTaskRequest updateTaskRequest) {

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(updateTaskRequest.getPerson(), RequestUtils.getLoginInfo());

        TaskDTO taskDTO = createTaskDTO(updateTaskRequest, personInfo);

        taskService.modifyTask(taskDTO);
    }


    @Override
    public List<AssignVo> getAssign(String employeeName) {
        return taskInfrastructureService.getAssign(employeeName);
    }

    @Override
    public List<TaskSituationVo> getSituation(Long taskId) {
        List<TaskSituationVo> list = taskSearchService.getSituation(taskId);
        return list;
    }

    @Override
    public List<TaskLogVo> getTaskLog(Long taskId) {
        List<TaskLogVo> list = taskSearchService.getTaskLog(taskId);
        return list;
    }

    @Override
    public TaskDetailVo getDetail(Long taskId,String person) {
        return taskSearchService.getDetail(taskId,person);
    }

    @Override
    @Transactional
    public void publish(TaskOperatorRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        TaskPublishDTO taskPublishDTO = new TaskPublishDTO();
        taskPublishDTO.setTaskId(request.getTaskId());
        taskPublishDTO.setProcessUserName(personInfo.getEmployeeName());
        taskPublishDTO.setProcessUserId(personInfo.getEmployeeId());

        taskPublishService.publish(taskPublishDTO);
    }



    @Override
    @Transactional
    public void batchPublish(TaskBatchCommand command) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(command.getPerson(), RequestUtils.getLoginInfo());

        List<Long> taskIds = command.getTaskIds();
        taskIds.forEach(e -> {
            TaskPublishDTO taskPublishDTO = new TaskPublishDTO();
            taskPublishDTO.setTaskId(e);
            taskPublishDTO.setProcessUserName(personInfo.getEmployeeName());
            taskPublishDTO.setProcessUserId(personInfo.getEmployeeId());
            taskPublishService.publish(taskPublishDTO);
        });
    }

    @Override
    @Transactional
    public void launchMeeting(LaunchMeetingRequest launchMeetingRequest) {
        taskProcessService.launchMeeting(launchMeetingRequest);
    }

    @Override
    @Transactional
    public void meetingAudit(TaskAuditRequest taskAuditRequest) {
        taskProcessService.meetingAudit(taskAuditRequest);
    }

    @Override
    public void meetingAuditFinish(TaskAuditRequest taskAuditRequest) {
        taskProcessService.meetingAuditFinish(taskAuditRequest);
    }

    @Override
    @Transactional
    public void taskContextModify(TaskContextModifyRequest taskContextModifyRequest) {
        taskService.taskContextModify(taskContextModifyRequest);
    }

    @Override
    @Transactional
    public void urge(TaskAuditRequest taskAuditRequest) {
        taskService.urge(taskAuditRequest);
    }

    @Override
    @Transactional
    public void batchUrge(TaskBatchCommand command) {
        List<Long> taskIds = command.getTaskIds();
        taskIds.forEach(e ->{
            SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(e);
            if(Objects.nonNull(sfaTaskEntity)){
                TaskAuditRequest taskAuditRequest = new TaskAuditRequest();
                taskAuditRequest.setPerson(command.getPerson());



                taskAuditRequest.setRemark("“"+sfaTaskEntity.getTaskName()+"”任务即将到期，请尽快办理！");
                taskAuditRequest.setTaskId(e);
                taskService.urge(taskAuditRequest);
            }

        });
    }




    @Override
    @Transactional
    public void revert(TaskOperatorRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        TaskPublishDTO taskPublishDTO = new TaskPublishDTO();
        taskPublishDTO.setTaskId(request.getTaskId());
        taskPublishDTO.setProcessUserName(personInfo.getEmployeeName());
        taskPublishDTO.setProcessUserId(personInfo.getEmployeeId());
        taskPublishService.revert(taskPublishDTO);
    }

    @Override
    @Transactional
    public void sign(TaskOperatorRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());
        TaskCustomerDTO taskCustomerDTO = new TaskCustomerDTO();
        taskCustomerDTO.setTaskId(request.getTaskId());
        taskCustomerDTO.setProcessUserName(personInfo.getEmployeeName());
        taskCustomerDTO.setProcessUserId(personInfo.getEmployeeId());
        taskCustomerService.sign(taskCustomerDTO);
    }

    @Override
    @Transactional
    public void modifyAssign(TaskAssignModifyRequest request) {
        TaskAssignModifyDTO taskAssignModifyDTO = new TaskAssignModifyDTO();
        taskAssignModifyDTO.setTaskId(request.getTaskId());

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());
        taskAssignModifyDTO.setProcessUserId(personInfo.getEmployeeId());
        taskAssignModifyDTO.setRemark(request.getRemark());
        taskAssignModifyDTO.setProcessUserName(personInfo.getEmployeeName());
        TaskAssignRequest mainProcessUser = request.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){
            TaskAssignDTO mainDTO = new TaskAssignDTO();
            BeanUtils.copyProperties(mainProcessUser,mainDTO);
            taskAssignModifyDTO.setMainProcessUser(mainDTO);
        }


        List<TaskAssignRequest> assistedProcessUsers = request.getAssistedProcessUsers();
        if(!CollectionUtils.isEmpty(assistedProcessUsers)){
            List<TaskAssignDTO> list = new ArrayList<>();
            assistedProcessUsers.forEach(e -> {
                TaskAssignDTO dto = new TaskAssignDTO();
                BeanUtils.copyProperties(e,dto);
                list.add(dto);
            });
            taskAssignModifyDTO.setAssistedProcessUsers(list);
        }


        List<TaskAssignRequest> ccAssistedProcessUsers = request.getCcProcessUsers();
        if(!CollectionUtils.isEmpty(ccAssistedProcessUsers)){
            List<TaskAssignDTO> list = new ArrayList<>();
            ccAssistedProcessUsers.forEach(e -> {
                TaskAssignDTO dto = new TaskAssignDTO();
                BeanUtils.copyProperties(e,dto);
                list.add(dto);
            });
            taskAssignModifyDTO.setCcProcessUsers(list);
        }


        taskService.modifyAssign(taskAssignModifyDTO);
    }

    @Override
    @Transactional
    public void submitSituation(TaskSituationSubmitRequest taskSituationSubmitRequest) {

        String person = taskSituationSubmitRequest.getPerson();

        if (!redisUtil.setLockIfAbsent(submitSituationLock, person, 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("请勿重复操作");
        }


        try {
            CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(taskSituationSubmitRequest.getPerson(), RequestUtils.getLoginInfo());
            TaskSituationDTO taskSituationDTO = new TaskSituationDTO();
            taskSituationDTO.setTaskId(taskSituationSubmitRequest.getTaskId());
            taskSituationDTO.setExpectedFinishDate(LocalDate.parse(taskSituationSubmitRequest.getExpectFinishDate()).atStartOfDay());
            taskSituationDTO.setSituation(taskSituationSubmitRequest.getSituation());
            taskSituationDTO.setProcessUserName(personInfo.getEmployeeName());
            taskSituationDTO.setRequireCallback(taskSituationSubmitRequest.getRequireCallback());
            taskSituationDTO.setProcessUserId(personInfo.getEmployeeId());
            List<TaskAnnexRequest> appendix = taskSituationSubmitRequest.getAppendix();
            if(!CollectionUtils.isEmpty(appendix)){
                List<String> annexUrls = new ArrayList<>();
                appendix.forEach(e -> {
                    annexUrls.add(e.getName()+"$$"+e.getUrl());
                });
                taskSituationDTO.setAppendix(String.join(",",annexUrls));
            }
            taskCustomerService.situationSubmit(taskSituationDTO);
        } finally {
            redisUtil.unLock(submitSituationLock, person);
        }
    }

    @Override
    @Transactional
    public void complete(TaskCompleteRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        TaskCompleteDTO taskCompleteDTO = new TaskCompleteDTO();

        taskCompleteDTO.setTaskId(request.getTaskId());
        taskCompleteDTO.setRemark(request.getRemark());
        taskCompleteDTO.setTaskTag(request.getTaskTag());
        taskCompleteDTO.setProcessUserName(personInfo.getEmployeeName());
        taskCompleteDTO.setProcessUserId(personInfo.getEmployeeId());
        taskCustomerService.complete(taskCompleteDTO);
    }

    @Override
    @Transactional
    public void finish(TaskOperatorRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        taskAuditService.finish(request.getTaskId(),personInfo.getEmployeeId(),personInfo.getEmployeeName(),request.getTaskTag(),request.getRemark());
    }


    @Override
    @Transactional
    public void batchFinish(TaskBatchCommand command) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(command.getPerson(), RequestUtils.getLoginInfo());

        List<Long> taskIds = command.getTaskIds();
        taskIds.forEach(e -> {
            taskAuditService.finish(e,personInfo.getEmployeeId(),personInfo.getEmployeeName(),null,command.getRemark());
        });
    }

    @Override
    @Transactional
    public void redo(TaskRedoRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        taskAuditService.redone(request.getTaskId(),request.getRemark(),request.getDeadline(),request.getTaskTag(),personInfo.getEmployeeId(),personInfo.getEmployeeName(),request.getSendMessage());

    }

    @Override
    @Transactional
    public void refuse(TaskRefuseRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        taskAuditService.refuse(request.getTaskId(),request.getRemark(),request.getTaskTag(),personInfo.getEmployeeId(),personInfo.getEmployeeName());

    }

    @Override
    @Transactional
    public void suspend(TaskSuspendRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        taskProcessService.suspend(request.getTaskId(),request.getSuspend(),request.getRemark(),personInfo.getEmployeeId(),personInfo.getEmployeeName());
    }

    @Override
    @Transactional
    public void close(TaskOperatorRequest request) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());

        taskProcessService.closed(request.getTaskId(),request.getRemark(),personInfo.getEmployeeId(),personInfo.getEmployeeName());
    }

    @Override
    public List<TaskSampleVo> searchTaskByName(String taskName) {

        return taskSearchService.searchTaskByName(taskName);
    }

    @Override
    public Page<TaskVo> selectList(TaskSelectRequest taskSelectRequest) {


        return taskSearchService.selectList(taskSelectRequest);
    }

    @Override
    public void downloadList(TaskSelectRequest request, HttpServletRequest req, HttpServletResponse res) {
        taskSearchService.downloadList(request, req, res);
    }

    @Override
    public TaskInfoVo taskInfo(String person) {
        return taskSearchService.taskInfo(person);
    }

    @Override
    public void modifyDeadline(ModifyDeadlineRequest modifyDeadlineRequest) {
        taskProcessService.modifyDeadline(modifyDeadlineRequest);
    }

    @Override
    @Transactional
    public void delete(TaskOperatorRequest request) {
        taskCustomerService.delete(request);
    }

    @Override
    @Transactional
    public void follow(TaskFollowRequest taskFollowRequest) {
        TaskFollowDTO taskFollowDTO = new TaskFollowDTO();
        BeanUtils.copyProperties(taskFollowRequest,taskFollowDTO);
        taskFollowDTO.setEmployeeId(taskFollowRequest.getPerson());
        taskCustomerService.follow(taskFollowDTO);
    }

    @Override
    public Page<TaskTraceVo> selectTaskTraceAudit(TaskTraceAuditSearchRequest request) {

        Page<TaskTraceVo> page = taskSearchService.selectTaskTraceAudit(request);

        return page;
    }

    @Override
    @Transactional
    public void callback(TraceCallBackRequest traceCallBackRequest) {
        taskCustomerService.callback(traceCallBackRequest);
    }

    @Override
    @Transactional
    public void audit(TraceAuditRequest traceAuditRequest) {
        taskCustomerService.audit(traceAuditRequest);
    }

    @Override
    public void taskTraceExport(TaskTraceAuditSearchRequest request) {
        request.setNeedPage(false);
        taskSearchService.taskTraceExport(request);
    }

    @Override
    public Boolean auditPermission(String person) {
        return taskSearchService.auditPermission(person);
    }



}
