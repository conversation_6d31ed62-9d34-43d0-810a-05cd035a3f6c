package com.wantwant.sfa.backend.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.task.SfaTaskLogMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.mapper.task.TaskChatMapper;
import com.wantwant.sfa.backend.metrics.vo.AlterMetricsVo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.task.entity.SfaTaskChatEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskLogEntity;
import com.wantwant.sfa.backend.task.service.ITaskChatService;
import com.wantwant.sfa.backend.taskManagement.request.TaskAssignRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatDelRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatEditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskLogReplyRequest;
import com.wantwant.sfa.backend.taskManagement.vo.TaskChatReplyVo;
import com.wantwant.sfa.backend.util.TreeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/07/下午2:09
 */
@Service
@Slf4j
public class TaskChatService implements ITaskChatService {
    @Autowired
    private SfaTaskLogMapper sfaTaskLogMapper;
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private TaskChatMapper taskChatMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private ICheckCustomerService checkCustomerService;

    @Override
    @Transactional
    public void reply(TaskLogReplyRequest taskLogReplyRequest) {

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(taskLogReplyRequest.getPerson(), RequestUtils.getLoginInfo());

        SfaTaskLogEntity sfaTaskLogEntity = sfaTaskLogMapper.selectById(taskLogReplyRequest.getLogId());
        if(Objects.isNull(sfaTaskLogEntity)){
            throw new ApplicationException("任务记录不存在");
        }

        Long taskId = sfaTaskLogEntity.getTaskId();
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        // 保存回复内容
        SfaTaskChatEntity sfaTaskChatEntity = new SfaTaskChatEntity();
        sfaTaskChatEntity.setLogId(taskLogReplyRequest.getLogId());
        sfaTaskChatEntity.setParentId(taskLogReplyRequest.getParentId());
        sfaTaskChatEntity.setEmployeeId(personInfo.getEmployeeId());
        sfaTaskChatEntity.setEmployeeName(personInfo.getEmployeeName());
        sfaTaskChatEntity.setContent(taskLogReplyRequest.getContext());


        List<NotifyPO> notifyPOS = new ArrayList<>();
        Long parentId = taskLogReplyRequest.getParentId();
        if(Objects.nonNull(parentId)){
            // 回复信息
            SfaTaskChatEntity parentChat = taskChatMapper.selectById(parentId);
            if(Objects.nonNull(parentChat)){
                sfaTaskChatEntity.setContent("回复 "+parentChat.getEmployeeName()+": "+taskLogReplyRequest.getContext());
                // 发送消息
                if(!parentChat.getEmployeeId().equals(taskLogReplyRequest.getPerson())){
                    NotifyPO po = new NotifyPO();
                    po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【"+personInfo.getEmployeeName()+"回复】");
                    po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                    po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【"+personInfo.getEmployeeName()+"回复】");
                    po.setCode("/MissionDetail?taskId="+sfaTaskLogEntity.getTaskId());
                    po.setEmployeeId(parentChat.getEmployeeId());
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                }

            }
        }

        List<TaskAssignRequest> users = taskLogReplyRequest.getUsers();
        if(!CollectionUtils.isEmpty(users)){
            users.forEach(e -> {

                if(!notifyPOS.stream().filter(f -> f.getEmployeeId().equals(e.getEmpId())).findFirst().isPresent()){
                    NotifyPO po = new NotifyPO();
                    po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【"+personInfo.getEmployeeName()+"回复】");
                    po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                    po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【"+personInfo.getEmployeeName()+"回复】");
                    po.setCode("/MissionDetail?taskId="+sfaTaskLogEntity.getTaskId());
                    po.setEmployeeId(e.getEmpId());
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                }


            });
        }

        taskChatMapper.insert(sfaTaskChatEntity);

        if(!CollectionUtils.isEmpty(notifyPOS)){
            notifyService.saveBatch(notifyPOS);
        }
    }

    @Override
    public List<TaskChatReplyVo> getReply(Long logId,String person) {

        SfaTaskLogEntity sfaTaskLogEntity = sfaTaskLogMapper.selectById(logId);
        if(Objects.isNull(sfaTaskLogEntity)){
            throw new ApplicationException("任务记录不存在");
        }

        List<SfaTaskChatEntity> sfaTaskChatEntities = taskChatMapper.selectList(new LambdaQueryWrapper<SfaTaskChatEntity>().eq(SfaTaskChatEntity::getLogId, logId)
                .and(i -> i.and(p1 -> p1.isNotNull(SfaTaskChatEntity::getParentId).eq(SfaTaskChatEntity::getDeleteFlag, 0)).or(p2 -> p2.isNull(SfaTaskChatEntity::getParentId))));

        if(CollectionUtils.isEmpty(sfaTaskChatEntities)){
            return null;
        }

        List<TaskChatReplyVo> list = new ArrayList<>();
        sfaTaskChatEntities.forEach(e -> {
            TaskChatReplyVo taskChatReplyVo = new TaskChatReplyVo();
            if(e.getEmployeeId().equals(person)){
                taskChatReplyVo.setCanProcess(true);
            }
            taskChatReplyVo.setId(e.getChatId());
            taskChatReplyVo.setEmployeeName(e.getEmployeeName());
            taskChatReplyVo.setParentId(e.getParentId());
            taskChatReplyVo.setContent(e.getContent());
            taskChatReplyVo.setCreateTime(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            if(Objects.isNull(e.getParentId()) && e.getDeleteFlag() == 1){
                taskChatReplyVo.setContent("该条评论已删除");
                taskChatReplyVo.setCanReply(false);
                taskChatReplyVo.setCanProcess(false);
            }
            list.add(taskChatReplyVo);
        });

        List<TaskChatReplyVo> taskChatReplyVos = TreeUtil.list2Tree(list);
        taskChatReplyVos.forEach(e -> {
            List<TaskChatReplyVo> children = e.getChildren();
            if(!CollectionUtils.isEmpty(children)){
                List<TaskChatReplyVo> collect = new ArrayList<>();
                addChildren(collect,children);

                collect = collect.stream().sorted(Comparator.comparing(TaskChatReplyVo::getCreateTime)).collect(Collectors.toList());
                e.setChildren(collect);


            }
        });


        taskChatReplyVos = taskChatReplyVos.stream().sorted(Comparator.comparing(TaskChatReplyVo::getId).reversed()).collect(Collectors.toList());
        return taskChatReplyVos;
    }

    private void addChildren(List<TaskChatReplyVo> collect, List<TaskChatReplyVo> children) {

        if(CollectionUtils.isEmpty(children)){
            return;
        }

        collect.addAll(children);

        children.forEach(e -> {
            List<TaskChatReplyVo> c = e.getChildren();
            addChildren(collect,c);
            e.setChildren(null);
        });

    }

    @Override
    @Transactional
    public void deleteReply(TaskChatDelRequest taskChatDelRequest) {
        SfaTaskChatEntity sfaTaskChatEntity = taskChatMapper.selectById(taskChatDelRequest.getId());
        if(Objects.isNull(sfaTaskChatEntity)){
            throw new ApplicationException("所删除的内容不存在");
        }

        sfaTaskChatEntity.setDeleteFlag(1);
        sfaTaskChatEntity.setUpdateTime(LocalDateTime.now());
        taskChatMapper.updateById(sfaTaskChatEntity);


        Long parentId = sfaTaskChatEntity.getChatId();
        if(Objects.nonNull(sfaTaskChatEntity.getParentId())){
            // 递归删除子集
            deleteChildren(parentId);
        }
    }

    private void deleteChildren(Long parentId) {
        List<SfaTaskChatEntity> sfaTaskChatEntities = taskChatMapper.selectList(new LambdaQueryWrapper<SfaTaskChatEntity>().eq(SfaTaskChatEntity::getParentId, parentId).eq(SfaTaskChatEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(sfaTaskChatEntities)){
            return;
        }

        sfaTaskChatEntities.forEach(e -> {
            e.setDeleteFlag(1);
            taskChatMapper.updateById(e);
            deleteChildren(e.getChatId());
        });
    }

    @Override
    public void edit(TaskChatEditRequest taskChatEditRequest) {
        SfaTaskChatEntity sfaTaskChatEntity = taskChatMapper.selectById(taskChatEditRequest.getId());
        if(Objects.isNull(sfaTaskChatEntity)){
            throw new ApplicationException("所删除的内容不存在");
        }

        sfaTaskChatEntity.setContent(taskChatEditRequest.getContent());
        sfaTaskChatEntity.setUpdateTime(LocalDateTime.now());
        taskChatMapper.updateById(sfaTaskChatEntity);
    }
}
