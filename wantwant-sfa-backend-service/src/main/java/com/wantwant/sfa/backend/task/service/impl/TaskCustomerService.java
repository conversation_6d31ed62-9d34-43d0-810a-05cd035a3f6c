package com.wantwant.sfa.backend.task.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.task.*;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.task.dto.*;
import com.wantwant.sfa.backend.task.entity.*;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskProcessStepEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.service.ITaskCustomerService;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import com.wantwant.sfa.backend.task.service.ITaskSearchService;
import com.wantwant.sfa.backend.taskManagement.request.TaskOperatorRequest;
import com.wantwant.sfa.backend.taskManagement.request.TraceAuditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TraceCallBackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/10/上午10:11
 */
@Service
@Slf4j
public class TaskCustomerService implements ITaskCustomerService {
    @Autowired
    private SfaTaskMapper taskMapper;
    @Autowired
    private SfaTaskInstanceMapper sfaTaskInstanceMapper;
    @Autowired
    private SfaTaskInstanceRecordMapper sfaTaskInstanceRecordMapper;
    @Autowired
    private ITaskLogService taskLogService;
    @Autowired
    private SfaTaskAssignMapper sfaTaskAssignMapper;
    @Autowired
    private SfaTaskSituationTraceMapper sfaTaskSituationTraceMapper;
    @Autowired
    private TaskFollowMapper taskFollowMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private ITaskSearchService taskSearchService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    public void sign(TaskCustomerDTO taskCustomerDTO) {
        log.info("【task sign】dto:{}",taskCustomerDTO);

        SfaTaskEntity sfaTaskEntity = taskMapper.selectById(taskCustomerDTO.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        Integer status = sfaTaskEntity.getStatus();
        if(status != TaskStatusEnum.READY_SIGN.getStatus() && status != TaskStatusEnum.RE_SIGN.getStatus()){
            throw new ApplicationException("当前任务不可签收");
        }

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskCustomerDTO.getTaskId()).eq("delete_flag", 0));
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("任务流程实例获取失败");
        }
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("任务记录获取失败");
        }
        String processUserId = sfaTaskInstanceRecordEntity.getProcessUserId();
        if(!processUserId.equals(taskCustomerDTO.getProcessUserId())){
            throw new ApplicationException("非主办人无法签收任务");
        }

        // 修改任务表状态
        sfaTaskEntity.setStatus(TaskStatusEnum.PROCESSING.getStatus());
        sfaTaskEntity.setUpdateUserName(taskCustomerDTO.getProcessUserName());
        sfaTaskEntity.setUpdateUserId(taskCustomerDTO.getProcessUserId());
        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        taskMapper.updateById(sfaTaskEntity);

        // 创建一条进行中的记录
        SfaTaskInstanceRecordEntity nextRecord = new SfaTaskInstanceRecordEntity();
        nextRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
        nextRecord.setProcessStep(TaskProcessStepEnum.PROCESSING.getProcessStep());
        nextRecord.setProcessResult(3);
        nextRecord.setProcessUserId(taskCustomerDTO.getProcessUserId());
        nextRecord.setProcessUserName(taskCustomerDTO.getProcessUserName());
        nextRecord.setCreateTime(LocalDateTime.now());
        nextRecord.setStatus(1);
        nextRecord.setDeleteFlag(0);
        nextRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
        sfaTaskInstanceRecordMapper.insert(nextRecord);

        // 记录表绑定
        sfaTaskInstanceRecordEntity.setNextRecord(nextRecord.getRecordId());
        sfaTaskInstanceRecordEntity.setProcessResult(1);
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);


        sfaTaskInstanceEntity.setProcessStep(nextRecord.getProcessStep());
        sfaTaskInstanceEntity.setProcessResult(nextRecord.getProcessResult());
        sfaTaskInstanceEntity.setRecordId(nextRecord.getRecordId());
        sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskInstanceEntity.setUpdateUserId(taskCustomerDTO.getProcessUserId());
        sfaTaskInstanceEntity.setUpdateUserName(taskCustomerDTO.getProcessUserName());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);


        // 操作记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(sfaTaskEntity.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.SIGN.getType());
        taskLogDTO.setProcessUserId(taskCustomerDTO.getProcessUserId());
        taskLogDTO.setProcessUserName(taskCustomerDTO.getProcessUserName());
        taskLogService.saveLog(taskLogDTO);
    }

    @Override
    @Transactional
    public void situationSubmit(TaskSituationDTO taskSituationDTO) {
        log.info("【task situation submit】dto:{}",taskSituationDTO);


        SfaTaskEntity sfaTaskEntity = taskMapper.selectById(taskSituationDTO.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        Integer status = sfaTaskEntity.getStatus();
        if(status != TaskStatusEnum.PROCESSING.getStatus()){
            throw new ApplicationException("当前任务已完结或已关闭");
        }

        // 检查是否是主办或者是协办
        SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskSituationDTO.getTaskId())
                .eq("assign_user_id", taskSituationDTO.getProcessUserId()).in("assign_type", 1, 2)
                .eq("status", 1).eq("delete_flag", 0).last("limit 1"));

        if(Objects.isNull(sfaTaskAssignEntity)){
            throw new ApplicationException("非主办人或协办人无法操作");
        }

        // 检查是否有提交的历史
        List<SfaTaskSituationTraceEntity> sfaTaskSituationTraceEntities = sfaTaskSituationTraceMapper.selectList(new LambdaQueryWrapper<SfaTaskSituationTraceEntity>()
                .eq(SfaTaskSituationTraceEntity::getTaskId, taskSituationDTO.getTaskId())
                .eq(SfaTaskSituationTraceEntity::getCreateUserId, taskSituationDTO.getProcessUserId())
                .eq(SfaTaskSituationTraceEntity::getIsCurrent, 1)
                .eq(SfaTaskSituationTraceEntity::getRequireCallback, 0)
                .eq(SfaTaskSituationTraceEntity::getStatus, 1)
        );
        // 历史如果是不需要老板审核 则设置is_current为0
        if(!CollectionUtils.isEmpty(sfaTaskSituationTraceEntities)){
            sfaTaskSituationTraceEntities.forEach(e -> {
                e.setIsCurrent(0);
                sfaTaskSituationTraceMapper.updateById(e);
            });
        }

        SfaTaskSituationTraceEntity trace = new SfaTaskSituationTraceEntity();
        trace.init(taskSituationDTO.getProcessUserId(),taskSituationDTO.getProcessUserName());
        trace.setTaskId(taskSituationDTO.getTaskId());
        trace.setEmpId(taskSituationDTO.getProcessUserId());
        trace.setRequireCallback(taskSituationDTO.getRequireCallback());
        trace.setSituation(taskSituationDTO.getSituation());
        trace.setExpectedFinishDate(taskSituationDTO.getExpectedFinishDate());
        trace.setStatus(1);
        trace.setIsCurrent(1);
        trace.setAuditStatus(0);
        trace.setAppendix(taskSituationDTO.getAppendix());
        sfaTaskSituationTraceMapper.insert(trace);

        Integer requireCallback = taskSituationDTO.getRequireCallback();
        if(requireCallback == 1){
            // 发消息给老板
            List<NotifyPO> notifyPOS = new ArrayList<>();

            NotifyPO po = new NotifyPO();
            po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【待回复】");
            po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
            po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【待回复】");
            po.setCode("/MissionAnalysis?traceId="+trace.getTraceId());
            po.setEmployeeId(configMapper.getValueByCode("zw_senior_hr_employee_id"));
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);


            notifyService.saveBatch(notifyPOS);
        }

        //任务更新项目管理接受消息通知
        String projectManagerDepartment = configMapper.getValueByCode("project_manager_department");
        List<String> employeeId = organizationMapper.selectEmployeeIdByDeptId(Long.parseLong(projectManagerDepartment));
        if (!org.springframework.util.CollectionUtils.isEmpty(employeeId)) {
            List<NotifyPO> notifyPOS = new ArrayList<>();
            employeeId.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【已更新】");
                po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【已更新】");
                po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                po.setEmployeeId(e);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }


        // 操作记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(sfaTaskEntity.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.SUBMIT_RESULT.getType());
        taskLogDTO.setProcessUserId(taskSituationDTO.getProcessUserId());
        taskLogDTO.setProcessUserName(taskSituationDTO.getProcessUserName());
        taskLogDTO.setTraceId(trace.getTraceId());
        taskLogService.saveLog(taskLogDTO);


    }

    @Override
    @Transactional
    public void complete(TaskCompleteDTO taskCompleteDTO) {
        log.info("【task complete】dto:{}",taskCompleteDTO);

        SfaTaskEntity sfaTaskEntity = taskMapper.selectById(taskCompleteDTO.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        Integer status = sfaTaskEntity.getStatus();
        if(status != TaskStatusEnum.PROCESSING.getStatus()){
            throw new ApplicationException("该任务审核中,请等待");
        }

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskCompleteDTO.getTaskId()).eq("delete_flag", 0));
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("任务流程实例获取失败");
        }
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("任务记录获取失败");
        }
        String processUserId = sfaTaskInstanceRecordEntity.getProcessUserId();
        if(!processUserId.equals(taskCompleteDTO.getProcessUserId())){
            throw new ApplicationException("非主办人无法操作");
        }

        Integer taskType = sfaTaskEntity.getTaskType();
        sfaTaskEntity.setTaskTag(taskCompleteDTO.getTaskTag());
        // 个人任务直接完结
        if(taskType == 2){
            sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
            sfaTaskInstanceRecordEntity.setProcessUserId(taskCompleteDTO.getProcessUserId());
            sfaTaskInstanceRecordEntity.setProcessUserName(taskCompleteDTO.getProcessUserName());
            sfaTaskInstanceRecordEntity.setProcessResult(1);
            sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

            // 任务状态设置为完成
            sfaTaskEntity.setStatus(TaskStatusEnum.FINISH.getStatus());
            sfaTaskEntity.setUpdateUserName(taskCompleteDTO.getProcessUserName());
            sfaTaskEntity.setUpdateUserId(taskCompleteDTO.getProcessUserId());
            sfaTaskEntity.setUpdateTime(LocalDateTime.now());
            taskMapper.updateById(sfaTaskEntity);

            // 保存记录
            TaskLogDTO taskLogDTO = new TaskLogDTO();
            taskLogDTO.setTaskId(sfaTaskEntity.getTaskId());
            taskLogDTO.setType(TaskLogTypeEnum.FINISH.getType());
            taskLogDTO.setProcessUserId(taskCompleteDTO.getProcessUserId());
            taskLogDTO.setProcessUserName(taskCompleteDTO.getProcessUserName());
            taskLogDTO.setRemark(taskCompleteDTO.getRemark());
            taskLogService.saveLog(taskLogDTO);
            return;
        }

        CeoBusinessOrganizationPositionRelation auditPerson = null;
        // 交办任务审核人为杜颖
        if(taskType == 1){
            String task_audit_person = configMapper.getValueByCode("task_audit_person");
            auditPerson = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, task_audit_person)
                    .eq(CeoBusinessOrganizationPositionRelation::getChannel, 3)
                    .last("limit 1")
            );
        }


        // 部门任务审核人为部门主管
        else if(taskType == 3){
            String deptCode = sfaTaskEntity.getDeptCode();
            DepartEntity entity = deptMapper.selectOne(new LambdaQueryWrapper<DepartEntity>().eq(DepartEntity::getDeptCode, deptCode).eq(DepartEntity::getDeleteFlag, 0));
            String leaderId = entity.getLeaderId();
            if(StringUtils.isBlank(leaderId)){
                leaderId = taskSearchService.getDeptLeadId(entity.getSuperiorDeptId());
            }

            auditPerson = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, leaderId)
                    .eq(CeoBusinessOrganizationPositionRelation::getChannel, 3)
                    .last("limit 1")
            );
        }

        // 创建一个送申的流程节点
        SfaTaskInstanceRecordEntity nextRecord = new SfaTaskInstanceRecordEntity();
        nextRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
        nextRecord.setProcessStep(TaskProcessStepEnum.AUDIT.getProcessStep());
        nextRecord.setProcessResult(0);
        nextRecord.setProcessUserId(auditPerson.getEmployeeId());
        nextRecord.setProcessUserName(auditPerson.getEmployeeName());
        nextRecord.setCreateTime(LocalDateTime.now());
        nextRecord.setStatus(1);
        nextRecord.setDeleteFlag(0);
        nextRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
        sfaTaskInstanceRecordMapper.insert(nextRecord);

        // 将当前任务节点设置为完成
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setProcessUserId(taskCompleteDTO.getProcessUserId());
        sfaTaskInstanceRecordEntity.setProcessUserName(taskCompleteDTO.getProcessUserName());
        sfaTaskInstanceRecordEntity.setProcessResult(1);
        sfaTaskInstanceRecordEntity.setNextRecord(nextRecord.getRecordId());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

        // 绑定流程
        sfaTaskInstanceEntity.setProcessStep(nextRecord.getProcessStep());
        sfaTaskInstanceEntity.setProcessResult(nextRecord.getProcessResult());
        sfaTaskInstanceEntity.setUpdateUserName(taskCompleteDTO.getProcessUserName());
        sfaTaskInstanceEntity.setRecordId(nextRecord.getRecordId());
        sfaTaskInstanceEntity.setUpdateUserId(taskCompleteDTO.getProcessUserId());
        sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);

        // 设置任务状态
        sfaTaskEntity.setStatus(TaskStatusEnum.READY_AUDIT.getStatus());
        sfaTaskEntity.setUpdateUserName(taskCompleteDTO.getProcessUserName());
        sfaTaskEntity.setUpdateUserId(taskCompleteDTO.getProcessUserId());
        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        taskMapper.updateById(sfaTaskEntity);


        // 保存记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(sfaTaskEntity.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.READY_AUDIT.getType());
        taskLogDTO.setProcessUserId(taskCompleteDTO.getProcessUserId());
        taskLogDTO.setProcessUserName(taskCompleteDTO.getProcessUserName());
        taskLogDTO.setRemark(taskCompleteDTO.getRemark());
        taskLogService.saveLog(taskLogDTO);
    }



    @Override
    @Transactional
    public void follow(TaskFollowDTO taskFollowDTO) {
        log.info("【task follow】dto:{}",taskFollowDTO);
        SfaTaskEntity sfaTaskEntity = taskMapper.selectById(taskFollowDTO.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        Integer follow = taskFollowDTO.getFollow();
        // 取消关注
        if(follow == 0){
            SfaTaskFollowEntity sfaTaskFollowEntity = taskFollowMapper.selectOne(new LambdaQueryWrapper<SfaTaskFollowEntity>()
                    .eq(SfaTaskFollowEntity::getTaskId, taskFollowDTO.getTaskId())
                    .eq(SfaTaskFollowEntity::getEmployeeId, taskFollowDTO.getEmployeeId())
                    .eq(SfaTaskFollowEntity::getDeleteFlag, 0)
            );
            if(Objects.isNull(sfaTaskFollowEntity)){
                throw new ApplicationException("暂无关注无法取消");
            }
            sfaTaskFollowEntity.setDeleteFlag(1);
            sfaTaskFollowEntity.setUpdateTime(LocalDateTime.now());
            taskFollowMapper.updateById(sfaTaskFollowEntity);
            return;
        }

        // 新增关注
        SfaTaskFollowEntity sfaTaskFollowEntity = taskFollowMapper.selectOne(new LambdaQueryWrapper<SfaTaskFollowEntity>()
                .eq(SfaTaskFollowEntity::getTaskId, taskFollowDTO.getTaskId())
                .eq(SfaTaskFollowEntity::getEmployeeId, taskFollowDTO.getEmployeeId())
                .eq(SfaTaskFollowEntity::getDeleteFlag, 0));
        if(Objects.nonNull(sfaTaskFollowEntity)){
            throw new ApplicationException("请勿重复操作");
        }

        SfaTaskFollowEntity followEntity = new SfaTaskFollowEntity();
        followEntity.setTaskId(taskFollowDTO.getTaskId());
        followEntity.setCreateTime(LocalDateTime.now());
        followEntity.setUpdateTime(LocalDateTime.now());
        followEntity.setDeleteFlag(0);
        followEntity.setEmployeeId(taskFollowDTO.getEmployeeId());
        taskFollowMapper.insert(followEntity);
    }

    @Override
    @Transactional
    public void delete(TaskOperatorRequest request) {
        SfaTaskEntity sfaTaskEntity = taskMapper.selectById(request.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(), RequestUtils.getLoginInfo());


        sfaTaskEntity.setDeleteFlag(1);
        sfaTaskEntity.update(personInfo.getEmployeeId(),personInfo.getEmployeeName());
        taskMapper.updateById(sfaTaskEntity);
    }

    @Override
    @Transactional
    public void callback(TraceCallBackRequest traceCallBackRequest) {
        SfaTaskSituationTraceEntity sfaTaskSituationTraceEntity = sfaTaskSituationTraceMapper.selectById(traceCallBackRequest.getTraceId());
        if(Objects.isNull(sfaTaskSituationTraceEntity)){
            throw new ApplicationException("任务追踪记录不存在");
        }

        sfaTaskSituationTraceEntity.setCallback(traceCallBackRequest.getComment());
        sfaTaskSituationTraceEntity.setCallbackTime(LocalDateTime.now());
        sfaTaskSituationTraceMapper.updateById(sfaTaskSituationTraceEntity);

        // 老板回复消息通知 项目管理/主办人/协办人
        String projectManagerDepartment = configMapper.getValueByCode("project_manager_department");
        List<String> employeeId = organizationMapper.selectEmployeeIdByDeptId(Long.parseLong(projectManagerDepartment));
        List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                .eq("task_id", sfaTaskSituationTraceEntity.getTaskId())
                .eq("status", 1).eq("delete_flag", 0)
                .in("assign_type",1,2));
        for (SfaTaskAssignEntity assignEntity : assignEntities) {
            if(!employeeId.contains(assignEntity.getAssignUserId())){
                employeeId.add(assignEntity.getAssignUserId());
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(employeeId)) {
            List<NotifyPO> notifyPOS = new ArrayList<>();
            SfaTaskEntity sfaTaskEntity = taskMapper.selectById(sfaTaskSituationTraceEntity.getTaskId());
            employeeId.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【LEO回复】");
                po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【LEO回复】");
                po.setCode("/MissionDetail?taskId="+sfaTaskSituationTraceEntity.getTaskId());
                po.setEmployeeId(e);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
    }

    @Override
    @Transactional
    public void audit(TraceAuditRequest traceAuditRequest) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(traceAuditRequest.getPerson(), RequestUtils.getLoginInfo());


        List<Long> traceIds = traceAuditRequest.getTraceIds();
        traceIds.forEach(e -> {
            SfaTaskSituationTraceEntity sfaTaskSituationTraceEntity = sfaTaskSituationTraceMapper.selectById(e);
            if(Objects.isNull(sfaTaskSituationTraceEntity)){
                throw new ApplicationException("任务追踪记录不存在");
            }
            sfaTaskSituationTraceEntity.setAuditStatus(traceAuditRequest.getResult());
            sfaTaskSituationTraceEntity.setAuditComment(traceAuditRequest.getComment());
            sfaTaskSituationTraceEntity.setAuditEmpId(personInfo.getEmployeeId());
            sfaTaskSituationTraceEntity.setAuditEmpName(personInfo.getEmployeeName());
            sfaTaskSituationTraceEntity.setAuditTime(LocalDateTime.now());
            sfaTaskSituationTraceMapper.updateById(sfaTaskSituationTraceEntity);

            if(traceAuditRequest.getResult() == 2){
                SfaTaskEntity sfaTaskEntity = taskMapper.selectById(sfaTaskSituationTraceEntity.getTaskId());
                //进度驳回消息通知主办人/协办人
                List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                        .eq("task_id", sfaTaskSituationTraceEntity.getTaskId())
                        .eq("status", 1).eq("delete_flag", 0)
                        .in("assign_type",1,2));
                if (!org.springframework.util.CollectionUtils.isEmpty(assignEntities)) {
                    List<NotifyPO> notifyPOS = new ArrayList<>();
                    assignEntities.forEach(entity -> {
                        NotifyPO po = new NotifyPO();
                        po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【进度驳回】");
                        po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                        po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【进度驳回】");
                        po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                        po.setEmployeeId(entity.getAssignUserId());
                        po.setCreateBy("-1");
                        po.setUpdateBy("-1");
                        notifyPOS.add(po);
                    });
                    notifyService.saveBatch(notifyPOS);
                }
            }
        });
    }


}
