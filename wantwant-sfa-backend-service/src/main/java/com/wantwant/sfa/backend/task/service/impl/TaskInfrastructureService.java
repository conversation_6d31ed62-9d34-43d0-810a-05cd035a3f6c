package com.wantwant.sfa.backend.task.service.impl;

import com.wantwant.sfa.backend.mapper.task.TaskInfrastructureMapper;
import com.wantwant.sfa.backend.task.service.ITaskInfrastructureService;
import com.wantwant.sfa.backend.taskManagement.vo.AssignVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/14/上午10:01
 */
@Service
@Slf4j
public class TaskInfrastructureService implements ITaskInfrastructureService {
    @Autowired
    private TaskInfrastructureMapper taskInfrastructureMapper;

    @Override
    public List<AssignVo> getAssign(String employeeName) {
        log.info("【task assign search】employeeName:{}",employeeName);

        return taskInfrastructureMapper.searchAssign(employeeName);

    }
}
