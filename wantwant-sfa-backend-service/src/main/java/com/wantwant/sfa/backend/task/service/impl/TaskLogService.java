package com.wantwant.sfa.backend.task.service.impl;

import com.wantwant.sfa.backend.mapper.task.SfaTaskLogMapper;
import com.wantwant.sfa.backend.task.dto.TaskLogDTO;
import com.wantwant.sfa.backend.task.entity.SfaTaskLogEntity;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午8:59
 */
@Service
@Slf4j
public class TaskLogService implements ITaskLogService {

    @Autowired
    private SfaTaskLogMapper sfaTaskLogMapper;

    @Override
    @Transactional
    public void saveLog(TaskLogDTO taskLogDTO) {
        log.info("【save task log】dto:{}",taskLogDTO);

        SfaTaskLogEntity sfaTaskLogEntity = new SfaTaskLogEntity();
        sfaTaskLogEntity.init(taskLogDTO.getProcessUserId(),taskLogDTO.getProcessUserName());
        sfaTaskLogEntity.setRemark(taskLogDTO.getRemark());
        sfaTaskLogEntity.setType(taskLogDTO.getType());
        sfaTaskLogEntity.setTaskId(taskLogDTO.getTaskId());
        sfaTaskLogEntity.setTraceId(taskLogDTO.getTraceId());
        sfaTaskLogEntity.setDiff(taskLogDTO.getDiff());

        List<String> processObj = taskLogDTO.getProcessObj();
        if(!CollectionUtils.isEmpty(processObj)){
            sfaTaskLogEntity.setProcessObj(String.join(",",processObj));
        }

        sfaTaskLogMapper.insert(sfaTaskLogEntity);
    }
}
