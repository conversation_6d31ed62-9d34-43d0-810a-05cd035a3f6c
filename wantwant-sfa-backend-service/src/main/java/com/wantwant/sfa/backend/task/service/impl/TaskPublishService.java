package com.wantwant.sfa.backend.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskAssignMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskInstanceMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskInstanceRecordMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.task.dto.TaskLogDTO;
import com.wantwant.sfa.backend.task.dto.TaskPublishDTO;
import com.wantwant.sfa.backend.task.entity.SfaTaskAssignEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskInstanceEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskInstanceRecordEntity;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskProcessStepEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.service.ITaskPublishService;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/下午7:30
 */
@Service
@Slf4j
public class TaskPublishService implements ITaskPublishService {
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private SfaTaskInstanceMapper sfaTaskInstanceMapper;
    @Autowired
    private SfaTaskInstanceRecordMapper sfaTaskInstanceRecordMapper;
    @Autowired
    private SfaTaskAssignMapper sfaTaskAssignMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ITaskLogService taskLogService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    @Transactional
    public void publish(TaskPublishDTO taskPublishDTO) {
        log.info("【task publish audit】dto:{}",taskPublishDTO);

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskPublishDTO.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("发布任务不存在");
        }

        Integer taskType = sfaTaskEntity.getTaskType();
        Integer status = sfaTaskEntity.getStatus();



        // v9.9.0取消老板发布审核环节
        sfaTaskEntity.setStatus(TaskStatusEnum.READY_SIGN.getStatus());
        sfaTaskEntity.setPublishTime(LocalDateTime.now());

        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskEntity.setUpdateUserId(taskPublishDTO.getProcessUserId());
        sfaTaskEntity.setUpdateUserName(taskPublishDTO.getProcessUserName());
        sfaTaskMapper.updateById(sfaTaskEntity);

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskPublishDTO.getTaskId())
                .eq("delete_flag", 0)
        );
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("任务实例获取失败");
        }

        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("任务实例记录获取失败");
        }


        // 当前流程设置为完成
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setProcessResult(1);
        if(taskType == 1 && status == TaskStatusEnum.READY_PUSH.getStatus()){
            sfaTaskInstanceRecordEntity.setProcessUserName(taskPublishDTO.getProcessUserName());
            sfaTaskInstanceRecordEntity.setProcessUserId(taskPublishDTO.getProcessUserId());
        }


        // 创建新的流程
        SfaTaskInstanceRecordEntity nextRecord = new SfaTaskInstanceRecordEntity();

//        if(taskType == 1 && status == TaskStatusEnum.READY_PUSH.getStatus()){
//            nextRecord.setProcessStep(TaskProcessStepEnum.AUDIT_PUSH.getProcessStep());
//        }else{
//            nextRecord.setProcessStep(TaskProcessStepEnum.SIGN.getProcessStep());
//        }
        // v9.9.0取消老板发布审核环节
        nextRecord.setProcessStep(TaskProcessStepEnum.SIGN.getProcessStep());

        nextRecord.setProcessResult(0);
        // 获取主办人信息
        SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskPublishDTO.getTaskId()).eq("assign_type", 1)
                .eq("status", 1).eq("delete_flag", 0).last("limit 1"));

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("employee_id", sfaTaskAssignEntity.getAssignUserId())
                .eq("channel", 3)
                .last("limit 1")
        );

        nextRecord.setProcessUserId(ceoBusinessOrganizationPositionRelation.getEmployeeId());
        nextRecord.setProcessUserName(ceoBusinessOrganizationPositionRelation.getEmployeeName());
        nextRecord.setCreateTime(LocalDateTime.now());
        nextRecord.setStatus(1);
        nextRecord.setDeleteFlag(0);
        nextRecord.setPrevRecord(sfaTaskInstanceRecordEntity.getRecordId());
        sfaTaskInstanceRecordMapper.insert(nextRecord);

        sfaTaskInstanceRecordEntity.setNextRecord(nextRecord.getRecordId());
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

        // 修改流程实例
        sfaTaskInstanceEntity.setRecordId(nextRecord.getRecordId());
        sfaTaskInstanceEntity.setProcessStep(nextRecord.getProcessStep());
        sfaTaskInstanceEntity.setProcessResult(nextRecord.getProcessResult());
        sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskInstanceEntity.setUpdateUserId(taskPublishDTO.getProcessUserId());
        sfaTaskInstanceEntity.setUpdateUserName(taskPublishDTO.getProcessUserName());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);

        // 记录发布日志
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskPublishDTO.getTaskId());
        taskLogDTO.setProcessUserName(taskPublishDTO.getProcessUserName());
        taskLogDTO.setProcessUserId(taskPublishDTO.getProcessUserId());
        if(taskType == 1 && status == TaskStatusEnum.AUDIT_PUSH.getStatus()){
            taskLogDTO.setType(TaskLogTypeEnum.AUDIT_PUSH.getType());
        }else{
            taskLogDTO.setType(TaskLogTypeEnum.PUSH_TASK.getType());
        }

        List<SfaTaskAssignEntity> sfaTaskAssignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskPublishDTO.getTaskId())
                .eq("status", 1).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaTaskAssignEntities)){
            List<String> objs = sfaTaskAssignEntities.stream().map(SfaTaskAssignEntity::getAssignUserName).collect(Collectors.toList());
            taskLogDTO.setProcessObj(objs);
        }

        taskLogService.saveLog(taskLogDTO);
    }

    @Override
    @Transactional
    public void revert(TaskPublishDTO taskPublishDTO) {
        log.info("【task publish audit】dto:{}",taskPublishDTO);

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskPublishDTO.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("发布任务不存在");
        }
        // 确认状态是否是待发布
        Integer status = sfaTaskEntity.getStatus();
        if(status != TaskStatusEnum.READY_PUSH.getStatus() && status != TaskStatusEnum.AUDIT_PUSH.getStatus()){
            throw new ApplicationException("已发布的任务无法撤回");
        }

        // 修改任务状态
        sfaTaskEntity.setStatus(TaskStatusEnum.DRAFT.getStatus());
        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskEntity.setUpdateUserId(taskPublishDTO.getProcessUserId());
        sfaTaskEntity.setUpdateUserName(taskPublishDTO.getProcessUserName());
        sfaTaskMapper.updateById(sfaTaskEntity);

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskPublishDTO.getTaskId())
                .eq("delete_flag",0)
                .eq("delete_flag", 0)
        );
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("任务实例获取失败");
        }

        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("任务实例记录获取失败");
        }

        // 当前流程设置为驳回
        sfaTaskInstanceRecordEntity.setProcessTime(LocalDateTime.now());
        sfaTaskInstanceRecordEntity.setProcessResult(2);
        sfaTaskInstanceRecordMapper.updateById(sfaTaskInstanceRecordEntity);

        // 删除流程
        sfaTaskInstanceEntity.setDeleteFlag(1);
        sfaTaskInstanceEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskInstanceMapper.updateById(sfaTaskInstanceEntity);

        //任务发布前驳回消息
        sendMsg(taskPublishDTO,sfaTaskEntity);
    }

    private void sendMsg(TaskPublishDTO taskPublishDTO,SfaTaskEntity sfaTaskEntity){
        //任务发布前驳回消息
        List<String> employeeId = new ArrayList<>();
        //leo驳回通知项目管理/主办人
        if(taskPublishDTO.getProcessUserId().equals(configMapper.getValueByCode("zw_senior_hr_employee_id"))) {
            String projectManagerDepartment = configMapper.getValueByCode("project_manager_department");
            employeeId = organizationMapper.selectEmployeeIdByDeptId(Long.parseLong(projectManagerDepartment));
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId())
                    .eq("status", 1).eq("delete_flag", 0)
                    .eq("assign_type", 1));
            for (SfaTaskAssignEntity assignEntity : assignEntities) {
                if (!employeeId.contains(assignEntity.getAssignUserId())) {
                    employeeId.add(assignEntity.getAssignUserId());
                }
            }
        }else{
            //项目管理驳回通知主办人
            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId())
                    .eq("status", 1).eq("delete_flag", 0)
                    .eq("assign_type", 1));
            for (SfaTaskAssignEntity assignEntity : assignEntities) {
                if (!employeeId.contains(assignEntity.getAssignUserId())) {
                    employeeId.add(assignEntity.getAssignUserId());
                }
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(employeeId)) {
            List<NotifyPO> notifyPOS = new ArrayList<>();
            employeeId.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("任务名称:" + sfaTaskEntity.getTaskName() + "【任务驳回】");
                po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                po.setContent("任务名称:" + sfaTaskEntity.getTaskName() + "【任务驳回】");
                po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                po.setEmployeeId(e);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
    }
}
