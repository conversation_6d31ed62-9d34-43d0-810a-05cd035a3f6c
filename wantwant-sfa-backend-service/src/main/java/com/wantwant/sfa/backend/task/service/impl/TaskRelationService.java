package com.wantwant.sfa.backend.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskRelationMapper;
import com.wantwant.sfa.backend.task.dto.TaskIssueRelationDTO;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskRelationEntity;
import com.wantwant.sfa.backend.task.service.ITaskRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/11/21/上午9:01
 */
@Service
@Slf4j
public class TaskRelationService implements ITaskRelationService {
    @Resource
    private SfaTaskRelationMapper sfaTaskRelationMapper;
    @Resource
    private SfaTaskMapper sfaTaskMapper;

    @Override
    @Transactional
    public void saveRelation(Long taskId,Long fKey, Integer type, Long issueId, ProcessUserDO processUserDO) {
        log.info("【task relation save】fKey:{},type:{},issueId:{}",fKey,type,issueId);

        SfaTaskRelationEntity sfaTaskRelationEntity = new SfaTaskRelationEntity();
        sfaTaskRelationEntity.init(processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
        sfaTaskRelationEntity.setTaskId(taskId);
        sfaTaskRelationEntity.setFKey(fKey);
        sfaTaskRelationEntity.setType(type);
        sfaTaskRelationEntity.setIssueId(issueId);
        sfaTaskRelationMapper.insert(sfaTaskRelationEntity);
    }

    @Override
    public List<TaskIssueRelationDTO> selectTask(Long fKey, Integer type) {

        List<SfaTaskRelationEntity> sfaTaskRelationEntities = sfaTaskRelationMapper.selectList(new LambdaQueryWrapper<SfaTaskRelationEntity>().eq(SfaTaskRelationEntity::getType, type).eq(SfaTaskRelationEntity::getFKey, fKey).eq(SfaTaskRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(sfaTaskRelationEntities)){
            return ListUtils.EMPTY_LIST;
        }

        List<Long> taskIds = sfaTaskRelationEntities.stream().map(SfaTaskRelationEntity::getTaskId).collect(Collectors.toList());

        List<SfaTaskEntity> sfaTaskEntities = sfaTaskMapper.selectBatchIds(taskIds);

        if(CollectionUtils.isEmpty(sfaTaskEntities)){
            return ListUtils.EMPTY_LIST;
        }

        List<TaskIssueRelationDTO> list = new ArrayList<>();
        sfaTaskEntities.forEach(e -> {
            TaskIssueRelationDTO taskIssueRelationDTO = new TaskIssueRelationDTO();
            taskIssueRelationDTO.setTaskId(e.getTaskId());
            taskIssueRelationDTO.setTaskName(e.getTaskName());
            Optional<SfaTaskRelationEntity> taskRelationEntityOptional = sfaTaskRelationEntities.stream().filter(f -> f.getTaskId().equals(e.getTaskId())).findFirst();
            if(taskRelationEntityOptional.isPresent()){
                taskIssueRelationDTO.setIssueId(taskRelationEntityOptional.get().getIssueId());
            }
            list.add(taskIssueRelationDTO);
        });

        return list;
    }
}
