package com.wantwant.sfa.backend.task.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.util.PageUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.arch.entity.DeptEmployeeRelationEntity;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.dict.service.impl.DictCodeServiceImpl;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.task.*;
import com.wantwant.sfa.backend.task.dto.TaskTraceDTO;
import com.wantwant.sfa.backend.task.entity.*;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.model.TaskModel;
import com.wantwant.sfa.backend.task.service.ITaskSearchService;
import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskSelectRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskTraceAuditSearchRequest;
import com.wantwant.sfa.backend.taskManagement.vo.*;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.ExportUtil;
import com.wantwant.sfa.backend.util.ObjectDiffModel;
import com.wantwant.sfa.backend.workReport.entity.SfaWorkReportTaskEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/14/上午10:23
 */
@Service
@Slf4j
public class TaskSearchService implements ITaskSearchService {

    @Autowired
    private DictCodeServiceImpl dictCodeServiceImpl;
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private SfaTaskSituationTraceMapper sfaTaskSituationTraceMapper;
    @Autowired
    private SfaTaskInstanceMapper sfaTaskInstanceMapper;
    @Autowired
    private SfaTaskInstanceRecordMapper sfaTaskInstanceRecordMapper;
    @Autowired
    private SfaTaskAssignMapper sfaTaskAssignMapper;
    @Autowired
    private SfaTaskLogMapper sfaTaskLogMapper;
    @Autowired
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private TaskFollowMapper taskFollowMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TaskChatMapper taskChatMapper;
    @Resource
    private TaskMeetingMapper taskMeetingMapper;
    @Resource
    private SfaTaskRelationMapper sfaTaskRelationMapper;



    @Override
    public List<TaskSituationVo> getSituation(Long taskId) {

        log.info("【get situation】taskId:{}",taskId);

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务ID不存在");
        }
        Integer status = sfaTaskEntity.getStatus();
        if(status == TaskStatusEnum.DRAFT.getStatus()){
            return ListUtils.EMPTY_LIST;
        }

        // 获取当前任务最后一次签收时间
        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskId).eq("delete_flag", 0));
        if(Objects.isNull(sfaTaskInstanceEntity)){
            throw new ApplicationException("任务实例获取失败");
        }

        Long recordId = sfaTaskInstanceEntity.getRecordId();
        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(recordId);
        if(Objects.isNull(sfaTaskInstanceRecordEntity)){
            throw new ApplicationException("任务记录获取失败");
        }

        List<TaskSituationVo> list = new ArrayList<>();

        if(sfaTaskInstanceRecordEntity.getProcessStep() >= 20){
            SfaTaskInstanceRecordEntity sendRecord = null;
            // 获取最后一次签收的日期
            if(sfaTaskInstanceRecordEntity.getProcessStep() == 40 || sfaTaskInstanceRecordEntity.getProcessStep() == 41){
                sendRecord = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceRecordEntity.getPrevRecord());
                sendRecord = sfaTaskInstanceRecordMapper.selectById(sendRecord.getPrevRecord());
            }else if(sfaTaskInstanceRecordEntity.getProcessStep() == 30){
                sendRecord = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceRecordEntity.getPrevRecord());
            }
            else{
                sendRecord = sfaTaskInstanceRecordEntity;
            }

            LocalDateTime createTime = null;
            if(Objects.isNull(sendRecord)){
                // 个人任务没有签收时间
                createTime = sfaTaskInstanceRecordEntity.getCreateTime();
            }else{
                createTime = sendRecord.getCreateTime();
            }


            // 获取签收日期之后的信息
            List<SfaTaskSituationTraceEntity> sfaTaskSituationTraceEntities = sfaTaskSituationTraceMapper.selectList(new QueryWrapper<SfaTaskSituationTraceEntity>()
                    .eq("task_id", taskId)
                    .eq("status", 1)
                    .eq("delete_flag", 0)
                    .ge("create_time",createTime)
            );

            if(!CollectionUtils.isEmpty(sfaTaskSituationTraceEntities)){

                sfaTaskSituationTraceEntities.stream().forEach(e -> {
                    TaskSituationVo vo = new TaskSituationVo();
                    SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", e.getTaskId()).eq("assign_user_id", e.getEmpId()).last("limit 1"));
                    if(Objects.nonNull(sfaTaskAssignEntity)){
                        vo.setUserName(sfaTaskAssignEntity.getAssignDeptName()+"-"+sfaTaskAssignEntity.getAssignUserName());
                    }
                    vo.setCurrentStatus("办理中");
                    long days = LocalDateTimeUtils.betweenTwoTime(e.getCreateTime(), LocalDateTime.now(), ChronoUnit.DAYS);
                    vo.setElapsedDays(days);
                    String appendix = e.getAppendix();
                    if(StringUtils.isNotBlank(appendix)){
                        List<String> appendixList = Arrays.asList(appendix.split(","));

                        List<TaskAnnexRequest> annexRequests = new ArrayList<>();
                        appendixList.forEach(a -> {
                            TaskAnnexRequest taskAnnexRequest = new TaskAnnexRequest();
                            String name = a.split("\\$\\$")[0];
                            String url =a.split("\\$\\$")[1];
                            taskAnnexRequest.setUrl(url);
                            taskAnnexRequest.setName(name);
                            annexRequests.add(taskAnnexRequest);
                        });
                       vo.setAppendix(annexRequests);
                    }
                    vo.setSituation(e.getSituation());
                    vo.setDeadline(LocalDateTimeUtils.formatTime(sfaTaskEntity.getDeadline(),LocalDateTimeUtils.yyyy_MM_dd));
                    vo.setExpectDeadline(LocalDateTimeUtils.formatTime(e.getExpectedFinishDate(),LocalDateTimeUtils.yyyy_MM_dd));
                    list.add(vo);
                });
            }

            // 添加送审的记录
            if(sfaTaskInstanceRecordEntity.getProcessStep() == 40){
                TaskSituationVo vo = new TaskSituationVo();
                SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskId).eq("assign_type", 1).last("limit 1"));
                if(Objects.nonNull(sfaTaskAssignEntity)){
                    vo.setUserName(sfaTaskAssignEntity.getAssignDeptName()+"-"+sfaTaskAssignEntity.getAssignUserName());
                }
                vo.setDeadline(LocalDateTimeUtils.formatTime(sfaTaskEntity.getDeadline(),LocalDateTimeUtils.yyyy_MM_dd));
                vo.setCurrentStatus("送审");
                Duration dur= Duration.between(sfaTaskInstanceRecordEntity.getCreateTime(), LocalDateTime.now());
                vo.setElapsedDays(dur.toDays());
                Integer processResult = sfaTaskInstanceRecordEntity.getProcessResult();
                if(processResult == 1){
                    vo.setSituation("已完成");
                }else{
                    vo.setSituation("审核中");
                }
                list.add(vo);
            }
        }

        return list;
    }

    @Override
    public List<TaskLogVo> getTaskLog(Long taskId) {
        log.info("【get task log】taskId:{}",taskId);
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务ID不存在");
        }


        List<SfaTaskLogEntity> sfaTaskLogEntities = sfaTaskLogMapper.selectList(new QueryWrapper<SfaTaskLogEntity>().eq("task_id", taskId).eq("delete_flag", 0).eq("status", 1));

        List<TaskLogVo> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(sfaTaskLogEntities)){

            sfaTaskLogEntities.forEach(e -> {
                TaskLogVo taskLogVo = new TaskLogVo();
                taskLogVo.setLogId(e.getLogId());
                taskLogVo.setProcessTime(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd));
                taskLogVo.setProcessDescription(e.getCreateUserName()+"操作了"+TaskLogTypeEnum.getTypeName(e.getType()));

                String diff = e.getDiff();
                if(StringUtils.isNotBlank(diff)){
                    List<ObjectDiffModel> objectDiffModels = JSONArray.parseArray(diff, ObjectDiffModel.class);
                    List<DiffVo> collect = objectDiffModels.stream().map(DiffVo::create).collect(Collectors.toList());
                    taskLogVo.setDiffVos(collect);
                }

                if(StringUtils.isNotBlank(e.getProcessObj())){
                    taskLogVo.setProcessObj("操作对象: "+e.getProcessObj());
                }

                // 获取回复数量
                Integer replyCount = Optional.ofNullable(taskChatMapper.selectCount(new LambdaQueryWrapper<SfaTaskChatEntity>().eq(SfaTaskChatEntity::getLogId, e.getLogId()).isNull(SfaTaskChatEntity::getParentId))).orElse(0);

                Integer childReplyCount = Optional.ofNullable(taskChatMapper.selectCount(new LambdaQueryWrapper<SfaTaskChatEntity>().eq(SfaTaskChatEntity::getLogId, e.getLogId()).eq(SfaTaskChatEntity::getDeleteFlag,0).isNotNull(SfaTaskChatEntity::getParentId))).orElse(0);

                taskLogVo.setReplyCount(replyCount+childReplyCount);


                if(e.getType() == TaskLogTypeEnum.SUBMIT_RESULT.getType()){

                    Long traceId = e.getTraceId();
                    if(Objects.nonNull(traceId)){
                        SfaTaskSituationTraceEntity sfaTaskSituationTraceEntity = sfaTaskSituationTraceMapper.selectById(traceId);
                        if(Objects.nonNull(sfaTaskSituationTraceEntity)){
                            String appendix = sfaTaskSituationTraceEntity.getAppendix();
                            if(StringUtils.isNotBlank(appendix)){
                                List<String> appendixList = Arrays.asList(appendix.split(","));

                                List<TaskAnnexRequest> annexRequests = new ArrayList<>();
                                appendixList.forEach(a -> {
                                    TaskAnnexRequest taskAnnexRequest = new TaskAnnexRequest();
                                    String name = a.split("\\$\\$")[0];
                                    String url =a.split("\\$\\$")[1];
                                    taskAnnexRequest.setUrl(url);
                                    taskAnnexRequest.setName(name);
                                    annexRequests.add(taskAnnexRequest);
                                });
                                taskLogVo.setAppendix(annexRequests);
                            }
                            StringBuffer message = new StringBuffer("");
                            String situation = "办理情况: "+ sfaTaskSituationTraceEntity.getSituation()+"<br>";
                            message.append(situation);
                            String expectedFinishDate = "预计完成时间: "+ LocalDateTimeUtils.formatTime(sfaTaskSituationTraceEntity.getExpectedFinishDate(),LocalDateTimeUtils.yyyy_MM_dd)+"<br>";
                            message.append(expectedFinishDate);
                            Integer auditStatus = sfaTaskSituationTraceEntity.getAuditStatus();

                            if(auditStatus == 2){
                                String status = "<span style='color: #D9001B'>驳回</span>";
                                String auditComment = sfaTaskSituationTraceEntity.getAuditComment();
                                String auditInfo = StringUtils.EMPTY;
                                if(StringUtils.isNotBlank(auditComment)){
                                    auditInfo = "审核: "+ status + " 意见: "+auditComment + "<br>";
                                }
                                message.append(auditInfo);
                            }

                            Integer requireCallback = sfaTaskSituationTraceEntity.getRequireCallback();

                            String callback = StringUtils.isBlank(sfaTaskSituationTraceEntity.getCallback())? StringUtils.EMPTY : sfaTaskSituationTraceEntity.getCallback();
                            if(StringUtils.isNotBlank(callback)){
                                String callbackInfo = "<span style='color: #000000'>LEO回复: "+ callback + "</span><br>";
                                message.append(callbackInfo);
                            }

                            taskLogVo.setMessage(message.toString());
                        }
                    }

                }else{
                    if(StringUtils.isNotBlank(e.getRemark())){
                        taskLogVo.setMessage("备注: "+e.getRemark());
                    }

                }
                //添加主办人或协办人标志
                LambdaQueryWrapper<SfaTaskAssignEntity> sfaTaskAssignEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
                sfaTaskAssignEntityLambdaQueryWrapper.eq(SfaTaskAssignEntity::getTaskId,taskId);
                sfaTaskAssignEntityLambdaQueryWrapper.eq(SfaTaskAssignEntity::getAssignUserId,e.getCreateUserId());
                sfaTaskAssignEntityLambdaQueryWrapper.eq(SfaTaskAssignEntity::getDeleteFlag,0);
                sfaTaskAssignEntityLambdaQueryWrapper.in(SfaTaskAssignEntity::getAssignType,1,2).orderByAsc(SfaTaskAssignEntity::getAssignType).last("limit 1");
                SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(sfaTaskAssignEntityLambdaQueryWrapper);
                if(Objects.nonNull(sfaTaskAssignEntity)){
                    taskLogVo.setAssignType(sfaTaskAssignEntity.getAssignType());
                }

                list.add(taskLogVo);
            });
        }
        return list;
    }

    @Override
    public TaskDetailVo getDetail(Long taskId,String person) {
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务ID不存在");
        }

        TaskDetailVo taskDetailVo = new TaskDetailVo();
        BeanUtils.copyProperties(sfaTaskEntity,taskDetailVo);
        Long contextTask = sfaTaskEntity.getContextTask();
        taskDetailVo.setCreateUserName(sfaTaskEntity.getRealCreateUserName());
        taskDetailVo.setDeadline(LocalDateTimeUtils.formatTime(sfaTaskEntity.getDeadline(),LocalDateTimeUtils.yyyy_MM_dd));
        taskDetailVo.setCreateTime(LocalDateTimeUtils.formatTime(sfaTaskEntity.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
        taskDetailVo.setStatus(TaskStatusEnum.getStatusName(sfaTaskEntity.getStatus(),sfaTaskEntity.getDeadline()));
        Integer suspend = sfaTaskEntity.getSuspend();
        if(suspend == 1){
            taskDetailVo.setStatus("已挂起");
        }

        if(sfaTaskEntity.getTaskType() == 2){
            taskDetailVo.setTaskNature(null);
        }

        if(Objects.nonNull(contextTask)){
            SfaTaskEntity parent = sfaTaskMapper.selectById(contextTask);
            if(Objects.nonNull(parent)){
                ContextTaskVo contextTaskVo = new ContextTaskVo();
                contextTaskVo.setTaskId(contextTask);
                contextTaskVo.setTaskName(parent.getTaskName());
                taskDetailVo.setParentTask(contextTaskVo);
            }
        }

        // 查询子任务
        List<SfaTaskEntity> sfaTaskEntities = sfaTaskMapper.selectList(new LambdaQueryWrapper<SfaTaskEntity>().eq(SfaTaskEntity::getContextTask, taskId).eq(SfaTaskEntity::getDeleteFlag, 0));
        if(!CollectionUtils.isEmpty(sfaTaskEntities)){
            List<ContextTaskVo> contextTaskVos = new ArrayList<>();
            sfaTaskEntities.forEach(e -> {
                ContextTaskVo contextTaskVo = new ContextTaskVo();
                contextTaskVo.setTaskId(e.getTaskId());
                contextTaskVo.setTaskName(e.getTaskName());
                contextTaskVos.add(contextTaskVo);
            });

            taskDetailVo.setChildrenTask(contextTaskVos);
        }


        LocalDateTime publishTime = sfaTaskEntity.getPublishTime();
        if(Objects.nonNull(publishTime)){
            taskDetailVo.setPublishTime(LocalDateTimeUtils.formatTime(publishTime,LocalDateTimeUtils.yyyy_MM_dd));
        }


        SfaTaskFollowEntity followEntity = taskFollowMapper.selectOne(new LambdaQueryWrapper<SfaTaskFollowEntity>().eq(SfaTaskFollowEntity::getEmployeeId, person).eq(SfaTaskFollowEntity::getTaskId, taskId).eq(SfaTaskFollowEntity::getDeleteFlag, 0).last("limit 1"));
        if(Objects.nonNull(followEntity)){
            taskDetailVo.setFollow(1);
        }else{
            taskDetailVo.setFollow(0);
        }


        String annex = sfaTaskEntity.getAnnex();
        if(StringUtils.isNotBlank(annex)){
            List<String> urls = Arrays.asList(annex.split(","));
            List<TaskAnnexRequest> annexList = new ArrayList<>();
            urls.forEach(e -> {
                String urlName = e.split("\\$\\$")[0];
                String url = e.split("\\$\\$")[1];
                TaskAnnexRequest taskAnnexRequest = new TaskAnnexRequest();
                taskAnnexRequest.setName(urlName);
                taskAnnexRequest.setUrl(url);
                annexList.add(taskAnnexRequest);
            });
            taskDetailVo.setAnnex(annexList);
        }

        String createUserId = sfaTaskEntity.getCreateUserId();

        Integer taskType = sfaTaskEntity.getTaskType();
        // 部门任务
        if(taskType == 3){
            String deptCode = sfaTaskEntity.getDeptCode();
            DepartEntity entity = deptMapper.selectOne(new LambdaQueryWrapper<DepartEntity>().eq(DepartEntity::getDeptCode, deptCode).eq(DepartEntity::getDeleteFlag, 0).last("limit 1"));
            if(Objects.nonNull(entity)){
                taskDetailVo.setCreateUserDeptName(entity.getDeptName());
            }
        }
        // 个人任务根据主办人获取部门名称
        else if(taskType == 2){
            SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, sfaTaskEntity.getTaskId()).eq(SfaTaskAssignEntity::getAssignType, 1).eq(SfaTaskAssignEntity::getDeleteFlag, 0).last("limit 1"));
            String assignDeptName = sfaTaskAssignEntity.getAssignDeptName();
            taskDetailVo.setCreateUserDeptName(assignDeptName);
        }

        else{
            // 获取创建人部门
            List<DeptEmployeeRelationEntity> deptEmployeeRelationEntities = deptEmployeeRelationMapper.selectList(new QueryWrapper<DeptEmployeeRelationEntity>().eq("employee_id", createUserId).eq("delete_flag", 0));
            if(!CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
                List<Integer> deptIds = deptEmployeeRelationEntities.stream().map(DeptEmployeeRelationEntity::getDeptId).collect(Collectors.toList());
                List<DepartEntity> departEntities = deptMapper.selectBatchIds(deptIds);
                if(!CollectionUtils.isEmpty(departEntities)){
                    String deptNames = departEntities.stream().map(DepartEntity::getDeptName).collect(Collectors.joining());
                    taskDetailVo.setCreateUserDeptName(deptNames);
                }
            }
        }


        // 指派人
        List<SfaTaskAssignEntity> sfaTaskAssignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskId).eq("delete_flag", 0).eq("status", 1));
        if(!CollectionUtils.isEmpty(sfaTaskAssignEntities)){
            Optional<SfaTaskAssignEntity> mainAssignOptional = sfaTaskAssignEntities.stream().filter(f -> f.getAssignType() == 1).findFirst();
            if(mainAssignOptional.isPresent()){
                AssignVo mainAssignVo = new AssignVo();
                SfaTaskAssignEntity sfaTaskAssignEntity = mainAssignOptional.get();
                mainAssignVo.setEmpId(sfaTaskAssignEntity.getAssignUserId());
                mainAssignVo.setEmpName(sfaTaskAssignEntity.getAssignUserName());
                mainAssignVo.setDeptCode(sfaTaskAssignEntity.getAssignDeptCode());
                mainAssignVo.setDeptName(sfaTaskAssignEntity.getAssignDeptName());
                taskDetailVo.setMainAssignUser(mainAssignVo);
            }


            List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignEntities.stream().filter(f -> f.getAssignType() == 2).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(assignEntities)){
                List<AssignVo> assigns = new ArrayList<>();
                assignEntities.forEach(e -> {
                    AssignVo assignVo = new AssignVo();
                    assignVo.setEmpId(e.getAssignUserId());
                    assignVo.setEmpName(e.getAssignUserName());
                    assignVo.setDeptCode(e.getAssignDeptCode());
                    assignVo.setDeptName(e.getAssignDeptName());
                    assigns.add(assignVo);
                });
                taskDetailVo.setAssignUser(assigns);
            }

            List<SfaTaskAssignEntity> ccAssignEntities = sfaTaskAssignEntities.stream().filter(f -> f.getAssignType() == 3).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(ccAssignEntities)){
                List<AssignVo> assigns = new ArrayList<>();
                ccAssignEntities.forEach(e -> {
                    AssignVo assignVo = new AssignVo();
                    assignVo.setEmpId(e.getAssignUserId());
                    assignVo.setEmpName(e.getAssignUserName());
                    assignVo.setDeptCode(e.getAssignDeptCode());
                    assignVo.setDeptName(e.getAssignDeptName());
                    assigns.add(assignVo);
                });
                taskDetailVo.setCcAssignUser(assigns);
            }
        }

        // 根据登陆人决定按钮状态
        setPermission(taskDetailVo,sfaTaskEntity,person);

        SfaTaskRelationEntity sfaTaskRelationEntity = sfaTaskRelationMapper.selectOne(new QueryWrapper<SfaTaskRelationEntity>().eq("task_id", taskId).eq("delete_flag", 0).last("limit 1"));
        if(Objects.nonNull(sfaTaskRelationEntity)) {
            taskDetailVo.setFKey(sfaTaskRelationEntity.getFKey());
        }
        return taskDetailVo;
    }

    @Override
    public List<TaskSampleVo> searchTaskByName(String taskName) {
        List<SfaTaskEntity> sfaTaskEntities = sfaTaskMapper.selectList(new QueryWrapper<SfaTaskEntity>().eq("delete_flag", 0).like("task_name", taskName).notIn("status",60,80));
        if(CollectionUtils.isEmpty(sfaTaskEntities)){
            return null;
        }
        List<TaskSampleVo> list = new ArrayList<>();
        sfaTaskEntities.forEach(e -> {
            TaskSampleVo vo = new TaskSampleVo();
            vo.setTaskId(e.getTaskId());
            vo.setTaskName(e.getTaskName());
            list.add(vo);
        });

        return list;
    }

    @Override
    public Page<TaskVo> selectList(TaskSelectRequest taskSelectRequest) {

        String person = taskSelectRequest.getPerson();
        DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getEmployeeId, taskSelectRequest.getPerson()).eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0).last("limit 1"));
        if(Objects.nonNull(deptEmployeeRelationEntity)){
            taskSelectRequest.setProject(true);
        }


        List<TaskModel> list = sfaTaskMapper.selectByCondition(taskSelectRequest);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        // 通过ID获取主办，协办信息
        List<Long> ids = list.stream().map(TaskModel::getTaskId).collect(Collectors.toList());

        List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().in("task_id", ids).eq("delete_flag", 0).eq("status", 1));

        List<TaskVo> result = new ArrayList<>();
        Page<TaskVo> page = new Page<>();
        list.forEach(e -> {
            TaskVo vo = new TaskVo();
            vo.setUrgencyLevel(e.getUrgencyLevel());
            vo.setTaskId(e.getTaskId());
            vo.setTaskTag(e.getTaskTag());
            vo.setTaskNature(e.getTaskNature());
            vo.setTaskSubType(e.getTaskSubType());
            vo.setTaskName(e.getTaskName());
            vo.setTaskType(e.getTaskType());
            vo.setCreateUserName(e.getCreateUserName());
            vo.setPriority(e.getPriority());
            vo.setStatus(TaskStatusEnum.getStatusName(e.getStatus(),e.getDeadline()));
            LocalDateTime publishTime = e.getPublishTime();
            if(Objects.nonNull(publishTime)){
                vo.setPublishTime(LocalDateTimeUtils.formatTime(publishTime,LocalDateTimeUtils.yyyy_MM_dd));
            }

            Integer weekRefresh = e.getWeekRefresh();

            // 可催办
            if(TaskStatusEnum.FINISH.getStatus() != e.getStatus() && TaskStatusEnum.CLOSED.getStatus() !=  e.getStatus()  && TaskStatusEnum.ENDING_FINISH.getStatus() != e.getStatus() && TaskStatusEnum.FINAL_AUDIT.getStatus() !=  e.getStatus() ){
                if(e.getStatus() == TaskStatusEnum.READY_SIGN.getStatus() || e.getStatus() == TaskStatusEnum.PROCESSING.getStatus()){
                    vo.setCanUrge(true);
                }

            }


            if(1 == weekRefresh){
                vo.setWeekRefreshStatus("已更新");
            }else{
                vo.setWeekRefreshStatus("未更新");
            }
            if(Objects.nonNull(e.getStatus())) {
                if (e.getStatus().equals(TaskStatusEnum.FINISH.getStatus()) || e.getStatus().equals(TaskStatusEnum.CLOSED.getStatus())) {
                    vo.setWeekRefreshStatus("-");
                }
            }

            if(e.getTaskType() == 2){
                vo.setTaskNature(null);
            }


            if(1 == e.getSuspend()){
                vo.setStatus("已挂起");
            }

            if(e.getStatus() == TaskStatusEnum.DRAFT.getStatus() && e.getTempName().equals(e.getCreateUserName())){
                vo.setCanDelete(true);
            }

            vo.setDeadline(LocalDateTimeUtils.formatTime(e.getDeadline(),LocalDateTimeUtils.yyyy_MM_dd));
            vo.setCreateTime(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));

            if(e.getStatus() == TaskStatusEnum.FINISH.getStatus()){
                vo.setFinishTime(LocalDateTimeUtils.formatTime(e.getUpdateTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            }

            Optional<SfaTaskAssignEntity> mainAssignOptional = assignEntities.stream().filter(f -> f.getAssignType() == 1 && f.getTaskId().equals(e.getTaskId())).findFirst();
            if(mainAssignOptional.isPresent()){
                SfaTaskAssignEntity sfaTaskAssignEntity = mainAssignOptional.get();
                vo.setMainAssignUser(sfaTaskAssignEntity.getAssignDeptName()+"-"+sfaTaskAssignEntity.getAssignUserName());
            }

            List<SfaTaskAssignEntity> collect = assignEntities.stream().filter(f -> f.getAssignType() == 2 && f.getTaskId().equals(e.getTaskId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(collect)){
                List<String> assignEntityList = new ArrayList<>();
                collect.forEach(c -> {
                    assignEntityList.add(c.getAssignDeptName()+"-"+c.getAssignUserName());
                });
                vo.setAssignUsers(String.join(",",assignEntityList));
            }



            if(e.getStatus() == TaskStatusEnum.AUDIT_PUSH.getStatus() || e.getStatus() == TaskStatusEnum.READY_PUSH.getStatus()){
                vo.setCanPublish(true);
            }
            if(e.getStatus() == TaskStatusEnum.FINAL_AUDIT.getStatus() || e.getStatus() == TaskStatusEnum.READY_AUDIT.getStatus()){
                vo.setCanFinish(true);
            }

            result.add(vo);
        });
        int count = sfaTaskMapper.selectCountByCondition(taskSelectRequest);
        page.setList(result);
        page.setTotalPage(PageUtil.totalPage(count,taskSelectRequest.getRows()));
        page.setTotalItem(count);
        return page;
    }

    @Override
    public void downloadList(TaskSelectRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downloadList request:{}", request);
        List<TaskExcelVo> excelList = new ArrayList<>();
        request.setRows(10000);
        Page<TaskVo> page = this.selectList(request);
        if (page != null) {
            List<TaskVo> list = page.getList();
            if (!CollectionUtils.isEmpty(list)) {
                BeanUtils.copyProperties(list, excelList, TaskVo.class, TaskExcelVo.class);
                Map<Integer, String> taskTypeMap = dictCodeServiceImpl.getMapByClassCd(DictCodeConstants.CLASSCD_TASK_TYPE);
                Map<Integer, String> taskSubTypeMap = dictCodeServiceImpl.getMapByClassCd(DictCodeConstants.CLASSCD_TASK_SUB_TYPE);
                Map<Integer, String> taskNatureMap = dictCodeServiceImpl.getMapByClassCd(DictCodeConstants.CLASSCD_TASK_NATURE);
                Map<Integer, String> taskPriorityMap = dictCodeServiceImpl.getMapByClassCd(DictCodeConstants.CLASSCD_TASK_PRIORITY);
                excelList.forEach(e -> {
                    e.setTaskTypeStr(Optional.ofNullable(taskTypeMap.get(e.getTaskType())).orElse("-"));
                    e.setTaskSubTypeStr(Optional.ofNullable(taskSubTypeMap.get(e.getTaskSubType())).orElse("-"));
                    e.setPriorityStr(Optional.ofNullable(taskPriorityMap.get(e.getPriority())).orElse("-"));
                    e.setTaskNatureStr(Optional.ofNullable(taskNatureMap.get(e.getTaskNature())).orElse("-"));
                    e.setDeadline1(e.getDeadline());
                });
            }
        }
        ExportUtil.writeEasyExcelResponse(res, req, "任务列表", TaskExcelVo.class, excelList);
    }

    @Override
    public TaskInfoVo taskInfo(String person) {
        TaskInfoVo vo = new TaskInfoVo();
        TaskSelectRequest taskSelectRequest = new TaskSelectRequest();
        taskSelectRequest.setPerson(person);

        DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getEmployeeId, taskSelectRequest.getPerson()).eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0).last("limit 1"));
        if(Objects.nonNull(deptEmployeeRelationEntity)){
            taskSelectRequest.setProject(true);
        }
        // 所有任务数
        taskSelectRequest.setProcessing(false);
        Integer allTaskCount = sfaTaskMapper.getAllTaskCount(taskSelectRequest);
        // 待审核数
        taskSelectRequest.setProcessing(true);
        Integer auditCount = sfaTaskMapper.selectCountByCondition(taskSelectRequest);
        // 已办任务
        taskSelectRequest.setProcessing(false);
        taskSelectRequest.setStatus(TaskStatusEnum.FINISH.getStatus().toString());
        Integer solvedCount = sfaTaskMapper.selectCountByCondition(taskSelectRequest);
        // 抄送任务
        taskSelectRequest.setCc(true);
        taskSelectRequest.setProcessing(false);
        taskSelectRequest.setStatus(null);
        Integer ccCount = sfaTaskMapper.selectCountByCondition(taskSelectRequest);
        // 到期任务
        taskSelectRequest.setCc(false);
        taskSelectRequest.setStatus("402");
        Integer expireCount = sfaTaskMapper.selectCountByCondition(taskSelectRequest);
        // 已逾期任务
        taskSelectRequest.setStatus("403");
        Integer delinquentCount = sfaTaskMapper.selectCountByCondition(taskSelectRequest);
        // 关注任务
        Integer followCount = sfaTaskMapper.getFollowCount(person);
        // 草稿任务
        Integer draftCount = Optional.ofNullable(sfaTaskMapper.selectCount(new LambdaQueryWrapper<SfaTaskEntity>().eq(SfaTaskEntity::getCreateUserId, person).eq(SfaTaskEntity::getStatus, TaskStatusEnum.DRAFT.getStatus()).eq(SfaTaskEntity::getDeleteFlag, 0))).orElse(0);

        vo.setAllTask(allTaskCount);
        vo.setProcessing(auditCount);
        vo.setSolved(solvedCount);
        vo.setCc(ccCount);
        vo.setExpire(expireCount);
        vo.setDelinquent(delinquentCount);
        vo.setDraft(draftCount);
        vo.setFollow(followCount);

        return vo;
    }

    @Override
    public String getDeptLeadId(Integer superiorDeptId) {
        // 获取上级部门
        if(Objects.isNull(superiorDeptId)){
            return configMapper.getValueByCode("zw_senior_hr_employee_id");
        }

        DepartEntity entity = deptMapper.selectById(superiorDeptId);
        if(StringUtils.isNotBlank(entity.getLeaderId())){
            return entity.getLeaderId();
        }

        return getDeptLeadId(entity.getSuperiorDeptId());
    }

    @Override
    public Page<TaskTraceVo> selectTaskTraceAudit(TaskTraceAuditSearchRequest request) {

        List<TaskTraceDTO> list = sfaTaskMapper.selectTaskTraceAudit(request);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        Page<TaskTraceVo> page =new Page<>();

        boolean canCallBack = configMapper.getValueByCode("zw_senior_hr_employee_id").equals(request.getPerson());


        boolean canAudit = false;
        DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getEmployeeId, request.getPerson()).eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0));
        if(Objects.nonNull(deptEmployeeRelationEntity)){
            canAudit = true;
        }

        List<TaskTraceVo> taskTraceVos = convertToVo(list,canCallBack,canAudit);


        page.setList(taskTraceVos);
        int count = sfaTaskMapper.selectTaskTraceAuditCount(request);
        page.setTotalItem(count);
        page.setTotalPage(PageUtil.totalPage(count,request.getRows()));

        return page;
    }

    @Override
    public void taskTraceExport(TaskTraceAuditSearchRequest request) {
        List<TaskTraceDTO> list = sfaTaskMapper.selectTaskTraceAudit(request);
        List<TaskTraceVo> taskTraceVos = convertToVo(list,false,false);


        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        String name = "任务分析" + sheetName;

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), TaskTraceVo.class, taskTraceVos);

        response.setContentType("application/vnd.ms-excel");

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
                    .encode(name + ".xlsx"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);

        }
    }

    @Override
    public Boolean auditPermission(String person) {
        DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getEmployeeId, person).eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0));
        if(Objects.nonNull(deptEmployeeRelationEntity)){
           return true;
        }
        return false;
    }

    private List<TaskTraceVo> convertToVo(List<TaskTraceDTO> list,boolean canCallBack, boolean canAudit) {
        List<TaskTraceVo> result = new ArrayList<>();

        if(CollectionUtils.isEmpty(list)){
            return result;
        }

        list.forEach(e -> {
            TaskTraceVo vo = new TaskTraceVo();
            BeanUtils.copyProperties(e, vo);
            vo.setStatus(TaskStatusEnum.getStatusName(e.getStatus(),e.getDeadline()));
            LocalDateTime publishTime = e.getPublishTime();
            LocalDateTime deadline = e.getDeadline();
            if(Objects.nonNull(deadline)){
                vo.setDeadline(LocalDateTimeUtils.formatTime(deadline,LocalDateTimeUtils.yyyy_MM_dd));
            }
            vo.setDeptName(e.getDeptName());

            Integer assignType = e.getAssignType();
            if(assignType == 1){
                vo.setAssignUserName("主办人:"+e.getAssignUserName());
            }else{
                vo.setAssignUserName("协办人:"+e.getAssignUserName());
            }

            if(e.getStatus() == TaskStatusEnum.FINISH.getStatus()){
                vo.setFinishDate(LocalDateTimeUtils.formatTime(e.getUpdateTime(),LocalDateTimeUtils.yyyy_MM_dd));
            }


            LocalDateTime refreshTime = e.getRefreshTime();
            if(Objects.nonNull(refreshTime)){
                LocalDateTime start = LocalDate.now().atStartOfDay().with(DayOfWeek.MONDAY);
                LocalDateTime end = start.plusWeeks(1L);
                if(start.compareTo(refreshTime) < 0 && end.compareTo(refreshTime) > 0){
                    vo.setWeekRefreshStatus("已更新");
                }else{
                    vo.setWeekRefreshStatus("未更新");
                }
            }else{
                vo.setWeekRefreshStatus("未更新");
            }

            if(e.getStatus() == TaskStatusEnum.FINISH.getStatus() || e.getStatus() == TaskStatusEnum.CLOSED.getStatus()){
                vo.setWeekRefreshStatus("-");
            }


            if(Objects.nonNull(publishTime)){
                vo.setPublishTime(LocalDateTimeUtils.formatTime(publishTime,LocalDateTimeUtils.yyyy_MM_dd));

                Duration between = Duration.between(publishTime, LocalDateTime.now());
                vo.setAgo(between.toDays());


                Long traceId = e.getTraceId();
                if(Objects.nonNull(traceId)){

                    SfaTaskSituationTraceEntity sfaTaskSituationTraceEntity = sfaTaskSituationTraceMapper.selectById(traceId);

                    vo.setSituation(sfaTaskSituationTraceEntity.getSituation());
                    vo.setExpectFinishDate(LocalDateTimeUtils.formatTime(sfaTaskSituationTraceEntity.getExpectedFinishDate(),LocalDateTimeUtils.yyyy_MM_dd));
                    vo.setAuditComment(sfaTaskSituationTraceEntity.getAuditComment());
                    vo.setAuditStatus(sfaTaskSituationTraceEntity.getAuditStatus());
                    vo.setCallback(sfaTaskSituationTraceEntity.getCallback());

                    vo.setSubmitTime(LocalDateTimeUtils.formatTime(sfaTaskSituationTraceEntity.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd));

                    Integer requireCallback = sfaTaskSituationTraceEntity.getRequireCallback();

                    vo.setRequireCallback(sfaTaskSituationTraceEntity.getRequireCallback());

                    if(StringUtils.isBlank(sfaTaskSituationTraceEntity.getCallback()) && canCallBack && requireCallback == 1){
                        vo.setCanCallback(true);
                    }

                    if(Objects.nonNull(sfaTaskSituationTraceEntity.getStatus()) && sfaTaskSituationTraceEntity.getAuditStatus() == 0 && canAudit){
                        vo.setCanAudit(true);
                    }



                }

            }




            result.add(vo);
        });
        return result;
    }

    private void setPermission(TaskDetailVo taskDetailVo, SfaTaskEntity sfaTaskEntity,String person) {

        RoleEmployeeRelationEntity roleEmployeeRelationEntity = roleEmployeeRelationMapper.selectOne(new QueryWrapper<RoleEmployeeRelationEntity>().eq("role_id", 55)
                .eq("delete_flag", 0).eq("employee_id", person).last("limit 1"));

        Integer status = sfaTaskEntity.getStatus();
        Integer taskType = sfaTaskEntity.getTaskType();


        if(status == TaskStatusEnum.DRAFT.getStatus() && sfaTaskEntity.getCreateUserId().equals(person)){
            taskDetailVo.setUpdate(true);
        }
        // 交办任务
        if(taskType == 1 && status == TaskStatusEnum.READY_PUSH.getStatus() ){

            DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getEmployeeId, person).eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0).last("limit 1"));
            if(Objects.nonNull(deptEmployeeRelationEntity)){
                taskDetailVo.setPublish(true);
                taskDetailVo.setRevert(true);
            }
        }

        // 待确认发布
//        if(taskType == 1 && status == TaskStatusEnum.AUDIT_PUSH.getStatus()){
//            String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
//            if(person.equals(zw_senior_hr_employee_id)){
//                taskDetailVo.setPublishAudit(true);
//                taskDetailVo.setRevert(true);
//            }
//        }


        if(status == TaskStatusEnum.READY_AUDIT.getStatus()){
            DeptEmployeeRelationEntity deptEmployeeRelationEntity = deptEmployeeRelationMapper.selectOne(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getEmployeeId, person).eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0).last("limit 1"));
            if(sfaTaskEntity.getTaskType() == 1 && Objects.nonNull(deptEmployeeRelationEntity)){
                taskDetailVo.setRedone(true);
                taskDetailVo.setFinish(true);
            }
            else if(sfaTaskEntity.getTaskType() != 1){
                SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", sfaTaskEntity.getTaskId()).eq("delete_flag", 0).last("limit 1"));
                SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
                if(sfaTaskInstanceRecordEntity.getProcessUserId().equals(person)){
                    taskDetailVo.setRedone(true);
                    taskDetailVo.setFinish(true);
                }
            }
        }


        if(taskType == 1 && status == TaskStatusEnum.FINAL_AUDIT.getStatus()){
            String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
            if(person.equals(zw_senior_hr_employee_id)){
                taskDetailVo.setFinishAudit(true);
                taskDetailVo.setRedone(true);
            }
        }

        if(taskType != 1 && status == TaskStatusEnum.FINAL_AUDIT.getStatus()) {
            SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", sfaTaskEntity.getTaskId()).eq("delete_flag", 0).last("limit 1"));
            SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
            if(sfaTaskInstanceRecordEntity.getProcessUserId().equals(person)){
                taskDetailVo.setRedone(true);
                taskDetailVo.setFinishAudit(true);
            }
        }


        // 部门任务
        if(taskType == 3 && status == TaskStatusEnum.READY_PUSH.getStatus()){
            SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new LambdaQueryWrapper<SfaTaskInstanceEntity>().eq(SfaTaskInstanceEntity::getTaskId, sfaTaskEntity.getTaskId()).eq(SfaTaskInstanceEntity::getDeleteFlag, 0));
            SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
            String processUserId = sfaTaskInstanceRecordEntity.getProcessUserId();
            if(processUserId.equals(person)){
                taskDetailVo.setPublish(true);
                taskDetailVo.setRevert(true);
            }
        }





        //「修改办理时限」按钮只给LEO、杜颖、孙帆
        if(Objects.nonNull(roleEmployeeRelationEntity) && (
                status != TaskStatusEnum.DRAFT.getStatus() && status != TaskStatusEnum.FINISH.getStatus()
                        && status != TaskStatusEnum.CLOSED.getStatus()
                )){
            taskDetailVo.setModifyDeadline(true);

            // 查询是否可创建会议
            SfaTaskMeetingEntity sfaTaskMeetingEntity = taskMeetingMapper.selectOne(new LambdaQueryWrapper<SfaTaskMeetingEntity>()
                    .eq(SfaTaskMeetingEntity::getTaskId, sfaTaskEntity.getTaskId())
                    .isNull(SfaTaskMeetingEntity::getInfoId).eq(SfaTaskMeetingEntity::getDeleteFlag, 0)
                    .last("limit 1"));
            if(Objects.nonNull(sfaTaskMeetingEntity)){
                taskDetailVo.setCreateMeeting(true);
            }
        }

        // LEO的待确认完结、项目管理（杜颖、孙帆）的待审核，新增驳回按钮
        String projectManagerDepartment = configMapper.getValueByCode("project_manager_department");
        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
        List<String> employeeId = organizationMapper.selectEmployeeIdByDeptId(Long.parseLong(projectManagerDepartment));
        if((person.equals(zw_senior_hr_employee_id) && status == TaskStatusEnum.FINAL_AUDIT.getStatus()) ||
                (employeeId.contains(person) && status == TaskStatusEnum.READY_AUDIT.getStatus())
        ){
            taskDetailVo.setRefuseButton(true);
        }

        //关闭/挂起只给LEO、项目管理
        if((person.equals(zw_senior_hr_employee_id) || employeeId.contains(person)) &&
                (status != TaskStatusEnum.CLOSED.getStatus() && status != TaskStatusEnum.FINISH.getStatus())){
            taskDetailVo.setClose(true);
            if( sfaTaskEntity.getSuspend() == 0){
                taskDetailVo.setSuspend(true);
            }
        }

        //V9.4 主办人有‘添加协办人’按钮权限
        LambdaQueryWrapper<SfaTaskAssignEntity> sfaTaskAssignEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sfaTaskAssignEntityLambdaQueryWrapper.eq(SfaTaskAssignEntity::getTaskId,sfaTaskEntity.getTaskId())
                .eq(SfaTaskAssignEntity::getAssignUserId,person)
                .eq(SfaTaskAssignEntity::getAssignType,1)
                .eq(SfaTaskAssignEntity::getDeleteFlag,0);
        SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(sfaTaskAssignEntityLambdaQueryWrapper);

        if((Objects.nonNull(roleEmployeeRelationEntity) && (status != TaskStatusEnum.DRAFT.getStatus()
                && status != TaskStatusEnum.FINISH.getStatus()
                && status != TaskStatusEnum.CLOSED.getStatus()))
                || (Objects.nonNull(sfaTaskAssignEntity) && status != TaskStatusEnum.DRAFT.getStatus()
                && status != TaskStatusEnum.FINISH.getStatus()
                && status != TaskStatusEnum.CLOSED.getStatus()
        )){

            taskDetailVo.setModifyAssign(true);

            if(Objects.nonNull(roleEmployeeRelationEntity)){
                taskDetailVo.setModifyMainAssign(true);
            }


        }


        if((Objects.nonNull(roleEmployeeRelationEntity) || person.equals(zw_senior_hr_employee_id)) && (status != TaskStatusEnum.DRAFT.getStatus()
                && status != TaskStatusEnum.FINISH.getStatus()
                && status != TaskStatusEnum.CLOSED.getStatus())){
            // 检查会议是否创建过
            Integer meetingCount = Optional.ofNullable(taskMeetingMapper.selectCount(new LambdaQueryWrapper<SfaTaskMeetingEntity>()
                    .eq(SfaTaskMeetingEntity::getTaskId, sfaTaskEntity.getTaskId()).isNull(SfaTaskMeetingEntity::getInfoId).eq(SfaTaskMeetingEntity::getDeleteFlag, 0))).orElse(0);


            taskDetailVo.setLaunchMeeting(true);



        }



        if(Objects.nonNull(roleEmployeeRelationEntity) && sfaTaskEntity.getSuspend() == 1){
            taskDetailVo.setRestore(true);
        }

        if(status == TaskStatusEnum.READY_SIGN.getStatus() || status == TaskStatusEnum.RE_SIGN.getStatus()){
            SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", sfaTaskEntity.getTaskId()).eq("delete_flag", 0).last("limit 1"));
            SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
            if(sfaTaskInstanceRecordEntity.getProcessUserId().equals(person)){
                taskDetailVo.setSign(true);
            }
        }

        if(status == TaskStatusEnum.PROCESSING.getStatus()) {
            SfaTaskAssignEntity assignEntity = sfaTaskAssignMapper.selectOne(new QueryWrapper<SfaTaskAssignEntity>()
                    .eq("task_id", sfaTaskEntity.getTaskId()).in("assign_type", 1, 2).eq("assign_user_id", person).eq("status", 1).eq("delete_flag", 0).last("limit 1"));
            if(Objects.nonNull(assignEntity)){
                taskDetailVo.setSubmitSituation(true);
                Integer assignType = assignEntity.getAssignType();
                if(assignType == 1){
                    taskDetailVo.setSend(true);
                }
            }
        }

        //如果是挂起leo,项目管理只有关闭和取消挂起
        if (status != TaskStatusEnum.CLOSED.getStatus() && status != TaskStatusEnum.FINISH.getStatus() && sfaTaskEntity.getSuspend() == 1) {
            if(person.equals(zw_senior_hr_employee_id) || employeeId.contains(person)){
                taskDetailVo.setRestore(true);
                taskDetailVo.setClose(true);
            }else{
                taskDetailVo.setRestore(false);
                taskDetailVo.setClose(false);
            }

            taskDetailVo.setUpdate(false);
            taskDetailVo.setModifyAssign(false);
            taskDetailVo.setSign(false);
            taskDetailVo.setPublish(false);
            taskDetailVo.setPublishAudit(false);
            taskDetailVo.setSubmitSituation(false);
            taskDetailVo.setSuspend(false);
            taskDetailVo.setFinish(false);
            taskDetailVo.setFinishAudit(false);
            taskDetailVo.setRedone(false);
            taskDetailVo.setRevert(false);
            taskDetailVo.setSend(false);
            taskDetailVo.setModifyDeadline(false);
            taskDetailVo.setRefuseButton(false);

        }


        // 项目管理和老板可以修改任务关联
        if(person.equals(zw_senior_hr_employee_id) || employeeId.contains(person)){
            taskDetailVo.setModifyContext(true);
        }

        if(status == TaskStatusEnum.RE_SIGN.getStatus() || status == TaskStatusEnum.READY_SIGN.getStatus() || status == TaskStatusEnum.PROCESSING.getStatus()){
            if(person.equals(zw_senior_hr_employee_id) || employeeId.contains(person)){
                taskDetailVo.setUrge(true);
            }
        }


        // 检查是否可修改
        if(status != TaskStatusEnum.DRAFT.getStatus() && status != TaskStatusEnum.FINISH.getStatus()
         && status != TaskStatusEnum.CLOSED.getStatus()) {
            if (taskType == 1) {
                // 交办任务，项目及老板可以修改
                if (person.equals(zw_senior_hr_employee_id) || employeeId.contains(person)) {
                    taskDetailVo.setModify(true);
                }
            } else if (taskType == 2) {
                // 个人任务都可修改
                taskDetailVo.setModify(true);
            } else if (taskType == 3) {
                // 部门任务，主管可以修改
                String deptCode = sfaTaskEntity.getDeptCode();
                DepartEntity entity = deptMapper.selectOne(new LambdaQueryWrapper<DepartEntity>().eq(DepartEntity::getDeptCode, deptCode).eq(DepartEntity::getDeleteFlag, 0));
                String leaderId = entity.getLeaderId();
                if(StringUtils.isNotBlank(leaderId)){
                    if(leaderId.equals(person)){
                        taskDetailVo.setModify(true);
                    }
                }else{
                    String deptLeadId = getDeptLeadId(entity);
                    if(deptLeadId.equals(person) || person.equals(zw_senior_hr_employee_id) || employeeId.contains(person)){
                        taskDetailVo.setModify(true);
                    }
                }

            }
        }
    }




    private String getDeptLeadId(DepartEntity deptEntity) {
        // 获取上级部门
        Integer superiorDeptId = deptEntity.getSuperiorDeptId();
        if(Objects.isNull(superiorDeptId)){
            return configMapper.getValueByCode("zw_senior_hr_employee_id");
        }

        DepartEntity entity = deptMapper.selectById(superiorDeptId);
        if(StringUtils.isNotBlank(entity.getLeaderId())){
            return entity.getLeaderId();
        }

        return getDeptLeadId(entity);
    }
}
