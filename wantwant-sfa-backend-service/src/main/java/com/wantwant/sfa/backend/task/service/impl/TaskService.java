package com.wantwant.sfa.backend.task.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gexin.fastjson.JSONArray;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.entity.DeptEmployeeRelationEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.notify.DO.MandatoryNoticeDO;
import com.wantwant.sfa.backend.domain.notify.service.IMandatoryNoticeService;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskAssignMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.task.dto.TaskAssignDTO;
import com.wantwant.sfa.backend.task.dto.TaskAssignModifyDTO;
import com.wantwant.sfa.backend.task.dto.TaskDTO;
import com.wantwant.sfa.backend.task.dto.TaskLogDTO;
import com.wantwant.sfa.backend.task.entity.SfaTaskAssignEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import com.wantwant.sfa.backend.task.service.ITaskProcessService;
import com.wantwant.sfa.backend.task.service.ITaskRelationService;
import com.wantwant.sfa.backend.task.service.ITaskService;
import com.wantwant.sfa.backend.taskManagement.request.TaskAuditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskContextModifyRequest;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.ObjectComparator;
import com.wantwant.sfa.backend.util.ObjectDiffModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午9:46
 */
@Service
@Slf4j
public class TaskService implements ITaskService {

    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaTaskAssignMapper sfaTaskAssignMapper;
    @Autowired
    private ITaskProcessService taskProcessService;
    @Autowired
    private ITaskLogService taskLogService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;
    @Resource
    private IMandatoryNoticeService mandatoryNoticeService;
    @Resource
    private ITaskRelationService taskRelationService;

    @Override
    @Transactional
    public void createTask(TaskDTO taskDTO) {
        log.info("【create task】dto:{}",taskDTO);
        SfaTaskEntity sfaTaskEntity = new SfaTaskEntity();
        sfaTaskEntity.init(taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        BeanUtils.copyProperties(taskDTO,sfaTaskEntity);
        sfaTaskEntity.setStatus(TaskStatusEnum.DRAFT.getStatus());
        sfaTaskEntity.setRealCreateUserId(taskDTO.getCreateUserId());
        sfaTaskEntity.setRealCreateUserName(taskDTO.getCreateUserName());

        sfaTaskMapper.insert(sfaTaskEntity);

        // 保存任务关联
        Long fKey = taskDTO.getFKey();
        if(Objects.nonNull(fKey)){
            ProcessUserDO processUserDO = new ProcessUserDO();
            processUserDO.setEmployeeId(taskDTO.getCreateUserId());
            processUserDO.setEmployeeName(taskDTO.getCreateUserName());
            taskRelationService.saveRelation(sfaTaskEntity.getTaskId(),fKey,taskDTO.getType(),taskDTO.getIssueId(),processUserDO);
        }


        Long contextTask = taskDTO.getContextTask();
        if(Objects.nonNull(contextTask)){
            SfaTaskEntity parentTask = sfaTaskMapper.selectById(contextTask);
            if(Objects.nonNull(parentTask) && parentTask.getTaskType() != taskDTO.getTaskType()){
                throw new ApplicationException("子任务类型必须与父任务类型一致");
            }
        }

        AtomicBoolean containsSeniorHr = new AtomicBoolean(false);
        AtomicBoolean containsAssistant = new AtomicBoolean(false);
        AtomicBoolean containsAssistant2 =  new AtomicBoolean(false);
        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");

        String taskAssistant = configMapper.getValueByCode("task_assistant");


        // 保存指派人信息
        TaskAssignDTO mainProcessUser = taskDTO.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){
            if(mainProcessUser.getEmpId().equals(zw_senior_hr_employee_id)){
                containsSeniorHr.set(true);
            }

            if(mainProcessUser.getEmpId().equals(taskAssistant.split(",")[0])){
                containsAssistant.set(true);
            }
            if(mainProcessUser.getEmpId().equals(taskAssistant.split(",")[1])){
                containsAssistant2.set(true);
            }

            // 保存指派信息
            saveAssign(sfaTaskEntity.getTaskId(),mainProcessUser,1,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());

            // 如果创建人是老板，修改任务创建人
            if(Objects.nonNull(taskDTO.getTransfer()) && taskDTO.getTransfer() && zw_senior_hr_employee_id.equals(taskDTO.getCreateUserId())){
                sfaTaskEntity.setCreateUserId(mainProcessUser.getEmpId());
                sfaTaskEntity.setCreateUserName(mainProcessUser.getEmpName());
                sfaTaskEntity.setDeptCode(mainProcessUser.getDeptCode());
                sfaTaskMapper.updateById(sfaTaskEntity);
            }
        }

        // 保存协办
        List<TaskAssignDTO> assistedProcessUsers = taskDTO.getAssistedProcessUsers();
        if(!CollectionUtils.isEmpty(assistedProcessUsers)){
            assistedProcessUsers.forEach(e -> {
                if(e.getEmpId().equals(zw_senior_hr_employee_id)){
                    containsSeniorHr.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[0])){
                    containsAssistant.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[1])){
                    containsAssistant2.set(true);
                }
                // 保存指派信息
                saveAssign(sfaTaskEntity.getTaskId(),e,2,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());

            });
        }

        // 保存抄送
        List<TaskAssignDTO> ccProcessUsers = taskDTO.getCcProcessUsers();
        if(!CollectionUtils.isEmpty(ccProcessUsers)){
            ccProcessUsers.forEach(e -> {
                if(e.getEmpId().equals(zw_senior_hr_employee_id)){
                    containsSeniorHr.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[0])){
                    containsAssistant.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[1])){
                    containsAssistant2.set(true);
                }
                // 保存指派信息
                saveAssign(sfaTaskEntity.getTaskId(),e,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
            });
        }


        if(taskDTO.getTaskType() == 1 && !containsSeniorHr.get()){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(zw_senior_hr_employee_id);

            String employeeName = findEmpName(zw_senior_hr_employee_id);
            taskAssignDTO.setEmpName(employeeName);
            taskAssignDTO.setDeptName("产品开发中心");
            taskAssignDTO.setDeptCode("D00000022");
            // 抄送增加
            saveAssign(sfaTaskEntity.getTaskId(),taskAssignDTO,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }

        if(taskDTO.getTaskType() == 1 && !containsAssistant.get()){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(taskAssistant.split(",")[0]);
            String employeeName = findEmpName(taskAssistant.split(",")[0]);
            taskAssignDTO.setEmpName(employeeName);
            taskAssignDTO.setDeptName("项目管理部");
            taskAssignDTO.setDeptCode("D00000057");
            // 抄送增加
            saveAssign(sfaTaskEntity.getTaskId(),taskAssignDTO,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }

        if(taskDTO.getTaskType() == 1 && !containsAssistant2.get()){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(taskAssistant.split(",")[1]);
            String employeeName = findEmpName(taskAssistant.split(",")[1]);
            taskAssignDTO.setEmpName(employeeName);
            taskAssignDTO.setDeptName("项目管理部");
            taskAssignDTO.setDeptCode("D00000057");
            // 抄送增加
            saveAssign(sfaTaskEntity.getTaskId(),taskAssignDTO,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }



        Integer submit = taskDTO.getSubmit();
        // 提交则创建流程
        if(submit == 1){
            taskProcessService.initProcess(sfaTaskEntity.getTaskId(),sfaTaskEntity.getTaskType(),taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }
    }

    private String findEmpName(String employeeId) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = Optional.ofNullable(ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, employeeId)
                .eq(CeoBusinessOrganizationPositionRelation::getChannel, 3)
                .last("limit 1")
        )).orElse(new CeoBusinessOrganizationPositionRelation());
        return ceoBusinessOrganizationPositionRelation.getEmployeeName();
    }

    @Override
    public void updateTask(TaskDTO taskDTO) {
        log.info("【update task】dto:{}",taskDTO);
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskDTO.getTaskId());
        BeanUtils.copyProperties(taskDTO,sfaTaskEntity);
        sfaTaskEntity.setStatus(TaskStatusEnum.DRAFT.getStatus());
        sfaTaskMapper.updateById(sfaTaskEntity);

        Long contextTask = taskDTO.getContextTask();
        if(Objects.nonNull(contextTask)){
            SfaTaskEntity parentTask = sfaTaskMapper.selectById(contextTask);
            if(Objects.nonNull(parentTask) && parentTask.getTaskType() != taskDTO.getTaskType()){
                throw new ApplicationException("子任务类型必须与父任务类型一致");
            }
        }

        // 删除原指派信息
        List<SfaTaskAssignEntity> sfaTaskAssignEntities = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskDTO.getTaskId()).eq("status", 1).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(sfaTaskAssignEntities)){
            sfaTaskAssignEntities.forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateTime(LocalDateTime.now());
                e.setUpdateUserId(taskDTO.getCreateUserId());
                e.setUpdateUserName(taskDTO.getCreateUserName());
                sfaTaskAssignMapper.updateById(e);
            });
        }


        AtomicBoolean containsSeniorHr = new AtomicBoolean(false);
        AtomicBoolean containsAssistant = new AtomicBoolean(false);
        AtomicBoolean containsAssistant2 =  new AtomicBoolean(false);
        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");

        String taskAssistant = configMapper.getValueByCode("task_assistant");

        // 保存指派人信息
        TaskAssignDTO mainProcessUser = taskDTO.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){


            // 保存指派信息
            saveAssign(sfaTaskEntity.getTaskId(),mainProcessUser,1,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());


            // 如果创建人是老板，修改任务创建人
            if(Objects.nonNull(taskDTO.getTransfer()) && taskDTO.getTransfer() && zw_senior_hr_employee_id.equals(taskDTO.getCreateUserId())){
                sfaTaskEntity.setCreateUserId(mainProcessUser.getEmpId());
                sfaTaskEntity.setCreateUserName(mainProcessUser.getEmpName());
                sfaTaskEntity.setDeptCode(mainProcessUser.getDeptCode());
                sfaTaskMapper.updateById(sfaTaskEntity);
            }

            if(mainProcessUser.getEmpId().equals(zw_senior_hr_employee_id)){
                containsSeniorHr.set(true);
            }

            if(mainProcessUser.getEmpId().equals(taskAssistant.split(",")[0])){
                containsAssistant.set(true);
            }
            if(mainProcessUser.getEmpId().equals(taskAssistant.split(",")[1])){
                containsAssistant2.set(true);
            }
        }

        // 保存协办
        List<TaskAssignDTO> assistedProcessUsers = taskDTO.getAssistedProcessUsers();
        if(!CollectionUtils.isEmpty(assistedProcessUsers)){
            assistedProcessUsers.forEach(e -> {

                if(e.getEmpId().equals(zw_senior_hr_employee_id)){
                    containsSeniorHr.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[0])){
                    containsAssistant.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[1])){
                    containsAssistant2.set(true);
                }
                // 保存指派信息
                saveAssign(sfaTaskEntity.getTaskId(),e,2,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
            });
        }

        // 保存抄送
        List<TaskAssignDTO> ccProcessUsers = taskDTO.getCcProcessUsers();
        if(!CollectionUtils.isEmpty(ccProcessUsers)){
            ccProcessUsers.forEach(e -> {
                if(e.getEmpId().equals(zw_senior_hr_employee_id)){
                    containsSeniorHr.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[0])){
                    containsAssistant.set(true);
                }
                if(e.getEmpId().equals(taskAssistant.split(",")[1])){
                    containsAssistant2.set(true);
                }
                // 保存指派信息
                saveAssign(sfaTaskEntity.getTaskId(),e,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
            });
        }

        if(taskDTO.getTaskType() == 1 && !containsSeniorHr.get()){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(zw_senior_hr_employee_id);
            taskAssignDTO.setEmpName("蔡旺祖");
            taskAssignDTO.setDeptName("产品开发中心");
            taskAssignDTO.setDeptCode("D00000022");
            // 抄送增加
            saveAssign(sfaTaskEntity.getTaskId(),taskAssignDTO,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }

        if(taskDTO.getTaskType() == 1 && !containsAssistant.get()){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(taskAssistant.split(",")[0]);
            taskAssignDTO.setEmpName("杜颖");
            taskAssignDTO.setDeptName("项目管理部");
            taskAssignDTO.setDeptCode("00500108");
            // 抄送增加
            saveAssign(sfaTaskEntity.getTaskId(),taskAssignDTO,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }

        if(taskDTO.getTaskType() == 1 && !containsAssistant2.get()){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(taskAssistant.split(",")[1]);
            taskAssignDTO.setEmpName("孙帆");
            taskAssignDTO.setDeptName("项目管理部");
            taskAssignDTO.setDeptCode("D00000057");
            // 抄送增加
            saveAssign(sfaTaskEntity.getTaskId(),taskAssignDTO,3,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }

        Integer submit = taskDTO.getSubmit();
        // 提交则创建流程
        if(submit == 1){
            taskProcessService.initProcess(sfaTaskEntity.getTaskId(),sfaTaskEntity.getTaskType(),taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        }
    }

    @Override
    @Transactional
    public void modifyAssign(TaskAssignModifyDTO taskAssignModifyDTO) {
        log.info("【modify assign】dto:{}",taskAssignModifyDTO);
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskAssignModifyDTO.getTaskId());
        if(sfaTaskEntity.getStatus() == TaskStatusEnum.FINISH.getStatus() || sfaTaskEntity.getStatus() == TaskStatusEnum.READY_AUDIT.getStatus() || sfaTaskEntity.getStatus() == TaskStatusEnum.CLOSED.getStatus()){
            throw new ApplicationException("当前流程已关闭，无法修改指派人");
        }
        sfaTaskEntity.setDeadline(taskAssignModifyDTO.getDeadline());
        sfaTaskEntity.setUpdateTime(LocalDateTime.now());
        sfaTaskEntity.setUpdateUserId(taskAssignModifyDTO.getProcessUserId());
        sfaTaskEntity.setUpdateUserName(taskAssignModifyDTO.getProcessUserName());
        sfaTaskMapper.updateById(sfaTaskEntity);



        // 保存指派人信息
        TaskAssignDTO mainProcessUser = taskAssignModifyDTO.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){
            updateMainAssign(sfaTaskEntity, mainProcessUser,taskAssignModifyDTO.getProcessUserId(),taskAssignModifyDTO.getProcessUserName());

            // 保存发布记录
            TaskLogDTO taskLogDTO = new TaskLogDTO();
            taskLogDTO.setTaskId(taskAssignModifyDTO.getTaskId());
            taskLogDTO.setType(TaskLogTypeEnum.MODIFY_MAIN_ASSIN.getType());
            taskLogDTO.setProcessUserName(taskAssignModifyDTO.getProcessUserName());
            taskLogDTO.setProcessUserId(taskAssignModifyDTO.getProcessUserId());
            taskLogDTO.setRemark(taskAssignModifyDTO.getRemark());
            taskLogDTO.setProcessObj(Arrays.asList(mainProcessUser.getEmpName()));
            taskLogService.saveLog(taskLogDTO);
        }



        // 保存协办
        updateAssignUSer(sfaTaskEntity,taskAssignModifyDTO.getAssistedProcessUsers(),2,taskAssignModifyDTO.getProcessUserId(),taskAssignModifyDTO.getProcessUserName());


        // 保存发布记录
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskAssignModifyDTO.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.MODIFY_ASSIGN.getType());
        taskLogDTO.setProcessUserName(taskAssignModifyDTO.getProcessUserName());
        taskLogDTO.setProcessUserId(taskAssignModifyDTO.getProcessUserId());
        taskLogDTO.setRemark(taskAssignModifyDTO.getRemark());
        // 获取接受人
        List<SfaTaskAssignEntity> currentAssigns = sfaTaskAssignMapper.selectList(new QueryWrapper<SfaTaskAssignEntity>().eq("task_id", taskAssignModifyDTO.getTaskId())
                .eq("status", 1).eq("delete_flag", 0));
        if(!CollectionUtils.isEmpty(currentAssigns)){
            List<String> assigns = currentAssigns.stream().map(SfaTaskAssignEntity::getAssignUserName).collect(Collectors.toList());
            taskLogDTO.setProcessObj(assigns);
        }
        taskLogService.saveLog(taskLogDTO);



    }

    private void updateAssignUSer(SfaTaskEntity sfaTaskEntity,List<TaskAssignDTO> assistedProcessUsers,int assignType,String processUserId,String processUserName) {
        assistedProcessUsers = Optional.ofNullable(assistedProcessUsers).orElse(new ArrayList<>());

        // 清空所有协办
        SfaTaskAssignEntity deleteOption = new SfaTaskAssignEntity();
        deleteOption.setDeleteFlag(1);
        sfaTaskAssignMapper.update(deleteOption,new LambdaQueryWrapper<SfaTaskAssignEntity>()
                .eq(SfaTaskAssignEntity::getTaskId,sfaTaskEntity.getTaskId())
                .eq(SfaTaskAssignEntity::getAssignType,assignType)
                .eq(SfaTaskAssignEntity::getDeleteFlag,0)
        );

        if(!CollectionUtils.isEmpty(assistedProcessUsers)){
            // 检查协办中是否有主办人员信息
            SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getAssignType, 1)
                    .in(SfaTaskAssignEntity::getAssignUserId, assistedProcessUsers.stream().map(TaskAssignDTO::getEmpId).collect(Collectors.toList()))
                    .eq(SfaTaskAssignEntity::getDeleteFlag, 0)
                    .eq(SfaTaskAssignEntity::getTaskId, sfaTaskEntity.getTaskId())
            );
            if(Objects.nonNull(sfaTaskAssignEntity)){
                throw new ApplicationException("所添加的协办人中含有主办人");
            }

            assistedProcessUsers.forEach(e -> {
                // 保存指派信息
                saveAssign(sfaTaskEntity.getTaskId(),e,assignType,processUserId,processUserName);
            });
        }
    }

    private void updateMainAssign(SfaTaskEntity sfaTaskEntity, TaskAssignDTO mainProcessUser,String processUserId,String processUserName) {
        if(sfaTaskEntity.getStatus() == TaskStatusEnum.READY_SIGN.getStatus() || sfaTaskEntity.getStatus() == TaskStatusEnum.RE_SIGN.getStatus() || sfaTaskEntity.getStatus() ==TaskStatusEnum.PROCESSING.getStatus()){
            // 更换流程表中的处理人信息
            taskProcessService.changeProcessUser(sfaTaskEntity.getTaskId(),mainProcessUser);
        }


        // 删除原指派人
        SfaTaskAssignEntity deleteOption = new SfaTaskAssignEntity();
        deleteOption.setDeleteFlag(1);
        deleteOption.setUpdateTime(LocalDateTime.now());
        sfaTaskAssignMapper.update(deleteOption,new LambdaQueryWrapper<SfaTaskAssignEntity>()
                .eq(SfaTaskAssignEntity::getTaskId,sfaTaskEntity.getTaskId())
                .eq(SfaTaskAssignEntity::getAssignType,1)
                .eq(SfaTaskAssignEntity::getDeleteFlag,0)
        );
        // 根据新的指派人删除协办及cc
        sfaTaskAssignMapper.update(deleteOption,new LambdaQueryWrapper<SfaTaskAssignEntity>()
                .eq(SfaTaskAssignEntity::getAssignUserId,mainProcessUser.getEmpId())
                .in(SfaTaskAssignEntity::getAssignType,2,3)
                .eq(SfaTaskAssignEntity::getDeleteFlag,0)
        );

        // 保存指派信息
        saveAssign(sfaTaskEntity.getTaskId(),mainProcessUser,1,processUserId,processUserName);
    }

    @Override
    @Transactional
    public void taskContextModify(TaskContextModifyRequest taskContextModifyRequest) {
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskContextModifyRequest.getTaskId());
        if(Objects.isNull(sfaTaskEntity)){
            throw new ApplicationException("任务不存在");
        }
        Integer type = taskContextModifyRequest.getType();
        // 修改主任务
        if(type == 1){
            sfaTaskEntity.setContextTask(taskContextModifyRequest.getNewContextTaskId());
            sfaTaskMapper.updateById(sfaTaskEntity);
        }else{
            Long oldContextTaskId = taskContextModifyRequest.getOldContextTaskId();
            if(Objects.nonNull(oldContextTaskId)){
                // 解除所有字任务的绑定
                SfaTaskEntity oldTask = sfaTaskMapper.selectById(oldContextTaskId);
                if(Objects.nonNull(oldTask)){
                    oldTask.setContextTask(null);
                    sfaTaskMapper.updateById(oldTask);
                }
            }

            Long newContextTaskId = taskContextModifyRequest.getNewContextTaskId();
            if(Objects.nonNull(newContextTaskId)){
                SfaTaskEntity nextTask = sfaTaskMapper.selectById(newContextTaskId);
                if(Objects.nonNull(nextTask)){
                    nextTask.setContextTask(sfaTaskEntity.getTaskId());
                    sfaTaskMapper.updateById(nextTask);
                }
            }

        }
    }

    @Override
    @Transactional
    public void urge(TaskAuditRequest taskAuditRequest) {
        String person = taskAuditRequest.getPerson();
        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskAuditRequest.getTaskId());
        if(Objects.isNull(sfaTaskEntity)) {
            throw new ApplicationException("任务不存在");
        }

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person, RequestUtils.getLoginInfo());

        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskAuditRequest.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.REMINDER.getType());
        taskLogDTO.setProcessUserName(personInfo.getEmployeeName());
        taskLogDTO.setProcessUserId(personInfo.getEmployeeId());
        taskLogDTO.setRemark(taskAuditRequest.getRemark());
        taskLogService.saveLog(taskLogDTO);

        // 获取任务主办人和协办人
        List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, taskAuditRequest.getTaskId()).eq(SfaTaskAssignEntity::getDeleteFlag, 0).in(SfaTaskAssignEntity::getAssignType, 1, 2));

        List<NotifyPO> notifyPOS = new ArrayList<>();
        assignEntities.forEach(e -> {
            NotifyPO po = new NotifyPO();
            po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【催办】");
            po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
            po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【催办】" + taskAuditRequest.getRemark());
            po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
            po.setEmployeeId(e.getAssignUserId());
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        });
        // 老板催办
        if(person.equals(zw_senior_hr_employee_id)){
            // 增加项目管理
            List<DeptEmployeeRelationEntity> deptEmployeeRelationEntities = deptEmployeeRelationMapper.selectList(new LambdaQueryWrapper<DeptEmployeeRelationEntity>().eq(DeptEmployeeRelationEntity::getDeptId, 57).eq(DeptEmployeeRelationEntity::getDeleteFlag, 0));
            if(!CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
                deptEmployeeRelationEntities.forEach(e -> {
                    NotifyPO po = new NotifyPO();
                    po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【催办】");
                    po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                    po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【催办】" + taskAuditRequest.getRemark());
                    po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                    po.setEmployeeId(e.getEmployeeId());
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                });
            }
        }


        notifyService.saveBatch(notifyPOS);
    }

    @Override
    @Transactional
    public void modifyTask(TaskDTO taskDTO) {
        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskDTO.getTaskId());
        // 保存老信息
        SfaTaskEntity oldTaskEntity = new SfaTaskEntity();
        BeanUtils.copyProperties(sfaTaskEntity,oldTaskEntity,"taskType");

        BeanUtils.copyProperties(taskDTO,sfaTaskEntity,"taskType");
        sfaTaskMapper.updateById(sfaTaskEntity);

        boolean needMandatoryNotice = false;

        List<ObjectDiffModel> objectDiffModels = Optional.ofNullable(ObjectComparator.objectCompare(oldTaskEntity, sfaTaskEntity)).orElse(new ArrayList<>());
        if(!CollectionUtils.isEmpty(objectDiffModels)){
            Optional<ObjectDiffModel> contentOptional = objectDiffModels.stream().filter(f -> f.getFiledName().equals("content")).findFirst();
            if(contentOptional.isPresent()){
                needMandatoryNotice = true;
            }
           objectDiffModels.stream().filter(f -> f.getFiledName().equals("contextTask")).forEach(e -> {

                String oldValue = e.getOldValue();
                if(CommonUtil.StringUtils.isNotBlank(oldValue)){
                    SfaTaskEntity oldTask = sfaTaskMapper.selectById(oldValue);
                    if(Objects.nonNull(oldTask)){
                        e.setOldValue(oldTask.getTaskName());
                    }
                }

                if("0".equals(oldValue)){
                    e.setOldValue(StringUtils.EMPTY);
                }

                String value = e.getValue();
                if(CommonUtil.StringUtils.isNotBlank(value)){
                    SfaTaskEntity newTask = sfaTaskMapper.selectById(value);
                    if(Objects.nonNull(newTask)){
                        e.setValue(newTask.getTaskName());
                    }
                }
                if("0".equals(value)){
                    e.setValue(StringUtils.EMPTY);
                }
            });

        }


        List<NotifyPO> notifyPOS = new ArrayList<>();



        // 保存指派人信息
        TaskAssignDTO mainProcessUser = taskDTO.getMainProcessUser();
        if(Objects.nonNull(mainProcessUser)){
            SfaTaskAssignEntity sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, taskDTO.getTaskId()).eq(SfaTaskAssignEntity::getAssignType,1)
                    .eq(SfaTaskAssignEntity::getDeleteFlag, 0).last("limit 1"));
            if(!sfaTaskAssignEntity.getAssignUserId().equals(mainProcessUser.getEmpId())){
                needMandatoryNotice = true;
                ObjectDiffModel objectDiffModel = new ObjectDiffModel();
                objectDiffModel.setFiledName("mainProcessUser");
                objectDiffModel.setFiledAnnotation("主办人");
                objectDiffModel.setOldValue(sfaTaskAssignEntity.getAssignUserName()+"("+sfaTaskAssignEntity.getAssignUserId()+")");
                objectDiffModel.setValue(mainProcessUser.getEmpName() + "("+mainProcessUser.getEmpId()+")");
                objectDiffModels.add(objectDiffModel);
                // 修改主办人
                updateMainAssign(sfaTaskEntity, mainProcessUser,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
                // 重新获取下主办人
                sfaTaskAssignEntity = sfaTaskAssignMapper.selectOne(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, taskDTO.getTaskId()).eq(SfaTaskAssignEntity::getAssignType,1)
                        .eq(SfaTaskAssignEntity::getDeleteFlag, 0).last("limit 1"));
            }


            NotifyPO po = new NotifyPO();
            po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【已修改】");
            po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
            po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【已修改】");
            po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
            po.setEmployeeId(mainProcessUser.getEmpId());
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        }

        // 检查协办人
        modifyAssign(sfaTaskEntity,taskDTO.getAssistedProcessUsers(),2,objectDiffModels,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());
        if(!CollectionUtils.isEmpty(taskDTO.getAssistedProcessUsers())){
            taskDTO.getAssistedProcessUsers().forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("任务名称:"+ sfaTaskEntity.getTaskName() +"【已修改】");
                po.setType(NotifyTypeEnum.MANAGER_TASK.getType());
                po.setContent("任务名称:"+ sfaTaskEntity.getTaskName() +"【已修改】");
                po.setCode("/MissionDetail?taskId="+sfaTaskEntity.getTaskId());
                po.setEmployeeId(e.getEmpId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
        }


        List<TaskAssignDTO> ccProcessUsers = Optional.ofNullable(taskDTO.getCcProcessUsers()).orElse(new ArrayList<>());
        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
        String taskAssistant = configMapper.getValueByCode("task_assistant");
        if(sfaTaskEntity.getTaskType() == 1){

            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(zw_senior_hr_employee_id);
            String employeeName = findEmpName(zw_senior_hr_employee_id);
            taskAssignDTO.setEmpName(employeeName);
            taskAssignDTO.setDeptName("产品开发中心");
            taskAssignDTO.setDeptCode("D00000022");

            if(!ccProcessUsers.stream().filter(f -> f.getEmpId().equals(zw_senior_hr_employee_id)).findFirst().isPresent()){
                // 抄送增加
                ccProcessUsers.add(taskAssignDTO);
            }

        }

        if(sfaTaskEntity.getTaskType() == 1 ){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(taskAssistant.split(",")[0]);
            String employeeName = findEmpName(taskAssistant.split(",")[0]);
            taskAssignDTO.setEmpName(employeeName);
            taskAssignDTO.setDeptName("项目管理部");
            taskAssignDTO.setDeptCode("D00000057");
            // 抄送增加
            if(!ccProcessUsers.stream().filter(f -> f.getEmpId().equals(taskAssistant.split(",")[0])).findFirst().isPresent()){
                // 抄送增加
                ccProcessUsers.add(taskAssignDTO);
            }
        }

        if(sfaTaskEntity.getTaskType() == 1 ){
            TaskAssignDTO taskAssignDTO = new TaskAssignDTO();
            taskAssignDTO.setEmpId(taskAssistant.split(",")[1]);
            String employeeName = findEmpName(taskAssistant.split(",")[1]);
            taskAssignDTO.setEmpName(employeeName);
            taskAssignDTO.setDeptName("项目管理部");
            taskAssignDTO.setDeptCode("D00000057");
            // 抄送增加
            if(!ccProcessUsers.stream().filter(f -> f.getEmpId().equals(taskAssistant.split(",")[1])).findFirst().isPresent()){
                // 抄送增加
                ccProcessUsers.add(taskAssignDTO);
            }
        }

        // 检查抄送人
        modifyAssign(sfaTaskEntity,ccProcessUsers,3,objectDiffModels,taskDTO.getCreateUserId(),taskDTO.getCreateUserName());

        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setTaskId(taskDTO.getTaskId());
        taskLogDTO.setType(TaskLogTypeEnum.MODIFY.getType());
        taskLogDTO.setProcessUserName(taskDTO.getCreateUserName());
        taskLogDTO.setProcessUserId(taskDTO.getCreateUserId());
        taskLogDTO.setDiff(JSONArray.toJSONString(objectDiffModels));
        taskLogService.saveLog(taskLogDTO);

        if(sfaTaskEntity.getStatus() != TaskStatusEnum.READY_PUSH.getStatus() && sfaTaskEntity.getStatus() != TaskStatusEnum.AUDIT_PUSH.getStatus()){
            // 发送消息
            notifyService.saveBatch(notifyPOS);

            Integer taskType = sfaTaskEntity.getTaskType();

            if(needMandatoryNotice && taskType != 2){
                List<MandatoryNoticeDO> list = new ArrayList<>();
                notifyPOS.forEach(e -> {
                    MandatoryNoticeDO mandatoryNoticeDO = new MandatoryNoticeDO();
                    mandatoryNoticeDO.setContent("任务名称：【"+ sfaTaskEntity.getTaskName() +"】发生修改，请及时前往处理。");
                    mandatoryNoticeDO.setType(1);
                    mandatoryNoticeDO.setExternalId(sfaTaskEntity.getTaskId());
                    mandatoryNoticeDO.setEmpId(e.getEmployeeId());
                    list.add(mandatoryNoticeDO);
                });
                mandatoryNoticeService.createMandatoryNotice(list);
            }
        }
    }

    private void modifyAssign(SfaTaskEntity sfaTaskEntity, List<TaskAssignDTO> assistedProcessUsers, int assignType, List<ObjectDiffModel> objectDiffModels, String processUserId, String processUserName) {
        List<SfaTaskAssignEntity> assignEntities = Optional.ofNullable(sfaTaskAssignMapper.selectList(new LambdaQueryWrapper<SfaTaskAssignEntity>().eq(SfaTaskAssignEntity::getTaskId, sfaTaskEntity.getTaskId())
                .eq(SfaTaskAssignEntity::getAssignType,assignType).eq(SfaTaskAssignEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());
        assistedProcessUsers = Optional.ofNullable(assistedProcessUsers).orElse(new ArrayList<>());

        AtomicBoolean update = new AtomicBoolean(false);
        List<TaskAssignDTO> finalAssistedProcessUsers = assistedProcessUsers;
        assignEntities.forEach(e -> {
            Optional<TaskAssignDTO> first = finalAssistedProcessUsers.stream().filter(f -> f.getEmpId().equals(e.getAssignUserId())).findFirst();
            if(!first.isPresent()){
                update.set(true);
            }
        });
        assistedProcessUsers.forEach(e -> {
            Optional<SfaTaskAssignEntity> first = assignEntities.stream().filter(f -> f.getAssignUserId().equals(e.getEmpId())).findFirst();
            if(!first.isPresent()){
                update.set(true);
            }
        });
        if(update.get()){
            ObjectDiffModel objectDiffModel = new ObjectDiffModel();
            if(assignType == 2){
                objectDiffModel.setFiledName("assistedProcessUsers");
                objectDiffModel.setFiledAnnotation("协办人");
            }else{
                objectDiffModel.setFiledName("ccProcessUsers");
                objectDiffModel.setFiledAnnotation("抄送人");
            }

            List<String> oldValueList = new ArrayList<>();
            assignEntities.forEach(e -> {
                oldValueList.add(e.getAssignUserName() +"(" + e.getAssignUserId() +")");
            });
            objectDiffModel.setOldValue(String.join(",",oldValueList));
            List<String> valueList = new ArrayList<>();
            assistedProcessUsers.forEach(e -> {
                valueList.add(e.getEmpName()+"(" + e.getEmpId()+")");
            });
            objectDiffModel.setValue(String.join(",",valueList));
            objectDiffModels.add(objectDiffModel);
            updateAssignUSer(sfaTaskEntity,assistedProcessUsers,assignType,processUserId,processUserName);

        }
    }

    private void saveAssign(Long taskId, TaskAssignDTO taskAssignDTO, int assignType, String createUserId, String createUserName) {
        log.info("【save assign】taskId:{},assign:{},assignType:{}",taskId,taskAssignDTO,assignType);
        SfaTaskAssignEntity sfaTaskAssignEntity = new SfaTaskAssignEntity();
        sfaTaskAssignEntity.init(createUserId,createUserName);
        sfaTaskAssignEntity.setAssignType(assignType);
        sfaTaskAssignEntity.setStatus(1);
        sfaTaskAssignEntity.setTaskId(taskId);
        sfaTaskAssignEntity.setAssignDeptCode(taskAssignDTO.getDeptCode());
        sfaTaskAssignEntity.setAssignDeptName(taskAssignDTO.getDeptName());
        sfaTaskAssignEntity.setAssignUserId(taskAssignDTO.getEmpId());
        sfaTaskAssignEntity.setAssignUserName(taskAssignDTO.getEmpName());
        sfaTaskAssignMapper.insert(sfaTaskAssignEntity);
    }

    private String getProcessUserName(String mainProcessUserId) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("channel", 3)
                .eq("employee_id", mainProcessUserId)
                .last("limit 1")
        );
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            return null;
        }

        return ceoBusinessOrganizationPositionRelation.getEmployeeName();
    }
}
