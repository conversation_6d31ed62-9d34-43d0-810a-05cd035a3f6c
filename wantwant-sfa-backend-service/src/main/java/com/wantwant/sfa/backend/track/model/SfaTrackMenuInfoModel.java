package com.wantwant.sfa.backend.track.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Description: 面试原因用Model。
 * @Auther: zhengxu
 * @Date: 2021/11/09/下午5:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sfa_track_menu_info")
@ApiModel(value = "SfaTrackMenuInfo对象", description = "菜单点击采集")
public class SfaTrackMenuInfoModel {

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("employee_id")
    private String employeeId;

    @TableField("employee_name")
    private String employeeName;

    @TableField("route_before")
    private String routeBefore;

    @TableField("route_before_name")
    private String routeBeforeName;

    @TableField("route_after")
    private String routeAfter;

    @TableField("route_after_name")
    private String routeAfterName;

    @TableField("status")
    private String status;

    @TableField("create_time")
    private LocalDateTime createTIme;

    @TableField("create_person")
    private String createPerson;
}
