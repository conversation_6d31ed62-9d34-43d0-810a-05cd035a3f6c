package com.wantwant.sfa.backend.track.service.impl;

import com.wantwant.sfa.backend.mapper.track.TrackMenuInfoMapper;
import com.wantwant.sfa.backend.track.model.SfaTrackMenuInfoModel;
import com.wantwant.sfa.backend.track.request.TrackMenuRequest;
import com.wantwant.sfa.backend.track.service.ITrackService;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class TrackService implements ITrackService {

    @Autowired
    private TrackMenuInfoMapper trackMenuInfoMapper;

    @Transactional
    @Override
    public void commitTrackMenuInfo(List<TrackMenuRequest> list) {
        list.forEach(e->{
            SfaTrackMenuInfoModel model = new SfaTrackMenuInfoModel();
            BeanUtils.copyProperties(e, model);
            model.setCreateTIme(LocalDateTime.now());
            model.setCreatePerson("system");
            trackMenuInfoMapper.insert(model);
        });
    }
}
