package com.wantwant.sfa.backend.transaction.dto;

import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.interview.dto.DistributionInfoRequest;
import com.wantwant.sfa.backend.interview.enums.CeoExEnum;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
public class InternationalEmpValidResultDto {
    /** 岗位枚举 */
    private PositionEnum positionEnum;
    /** 岗位表 */
    private CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation;
    /** 组织视图 */
    private CeoBusinessOrganizationViewEntity ceoBusinessOrganizationViewEntity;
    /** 入职日期 */
    private LocalDate hireDate;
    /** 产品组信息 */
    private SfaBusinessGroupEntity sfaBusinessGroupEntity;
    /** 角色ID */
    private Integer roleId;
    /** 管辖四级地 */
    private List<DistributionInfoRequest> distributionInfoRequestList;
    /** 旺铺角色 */
    private CeoExEnum ceoExEnum;
    /** 办公地点 */
    private String officeLocationCode;
}
