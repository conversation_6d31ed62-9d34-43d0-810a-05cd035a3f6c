package com.wantwant.sfa.backend.util;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.dto.FreeCostTypeDTO;
import com.wantwant.sfa.backend.activityQuota.model.*;
import com.wantwant.sfa.backend.model.UpdateCeoFreeAccountModel;
import com.wantwant.sfa.backend.wallet.dto.CeoFreeAccountBatchQueryRequest;
import com.wantwant.sfa.backend.wallet.dto.CeoFreeAccountBatchQueryResponse;
import com.wantwant.sfa.backend.wallet.dto.CeoRevertModel;
import com.wantwant.sfa.backend.wallet.vo.CeoQuotaDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/24/下午4:32
 */
@Component
@Slf4j
public class ActivityQuotaConnectorUtil {
    @Value("${URL.ACTIVITY.batchQuery}")
    private String BATCH_QUERY;

    @Value("${URL.ACTIVITY.updateApply}")
    private String UPDATE_APPLY;

    @Value("${URL.ACTIVITY.quotaQuery}")
    private String QUOTA_QUERY;

    @Value("${URL.ACTIVITY.saveDeduct}")
    private String SAVE_DEDUCT;

    @Value("${URL.ACTIVITY.grant}")
    private String BATCH_GRANT;

    @Value("${URL.ACTIVITY.queryDeptQuota}")
    private String QUERY_DEPT_QUOTA;

    @Value("${URL.ACTIVITY.accountList}")
    private String ACCOUNT_LIST;

    @Value("${URL.ACTIVITY.updateBatchSaleAmount}")
    private String UPDATE_BATCH_SALE_AMOUNT;

    @Value("${URL.ACTIVITY.saveCeoFreeCostType}")
    private String SAVE_CEO_FREE_COST_TYPE;

    @Value("${URL.ACTIVITY.getAllSpuInfo}")
    private String GET_ALL_SPU_INFO;

    @Value("${URL.ACTIVITY.updateCeoFreeAccountInfo}")
    private String UPDATE_CEO_FREE_ACCOUNT;


    @Value("${URL.ACTIVITY.recoveryWantGoldCoin}")
    private String CEO_REVERT;

    @Value("${URL.ACTIVITY.queryMemberCoinAmountByMemberKey}")
    private String queryMemberCoinAmountByMemberKey;


    public Boolean updateBatchSaleAmountForSfa(UpdateBatchSaleAmountModel model){
        log.info("start updateBatchSaleAmountForSfa,model:{}",model);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(model);
            log.info("updateBatchSaleAmountForSfa request:{}",requestStr);
            httpPost = new HttpPost(UPDATE_BATCH_SALE_AMOUNT);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" updateBatchSaleAmountForSfa StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" updateBatchSaleAmountForSfa response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("updateBatchSaleAmountForSfa response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("临期售后-额度扣减失败");
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
        return true;
    }

    public Boolean saveCeoFreeCostType(FreeCostTypeDTO dto){
        log.info("start save ceo free cost type,dto:{}",dto);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(dto);
            log.info("save ceo free cost type:{}",dto);
            httpPost = new HttpPost(SAVE_CEO_FREE_COST_TYPE);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" save ceo free cost type StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" save ceo free cost type response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("save ceo free cost type response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("旺铺修改费用类型失败");
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
        return true;
    }

    public Boolean updateApply(ActivityQuotaValidModel model){
        log.info("start updateApply,model:{}",model);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(model);
            log.info("updateApply request:{}",model);
            httpPost = new HttpPost(UPDATE_APPLY);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" updateApply StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" updateApply response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("updateApply response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("旺铺修改活动审核失败");
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
        return true;
    }


    public CeoSmallFreeAccountModel accountList(String mobile){
        log.info("【coins accountList】,mobile:{}",mobile);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
        HashMap<String,String> map = new HashMap<>();
        map.put("mobileNumber",mobile);
        try {
            requestStr = JSONObject.toJSONString(map);
            log.info("【coins accountList】 request:{}",requestStr);
            httpPost = new HttpPost(ACCOUNT_LIST);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" 【coins accountList】 StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" 【coins accountList】 response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("updateApply response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Map<String, Object> data = mapper.readValue(JSONObject.toJSONString(responseValue.get("data")), Map.class);

            List<CeoSmallFreeAccountModel> records = JSONArray.parseArray(JSONObject.toJSONString(data.get("records")), CeoSmallFreeAccountModel.class);
            if(CollectionUtils.isEmpty(records)){
                return null;
            }

            return records.get(0);

        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }

    }

    public List<CeoQuotaModel> queryDeptQuota(Integer memberKey){
        log.info("query dept quota,memberKey:{}",memberKey);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        Map<String,Integer> param = new HashMap<>();
        param.put("memberKey",memberKey);


        try {
            requestStr = mapper.writeValueAsString(param);
            log.info("query dept quota,request:{}",requestStr);
            httpPost = new HttpPost(QUERY_DEPT_QUOTA);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" query dept quota StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" query dept quota response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("query dept quota response: {}", responseString);
            if(StringUtils.isBlank(responseString)){
                return null;
            }
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("查询旺铺额度失败");
            }

            List<CeoQuotaModel> list = (List<CeoQuotaModel>)responseValue.get("data");
            return JSONArray.parseArray(JSONObject.toJSONString(list),CeoQuotaModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    public List<CeoQuotaDetailVO> queryMemberCoinAmountByMemberKey(Long memberKey){
        log.info("query dept quota,memberKey:{}",memberKey);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        Map<String,Long> param = new HashMap<>();
        param.put("memberKey",memberKey);


        try {
            requestStr = mapper.writeValueAsString(param);
            log.info("query dept quota,request:{}",requestStr);
            httpPost = new HttpPost(queryMemberCoinAmountByMemberKey);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" query dept quota StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" query dept quota response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("query dept quota response: {}", responseString);
            if(StringUtils.isBlank(responseString)){
                return null;
            }
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("查询旺铺额度失败");
            }

            List<CeoQuotaDetailVO> list = (List<CeoQuotaDetailVO>)responseValue.get("data");
            return JSONArray.parseArray(JSONObject.toJSONString(list),CeoQuotaDetailVO.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    public List<CeoFreeAccountBatchQueryResponse> batchQueryCeoFreeAccountBalance(CeoFreeAccountBatchQueryRequest ceoFreeAccountBatchQueryRequest){
        log.info("batch query ceo free account balance:{}",JSONObject.toJSONString(ceoFreeAccountBatchQueryRequest));
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;


        try {
            requestStr = JSONObject.toJSONString(ceoFreeAccountBatchQueryRequest);
            httpPost = new HttpPost(BATCH_QUERY);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" batch query ceo free account balance StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" batch query ceo free account balance response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("batch query ceo free account balance response: {}", responseString);

            if(StringUtils.isBlank(responseString)){
                return null;
            }
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("查询旺铺额度失败");
            }

            List<CeoFreeAccountBatchQueryResponse> list = (List<CeoFreeAccountBatchQueryResponse>)responseValue.get("data");
            return JSONArray.parseArray(JSONObject.toJSONString(list),CeoFreeAccountBatchQueryResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    public List<SPUInfoModel> querySPUInfo(){
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            httpPost = new HttpPost(GET_ALL_SPU_INFO);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            log.info(" query spu info StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" query spu info response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("query spu info response: {}", responseString);
            if(StringUtils.isBlank(responseString)){
                return null;
            }
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("查询SPU信息失败");
            }

            List<CeoQuotaModel> list = (List<CeoQuotaModel>)responseValue.get("data");
            return JSONArray.parseArray(JSONObject.toJSONString(list),SPUInfoModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }




    /**
     * 批量发放额度
     *
     * @param list
     * @return
     */
    public Boolean batchGrant(List<ActivityQuotaValidModel> list){
        log.info("start batchGrant,model:{}",list);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(list);
            log.info("batchGrant request:{}",requestStr);
            httpPost = new HttpPost(BATCH_GRANT);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" batchGrant StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" batchGrant response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("batchGrant response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("旺铺增加额度失败");
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
        return true;
    }

    /**
     * 获取剩余额度
     *
     * @return
     */
    public BigDecimal queryQuota(String memberKey){
        log.info("start queryQuota,memberKey:{}",memberKey);

        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
        HashMap<String,String> map = new HashMap<>();
        map.put("memberKey",memberKey);
        try {
            requestStr = JSONObject.toJSONString(map);
            httpPost = new HttpPost(QUOTA_QUERY);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" queryQuota StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" queryQuota response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("queryQuota request: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);
            }

            BigDecimal result = new BigDecimal(responseValue.get("data").toString());
            return result;
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
    }

    /**
     * 收回额度
     *transfer recycle quota】
     * @param memberKey 会员memberKey
     * @param mobileNumber 会员手机号
     * @param month 月份
     * @param quota 额度
     * @param employeeName 操作人名称
     * @param channel 渠道
     */
    public void retrive(Long memberKey, String mobileNumber, String month, BigDecimal quota, String employeeName,int applyType,String deptCode, int channel) {
        log.info("start retrive,memberKey:{}",memberKey);

        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
        HashMap<String,String> map = new HashMap<>();
        map.put("memberKey",memberKey.toString());
        map.put("applyYearMonth",month);
        if(channel == 3){
            map.put("businessCode","2");
        }
        map.put("applyType",String.valueOf(applyType));
        // 如果是试吃费用设置旺铺试吃类型
        if(String.valueOf(applyType).equals("0")){
            map.put("amountType","1");
        }

        map.put("departmentCode",deptCode);
        map.put("mobileNumber",mobileNumber);
        map.put("deductAmount",quota.toString());
        map.put("creator",employeeName);

        try {
            requestStr = JSONObject.toJSONString(map);
            httpPost = new HttpPost(SAVE_DEDUCT);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            log.info(" retrive request: {}", requestStr);
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" retrive StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" retrive response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("queryQuota response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer) responseValue.get("code");
            if (Objects.isNull(code) || code != 0) {
                throw new ApplicationException("旺铺收回额度接口获取失败");
            }

        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new ApplicationException("旺铺收回额度接口获取失败");
        }
    }


    public void synWpCeoFreeAccount(UpdateCeoFreeAccountModel updateCeoFreeAccountModel) {
        log.info("syn ceo free account,model:{}",updateCeoFreeAccountModel);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(updateCeoFreeAccountModel);
            log.info("syn ceo free account request:{}",requestStr);
            httpPost = new HttpPost(UPDATE_CEO_FREE_ACCOUNT);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" syn ceo free account StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" syn ceo free account response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("syn ceo free account response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("旺铺冻结操作失败");
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
    }

    public BigDecimal ceoRevert(CeoRevertModel ceoRevertModel) {
        log.info("ceo quota revert,model:{}",JSONObject.toJSONString(ceoRevertModel));
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(ceoRevertModel);
            log.info("ceo quota revert request:{}",requestStr);
            httpPost = new HttpPost(CEO_REVERT);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" syn ceo free account StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" syn ceo free account response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("syn ceo free account response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new ApplicationException(error);

            }

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                String msg = (String) responseValue.get("msg");
                throw new ApplicationException(msg);
            }

            return new BigDecimal(String.valueOf((Double) responseValue.get("data")));
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new ApplicationException(e.getMessage());
        }
    }
}
