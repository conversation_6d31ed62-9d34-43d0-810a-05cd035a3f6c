package com.wantwant.sfa.backend.util;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.model.ApplyMemberModel;
import com.wantwant.sfa.backend.zw.request.ZWSignRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * @Description: 申请造旺会员调用旺铺接口Util。
 * @Auther: zhengxu
 * @Date: 2021/10/26/上午10:01
 */
@Component
@Slf4j
public class ApplyMemberConnectorUtil {
    @Value("${URL.ZW.updateApplyMember}")
    private String updateApplyMemberUrl;
    @Autowired
    private RestTemplate serviceRestTemplate;

    public boolean updateApply(ZWSignRequest request){

        ApplyMemberModel applyMemberModel = covertToApplyMemberModel(request);

        ResponseEntity<Response> responseResponseEntity = null;
        try {
            responseResponseEntity = serviceRestTemplate.postForEntity(updateApplyMemberUrl, applyMemberModel, Response.class);
        } catch (RestClientException e) {
            log.error("调用旺铺同步造旺申请信息出错:{}",e);
        }
        if(HttpStatus.OK == responseResponseEntity.getStatusCode()){
            Response body = responseResponseEntity.getBody();
            if(body.getCode() == 0){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    private ApplyMemberModel covertToApplyMemberModel(ZWSignRequest request) {
        ApplyMemberModel model = new ApplyMemberModel();
        model.setAgentCity(request.getCity());
        model.setAgentDistrict(request.getDistrict());
        model.setAgentProvince(request.getProvince());
        model.setEmployId(request.getEmployeeId());
        model.setUserMobile(request.getMobile());
        if(2 == request.getAffiliation()){
            model.setCeoFlag(0);
        }else{
            model.setCeoFlag(1);
        }
        model.setId(request.getApplicationId());
        model.setJobsType(request.getPostType());
        return model;
    }
}
