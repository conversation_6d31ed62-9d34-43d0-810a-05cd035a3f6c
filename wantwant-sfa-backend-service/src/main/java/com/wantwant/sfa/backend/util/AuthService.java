package com.wantwant.sfa.backend.util;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/17 15:36
 * 获取百度toke的工具类
 */
@Service
@Slf4j
public class AuthService {

    @Value("${baidu.access_key}")
    private String clientId;

    @Value("${baidu.secret_key}")
    private String clientSecret;

    @Value("${baidu.compare_face_similar}")
    private String compareFaceSimilar;

    /**
     * 获取权限token
     * @return 返回示例：
     * {
     * "access_token": "24.460da4889caad24cccdb1fea17221975.2592000.1491995545.282335-1234567",
     * "expires_in": 2592000
     * }
     */
    public String getAuth() {
        // 官网获取的 API Key 更新为你注册的
//        String clientId = "百度云应用的AK";
        // 官网获取的 Secret Key 更新为你注册的
//        String clientSecret = "百度云应用的SK";
        return getAuth(clientId, clientSecret);
    }

    /**
     * 获取API访问token
     * 该token有一定的有效期，需要自行管理，当失效时需重新获取.
     * @param ak - 百度云官网获取的 API Key
     * @param sk - 百度云官网获取的 Securet Key
     * @return assess_token 示例：
     * "24.460da4889caad24cccdb1fea17221975.2592000.1491995545.282335-1234567"
     */
    public String getAuth(String ak, String sk) {
        // 获取token地址
        String authHost = "https://aip.baidubce.com/oauth/2.0/token?";
        String getAccessTokenUrl = authHost
                // 1. grant_type为固定参数
                + "grant_type=client_credentials"
                // 2. 官网获取的 API Key
                + "&client_id=" + ak
                // 3. 官网获取的 Secret Key
                + "&client_secret=" + sk;
        try {
            URL realUrl = new URL(getAccessTokenUrl);
            // 打开和URL之间的连接
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();

            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String result = "";
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            /**
             * 返回结果示例
             */
            log.info("result:{}" + result);
            JSONObject jsonObject = new JSONObject(result);
            String access_token = jsonObject.getStr("access_token");
            return access_token;
        } catch (Exception e) {
        	log.error("获取百度云token失败！");
            log.error(e.getMessage(),e);
        }
        return null;
    }

    public String compareFace(String referToPicUrl, String picUrl){
        log.info("compareFace referToPicUrl:{}, {}",referToPicUrl, picUrl);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            String referToBase64 =  Base64Util.netUrl2Base64(referToPicUrl);
            String picUrlBase64 = Base64Util.netUrl2Base64(picUrl);
            List<Map<String, String>> images = Arrays.asList(
                    new HashMap<String, String>() {{
                        put("image", referToBase64);
                        put("image_type", "BASE64");
                    }},
                    new HashMap<String, String>() {{
                        put("image", picUrlBase64);
                        put("image_type", "BASE64");
                    }}
            );
            requestStr = com.alibaba.fastjson.JSONObject.toJSONString(images);
            httpPost = new HttpPost(compareFaceSimilar + "?access_token=" + getAuth());
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info("compareFace response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("compareFace request: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Map<String, Object> resultMap = (Map<String, Object>) responseValue.get("result");
            if(Objects.isNull(resultMap)) {
                return null;
            }
            return String.valueOf(resultMap.get("score"));
        }catch (Exception e) {
            log.info(e.getMessage(),e);
        }
        return null;
    }

    public String compareFace(String referToBase64, String picUrl, String accessToken) {
        log.info("compareFace picUrl:{}", picUrl);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            String picUrlBase64 = Base64Util.netUrl2Base64(picUrl);
            List<Map<String, String>> images = Arrays.asList(
                    new HashMap<String, String>() {{
                        put("image", referToBase64);
                        put("image_type", "BASE64");
                    }},
                    new HashMap<String, String>() {{
                        put("image", picUrlBase64);
                        put("image_type", "BASE64");
                    }}
            );
            requestStr = com.alibaba.fastjson.JSONObject.toJSONString(images);
            if (StringUtils.isBlank(accessToken)) {
                accessToken = getAuth();
            }
            httpPost = new HttpPost(compareFaceSimilar + "?access_token=" + accessToken);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info("compareFace response StatusCode:{}", response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("compareFace responseString: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Map<String, Object> resultMap = (Map<String, Object>) responseValue.get("result");
            if (Objects.isNull(resultMap)) {
                return null;
            }
            return String.valueOf(resultMap.get("score"));
        } catch (Exception e) {
            log.info(e.getMessage(), e);
        }
        return null;
    }
}
