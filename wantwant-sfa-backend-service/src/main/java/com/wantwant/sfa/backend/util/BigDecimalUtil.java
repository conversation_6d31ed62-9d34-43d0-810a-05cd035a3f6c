package com.wantwant.sfa.backend.util;

import java.math.BigDecimal;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.util
 * @Description:
 * @Date: 2024/11/13 11:13
 */
public class BigDecimalUtil {

    public static boolean isNotNullAndGreaterThanZero(BigDecimal value) {
        return value!= null && value.compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isNullOrLessThanOrEqualToZero(BigDecimal value) {
        return value == null || value.compareTo(BigDecimal.ZERO) <= 0;
    }
}
