package com.wantwant.sfa.backend.util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wantwant.commons.core.util.HttpUtil;
import com.wantwant.commons.web.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BonusIssueUtil {

    @Value("${URL.BONUS.BONUSISSUE}")
    private String BONUS_BONUSISSUE;


    public  Response post(String paramJsonString) {
        return postForResponse(BONUS_BONUSISSUE, paramJsonString);
    }

    private static Response postForResponse(String url, String body) {
        try {
            log.info("postForResponse post to {} param:{}", url, body);
            String result = HttpUtil.postJsonData(url, body);
            log.info("postForResponse post to {} param:{},result -> {}", url, body, result);
            JSONObject resultJson = JSONObject.parseObject(result);
            if (0 == resultJson.getInteger("code")) {
                JSONArray data = resultJson.getJSONArray("data");
                return Response.success();
            } else {
                return Response.error(resultJson.getString("msg"));
            }
        } catch (Exception e) {
            log.error("postForResponse 调用旺铺奖金发放接口 失败 ", e);
            return Response.error("系统错误");
        }
    }
}
