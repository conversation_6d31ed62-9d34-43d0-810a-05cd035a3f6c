package com.wantwant.sfa.backend.util;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/下午12:08
 */
public abstract  class CalculateUtils {


    public static BigDecimal ratio(BigDecimal sub,BigDecimal divide,int scala){
        if(divide.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        return sub.divide(divide,scala,BigDecimal.ROUND_HALF_UP);
    }



    public static BigDecimal ratioPercent(BigDecimal sub,BigDecimal divide,int scala){

        if(divide.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }

        return sub.divide(divide,scala,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
    }

    public static BigDecimal rate(BigDecimal rate, int scala) {
        if(Objects.isNull(rate)){
            return null;
        }

        return rate.multiply(new BigDecimal(100)).setScale(scala);
    }
}
