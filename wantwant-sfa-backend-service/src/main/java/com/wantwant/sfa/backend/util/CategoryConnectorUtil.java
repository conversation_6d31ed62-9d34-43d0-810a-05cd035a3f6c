package com.wantwant.sfa.backend.util;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.core.util.HttpUtil;
import com.wantwant.sfa.backend.model.CategoryMemberUpdateModel;
import com.wantwant.sfa.backend.model.CategoryModel;
import com.wantwant.sfa.backend.model.MemberProductGroupModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/21/上午10:30
 */
@Component
@Slf4j
public class CategoryConnectorUtil {
    @Value("${URL.CATEGORY.list}")
    private String CATEGORY_LIST;

    @Value("${URL.CATEGORY.syn}")
    private String CATEGORY_SYN;

    public List<CategoryModel> getCategory(){
        try {
            log.info("select all category url:{}",CATEGORY_LIST);
            String result = HttpUtil.post(CATEGORY_LIST, null);
            log.info("elect all category result:{}",result);
            if(StringUtils.isBlank(result)){
                return ListUtils.EMPTY_LIST;
            }
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> responseValue = mapper.readValue(result, Map.class);
            Object data = responseValue.get("data");
            if(Objects.isNull(data)){
                return ListUtils.EMPTY_LIST;
            }

            List<CategoryModel> categoryModels = JSONArray.parseArray(JSONObject.toJSONString(data), CategoryModel.class);
            return categoryModels;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return ListUtils.EMPTY_LIST;
    }


//    public boolean updateCategory(String memberKey,List<String>categoryLists,String businessGroupCode){
//        CategoryMemberUpdateModel categoryMemberUpdateModel = new CategoryMemberUpdateModel();
//        categoryMemberUpdateModel.setMemberKey(memberKey);
//        List<MemberProductGroupModel> list = new ArrayList<>();
//        MemberProductGroupModel memberProductGroupModel = new MemberProductGroupModel();
//        memberProductGroupModel.setBusinessGroupCode(businessGroupCode);
//        memberProductGroupModel.setCategoryIds(categoryLists);
//        list.add(memberProductGroupModel);
//        categoryMemberUpdateModel.setProductGroupList(list);
//
//        String request = JSONObject.toJSONString(categoryMemberUpdateModel);
//        log.info("updateMemberCategory url:{},request:{}",CATEGORY_SYN,request);
//        try {
//            String result = HttpUtil.postJsonData(CATEGORY_SYN, request);
//
//            log.info("updateMemberCategory response:{}",result);
//            if(StringUtils.isBlank(result)){
//                return false;
//            }
//            ObjectMapper mapper = new ObjectMapper();
//            Map<String, Object> responseValue = mapper.readValue(result, Map.class);
//            int code = (int)responseValue.get("code");
//            if(code != 0){
//                return false;
//            }
//            return true;
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//
//        return false;
//    }
}
