package com.wantwant.sfa.backend.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CouponConnectorUtil {
	
	private String  disableCouponUrl;
	
	@Value("${URL.COUPON.disableCouponUrl}")	
	public void setDisableCouponUrl(String disableCouponUrl) {
		this.disableCouponUrl = disableCouponUrl;
	}
	
		public  Object disableCoupon(<PERSON> ceoKey) {
	//		int is = 0 ;
			HttpClient httpClient = HttpClientBuilder.create().build();
	    	ObjectMapper mapper = new ObjectMapper();
	    	String requestStr = null;
	    	HttpPost httpPost = null;
	    	HttpResponse response = null;
	    	HttpEntity entity = null;
	    	String responseString = null;
	//    	boolean result = false;
	    	Map<String ,String> obj = new HashMap<>();
	    	Map<String, Object> responseValue  = new HashMap<>();
	    	Object  res = new Object();
	    	obj.put("accountId", ceoKey.toString());
	    	
	    	try {
				requestStr = mapper.writeValueAsString(obj);
				System.out.println("requestStr"+requestStr);
				httpPost = new HttpPost(disableCouponUrl);
				httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
				httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
				// 发送请求
				response = httpClient.execute(httpPost);
				// 解析应答
				entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				responseValue = mapper.readValue(responseString, Map.class);
				res = responseValue.get("Response");
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} 
	    	return res;
		}
	
}
