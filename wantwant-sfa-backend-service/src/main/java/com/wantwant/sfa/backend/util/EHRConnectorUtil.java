package com.wantwant.sfa.backend.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.interview.model.ApplyMemberSynDto;
import com.wantwant.sfa.backend.interview.model.SynDeptRangeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.Objects;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/09/下午1:57
 */
@Component
@Slf4j
public class EHRConnectorUtil {

    @Autowired
    Gson gson;

    @Value("${URL.EHR.synApplyMember}")
    private String SYN_APPLY_MEMBER;

    @Value("${URL.EHR.synDeptRange}")
    private String SYN_DEPT_RANGE;


    public void synApplyMember(ApplyMemberSynDto dto) {

        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;


        log.info("【同步候选人接口】:{}",SYN_APPLY_MEMBER);
        try {
            requestStr = mapper.writeValueAsString(dto);
            httpPost = new HttpPost(SYN_APPLY_MEMBER);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info("syn apply member request: {}",requestStr);
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("synApplyMember request: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
            log.info("synApplyMember() request: {}", responseValue);
            Integer code = (Integer) responseValue.get("code");
            log.info("synApplyMember() code request: {}", code);
            if (Objects.isNull(code) || code != 0) {
                String msg = (String) responseValue.get("msg");
                throw new ApplicationException(msg);
            }

        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new ApplicationException("调用同步候选人接口失败");
        }
    }



    public void synDeptRange(SynDeptRangeDto dto) {
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;


        log.info("【同步渠道范围】:{}",SYN_DEPT_RANGE);
        try {
            requestStr = mapper.writeValueAsString(dto);
            httpPost = new HttpPost(SYN_DEPT_RANGE);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info("syn apply member request: {}",requestStr);
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("synDeptRange request: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
            log.info("synDeptRange() request: {}", responseValue);
            Integer code = (Integer) responseValue.get("code");
            log.info("synDeptRange() code request: {}", code);
            if (Objects.isNull(code) || code != 0) {
                String msg = (String) responseValue.get("msg");
                throw new ApplicationException(msg);
            }

        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new ApplicationException("调用同步渠道范围失败");
        }
    }
}
