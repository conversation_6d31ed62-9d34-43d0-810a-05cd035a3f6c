package com.wantwant.sfa.backend.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.model.GoldCoinTransactionGrantRequest;
import com.wantwant.sfa.backend.estimate.model.CatchSkuModel;
import com.wantwant.sfa.backend.estimate.model.SaveSkuSaleModel;
import com.wantwant.sfa.backend.estimate.model.SkuOverSaleControlSaveWithPhaseRequest;
import com.wantwant.sfa.backend.model.estimate.EstimateModel;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/29/下午2:18
 */
@Component
@Slf4j
@RefreshScope
public class EstimateConnectorUtil {

    @Value("${URL.ESTIMATE.auditSalesEstimate}")
    private String AUDIT_ESTIMATE_URL;

    @Value("${URL.ESTIMATE.catchSku}")
    private String SKU_CATCH;

    @Value("${URL.ESTIMATE.saveSkuSale}")
    private String SAVE_SKU_SALE;

    @Value("${URL.ESTIMATE.SAVE_SKU_OVER_SALE_CONTROL_URL:localhost:8080}")
    private String SAVE_SKU_OVER_SALE_CONTROL_URL;

    @Resource
    private RestTemplate serviceRestTemplate;

    public Boolean auditSalesEstimate(EstimateModel model){
        log.info("start auditSalesEstimate,model:{}",model);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(model);
            log.info("auditSalesEstimate request: {}",requestStr);
            httpPost = new HttpPost(AUDIT_ESTIMATE_URL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" auditSalesEstimate StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" auditSalesEstimate response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("auditSalesEstimate request: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer result = (Integer)responseValue.get("code");
            String msg = (String)responseValue.get("msg");
            if(result != 0){
                throw new ApplicationException(msg);
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new ApplicationException("销售预估同步旺铺失败");
        }
        return true;
    }





    public List<CatchSkuModel> catchSku(){
        HttpClient httpClient = HttpClientBuilder.create().build();
        HttpEntity entity = null;
        String responseString = null;

        try {

            SSLContextBuilder builder = new SSLContextBuilder();
            builder.loadTrustMaterial(null, new TrustSelfSignedStrategy());
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                    builder.build());
            CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(
                    sslsf).build();


            HttpGet httpGet = new HttpGet(SKU_CATCH);
            log.info("【抓取旺铺sku】url:{}",SKU_CATCH);
            // 发送请求
            CloseableHttpResponse response = httpclient.execute(httpGet);



            log.info(" 【抓取旺铺sku】 response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("【抓取旺铺sku】 responseString: {}", responseString);

            JSONObject jsonObject = JSONObject.parseObject(responseString);

            JSONArray data = jsonObject.getJSONArray("data");

            List<CatchSkuModel> catchSkuModels = JSONArray.parseArray(data.toJSONString(), CatchSkuModel.class);

            return catchSkuModels;
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
    }

    public void saveSkuSale(SaveSkuSaleModel saveSkuSaleModel) {

        log.info("start save sku sale,saveSkuSaleModel:{}",saveSkuSaleModel);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(saveSkuSaleModel);
            log.info("save sku sale request: {}",requestStr);
            httpPost = new HttpPost(SAVE_SKU_SALE);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" save sku sale StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" save sku sale response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("save sku sale  request: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            String error = (String) responseValue.get("error");
            if (error != null) {
                throw new IllegalStateException(error);

            }

            Integer result = (Integer)responseValue.get("code");
            String msg = (String)responseValue.get("msg");
            if(result != 0){
                throw new ApplicationException(msg);
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
    }

    /**
     * 保存商品销售预估基础数据
     *
     * @param request 保存商品销售预估基础数据息
     * @throws ApplicationException 当请求参数为空或接口调用失败时抛出异常
     */
    public void saveSkuOverSaleControlInfoWithPhase(SkuOverSaleControlSaveWithPhaseRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new ApplicationException("保存商品销售预估基础数据请求参数不能为空");
        }

        log.info("save sku over sale control info with phase request:{}", JacksonHelper.toJson(request, true));

        try {
            ResponseEntity<Response> responseResponseEntity = serviceRestTemplate.postForEntity(SAVE_SKU_OVER_SALE_CONTROL_URL, request, Response.class);

            if (HttpStatus.OK == responseResponseEntity.getStatusCode()) {
                Response body = responseResponseEntity.getBody();
                if (Objects.isNull(body)) {
                    throw new ApplicationException("保存商品销售预估基础数据失败");
                }

                if (body.getCode() != 0) {
                    log.error("save sku over sale control info with phase, error code: {}, error message: {}, request params: {}",
                            body.getCode(), body.getMsg(), JacksonHelper.toJson(request, true));
                    throw new ApplicationException("保存商品销售预估基础数据失败");
                }

                log.info("save sku over sale control info with phase successful, response: {}", JacksonHelper.toJson(body, true));
            } else {
                log.error("save sku over sale control info with phase API call failed, HTTP status code: {}", responseResponseEntity.getStatusCode());
                throw new ApplicationException("保存商品销售预估基础数据调用失败");
            }
        } catch (Exception e) {
            if (e instanceof ApplicationException) {
                throw e;
            }
            log.error("Exception occurred when calling save sku over sale control info with phase API, request params: {}", JacksonHelper.toJson(request, true), e);
            throw new ApplicationException("保存商品销售预估基础数据接口异常：" + e.getMessage());
        }
    }

}
