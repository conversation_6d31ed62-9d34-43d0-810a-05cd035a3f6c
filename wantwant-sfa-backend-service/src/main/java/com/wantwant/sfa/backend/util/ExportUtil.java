package com.wantwant.sfa.backend.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExportUtil {
    private static final String BROWSER_FIREFOX = "firefox";
    private static final String EXCEL_SUFFIX = ".xlsx";

    private static String getName(String agent, String filename) throws UnsupportedEncodingException {
        String name = filename + System.nanoTime();
        if (agent.contains(BROWSER_FIREFOX)) {
            // 火狐浏览器
            filename = new String(name.getBytes(), "ISO8859-1");
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(name, "UTF-8");
        }
        return filename;
    }

    public static void writeEasyExcelResponse(HttpServletResponse response, HttpServletRequest request, String fileName, Class clazz, List<?> list) {
        if (list != null) {
            try {
                String agent = request.getHeader("USER-AGENT").toLowerCase();
                String codedFileName = ExportUtil.getName(agent, fileName);
                response.setCharacterEncoding("utf-8");
                if (agent.contains(BROWSER_FIREFOX)) {
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                } else {
                    response.setContentType("application/vnd.ms-excel");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                }
                EasyExcel.write(response.getOutputStream(), clazz).sheet(fileName).doWrite(list);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void writeEasyExcelResponseWithDynamicHeader(HttpServletResponse response, HttpServletRequest request, String fileName,
                                                               Class clazz, List<?> list, CellWriteHandler cellWriteHandler) {
        if (list != null) {
            try {
                String agent = request.getHeader("USER-AGENT").toLowerCase();
                String codedFileName = ExportUtil.getName(agent, fileName);
                response.setCharacterEncoding("utf-8");
                if (agent.contains(BROWSER_FIREFOX)) {
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                } else {
                    response.setContentType("application/vnd.ms-excel");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                }
                EasyExcel.write(response.getOutputStream(), clazz).registerWriteHandler(cellWriteHandler).sheet(fileName).doWrite(list);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 手写excel导出
     * @param response
     * @param request
     * @param fileName
     * @param list
     */
    public static void writeEasyExcelResponseBySelf(HttpServletResponse response, HttpServletRequest request, String fileName, List<List<String>> header,List<List<String>> list) {
        if (list != null) {
            try {
                String agent = request.getHeader("USER-AGENT").toLowerCase();
                String codedFileName = ExportUtil.getName(agent, fileName);
                response.setCharacterEncoding("utf-8");
                if (agent.contains(BROWSER_FIREFOX)) {
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                } else {
                    response.setContentType("application/vnd.ms-excel");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                }
                EasyExcel.write(response.getOutputStream()).head(header).sheet(fileName).doWrite(list);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 手写excel导出
     * @param response
     * @param request
     * @param fileName
     * @param list
     */
    public static void writeEasyExcelResponseBySelf(HttpServletResponse response, HttpServletRequest request, String fileName, RowWriteHandler rowWriteHandler,List<List<String>> header, List<List<String>> list) {
        if (list != null) {
            try {
                String agent = request.getHeader("USER-AGENT").toLowerCase();
                String codedFileName = ExportUtil.getName(agent, fileName);
                response.setCharacterEncoding("utf-8");
                if (agent.contains(BROWSER_FIREFOX)) {
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                } else {
                    response.setContentType("application/vnd.ms-excel");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                }
                EasyExcel.write(response.getOutputStream()).head(header).sheet(fileName).registerWriteHandler(rowWriteHandler).doWrite(list);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void writeBatchEasyExcelResponseOneSheet(HttpServletResponse response, HttpServletRequest request, String fileName, Class clazz1, Class clazz2, List<?> list1, List<?> list2) {
        if (list1 != null && list2 != null) {
            try {
                String agent = request.getHeader("USER-AGENT").toLowerCase();
                String codedFileName = ExportUtil.getName(agent, fileName);
                response.setCharacterEncoding("utf-8");
                if (agent.contains(BROWSER_FIREFOX)) {
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                } else {
                    response.setContentType("application/vnd.ms-excel");
                    response.setHeader("content-disposition", "attachment;filename=" + codedFileName + EXCEL_SUFFIX);
                }

                ExcelWriter build = EasyExcel.write(response.getOutputStream()).build();

                //一个sheet包含多个table情况
                WriteSheet writeSheet = new WriteSheet();

                WriteTable table1 = EasyExcel.writerTable(0).head(clazz1).build();
                WriteTable table2 = EasyExcel.writerTable(1).relativeHeadRowIndex(1).head(clazz2).build();

                build.write(list1,writeSheet,table1);
                build.write(list2,writeSheet,table2);

                build.finish();

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
