package com.wantwant.sfa.backend.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;

/**
 * 字段比较工具类
 * 提供多种方式比较两个对象的字段值
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
public class FieldCompareUtil {
    
    /**
     * 使用 Hutool 工具类比较两个对象的指定字段
     * 推荐使用这种方式，简洁且可读性好
     * 
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param fieldMapping 字段映射关系，key为obj1的字段名，value为obj2的字段名
     * @return 是否有字段发生变化
     */
    public static boolean compareFields(Object obj1, Object obj2, Map<String, String> fieldMapping) {
        if (obj1 == null || obj2 == null) {
            return obj1 != obj2;
        }
        
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String field1Name = entry.getKey();
            String field2Name = entry.getValue();
            
            try {
                Object value1 = ReflectUtil.getFieldValue(obj1, field1Name);
                Object value2 = ReflectUtil.getFieldValue(obj2, field2Name);
                
                if (!ObjectUtil.equal(value1, value2)) {
                    log.debug("字段 {} 发生变化: {} -> {}", field1Name, value1, value2);
                    return true;
                }
            } catch (Exception e) {
                log.warn("字段比较异常: {}", e.getMessage());
                // 继续比较其他字段
            }
        }
        return false;
    }
    
    /**
     * 使用 Hutool 工具类比较两个对象的指定字段，并返回变化详情
     * 
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param fieldMapping 字段映射关系
     * @return 字段变化结果
     */
    public static FieldCompareResult compareFieldsWithDetails(Object obj1, Object obj2, Map<String, String> fieldMapping) {
        List<FieldChange> changes = new ArrayList<>();
        
        if (obj1 == null || obj2 == null) {
            return new FieldCompareResult(obj1 != obj2, changes);
        }
        
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String field1Name = entry.getKey();
            String field2Name = entry.getValue();
            
            try {
                Object value1 = ReflectUtil.getFieldValue(obj1, field1Name);
                Object value2 = ReflectUtil.getFieldValue(obj2, field2Name);
                
                if (!ObjectUtil.equal(value1, value2)) {
                    changes.add(new FieldChange(field1Name, value1, value2));
                }
            } catch (Exception e) {
                log.warn("字段比较异常: {}", e.getMessage());
            }
        }
        
        return new FieldCompareResult(!changes.isEmpty(), changes);
    }
    
    /**
     * 使用字符串比较两个对象的指定字符串字段
     * 适用于确定是字符串类型的字段
     * 
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param fieldMapping 字段映射关系
     * @return 是否有字段发生变化
     */
    public static boolean compareStringFields(Object obj1, Object obj2, Map<String, String> fieldMapping) {
        if (obj1 == null || obj2 == null) {
            return obj1 != obj2;
        }
        
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String field1Name = entry.getKey();
            String field2Name = entry.getValue();
            
            try {
                String value1 = (String) ReflectUtil.getFieldValue(obj1, field1Name);
                String value2 = (String) ReflectUtil.getFieldValue(obj2, field2Name);
                
                if (!StringUtils.equals(value1, value2)) {
                    log.debug("字段 {} 发生变化: {} -> {}", field1Name, value1, value2);
                    return true;
                }
            } catch (Exception e) {
                log.warn("字段比较异常: {}", e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 使用函数式接口比较字段
     * 提供更灵活的字段获取方式
     * 
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param fieldExtractors 字段提取器映射
     * @return 是否有字段发生变化
     */
    public static <T1, T2> boolean compareFieldsWithExtractors(T1 obj1, T2 obj2, Map<Function<T1, ?>, Function<T2, ?>> fieldExtractors) {
        if (obj1 == null || obj2 == null) {
            return obj1 != obj2;
        }
        
        for (Map.Entry<Function<T1, ?>, Function<T2, ?>> entry : fieldExtractors.entrySet()) {
            Function<T1, ?> extractor1 = entry.getKey();
            Function<T2, ?> extractor2 = entry.getValue();
            
            try {
                Object value1 = extractor1.apply(obj1);
                Object value2 = extractor2.apply(obj2);
                
                if (!ObjectUtil.equal(value1, value2)) {
                    log.debug("字段发生变化: {} -> {}", value1, value2);
                    return true;
                }
            } catch (Exception e) {
                log.warn("字段比较异常: {}", e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 批量比较多个字段对
     * 
     * @param comparePairs 比较对列表
     * @return 是否有任何字段发生变化
     */
    public static boolean batchCompareFields(List<FieldComparePair> comparePairs) {
        for (FieldComparePair pair : comparePairs) {
            if (compareFields(pair.getObj1(), pair.getObj2(), pair.getFieldMapping())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 字段比较结果
     */
    @Data
    @AllArgsConstructor
    public static class FieldCompareResult {
        private boolean hasChanges;
        private List<FieldChange> changes;
    }
    
    /**
     * 字段变化详情
     */
    @Data
    @AllArgsConstructor
    public static class FieldChange {
        private String fieldName;
        private Object oldValue;
        private Object newValue;
    }
    
    /**
     * 字段比较对
     */
    @Data
    @AllArgsConstructor
    public static class FieldComparePair {
        private Object obj1;
        private Object obj2;
        private Map<String, String> fieldMapping;
    }
} 