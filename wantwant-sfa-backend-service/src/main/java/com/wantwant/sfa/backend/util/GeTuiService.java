package com.wantwant.sfa.backend.util;


import com.alibaba.fastjson.JSONObject;
import com.wantwant.arch.notification.api.dto.MessageDto;
import com.wantwant.arch.notification.api.dto.NotifyShortMessageRequestDto;
import com.wantwant.arch.notification.api.service.NotificationApi;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.common.base.Helper;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 个推发送短信服务
 * @Date 上午11:49 2020/7/14
 * @Param
 * @return
 */
@RefreshScope
@Component
public class GeTuiService {

    @Value("${getui.appId}")
    private String APPID;
    @Value("${getui.appKey}")
    private String APPKEY;
    @Value("${getui.masterSecret}")
    private String MASTERSECRET;
    @Value("${getui.authURL}")
    private String AUTHURL;
    @Value("${getui.sendURL}")
    private String SENDURL;
    @Value("${getui.notification.switch:false}")
    private boolean NOTIFICATION_SWITCH = false;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    NotificationApi notificationApi;


    private static final String REDIS_CACHE_KEY = "redisCache:geTui:sms:token";

    private final Logger logger = LoggerFactory.getLogger(GeTuiService.class);


    @PostConstruct
    public void clearCache(){
        redisUtil.del(REDIS_CACHE_KEY);
    }

    /**
     * 鉴权接口
     */
    private synchronized String AuthSign() throws Exception {
        if(redisUtil.hasKey(REDIS_CACHE_KEY)){
            return (String) redisUtil.get(REDIS_CACHE_KEY);
        }
        URL urlObj = new URL(AUTHURL);
        URLConnection con = urlObj.openConnection();
        HttpURLConnection httpURLConnection = (HttpURLConnection) con;

        //http头部
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        long timestamp = System.currentTimeMillis();


        //sha256加密，使用org.apache.commons包中自带的加密方法，需将加密后数据一起上传
        String sign = DigestUtils.sha256Hex(String.format("%s%d%s", APPKEY, timestamp, MASTERSECRET));
        JSONObject requestDataObject = new JSONObject();
        requestDataObject.put("sign", sign);
        requestDataObject.put("timestamp", timestamp);
        requestDataObject.put("appId", APPID);

        //建立连接，将数据写入内存
        OutputStreamWriter out = new
                OutputStreamWriter(httpURLConnection.getOutputStream());
        out.write(requestDataObject.toString());
        out.flush();
        out.close();

        BufferedReader in = null;
        String result = "";

        //将数据发送给服务端，并获取返回结果
        in = new BufferedReader(new InputStreamReader(httpURLConnection.getInputStream()));
        String line;
        while ((line = in.readLine()) != null) {
            result += line;
        }
        logger.info("请求个推鉴权返回报文----" + result);
        GeTuiResponse getuiResponse = JSONObject.toJavaObject(JSONObject.parseObject(result), GeTuiResponse.class);
        if (!"20000".equalsIgnoreCase(getuiResponse.getResult())) {
            logger.error("请求个推鉴权异常，异常原因----" + getuiResponse.getMsg());
            throw new ApplicationException(getuiResponse.getMsg());
        }
        Map data =  getuiResponse.getData();
        if(data.containsKey("authToken")){
           String token = String.valueOf(data.get("authToken"));
           redisUtil.set(REDIS_CACHE_KEY,token, (2*60-5)*60);
           return token;
        }else{
            logger.error("无法获取TOKEN,getuiResponse : {}",JSONObject.toJSONString(getuiResponse));
            throw new ApplicationException(getuiResponse.getMsg());
        }

    }


    @Async(value = "geTuiSmsSend")
    public void smsPushListSync(String smsTemplateId, Map<String,String> smsParam, List<String> recNum){
        try {
            SmsPushList(smsTemplateId,smsParam,recNum);
        } catch (Exception e) {
            logger.error("ERROR!!!!!!!!",e);
        }
    }

    /**
     * 群推接口  单次最大上限50条
     * 接口文档:https://docs.getui.com/sms/interface/#a4
     */
    public boolean SmsPushList(@NotNull String smsTemplateId, Map<String, String> smsParam, List<String> recNum) throws Exception {
        Assert.notNull(smsTemplateId, "短信模板ID不能为空");
        if (NOTIFICATION_SWITCH) {
            Map<String, Object> _smsParam;
            if (smsParam == null) {
                _smsParam = new HashMap<>();
            } else {
                _smsParam = new HashMap<>(smsParam);
            }
            logger.info("请求个推发送短信-消息中心:smsTemplateId:{},smsParam:{},recNum:{}", smsTemplateId, smsParam, recNum);
            NotifyShortMessageRequestDto notifyShortMessageRequestDto = new NotifyShortMessageRequestDto();
            notifyShortMessageRequestDto.setOutsideTemplateId(smsTemplateId);
            List<MessageDto> messageDtoList = recNum.stream().map(mobile -> {
                MessageDto messageDto = new MessageDto();
                messageDto.setTo(mobile);
                messageDto.setTemplateParameters(new HashMap<>(_smsParam));
                return messageDto;
            }).collect(Collectors.toList());
            notifyShortMessageRequestDto.setMessages(messageDtoList);
            return Helper.getResultData(notificationApi.notifyShortMessage(notifyShortMessageRequestDto), true);
        }
        logger.info("请求个推发送短信请求参数----" + smsTemplateId + smsParam + recNum);
        String authToken = AuthSign();
        //短信群推url
        URL urlObj = new URL(SENDURL);
        URLConnection con = urlObj.openConnection();
        HttpURLConnection httpURLConnection = (HttpURLConnection) con;

        //http头部
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");

        JSONObject requestDataObject = new JSONObject();
        requestDataObject.put("appId", APPID);
        requestDataObject.put("authToken", authToken);
        requestDataObject.put("smsTemplateId", smsTemplateId);
        requestDataObject.put("smsParam", smsParam);
        List<String> recNumMD5 = new ArrayList<>();
        recNum.forEach(num -> {
            recNumMD5.add(org.springframework.util.DigestUtils.md5DigestAsHex(num.getBytes()));
        });
        requestDataObject.put("recNum", recNumMD5);

        //建立连接，将数据写入内存
        OutputStreamWriter out = new
                OutputStreamWriter(httpURLConnection.getOutputStream());
        out.write(requestDataObject.toString());
        out.flush();
        out.close();
        BufferedReader in = null;
        String result = "";

        //将数据发送给服务端，并获取返回结果
        in = new BufferedReader(new
                InputStreamReader(httpURLConnection.getInputStream()));
        String line;
        while ((line = in.readLine()) != null) {
            result += line;
        }
        logger.info("请求个推发送短信返回报文----" + result);
        GeTuiResponse getuiResponse = JSONObject.toJavaObject(JSONObject.parseObject(result), GeTuiResponse.class);
        if (!"20000".equalsIgnoreCase(getuiResponse.getResult())) {
            logger.error("请求个推发送短信异常，异常原因----" + getuiResponse.getMsg());
            throw new ApplicationException(getuiResponse.getMsg());
        }
        return true;
    }
}
