package com.wantwant.sfa.backend.util;

import com.alibaba.fastjson.JSONObject;
import com.gexin.rp.sdk.base.IBatch;
import com.gexin.rp.sdk.base.IIGtPush;
import com.gexin.rp.sdk.base.IPushResult;
import com.gexin.rp.sdk.base.impl.AppMessage;
import com.gexin.rp.sdk.base.impl.ListMessage;
import com.gexin.rp.sdk.base.impl.SingleMessage;
import com.gexin.rp.sdk.base.impl.Target;
import com.gexin.rp.sdk.base.payload.APNPayload;
import com.gexin.rp.sdk.base.payload.APNPayload.DictionaryAlertMsg;
import com.gexin.rp.sdk.base.payload.APNPayload.Sound;
import com.gexin.rp.sdk.base.uitls.AppConditions;
import com.gexin.rp.sdk.http.IGtPush;
import com.gexin.rp.sdk.template.AbstractTemplate;
import com.gexin.rp.sdk.template.NotificationTemplate;
import com.gexin.rp.sdk.template.TransmissionTemplate;
import com.gexin.rp.sdk.template.style.Style0;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class GeTuiUtil {

	// STEP1：获取应用基本信息
	@Value("${getui.appId}")
	private String appId;
	@Value("${getui.appKey}")
	private String appKey;
	@Value("${getui.masterSecret}")
	private String masterSecret;
	@Value("${getui.url}")
	private String url;

	/**
	 * 异步执行停止task推送
	 * @param taskId
	 */
    @Async(value = "geTuiStopTask")
    public void stopTaskSync(List<String> taskIdList){
		log.info("start GeTuiUtil stopTaskSync taskIdList:{}", taskIdList);

        try {
			IGtPush push = new IGtPush(url, appKey, masterSecret);
			taskIdList.forEach(taskId -> {
				boolean ret = push.stop(taskId);
				log.info("end GeTuiUtil stopTaskSync taskId:{},ret:{}",taskId,ret);
			});

        } catch (Exception e) {
        	log.error("ERROR GeTuiUtil stopTaskSync taskIdList:{}",taskIdList);
            log.error("ERROR!!!!!!!!",e);
        }
    }
    
	/**
	 * 执行单推
	 * @param Alias 别名
	 * @param title 标题
	 * @param message 消息体
	 * @param payload 额外字段
	 * @param templateType 消息模板类型 1:通知模板,2:透传模板
	 * @return
	 */
	public JSONObject AppPushToSingle(String Alias,String title,String message,String payload,int templateType) {
			log.info("start GeTuiUtil AppPushToSingle Alias:{},title:{},Message:{},Payload:{},templateType:{}", Alias,title,message,payload,templateType);
			IGtPush push = new IGtPush(url, appKey, masterSecret);
	
			// STEP5：定义"AppMessage"类型消息对象,设置推送消息有效期等推送参数
			SingleMessage singleMessage = new SingleMessage();
			AbstractTemplate template = null;
			
			JSONObject strategyJson = new JSONObject();
			
			if(templateType == 1) {
				template = getNotificationTemplate(title, message, payload);
				strategyJson.put("ios", 2);
			}else if(templateType == 2) {
				template = getTransmissionTemplate(payload);
				strategyJson.put("ios", 3);
			}
			
			singleMessage.setStrategyJson(strategyJson.toString());

			singleMessage.setData(template);
	//		singleMessage.setAppIdList(appIds);
			singleMessage.setOffline(true);
			singleMessage.setOfflineExpireTime(1000 * 600); // 时间单位为毫秒
			
			log.info(singleMessage.getStrategyJson());
			
			Target target = new Target();
	        target.setAppId(appId);
	        target.setAlias(Alias);
//	        target.setClientId("72e5d96ad204942e112db38b7e4c0283");
//      	 target.setClientId("23714b7441d34dc7b3ffe247e9a0c979");
//      	 target.setClientId("2b92254bc01a7fe8c1ae9009d127a099");
	        
			// STEP6：执行推送
			IPushResult ret = push.pushMessageToSingle(singleMessage,target);
			log.info("end GeTuiUtil AppPushToSingle Res:{} Alias:{},title:{},Message:{},Payload:{},templateType:{}",ret.getResponse().toString(),Alias,title,message,payload,templateType);
			return new JSONObject(ret.getResponse());
	}
	

	/**
	 * 异步执行单推
	 * @param Alias 别名
	 * @param title 标题
	 * @param message 消息体
	 * @param payload 额外字段
	 * @param templateType 消息模板类型 1:通知模板,2:透传模板
	 * @return
	 */
    @Async(value = "geTuiAppPushToSingle")
    public void AppPushToSingleSync(String Alias,String title,String message,String payload,int templateType){
		log.info("start GeTuiUtil AppPushToSingleSync Alias:{},title:{},Message:{},Payload:{}", Alias,title,message,payload);

        try {
        	AppPushToSingle(Alias, title, message, payload, templateType);
        } catch (Exception e) {
        	log.error("ERROR AppPushToSingleSync Alias:{},title:{},Message:{},Payload:{}", Alias,title,message,payload);
            log.error("ERROR!!!!!!!!",e);
        }
    }
    
	/**
	 * 执行批量单推
	 * @param array
	 * @return
	 */
	public JSONObject AppPushToSingleList(List<JSONObject> array) {
		log.info("start GeTuiUtil AppPushToSinglelist array:{}", array);

		 IIGtPush push = new IGtPush(url, appKey, masterSecret);
         IBatch batch = push.getBatch();
         
         try {
        	 array.forEach(object -> {
        		 NotificationTemplate template = getNotificationTemplate(object.getString("title"), object.getString("message"), object.getString("payload"));
        		 
     			// STEP5：定义"AppMessage"类型消息对象,设置推送消息有效期等推送参数
     			SingleMessage singleMessage = new SingleMessage();
     			singleMessage.setData(template);
     	//		singleMessage.setAppIdList(appIds);
     			singleMessage.setOffline(true);
     			singleMessage.setOfflineExpireTime(1000 * 600); // 时间单位为毫秒
     			
    			JSONObject strategyJson = new JSONObject();
    			strategyJson.put("ios", 2);
    			singleMessage.setStrategyJson(strategyJson.toString());
        		 
        		  // 设置推送目标，填入appid和clientId
                 Target target = new Target();
                 target.setAppId(appId);
//                 target.setClientId(cid);
     	         target.setAlias(object.getString("Alias"));

                 try {
					batch.add(singleMessage, target);
				} catch (Exception e) {
					log.error(e.getMessage(),e);
				}
        	 });
        	 
        	 IPushResult ret = batch.submit();
        	 log.info("end GeTuiUtil AppPushToSinglelist:{}", ret.getResponse().toString());
 			return new JSONObject(ret.getResponse());
 			
		} catch (Exception e) {
       	 	log.info(e.getMessage(),e);
			return null;
		}
	}
	
	/**
	 * 执行批量推 通知消息
	 * @param AliasList 别名集合
	 * @param title
	 * @param message
	 * @param payload
	 * @return
	 */
	public JSONObject AppNoticePushToList(List<String> AliasList,String title,String message,String payload){
		return this.AppPushToList(AliasList, title, message, payload,1);
	}
	/**
	 * 执行批量推 透传消息
	 * @param AliasList 别名集合
	 * @param payload
	 * @return
	 */
	public JSONObject AppTranPushToList(List<String> AliasList,String payload){
		return this.AppPushToList(AliasList, null, null, payload,2);
	}
	/**
	 * 执行批量推
	 * @param AliasList 别名集合
	 * @param title
	 * @param message
	 * @param payload
	 * @param 消息模板类型 1:通知模板,2:透传模板
	 * @return
	 */
	public JSONObject AppPushToList(List<String> AliasList,String title,String message,String payload,int templateType){
		log.info("start GeTuiUtil AppPushToList title:{},Message:{},Payload:{},Alias:{},templateType:{}",title,message,payload,AliasList,templateType);

	        // 配置返回每个用户返回用户状态，可选
	        System.setProperty("gexin_pushList_needDetails", "true");
	        // 配置返回每个别名及其对应cid的用户状态，可选
	        // System.setProperty("gexin_pushList_needAliasDetails", "true");
	        IGtPush push = new IGtPush(url, appKey, masterSecret);
	        // 通知透传模板
	        AbstractTemplate template = null;
	        
	        // 厂商通道下发策略
			JSONObject strategyJson = new JSONObject();
	        
	        if(templateType == 1) {
				template = getNotificationTemplate(title, message, payload);
				strategyJson.put("ios", 2);
			}else if(templateType == 2) {
				template = getTransmissionTemplate(payload);
				strategyJson.put("ios", 3);
			}
	        
	        ListMessage listMessage = new ListMessage();
	        listMessage.setData(template);
	        // 设置消息离线，并设置离线时间
	        listMessage.setOffline(true);
	        // 离线有效时间，单位为毫秒
	        listMessage.setOfflineExpireTime(24 * 1000 * 3600);
			listMessage.setStrategyJson(strategyJson.toString());
//	        message.setStrategyJson("{\"default\":4,\"ios\":4,\"st\":4}");
	
	        // 配置推送目标
	        List<Target> targets = new ArrayList<Target>();
	        AliasList.forEach(alias ->{
	        	Target target = new Target();
	        	target.setAppId(appId);
	 	        target.setAlias(alias);
	 	        
	 	        targets.add(target);
	        });
	        
	        // taskId用于在推送时去查找对应的message
	        String taskId = push.getContentId(listMessage);
	        IPushResult ret = push.pushMessageToList(taskId, targets);
	        log.info("end GeTuiUtil AppPushToList:{}", ret.getResponse().toString());
			return new JSONObject(ret.getResponse());
	}
	
	/**
	 * 执行群推
	 * @param phoneTypeList 手机类型 大写 IOS ANDROID
	 * @param title 
	 * @param message
	 * @param payload
	 * @return
	 */
	public JSONObject AppPush(List<String> phoneTypeList,String title,String message,String payload) {
			log.info("start GeTuiUtil AppPushToList title:{},Message:{},Payload:{},phoneTypeList:{}",title,message,payload,phoneTypeList);

	      	IGtPush push = new IGtPush(url, appKey, masterSecret);

	        NotificationTemplate template =  getNotificationTemplate(title, message, payload);
	        AppMessage appMessage = new AppMessage();
	        appMessage.setData(template);

	        appMessage.setOffline(true);
	        // 离线有效时间，单位为毫秒
	        appMessage.setOfflineExpireTime(24 * 1000 * 3600);
	        // 厂商通道下发策略
	        JSONObject strategyJson = new JSONObject();
			strategyJson.put("ios", 2);
	        appMessage.setStrategyJson(strategyJson.toString());

	        // 推送给App的目标用户需要满足的条件
	        AppConditions cdt = new AppConditions();
	        List<String> appIdList = new ArrayList<String>();
	        appIdList.add(appId);
	        appMessage.setAppIdList(appIdList);

	        cdt.addCondition(AppConditions.PHONE_TYPE, phoneTypeList, AppConditions.OptType.or);

	        appMessage.setConditions(cdt);

	        IPushResult ret = push.pushMessageToApp(appMessage);
	        
	        log.info("end GeTuiUtil PushtoAPP:{}", ret.getResponse().toString());
			return new JSONObject(ret.getResponse());

	}
	
	/**
	 * 通知消息模板
	 * @param title
	 * @param message
	 * @param payload
	 * @return
	 */
	private NotificationTemplate getNotificationTemplate(String title,String message,String payload) {
	        NotificationTemplate template = new NotificationTemplate();
	        // 设置APPID与APPKEY
	        template.setAppId(appId);
	        template.setAppkey(appKey);

	        // 透传消息接受方式设置，1：立即启动APP，2：客户端收到消息后需要自行处理
	        template.setTransmissionType(2);
	        
	        JSONObject payload1 = JSONObject.parseObject(payload);
			//pushType 用于app标识消息推送的类型，1:通知消息，2:透传消息
	        payload1.put("pushType", 1);
	        payload =  payload1.toJSONString();
	        
	        template.setTransmissionContent(payload);

	        Style0 style = new Style0();
	        // 设置通知栏标题与内容
	        style.setTitle(title);
	        style.setText(message);
	        // 配置通知栏图标
	        style.setLogo("icon.png");
	        // 配置通知栏网络图标
//	        style.setLogoUrl("");
	        // 设置通知是否响铃，震动，或者可清除
	        style.setRing(true);
	        style.setVibrate(true);
	        style.setClearable(true);
//	        style.setChannel("通知渠道id");
//	        style.setChannelName("通知渠道名称");
//	        style.setChannelLevel(3); //设置通知渠道重要性
	        template.setStyle(style);	        
			
//			Notify notify = new Notify(); 
//			notify.setTitle(title); 
//			notify.setContent(message);
//			notify.setPayload(payload);

	        template.setAPNInfo(getAPNPayload(title,message,payload.toString())); //详见【推送模板说明】iOS通知样式设置
	        return template;
	}
	
	/**
	 * 透传消息模板
	 * @param title
	 * @param message
	 * @param payload
	 * @return
	 */
	private TransmissionTemplate getTransmissionTemplate(String payload) {
			TransmissionTemplate template = new TransmissionTemplate();
	        // 设置APPID与APPKEY
	        template.setAppId(appId);
	        template.setAppkey(appKey);

	        // 透传消息接受方式设置，1：立即启动APP，2：客户端收到消息后需要自行处理
	        template.setTransmissionType(2);
	        
	        
	        JSONObject payload1 = JSONObject.parseObject(payload);
			//pushType 用于app标识消息推送的类型，1:通知消息，2:透传消息
	        payload1.put("pushType", 2);
	        payload =  payload1.toJSONString();

	        template.setTransmissionContent(payload);        

	        template.setAPNInfo(getAPNPayload(payload1)); //详见【推送模板说明】iOS通知样式设置
	        return template;
	}
	   
	   
	/**
	 * IOS消息样式及内容
	 * @param title
	 * @param message
	 * @param payload
	 * @return
	 */
	private APNPayload getAPNPayload(String title,String message,String payload) {
       APNPayload apnPayload = new APNPayload();
       //在已有数字基础上加1显示，设置为-1时，在已有数字上减1显示，设置为数字时，显示指定数字
       apnPayload.setAutoBadge("+1");
       apnPayload.setContentAvailable(0);
       //ios 12.0 以上可以使用 Dictionary 类型的 sound
       apnPayload.setSound("default");
       addCustomMsg(apnPayload,JSONObject.parseObject(payload));

//       apnPayload.setCategory(payload);
//       payload.addCustomMsg("由客户自定义消息key", "由客户自定义消息value");

       //简单模式APNPayload.SimpleMsg
       DictionaryAlertMsg alertMsg = new APNPayload.DictionaryAlertMsg();
       alertMsg.setTitle(title);
       alertMsg.setBody(message);
       apnPayload.setAlertMsg(alertMsg);

       return apnPayload;
   }
	
	/**
	 * IOS 透传消息样式及内容
	 * @param payload
	 * @return
	 */
	private APNPayload getAPNPayload(JSONObject payload) {
		APNPayload apnPayload = new APNPayload();
		apnPayload.setContentAvailable(1);
		//设置铃声
		Sound sound = new Sound();
		sound.setVolume(0);
		
		apnPayload.setSound(sound);
		addCustomMsg(apnPayload,payload);
		return apnPayload;
	}
	
	private void addCustomMsg(APNPayload apnPayload,JSONObject object) {
		object.forEach( (key,value)-> {
			apnPayload.addCustomMsg(key, value);
		});			
	}


}
