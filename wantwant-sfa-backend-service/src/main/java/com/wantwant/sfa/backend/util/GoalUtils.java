package com.wantwant.sfa.backend.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.wantwant.commons.ex.ApplicationException;

import com.wantwant.sfa.common.base.DateTimeUtility;
import lombok.extern.slf4j.Slf4j;
import model.SfaBusinessBranchGoal;
import model.SfaBusinessBranchGoalDTO;
import org.joda.time.DateTimeUtils;
import util.GoalUtil;

@Slf4j
public class GoalUtils {


	/**
	 * 计算汇总目标
	 * @param goal 设置目标
	 * @param organizationId 组织Id
	 * @return
	 */
	public static List<SfaBusinessBranchGoalDTO> handleGoal(List<SfaBusinessBranchGoal> goal,String organizationId) {
		List<SfaBusinessBranchGoalDTO> list;
		try {
			list =  GoalUtil.handleGoal(goal, organizationId);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new ApplicationException("目标异常");
		}
		return list;
	}
	
	/**
	 * 计算汇总目标
	 * @param zbGoal 总部设置目标
	 * @param areaGoal 大区设置目标
	 * @param organizationId 组织Id
	 * @return
	 */
	public static List<SfaBusinessBranchGoalDTO> handleGoal(List<SfaBusinessBranchGoal> zbGoal,List<SfaBusinessBranchGoal> areaGoal,String organizationId){
		
		List<SfaBusinessBranchGoalDTO> list;
		try {
			list =  GoalUtil.handleGoal(zbGoal,areaGoal, organizationId);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new ApplicationException("目标异常");
		}
		return list;
	}
	
	/**
	 * 计算汇总目标
	 * @param zbGoal 总部设置目标
	 * @param areaGoal 大区设置目标
	 * @param organizationId 组织Id
	 * @return
	 */
	public static SfaBusinessBranchGoalDTO handleGoal(SfaBusinessBranchGoal zbGoal,SfaBusinessBranchGoal areaGoal,String organizationId){
		SfaBusinessBranchGoalDTO goal;
		try {
			goal = GoalUtil.handleGoal(zbGoal, areaGoal, organizationId);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new ApplicationException("目标异常");
		}
		return goal;
	}


	public static HashMap<String,String> getDateRangeByType(String  type){
		// 1 根据当前时间 返回时间列表 如 2025-03 2025-04 2025-05 。。。到2025-12  用key - value 方式展示 key和value值一样
		// 2 根据当前时间 返回时间列表 如 2025第一季度 2025第二季度 2025第三季度 2025第四季度 用key - value 方式展示 key为2025-01 2025-02 2025-03 2025-04
		HashMap<String,String> map = new HashMap<>();
		// 如上用type判断是 返回 1 还是 2
		if (type.equals("1")){
			for (int i = 0; i < 12; i++) {
				String year = String.valueOf(LocalDate.now().getYear());
				String month = String.valueOf(LocalDate.now().getMonthValue());
				if (month.length() == 1){
					month = "0" + month;
				}
				String date = year + "-" + month;
				map.put(date,date);
			}
		}else if (type.equals("2")){
			for (int i = 0; i < 4; i++) {
				String year = String.valueOf(LocalDate.now().getYear());
				String month = String.valueOf(LocalDate.now().getMonthValue());
				if (month.length() == 1){
					month = "0" + month;
				}
				String date = year + "-" + month;
				map.put(date,year+"第"+(i+1)+"季度");
			}
		}
		return map;
	}


	/**
	 *  获取季度
	 * @param date 2025-01
	 * @return 1 2 3 4
	 */
	public static Integer getQuarterFromMonth(String date) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate date1 = LocalDate.parse(date + "-01", formatter);
		int month=date1.getMonthValue();
        return (month - 1) / 3 + 1;
	}


}
