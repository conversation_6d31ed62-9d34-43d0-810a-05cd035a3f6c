package com.wantwant.sfa.backend.util;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.model.GoldCoinTransactionGrantRequest;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;


@Component
@Slf4j
@RefreshScope
public class GoldCoinConnectorUtil {

    @Value("${URL.ACTIVITY.GOLD_COIN_GRANT_URL:localhost:8080}")
    private String GOLD_COIN_GRANT_URL;

    @Resource
    private RestTemplate serviceRestTemplate;



    /**
     * 旺金币发放接口调用
     *
     * @param request 旺金币发放请求参数
     * @throws ApplicationException 当请求参数为空或接口调用失败时抛出异常
     */
    public void grantGoldCoin(GoldCoinTransactionGrantRequest request) {
        // 参数校验
        if (Objects.isNull(request)) {
            throw new ApplicationException("旺金币发放请求参数不能为空");
        }
        
        log.info("grant gold coin request:{}", JacksonHelper.toJson(request, true));

        try {
            ResponseEntity<Response> responseResponseEntity = serviceRestTemplate.postForEntity(GOLD_COIN_GRANT_URL, request, Response.class);

            if (HttpStatus.OK == responseResponseEntity.getStatusCode()) {
                Response body = responseResponseEntity.getBody();
                if (Objects.isNull(body)) {
                    throw new ApplicationException("旺金币发放接口返回数据为空");
                }
                
                if (body.getCode() != 0) {
                    log.error("Gold coin grant failed, error code: {}, error message: {}, request params: {}", 
                            body.getCode(), body.getMsg(), JacksonHelper.toJson(request, true));
                    throw new ApplicationException("旺金币发放失败");
                }
                
                log.info("Gold coin grant successful, response: {}", JacksonHelper.toJson(body, true));
            } else {
                log.error("Gold coin grant API call failed, HTTP status code: {}", responseResponseEntity.getStatusCode());
                throw new ApplicationException("旺金币发放接口调用失败");
            }
        } catch (Exception e) {
            if (e instanceof ApplicationException) {
                throw e;
            }
            log.error("Exception occurred when calling gold coin grant API, request params: {}", JacksonHelper.toJson(request, true), e);
            throw new ApplicationException("调用旺金币发放接口异常：" + e.getMessage());
        }
    }
}
