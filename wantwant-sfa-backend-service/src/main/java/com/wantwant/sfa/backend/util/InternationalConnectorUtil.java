package com.wantwant.sfa.backend.util;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.dto.OverseaMemberManagerOpenAccountRequest;
import com.wantwant.sfa.backend.interview.dto.OverseaMemberSaveResponse;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
@RefreshScope
public class InternationalConnectorUtil {

    @Value("${URL.ROOT.overseaMemberManagerOpenAccount:localhost:8080}")
    private String OVER_SEA_MEMBER_MANAGER_OPEN_ACCOUNT_URL;

    @Resource
    private RestTemplate serviceRestTemplate;



    /**
     * 海外项目开户
     *
     * @param request 海外项目开户request
     * @throws ApplicationException 异常
     */
    public OverseaMemberSaveResponse openAccount(OverseaMemberManagerOpenAccountRequest request) {
        log.info("international member open account request:{}", JacksonHelper.toJson(request, true));

        try {
            ResponseEntity<Response> responseResponseEntity = serviceRestTemplate.postForEntity(OVER_SEA_MEMBER_MANAGER_OPEN_ACCOUNT_URL, request, Response.class);

            if (HttpStatus.OK == responseResponseEntity.getStatusCode()) {
                Response body = responseResponseEntity.getBody();
                if (Objects.isNull(body)) {
                    throw new ApplicationException("international member open account API response is null");
                }

                if (body.getCode() != 0) {
                    log.error("international member open account API call failed, response: {}", JacksonHelper.toJson(body, true));
                    throw new ApplicationException("open account API call failed");
                }
                log.info("international member open account API call succeeded, response: {}", JacksonHelper.toJson(body, true));
                
                // 使用JSONObject.parseObject方法将LinkedHashMap转换为OverseaMemberSaveResponse
                Object data = body.getData();
                if (Objects.isNull(data)) {
                    throw new ApplicationException("international member open account API response data is null");
                }

                return JacksonHelper.toObj(JacksonHelper.toJson(data, true), OverseaMemberSaveResponse.class, true);

            } else {
                log.error("international member open account API call failed, response status: {}, response: {}", responseResponseEntity.getStatusCode(), JacksonHelper.toJson(responseResponseEntity.getBody(), true));
                throw new ApplicationException("international member open account API call failed");
            }
        } catch (Exception e) {
            if (e instanceof ApplicationException) {
                throw e;
            }
            log.error("Exception occurred when calling international member open account API, request params: {}", JacksonHelper.toJson(request, true), e);
            throw new ApplicationException("international member open account API call exception: " + e.getMessage());
        }
    }
}
