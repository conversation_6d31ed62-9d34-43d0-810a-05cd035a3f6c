package com.wantwant.sfa.backend.util;

import java.util.HashMap;
import java.util.Map;

import com.wantwant.sfa.backend.common.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wantwant.commons.ex.ApplicationException;

@Component
@Slf4j
public class MEMBERConnectorUtil {

    @Value("${URL.MEMBER.queryByMobileUrl}")
    private String QUERY_BY_MOBILE_URL;
    @Value("${URL.MEMBER.queryByKeyUrl}")
    private String QUERY_BY_KEY_URL;
    @Value("${URL.MEMBER.queryByKeyListUrl}")
    private String QUERY_BY_KEY_LIST_URL;
    @Value("${URL.MEMBER.queryByMobileListUrl}")
    private String QUERY_BY_MOBILE_LIST_URL;
    @Value("${URL.MEMBER.updateMobileByKeyUrl}")
    private String UPDATE_MOBILE_BY_KEY_URL;
    @Autowired
    Gson gson;

    /**
     * 根据memberKey去查询手机号
     *
     * @param key
     * @return
     */
    public String sendQueryByKeyToMEMBER(String key) {
    	log.info("start MEMBERConnectorUtil sendQueryByKeyToMEMBER key:{}",key);
        String mobile = null;
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
//		boolean result = false;
        Map<String, String> obj = new HashMap<>();
//		Map<String, Object> responseValue  = new HashMap<>();
        obj.put("key", key);

        try {
            requestStr = mapper.writeValueAsString(obj);
            httpPost = new HttpPost(QUERY_BY_KEY_URL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                log.info("jsonObject:{}",jsonObject);
                if (jsonObject.get("code").getAsInt() == 0 && jsonObject.get("data") != null && !jsonObject.get("data").isJsonNull()) {
                    JsonObject data = jsonObject.get("data").getAsJsonObject();
                    mobile = data.get("mobile") == null || data.get("mobile").isJsonNull() ? null : data.get("mobile").getAsString();
                } else {
                	log.error("内部调用member接口异常,接口状态:" + response.getEntity());
                    throw new ApplicationException("内部调用member接口异常,接口状态:" + response.getEntity());
                }
            } else {
            	log.error("内部调用member接口异常:" + response.getStatusLine().getStatusCode());
                throw new ApplicationException("内部调用member接口异常:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("内部调用member接口异常!");
        }
        return mobile;
    }

    /**
     * 根据手机号去查询key
     *
     * @param mobile
     * @return
     */
    public String sendQueryByMobileToMEMBER(String mobile) {
        log.info("start MEMBERConnectorUtil sendQueryByMobileToMEMBER mobile:{}",mobile);
        String key = null;
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
//		boolean result = false;
        Map<String, String> obj = new HashMap<>();
//		Map<String, Object> responseValue  = new HashMap<>();
        obj.put("mobile", mobile);

        int channel = RequestUtils.getChannel();
        if(channel == 3) {
            obj.put("businessCode","2");
        }else if(channel == 2){
            obj.put("businessCode","3");
        }else{
            obj.put("businessCode","1");
        }

        try {
            requestStr = mapper.writeValueAsString(obj);
            httpPost = new HttpPost(QUERY_BY_MOBILE_URL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                log.info("jsonObject:{}",jsonObject);
                if (jsonObject.get("code").getAsInt() == 0 && jsonObject.get("data") != null && !jsonObject.get("data").isJsonNull()) {
                    JsonObject data = jsonObject.get("data").getAsJsonObject();
                    key = data.get("key") == null || data.get("key").isJsonNull() || "".equals(data.get("key").getAsString()) ? null : data.get("key").getAsString();
                } else {
                    throw new ApplicationException("内部调用member接口异常,接口状态:" + response.getEntity());
                }
            } else {
                throw new ApplicationException("内部调用member接口异常:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("内部调用member接口异常!");
        }
        return key;
    }

    /**
     * 根据手机号去查询key
     *
     * @param mobile
     * @return
     */
    public String sendQueryByMobileToMEMBER(String mobile,int channel) {
        log.info("start MEMBERConnectorUtil sendQueryByMobileToMEMBER mobile:{}",mobile);
        String key = null;
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
//		boolean result = false;
        Map<String, String> obj = new HashMap<>();
//		Map<String, Object> responseValue  = new HashMap<>();
        obj.put("mobile", mobile);

        obj.put("businessCode","1");


        try {
            requestStr = mapper.writeValueAsString(obj);
            httpPost = new HttpPost(QUERY_BY_MOBILE_URL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                log.info("jsonObject:{}",jsonObject);
                if (jsonObject.get("code").getAsInt() == 0 && jsonObject.get("data") != null && !jsonObject.get("data").isJsonNull()) {
                    JsonObject data = jsonObject.get("data").getAsJsonObject();
                    key = data.get("key") == null || data.get("key").isJsonNull() || "".equals(data.get("key").getAsString()) ? null : data.get("key").getAsString();
                } else {
                    throw new ApplicationException("内部调用member接口异常,接口状态:" + response.getEntity());
                }
            } else {
                throw new ApplicationException("内部调用member接口异常:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("内部调用member接口异常!");
        }
        return key;
    }

    /**
     * 根据memberKey去更新手机号
     *
     * @param mobile
     * @return
     */
    public boolean sendUpdateMobileByKeyToMEMBER(String key, String mobile) {
    	log.info("start MEMBERConnectorUtil sendUpdateMobileByKeyToMEMBER key:{},mobile:{}",key,mobile);
        boolean flag = false;
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
        Map<String, String> obj = new HashMap<>();
        obj.put("mobile", mobile);
        obj.put("key", key);

        try {
            requestStr = mapper.writeValueAsString(obj);
            httpPost = new HttpPost(UPDATE_MOBILE_BY_KEY_URL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                log.info("jsonObject:{}",jsonObject);
                if (jsonObject.get("code").getAsInt() == 0) {
                    flag = true;
                } else {
                    throw new ApplicationException("内部调用member接口异常,接口状态:" + response.getEntity());
                }
            } else {
                throw new ApplicationException("内部调用member接口异常:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("内部调用member接口异常!");
        }
        return flag;
    }



}
