package com.wantwant.sfa.backend.util;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.model.MessageInfoModel;
import com.wantwant.sfa.backend.model.MessageModifyModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/25/上午9:00
 */
@Component
@Slf4j
public class MessageUtil {

    @Value("${URL.message.add}")
    private String addMessageUrl;
    @Value("${URL.message.modify}")
    private String modifyMessageUrl;
    @Autowired
    private RestTemplate serviceRestTemplate;


    public boolean addMessage(MessageInfoModel model) {
        ResponseEntity<Response> responseResponseEntity = null;
        try {
            log.info("syn message to ceo url:{},request:{}",addMessageUrl,model);

            responseResponseEntity = serviceRestTemplate.postForEntity(addMessageUrl, model, Response.class);
        } catch (RestClientException e) {
            log.error("同步消息到旺铺出错:{}",e);
        }

        log.info("syn message to ceo response:{}",responseResponseEntity.toString());

        if(HttpStatus.OK == responseResponseEntity.getStatusCode()){
            Response body = responseResponseEntity.getBody();
            if(body.getCode() == 0){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }


    public boolean modify(MessageModifyModel model){
        ResponseEntity<Response> responseResponseEntity = null;
        try {
            log.info("syn modify message to ceo url:{},request:{}",modifyMessageUrl,model);

            responseResponseEntity = serviceRestTemplate.postForEntity(modifyMessageUrl, model, Response.class);
        } catch (RestClientException e) {
            log.error("同步消息到旺铺出错:{}",e);
        }

        log.info("syn modify message to ceo response:{}",responseResponseEntity.toString());

        if(HttpStatus.OK == responseResponseEntity.getStatusCode()){
            Response body = responseResponseEntity.getBody();
            if(body.getCode() == 0){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
}
