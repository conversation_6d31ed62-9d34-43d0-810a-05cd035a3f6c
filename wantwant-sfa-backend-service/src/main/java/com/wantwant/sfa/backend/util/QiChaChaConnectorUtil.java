package com.wantwant.sfa.backend.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wantwant.commons.ex.ApplicationException;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class QiChaChaConnectorUtil {
	
	@Value("${qichacha.access_key}")
	private String  ACCESS_KEY;
	@Value("${qichacha.secret_key}")
	private String  SECRET_KEY;
	@Value("${qichacha.basic_detail_url}")
	private String  BASIC_DETAIL_URL;
	
    @Autowired
    Gson gson;
	
	
	public JsonObject getBasicDetailsByName(String keyword){
		log.info("start QiChaChaConnectorUtil getBasicDetailsByName keyword:{}",keyword);
		HttpClient httpClient = HttpClientBuilder.create().build();
    	HttpUriRequest httpGet = null;
    	HttpResponse response = null;
    	HttpEntity entity = null;
    	String responseString = null;
     	
    	try {
			httpGet = new HttpGet(BASIC_DETAIL_URL+"?key="+ACCESS_KEY+"&keyword="+keyword);
			httpGet.setHeaders(this.RandomAuthentHeader().getAllHeaders());

			// 发送请求
			response = httpClient.execute(httpGet);
			
            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                if (jsonObject.get("Status").getAsInt() == 200 && jsonObject.get("Result") != null && !jsonObject.get("Result").isJsonNull()) {
                    
                    return jsonObject.get("Result").getAsJsonObject();

                } else if(jsonObject.get("Status").getAsInt() == 201){
                	//未查到结果
                	log.info(jsonObject.toString());
                	return null;
                }else {
                	log.error("内部调用企查查接口异常,response:{}",jsonObject);
                    throw new ApplicationException("内部调用企查查接口异常,response:" + jsonObject);
                }
            } else {
            	log.error("内部调用企查查接口异常,status:" + response.getStatusLine().getStatusCode());
                throw new ApplicationException("内部调用企查查接口异常,status:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ApplicationException(e.getMessage(),e);
        }
        
	}
	
	// 获取Auth Code
	protected HttpHead RandomAuthentHeader() {
		HttpHead reqHeader = new HttpHead();

		String timeSpan = String.valueOf(System.currentTimeMillis() / 1000);
		reqHeader.setHeader("Token", DigestUtils.md5Hex(ACCESS_KEY.concat(timeSpan).concat(SECRET_KEY)).toUpperCase());
		reqHeader.setHeader("Timespan", timeSpan);
		return reqHeader;
	}

}
