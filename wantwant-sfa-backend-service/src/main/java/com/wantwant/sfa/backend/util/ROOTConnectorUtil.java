package com.wantwant.sfa.backend.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.afterSales.request.FindingsRequest;
import com.wantwant.sfa.backend.afterSales.vo.AfterSalesEmpQuotaVO;
import com.wantwant.sfa.backend.afterSales.vo.QuotaVO;
import com.wantwant.sfa.backend.business.request.BusinessAuditRequest;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.customer.request.CustomerAuditRequest;
import com.wantwant.sfa.backend.customer.request.DisplayCustomerAuditRequest;
import com.wantwant.sfa.backend.dataModify.request.AuditStatusRequest;
import com.wantwant.sfa.backend.dataModify.request.MemberFindingsRequest;
import com.wantwant.sfa.backend.dataModify.request.PartnerAuditRequest;
import com.wantwant.sfa.backend.display.request.AbnormalRequest;
import com.wantwant.sfa.backend.display.request.ActivityFindingsRequest;
import com.wantwant.sfa.backend.displayRule.request.RuleDisableRequest;
import com.wantwant.sfa.backend.displayRule.request.RuleSaveRequest;
import com.wantwant.sfa.backend.feedback.request.AuditFeedbackRequest;
import com.wantwant.sfa.backend.feedback.request.LikeRequest;
import com.wantwant.sfa.backend.feedback.request.PushNodeRequest;
import com.wantwant.sfa.backend.interview.dto.SalaryPaymentTypeModel;
import com.wantwant.sfa.backend.interview.dto.SaveOrUpdateCeoDto;
import com.wantwant.sfa.backend.mainProduct.request.MainProductSkuPushWpRequest;
import com.wantwant.sfa.backend.mainProduct.request.TargetMainProductRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.request.CompanyClassBatchDTO;
import com.wantwant.sfa.backend.marketAndPersonnel.request.CustomerAuthorizationRequest;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.rebase.dto.RebaseRulePushWPDTO;
import com.wantwant.sfa.backend.sap.dto.SapMergeOrderHeaderDTO;
import com.wantwant.sfa.backend.sap.dto.SapResVoListDTO;
import com.wantwant.sfa.backend.sap.dto.SapResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Component
@Slf4j
public class ROOTConnectorUtil {
	
	private String  TOAPPRPVEDMEMBERURL;

	@Value("${URL.ROOT.updateAddressByKeyUrl}")
	private String UPDATE_ADDRESS_BY_KEY_URL;
	
	@Value("${URL.ROOT.disableCeoAreaInfoUrl}")
	private String DISABLE_CEO_AREA_INFO_URL;
	
	@Value("${URL.ROOT.updateStoreChannelUrl}")
	private String UPDATE_CEO_STORECHANNEL;

	@Value("${URL.ROOT.synAddress}")
	private String SYN_ADDRESS;

	@Value("${URL.ROOT.closeMemberUrl}")
	private String CLOSE_MEMBER;

	@Value("${URL.ROOT.saveOrUpdateCeoExInfoUrl}")
	private String SAVE_OR_UPDATE_CEO_INFO_URL;

	@Value("${URL.ROOT.toApprpvedMemberUrl}")	
	public void setTOAPPRPVEDMEMBERURL(String tOAPPRPVEDMEMBERURL) {
		TOAPPRPVEDMEMBERURL = tOAPPRPVEDMEMBERURL;
	}

	@Value("${URL.ROOT.updateMemberSigningCompany}")
	private String updateMemberSigningCompany;

	@Value("${URL.ROOT.updateEmployeeInfo}")
	private String UPDATE_EMPLOYEE_INFO;

	@Value("${URL.ROOT.customerAudit}")
	private String CUSTOMER_AUDIT;

	@Value("${URL.ROOT.leaveAudit}")
	private String LEAVE_AUDIT;

	@Value("${URL.ROOT.attendanceAudit}")
	private String ATTENDANCE_AUDIT;

	@Value("${URL.ROOT.visitAudit}")
	private String VISIT_AUDIT;

	@Value("${URL.ROOT.businessAudit}")
	private String BUSINESS_AUDIT;

	@Value("${URL.ROOT.customerTransfer}")
	private String CUSTOMER_TRANSFER;

	@Value("${URL.ROOT.batchCompanyClassification}")
	private String BATCH_COMPANY;

	@Value("${URL.ROOT.overdueAuthorization}")
	private String OVERDUE_AUTHORIZATION;

	@Value("${URL.ROOT.displayActivity}")
	private String DISPLAY_ACTIVITY;

	@Value("${URL.ROOT.memberAddress}")
	private String MEMBER_ADDRESS;

	@Value("${URL.ROOT.partnerAudit}")
	private String PARTNER_AUDIT;

	@Value("${URL.ROOT.auditCertification}")
	private String Audit_Certification;


	@Value("${URL.ROOT.displayAudit}")
	private String DISPLAY_AUDIT;

	@Value("${URL.ROOT.displayTransfer}")
	private String DISPLAY_TRANSFER;

	@Value("${URL.ROOT.afterSalesFinding}")
	private String AFTER_SALES_FINDING;

	@Value("${URL.ROOT.afterSalesQueryQuota}")
	private String AFTER_SALES_QUERY_QUOTA;

	@Value("${URL.ROOT.sapAddOrder}")
	private String SAP_ADD_ORDER;

	@Value("${URL.ROOT.displayPushAbnormal}")
	private String DISPLAY_PUSH_ABNORMAL;

	@Value("${URL.ROOT.feedbackAudit}")
	private String FEEDBACK_AUDIT;

	@Value("${URL.ROOT.feedbackNode}")
	private String FEEDBACK_NODE;

	@Value("${URL.ROOT.feedbackLike}")
	private String FEEDBACK_LIKE;

	@Value("${URL.ROOT.auditActDisplay}")
	private String DISPLAY_RULE_AUDIT;

	@Value("${URL.ROOT.disableActDisplayStandard}")
	private String DISPLAY_RULE_DISABLE;

	@Value("${URL.ROOT.pushMainProduct}")
	private String PUSH_MAIN_PRODUCT;

	@Value("${URL.ROOT.spuRebaseRules}")
	private String SPU_REBASE_RULES;

	@Value("${URL.ROOT.openMemberAccount}")
	private String OPEN_MEMBER_ACCOUNT;

	@Value("${URL.ROOT.updateMemberAccount}")
	private String UPDATE_MEMBER_ACCOUNT;

	@Value("${URL.ROOT.updateSmallMarket}")
	private String UPDATE_SMALL_MARKET;


	@Value("${URL.ROOT.updateBdServer}")
	private String UPDATE_BD_SERVER;


	@Value("${URL.ROOT.salaryPaymentType}")
	private String SALARY_PAYMENT_TYPE;

	@Value("${URL.ROOT.openShoppingGuideBDAccount}")
	private String OPEN_SHOPPING_GUIDE_ACCOUNT;

	@Value("${URL.ROOT.updateShoppingGuideBDAccount}")
	private String UPDATE_SHOPPING_GUIDE_ACCOUNT;

	@Value("${URL.ROOT.pushWpMainProductSku:}")
	private String PUSH_WP_MAIN_PRODUCT_SKU;

	@Autowired
	Gson gson;

	public void addHeader(HttpRequestBase requestBase) {
		LoginModel loginModel = RequestUtils.getLoginInfo();
		requestBase.addHeader("channel", String.valueOf(loginModel.getChannel()));
		requestBase.addHeader("organizationtype", String.valueOf(loginModel.getOrganizationType()));
		requestBase.addHeader("positiontypeid", String.valueOf(loginModel.getPositionTypeId()));
		requestBase.addHeader("businessgroup",String.valueOf(loginModel.getBusinessGroup()));
	}

	public void salaryPaymentType(SalaryPaymentTypeModel model){
		log.info("start update account,request:{}",JSONObject.toJSONString(model));

		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;

		try {
			requestStr = mapper.writeValueAsString(model);
			httpPost = new HttpPost(SALARY_PAYMENT_TYPE);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info("  salary payment type: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info("  salary payment type response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info(" salary payment type request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);

			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}

	public void updateAccount(MemberAccountUpdateRequest memberAccountUpdateRequest){
		log.info("start update account,request:{}",JSONObject.toJSONString(memberAccountUpdateRequest));


		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;

		try {
			requestStr = mapper.writeValueAsString(memberAccountUpdateRequest);
			httpPost = new HttpPost(UPDATE_MEMBER_ACCOUNT);
			log.info("update account requestStr:{}",requestStr);
			log.info("update account url:{}",UPDATE_MEMBER_ACCOUNT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info("  update account StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info("  update account response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info(" update account response: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);
			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}


	public void updateBDService(BDServicePersonnelModel bdServicePersonnelModel){
		log.info("start update bd server,request:{}",JSONObject.toJSONString(bdServicePersonnelModel));


		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;

		try {
			requestStr = mapper.writeValueAsString(bdServicePersonnelModel);
			httpPost = new HttpPost(UPDATE_BD_SERVER);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info("  update bd server StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info("  update bd server response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info(" update bd request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);

			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}


	public void updateSigningCompany(Long memberKey,String signingCompany){
		log.info("start update signing company,memberKey:{},signingCompany:{}",memberKey,signingCompany);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpGet httpGet = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;

		try {
			String url = updateMemberSigningCompany +"?memberKey="+memberKey.toString()+"&signingCompany="+signingCompany;
			httpGet = new HttpGet(url);

			log.info(" update signing company request: {}", url);
			log.info("  update signing company StringEntity: {}",httpGet.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpGet);
			log.info("  update signing company response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info(" update signing company response: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);

			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}



	public Long openAccount(OpenAccountModel openAccountModel){
		log.info("start ROOTConnectorUtil openAccount model:{}",JSONObject.toJSONString(openAccountModel));
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;


		try {
			requestStr = mapper.writeValueAsString(openAccountModel);
			httpPost = new HttpPost(OPEN_MEMBER_ACCOUNT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" openAccount StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("openAccount request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
			log.info("openAccount response: {}", responseValue);
			String error = (String) responseValue.get("error");
			log.info("openAccount() error request: {}", error);
			if (error != null) {
				throw new IllegalStateException(error);

			}

			Object memberKeyObj = responseValue.get("data");
			if(Objects.isNull(memberKeyObj)){
				throw new ApplicationException("获取数据失败");
			}
			
			Long memberKey = convertToLong(memberKeyObj);
			return memberKey;
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}

	}

	public void updateSmallMarket(List<MemberSmallMarketUpdateRequest> smallMarketList) {
		log.info("start ROOTConnectorUtil updateSmallMarket model:{}",smallMarketList);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;


		try {
			requestStr = mapper.writeValueAsString(smallMarketList);
			httpPost = new HttpPost(UPDATE_SMALL_MARKET);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" updateSmallMarket StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("updateSmallMarket request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
			log.info("updateSmallMarket response: {}", responseValue);
			String error = (String) responseValue.get("error");
			log.info("updateSmallMarket() error request: {}", error);
			if (error != null) {
				throw new IllegalStateException(error);

			}

		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}



	@Transactional
	public Long memberToCeo(String mobile,String storeChannel,String posID,String storeName, int channel){
		log.info("start ROOTConnectorUtil memberToCeo mobile:{},storeChannel:{},posID:{},storeName:{}",mobile,storeChannel,posID,storeName);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String,String> obj = new HashMap<>();
		obj.put("mobileNumber", mobile);
		obj.put("storeChannel", storeChannel);
		obj.put("parentKey", "0");
		obj.put("posID", posID);
		obj.put("storeName", storeName);
		obj.put("businessCode","1");

		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(TOAPPRPVEDMEMBERURL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" memberToCeo StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("CustomerService() memberToCeo request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
			log.info("memberToCeo() toCeo request: {}", responseValue);
			String error = (String) responseValue.get("error");
			log.info("memberToCeo() error request: {}", error);
			if (error != null) {
				throw new IllegalStateException(error);

			}

			LinkedHashMap<String,Object> data = (LinkedHashMap)responseValue.get("object");
			if(Objects.isNull(data)){
				throw new ApplicationException("获取数据失败");
			}

			Integer memberKey = (Integer)data.get("memberKey");
			if(Objects.isNull(memberKey)){
				throw new ApplicationException("memberKey获取失败");
			}
			return memberKey.longValue();
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}


	public Long memberToCeo(String mobile,String storeChannel,String posID,String storeName) {
		log.info("start ROOTConnectorUtil memberToCeo mobile:{},storeChannel:{},posID:{},storeName:{}",mobile,storeChannel,posID,storeName);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String,String> obj = new HashMap<>();
		obj.put("mobileNumber", mobile);
		obj.put("storeChannel", storeChannel);
		obj.put("parentKey", "0");
		obj.put("posID", posID);
		obj.put("storeName", storeName);
		int channel = RequestUtils.getChannel();
		if(channel == 3) {
			obj.put("businessCode","2");
		}else if(channel == 2){
			obj.put("businessCode","3");
		}else{
			obj.put("businessCode","1");
		}
		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(TOAPPRPVEDMEMBERURL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" memberToCeo StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("CustomerService() memberToCeo request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
			log.info("memberToCeo() toCeo request: {}", responseValue);
			String error = (String) responseValue.get("error");
			log.info("memberToCeo() error request: {}", error);
			if (error != null) {
				throw new IllegalStateException(error);
				
			}

			LinkedHashMap<String,Object> data = (LinkedHashMap)responseValue.get("object");
			if(Objects.isNull(data)){
				throw new ApplicationException("获取数据失败");
			}

			Integer memberKey = (Integer)data.get("memberKey");
			if(Objects.isNull(memberKey)){
				throw new ApplicationException("memberKey获取失败");
			}
			return memberKey.longValue();
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}

	public Long operStore(String mobile,String storeChannel,String posID,String storeName) {
		log.info("start ROOTConnectorUtil memberToCeo mobile:{},storeChannel:{},posID:{},storeName:{}",mobile,storeChannel,posID,storeName);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String,String> obj = new HashMap<>();
		obj.put("mobileNumber", mobile);
		obj.put("storeChannel", storeChannel);
		obj.put("parentKey", "0");
		obj.put("posID", posID);
		obj.put("storeName", storeName);
		int channel = RequestUtils.getChannel();
		if(channel == 3) {
			obj.put("businessCode","2");
		}else if(channel == 2){
			obj.put("businessCode","3");
		}else{
			obj.put("businessCode","1");
		}

		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(TOAPPRPVEDMEMBERURL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" memberToCeo StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("CustomerService() memberToCeo request: {}", responseString);
			Map<String, Object> responseValue = null;
			try{
				responseValue = mapper.readValue(responseString, Map.class);
			}catch (Exception e) {
				log.error(e.getMessage());
				throw new ApplicationException("旺铺开户接口返回信息错误");
			}
			log.info("memberToCeo() toCeo request: {}", responseValue);
			String error = (String) responseValue.get("error");
			log.info("memberToCeo() error request: {}", error);
			if (error != null) {
				throw new IllegalStateException(error);
			}

			LinkedHashMap<String,Object> data = (LinkedHashMap)responseValue.get("object");
			if(Objects.isNull(data)){
				throw new ApplicationException("获取数据失败");
			}

			Integer memberKey = (Integer)data.get("memberKey");
			if(Objects.isNull(memberKey)){
				// 修改不返回memberKey
				return null;
			}
			return memberKey.longValue();
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}


	public Boolean openMember(String memberKey){
		log.info("start close member,memberKey:{}",memberKey);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String,String> obj = new HashMap<>();
		obj.put("memberKey", memberKey);
		obj.put("deadStatus", "0");
		obj.put("quitDate", LocalDateTimeUtils.formatTime(LocalDateTime.now(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(CLOSE_MEMBER);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" openMember StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" openMember response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("openMember request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);

			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
		return true;
	}





	/**
	 * 旺铺汰换接口
	 *
	 * @param memberKey
	 * @param accountStatus
	 * @return
	 */
	public Boolean closeMember(String memberKey, Integer accountStatus,String offTime) {
		log.info("start close member,memberKey:{}",memberKey);
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String,String> obj = new HashMap<>();
		obj.put("memberKey", memberKey);
		obj.put("deadStatus", "1");
		obj.put("accountStatus",accountStatus.toString());
		obj.put("quitDate",offTime);
		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(CLOSE_MEMBER);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" closeMember StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" closeMember response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("closeMember request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);

			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
		return true;
	}



	/**
	 * 修改省市区岗位
	 * @param memberKey
	 * @param areaCode
	 * @param area
	 * @param companyCode
	 * @param company
	 * @param branchCode
	 * @param branch
	 * @param province
	 * @param city
	 * @param district
	 * @param street
	 * @return
	 */
	public boolean sendUpdateAddressByKeyToMEMBER(String memberKey, String areaCode, String area,String companyCode, String company,String branchCode, String branch, String province, String city, String district, String street) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String, String> obj = new HashMap<>();
		obj.put("memberKey", memberKey);
		obj.put("areaCode", areaCode);
		obj.put("area", area);
		obj.put("companyCode", companyCode);
		obj.put("company", company);
		obj.put("branchCode", branchCode);
		obj.put("branch", branch);
		obj.put("province", province);
		obj.put("city", city);
		obj.put("district", district);
		obj.put("street", street);
		obj.put("ex1","0");
		obj.put("ex2","0");
		obj.put("processDate",LocalDateTimeUtils.formatTime(LocalDateTime.now(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));

		if("TS".equals(areaCode)){
			obj.put("businessCode","4");
		}else{
			obj.put("businessCode","1");
		}



		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(SAVE_OR_UPDATE_CEO_INFO_URL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);

			log.info("sendUpdateAddressByKeyToMEMBER response: {}", response);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用接root口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用root接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用root接口异常!");
		}
		return flag;
	}

	/**
	 * 修改省市区岗位
	 *
	 * @param memberKey memberKey
	 * @param areaCode 大区code
	 * @param area 大区名称
	 * @param companyCode 分公司code
	 * @param company 分公司名称
	 * @param branchCode 组织code
	 * @param branch 组织名称
	 * @param province 省
	 * @param city 市
	 * @param district 区
	 * @param street 街道
	 * @return
	 */
	public boolean sendUpdateAddressByKeyToMEMBERFor123(String memberKey, String areaCode, String area,String companyCode, String company,String branchCode, String branch, String province, String city, String district, String street) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String, String> obj = new HashMap<>();
		obj.put("memberKey", memberKey);
		obj.put("areaCode", areaCode);
		obj.put("area", area);
		obj.put("companyCode", companyCode);
		obj.put("company", company);
		obj.put("branchCode", branchCode);
		obj.put("branch", branch);
		obj.put("province", province);
		obj.put("city", city);
		obj.put("district", district);
		obj.put("street", street);
		obj.put("ex1","0");
		obj.put("ex2","0");
		obj.put("businessCode","3");
		obj.put("processDate",LocalDateTimeUtils.formatTime(LocalDateTime.now(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));


		try {
			requestStr = mapper.writeValueAsString(obj);
			httpPost = new HttpPost(SAVE_OR_UPDATE_CEO_INFO_URL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);

			log.info("sendUpdateAddressByKeyToMEMBER response: {}", response);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用接root口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用root接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用root接口异常!");
		}
		return flag;
	}


	/**
	 * B端用户禁用接口
	 * @param ceoKey
	 * @return
	 */
    public boolean sendDisableCeoAreaInfo(Integer ceoKey) {
    	boolean flag = false;
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
//		boolean result = false;
        Map<String, Object> obj = new HashMap<>();
//		Map<String, Object> responseValue  = new HashMap<>();
        obj.put("ceoKey", ceoKey);

        try {
            requestStr = mapper.writeValueAsString(obj);
            httpPost = new HttpPost(DISABLE_CEO_AREA_INFO_URL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);

            log.info(""+response);
            
            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                if ("success".contentEquals(jsonObject.get("message").getAsString() )  && jsonObject.get("data").getAsBoolean() == true ) {
                	flag = true;
                } else {
                	log.error("内部调用 B端用户禁用接口 接口异常,接口状态:" + jsonObject);
                    throw new ApplicationException("内部调用 B端用户禁用接口 接口异常,接口状态:" + jsonObject);
                }
            } else {
                throw new ApplicationException("内部调用 B端用户禁用接口接口 异常:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("内部调用 B端用户禁用接口 接口异常!");
        }
        return flag;
    }
    
	/**
	 * 修改客户类型
	 * @param ceoKey
	 * @param storeChannel 客户类型
	 * @return
	 */
    public boolean updateStoreChannel(String ceoKey,String storeChannel) {
    	boolean flag = false;
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;
//		boolean result = false;
        Map<String, Object> obj = new HashMap<>();
//		Map<String, Object> responseValue  = new HashMap<>();
        obj.put("ceoKey", ceoKey);
        obj.put("storeChannel", storeChannel);

        try {
            requestStr = mapper.writeValueAsString(obj);
            httpPost = new HttpPost(UPDATE_CEO_STORECHANNEL);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
            // 发送请求
            response = httpClient.execute(httpPost);

            log.info(""+response);
            
            if (response.getStatusLine().getStatusCode() == 200) {
                // 解析应答
                entity = response.getEntity();
                responseString = EntityUtils.toString(entity, "UTF-8");
                JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
                if ("success".contentEquals(jsonObject.get("message").getAsString() )  && jsonObject.get("data").getAsBoolean() == true ) {
                	flag = true;
                } else {
                	log.error("内部调用 修改客户接口异常,接口状态:" + jsonObject);
                    throw new ApplicationException("内部调用 修改客户接口异常,接口状态:" + jsonObject);
                }
            } else {
            	log.error("内部调用 修改客户接口异常 异常:" + response.getStatusLine().getStatusCode());
                throw new ApplicationException("内部调用 修改客户接口异常 异常:" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("内部调用 修改客户接口异常 接口异常!");
        }
        return flag;
    }

	/**
	 * 同步地址到旺铺
	 *
	 * @return
	 */
	public void synAddress(List<SynAddressModel> model){

		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;


		try {
			requestStr = mapper.writeValueAsString(model);
			log.info("同步地址到旺铺:"+requestStr);
			httpPost = new HttpPost(SYN_ADDRESS);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);

			log.info(""+response);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				entity = response.getEntity();
				log.info("同步地址到旺铺响应:"+requestStr);
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if ("0".contentEquals(jsonObject.get("code").getAsString() )) {
					flag = true;
				} else {
					log.error("内部调用 同步客户地址 调用异常:" + jsonObject);
					throw new ApplicationException("内部调用 同步客户地址 调用异常:" + jsonObject);
				}
			} else {
				log.error("内部调用 同步客户地址 调用异常:" + response.getStatusLine().getStatusCode());
				throw new ApplicationException("内部调用 修改客户接口异常 异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用 同步客户地址 接口异常!");
		}
	}

	public boolean sendUpdateAddressByKeyToMEMBERForBh(SaveOrUpdateCeoDto saveOrUpdateCeoDto) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;


		try {
			requestStr = mapper.writeValueAsString(saveOrUpdateCeoDto);
			log.info("sendUpdateAddressByKeyToMEMBER request:{}",requestStr);
			httpPost = new HttpPost(SAVE_OR_UPDATE_CEO_INFO_URL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);

			log.info("sendUpdateAddressByKeyToMEMBER response: {}", response);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用接root口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用root接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用root接口异常!");
		}
		return flag;
	}


	public boolean updateEmployeeInfo(String memberKey, String imageName, String name, String dob,String gender,String workDate,String partnerCompanyName) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
		Map<String, String> obj = new HashMap<>();
		obj.put("key",memberKey);
		if(StringUtils.isNotBlank(memberKey)){
			obj.put("imageName",imageName);
		}

		if(StringUtils.isNotBlank(name)){
			obj.put("name",name);
		}

		if(StringUtils.isNotBlank(dob)){
			obj.put("dob",dob);
		}

		if(StringUtils.isNotBlank(gender)){
			obj.put("gender",gender);
		}

		if(StringUtils.isNotBlank(workDate)){
			obj.put("workDate",workDate);
		}

		if(StringUtils.isNotBlank(partnerCompanyName)){
			obj.put("companyName",partnerCompanyName);
		}


		try {
			requestStr = mapper.writeValueAsString(obj);
			log.info("updateEmployeeInfo request:{}",requestStr);
			httpPost = new HttpPost(UPDATE_EMPLOYEE_INFO);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);

			log.info("updateEmployeeInfo response: {}", response);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用接root口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用root接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用root接口异常!");
		}
		return flag;
	}

	public boolean customerAudit(CustomerAuditRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		//HttpResponse response = null;
		//HttpEntity entity = null;
		String responseString = null;
		//请求参数
		Map<String,String> obj= JSON.parseObject(JSON.toJSONString(request), Map.class);
		JsonObject jsonObject = null;
		try {
			requestStr = mapper.writeValueAsString(obj);
			log.info("customerAudit request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(CUSTOMER_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("customerAudit response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				jsonObject = gson.fromJson(responseString, JsonObject.class);
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		if(jsonObject != null) {
			if (jsonObject.get("code").getAsInt() == 0) {
				flag = true;
			} else {
				String msg = "内部调用ceo-customer接口异常";
				if(jsonObject.get("msg") != null && !jsonObject.get("msg").isJsonNull()) {
					msg = jsonObject.get("msg").getAsString();
				}
				throw new ApplicationException(msg);
			}
		}
		return flag;
	}

	public boolean displayCustomerAudit(DisplayCustomerAuditRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		//HttpResponse response = null;
		//HttpEntity entity = null;
		String responseString = null;
		//请求参数
		Map<String,String> obj= JSON.parseObject(JSON.toJSONString(request), Map.class);
		JsonObject jsonObject = null;
		try {
			requestStr = mapper.writeValueAsString(obj);
			log.info("customerAudit request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(DISPLAY_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("customerAudit response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				jsonObject = gson.fromJson(responseString, JsonObject.class);
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		if(jsonObject != null) {
			if (jsonObject.get("code").getAsInt() == 0) {
				flag = true;
			} else {
				String msg = "内部调用ceo-customer接口异常";
				if(jsonObject.get("msg") != null && !jsonObject.get("msg").isJsonNull()) {
					msg = jsonObject.get("msg").getAsString();
				}
				throw new ApplicationException(msg);
			}
		}
		return flag;
	}

	public boolean leaveAudit(Map map) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		String responseString = null;
		//请求参数
		JsonObject jsonObject = null;
		try {
			requestStr = mapper.writeValueAsString(map);
			log.info("leaveAudit request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(LEAVE_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("leaveAudit response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				jsonObject = gson.fromJson(responseString, JsonObject.class);
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		if(jsonObject != null) {
			if (jsonObject.get("code").getAsInt() == 0) {
				flag = true;
			} else {
				String msg = "内部调用ceo-customer接口异常";
				if(jsonObject.get("msg") != null && !jsonObject.get("msg").isJsonNull()) {
					msg = jsonObject.get("msg").getAsString();
				}
				throw new ApplicationException(msg);
			}
		}
		return flag;
	}

	public boolean attendanceAudit(Map map) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		String responseString = null;
		//请求参数
		JsonObject jsonObject = null;
		try {
			requestStr = mapper.writeValueAsString(map);
			log.info("attendanceAudit request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(ATTENDANCE_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("leaveAudit response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				jsonObject = gson.fromJson(responseString, JsonObject.class);
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		if(jsonObject != null) {
			if (jsonObject.get("code").getAsInt() == 0) {
				flag = true;
			} else {
				String msg = "内部调用ceo-customer接口异常";
				if(jsonObject.get("msg") != null && !jsonObject.get("msg").isJsonNull()) {
					msg = jsonObject.get("msg").getAsString();
				}
				throw new ApplicationException(msg);
			}
		}
		return flag;
	}


	public boolean customerTransfer(String memberKey, String customerId) {
		boolean flag;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		String responseString = null;
		//请求参数
		Map<String,String> obj= new HashMap<>();
		obj.put("memberKey", memberKey);
		obj.put("customerId", customerId);
		try {
			requestStr = mapper.writeValueAsString(obj);
			log.info("customerTransfer request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(CUSTOMER_TRANSFER);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("customerTransfer response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-customer接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		return flag;
	}

	public boolean displayCustomerTransfer(String memberKey, String customerId,String operator) {
		boolean flag;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		String responseString = null;
		//请求参数
		Map<String,String> obj= new HashMap<>();
		obj.put("partnerMemberKey", memberKey);
		obj.put("customerId", customerId);
		obj.put("operator", operator);
		try {
			requestStr = mapper.writeValueAsString(obj);
			log.info("customerTransfer request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(DISPLAY_TRANSFER);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("customerTransfer response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-customer接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		return flag;
	}

    public Boolean businessAudit(BusinessAuditRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		//请求参数
		Map<String,String> obj= JSON.parseObject(JSON.toJSONString(request), Map.class);
		try {
			String requestStr = mapper.writeValueAsString(obj);
			log.info("businessAudit request:{}",requestStr);
			HttpPost httpPost = new HttpPost(BUSINESS_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			log.info("businessAudit response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else if (jsonObject.get("code").getAsInt() == 400){
					throw new ApplicationException(jsonObject.get("msg").getAsString());
				}
				else {
					throw new ApplicationException("内部调用ceo-customer接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		return flag;
    }

    /**
     * 修改分公司分类同步旺铺
	 *
     * @param list
     * @return: java.lang.Boolean
     * @date: 4/19/22 11:51 AM
     */
	public Boolean batchCompany(List<CompanyClassBatchDTO> list) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(list);
			log.info("batchCompany request:{}",requestStr);
			HttpPost httpPost = new HttpPost(BATCH_COMPANY);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("batchCompany response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else if (jsonObject.get("code").getAsInt() == 400){
					throw new ApplicationException(jsonObject.get("msg").getAsString());
				}
				else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}
		return flag;
	}

	/**
	 * 授权失效同步旺铺
	 *
	 * @param request
	 * @return: java.lang.Boolean
	 * @date: 5/12/22 5:28 PM
	 */
	public Boolean overdueAuthorization(CustomerAuthorizationRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("overdueAuthorization request:{}",requestStr);
			HttpPost httpPost = new HttpPost(OVERDUE_AUTHORIZATION);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("batchCompany response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-customer接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		return flag;
	}

	/**
	 * 陈列审批结果调用旺铺
	 *
	 * @param request
	 * @return: java.lang.Boolean
	 * @date: 5/20/22 9:52 PM
	 */
	public Boolean displayActivityFindings(ActivityFindingsRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("displayActivityFindings request:{}",requestStr);
			HttpPost httpPost = new HttpPost(DISPLAY_ACTIVITY);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("displayActivityFindings response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-customer接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		return flag;
	}

	/**
	 * 合伙人审核通知旺铺
	 *
	 * @param request
	 * @return: java.lang.Boolean
	 * @date: 6/10/22 9:52 PM
	 */
	public Boolean memberFindings(MemberFindingsRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("memberFindings request:{}",requestStr);
			HttpPost httpPost = new HttpPost(MEMBER_ADDRESS);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("memberFindings response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}
		return flag;
	}

	/**
	 * 企业合伙人审核通知旺铺
	 *
	 * @param request
	 * @return: java.lang.Boolean
	 * @date: 6/10/22 9:52 PM
	 */
	public Boolean partnerAudit(PartnerAuditRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("partnerAudit request:{}",requestStr);
			HttpPost httpPost = new HttpPost(PARTNER_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("partnerAudit response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}
		return flag;
	}

	public Boolean auditCertification(AuditStatusRequest request){
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("auditCertification request:{}",requestStr);
			HttpPost httpPost = new HttpPost(Audit_Certification);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("auditCertification response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}
		return flag;
	}


	/**
	 * 售后审批结果调用旺铺
	 * 调用旺铺:张浩然
	 * swagger:
	 * http://115.159.49.184:9160/doc.html?wework_cfm_code=ORw6nmQI9pd81eswl7725jR2GE%2BtdltuFkzKLrkOTPXbD1GyN6hfEslfBAtbGT6mWdG7KXCuQXADoP08dkiVdvNprYhbTIg1%2FA%3D%3D#/ceo-order-service/%E4%B8%B4%E6%9C%9F%E5%94%AE%E5%90%8E%E6%8E%A5%E5%8F%A3/batchSaleAfterSfaCheckUsingPOST
	 *
	 * @param request
	 * @return: java.lang.Boolean
	 * @date: 11/23/22 9:52 PM
	 */
	public Boolean afterSalesFindings(FindingsRequest request) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("afterSalesFindings request:{}",requestStr);
			HttpPost httpPost = new HttpPost(AFTER_SALES_FINDING);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("afterSalesFindings response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
					flag = true;
				} else {
					throw new ApplicationException("内部调用ceo-backend接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-backend接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-backend接口异常!");
		}
		return flag;
	}

	/**
	 * 额度查询调用旺铺实时查询(废弃)
	 * 调用旺铺:张浩然
	 * swagger:http://115.159.49.184:9160/doc.html?wework_cfm_code=ORw6nmQI9pd81eswl7725jR2GE%2BtdltuFkzKLrkOTPXbD1GyN6hfEslfBAtbGT6mWdG7KXCuQXADoP08dkiVdvNprYhbTIg1%2FA%3D%3D#/ceo-order-service/%E4%B8%B4%E6%9C%9F%E5%94%AE%E5%90%8E%E6%8E%A5%E5%8F%A3/queryBatchLimitForSfaUsingPOST
	 *
	 * @param applicationNo
	 * @return: com.wantwant.sfa.backend.afterSales.vo.QuotaVO
	 * @date: 11/24/22 5:32 PM
	 */
	public List<QuotaVO> queryQuota(String applicationNo) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			Map<String,String> map = Maps.newHashMap();
			map.put("afterSaleNo", applicationNo);
			map.put("memberKey", "0");
			String requestStr = mapper.writeValueAsString(map);
			log.info("afterSalesQueryQuota request:{}",requestStr);
			HttpPost httpPost = new HttpPost(AFTER_SALES_QUERY_QUOTA);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("afterSalesQueryQuota response: {}", responseString);

				Response<List<QuotaVO>> quotaVOResponse = JSONObject.parseObject(responseString, new TypeReference<Response<List<QuotaVO>>>(){});

				if (quotaVOResponse.getCode() == 0) {
					return quotaVOResponse.getData();
				} else {
					throw new ApplicationException("内部调用ceo-backend接口异常,jsonObject:{}" + quotaVOResponse.toString());
				}
			} else {
				throw new ApplicationException("内部调用ceo-backend接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-backend接口异常!");
		}

	}

	/**
	 * 调用sapaddOrder接口
	 *
	 * @param orderHeaderList
	 * @return: java.util.List<com.wantwant.sfa.backend.sap.dto.SapResVoListDTO>
	 * @date: 12/6/22 2:52 PM
	 */
	public List<SapResVoListDTO> sapSubmit(List<SapMergeOrderHeaderDTO> orderHeaderList) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			Map<String,List<SapMergeOrderHeaderDTO>> map = Maps.newHashMap();
			map.put("orderVoList", orderHeaderList);
			String requestStr = mapper.writeValueAsString(map);
			log.info("sapOrder request:{}",requestStr);
			HttpPost httpPost = new HttpPost(SAP_ADD_ORDER);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setHeader("Authorization", "Basic YWRtaW46WVdSdGFXNDZjbTl2ZEE9PQ==");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("sapOrder response: {}", responseString);

				SapResponseDTO sapResponseDTO = JSONObject.parseObject(responseString, new TypeReference<SapResponseDTO>(){});
				log.info("sapOrder SapResponseDTO: {}", sapResponseDTO);
				if (Objects.isNull(sapResponseDTO) || CommonUtil.ListUtils.isEmpty(sapResponseDTO.getSapResVoList())){
					throw new ApplicationException("内部调用sap系统/addOrder接口异常:" + sapResponseDTO.toString());
				}
				return sapResponseDTO.getSapResVoList();
			} else {
				throw new ApplicationException("内部调用sap系统/addOrder接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用sap系统/addOrder接口异常!");
		}
	}

	/**
	 * 人员异常推送
	 * 调用旺铺:徐腾
	 * swagger:http://115.159.49.184:9180/doc.html?wework_cfm_code=ORw6nmQI9pd8oypVmmjZ3lhaaobBa998Wsv0zAv9%2BVyVL4Jpww3eAIsQ44Pu8bEeniC4jivJHhBEa6dBQdA9eAARX0cchUbu8A%3D%3D#/root-ceo-customer-service/%E9%99%88%E5%88%97%E6%B4%BB%E5%8A%A8%E7%9B%B8%E5%85%B3API/sfaPushAbnormalUsingPOST
	 *
	 * @param request
	 * @date: 12/08/22 11:32 AM
	 */
	public void pushAbnormal(AbnormalRequest request) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("displayPushAbnormal request:{}",requestStr);
			HttpPost httpPost = new HttpPost(DISPLAY_PUSH_ABNORMAL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("displayPushAbnormal response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {

				} else {
					throw new ApplicationException("内部调用ceo-customer接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
	}

	/**
	 * 用户反馈回复接口
	 * 调用旺铺:崔博文
	 * swagger:http://115.159.49.184:9180/doc.html#/root-ceo-member-service/%E7%94%A8%E6%88%B7%E5%8F%8D%E9%A6%88%E8%8A%82%E7%82%B9%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/insertNodeUsingPOST
	 *
	 * @param request
	 * @return: void
	 * @date: 1/4/23 5:30 PM
	 */
    public void auditFeedbackInfo(AuditFeedbackRequest request) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("auditFeedbackInfo request:{}",requestStr);
			HttpPost httpPost = new HttpPost(FEEDBACK_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("auditFeedbackInfo response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
				} else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}
    }

    /**
     * 问题反馈新增节点
	 * 调用旺铺:崔博文
	 * swagger:http://115.159.49.184:9180/doc.html#/root-ceo-member-service/用户反馈节点相关接口/insertNodeUsingPOST
	 *
     * @param nodeRequest
     * @return: void
     * @date: 12/8/23 2:24 PM
     */
	public void pushFeedBackNode(PushNodeRequest nodeRequest) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(nodeRequest);
			log.info("pushFeedBackNode request:{}",requestStr);
			HttpPost httpPost = new HttpPost(FEEDBACK_NODE);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("pushFeedBackNode response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
				} else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}
	}

    /**
     * 问题反馈点赞同步旺铺
	 * 调用旺铺:崔博文
	 *
     * @param request
     * @return: void
     * @date: 3/8/23 1:55 PM
     */
    public void pushFeedbackLike(LikeRequest request){
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("pushFeedbackLike request:{}",requestStr);
			HttpPost httpPost = new HttpPost(FEEDBACK_LIKE);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("pushFeedbackLike response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() == 0) {
				} else {
					throw new ApplicationException("内部调用ceo-member接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-member接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-member接口异常!");
		}

	}

	/**
	 * 陈列活动费用信息审核通知接口
	 * 调用旺铺:王苏斌
	 * http://115.159.49.184:9160/doc.html#/ceo-commodity-service/%E9%99%88%E5%88%97%E6%B4%BB%E5%8A%A8%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/auditActDisplayAmountUsingPOST
	 *
	 * @param request
	 * @return: void
	 * @date: 5/9/23 10:33 AM
	 */
    public void auditActDisplayAmount(RuleSaveRequest request) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("auditActDisplayAmount request:{}",requestStr);
			HttpPost httpPost = new HttpPost(DISPLAY_RULE_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("auditActDisplayAmount response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() != 0) {
					throw new ApplicationException("内部调用ceo-commodity-service接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-commodity-service接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-commodity-service接口异常!");
		}
    }

    /** 
     * 陈列活动的陈列标准禁用通知接口
	 * 调用旺铺:王苏斌
	 * http://115.159.49.184:9160/doc.html#/ceo-commodity-service/%E9%99%88%E5%88%97%E6%B4%BB%E5%8A%A8%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/disableActDisplayStandardUsingPOST
	 *
     */
	public void disableActDisplayStandard(RuleDisableRequest request) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(request);
			log.info("disableActDisplayStandard request:{}",requestStr);
			HttpPost httpPost = new HttpPost(DISPLAY_RULE_DISABLE);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("disableActDisplayStandard response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() != 0) {
					throw new ApplicationException("内部调用ceo-commodity-service接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("内部调用ceo-commodity-service接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-commodity-service接口异常!");
		}
	}

	public void pushMainProduct(TargetMainProductRequest request) {
		List<TargetMainProductRequest> list = new ArrayList<>();
		list.add(request);
		Map<String, List<TargetMainProductRequest>> map = new HashMap<>();
		map.put("list",list);

		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(map);
			log.info("pushMainProductGroup request:{}",requestStr);
			HttpPost httpPost = new HttpPost(PUSH_MAIN_PRODUCT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("pushMainProductGroup response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() != 0) {
					throw new ApplicationException("同步旺铺主推品接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("同步旺铺主推品接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("同步旺铺主推品接口异常!");
		}
	}

	public void pushWpMainProductSku(MainProductSkuPushWpRequest request) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		try {
			String requestStr = JSON.toJSONString(request);
			log.info("pushWpMainProduct request:{}",requestStr);
			if (StringUtils.isEmpty(PUSH_WP_MAIN_PRODUCT_SKU)){
				throw new ApplicationException("推送旺铺地址未配置");
			}
			HttpPost httpPost = new HttpPost(PUSH_WP_MAIN_PRODUCT_SKU);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);
			if (response.getStatusLine().getStatusCode() == 200) {
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("pushWpMainProduct response: {}", responseString);
				JsonObject jsonObject = gson.fromJson(responseString, JsonObject.class);
				if (jsonObject.get("code").getAsInt() != 0) {
					throw new ApplicationException("同步旺铺主推品接口异常,jsonObject:{}" + jsonObject);
				}
			} else {
				throw new ApplicationException("同步旺铺主推品接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("同步旺铺主推品接口异常!");
		}
	}

	public boolean spuTaskRebaseRules(RebaseRulePushWPDTO rulePushWPDTO) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		String responseString = null;
		//请求参数
		JsonObject jsonObject = null;
		try {
			requestStr = mapper.writeValueAsString(rulePushWPDTO);
			log.info("spuTaskRebaseRules request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(SPU_REBASE_RULES);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("spuTaskRebaseRules response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				jsonObject = gson.fromJson(responseString, JsonObject.class);
			} else {
				throw new ApplicationException("内部调用backend-ceo-marketing接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用backend-ceo-marketing接口异常!");
		}
		if(jsonObject != null) {
			if (jsonObject.get("code").getAsInt() == 0) {
				flag = true;
			} else {
				String msg = "内部调用backend-ceo-marketing接口异常";
				if(jsonObject.get("msg") != null && !jsonObject.get("msg").isJsonNull()) {
					msg = jsonObject.get("msg").getAsString();
				}
				throw new ApplicationException(msg);
			}
		}
		return flag;
	}

	public boolean visitAudit(Map map) {
		boolean flag = false;
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();

		String requestStr = null;
		HttpPost httpPost = null;
		String responseString = null;
		//请求参数
		JsonObject jsonObject = null;
		try {
			requestStr = mapper.writeValueAsString(map);
			log.info("visitAudit request:{}",requestStr);
			//设置请求头和报文
			httpPost = new HttpPost(VISIT_AUDIT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			// 发送请求
			HttpResponse response = httpClient.execute(httpPost);
			log.info("visitAudit response: {}", response);
			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				responseString = EntityUtils.toString(entity, "UTF-8");
				jsonObject = gson.fromJson(responseString, JsonObject.class);
			} else {
				throw new ApplicationException("内部调用ceo-customer接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-customer接口异常!");
		}
		if(jsonObject != null) {
			if (jsonObject.get("code").getAsInt() == 0) {
				flag = true;
			} else {
				String msg = "内部调用ceo-customer接口异常";
				if(jsonObject.get("msg") != null && !jsonObject.get("msg").isJsonNull()) {
					msg = jsonObject.get("msg").getAsString();
				}
				throw new ApplicationException(msg);
			}
		}
		return flag;
	}

	/**
	 * 额度查询调用旺铺实时查询
	 * 调用旺铺:张浩然
	 * http://115.159.49.184:9160/doc.html?wework_cfm_code=ORw6nmQI9pd81eswl7725jR2GE%2BtdltuFkzKLrkOTPXbD1GyN6hfEslfBAtbGT6mWdG7KXCuQXADoP08dkiVdvNprYhbTIg1%2FA%3D%3D#/ceo-order-service/%E4%B8%B4%E6%9C%9F%E5%94%AE%E5%90%8E%E6%8E%A5%E5%8F%A3/queryBatchLimitForSfaUsingPOST
	 */
	public List<AfterSalesEmpQuotaVO> queryBatchLimitForSfa(Map<String, String> map) {
		HttpClient httpClient = HttpClientBuilder.create().build(); //创建http客户端
		ObjectMapper mapper = new ObjectMapper();
		try {
			String requestStr = mapper.writeValueAsString(map);
			log.info("queryBatchLimitForSfa request:{}",requestStr);
			HttpPost httpPost = new HttpPost(AFTER_SALES_QUERY_QUOTA);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr, "UTF-8"));
			HttpResponse response = httpClient.execute(httpPost);

			if (response.getStatusLine().getStatusCode() == 200) {
				// 解析应答
				HttpEntity entity = response.getEntity();
				String responseString = EntityUtils.toString(entity, "UTF-8");
				log.info("queryBatchLimitForSfa response: {}", responseString);

				Response<List<AfterSalesEmpQuotaVO>> quotaVOResponse = JSONObject.parseObject(responseString, new TypeReference<Response<List<AfterSalesEmpQuotaVO>>>(){});

				if (quotaVOResponse.getCode() == 0) {
					return quotaVOResponse.getData();
				} else {
					throw new ApplicationException("内部调用ceo-backend接口异常,jsonObject:{}" + quotaVOResponse.toString());
				}
			} else {
				throw new ApplicationException("内部调用ceo-backend接口异常:" + response.getStatusLine().getStatusCode());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new IllegalStateException("内部调用ceo-backend接口异常!");
		}
	}

	public Long openAccountopenShoppingGuideBDAccount(OpenShoppingGuideBDAccountModel model){
		log.info("start ROOTConnectorUtil openAccountopenShoppingGuideBDAccount model:{}",JSONObject.toJSONString(model));
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;


		try {
			requestStr = mapper.writeValueAsString(model);
			httpPost = new HttpPost(OPEN_SHOPPING_GUIDE_ACCOUNT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info(" openAccountopenShoppingGuideBDAccount StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info(" response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info("openAccountopenShoppingGuideBDAccount request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
			log.info("openAccountopenShoppingGuideBDAccount response: {}", responseValue);
			String error = (String) responseValue.get("error");
			log.info("openAccountopenShoppingGuideBDAccount() error request: {}", error);
			if (error != null) {
				throw new IllegalStateException(error);
			}
			if((Integer)responseValue.get("code")!=0){
				throw new ApplicationException((String) responseValue.get("msg"));
			}
			Object memberKeyObj = responseValue.get("data");
			if(Objects.isNull(memberKeyObj)){
				throw new ApplicationException("获取数据失败");
			}

			return convertToLong(memberKeyObj);
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new ApplicationException(e.getMessage());
		}
	}

	public void updateOpenAccountopenShoppingGuideBDAccount(UpdateShoppingGuideBDAccountModel model){
		log.info("start updateOpenAccountopenShoppingGuideBDAccount,request:{}",JSONObject.toJSONString(model));


		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;

		try {
			requestStr = mapper.writeValueAsString(model);
			httpPost = new HttpPost(UPDATE_SHOPPING_GUIDE_ACCOUNT);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			log.info("  update account StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info("  update account response StatusCode:{}",response.getStatusLine().getStatusCode());
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
			log.info(" update account request: {}", responseString);
			Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

			String error = (String) responseValue.get("error");

			if (error != null) {
				throw new IllegalStateException(error);

			}
		}catch (Exception e) {
			log.info(e.getMessage(),e);
			throw new IllegalStateException(e.getMessage(),e);
		}
	}

	/**
	 * 安全地将Object转换为Long
	 * 处理从Map中获取数值时可能是Integer或Long的情况
	 * @param obj 要转换的对象
	 * @return Long值
	 * @throws ApplicationException 如果转换失败
	 */
	private Long convertToLong(Object obj) {
		if (obj == null) {
			return null;
		}
		
		if (obj instanceof Long) {
			return (Long) obj;
		} else if (obj instanceof Integer) {
			return ((Integer) obj).longValue();
		} else if (obj instanceof String) {
			try {
				return Long.parseLong((String) obj);
			} catch (NumberFormatException e) {
				throw new ApplicationException("无法将字符串转换为Long: " + obj);
			}
		} else {
			throw new ApplicationException("无法将类型 " + obj.getClass().getSimpleName() + " 转换为Long: " + obj);
		}
	}


}
