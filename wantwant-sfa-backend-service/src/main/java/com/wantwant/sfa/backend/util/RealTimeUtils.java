package com.wantwant.sfa.backend.util;

import cn.hutool.core.img.ColorUtil;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.mapper.RealtimeMapper;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.awt.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.util
 * @Description: 实时数据
 * @Date: 2024/6/19 10:13
 */
@Component
@Slf4j
public class RealTimeUtils {

    public final static String FISCAL_YEAR = "2";

    public final static String QUARTER = "11";

    public final static String MONTHS = "10";

    public final static Color RED = new Color(217, 0, 27);
    public final static Color GREEN = new Color(104,166,3);
    public final static Color GRAY = new Color(85,85,85);
    public final static Color ORANGE = new Color(245,154,35);
    public final static Color BLUE = new Color(2,167,240);
    public static final BigDecimal SEVENTY = new BigDecimal("70");
    public static final BigDecimal EIGHTY = new BigDecimal("80");
    public static final BigDecimal ONE_HUNDRED_TWENTY_FIVE = new BigDecimal("125");
    private static final BigDecimal HUNDRED = new BigDecimal(100);

    @Resource
    private RealtimeMapper realtimeMapper;
    @Autowired
    private SettingServiceImpl settingServiceImpl;

    /**
     * 标色逻辑
     * Color.RED 红色:(目标达成率/时间进度)<70%
     * Color.ORANGE 橙色:(目标达成率/时间进度)区间在70.01%~80%
     * Color.GRAY 灰色:(目标达成率/时间进度)区间在80.01~124.99%
     * Color.GREEN 绿色:(目标达成率/时间进度)>125%
     * @param dateTypeId
     * @param yearMonth
     * @param performanceAchievementRate
     * @return
     */
    public String saleGoalAchievementColor(String dateTypeId, String yearMonth, BigDecimal performanceAchievementRate, boolean isHomePage){
        Color color= Color.RED;
        if(Objects.isNull(performanceAchievementRate) || BigDecimal.ZERO.compareTo(performanceAchievementRate) > 0){
            return null;
        }
        LocalDate now = LocalDate.now();
        //yyyy-MM-dd
        String nowDate = now.toString();
        //判断时间是否是当前时间
        if("2".equals(dateTypeId)){
            //财年是4月到3月
            int month = now.getMonthValue();
            int year = now.getYear(); // 获取当前年份
            int fiscalYear = month<4?year-1:year;
            if(fiscalYear== Integer.valueOf(yearMonth)){
                //使用毫秒数计算
                LocalDate firstDay = LocalDate.of(fiscalYear,4,1);
                LocalDate lastDay = LocalDate.of(fiscalYear+1,3,31);
                //时间进度
                Long dayOfYear = now.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - firstDay.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                Long numberOfDaysInYear = lastDay.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - firstDay.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

                BigDecimal processRate = new BigDecimal(dayOfYear).divide(new BigDecimal(numberOfDaysInYear),4,RoundingMode.HALF_UP);
                color = getSaleGoalAchievementColor(performanceAchievementRate,processRate,isHomePage);
            }else {
                color = getSaleGoalAchievementColor(performanceAchievementRate,BigDecimal.ONE,isHomePage);
            }
        }else if("10".equals(dateTypeId)){
            if(nowDate.contains(yearMonth)){
                //时间进度
                int dayOfMonth = now.getDayOfMonth();
                int lengthOfMonth = now.lengthOfMonth();
                BigDecimal processRate = new BigDecimal(dayOfMonth).divide(new BigDecimal(lengthOfMonth),4,RoundingMode.HALF_UP);
                color = getSaleGoalAchievementColor(performanceAchievementRate,processRate,isHomePage);
            }else {
                color = getSaleGoalAchievementColor(performanceAchievementRate,BigDecimal.ONE,isHomePage);
            }
        }else if("11".equals(dateTypeId)){
            //转换当前季度
            int year = now.getYear();
            int quarter = getQuarter(now);
            String nowYearMonth = year+"-Q"+quarter;
            if(nowYearMonth.equals(yearMonth)){
                //时间进度
                int dayOfQuarter = getDaysOfQuarter(now);
                int lengthOfQuarter = getDaysInQuarter(year,quarter);
                BigDecimal processRate = new BigDecimal(dayOfQuarter).divide(new BigDecimal(lengthOfQuarter),4,RoundingMode.HALF_UP);
                color = getSaleGoalAchievementColor(performanceAchievementRate,processRate,isHomePage);
            } else {
                color = getSaleGoalAchievementColor(performanceAchievementRate,BigDecimal.ONE,isHomePage);
            }
        }else if("9".equals(dateTypeId)) {
            if (yearMonth.equals(settingServiceImpl.getValue(DictCodeConstants.YEAR_FESTIVAL_CURRENT))) {
                String yearFestivalDate = settingServiceImpl.getValue(DictCodeConstants.YEAR_FESTIVAL_DATE);
                if (StringUtils.isNotBlank(yearFestivalDate)) {
                    try {
                        String[] splitDate = yearFestivalDate.split(",");
                        LocalDate startDate = LocalDate.parse(splitDate[0], DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd));
                        LocalDate endDate = LocalDate.parse(splitDate[1], DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd));
                        if (!now.isBefore(startDate) && !now.isAfter(endDate)) {
                            long dayOfYearFestival = ChronoUnit.DAYS.between(startDate, now) + 1;
                            long lengthOfYearFestival = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                            BigDecimal processRate = new BigDecimal(dayOfYearFestival).divide(new BigDecimal(lengthOfYearFestival), 4, RoundingMode.HALF_UP);
                            color = getSaleGoalAchievementColor(performanceAchievementRate, processRate, isHomePage);
                        } else {
                            color = getSaleGoalAchievementColor(performanceAchievementRate, BigDecimal.ONE, isHomePage);
                        }
                    } catch (Exception e) {
                        color = getSaleGoalAchievementColor(performanceAchievementRate, BigDecimal.ONE, isHomePage);
                    }
                } else {
                    color = getSaleGoalAchievementColor(performanceAchievementRate, BigDecimal.ONE, isHomePage);
                }
            } else {
                color = getSaleGoalAchievementColor(performanceAchievementRate, BigDecimal.ONE, isHomePage);
            }
        }


        return ColorUtil.toHex(color);
    }

    public String saleGoalAchievementColor(String dateTypeId, String yearMonth, BigDecimal performanceAchievementRate){
        return saleGoalAchievementColor(dateTypeId,yearMonth,performanceAchievementRate,false);
    }
    /**
     * 标色逻辑
     * Color.RED 红色:(目标达成率/时间进度)<70%
     * Color.ORANGE 橙色:(目标达成率/时间进度)区间在70.01%~80%
     * Color.GRAY 灰色:(目标达成率/时间进度)区间在80.01~124.99%
     * Color.GREEN 绿色:(目标达成率/时间进度)>125%
     * @param performanceAchievementRate
     * @param processRate
     * @return
     */
    private Color getSaleGoalAchievementColor(BigDecimal performanceAchievementRate,BigDecimal processRate,boolean isHomePage) {
        BigDecimal value = performanceAchievementRate.divide(processRate.multiply(HUNDRED), 4, RoundingMode.HALF_UP).multiply(HUNDRED);
        //判断大小
        if (value.compareTo(SEVENTY) <= 0) {
            return RED;
        } else if (value.compareTo(EIGHTY) <= 0) {
            return ORANGE;
        } else if (value.compareTo(ONE_HUNDRED_TWENTY_FIVE) < 0) {
            return isHomePage ? BLUE : GRAY;
        } else {
            return GREEN;
        }
    }

    /**
     * 获取当天处于季度的第几天
     * @param localDate
     * @return
     */
    public int getDaysOfQuarter(LocalDate localDate) {
        int quarter = getQuarter(localDate);
        int year = localDate.getYear();
        // 计算季度的起始月份
        int startMonth = ((quarter - 1) * 3) + 1;
        // 获取季度的第一天
        LocalDate firstDayOfQuarter = LocalDate.of(year, startMonth, 1);
        // 计算季度的天数
        long days = ChronoUnit.DAYS.between(firstDayOfQuarter, localDate.plusDays(1));

        return (int) days;
    }
    /**
     * 获取一个季度有多少天
     * @param year
     * @param quarter
     * @return
     */
    public int getDaysInQuarter(int year, int quarter) {
        // 计算季度的起始月份
        int startMonth = ((quarter - 1) * 3) + 1;
        int endMonth = quarter * 3;
        // 获取季度的第一天
        LocalDate firstDayOfQuarter = LocalDate.of(year, startMonth, 1);
        // 使用TemporalAdjusters调整到季度的最后一天
        LocalDate lastDayOfQuarter = LocalDate.of(year, endMonth, 1).with(TemporalAdjusters.lastDayOfMonth());

        // 计算季度的天数
        long days = ChronoUnit.DAYS.between(firstDayOfQuarter, lastDayOfQuarter.plusDays(1));

        return (int) days;
    }



    /**
     * 获取月份最新的一天 关联当日业绩信息
     * @param dateTypeId
     * @param yearMonth
     * @return
     */
    public String getNearDate(String dateTypeId,String yearMonth){
        LocalDate now = LocalDate.now();
        String localDate = now.toString();
        if("10".equals(dateTypeId)){
            //yearMonth= 2024-01
            if (localDate.contains(yearMonth)) {
                return localDate;
            } else {
                LocalDate parse = LocalDate.parse(yearMonth + "-01");
                LocalDate lastDayOfMonth = parse.withDayOfMonth(parse.lengthOfMonth());
                return lastDayOfMonth.toString();
            }
        }else if("2".equals(dateTypeId)){
            //财年 yearMonth =2024
            if(localDate.contains(yearMonth)){
                return localDate;
            }else {
                return yearMonth + "-12-31";
            }
        }else if("11".equals(dateTypeId)){
            //季度 yearMonth = 2024-Q1
            String year = yearMonth.substring(0,4);
            int quarter = Integer.valueOf(yearMonth.substring(6));
            int nowQuarter = getQuarter(now);

            //当前年度 当前季度
            if(localDate.contains(year) && nowQuarter == quarter){
                return localDate;
            }else {
                //获取季度最新一天的数据
                return getLastDayOfQuarter(Integer.valueOf(year),quarter).toString();
            }
        }
        return null;
    }

    /**
     * 根据LocalDate获取季度
     * @param date
     * @return
     */
    public int getQuarter(LocalDate date) {
        // 获取月份
        int month = date.getMonthValue();
        if (month >= 1 && month <= 3) {
            return 1;
        } else if (month >= 4 && month <= 6) {
            return 2;
        } else if (month >= 7 && month <= 9) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * 根据LocalDate获取季度
     *
     * @param date
     * @return
     */
    public String getQuarterStr(LocalDate date) {
        int year = date.getYear();
        // 获取月份
        int quarter;
        int month = date.getMonthValue();
        if (month >= 1 && month <= 3) {
            quarter = 1;
        } else if (month >= 4 && month <= 6) {
            quarter = 2;
        } else if (month >= 7 && month <= 9) {
            quarter = 3;
        } else {
            quarter = 4;
        }
        return year + "-Q" + quarter;
    }

    /**
     * 获取季度最后一天
     * @param year
     * @param quarter
     * @return
     */
    public LocalDate getLastDayOfQuarter(int year, int quarter) {
        // 根据季度确定月份
        Month month;
        switch (quarter) {
            case 2:
                month = Month.JUNE;
                break;
            case 3:
                month = Month.SEPTEMBER;
                break;
            case 4:
                month = Month.DECEMBER;
                break;
            default:
                //默认使用1季度3月份
                month = Month.MARCH;
        }

        // 使用TemporalAdjusters.lastDayOfMonth获取该月的最后一天
        return LocalDate.of(year, month, 1).with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 合伙人使用mon表吧，新表不会放合伙人
     * 管理人员数据用新表，月度数据用月份关联去取，季度/财年数据放季度/财年最新一个月的数据，
     * 比如现在看23财年，就放2024-03的数据，看24财年，就放2024-05的数据
     * @param dateTypeId
     * @param yearMonth
     * @return
     */
    public String getNearMonth(String dateTypeId,String yearMonth){
        //获取财年和季度最新的一个月：dim_td_date表
        String theDate = null;
        if("10".equals(dateTypeId)){
            theDate = yearMonth;
        }else {
            //获取财年或者季度最新一个月
            String dateParam = yearMonth;
            if("11".equals(dateTypeId)){
                //季度 2024-Q2 --> 2024-02
                dateParam = dateParam.replace("Q","0");
            }
            List<String> nearestMonths = realtimeMapper.getFinancialYearOrQuarterNearestMonth(dateTypeId, dateParam);
            if(CollectionUtils.isEmpty(nearestMonths)){
                log.error("请求参数dateTypeId = [{}] yearMonth=[{}] 查询不到月份数据",dateTypeId,yearMonth);
                return theDate;
            }
            int size = nearestMonths.size();
            LocalDate now = LocalDate.now();
            List<String> filterNearestMonths = nearestMonths.stream().filter(nearestMonth->{
                LocalDate date = LocalDate.of(Integer.valueOf(nearestMonth.substring(0,4)),Integer.valueOf(nearestMonth.substring(5)),1);
                return date.isAfter(now);
            }).collect(Collectors.toList());
            if(filterNearestMonths.isEmpty()){
                //取最后一个月
                theDate = nearestMonths.get(size-1);
            }else {
                //取本月
                theDate = now.toString().substring(0, 7);
            }
        }
        return theDate;
    }

    /**
     * 获取当天处于月度的时间进度
     * @param localDate
     * @return
     */
    public BigDecimal dateMonthRate(LocalDate localDate) {
        return new BigDecimal(localDate.getDayOfMonth()).multiply(HUNDRED).divide(new BigDecimal(localDate.lengthOfMonth()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取当天处于季度的时间进度
     * @param localDate
     * @return
     */
    public BigDecimal dateQuarterRate(LocalDate localDate) {
        int dayOfQuarter = getDaysOfQuarter(localDate);
        int lengthOfQuarter = getDaysInQuarter(localDate);
        return new BigDecimal(dayOfQuarter).multiply(HUNDRED).divide(new BigDecimal(lengthOfQuarter), 2, RoundingMode.HALF_UP);
    }
    public int getDaysInQuarter(LocalDate localDate) {
        int year = localDate.getYear();
        int quarter = getQuarter(localDate);
        return getDaysInQuarter(year,quarter);
    }

    public String formatter(String dateTypeId, LocalDate localDate) {

        if ("10".equals(dateTypeId)) {
            // 创建格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            // 格式化输出
            return formatter.format(localDate);
        } else if ("11".equals(dateTypeId)) {
            // 获取年份
            int year = localDate.getYear();
            // 获取季度 (1-4)
            int quarter = getQuarter(localDate);
            // 格式化输出
            return year + "-Q" + quarter;
        } else {
            return "";
        }
    }

    public List<String> quarterMonths(LocalDate localDate) {
        List<String> monthList = new ArrayList<>();
        // 创建格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate firstMonth = localDate.withMonth((localDate.getMonthValue() - 1) / 3 * 3 + 1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate midMonth = firstMonth.plusMonths(1);
        LocalDate lastMonth = localDate.withMonth(((localDate.getMonthValue() - 1) / 3 + 1) * 3).with(TemporalAdjusters.lastDayOfMonth());
        monthList.add(firstMonth.format(formatter));
        monthList.add(midMonth.format(formatter));
        monthList.add(lastMonth.format(formatter));
        return monthList;

    }

    /**
     * list[0]时间开始月份
     * list[1]时间结束月份
     * @param dateTypeId
     * @param yearMonth
     * @return
     */
    public List<String> queryTimeStartMonth(String dateTypeId,String yearMonth){
        List<String> times = new ArrayList<>(2);
        if("2".equals(dateTypeId)){
            //财年开始月份 t-1年4月 到t年3月
            times.add((Integer.valueOf(yearMonth)-1)+"-04");
            times.add(yearMonth+"-03");
        }else if("11".equals(dateTypeId)){
            //2024-Q1
            Integer quarter = Integer.valueOf(yearMonth.substring(6,7));
            Month startMonth;
            Month endMonth;
            switch (quarter) {
                case 2:
                    startMonth = Month.APRIL;
                    endMonth = Month.JUNE;
                    break;
                case 3:
                    startMonth = Month.JULY;
                    endMonth = Month.SEPTEMBER;
                    break;
                case 4:
                    startMonth = Month.OCTOBER;
                    endMonth = Month.DECEMBER;
                    break;
                default:
                    //默认使用1季度3月份
                    startMonth = Month.JANUARY;
                    endMonth = Month.MARCH;
            }
            Integer year = Integer.valueOf(yearMonth.substring(0,4));
            times.add(YearMonth.of(year,startMonth.getValue()).toString());
            times.add(YearMonth.of(year,endMonth.getValue()).toString());
        }else {
            //开始时间和结束时间都是当前月份
            times.add(yearMonth);
            times.add(yearMonth);
        }
        return times;
    }

    /**
     * 获取季度/财年所有的月份
     * @param dateTypeId
     * @param yearMonth
     * @return
     */
    public List<String> queryYearMonthList(String dateTypeId,String yearMonth){
        if ("10".equals(dateTypeId)) {
            return Collections.singletonList(yearMonth);
        }
        String dateParam = yearMonth;
        if("11".equals(dateTypeId)){
            //季度 2024-Q2 --> 2024-02
            dateParam = dateParam.replace("Q","0");
        }
        return realtimeMapper.getFinancialYearOrQuarterNearestMonth(dateTypeId, dateParam);
    }

    /**
     * 获取同期
     * @param dateTypeId
     * @param yearMonth
     * @return
     */
    public String querySameTerm(String dateTypeId,String yearMonth){
        if(StringUtils.isBlank(yearMonth)){
            return null;
        }
        if(FISCAL_YEAR.equals(dateTypeId)){
            //2024
            return String.valueOf(Integer.valueOf(yearMonth) - 1);
        }else if(QUARTER.equals(dateTypeId)){
            //2024-Q1
            Integer year = Integer.valueOf(yearMonth.substring(0,4));
            Integer quarter = Integer.valueOf(yearMonth.substring(6));
            year--;
            return StringUtils.join(year,"-Q",quarter);
        }else if(MONTHS.equals(dateTypeId)){
            //2024-11
            Integer year = Integer.valueOf(yearMonth.substring(0,4));
            Integer month = Integer.valueOf(yearMonth.substring(5));
            year--;
            return StringUtils.join(year,"-",String.format("%02d",month));
        }
        return null;
    }

    /**
     * 获取上期
     * @param dateTypeId
     * @param yearMonth
     * @return
     */
    public String queryPriorPeriod(String dateTypeId,String yearMonth){
        if(StringUtils.isBlank(yearMonth)){
            return null;
        }
        if(FISCAL_YEAR.equals(dateTypeId)){
            //2024
            return String.valueOf(Integer.valueOf(yearMonth) - 1);
        }else if(QUARTER.equals(dateTypeId)){
            //2024-Q1
            Integer year = Integer.valueOf(yearMonth.substring(0,4));
            Integer quarter = Integer.valueOf(yearMonth.substring(6));
            if(quarter == 1){
                year--;
                quarter = 4;
            }else {
                quarter--;
            }
            return StringUtils.join(year,"-Q",quarter);
        }else if(MONTHS.equals(dateTypeId)){
            //2024-11
            Integer year = Integer.valueOf(yearMonth.substring(0,4));
            Integer month = Integer.valueOf(yearMonth.substring(5));
            if(month == 1){
                year--;
                month = 12;
            }else {
                month--;
            }
            return StringUtils.join(year,"-",String.format("%02d",month));
        }
        return null;
    }

    public String queryTitle(String dateTypeId, String yearMonth, String region) {
        if (StringUtils.isBlank(yearMonth)) {
            return null;
        }
        if (FISCAL_YEAR.equals(dateTypeId)) {
            //2024
            if (CommonConstant.LANGUAGE_CHINESE.equals(region)) {
                return yearMonth + "财年";
            } else {
                return "Fiscal Year" + yearMonth;
            }
        } else if (QUARTER.equals(dateTypeId)) {
            //2024年1季
            Integer year = Integer.valueOf(yearMonth.substring(0, 4));
            Integer quarter = Integer.valueOf(yearMonth.substring(6));
            if (CommonConstant.LANGUAGE_CHINESE.equals(region)) {
                return StringUtils.join(year, "年", quarter, "季");
            } else {
                return StringUtils.join("Q", quarter, " ", year);
            }
        } else if (MONTHS.equals(dateTypeId)) {
            //2024年11月
            Integer year = Integer.valueOf(yearMonth.substring(0, 4));
            Integer month = Integer.valueOf(yearMonth.substring(5));
            if (CommonConstant.LANGUAGE_CHINESE.equals(region)) {
                return StringUtils.join(year, "年", String.format("%02d", month), "月");
            } else {
                return yearMonth;
            }

        }
        return null;
    }
}
