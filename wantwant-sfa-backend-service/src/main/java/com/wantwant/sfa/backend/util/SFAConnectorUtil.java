package com.wantwant.sfa.backend.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class SFAConnectorUtil {
	
	private static String  VERIFIED_CEO_URL;
	
	private static String  VERIFIED_URL;
	
	@Value("${URL.SFA.msSendVerifiedCeoUrl}")
	public void setVerifiedCeoUrl(String msSendVerifiedCeoUrl) {
		SFAConnectorUtil.VERIFIED_CEO_URL = msSendVerifiedCeoUrl;
	}
	
	@Value("${URL.SFA.msSendVerifiedUrl}")
	public void setVerifiedUrl(String msSendVerifiedUrl) {
		SFAConnectorUtil.VERIFIED_URL = msSendVerifiedUrl;
	}
	
	public static boolean sendVerifiedCeoToQueue(String mobileNumber){
		boolean is = false ;
		HttpClient httpClient = HttpClientBuilder.create().build();
    	ObjectMapper mapper = new ObjectMapper();
    	String requestStr = null;
    	HttpPost httpPost = null;
    	HttpResponse response = null;
    	HttpEntity entity = null;
    	String responseString = null;
//    	boolean result = false;
    	Map<String ,String> obj = new HashMap<>();
//    	Map<String, Object> responseValue  = new HashMap<>();
    	obj.put("mobileNumber", mobileNumber);
    	
    	try {
			requestStr = mapper.writeValueAsString(obj);
			System.out.println("requestStr"+requestStr);
			httpPost = new HttpPost(VERIFIED_CEO_URL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
//			responseValue = mapper.readValue(responseString, Map.class);
			is = Boolean.parseBoolean(responseString);
//			is = (int)responseValue.get("status");
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		} 
    	return false;
	}
	
	
	public static boolean sendVerifiedCeoToSFA(String mobileNumber,
			int isVerified,int isRepetition,String closeSuggestions,
			String dismissedSuggestions,int visitId){
		boolean is = false ;
		HttpClient httpClient = HttpClientBuilder.create().build();
		ObjectMapper mapper = new ObjectMapper();
		String requestStr = null;
		HttpPost httpPost = null;
		HttpResponse response = null;
		HttpEntity entity = null;
		String responseString = null;
//		boolean result = false;
		Map<String ,String> obj = new HashMap<>();
//		Map<String, Object> responseValue  = new HashMap<>();
		obj.put("mobileNumber", mobileNumber);
		obj.put("isVerified", String.valueOf(isVerified));
		obj.put("isRepetition", String.valueOf(isRepetition));
		obj.put("closeSuggestions", closeSuggestions);
		obj.put("dismissedSuggestions", dismissedSuggestions);
		obj.put("visitId", String.valueOf(visitId));
		
		try {
			requestStr = mapper.writeValueAsString(obj);
			System.out.println("requestStr"+requestStr);
			httpPost = new HttpPost(VERIFIED_URL);
			httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
			httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
			// 发送请求
			response = httpClient.execute(httpPost);
			log.info("response:{}",response);
			// 解析应答
			entity = response.getEntity();
			responseString = EntityUtils.toString(entity, "UTF-8");
//			responseValue = mapper.readValue(responseString, Map.class);
//			is = (int)responseValue.get("status");
			is = Boolean.parseBoolean(responseString);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		} 
		return is;
	}

}
