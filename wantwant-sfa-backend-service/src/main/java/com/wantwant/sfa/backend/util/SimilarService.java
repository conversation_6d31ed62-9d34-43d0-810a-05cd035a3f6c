package com.wantwant.sfa.backend.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.model.SkuInfoDTO;
import com.wantwant.sfa.backend.infrastructure.client.Estimate.request.QuerySkuRequest;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import static com.wantwant.sfa.backend.util.Base64Util.netUrl2Base64;

/**
 * <AUTHOR>
 * @Date 2020/11/17 16:19
 */
@Component
@Slf4j
public class SimilarService {

    @Value("${baidu.add_url}")
    private static String addUrl;

    @Value("${baidu.search_url}")
    private static String searchUrl;

//    @Value("${baidu.search_url}")
//    private static String faceCompareUrl;

    @Autowired
    private AuthService authService;


//    public BigDecimal compareFace(String referToPicUrl, String picUrl){
//        log.info("compareFace referToPicUrl:{}, {}",referToPicUrl, picUrl);
//        HttpClient httpClient = HttpClientBuilder.create().build();
//        ObjectMapper mapper = new ObjectMapper();
//        String requestStr = null;
//        HttpPost httpPost = null;
//        HttpResponse response = null;
//        HttpEntity entity = null;
//        String responseString = null;
//
//        try {
//            String referToBase64 =  Base64Util.netUrl2Base64(referToPicUrl);
//            String picUrlBase64 = Base64Util.netUrl2Base64(picUrl);
//            List<Map<String, String>> images = Arrays.asList(
//                    new HashMap<String, String>() {{
//                        put("image", referToBase64);
//                        put("image_type", "BASE64");
//                    }},
//                    new HashMap<String, String>() {{
//                        put("image", picUrlBase64);
//                        put("image_type", "BASE64");
//                    }}
//            );
//            requestStr = JSONObject.toJSONString(images);
//            httpPost = new HttpPost(faceCompareUrl + authService.getAuth());
//            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
//            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
//            // 发送请求
//            response = httpClient.execute(httpPost);
//            log.info("compareFace response StatusCode:{}",response.getStatusLine().getStatusCode());
//            // 解析应答
//            entity = response.getEntity();
//            responseString = EntityUtils.toString(entity, "UTF-8");
//            log.info("compareFace request: {}", responseString);
//            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);
//
//            String error = (String) responseValue.get("error");
//            if (error != null) {
//                throw new IllegalStateException(error);
//            }
//            BigDecimal result = (BigDecimal)responseValue.get("score");
//            return result;
//        }catch (Exception e) {
//            log.info(e.getMessage(),e);
//        }
//        return null;
//    }



    public static String similarAdd(String filePath,String accessToken,String id) {
    	log.info("start SimilarService similarAdd filePath:{},id:{}",filePath,id);
    	log.info("addUrl:{},accessToken:{}",addUrl,accessToken);
        // 请求url
//        String url = "https://aip.baidubce.com/rest/2.0/image-classify/v1/realtime_search/similar/add";
        try {
            // 本地文件路径
            byte[] imgData = FileUtil.urlTobyte(filePath);
            String imgStr = Base64Util.encode(imgData);
            String imgParam = URLEncoder.encode(imgStr, "UTF-8");

            String param = "brief=" + id + "&image=" + imgParam + "&tags=" + id;

            String result = HttpUtil.post(addUrl, accessToken, param);
            log.info("send similarAdd result:{}",result);
            return result;
        } catch (Exception e) {
            log.error("上传百度云图片失败");
            log.error(e.getMessage(),e);
        }
        return null;
    }
}
