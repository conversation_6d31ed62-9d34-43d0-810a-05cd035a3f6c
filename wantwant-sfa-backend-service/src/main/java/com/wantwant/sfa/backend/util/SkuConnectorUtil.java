package com.wantwant.sfa.backend.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.model.QuerySkuModel;
import com.wantwant.sfa.backend.model.SkuModel;
import com.wantwant.sfa.backend.model.UpdateMemberBatchModel;
import com.wantwant.sfa.backend.model.UpdateMemberSkuModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/12/06/上午8:49
 */
@Component
@Slf4j
public class SkuConnectorUtil {

    @Value("${URL.sku.query}")
    private String QUERY_SKU;

    @Value("${URL.sku.updateMemberCategorySku}")
    private String UPDATE_MEMBER_SKU;

    @Value("${URL.sku.updateMemberCategorySkuBatch}")
    private String UPDATE_MEMBER_BATCH;

    public List<SkuModel> querySkuInfoByGroupIdOrSpuId(QuerySkuModel querySkuModel){
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            String requestStr = mapper.writeValueAsString(querySkuModel);
            log.info("【query sku】url:{},body:{}",QUERY_SKU,requestStr);
            httpPost = new HttpPost(QUERY_SKU);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" query sku info StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" query sku info response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("query sku info response: {}", responseString);
            if(StringUtils.isBlank(responseString)){
                return null;
            }
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("查询SKU信息失败");
            }

            List<SkuModel> list = (List<SkuModel>)responseValue.get("data");
            return JSONArray.parseArray(JSONObject.toJSONString(list),SkuModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    public void updateMemberCategorySku(UpdateMemberSkuModel updateMemberSkuModel){
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            String requestStr = mapper.writeValueAsString(updateMemberSkuModel);
            log.info("【updateMemberCategorySku】url:{},body:{}",UPDATE_MEMBER_SKU,requestStr);
            httpPost = new HttpPost(UPDATE_MEMBER_SKU);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" update member category sku info StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" update member category sku info response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("update member category sku info response: {}", responseString);

            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("更新用户可销sku失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void updateMemberCategorySkuBatch(UpdateMemberBatchModel UpdateMemberBatchModel){
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            String requestStr = mapper.writeValueAsString(UpdateMemberBatchModel);
            log.info("【updateMemberCategorySku batch】url:{},body:{}",UPDATE_MEMBER_BATCH,requestStr);
            httpPost = new HttpPost(UPDATE_MEMBER_BATCH);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" update member category sku  batch info StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" update member category sku batch info response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info("update member category sku batch info response: {}", responseString);

            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);

            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 0){
                throw new ApplicationException("更新用户可销sku失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
