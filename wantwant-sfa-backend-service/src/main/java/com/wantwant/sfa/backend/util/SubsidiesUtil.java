package com.wantwant.sfa.backend.util;


import com.alibaba.fastjson.JSONObject;
import com.wantwant.commons.core.util.HttpUtil;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.CompanySubsidyAuditResultReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class SubsidiesUtil {

    @Value("${URL.ENTREPRENEURSHIP.SUBSIDIES}")
    private String BONUS_BONUSISSUE;


    public Response post(CompanySubsidyAuditResultReq paramJsonString) {
        return postForResponse(BONUS_BONUSISSUE, paramJsonString);
    }

    private static Response postForResponse(String url, CompanySubsidyAuditResultReq paramJsonString) {
        try {
            log.info("postForResponse post to {} param:{}", url, paramJsonString);
            String body = JSONObject.toJSONString(paramJsonString);
            String result = HttpUtil.postJsonData(url, body);
            log.info("postForResponse post to {} param:{},result -> {}", url, body, result);
            JSONObject resultJson = JSONObject.parseObject(result);
            if (0 != resultJson.getInteger("code")) {
                return Response.error(resultJson.getString("msg"));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
           return  Response.error("系统错误");
        }
        return Response.success();
    }
}
