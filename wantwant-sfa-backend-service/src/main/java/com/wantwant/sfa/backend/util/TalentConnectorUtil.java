package com.wantwant.sfa.backend.util;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.interview.dto.MemberExperienceDTO;
import com.wantwant.sfa.backend.interview.model.*;
import com.wantwant.sfa.backend.interview.request.InterviewInfoSynRequest;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/08/下午9:58
 */
@Component
@Slf4j
public class TalentConnectorUtil {

    @Value("${URL.talent.push}")
    private String TALENT_PUSH;

    public Boolean push(InterviewInfoSynRequest request, ApplyMemberPo applyMemberPo,SfaInterviewProcessModel interviewProcessModel){
        log.info("talent push,request:{}",request);
        TalentSaveRequest talentSaveRequest = assemble(request,applyMemberPo,interviewProcessModel);
        HttpClient httpClient = HttpClientBuilder.create().build();
        ObjectMapper mapper = new ObjectMapper();
        String requestStr = null;
        HttpPost httpPost = null;
        HttpResponse response = null;
        HttpEntity entity = null;
        String responseString = null;

        try {
            requestStr = JSONObject.toJSONString(talentSaveRequest);
            log.info("talent push request:{}",requestStr);
            httpPost = new HttpPost(TALENT_PUSH);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestStr,"UTF-8"));
            log.info(" talent push StringEntity: {}:{}", httpPost.getEntity(),httpPost.getAllHeaders());
            // 发送请求
            response = httpClient.execute(httpPost);
            log.info(" talent push response StatusCode:{}",response.getStatusLine().getStatusCode());
            // 解析应答endDate
            entity = response.getEntity();
            responseString = EntityUtils.toString(entity, "UTF-8");
            log.info(" talent push response: {}", responseString);
            Map<String, Object> responseValue = mapper.readValue(responseString, Map.class);


            Integer code = (Integer)responseValue.get("code");
            if(Objects.isNull(code) || code != 200){
                throw new ApplicationException("推送消息至人才库失败");
            }
        }catch (Exception e) {
            log.info(e.getMessage(),e);
            throw new IllegalStateException(e.getMessage(),e);
        }
        return true;
    }

    private TalentSaveRequest assemble(InterviewInfoSynRequest request, ApplyMemberPo applyMemberPo,SfaInterviewProcessModel interviewProcessModel) {

        TalentSaveRequest talentSaveRequest = new TalentSaveRequest();
        talentSaveRequest.setApplicationId(request.getApplicationId());

        // 职业信息
        TpUserCareerObjective tpUserCareerObjective = new TpUserCareerObjective();
        tpUserCareerObjective.setPosition(request.getPosition().toString());
        tpUserCareerObjective.setApplyDate(applyMemberPo.getApplyTime().toLocalDate());
        String salaryExpectation = request.getSalaryExpectation();
        if(StringUtils.isNotBlank(salaryExpectation)){
            tpUserCareerObjective.setSalaryExpectation(new BigDecimal(salaryExpectation));
        }
        tpUserCareerObjective.setResidenceYear(request.getResidenceYears());
        String distributionArea = StringUtils.EMPTY;
        if(applyMemberPo.getPosition() == 2 || applyMemberPo.getPosition() == 3 || applyMemberPo.getPosition() == 5 || applyMemberPo.getPosition() == 6){
            distributionArea = applyMemberPo.getAgentProvince() + applyMemberPo.getAgentCity();
        }else{
            distributionArea = applyMemberPo.getAgentProvince() + applyMemberPo.getAgentCity() + applyMemberPo.getAgentDistrict();
        }

        tpUserCareerObjective.setDistributionArea(distributionArea);
        tpUserCareerObjective.setSalesYear(request.getSalesYears());
        tpUserCareerObjective.setIdentity(request.getIdentity());
        tpUserCareerObjective.setIndustry(request.getIndustry());
        if(StringUtils.isNotBlank(request.getFoodExperience()) || !"无".equals(request.getFoodExperience())){
            tpUserCareerObjective.setFoodBeverageExperience(1);
        }else{
            tpUserCareerObjective.setFoodBeverageExperience(0);
        }
        tpUserCareerObjective.setCustomerResources(request.getCustomerResources());
        tpUserCareerObjective.setSpecialResources(request.getSpecialChannelResources());
        if(Objects.nonNull(request.getCurrentStatus())){
            tpUserCareerObjective.setCurrentState(request.getCurrentStatus().toString());
        }
        tpUserCareerObjective.setEvaluation(request.getRemark());
        tpUserCareerObjective.setFileUrl(applyMemberPo.getResumeUrl());

        Integer jobsType = applyMemberPo.getJobsType();
        Integer ceoFlag = applyMemberPo.getCeoFlag();
        if(Objects.nonNull(jobsType)){
            if(jobsType == 2 && ceoFlag == 1){
                tpUserCareerObjective.setPositionType("2");
            }else if(jobsType == 2 && ceoFlag == 0){
                tpUserCareerObjective.setPositionType("3");
            }else{
                tpUserCareerObjective.setPositionType("1");
            }
        }
        tpUserCareerObjective.setRetestDate(interviewProcessModel.getOptionalRetestTime());
        talentSaveRequest.setObjective(tpUserCareerObjective);



        // 用户基本信息
        TpUserBasicInfo tpUserBasicInfo = new TpUserBasicInfo();
        tpUserBasicInfo.setHeadUrl(applyMemberPo.getPicUrl());
        tpUserBasicInfo.setName(request.getUserName());
        tpUserBasicInfo.setMobile(request.getUserMobile());
        tpUserBasicInfo.setSex(request.getGender());
        tpUserBasicInfo.setIdCardNo(request.getIdCardNum());
        if(StringUtils.isNotBlank(request.getBirthDate())){
            tpUserBasicInfo.setBirthday(LocalDate.parse(request.getBirthDate()));
        }
        tpUserBasicInfo.setCeoFlag(applyMemberPo.getCeoFlag());
        tpUserBasicInfo.setEmployId(applyMemberPo.getEmployId());
        tpUserBasicInfo.setWwPosition(applyMemberPo.getWwPosition());
        talentSaveRequest.setBasic(tpUserBasicInfo);

        // 推荐人信息
        TpUserRecommandInfo tpUserRecommandInfo = new TpUserRecommandInfo();
        tpUserRecommandInfo.setSignUpChannel(applyMemberPo.getSource());
        tpUserRecommandInfo.setOutsourcingCompany(applyMemberPo.getSuperiorCompany());
        tpUserRecommandInfo.setName(applyMemberPo.getSuperiorName());
        tpUserRecommandInfo.setEmployeeId(applyMemberPo.getSuperiorEmployId());
        tpUserRecommandInfo.setMobile(applyMemberPo.getSuperiorMobile());
        talentSaveRequest.setRecommand(tpUserRecommandInfo);

        // 教育信息
        String highestEducation = request.getHighestEducation();
        if(StringUtils.isNotBlank(highestEducation)){
            List<TpUserEducationExperience> list = new ArrayList<>();
            TpUserEducationExperience tpUserEducationExperience = new TpUserEducationExperience();
            tpUserEducationExperience.setDiploma(highestEducation);
            tpUserEducationExperience.setGraduatedSchool(request.getGraduateSchool());
            tpUserEducationExperience.setSpecialty(request.getSpecializing());

            if(StringUtils.isNotBlank(request.getGraduationDate())){
                tpUserEducationExperience.setEndDate(request.getGraduationDate());
            }

            list.add(tpUserEducationExperience);
            talentSaveRequest.setEducation(list);
        }

        // 用户认证
        TpUserRealAuthentication tpUserRealAuthentication = new TpUserRealAuthentication();
        tpUserRealAuthentication.setIdcardBackUrl(request.getIdCardBackUrl());
        tpUserRealAuthentication.setIdcardFrontUrl(request.getIdCardFrontUrl());
        tpUserRealAuthentication.setHoldCardUrl(request.getHoldCardUrl());
        talentSaveRequest.setAuthentication(tpUserRealAuthentication);

        // 工作经验
        List<MemberExperienceDTO> experiences = request.getExperiences();
        if(!CollectionUtils.isEmpty(experiences)){
           List<TpUserWorkExperience> workExperiences = new ArrayList<>();
            experiences.forEach(e -> {
                TpUserWorkExperience tpUserWorkExperience = new TpUserWorkExperience();
                tpUserWorkExperience.setCategory(e.getProductionCategory());

                tpUserWorkExperience.setIndustry(e.getIndustry());
                tpUserWorkExperience.setPosition(e.getPosition());
                tpUserWorkExperience.setRemark(e.getRemark());
                tpUserWorkExperience.setTeamSize(e.getTeamSize());
                if(Objects.nonNull(e.getStartDate())){
                    tpUserWorkExperience.setStartDate(e.getStartDate());
                }
                if(Objects.nonNull(e.getEndDate())){
                    tpUserWorkExperience.setEndDate(e.getEndDate());
                }
                workExperiences.add(tpUserWorkExperience);
            });
            talentSaveRequest.setWorkExperiences(workExperiences);
        }

        // 工作信息
        TpUserWork tpUserWork = new TpUserWork();
        if(StringUtils.isNotBlank(request.getManagementExperienceYears()) && !"无".equals(request.getManagementExperienceYears()) && !"0".equals(request.getManagementExperienceYears())){
            tpUserWork.setIsManage(0);
            tpUserWork.setManageYear(request.getManagementExperienceYears());
        }else{
            tpUserWork.setIsManage(1);
        }
        talentSaveRequest.setWorkInfo(tpUserWork);

        return talentSaveRequest;
    }
}
