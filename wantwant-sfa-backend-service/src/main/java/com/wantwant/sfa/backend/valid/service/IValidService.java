package com.wantwant.sfa.backend.valid.service;

import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;

import java.util.function.Supplier;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/28/下午4:47
 */
public interface IValidService {
    /**
     * 选择审核人
     *
     * @param position 岗位
     * @param filterEmptyPosition 是否过滤空岗位
     * @param  filterCompany 是否过滤分公司
     * @return
     */
    CeoBusinessOrganizationPositionRelation chooseValidPerson(CeoBusinessOrganizationPositionRelation position, boolean filterEmptyPosition, boolean filterCompany, int channel);


    /**
     * 选择授权审核人
     *
     * @param position
     * @param channel
     * @return
     */
    CeoBusinessOrganizationPositionRelation chooseAuhorizationPerson(CeoBusinessOrganizationPositionRelation position, boolean filterCompany, int channel);
    /**
     * 根据组织ID获取岗位
     *
     * @param organizationId
     * @return
     */
    CeoBusinessOrganizationPositionRelation getPositionIdByOrganizationId(String organizationId);
}
