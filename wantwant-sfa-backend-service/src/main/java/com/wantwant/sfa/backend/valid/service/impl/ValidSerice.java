package com.wantwant.sfa.backend.valid.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.valid.service.IValidService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.wantwant.sfa.backend.interview.service.InterviewService.ZW_HR_EMPLOYEE_ID_CODE;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/28/下午4:48
 */
@Service
public class ValidSerice implements IValidService {

    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ConfigMapper configMapper;

    @Override
    public CeoBusinessOrganizationPositionRelation chooseValidPerson(CeoBusinessOrganizationPositionRelation position, boolean filterEmptyPosition, boolean filterCompany, int channel) {
        if(filterCompany && position.getPositionTypeId() == 2){
            return position;
        }

        // 获取父组织ID
        String parentId = organizationMapper.getOrganizationParentId(position.getOrganizationId());


        // 大区信息
        if(StringUtils.isBlank(parentId)){
            return null;
        }

        // 获取分公司岗位信息
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", parentId)
                .eq("channel", channel));

        // 获取大区的公司岗位信息
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {

            parentId = organizationMapper.getOrganizationParentId(ceoBusinessOrganizationPositionRelation.getOrganizationId());

            ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("organization_id", parentId)
                    .eq("channel", channel));

        }

        // 大区无法获取的情况到达指定的人审核
        if (filterEmptyPosition && StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
            String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
            ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("employee_id", employeeId)
                    .eq("channel", 3));
        }

        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException("无法获取审核人信息");
        }

        return ceoBusinessOrganizationPositionRelation;
    }

    @Override
    public CeoBusinessOrganizationPositionRelation chooseAuhorizationPerson(CeoBusinessOrganizationPositionRelation position, boolean filterCompany, int channel) {
        if(filterCompany && position.getPositionTypeId() == 2){
            return position;
        }

        // 大区直接返回
        if(position.getPositionTypeId() == 1){
            return null;
        }

        // 获取父组织ID
        String parentId = organizationMapper.getOrganizationParentId(position.getOrganizationId());

        // 获取上级岗位信息（所/分公司/大区）
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", parentId)
                .eq("channel", channel));

        // 获取大区的公司岗位信息
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation) || StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {

            parentId = organizationMapper.getOrganizationParentId(ceoBusinessOrganizationPositionRelation.getOrganizationId());

            ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("organization_id", parentId)
                    .eq("channel", channel));

        }

        // 大区无法获取的情况直接返回
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation) || StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
            return null;
        }

        return ceoBusinessOrganizationPositionRelation;
    }


    @Override
    public CeoBusinessOrganizationPositionRelation getPositionIdByOrganizationId(String organizationId) {

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", organizationId).eq("channel", RequestUtils.getChannel()));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("组织岗位获取失败");
        }
        return ceoBusinessOrganizationPositionRelation;
    }
}
