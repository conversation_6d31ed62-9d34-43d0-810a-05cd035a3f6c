package com.wantwant.sfa.backend.MonitoringSku.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.monitoringSku.api.MonitoringSkuApi;
import com.wantwant.sfa.backend.monitoringSku.vo.SkuVo;
import com.wantwant.sfa.backend.service.MonitoringSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
public class MonitoringSkuController implements MonitoringSkuApi {

  @Resource
  private MonitoringSkuService monitoringSkuService;

  @Override
  public Response<Page<SkuVo>> MonitoringSkuList() {
    log.info("start MonitoringSkuController MonitoringSkuList ");

    return Response.success(monitoringSkuService.sleMonitoringSkuList());
  }
}
