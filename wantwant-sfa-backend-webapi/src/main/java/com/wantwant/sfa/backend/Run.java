package com.wantwant.sfa.backend;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.wantwant.commons.core.util.HttpUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.servlet.Filter;

@EnableScheduling
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@EnableAsync
@EnableCaching
@EnableSwagger2
@EnableTransactionManagement
@EnableFeignClients(basePackages = {"com.wantwant"})
@ComponentScan(basePackages = {"com.wantwant.sfa.backend", "com.wantwant.sfa.common.architecture"})
@MapperScan({"com.wantwant.sfa.backend.mapper","com.wantwant.sfa.backend.labels.mapper","com.wantwant.sfa.backend.domain.*.mapper"})
@EnableDiscoveryClient
public class Run {

	public static void main(String[] args) {
		SpringApplication.run(Run.class, args);
	}

	@Bean
    public Filter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOrigin("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        config.addExposedHeader("x-auth-token");
        config.addExposedHeader("x-total-count");
        source.registerCorsConfiguration("/**",config);
        return new CorsFilter(source);
    }

    @Bean
    @LoadBalanced
    public RestTemplate createRestTemplate() {
        return new RestTemplate(new HttpComponentsClientHttpRequestFactory(HttpUtil.createHttpClient()));
    }


}
