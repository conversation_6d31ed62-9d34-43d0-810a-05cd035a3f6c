package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.entity.ActivityQuotaLogEntity;
import com.wantwant.sfa.backend.activityQuota.entity.QuotaControlEntity;
import com.wantwant.sfa.backend.activityQuota.enums.ActivityLogTypeEnum;
import com.wantwant.sfa.backend.activityQuota.enums.ActivityQuotaStatusEnum;
import com.wantwant.sfa.backend.activityQuota.model.*;
import com.wantwant.sfa.backend.activityQuota.service.IActivityQuotaLogService;
import com.wantwant.sfa.backend.authorization.entity.AuthorizationEntity;
import com.wantwant.sfa.backend.authorization.vo.ExpireAuthorizationVo;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.activityQuota.*;
import com.wantwant.sfa.backend.mapper.authorization.AuthorizationMapper;
import com.wantwant.sfa.backend.mapper.authorization.AuthorizationSearchMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.marketAndPersonnel.request.CustomerAuthorizationRequest;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/02/下午4:44
 */
@Component
@Slf4j
public class ActivityQuotaTask {
    @Autowired
    private ActivityQuotaApplicationMapper activityQuotaApplicationMapper;
    @Autowired
    private ActivityQuotaMapper activityQuotaMapper;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Autowired
    private ActivityQuotaDetailMapper activityQuotaDetailMapper;
    @Autowired
    private AuthorizationMapper authorizationMapper;
    @Autowired
    private AuthorizationSearchMapper authorizationSearchMapper;
    @Autowired
    private NotifyService notifyService;
    @Resource
    private ROOTConnectorUtil connectorUtil;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private QuotaControlMapper quotaControlMapper;

    private static String ROOT = "ROOT";

    private static String AUTOCOMMIT = "系统自动执行";

    private static String ZB = "ZB_Z";

    private static String ZB_TYPE = "zb";

    private static Integer ZW_CHANNEL = 3;
    @Autowired
    private IActivityQuotaLogService activityQuotaLogService;
    @Autowired
    private ActivityQuotaLogMapper activityQuotaLogMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;


    @XxlJob("activityQuotaTask")
    @Transactional
    public ReturnT<String> excute(String param) {
        log.info("【活动额度检查】start..");
        LocalDate now = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        log.info("【活动额度检查】执行日期:{}",now.toString());

        List<ActivityQuotaApplicationModel> activityQuotaApplicationModels = activityQuotaApplicationMapper.selectList(new QueryWrapper<ActivityQuotaApplicationModel>()
                .eq("status", ActivityQuotaStatusEnum.NORMAL.getCode())
                .lt("apply_time", now.toString()));
        if(CollectionUtils.isEmpty(activityQuotaApplicationModels)){
            log.info("【活动额度检查】共检测到过期数据:0条");
            log.info("【活动额度检查】end..");
            return ReturnT.SUCCESS;
        }

        log.info("【活动额度检查】共检测到过期数据:"+activityQuotaApplicationModels.size()+"条");
        activityQuotaApplicationModels.forEach(e -> {
            e.setStatus(ActivityQuotaStatusEnum.FAILED.getCode());
            e.setRemark("过期申请");
            e.setAuditTime(LocalDateTime.now());
            activityQuotaApplicationMapper.updateById(e);

            // 同步旺铺额度申请
            activityQuotaConnectorUtil.updateApply(buildModel(e));
        });
        log.info("【活动额度检查】end..");
        return ReturnT.SUCCESS;
    }

    @XxlJob("quotaTransferTask")
    @Transactional
    public ReturnT<String> transfer(String param){
        log.info("【活动额度转移】start..");
        // 获取当月
        LocalDate month = getMonth(param);

        // 初始化本月活动
        List<ActivityQuotaModel> activityQuotaModels = initActivity(month);
        // 初始化管控
        initControl(activityQuotaModels);

        // 上月日期
        LocalDate prevMonth = month.minusMonths(1);

        for(ActivityQuotaModel activityQuotaModel : activityQuotaModels){
            // 获取上月活动
            ActivityQuotaModel prevActivity = activityQuotaMapper.selectOne(new QueryWrapper<ActivityQuotaModel>()
                    .eq("activity_type", 0)
                    .eq("business_group",activityQuotaModel.getBusinessGroup())
                    .eq("activity_time", prevMonth.toString())
            );

            if(Objects.isNull(prevActivity)){
                log.info("【活动额度转移】上月活动不存在,活动日期:"+month.toString());
                continue;
            }

            // 获取上月配置的额度
            List<ActivityQuotaDetailModel> settingDetails = activityQuotaDetailMapper.selectList(new QueryWrapper<ActivityQuotaDetailModel>()
                    .eq("activity_quota_id", prevActivity.getId())
                    .eq("is_apply_quota", 0));

            if(!CollectionUtils.isEmpty(settingDetails)){
                // 根据上月剩余额度配置本月的额度
                transferQuota(settingDetails,prevActivity,activityQuotaModel,0);
            }

        }

        log.info("【活动额度转移】end..");

        return ReturnT.SUCCESS;
    }

    private void initControl(List<ActivityQuotaModel> list) {
        list.forEach(e -> {
            List<String> companyCodes = organizationMapper.selectCompanyOrganization(3, null,e.getBusinessGroup());
            companyCodes.forEach(c -> {
                QuotaControlEntity quotaControlEntity = new QuotaControlEntity();
                quotaControlEntity.setCompanyCode(c);
                quotaControlEntity.setActivityQuotaId(e.getId());
                quotaControlEntity.setControlQuota(new BigDecimal(1000));
                quotaControlEntity.setCreateTime(LocalDateTime.now());
                quotaControlEntity.setUsedQuota(BigDecimal.ZERO);
                quotaControlEntity.setDeleteFlag(0);
                quotaControlMapper.insert(quotaControlEntity);
            });
        });


    }


    @XxlJob("quotaAuthorizationTask")
    @Transactional
    public ReturnT<String> updateAuthorization(String param){
        log.info("【客户授权更新】start..");
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
        List<AuthorizationEntity> entities = authorizationMapper.selectList(new QueryWrapper<AuthorizationEntity>().lt("DATE_FORMAT(valid_end_time,'%Y-%m-%d')", date).eq("is_delete", 0));
        entities.forEach(a ->{
            a.setIsDelete(1);
            a.setUpdateUserId("system");
            a.setUpdateTIme(LocalDateTime.now());
            authorizationMapper.updateById(a);
            connectorUtil.overdueAuthorization(new CustomerAuthorizationRequest(a.getCustomerId()));
            log.info("id:{},过期失效",a.getId());
        });
        //距离结束日期15天消息提醒
        String future = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().plusDays(15));
        List<ExpireAuthorizationVo> list = authorizationSearchMapper.selectExpireList(future);
        List<NotifyPO> notifyPOS = new ArrayList<>();
        if (CommonUtil.ListUtils.isNotEmpty(list)) {
            list.forEach(a -> {
                //分公司负责人
                NotifyPO po = new NotifyPO();
                po.setTitle(a.getCustomerName()+"用户，经销期限即将到期，请前往授权管理查看处理");
                po.setContent(a.getCustomerName()+"用户，经销期限即将到期，请前往授权管理查看处理");
                po.setType(1);
                po.setCode("/AuthorizationList");
                po.setEmployeeId(a.getCompanyEmployeeId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                po.setCreateTime(LocalDateTime.now());
                po.setUpdateTime(LocalDateTime.now());
                if (StringUtils.isNotEmpty(a.getCompanyEmployeeId())) {
                    notifyPOS.add(po);
                }
                //大区负责人
                NotifyPO po1 = new NotifyPO();
                BeanUtils.copyProperties(po,po1);
                po1.setEmployeeId(a.getAreaEmployeeId());
                if (StringUtils.isNotEmpty(a.getAreaEmployeeId())) {
                    notifyPOS.add(po1);
                }
                //总部张远
                NotifyPO po2 = new NotifyPO();
                BeanUtils.copyProperties(po,po2);
                po2.setEmployeeId("00441211");
                notifyPOS.add(po2);
                log.info("用户:{}，经销期限即将到期",a.getCustomerName());
            });
        }
        if (CommonUtil.ListUtils.isNotEmpty(notifyPOS)) {
            notifyService.saveBatch(notifyPOS);
        }
        log.info("【客户授权更新】end..");
        return ReturnT.SUCCESS;
    }


    public void transferQuota(List<ActivityQuotaDetailModel> activityQuotaDetailModels, ActivityQuotaModel prevActivity, ActivityQuotaModel activity, int isApplyQuota) {
        // 获取所有余额大于0的
        activityQuotaDetailModels.stream().filter(e -> e.getQuota().subtract(e.getUsedQuota()).compareTo(BigDecimal.ZERO) > 0).forEach(e -> {
            BigDecimal quota = e.getQuota().subtract(e.getUsedQuota());
            // 保存明细
            saveActivityDetail(activity,prevActivity, e,quota,BigDecimal.ZERO,isApplyQuota);
            // 添加记录
            saveActivityQuotaLog(activity,prevActivity,e.getOrganizationId(),e.getOrganizationType(),isApplyQuota,e.getParentOrganizationId());
        });

    }


    private void saveActivityDetail(ActivityQuotaModel activity, ActivityQuotaModel prevActivity, ActivityQuotaDetailModel e, BigDecimal quota, BigDecimal usedQuota, int isApplyQuota) {
        // 获取本月配置明细
        ActivityQuotaDetailModel activityQuotaDetailModel = activityQuotaDetailMapper.selectOne(new QueryWrapper<ActivityQuotaDetailModel>()
                .eq("organization_id", e.getOrganizationId())
                .eq("activity_quota_id",activity.getId())
                .eq("is_apply_quota", isApplyQuota));

        // 额度明细不存在插入
        if(Objects.isNull(activityQuotaDetailModel)){
            activityQuotaDetailModel = new ActivityQuotaDetailModel();
            BeanUtils.copyProperties(e,activityQuotaDetailModel,"id","activityQuotaId","updateUserId","updateUserName","retriveQuota","quota","usedQuota");
            activityQuotaDetailModel.setActivityQuotaId(activity.getId());
            activityQuotaDetailModel.setUpdateUserId("ROOT");
            activityQuotaDetailModel.setUpdateUserName("系统自动审核");
            activityQuotaDetailModel.setQuota(quota);
            activityQuotaDetailModel.setUsedQuota(usedQuota);
            activityQuotaDetailMapper.insert(activityQuotaDetailModel);
        }
        // 额度累加
        else{
            activityQuotaDetailModel.setQuota(activityQuotaDetailModel.getQuota().add(quota));
            activityQuotaDetailModel.setUsedQuota(activityQuotaDetailModel.getUsedQuota().add(usedQuota));
            activityQuotaDetailMapper.updateById(activityQuotaDetailModel);
        }

        String parentOrganizationId = activityQuotaDetailModel.getParentOrganizationId();
        if(activityQuotaDetailModel.getOrganizationType().equals("area")){
            // 根据产线获取总部CODE
            parentOrganizationId = organizationMapper.selectOrganizationByProductGroupAndType(activity.getBusinessGroup(),"zb");
        }

        // TODO V7.0.0特殊处理，跳过区域经理层
        if(activityQuotaDetailModel.getOrganizationType().equals("branch")){
            parentOrganizationId = organizationMapper.getOrganizationParentId(parentOrganizationId);
        }

        // TODO V8.3.0特殊处理，跳过虚拟大区及省区
        if(activityQuotaDetailModel.getOrganizationType().equals("company")){
            parentOrganizationId = parentOrganizationId;
        }

        String organizationType = organizationMapper.getOrganizationType(parentOrganizationId);

        // 递归设置父类额度
        if(StringUtils.isNotBlank(parentOrganizationId)){
            ActivityQuotaDetailModel detailModel = activityQuotaDetailMapper.selectOne(new QueryWrapper<ActivityQuotaDetailModel>()
                    .eq("activity_quota_id", prevActivity.getId())
                    .eq("organization_id",parentOrganizationId)
                    .eq("is_apply_quota", 0));

            saveActivityDetail(activity,prevActivity,detailModel,quota,quota,0);
        }

        // 总部还需要设置主表额度
        if("zb".equals(organizationType)){
            activity.setUsedQuota(activity.getUsedQuota().add(usedQuota));
            activity.setQuota(activity.getQuota().add(quota));
            activityQuotaMapper.updateById(activity);
        }
    }

    private void saveActivityQuotaLog(ActivityQuotaModel activity, ActivityQuotaModel prevActivity, String organizationId, String organizationType, int isApplyQuota, String parentOrganizationId) {

        // 获取所有加法记录
        List<ActivityQuotaLogEntity> addList = activityQuotaLogMapper.selectList(new QueryWrapper<ActivityQuotaLogEntity>().eq("activity_quota_id", prevActivity.getId()).in("type", 1, 3,4).eq("organization_id", organizationId).eq("is_business", isApplyQuota));
        // 获取所有减法记录
        List<ActivityQuotaLogEntity> subList = activityQuotaLogMapper.selectList(new QueryWrapper<ActivityQuotaLogEntity>().eq("activity_quota_id", prevActivity.getId()).in("type", 2,5).eq("organization_id", organizationId).eq("is_business", isApplyQuota));

        // addList根据applyType分组
        Map<String, List<ActivityQuotaLogEntity>> addMap = addList.stream().collect(Collectors.groupingBy(e -> e.getApplyType() +"_" + e.getBoundary()));
        // subList根据applyType分组
        Map<String, List<ActivityQuotaLogEntity>> subMap =subList.stream().collect(Collectors.groupingBy(e -> e.getApplyType() +"_" + e.getBoundary()));


        // TODO V7.0.0跳过区域经理层
        if(organizationType.equals("branch")){
            parentOrganizationId = organizationMapper.getOrganizationParentId(parentOrganizationId);
        }

        // TODO V8.3.0特殊处理，跳过虚拟大区及省区
        if(organizationType.equals("company")){
            parentOrganizationId = parentOrganizationId;
        }


        // 迭代增加额度map
        for (Map.Entry<String, List<ActivityQuotaLogEntity>> entry : addMap.entrySet()) {
            Map<DeptQuotaModel,BigDecimal> parentSendMap = new HashMap<>();

            Integer applyType = Integer.parseInt(entry.getKey().split("_")[0]);
            Integer boundary = Integer.parseInt(entry.getKey().split("_")[1]);
            List<ActivityQuotaLogEntity> list = entry.getValue();
            // 获取需要增加的值
            BigDecimal addQuota = list.stream().map(ActivityQuotaLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);

            // 减去需要扣除的部分
            if (subMap.containsKey(entry.getKey())) {
                BigDecimal subQuota = subMap.get(entry.getKey()).stream().map(ActivityQuotaLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
                addQuota = addQuota.subtract(subQuota);
            }

            // 如果额度大于0则记录
            if (addQuota.compareTo(BigDecimal.ZERO) > 0) {

                String currentParentOrganizationId = parentOrganizationId;
                String organizationName = organizationMapper.getOrganizationName(parentOrganizationId);
                QuotaLogAdditionalModel quotaLogAdditionalModel = new QuotaLogAdditionalModel();
                quotaLogAdditionalModel.setRevenue(organizationName);
                quotaLogAdditionalModel.setBoundary(boundary);
                quotaLogAdditionalModel.setExpenditure("系统");

                // 循环设置部门记录
                List<DeptQuotaModel> deptQuotaModels = activityQuotaLogService.selectDeptQuota(prevActivity.getActivityTime().toString().substring(0, 7), applyType, addQuota, organizationId,false);

                // 需要发送的额度
                AtomicReference<BigDecimal> needSendQuota = new AtomicReference(addQuota);

                deptQuotaModels.forEach(e -> {
                    // 发放额度拆分
                    if(needSendQuota.get().compareTo(BigDecimal.ZERO)  > 0){
                        // 需要发送的额度
                        BigDecimal surplusQuota = needSendQuota.get();
                        // 部门额度
                        BigDecimal deptQuota = e.getQuota();
                        // 发送额度
                        BigDecimal sendQuota = BigDecimal.ZERO;
                        // 部门额度大于等于需要发送的额度则直接发放
                        if(deptQuota.compareTo(surplusQuota) >= 0){
                            sendQuota = surplusQuota;
                            needSendQuota.set(BigDecimal.ZERO);
                        }else{
                            sendQuota = deptQuota;
                            needSendQuota.set(surplusQuota.subtract(sendQuota));
                        }
                        quotaLogAdditionalModel.setDeptCode(e.getDeptCode());
                        quotaLogAdditionalModel.setDeptName(e.getDeptName());

                        parentSendMap.put(e,sendQuota);
                        // 方法记录
                        activityQuotaLogService.saveLog(activity.getId(), organizationId, organizationType, sendQuota, ActivityLogTypeEnum.EXTEND.getCode(), applyType, "ROOT", false,null,quotaLogAdditionalModel);
                    }
                });



                while (StringUtils.isNotBlank(currentParentOrganizationId)) {
                    // 根据parentOrganizationId获取类型
                    String orgType = organizationMapper.getOrganizationType(currentParentOrganizationId);

                    String orgName = organizationMapper.getOrganizationName(parentOrganizationId);
                    QuotaLogAdditionalModel tempModel = new QuotaLogAdditionalModel();
                    tempModel.setRevenue(orgName);
                    tempModel.setExpenditure("系统");
                    tempModel.setBoundary(boundary);
                    // 特殊账号设置
                    LoginModel loginModel = new LoginModel();
                    loginModel.setBusinessGroup(activity.getBusinessGroup());
                    loginModel.setChannel(3);
                    loginModel.setPositionTypeId(7);
                    tempModel.setLoginModel(loginModel);

                    // 循环设置部门额度
                    String finalCurrentParentOrganizationId = currentParentOrganizationId;
                    parentSendMap.forEach((key, value) -> {
                        tempModel.setDeptName(key.getDeptName());
                        tempModel.setDeptCode(key.getDeptCode());
                        activityQuotaLogService.saveLog(activity.getId(), finalCurrentParentOrganizationId, orgType, value, ActivityLogTypeEnum.EXTEND.getCode(), applyType, "00462947", false,null,tempModel);
                        activityQuotaLogService.saveLog(activity.getId(), finalCurrentParentOrganizationId, orgType, value, ActivityLogTypeEnum.USED.getCode(), applyType, "00462947", false,null,tempModel);
                    });


                    String organizationParentId = organizationMapper.getOrganizationParentId(parentOrganizationId);
                    if (StringUtils.isBlank(organizationParentId) && orgType.equals("area")) {
                        // 根据产线获取总部CODE
                        currentParentOrganizationId = organizationMapper.selectOrganizationByProductGroupAndType(activity.getBusinessGroup(),"zb");
                    }else{
                        currentParentOrganizationId = organizationParentId;
                    }
                }
            }
        }
    }


    private LocalDate getMonth(String param) {
        if(StringUtils.isBlank(param)){

            return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());

        }else{
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return  LocalDate.parse(param, format);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<ActivityQuotaModel> initActivity(LocalDate month) {

        // 获取产品组
        List<SfaBusinessGroupEntity> sfaBusinessGroupEntities = sfaBusinessGroupMapper.selectList(new QueryWrapper<SfaBusinessGroupEntity>().eq("status", 1).eq("delete_flag", 0));
        if(CollectionUtils.isEmpty(sfaBusinessGroupEntities)){
            throw new ApplicationException("获取产品组失败");
        }



        List<ActivityQuotaModel> list = new ArrayList<>();

        sfaBusinessGroupEntities.forEach(e -> {

            String zbOrg = organizationMapper.selectOrganizationByProductGroupAndType(e.getId(),"zb");

            if(StringUtils.isNotBlank(zbOrg)){
                ActivityQuotaModel activityQuotaModel = activityQuotaMapper.selectOne(new QueryWrapper<ActivityQuotaModel>()
                        .eq("activity_type", 0)
                        .eq("business_group",e.getId())
                        .eq("activity_time", month.toString())
                );
                if(Objects.isNull(activityQuotaModel)){
                    activityQuotaModel = new ActivityQuotaModel(0, BigDecimal.ZERO, BigDecimal.ZERO, month, LocalDateTime.now(), ROOT, AUTOCOMMIT, LocalDateTime.now(),ROOT, AUTOCOMMIT,ZW_CHANNEL);
                    activityQuotaModel.setBusinessGroup(e.getId());
                    activityQuotaMapper.insert(activityQuotaModel);

                    // 插入活动额度子表信息
                    ActivityQuotaDetailModel activityQuotaDetailModel = new ActivityQuotaDetailModel(activityQuotaModel.getId(), zbOrg, null, BigDecimal.ZERO, BigDecimal.ZERO, ROOT, AUTOCOMMIT, LocalDateTime.now(),ZB_TYPE, ZW_CHANNEL);
                    activityQuotaDetailMapper.insert(activityQuotaDetailModel);
                }
                list.add(activityQuotaModel);
            }
        });


        return list;
    }


    private ActivityQuotaValidModel buildModel(ActivityQuotaApplicationModel e) {
        ActivityQuotaValidModel model = new ActivityQuotaValidModel();

        model.setStatus(ActivityQuotaStatusEnum.FAILED.getCode());
        model.setAuditor("ROOT");
        model.setAuditRemark("过期申请");
        model.setCode(e.getCode());
        model.setSurplusAmount(BigDecimal.ZERO);
        model.setAuditAmount(BigDecimal.ZERO);

        return model;
    }



}
