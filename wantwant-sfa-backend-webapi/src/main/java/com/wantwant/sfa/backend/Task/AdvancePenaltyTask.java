package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyRegularEntity;
import com.wantwant.sfa.backend.activityQuota.service.impl.PenaltyService;
import com.wantwant.sfa.backend.domain.advanceCoin.enums.BillStatusEnum;
import com.wantwant.sfa.backend.domain.advanceCoin.mapper.AdvanceCoinRepaymentLogMapper;
import com.wantwant.sfa.backend.domain.advanceCoin.repository.facade.IAdvanceCoinRepository;
import com.wantwant.sfa.backend.domain.advanceCoin.repository.po.AdvanceCoinApplyPO;
import com.wantwant.sfa.backend.domain.advanceCoin.repository.po.AdvanceCoinPaymentPO;
import com.wantwant.sfa.backend.domain.advanceCoin.repository.po.AdvanceCoinRepaymentLogPO;
import com.wantwant.sfa.backend.domain.advanceCoin.utils.AdvanceCoinUtils;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyRegularMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.NotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/18/上午11:35
 */
@Component
@Slf4j
public class AdvancePenaltyTask {

    @Resource
    private IAdvanceCoinRepository advanceCoinRepository;
    @Resource
    private PenaltyService penaltyService;
    @Resource
    private PenaltyRegularMapper penaltyRegularMapper;

    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private AdvanceCoinRepaymentLogMapper advanceCoinRepaymentLogMapper;
    @Resource
    private NotifyService notifyService;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    private String msg = "您好,批款单号{0},第{1}期{2}已从您账户中完成扣减";

    @XxlJob("advancePenalty")
    @Transactional
    public ReturnT<String> execute(String param) {
        LocalDate currentDay =  getDate(param);

        // 检查当天需要还款的记录
        List<AdvanceCoinRepaymentLogPO> advanceCoinRepaymentLogPOS = advanceCoinRepaymentLogMapper.selectList(new LambdaQueryWrapper<AdvanceCoinRepaymentLogPO>().eq(AdvanceCoinRepaymentLogPO::getRepaymentDate, currentDay).eq(AdvanceCoinRepaymentLogPO::getStatus, 0).eq(AdvanceCoinRepaymentLogPO::getDeleteFlag, 0));
        log.info("【advance penalty】payment list:{}",advanceCoinRepaymentLogPOS);
        if(CollectionUtils.isEmpty(advanceCoinRepaymentLogPOS)){
            return ReturnT.SUCCESS;
        }



        advanceCoinRepaymentLogPOS.forEach(e -> {
            BigDecimal repaymentQuota = e.getRepaymentQuota();
            Integer repaymentPeriod = e.getRepaymentPeriod();

            AdvanceCoinPaymentPO advanceCoinPaymentPO = advanceCoinRepository.getPaymentById(e.getPaymentId());
            if(Objects.nonNull(advanceCoinPaymentPO)){
                Long applyId = advanceCoinPaymentPO.getApplyId();

                AdvanceCoinApplyPO advanceCoinApplyPO = advanceCoinRepository.selectAdvanceCoinApplyById(applyId);
                if(Objects.nonNull(advanceCoinApplyPO)){
                    String applyOrganizationId = advanceCoinApplyPO.getApplyOrganizationId();


                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, applyOrganizationId).last("limit 1"));
                    Integer businessGroup = ceoBusinessOrganizationPositionRelation.getBusinessGroup();

                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>()
                            .eq(PenaltyRegularEntity::getRegularName, "费用前置扣罚")
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroup)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));


                    if(Objects.nonNull(regularEntity)){

                        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(advanceCoinApplyPO.getEmployeeInfoId());
                        // 调用扣罚接口
                        List<Long> penaltyIds = Optional.ofNullable(penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, repaymentQuota, regularEntity.getId(), "系统自动扣罚", null)).orElse(new ArrayList<>());
                        String penaltyIdStr = String.join(",", penaltyIds.stream().map(String::valueOf).collect(Collectors.toList()));

                        // 推送消息
                        NotifyPO po = new NotifyPO();
                        po.setTitle(MessageFormat.format(msg,e.getPaymentId().toString(),repaymentPeriod,repaymentQuota.toString()));
                        po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
                        po.setContent(MessageFormat.format(msg,e.getPaymentId().toString(),repaymentPeriod,repaymentQuota.toString()));
                        po.setCode("/preExpenseGrantList");
                        po.setEmployeeId(sfaEmployeeInfoModel.getEmployeeId());
                        po.setCreateBy("-1");
                        po.setUpdateBy("-1");
                        List<NotifyPO> notifyPOS = Collections.singletonList(po);
                        notifyService.saveBatch(notifyPOS);

                        e.setStatus(1);
                        e.setPenaltyIds(penaltyIdStr);
                        advanceCoinRepaymentLogMapper.updateById(e);

                        // 更新批款单进度
                        int repaymentProgress = Optional.ofNullable(advanceCoinPaymentPO.getRepaymentProgress()).orElse(0);
                        repaymentProgress = repaymentProgress + 1;

                        Integer totalCount = advanceCoinRepaymentLogMapper.selectCount(new LambdaQueryWrapper<AdvanceCoinRepaymentLogPO>()
                                .eq(AdvanceCoinRepaymentLogPO::getPaymentId, e.getPaymentId()).eq(AdvanceCoinRepaymentLogPO::getDeleteFlag, 0));
                        if(repaymentProgress == totalCount){
                            advanceCoinPaymentPO.setBillStatus(2);
                        }

                        advanceCoinPaymentPO.setRepaymentProgress(repaymentProgress);
                        advanceCoinRepository.updatePayment(advanceCoinPaymentPO);

                    }

                }

            }
        });

        return ReturnT.SUCCESS;
    }

    private LocalDate getDate(String param) {
        if(StringUtils.isNotBlank(param)){
            return LocalDate.parse(param);
        }

        return LocalDate.now();
    }
}
