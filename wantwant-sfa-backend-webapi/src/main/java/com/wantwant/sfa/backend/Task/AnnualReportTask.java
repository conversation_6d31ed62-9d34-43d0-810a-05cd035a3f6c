package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.annualReport.service.IAnnualReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.Task
 * @Description:
 * @Date: 2025/1/15 9:07
 */
@Component
@Slf4j
public class AnnualReportTask {
    @Resource
    private IAnnualReportService annualReportService;

    @XxlJob("2024AnnualReportNotify")
    public ReturnT<String> annualReportNotify(String param){
        log.info("2024年度报告推送任务开始执行:annualReportNotify");
        annualReportService.annualReportNotify();
        log.info("2024年度报告推送任务执行完成:annualReportNotify");
        return ReturnT.SUCCESS;
    }
}
