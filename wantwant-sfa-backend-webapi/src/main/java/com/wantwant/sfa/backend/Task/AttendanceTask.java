package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.attendance.service.IAttendanceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


@Configuration
@Slf4j
public class AttendanceTask {



    @Autowired
    IAttendanceService iAttendanceService;

    @XxlJob("attendanceInitTask")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> attendanceInitEveryDayExecute(String param) {
        log.info("execute attendanceInitEveryDayExecute :{}", param);
        iAttendanceService.attendanceInitTask(param);
        return ReturnT.SUCCESS;
    }

}
