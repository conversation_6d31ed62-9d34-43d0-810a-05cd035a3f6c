package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.authorization.entity.AuthorizationVerifyTaskEntity;
import com.wantwant.sfa.backend.authorization.enums.AuthorizeTaskProcessStepEnum;
import com.wantwant.sfa.backend.employee.request.ParentRequest;
import com.wantwant.sfa.backend.employee.vo.ParentVo;
import com.wantwant.sfa.backend.entity.SfaAuditProcessTaskRecord;
import com.wantwant.sfa.backend.mapper.SfaAuditProcessTaskRecordMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.afterSales.AfterSalesProcessDetailMapper;
import com.wantwant.sfa.backend.mapper.authorization.AuthorizationVerifyTaskMapper;
import com.wantwant.sfa.backend.mapper.dataModify.ModifyProcessDetailMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayProcessDetailMapper;
import com.wantwant.sfa.backend.model.afterSales.AfterSalesProcessDetailPO;
import com.wantwant.sfa.backend.model.dataModify.ModifyProcessDetailPO;
import com.wantwant.sfa.backend.model.display.DisplayProcessDetailPO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AuditProcessTask {
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private DisplayProcessDetailMapper displayProcessDetailMapper;
    @Autowired
    private AfterSalesProcessDetailMapper afterSalesProcessDetailMapper;
    @Autowired
    private ModifyProcessDetailMapper modifyProcessDetailMapper;
    @Autowired
    private AuthorizationVerifyTaskMapper authorizationVerifyTaskMapper;
    @Autowired
    private SfaAuditProcessTaskRecordMapper sfaAuditProcessTaskRecordMapper;

    @XxlJob("auditProcessByEmployeeTask")
    public ReturnT<String> AuditProcessByEmployee(String param) {

        log.info("审核流程更新审核人自动定时任务开始..param:{}", param);

        List<Integer> employeeInfoIds = new ArrayList<>();

        List<DisplayProcessDetailPO> auditDisplayList = displayProcessDetailMapper.selectAuditDisplay();
        if (CollectionUtils.isEmpty(auditDisplayList)) {
            log.info("无待更新审核人的【特陈】记录.");
        } else {
            log.info("待更新审核人的【特陈】记录数：" + auditDisplayList.size());
            employeeInfoIds.addAll(auditDisplayList.stream().map(DisplayProcessDetailPO::getEmployeeInfoId).collect(Collectors.toList()));
        }

        List<AfterSalesProcessDetailPO> auditAfterSalesList = afterSalesProcessDetailMapper.selectAuditAfterSales();
        if (CollectionUtils.isEmpty(auditAfterSalesList)) {
            log.info("无待更新审核人的【临期售后】记录.");
        } else {
            log.info("待更新审核人的【临期售后】记录数：" + auditAfterSalesList.size());
            employeeInfoIds.addAll(auditAfterSalesList.stream().map(AfterSalesProcessDetailPO::getEmployeeInfoId).collect(Collectors.toList()));
        }

        List<ModifyProcessDetailPO> auditDataModifyList = modifyProcessDetailMapper.selectAuditDataModify();
        if (CollectionUtils.isEmpty(auditDataModifyList)) {
            log.info("无待更新审核人的【资料修改】记录.");
        } else {
            log.info("待更新审核人的【资料修改】记录数：" + auditDataModifyList.size());
            employeeInfoIds.addAll(auditDataModifyList.stream().map(ModifyProcessDetailPO::getEmployeeInfoId).collect(Collectors.toList()));
        }


        if (!CollectionUtils.isEmpty(employeeInfoIds)) {
            ParentRequest request = new ParentRequest();
            request.setIsExclude(1);
            request.setStatus(0);
            request.setEmployeeInfoIds(employeeInfoIds);
            List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds2(request);
            Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));

            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDate nowDate = nowDateTime.toLocalDate();

            if (!CollectionUtils.isEmpty(auditDisplayList)) {
                auditDisplayList.forEach(auditDisplay -> {

                    SfaAuditProcessTaskRecord auditProcessTaskRecord = new SfaAuditProcessTaskRecord();
                    auditProcessTaskRecord.init("", "");
                    auditProcessTaskRecord.setTaskDate(nowDate);
                    auditProcessTaskRecord.setType(1);
                    auditProcessTaskRecord.setProcessId(auditDisplay.getId());
                    auditProcessTaskRecord.setEmployeeIdFrom(auditDisplay.getReviewerId());
                    auditProcessTaskRecord.setEmployeeInfoIdFrom(auditDisplay.getEmployeeInfoId());
                    auditProcessTaskRecord.setOrganizationIdFrom(auditDisplay.getOrganizationId());

                    List<ParentVo> parentVoList1 = parentMap.get(auditDisplay.getEmployeeInfoId());
                    if (!CollectionUtils.isEmpty(parentVoList1)) {
                        auditDisplay.setReviewerId(parentVoList1.get(0).getParentEmployeeId());
                        auditDisplay.setReviewerName(parentVoList1.get(0).getParentOrganizationPositionEmployeeName());
                        auditDisplay.setOrganizationId(parentVoList1.get(0).getParentOrganizationId());
                        auditDisplay.setUpdateTime(nowDateTime);
                        auditProcessTaskRecord.setEmployeeIdTo(auditDisplay.getReviewerId());
                        auditProcessTaskRecord.setEmployeeInfoIdTo(parentVoList1.get(0).getParentEmployeeInfoId());
                        auditProcessTaskRecord.setOrganizationIdTo(auditDisplay.getOrganizationId());
                    } else {
                        auditDisplay.setReviewerId(null);
                        auditDisplay.setReviewerName(null);
                        auditDisplay.setOrganizationId(null);
                        auditDisplay.setUpdateTime(nowDateTime);
                        auditProcessTaskRecord.setEmployeeIdTo(null);
                        auditProcessTaskRecord.setEmployeeInfoIdTo(null);
                        auditProcessTaskRecord.setOrganizationIdTo(null);
                    }
                    displayProcessDetailMapper.updateAuditDisplay(auditDisplay);
                    sfaAuditProcessTaskRecordMapper.insert(auditProcessTaskRecord);
                });
            }

            if (!CollectionUtils.isEmpty(auditAfterSalesList)) {
                auditAfterSalesList.forEach(auditAfterSales -> {

                    SfaAuditProcessTaskRecord auditProcessTaskRecord = new SfaAuditProcessTaskRecord();
                    auditProcessTaskRecord.init("", "");
                    auditProcessTaskRecord.setTaskDate(nowDate);
                    auditProcessTaskRecord.setType(2);
                    auditProcessTaskRecord.setProcessId(auditAfterSales.getId());
                    auditProcessTaskRecord.setEmployeeIdFrom(auditAfterSales.getReviewerId());
                    auditProcessTaskRecord.setEmployeeInfoIdFrom(auditAfterSales.getEmployeeInfoId());
                    auditProcessTaskRecord.setOrganizationIdFrom(auditAfterSales.getOrganizationId());

                    List<ParentVo> parentVoList1 = parentMap.get(auditAfterSales.getEmployeeInfoId());
                    if (!CollectionUtils.isEmpty(parentVoList1)) {
                        auditAfterSales.setReviewerId(parentVoList1.get(0).getParentEmployeeId());
                        auditAfterSales.setReviewerName(parentVoList1.get(0).getParentOrganizationPositionEmployeeName());
                        auditAfterSales.setOrganizationId(parentVoList1.get(0).getParentOrganizationId());
                        auditAfterSales.setUpdateTime(nowDateTime);
                        auditProcessTaskRecord.setEmployeeIdTo(auditAfterSales.getReviewerId());
                        auditProcessTaskRecord.setEmployeeInfoIdTo(parentVoList1.get(0).getParentEmployeeInfoId());
                        auditProcessTaskRecord.setOrganizationIdTo(auditAfterSales.getOrganizationId());
                    } else {
                        auditAfterSales.setReviewerId(null);
                        auditAfterSales.setReviewerName(null);
                        auditAfterSales.setOrganizationId(null);
                        auditAfterSales.setUpdateTime(nowDateTime);
                        auditProcessTaskRecord.setEmployeeIdTo(null);
                        auditProcessTaskRecord.setEmployeeInfoIdTo(null);
                        auditProcessTaskRecord.setOrganizationIdTo(null);
                    }
                    afterSalesProcessDetailMapper.updateAuditAfterSales(auditAfterSales);
                    sfaAuditProcessTaskRecordMapper.insert(auditProcessTaskRecord);
                });
            }

            if (!CollectionUtils.isEmpty(auditDataModifyList)) {
                auditDataModifyList.forEach(auditDataModify -> {

                    SfaAuditProcessTaskRecord auditProcessTaskRecord = new SfaAuditProcessTaskRecord();
                    auditProcessTaskRecord.init("", "");
                    auditProcessTaskRecord.setTaskDate(nowDate);
                    auditProcessTaskRecord.setType(3);
                    auditProcessTaskRecord.setProcessId(auditDataModify.getId());
                    auditProcessTaskRecord.setEmployeeIdFrom(auditDataModify.getReviewerId());
                    auditProcessTaskRecord.setEmployeeInfoIdFrom(auditDataModify.getEmployeeInfoId());
                    auditProcessTaskRecord.setOrganizationIdFrom(auditDataModify.getOrganizationId());

                    List<ParentVo> parentVoList1 = parentMap.get(auditDataModify.getEmployeeInfoId());
                    if (!CollectionUtils.isEmpty(parentVoList1)) {
                        auditDataModify.setReviewerId(parentVoList1.get(0).getParentEmployeeId());
                        auditDataModify.setReviewerName(parentVoList1.get(0).getParentOrganizationPositionEmployeeName());
                        auditDataModify.setOrganizationId(parentVoList1.get(0).getParentOrganizationId());
                        auditDataModify.setUpdateTime(nowDateTime);
                        auditProcessTaskRecord.setEmployeeIdTo(auditDataModify.getReviewerId());
                        auditProcessTaskRecord.setEmployeeInfoIdTo(parentVoList1.get(0).getParentEmployeeInfoId());
                        auditProcessTaskRecord.setOrganizationIdTo(auditDataModify.getOrganizationId());
                    } else {
                        auditDataModify.setReviewerId(null);
                        auditDataModify.setReviewerName(null);
                        auditDataModify.setOrganizationId(null);
                        auditDataModify.setUpdateTime(nowDateTime);
                        auditProcessTaskRecord.setEmployeeIdTo(null);
                        auditProcessTaskRecord.setEmployeeInfoIdTo(null);
                        auditProcessTaskRecord.setOrganizationIdTo(null);
                    }
                    modifyProcessDetailMapper.updateAuditDataModify(auditDataModify);
                    sfaAuditProcessTaskRecordMapper.insert(auditProcessTaskRecord);
                });
            }

        }
        log.info("审核流程更新审核人自动定时任务结束..");
        return ReturnT.SUCCESS;
    }


    @XxlJob("auditProcessByOrganizationTask")
    public ReturnT<String> AuditProcessByOrganization(String param) {

        log.info("审核流程更新审核人自动定时任务开始..param:{}", param);

        List<String> organizationIds = new ArrayList<>();

        List<AuthorizationVerifyTaskEntity> auditAuthorizationVerifyList = authorizationVerifyTaskMapper.selectAuditAuthorizationVerify();
        if (CollectionUtils.isEmpty(auditAuthorizationVerifyList)) {
            log.info("无待更新审核人的【授权审核】记录.");
        } else {
            log.info("待更新审核人的【授权审核】记录数：" + auditAuthorizationVerifyList.size());
            organizationIds.addAll(auditAuthorizationVerifyList.stream().map(AuthorizationVerifyTaskEntity::getOrganizationId).collect(Collectors.toList()));
        }


        if (!CollectionUtils.isEmpty(organizationIds)) {
            ParentRequest request = new ParentRequest();
            request.setIsExclude(1);
            request.setIsIncludingSelf(0);
            request.setOrganizationIds(organizationIds);
            List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByOrganizationIds(request);
            Map<String, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getOrganizationId));

            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDate nowDate = nowDateTime.toLocalDate();

            if (!CollectionUtils.isEmpty(auditAuthorizationVerifyList)) {
                auditAuthorizationVerifyList.forEach(auditAuthorizationVerify -> {

                    SfaAuditProcessTaskRecord auditProcessTaskRecord = new SfaAuditProcessTaskRecord();
                    auditProcessTaskRecord.init("", "");
                    auditProcessTaskRecord.setTaskDate(nowDate);
                    auditProcessTaskRecord.setType(4);
                    auditProcessTaskRecord.setProcessId(auditAuthorizationVerify.getTaskId());
                    auditProcessTaskRecord.setOrganizationIdFrom(auditAuthorizationVerify.getOrganizationId());

                    List<ParentVo> parentVoList1 = parentMap.get(auditAuthorizationVerify.getOrganizationId());
                    // 子流程处理阶段(0.无子流程 10.区域经理审核 20.区域总监审核 30.省区总监审核 40.大区总监审核 50.总督导审核 60.营运审核)
                    if (!CollectionUtils.isEmpty(parentVoList1)) {
                        auditAuthorizationVerify.setSubProcessStep(AuthorizeTaskProcessStepEnum.getProcessStep(parentVoList1.get(0).getParentPositionTypeId()));
                        auditAuthorizationVerify.setPositionId(parentVoList1.get(0).getParentPositionId());
                        auditAuthorizationVerify.setOrganizationId(parentVoList1.get(0).getParentOrganizationId());
                        auditProcessTaskRecord.setOrganizationIdTo(auditAuthorizationVerify.getOrganizationId());
                    } else {
                        auditAuthorizationVerify.setSubProcessStep(60);
                        auditAuthorizationVerify.setPositionId(null);
                        String[] organizationArr = auditAuthorizationVerify.getOrganizationId().split("_");
                        String organizationId = "ZB_Z_" + organizationArr[organizationArr.length - 1];
                        auditAuthorizationVerify.setOrganizationId(organizationId);
                        auditProcessTaskRecord.setOrganizationIdTo(organizationId);
                    }
                    authorizationVerifyTaskMapper.updateAuditAuthorizationVerify(auditAuthorizationVerify);
                    sfaAuditProcessTaskRecordMapper.insert(auditProcessTaskRecord);
                });
            }

        }
        log.info("审核流程更新审核人自动定时任务结束..");
        return ReturnT.SUCCESS;
    }
}
