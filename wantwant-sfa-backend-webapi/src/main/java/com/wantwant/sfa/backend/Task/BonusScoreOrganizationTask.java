package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.impl.BonusEvaluationServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class BonusScoreOrganizationTask {

    @Autowired
    private BonusEvaluationServiceImpl bonusEvaluationServiceImpl;

    //每季度 第一个月 1,4,7,10 月，11号凌晨12.30跑  处理的是大区，分公司
    @XxlJob("bonusOrganizationScore")
    public ReturnT<String> taskBonusOrganizationScore(String param){
        XxlJobLogger.log("start BonusScoreDepartmentOrganizationTask taskBonusOrganizationScore time:{}", LocalDateTime.now());
        log.info("start BonusScoreDepartmentOrganizationTask taskBonusOrganizationScore time:{}",LocalDateTime.now());
        bonusEvaluationServiceImpl.performanceEvaluatioOrganizationTask();
        return ReturnT.SUCCESS;
    }
}
