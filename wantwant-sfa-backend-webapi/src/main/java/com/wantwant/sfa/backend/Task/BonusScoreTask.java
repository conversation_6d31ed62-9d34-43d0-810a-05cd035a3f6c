package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.impl.BonusEvaluationServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class BonusScoreTask {

    @Autowired
    private BonusEvaluationServiceImpl bonusEvaluationServiceImpl;

    //每月11号跑评分
    @XxlJob("bonusScore")
    public ReturnT<String> taskBonusScore(String param){
        XxlJobLogger.log("start PerformanceBonusScoreTask taskBonusScore time:{}", LocalDateTime.now());
        log.info("start PerformanceBonusScoreTask taskBonusScore time:{}",LocalDateTime.now());
        bonusEvaluationServiceImpl.performanceEvaluatioTask();
        return ReturnT.SUCCESS;
    }
}
