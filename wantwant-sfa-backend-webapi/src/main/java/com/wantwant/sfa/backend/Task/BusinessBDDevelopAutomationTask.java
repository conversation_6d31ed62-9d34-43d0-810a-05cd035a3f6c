package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.application.JobTransferApplication;
import com.wantwant.sfa.backend.application.businessBd.BusinessBdOrgQuotaAppService;
import com.wantwant.sfa.backend.businessBd.request.QuotaAdjustRequest;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.domain.businessBd.DO.*;
import com.wantwant.sfa.backend.domain.businessBd.enums.QuotaControlLogTypeEnum;
import com.wantwant.sfa.backend.domain.businessBd.enums.QuotaOperationTypeEnum;
import com.wantwant.sfa.backend.domain.businessBd.mapper.BusinessBdSalaryControlMapper;
import com.wantwant.sfa.backend.domain.businessBd.repository.facade.BusinessBdOrgQuotaRepository;
import com.wantwant.sfa.backend.domain.businessBd.repository.facade.IBusinessBdSalaryHistoryRepository;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryControlPO;
import com.wantwant.sfa.backend.domain.businessBd.repository.po.BusinessBdSalaryHistoryPO;
import com.wantwant.sfa.backend.domain.businessBd.service.BusinessBdSalaryControlService;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDDO;
import com.wantwant.sfa.backend.domain.emp.mapper.BusinessBDConfigMapper;
import com.wantwant.sfa.backend.domain.emp.mapper.BusinessBDRuleMapper;
import com.wantwant.sfa.backend.domain.emp.repository.facade.EmpRepositoryInterface;
import com.wantwant.sfa.backend.domain.emp.repository.model.BusinessBDCompileDetailModel;
import com.wantwant.sfa.backend.domain.emp.repository.persistence.EmpBigTableRepository;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdRulePO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.ChangeBusinessBDTypeDO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.ChangeBusinessGroupDO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.JobTransferDO;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.ChangeColumnEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferEnum;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaJobPositionTaskMapper;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionActionMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionApplyMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionProcessMapper;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionActionEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessEntity;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/11/上午9:04
 */
@Component
@Slf4j
public class BusinessBDDevelopAutomationTask {

    @Resource
    private EmpBigTableRepository empBigTableRepository;
    @Resource
    private EmpRepositoryInterface empRepositoryInterface;
    @Resource
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Resource
    private SfaTransactionApplyMapper sfaTransactionApplyMapper;
    @Resource
    private SfaTransactionActionMapper sfaTransactionActionMapper;
    @Resource
    private SfaJobPositionTaskMapper sfaJobPositionTaskMapper;
    @Resource
    private JobTransferApplication jobTransferApplication;
    @Resource
    private SfaTransactionProcessMapper sfaTransactionProcessMapper;
    @Resource
    private BusinessBDConfigMapper businessBDConfigMapper;
    @Resource
    private BusinessBdOrgQuotaRepository businessBdOrgQuotaRepository;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private BusinessBDRuleMapper businessBDRuleMapper;
    @Resource
    private BusinessBdOrgQuotaAppService businessBdOrgQuotaAppService;
    @Resource
    private EmployeeSalaryMapper employeeSalaryMapper;
    @Value("${businessBD.FeeStartDate:21}")
    public String BUSINESS_BD_FEE_START_DATE;
    @Resource
    private BusinessBdSalaryControlMapper businessBdSalaryControlMapper;
    @Resource
    private BusinessBdSalaryControlService businessBdSalaryControlService;
    @Resource
    private IBusinessBdSalaryHistoryRepository businessBdSalaryHistoryRepository;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    /**
     * 业务BD编织检查
     *
     * @param param
     * @return
     */
    @XxlJob("businessBDDevelopAutomatic")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> businessBDDevelopAutomatic(String param) {
        // 获取执行日期
        LocalDate date = getDate(param);
        log.info("【business bd develop automatic】考察月份:{}", date.toString());

        // 读取配置文件
        List<BusinessBdConfigPO> businessBdConfigPOS = businessBDConfigMapper.selectList(new LambdaQueryWrapper<BusinessBdConfigPO>().eq(BusinessBdConfigPO::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(businessBdConfigPOS)) {
            log.info("【business bd develop automatic】config empty");
            return ReturnT.SUCCESS;
        }

        // 业务BD按合伙人检查
        List<BusinessBdConfigPO> ceoConfigList = businessBdConfigPOS.stream().filter(f -> f.getCheckType() == 1).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ceoConfigList)) {
            checkCEO(date, ceoConfigList);
        }

        // 按组织检查
        List<BusinessBdConfigPO> orgConfigList = businessBdConfigPOS.stream().filter(f -> f.getCheckType() == 2).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgConfigList)) {
            checkOrg(date, orgConfigList);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 业务BD外部额度检查
     *
     * @param param
     * @return
     */
    @XxlJob("addExternalQuota")
    @Transactional
    public ReturnT<String> addExternalQuota(String param) {
        // 获取执行日期
        LocalDate date = getDate(param);
        log.info("【business bd develop automatic】考察月份:{}", date.toString());
        // 目前只适用于A组
        List<Integer> businessGroupIds = Collections.singletonList(1);

        List<SfaBusinessGroupEntity> sfaBusinessGroupEntities = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>().in(SfaBusinessGroupEntity::getId, businessGroupIds));
        if (CollectionUtils.isEmpty(sfaBusinessGroupEntities)) {
            return ReturnT.SUCCESS;
        }


        List<String> businessGroupCodes = sfaBusinessGroupEntities.stream().map(SfaBusinessGroupEntity::getBusinessGroupCode).collect(Collectors.toList());
        // 读取大数据数据
        List<ExternalQuota> list = empBigTableRepository.selectExternalQuota(date.toString().substring(0, 7),businessGroupCodes);
        if (CollectionUtils.isEmpty(list)) {
            return ReturnT.SUCCESS;
        }

        for (ExternalQuota externalQuota : list) {
            QuotaAdjustRequest quotaAdjustRequest = QuotaAdjustRequest.builder().empId("ROOT")
                    .amount(externalQuota.getQuota())
                    .type(QuotaControlLogTypeEnum.EXTERNAL_QUOTA_ADJUST.getCode())
                    .organizationId(externalQuota.getDepartmentId())
                    .processDate(date).build();
            businessBdOrgQuotaAppService.adjustQuota(quotaAdjustRequest);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 检查在职人员编制
     *
     * @param param
     * @return
     */
    @XxlJob("checkStaffingOccupancy")
    @Transactional
    public ReturnT<String> checkStaffingOccupancy(String param) {
        // 获取执行日期,当前日期的前一个月的21号
        LocalDate date = getCheckStaffingOccupancyDate(param);
        log.info("checkStaffingOccupancy startDate:{}", date.toString());

        // 读取配置文件
        List<BusinessBdConfigPO> businessBdConfigPOS = businessBDConfigMapper.selectList(new LambdaQueryWrapper<BusinessBdConfigPO>().eq(BusinessBdConfigPO::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(businessBdConfigPOS)) {
            log.info("【business bd develop automatic】config empty");
            return ReturnT.SUCCESS;
        }

        // 按组织检查
        List<BusinessBdConfigPO> orgConfigList = businessBdConfigPOS.stream().filter(f -> f.getCheckType() == 2).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgConfigList)) {
            return ReturnT.SUCCESS;
        }

        List<String> departmentCodes = orgConfigList.stream().filter(f -> StringUtils.isNotBlank(f.getDepartmentId())).map(BusinessBdConfigPO::getDepartmentId).collect(Collectors.toList());

        for (String departmentCode : departmentCodes) {
            // 根据组织获取所有承揽BD和全职BD
            List<BusinessBD> list = businessBdOrgQuotaRepository.selectBDServer(departmentCode, date);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            for (BusinessBD businessBD : list) {
                PositionEnum curPosition = PositionEnum.getEnum(businessBD.getCeoType(), businessBD.getJobsType(), businessBD.getPosition());


                LocalDate now = LocalDate.now();
                if (now.getDayOfMonth() >= Integer.parseInt(BUSINESS_BD_FEE_START_DATE)) {
                    now = now.plusMonths(1L).withDayOfMonth(1);
                }
                LocalDate onBoardDate = businessBD.getOnBoardDate().withDayOfMonth(1);

                long monthsDiff = ChronoUnit.MONTHS.between(onBoardDate, now) + 1;

                BusinessBdEmployeeQuotaOperationValue businessBdEmployeeQuotaOperationValue = BusinessBdEmployeeQuotaOperationValue.builder()
                        .employeeInfoId(businessBD.getEmployeeInfoId())
                        .organizationId(departmentCode)
                        .monthValue(monthsDiff)
                        .theYearMonth(date.toString())
                        .curPositionId(curPosition.getId()).type(QuotaOperationTypeEnum.ON_BOARD.getCode())
                        .userId("ROOT").userName("系统自动").build();
                try {
                    businessBdOrgQuotaAppService.adjustQuotaByEmployeeOperation(businessBdEmployeeQuotaOperationValue);
                } catch (Exception e) {
                    log.error("更新失败:{}", e.getMessage());
                }
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 业务BD额度管控结余
     *
     * @param param
     * @return
     */
    @XxlJob("checkCEOBusinessBDEstablishment")
    @Transactional
    public ReturnT<String> checkCEOBusinessBDEstablishment(String param) {
        // 获取执行日期
        LocalDate date = getDate(param);

        // 执行日期设置为每月1号
        date = date.withDayOfMonth(1);

        log.info("checkCEOBusinessBDEstablishment startDate:{}", date.toString());
        // 如果执行日期是季度第一个月，则不做任何处理
//        if (date.getMonthValue() == 1 || date.getMonthValue() == 4 || date.getMonthValue() == 7 || date.getMonthValue() == 10) {
//            log.info("季度第一个月不做处理");
//            return ReturnT.SUCCESS;
//        }
        // 读取配置文件
        List<BusinessBdConfigPO> businessBdConfigPOS = businessBDConfigMapper.selectList(new LambdaQueryWrapper<BusinessBdConfigPO>().eq(BusinessBdConfigPO::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(businessBdConfigPOS)) {
            log.info("【business bd develop automatic】config empty");
            return ReturnT.SUCCESS;
        }


        LocalDate finalDate = date;
        LocalDate startDate = date.minusMonths(1L);
        List<BusinessBdConfigPO> businessBdConfigPOList = businessBdConfigPOS.stream().filter(f -> f.getCheckType() == 3).collect(Collectors.toList());

        for (BusinessBdConfigPO businessBdConfigPO : businessBdConfigPOList){

            String departmentId = businessBdConfigPO.getDepartmentId();
            // 根据组织获取承揽BD，全职BD上月在职过的额度
            List<BusinessBdSalaryHistoryPO> businessBdSalaryHistoryPOS = Optional.ofNullable(businessBdSalaryHistoryRepository.selectByOrgIdAndDate(departmentId, startDate.toString().substring(0, 7))).orElse(new ArrayList<>());

            AtomicReference<BigDecimal> totalSalary = new AtomicReference<>(BigDecimal.ZERO);

            // 计算总额度
            businessBdSalaryHistoryPOS.stream().filter(Objects::nonNull).forEach(s -> {
                BigDecimal employeeBaseSalary = Optional.ofNullable(s.getEmployeeBaseSalary()).orElse(BigDecimal.ZERO);
                BigDecimal socialSecurityBase = Optional.ofNullable(s.getSocialSecurityBase()).orElse(BigDecimal.ZERO);
                BigDecimal travelExpenses = Optional.ofNullable(s.getTravelExpenses()).orElse(BigDecimal.ZERO);
                BigDecimal fullRiskFee = Optional.ofNullable(s.getFullRiskFee()).orElse(BigDecimal.ZERO);
                BigDecimal employeeBonus = Optional.ofNullable(s.getEmployeeBonus()).orElse(BigDecimal.ZERO);
                BigDecimal employeeAllowance = Optional.ofNullable(s.getEmployeeAllowance()).orElse(BigDecimal.ZERO);

                BigDecimal empSalary = employeeBaseSalary.add(employeeAllowance).add(socialSecurityBase)
                        .add(employeeBonus).add(travelExpenses).add(fullRiskFee);
                log.info("getEmpSalary deptCode:{},employeeInfoId:{} totalSalary:{}", departmentId, s.getEmployeeInfoId(), empSalary);
                BigDecimal result = totalSalary.get().add(empSalary);
                totalSalary.set(result);
            });

            log.info("total salary deptCode:{},totalSalary:{}", departmentId, totalSalary.get());

            // 获取组织额度管控
            BusinessBdSalaryControlPO lastMonthSalaryPo = Optional.ofNullable(businessBdSalaryControlMapper.selectOne(new LambdaQueryWrapper<BusinessBdSalaryControlPO>().eq(BusinessBdSalaryControlPO::getOrganizationId, departmentId)
                    .eq(BusinessBdSalaryControlPO::getTheYearMonth, finalDate.minusMonths(1L).toString().substring(0, 7))
                    .eq(BusinessBdSalaryControlPO::getDeleteFlag, 0)
                    .last("limit 1")
            )).orElse(BusinessBdSalaryControlPO.builder().avgSalaryPackage(BigDecimal.ZERO).lastMonthBalance(BigDecimal.ZERO).build());


            // 上月额度
            BigDecimal lastMonthSalary = lastMonthSalaryPo.getLastMonthBalance().add(lastMonthSalaryPo.getAvgSalaryPackage());
            log.info("last month salary deptCode:{},lastMonthSalary:{}", departmentId, lastMonthSalary);

            // 结余
            BigDecimal establishBalance = lastMonthSalary.subtract(totalSalary.get());
            if(establishBalance.compareTo(BigDecimal.ZERO) < 0) {
                establishBalance = BigDecimal.ZERO;
            }

            // 获取组织额度管控
            BusinessBdSalaryControlPO salaryPo = Optional.ofNullable(businessBdSalaryControlMapper.selectOne(new LambdaQueryWrapper<BusinessBdSalaryControlPO>().eq(BusinessBdSalaryControlPO::getOrganizationId, departmentId)
                    .eq(BusinessBdSalaryControlPO::getTheYearMonth, finalDate.toString().substring(0, 7))
                    .eq(BusinessBdSalaryControlPO::getDeleteFlag, 0)
                    .last("limit 1")
            )).orElse(BusinessBdSalaryControlPO.builder().avgSalaryPackage(BigDecimal.ZERO).lastMonthBalance(BigDecimal.ZERO).build());

            if(establishBalance.compareTo(BigDecimal.ZERO) > 0){
                businessBdSalaryControlService.saveOrUpdate(BusinessBdSalaryControlDO.builder().organizationId(departmentId)
                        .lastMonthBalance(establishBalance)
                        .processUserId("ROOT").processUserName("系统自动")
                        .avgSalaryPackage(salaryPo.getAvgSalaryPackage())
                        .theYearMonth(date.toString().substring(0, 7))
                        .build());
            }

        }

        return ReturnT.SUCCESS;
    }

    @XxlJob("saveBdSalarySnapshot")
    @Transactional
    public ReturnT<String> saveBdSalarySnapshot(String param){
        String theYearMonth = getDate(param).toString().substring(0, 7);
        log.info("save bd salary snapshot theYearMonth:{}",theYearMonth);

        List<BusinessBdSalaryHistoryPO> businessBdSalaryHistoryPOS = new ArrayList<>(Optional.ofNullable(employeeSalaryMapper.selectBusinessBdSnapshot(theYearMonth)).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(BusinessBdSalaryHistoryPO::getEmployeeInfoId, item -> item, (existing, replacement) -> existing))
                .values());

        if (CollectionUtils.isEmpty(businessBdSalaryHistoryPOS)) {
            log.info("save bd salary list is empty");
            return ReturnT.SUCCESS;
        }
        // 防止重复保存,先删除历史
        businessBdSalaryHistoryRepository.deleteByTheYearMonth(theYearMonth);


        // 批量保存
        businessBdSalaryHistoryRepository.batchInsert(businessBdSalaryHistoryPOS);

        return ReturnT.SUCCESS;
    }

    private LocalDate getCheckStaffingOccupancyDate(String param) {

        LocalDate currentDate = LocalDate.now();
        if (StringUtils.isNotBlank(param)) {
            currentDate = LocalDate.parse(param);
        }


        return currentDate.withDayOfMonth(Integer.parseInt(BUSINESS_BD_FEE_START_DATE)).minusMonths(1L);
    }


    private void checkOrg(LocalDate date, List<BusinessBdConfigPO> orgConfigList) {
        List<List<BusinessBdConfigPO>> partition = ListUtils.partition(orgConfigList, 50);
        for (List<BusinessBdConfigPO> businessBdConfigPOS : partition) {

            for (BusinessBdConfigPO businessBdConfigPO : businessBdConfigPOS) {
                BusinessBdOrgQuotaControlDO businessBdOrgQuotaControlDO = businessBdOrgQuotaRepository.findByOrganizationId(businessBdConfigPO.getDepartmentId(), date.toString().substring(0, 7));
                // 获取分公司CODE
                String companyCode = organizationMapper.getCompanyCodeByDepartmentId(businessBdConfigPO.getDepartmentId());
                // 获取配置规则
                BusinessBdRulePO businessBdRulePO = businessBDRuleMapper.selectOne(new LambdaQueryWrapper<BusinessBdRulePO>()
                        .le(BusinessBdRulePO::getTheYearMonth, date.toString().substring(0, 7))
                        .eq(BusinessBdRulePO::getCompanyCode, companyCode)
                        .eq(BusinessBdRulePO::getDeleteFlag, 0)
                        .orderByDesc(BusinessBdRulePO::getRuleId)
                        .last("limit 1")
                );
                if (Objects.isNull(businessBdRulePO)) {
                    log.info("未配置分公司额度,分公司CODE:{}", companyCode);
                    continue;
                }

                // 计算全职BD和承揽BD的比例
                BigDecimal fullTimePerformanceRequire = businessBdRulePO.getFullTimePerformanceRequire();
                BigDecimal contractPerformanceRequire = businessBdRulePO.getContractPerformanceRequire();
                if (fullTimePerformanceRequire.compareTo(BigDecimal.ZERO) <= 0 || contractPerformanceRequire.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("【business bd develop automatic】配置文件错误,全职BD业绩要求:{},承揽BD业绩要求:{}", fullTimePerformanceRequire, contractPerformanceRequire);
                    continue;
                }
                BigDecimal ratio = fullTimePerformanceRequire.divide(contractPerformanceRequire, 10, RoundingMode.DOWN);

                if (Objects.isNull(businessBdOrgQuotaControlDO)) {
                    log.info("组织CODE:{},未配置额度", businessBdConfigPO.getDepartmentId());
                    continue;
                }
                // 剩余额度
                BigDecimal remainingQuota = businessBdOrgQuotaControlDO.getRemainingQuota();

                // 自动汰换
                if (remainingQuota.compareTo(BigDecimal.ZERO) <= 0) {
                    // 根据组织获取所有承揽BD和全职BD
                    List<BusinessBD> list = businessBdOrgQuotaRepository.selectBDServer(businessBdOrgQuotaControlDO.getOrganizationId(), null);
                    if (CollectionUtils.isEmpty(list)) {
                        continue;
                    }

                    List<Integer> employeeInfoIds = list.stream().map(BusinessBD::getEmployeeInfoId).collect(Collectors.toList());

                    // 检查是否处于离职流程中
                    List<Integer> resignEmpInfoIds = Optional.ofNullable(businessBdOrgQuotaRepository.selectResignProcess(employeeInfoIds)).orElse(new ArrayList<>());

                    // 检查是否处于异动流程中
                    List<Integer> transactionProcessingEmpInfoIds = Optional.ofNullable(businessBdOrgQuotaRepository.selectTransactionProcessing(employeeInfoIds)).orElse(new ArrayList<>());

                    // 检查处于定时任务中的数据
                    List<Integer> processingEmpInfoIds = Optional.ofNullable(businessBdOrgQuotaRepository.selectProcessing(employeeInfoIds)).orElse(new ArrayList<>());

                    log.info("离职人员名单:{},异动处理中人员名单:{},待定时任务执行人员名单:{}", resignEmpInfoIds, transactionProcessingEmpInfoIds, processingEmpInfoIds);
                    // 可操作人员名单
                    List<BusinessBD> blackList = new ArrayList<>();

                    // 根据配比将离职中，异动中，定时任务中的额度加到剩余额度中
                    for (BusinessBD businessBD : list) {
                        PositionEnum curPosition = PositionEnum.getEnum(businessBD.getCeoType(), businessBD.getJobsType(), businessBD.getPosition());
                        Optional<Integer> resignOptional = resignEmpInfoIds.stream().filter(f -> businessBD.getEmployeeInfoId().equals(f)).findFirst();
                        Optional<Integer> transactionProcessingOptional = transactionProcessingEmpInfoIds.stream().filter(f -> businessBD.getEmployeeInfoId().equals(f)).findFirst();
                        Optional<Integer> processingOptional = processingEmpInfoIds.stream().filter(f -> businessBD.getEmployeeInfoId().equals(f)).findFirst();
                        if (resignOptional.isPresent()) {
                            remainingQuota = remainingQuota.add(calculateQuota(curPosition, ratio));
                        } else if (transactionProcessingOptional.isPresent()) {
                            remainingQuota = remainingQuota.add(calculateQuota(curPosition, ratio));
                        } else if (processingOptional.isPresent()) {
                            remainingQuota = remainingQuota.add(calculateQuota(curPosition, ratio));
                        } else {
                            blackList.add(businessBD);
                        }
                    }

                    log.info("实际剩余编织数:{}", remainingQuota);

                    // 再次检查剩余额度是否小于0，小于0则进行自动转兼职
                    if (remainingQuota.compareTo(BigDecimal.ZERO) < 0 && !CollectionUtils.isEmpty(blackList)) {
                        for (BusinessBD businessBD : blackList) {
                            // 额度已足够，无需转兼职
                            if (remainingQuota.compareTo(BigDecimal.ZERO) >= 0) {
                                return;
                            }

                            JobTransferDO jobTransferDO = new JobTransferDO();
                            jobTransferDO.setEmployeeInfoId(businessBD.getEmployeeInfoId());
                            jobTransferDO.setReason("超出编制,自动执行异动");
                            jobTransferDO.setType(JobTransferEnum.CHANGE_BD_POSITION.getType());

                            ChangeBusinessBDTypeDO changeBusinessBDTypeDO = new ChangeBusinessBDTypeDO();
                            changeBusinessBDTypeDO.setId(PositionEnum.BUSINESS_BD_PART_TIME.getId());
                            changeBusinessBDTypeDO.setName(PositionEnum.BUSINESS_BD_PART_TIME.getPositionName());
                            jobTransferDO.setChangeBusinessBDType(changeBusinessBDTypeDO);

                            // 设置产品组
                            ChangeBusinessGroupDO changeBusinessGroupDO = new ChangeBusinessGroupDO();
                            changeBusinessGroupDO.setBusinessGroup(businessBD.getBusinessGroup());
                            jobTransferDO.setChangeBusinessGroupDO(changeBusinessGroupDO);

                            // 当前编制数
                            BigDecimal currentRatio = BigDecimal.ZERO;

                            jobTransferDO.setAdviceExecuteDate(LocalDate.now());
                            jobTransferApplication.jobTransferApply(jobTransferDO, "ROOT");
                            PositionEnum anEnum = PositionEnum.getEnum(businessBD.getCeoType(), businessBD.getJobsType(), businessBD.getPosition());
                            if (Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
                                currentRatio = BigDecimal.ONE;
                            } else if (Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD.getId())) {
                                currentRatio = ratio;
                            }

                            remainingQuota = remainingQuota.add(currentRatio);
                        }
                    }
                }
            }

        }
    }

    private BigDecimal calculateQuota(PositionEnum curPosition, BigDecimal ratio) {
        if (curPosition.getId().equals(PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
            return BigDecimal.ONE;
        }

        return BigDecimal.ONE.multiply(ratio);
    }

    private void checkCEO(LocalDate date, List<BusinessBdConfigPO> businessBdConfigPOList) {

        List<List<BusinessBdConfigPO>> partition = ListUtils.partition(businessBdConfigPOList, 50);

        for (List<BusinessBdConfigPO> businessBdConfigPOS : partition) {

            List<String> orgCodes = businessBdConfigPOS.stream().map(BusinessBdConfigPO::getDepartmentId).collect(Collectors.toList());

            // 获取超编制的数量
            List<BusinessBDCompileDetailModel> exceedEstablished = Optional.ofNullable(empBigTableRepository.getExceedEstablished(date.toString().substring(0, 7), orgCodes)).orElse(new ArrayList<>());
            log.info("【business bd develop automatic】exceedEstablished size:{}", exceedEstablished.size());
            if (CollectionUtils.isEmpty(exceedEstablished)) {
                return;
            }


            exceedEstablished.forEach(e -> {
                Long memberKey = e.getMemberKey();
                String businessGroupCode = e.getBusinessGroupCode();
                BigDecimal remainderConfigurable = e.getRemainderConfigurable();
                BigDecimal convertedRatio = e.getConvertedRatio();
                log.info("【business bd develop automatic】memberKey:{},businessGroupCode:{},remainderConfigurable:{},convertedRatio:{}", memberKey, businessGroupCode, remainderConfigurable, convertedRatio);

                // 记录下未执行异动的人员
                List<BusinessBDDO> list = new ArrayList<>();

                if (remainderConfigurable.compareTo(BigDecimal.ZERO) < 0) {
                    // 根据服务对象及产品组获取全职/承揽业务BD
                    List<BusinessBDDO> businessBDDOS = Optional.ofNullable(empRepositoryInterface.selectBusinessBDByMemberKey(memberKey, businessGroupCode)).orElse(new ArrayList<>());
                    log.info("【business bd develop automatic】total businessBD count :{}", businessBDDOS.size());

                    for (BusinessBDDO emp : businessBDDOS) {
                        if (remainderConfigurable.compareTo(BigDecimal.ZERO) >= 0) {
                            return;
                        }
                        // 检查是否处于离职流程中
                        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new LambdaQueryWrapper<SfaInterviewProcessModel>().eq(SfaInterviewProcessModel::getApplicationId, emp.getApplyId()));
                        boolean resignProcess = sfaInterviewProcessModel.getProcessType() > 6 ? true : false;

                        // 检查是否处于异动流程中
                        List<Integer> transactionProcessId = Optional.ofNullable(sfaTransactionApplyMapper.selectProcessingApply(emp.getEmployeeInfoId())).orElse(new ArrayList<>());
                        boolean transactionProcess = transactionProcessId.size() > 0 ? true : false;

                        // 检查处于定时任务中的数据
                        List<SfaJobPositionTask> jobTaskCount = getJobTaskCount(emp.getEmployeeInfoId());
                        boolean jobTaskProcess = jobTaskCount.size() > 0 ? true : false;

                        PositionEnum anEnum = PositionEnum.getEnum(emp.getCeoType(), emp.getJobsType(), emp.getPosition());
                        log.info("【business bd develop automatic】检查人员信息,手机号:{},身份:{},是否处于离职流程中:{},是否处于异动流程中:{},是否处于定时任务中:{}"
                                , emp.getMobile(), anEnum.getPositionName(), resignProcess, transactionProcess, jobTaskProcess);

                        // 当前编制数
                        BigDecimal currentRatio = BigDecimal.ZERO;

                        // 执行异动申请
                        if (!resignProcess && !transactionProcess && !jobTaskProcess) {
                            if (!Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD_PART_TIME.getId())) {
                                list.add(emp);
                            }
                        } else if (resignProcess) {
                            if (Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
                                currentRatio = BigDecimal.ONE;
                            } else if (Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD.getId())) {
                                currentRatio = convertedRatio;
                            }
                        } else if (transactionProcess) {
                            Integer processId = transactionProcessId.stream().findFirst().get();
                            // 获取异动ID
                            SfaTransactionProcessEntity sfaTransactionProcessEntity = sfaTransactionProcessMapper.selectById(processId);

                            currentRatio = getTransactionRatio(convertedRatio, anEnum, currentRatio, sfaTransactionProcessEntity.getTransactionApplyId());
                        } else if (jobTaskProcess) {
                            SfaJobPositionTask sfaJobPositionTask = jobTaskCount.stream().findFirst().get();
                            currentRatio = getTransactionRatio(convertedRatio, anEnum, currentRatio, sfaJobPositionTask.getTransactionId());
                        }

                        remainderConfigurable = remainderConfigurable.add(currentRatio);
                        log.info("【business bd develop automatic】剩余编制数:{}", remainderConfigurable);
                    }

                    // 检查需要执行异动的人员
                    if (remainderConfigurable.compareTo(BigDecimal.ZERO) < 0) {
                        for (BusinessBDDO emp : list) {

                            if (remainderConfigurable.compareTo(BigDecimal.ZERO) >= 0) {
                                return;
                            }

                            JobTransferDO jobTransferDO = new JobTransferDO();
                            jobTransferDO.setEmployeeInfoId(emp.getEmployeeInfoId());
                            jobTransferDO.setReason("超出编制,自动执行异动");
                            jobTransferDO.setType(JobTransferEnum.CHANGE_BD_POSITION.getType());

                            ChangeBusinessBDTypeDO changeBusinessBDTypeDO = new ChangeBusinessBDTypeDO();
                            changeBusinessBDTypeDO.setId(PositionEnum.BUSINESS_BD_PART_TIME.getId());
                            changeBusinessBDTypeDO.setName(PositionEnum.BUSINESS_BD_PART_TIME.getPositionName());
                            jobTransferDO.setChangeBusinessBDType(changeBusinessBDTypeDO);

                            // 设置产品组
                            ChangeBusinessGroupDO changeBusinessGroupDO = new ChangeBusinessGroupDO();
                            changeBusinessGroupDO.setBusinessGroup(emp.getBusinessGroup());
                            jobTransferDO.setChangeBusinessGroupDO(changeBusinessGroupDO);

                            // 当前编制数
                            BigDecimal currentRatio = BigDecimal.ZERO;

                            jobTransferDO.setAdviceExecuteDate(LocalDate.now());
                            jobTransferApplication.jobTransferApply(jobTransferDO, "ROOT");
                            PositionEnum anEnum = PositionEnum.getEnum(emp.getCeoType(), emp.getJobsType(), emp.getPosition());
                            if (Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
                                currentRatio = BigDecimal.ONE;
                            } else if (Objects.equals(anEnum.getId(), PositionEnum.BUSINESS_BD.getId())) {
                                currentRatio = convertedRatio;
                            }

                            remainderConfigurable = remainderConfigurable.add(currentRatio);
                            log.info("【business bd develop automatic】剩余编制数:{}", remainderConfigurable);
                        }
                    }
                }
            });
        }


    }

    private BigDecimal getTransactionRatio(BigDecimal convertedRatio, PositionEnum currentPosition, BigDecimal currentRatio, Long transactionId) {
        SfaTransactionApplyEntity sfaTransactionApplyEntity = sfaTransactionApplyMapper.selectById(transactionId);
        Integer transactionType = sfaTransactionApplyEntity.getTransactionType();
        if (transactionType == JobTransferEnum.CHANGE_BD_POSITION.getType()) {

            List<SfaTransactionActionEntity> sfaTransactionActionEntities = sfaTransactionActionMapper.selectList(new LambdaQueryWrapper<SfaTransactionActionEntity>()
                    .eq(SfaTransactionActionEntity::getTransactionId, transactionId).eq(SfaTransactionActionEntity::getDeleteFlag, 0));

            Optional<SfaTransactionActionEntity> positionOptional = sfaTransactionActionEntities.stream().filter(f -> f.getChangeColumn().equals(ChangeColumnEnum.POSITION.getColumn()) && StringUtils.isNotBlank(f.getTransactionValue())).findFirst();
            if (!positionOptional.isPresent()) {
                throw new ApplicationException("异动信息异常");
            }
            Optional<SfaTransactionActionEntity> jobsTypeOptional = sfaTransactionActionEntities.stream().filter(f -> f.getChangeColumn().equals(ChangeColumnEnum.JOBS_TYPE.getColumn()) && StringUtils.isNotBlank(f.getTransactionValue())).findFirst();
            if (!jobsTypeOptional.isPresent()) {
                throw new ApplicationException("异动信息异常");
            }
            Optional<SfaTransactionActionEntity> ceoTypeOptional = sfaTransactionActionEntities.stream().filter(f -> f.getChangeColumn().equals(ChangeColumnEnum.CEO_TYPE.getColumn()) && StringUtils.isNotBlank(f.getTransactionValue())).findFirst();
            if (!ceoTypeOptional.isPresent()) {
                throw new ApplicationException("异动信息异常");
            }

            PositionEnum newPosition = PositionEnum.getEnum(Integer.parseInt(ceoTypeOptional.get().getTransactionValue()),
                    Integer.parseInt(jobsTypeOptional.get().getTransactionValue()),
                    Integer.parseInt(positionOptional.get().getTransactionValue())
            );

            if (Objects.equals(newPosition.getId(), PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
                currentRatio = BigDecimal.ONE;
            } else if (Objects.equals(newPosition.getId(), PositionEnum.BUSINESS_BD.getId())) {
                currentRatio = convertedRatio;
            } else if (Objects.equals(newPosition.getId(), PositionEnum.BUSINESS_BD_PART_TIME.getId())) {
                if (Objects.equals(currentPosition.getId(), PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
                    currentRatio = BigDecimal.ONE;
                } else if (Objects.equals(currentPosition.getId(), PositionEnum.BUSINESS_BD.getId())) {
                    currentRatio = convertedRatio;
                }
            }

        } else {
            if (Objects.equals(currentPosition.getId(), PositionEnum.BUSINESS_BD_CONTRACT.getId())) {
                currentRatio = BigDecimal.ONE;
            } else if (Objects.equals(currentPosition.getId(), PositionEnum.BUSINESS_BD.getId())) {
                currentRatio = convertedRatio;
            }
        }
        return currentRatio;
    }

    private List<SfaJobPositionTask> getJobTaskCount(Integer employeeInfoId) {
        // 异动单获取
        List<SfaTransactionApplyEntity> transactionList = sfaTransactionApplyMapper.selectList(new QueryWrapper<SfaTransactionApplyEntity>().eq("employee_info_id", employeeInfoId));
        // 检查有没有异动中的任务
        if (!CollectionUtils.isEmpty(transactionList)) {
            List<Long> collect = transactionList.stream().map(SfaTransactionApplyEntity::getId).collect(Collectors.toList());
            return Optional.ofNullable(sfaJobPositionTaskMapper.selectList(new QueryWrapper<SfaJobPositionTask>().in("transaction_id", collect).eq("status", 0))).orElse(new ArrayList<>());
        }

        return new ArrayList<>();
    }

    private LocalDate getDate(String param) {
        // 获取上月
        if (StringUtils.isBlank(param)) {
            return LocalDate.now();
        }

        return LocalDate.parse(param);
    }
}
