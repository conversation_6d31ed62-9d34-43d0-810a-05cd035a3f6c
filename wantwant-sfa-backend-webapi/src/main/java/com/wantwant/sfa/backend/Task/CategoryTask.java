package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.entity.ProductionGroupEntity;
import com.wantwant.sfa.backend.mapper.ProductionGroupMapper;
import com.wantwant.sfa.backend.model.CategoryModel;
import com.wantwant.sfa.backend.util.CategoryConnectorUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/21/上午10:54
 */
@Component
@Slf4j
public class CategoryTask {

    @Autowired
    private CategoryConnectorUtil categoryConnectorUtil;
    @Autowired
    private ProductionGroupMapper productionGroupMapper;

    @XxlJob("saveCategory")
    @Transactional
    public ReturnT<String> excute(String param) {
        XxlJobLogger.log("get all category start");
        List<CategoryModel> category = categoryConnectorUtil.getCategory();
        // 找到所有类型
        List<ProductionGroupEntity> productionGroupEntities = productionGroupMapper.selectList(new QueryWrapper<ProductionGroupEntity>());
        if(CollectionUtils.isEmpty(productionGroupEntities)){
            productionGroupEntities = ListUtils.EMPTY_LIST;
        }

        // 获取所有需要标记去除的
        List<ProductionGroupEntity> deleteList = new ArrayList<>();

        productionGroupEntities.forEach(e -> {
            Optional<CategoryModel> first = category.stream().filter(f -> f.getCategoryId().equals(e.getCategoryId())).findFirst();
            if(!first.isPresent()){
                deleteList.add(e);
            }
        });

        // 获取需要新增的
        List<CategoryModel> addList = new ArrayList<>();
        // 获取需要修改的
        List<ProductionGroupEntity> updateList = new ArrayList<>();

        List<ProductionGroupEntity> finalProductionGroupEntities = productionGroupEntities;
        category.forEach(e -> {
            Optional<ProductionGroupEntity> first = finalProductionGroupEntities.stream().filter(f -> f.getCategoryId().equals(e.getCategoryId())).findFirst();
            if(!first.isPresent()){
                addList.add(e);
            }else{
                ProductionGroupEntity productionGroupEntity = first.get();
                if(productionGroupEntity.getStatus() == 0){
                    updateList.add(productionGroupEntity);
                }
            }
        });

        // 处理新增
        saveProdutionGroup(addList);
        // 处理删除
        deleteProductionGroup(deleteList);
        // 处理修改
        updateProductionGroup(updateList);

        return ReturnT.SUCCESS;
    }

    private void updateProductionGroup(List<ProductionGroupEntity> updateList) {
        if(CollectionUtils.isEmpty(updateList)){
            return;
        }

        updateList.forEach(e -> {
            e.setStatus(1);
            e.setUpdateTime(LocalDateTime.now());
            productionGroupMapper.updateById(e);
        });

    }

    private void deleteProductionGroup(List<ProductionGroupEntity> deleteList) {
        if(CollectionUtils.isEmpty(deleteList)){
            return;
        }

        deleteList.forEach(e -> {
            e.setStatus(0);
            e.setUpdateTime(LocalDateTime.now());
            productionGroupMapper.updateById(e);
        });
    }

    private void saveProdutionGroup(List<CategoryModel> addList) {
        if(CollectionUtils.isEmpty(addList)){
            return;
        }

        addList.forEach(e -> {
            ProductionGroupEntity entity = new ProductionGroupEntity();
            entity.setGroupName(e.getCategoryName());
            entity.setStatus(1);
            entity.setCategoryId(e.getCategoryId());
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreateUserId("ROOT");
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId("ROOT");
            productionGroupMapper.insert(entity);
        });
    }
}
