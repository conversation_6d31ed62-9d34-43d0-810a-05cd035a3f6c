package com.wantwant.sfa.backend.Task;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.model.AccountModel;
import com.wantwant.sfa.backend.arch.request.SAccountRequest;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupRequest;
import com.wantwant.sfa.backend.businessGroup.service.impl.BusinessGroupService;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupVo;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.complete.entity.SfaComplete;
import com.wantwant.sfa.backend.complete.entity.SfaCompleteRule;
import com.wantwant.sfa.backend.complete.entity.SfaCompleteRuleRange;
import com.wantwant.sfa.backend.complete.request.CompleteListRequest;
import com.wantwant.sfa.backend.complete.service.impl.CompleteRuleServiceImpl;
import com.wantwant.sfa.backend.complete.vo.CompleteListVo;
import com.wantwant.sfa.backend.complete.vo.CompletePushListVo;
import com.wantwant.sfa.backend.complete.vo.CompleteRuleListVo;
import com.wantwant.sfa.backend.employee.vo.ParentVo;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.NotifyMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.AccountMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteRuleMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteRuleRangeMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.workReport.DailyReportMapper;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CompleteTask {

    @Autowired
    private GeTuiUtil geTuiUtil;

    @Autowired
    private CompleteRuleServiceImpl completeRuleServiceImpl;

    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaCompleteRuleMapper sfaCompleteRuleMapper;
    @Autowired
    private SfaCompleteRuleRangeMapper sfaCompleteRuleRangeMapper;
    @Autowired
    private SfaCompleteMapper sfaCompleteMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private NotifyMapper notifyMapper;
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Resource
    private DailyReportMapper dailyReportMapper;

    @Resource
    private GeTuiService geTuiService;

    @Resource
    private SettingServiceImpl settingService;

    @Autowired
    private BusinessGroupService businessGroupService;

    @XxlJob("CompleteRuleAutoCommitTask")
    @Transactional
    public ReturnT<String> CompleteRuleAutoCommitTask(String param) {

        log.info("通关自动发起定时任务开始..param:{}", param);

        LocalDate completeStartDate;
        if (CommonUtil.StringUtils.isNotEmpty(param)) {
            completeStartDate = LocalDate.parse(param, DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd));
        } else {
            completeStartDate = LocalDate.now();
        }
        // 检查日期是否是工作日
        Integer isWorkingInt = dailyReportMapper.checkWorkDate(completeStartDate.toString());
        if (Objects.isNull(isWorkingInt) || isWorkingInt != 0) {
            log.info("【通关自动发起】非工作日");
            return ReturnT.SUCCESS;
        }
        LocalTime localTimeStart = LocalTime.parse(settingService.getValue("complete_start_time"), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime localTimeEnd = LocalTime.parse(settingService.getValue("complete_end_time"), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalDateTime completeStartTime = completeStartDate.atTime(localTimeStart);
        LocalDateTime completeEndTime = completeStartDate.atTime(localTimeEnd);

        String completeNum = completeStartDate.toString().replace("-", "") + "01";

        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        SfaCompleteRule exists = sfaCompleteRuleMapper.selectOne(new LambdaQueryWrapper<SfaCompleteRule>()
                .eq(SfaCompleteRule::getCompleteDate, completeStartDate)
                .eq(SfaCompleteRule::getDeleteFlag, 0)
                .orderByDesc(SfaCompleteRule::getCompleteNum)
                .last("limit 1")
        );
        if (Objects.nonNull(exists)) {
            completeNum = String.valueOf(Long.parseLong(exists.getCompleteNum()) + 1);
        }

        SfaCompleteRule rule = new SfaCompleteRule();
        rule.init("ROOT", "系统默认", nowDateTime);
        rule.setCompleteNum(completeNum);
        rule.setCompleteDate(completeStartDate);
        rule.setCompleteStartTime(completeStartTime);
        rule.setCompleteEndTime(completeEndTime.plusMinutes(1));
        rule.setStatus(Boolean.TRUE);
        sfaCompleteRuleMapper.insert(rule);

        List<SfaBusinessGroupEntity> businessGroupList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                .eq(SfaBusinessGroupEntity::getStatus, 1)
                .ne(SfaBusinessGroupEntity::getId, 99)
        );
        for (SfaBusinessGroupEntity sfaBusinessGroupEntity : businessGroupList) {

            Map<String, List<CeoBusinessOrganizationEntity>> orgMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getChannel, 3)
                    .eq(CeoBusinessOrganizationEntity::getBusinessGroup, sfaBusinessGroupEntity.getId())
                    .ne(CeoBusinessOrganizationEntity::getOrganizationType, OrganizationTypeEnum.ZB.getOrganizationType())
                    .ne(CeoBusinessOrganizationEntity::getOrganizationType, "branch")
            ).stream().collect(Collectors.groupingBy(CeoBusinessOrganizationEntity::getOrganizationType));
            orgMap.forEach((organizationType, v) -> {
                List<String> orgCodes = v.stream().map(CeoBusinessOrganizationEntity::getOrganizationId).collect(Collectors.toList());
                String organizationCodes = String.join(",", orgCodes);
                SfaCompleteRuleRange range = new SfaCompleteRuleRange();
                range.init("ROOT", "系统默认", nowDateTime);
                range.setRuleId(rule.getId());
                range.setBusinessGroup(sfaBusinessGroupEntity.getId());
                range.setOrganizationType(organizationType);
                range.setOrganizationCode(organizationCodes);
                sfaCompleteRuleRangeMapper.insert(range);
            });
        }

        return ReturnT.SUCCESS;
    }


    @XxlJob("CompleteAutoPushTask")
    @Transactional
    public ReturnT<String> CompleteAutoPushTask(String param) {

        log.info("通关自动推送定时任务开始..param:{}", param);

        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();

        List<SfaCompleteRule> sfaCompleteRuleList = sfaCompleteRuleMapper.selectList(new LambdaQueryWrapper<SfaCompleteRule>()
                .le(SfaCompleteRule::getCompleteStartTime, nowDateTime)
                .gt(SfaCompleteRule::getCompleteEndTime, nowDateTime)
                .eq(SfaCompleteRule::getStartStatus, Boolean.FALSE)
                .eq(SfaCompleteRule::getStatus, Boolean.TRUE)
                .eq(SfaCompleteRule::getDeleteFlag, 0)
        );

        if (CollectionUtils.isEmpty(sfaCompleteRuleList)) {
            log.info("无待开始的通关.");
            return ReturnT.SUCCESS;
        }
        Map<Long, CompleteRuleListVo> sfaCompleteRuleMap = sfaCompleteRuleList.stream().collect(Collectors.toMap(SfaCompleteRule::getId, v -> {
                    v.setStartStatus(Boolean.TRUE);
                    v.setUpdateUserId("-1");
                    v.setUpdateUserName("-1");
                    v.setUpdateTime(nowDateTime);
                    CompleteRuleListVo vo = new CompleteRuleListVo();
                    BeanUtils.copyProperties(v, vo);
                    return vo;
                }
        ));

        completeRuleServiceImpl.updateBatchById(sfaCompleteRuleList);

        List<CompletePushListVo> completePushList = new ArrayList<>();
        Map<Long, List<SfaCompleteRuleRange>> sfaCompleteRuleRangeMap = sfaCompleteRuleRangeMapper.selectList(new LambdaQueryWrapper<SfaCompleteRuleRange>()
                .in(SfaCompleteRuleRange::getRuleId, sfaCompleteRuleList.stream().map(SfaCompleteRule::getId).collect(Collectors.toList()))
                .eq(SfaCompleteRuleRange::getDeleteFlag, 0)).stream().collect(
                Collectors.groupingBy(SfaCompleteRuleRange::getRuleId));
        sfaCompleteRuleRangeMap.forEach((ruleId, rangeList) -> {
            if (!CollectionUtils.isEmpty(rangeList)) {

                List<String> orgCodes = new ArrayList<>();
                rangeList.forEach(range -> orgCodes.addAll(Arrays.asList(range.getOrganizationCode().split(","))));
                List<SfaPositionRelationEntity> sfaPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                        .in(SfaPositionRelationEntity::getOrganizationCode, orgCodes)
                        .in(SfaPositionRelationEntity::getPositionTypeId, Lists.newArrayList(1, 12, 11, 2, 10))
                        .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                        .eq(SfaPositionRelationEntity::getStatus, 1)
                );
                if (!CollectionUtils.isEmpty(sfaPositionRelationList)) {
                    Set<Integer> employeeInfoIdSet = sfaPositionRelationList.stream().map(SfaPositionRelationEntity::getEmployeeInfoId).collect(Collectors.toSet());
                    List<SfaEmployeeInfoModel> employeeStatusList = sfaEmployeeInfoMapper.queryEmployeeStatusList(employeeInfoIdSet, nowDate, sfaCompleteRuleMap.get(ruleId).getCompleteStartTime());

                    Map<Integer, Integer> employeeStatusMap = employeeStatusList.stream()
                            .filter(e -> e.getId() != null && e.getEmployeeStatus() != null)
                            .collect(Collectors.toMap(
                                    SfaEmployeeInfoModel::getId,
                                    SfaEmployeeInfoModel::getEmployeeStatus,
                                    (existingValue, newValue) -> existingValue
                            ));
                    Map<Integer, String> employeeIdMap = employeeStatusList.stream()
                            .filter(e -> e.getId() != null && e.getEmployeeId() != null)
                            .collect(Collectors.toMap(
                                    SfaEmployeeInfoModel::getId,
                                    SfaEmployeeInfoModel::getEmployeeId,
                                    (existingValue, newValue) -> existingValue
                            ));
                    Map<Integer, String> employeeMobileMap = employeeStatusList.stream()
                            .filter(e -> e.getId() != null && e.getMobile() != null)
                            .collect(Collectors.toMap(
                                    SfaEmployeeInfoModel::getId,
                                    SfaEmployeeInfoModel::getMobile,
                                    (existingValue, newValue) -> existingValue
                            ));

                    employeeInfoIdSet.forEach(employeeInfoId -> {
                        SfaComplete complete = new SfaComplete();
                        complete.setRuleId(ruleId);
                        complete.setEmployeeInfoId(employeeInfoId);
                        complete.setEmployeeStatus(employeeStatusMap.get(employeeInfoId));
                        complete.setCompleteStatus(DictCodeConstants.CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_0.equals(employeeStatusMap.get(employeeInfoId)) ? DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0 : DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_2);
                        complete.setCreateUserId("-1");
                        complete.setCreateUserName("-1");
                        complete.setCreateTime(nowDateTime);
                        complete.setUpdateUserId("-1");
                        complete.setUpdateUserName("-1");
                        complete.setUpdateTime(nowDateTime);
                        sfaCompleteMapper.insert(complete);
                        if (DictCodeConstants.CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_0.equals(employeeStatusMap.get(employeeInfoId))) {
                            CompletePushListVo pushListVo = new CompletePushListVo();
                            pushListVo.setEmployeeId(employeeIdMap.get(employeeInfoId));
                            pushListVo.setRule(sfaCompleteRuleMap.get(ruleId));
                            pushListVo.setMobile(employeeMobileMap.get(employeeInfoId));
                            completePushList.add(pushListVo);
                        }
                    });
                }

            }
        });
        if (!CollectionUtils.isEmpty(completePushList)) {
            try {
                completePushList.stream().forEach(e -> {
                    String payload = "{\"title\":\"通关打卡\",\"completeStartTime\":\"" + e.getRule().getCompleteStartTime() + "\",\"completeEndTime\":\"" + e.getRule().getCompleteEndTime() + "\",\"completeNum\":\"" + e.getRule().getCompleteNum() + "\",\"type\":603}";
                    geTuiUtil.AppPushToSingleSync(e.getEmployeeId(), "通关打卡", "本次通关时间段：\n" +
                            LocalDateTimeUtils.formatTime(e.getRule().getCompleteStartTime(), "yyyy-MM-dd HH:mm") + "——"
                            + LocalDateTimeUtils.formatTime(e.getRule().getCompleteEndTime(), "yyyy-MM-dd HH:mm")
                            + "\n请在时间段内完成通过打卡！", payload, 1);
                });
            } catch (Exception e) {
                log.error("通关打卡推送异常：", e);
            }

            //发送短信
            String smsTemplateCode = settingService.getValue("sms_complete_rule_template");
            if(StringUtils.isNotBlank(smsTemplateCode)){
                try {
                    Map<Long, List<CompletePushListVo>> groupByRuleIdMap= completePushList.stream().collect(Collectors.groupingBy(x -> x.getRule().getId()));
                    groupByRuleIdMap.forEach((ruleId,pushList)->{
                        CompletePushListVo completePushListVo = pushList.get(0);
                        Duration duration = Duration.between(completePushListVo.getRule().getCompleteStartTime(), completePushListVo.getRule().getCompleteEndTime());
                        String timeInterval = LocalDateTimeUtils.formatTime(completePushListVo.getRule().getCompleteStartTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss) + "——"
                                + LocalDateTimeUtils.formatTime(completePushListVo.getRule().getCompleteEndTime(), "HH:mm:ss")
                                +"("+duration.toMinutes()+"分钟)";
                        Map<String,String> params = new HashMap<>(1);
                        params.put("timeInterval",timeInterval);
                        List<String> mobileList = pushList.stream().map(CompletePushListVo::getMobile).filter(x->StringUtils.isNotBlank(x)).distinct().collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(mobileList)){
                            log.info("ruleId=[{}],短信模板信息=[{}],总数据条数=[{}],去重后数据条数=[{}],timeInterval=[{}],手机号列表=[{}]",ruleId,smsTemplateCode,pushList.size(),mobileList.size(),timeInterval,mobileList);
                            int pageSize = 40;
                            if(mobileList.size()>pageSize){
                                int totalPage = PageUtil.totalPage(mobileList.size(), pageSize);
                                for(int i=1;i<=totalPage;i++){
                                    int fromIndex = (i - 1) * pageSize;
                                    int toIndex = Math.min(fromIndex + pageSize, mobileList.size());
                                    List<String> subList = mobileList.subList(fromIndex, toIndex);
                                    log.info("ruleId=[{}],短信模板信息=[{}],总数据条数=[{}],当前页码=[{}],数据条数=[{}],手机号列表=[{}]",ruleId,smsTemplateCode,pushList.size(),i,subList.size(),subList);
                                    geTuiService.smsPushListSync(smsTemplateCode,params,subList);
                                }
                            }else {
                                geTuiService.smsPushListSync(smsTemplateCode,params,mobileList);
                            }

                        }

                    });

                } catch (Exception e) {
                    log.error("通关打卡短信推送异常：", e);
                }
            }

        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("CompleteTimeoutTask")
    @Transactional
    public ReturnT<String> CompleteTimeoutTask(String param) {

        log.info("通关超时定时任务开始..param:{}", param);

        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        CompleteListRequest request = new CompleteListRequest();
        request.setCompleteStatus(DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0);
        request.setCompleteTimeoutTime(nowDateTime);
        request.setPartTime(0);
        request.setTimeout(Boolean.FALSE);
        List<CompleteListVo> list = sfaCompleteMapper.queryCompleteList(request);

        if (CollectionUtils.isEmpty(list)) {
            log.info("无超时的通关.");
            return ReturnT.SUCCESS;
        }

        Map<Integer, String> businessGroupIdNameMap = businessGroupService.getBusinessGroupList(new BusinessGroupRequest())
                .stream().collect(Collectors.toMap(BusinessGroupVo::getBusinessGroup, BusinessGroupVo::getBusinessGroupName));

        Map<Long, String> businessGroupMap = list.stream().collect(
                Collectors.toMap(CompleteListVo::getId, o -> businessGroupIdNameMap.get(o.getBusinessGroup())));

        Map<Long, String> employeeNameMap = list.stream().collect(
                Collectors.toMap(CompleteListVo::getId, CompleteListVo::getEmployeeName));

        Map<Long, String> completeNumMap = list.stream().collect(
                Collectors.toMap(CompleteListVo::getId, CompleteListVo::getCompleteNum));

        // 查询所有人的上级
        List<Integer> employeeInfoIds = list.stream().map(CompleteListVo::getEmployeeInfoId).collect(Collectors.toList());
        List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds(employeeInfoIds);
        Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));

        List<String> accountList = getAccountList();

        Map<String, NotifyPO> notifyMap = new HashMap<>();
        Map<String, String> notifyEmployeeNameMap = new HashMap<>();

        List<SfaComplete> sfaCompleteList = sfaCompleteMapper.selectList(new LambdaQueryWrapper<SfaComplete>()
                .in(SfaComplete::getId, list.stream().map(CompleteListVo::getId).collect(Collectors.toList())));
        sfaCompleteList.stream().forEach(sfaComplete -> {
            sfaComplete.setTimeout(Boolean.TRUE);
            sfaComplete.setUpdateUserId("-1");
            sfaComplete.setUpdateUserName("-1");
            sfaComplete.setUpdateTime(nowDateTime);
            sfaCompleteMapper.updateById(sfaComplete);

            NotifyPO po = new NotifyPO();
            po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
            po.setCode(MessageFormat.format("/PassLevelList?completeStatus=0&completeNum={0}", completeNumMap.get(sfaComplete.getId())));
            po.setCreateTime(LocalDateTime.now());
            po.setCreateBy("-1");
            po.setUpdateTime(LocalDateTime.now());
            po.setUpdateBy("-1");
            List<ParentVo> parentList = parentMap.get(sfaComplete.getEmployeeInfoId());
            if (!CollectionUtils.isEmpty(parentList)) {
                if (StringUtils.isNotBlank(parentList.get(0).getParentEmployeeId())) {
                    po.setEmployeeId(parentList.get(0).getParentEmployeeId());
                    employeeNameBuild(sfaComplete, notifyEmployeeNameMap, po, employeeNameMap, notifyMap, businessGroupMap);
                }
//                notifyMapper.insert(po);
            } else {
                accountList.forEach(employeeId -> {
                    NotifyPO notifyPO = new NotifyPO();
                    BeanUtils.copyProperties(po, notifyPO);
                    notifyPO.setEmployeeId(employeeId);
                    employeeNameBuild(sfaComplete, notifyEmployeeNameMap, notifyPO, employeeNameMap, notifyMap, businessGroupMap);
//                    notifyMapper.insert(po);
                });
            }
        });
        notifyMap.forEach((k, v) -> notifyMapper.insert(v));
        return ReturnT.SUCCESS;
    }

    @XxlJob("CompleteRefreshStatusTask")
    @Transactional
    public ReturnT<String> CompleteRefreshStatusTask(String param) {

        log.info("通关刷新状态定时任务开始..param:{}", param);
        // 默认昨天
        LocalDate completeDate = LocalDate.now().minusDays(1);
        if (StringUtils.isNotBlank(param)) {
            try {
                completeDate = LocalDate.parse(param, DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd));
            } catch (Exception e) {
                completeDate = LocalDate.now().minusDays(1);
            }
        }

        CompleteListRequest request = new CompleteListRequest();
        request.setCompleteDate(completeDate);
        sfaCompleteMapper.updateLeaveNotComplete(request);

        return ReturnT.SUCCESS;
    }

    private static void employeeNameBuild(SfaComplete sfaComplete, Map<String, String> notifyEmployeeNameMap, NotifyPO po, Map<Long, String> employeeNameMap, Map<String, NotifyPO> notifyMap, Map<Long, String> businessGroupMap) {
        String employeeName = notifyEmployeeNameMap.get(po.getEmployeeId() + "_" + sfaComplete.getRuleId());
        if (StringUtils.isNotBlank(employeeName)) {
            employeeName = employeeName + "," + employeeNameMap.get(sfaComplete.getId()) + "(" + businessGroupMap.get(sfaComplete.getId()) + ")";
        } else {
            employeeName = employeeNameMap.get(sfaComplete.getId()) + "(" + businessGroupMap.get(sfaComplete.getId()) + ")";
        }
        notifyEmployeeNameMap.put(po.getEmployeeId() + "_" + sfaComplete.getRuleId(), employeeName);

        String titleEmployeeName = employeeName;
        List<String> employeeNameList = Arrays.asList(employeeName.split(","));
        if (employeeNameList.size() > 3) {
            titleEmployeeName = String.join(",", employeeNameList.subList(0, 3)) + "...等人";
        }
        String title = MessageFormat.format("{0}的通关打卡未提交，请查看！\n" +
                "注：不同组的人员需要切换组别查看相关的通关记录", titleEmployeeName);
        String formatContent = MessageFormat.format("{0}的通关打卡未提交，请查看！\n" +
                "注：不同组的人员需要切换组别查看相关的通关记录", employeeName);
        po.setTitle(title);
        po.setContent(formatContent);
        notifyMap.put(po.getEmployeeId() + "_" + sfaComplete.getRuleId(), po);
    }


    private List<String> getAccountList() {
        SAccountRequest accountRequest = new SAccountRequest();
        accountRequest.setRows(1000);
        accountRequest.setRoleId(30);
        List<AccountModel> accountList = accountMapper.selectList(accountRequest, RequestUtils.getChannel(), RequestUtils.getBusinessGroup());
        return accountList.stream().map(AccountModel::getEmployeeId).collect(Collectors.toList());
    }
}
