package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.mapper.feedback.FeedbackInfoMapper;
import com.wantwant.sfa.backend.mapper.workReport.DailyReportMapper;
import com.wantwant.sfa.backend.workReport.service.IDailyReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/03/07/下午5:30
 */
@Component
@Slf4j
public class DailyReportTask {

    @Autowired
    private IDailyReportService dailyReportService;
    @Autowired
    private FeedbackInfoMapper feedbackInfoMapper;
    @Resource
    private DailyReportMapper dailyReportMapper;

    @XxlJob("dailyReportAutoSubmit")
    @Transactional
    public ReturnT<String> dailyReportAutoSubmit(String param) {

        // 获取执行日期
        String date = getYearMonth(param);

        log.info("【daily report auto submit】date:{}",date);
        // 检查日期是否是工作日
        Integer isWorkingInt = dailyReportMapper.checkWorkDate(LocalDate.parse(date).toString());
//        Integer isWorkingInt = feedbackInfoMapper.getIsWorkingInt(LocalDate.parse(date).atStartOfDay());
        if(Objects.isNull(isWorkingInt) || isWorkingInt == 1){

            log.info("【daily report auto submit】is not working date");

            return ReturnT.SUCCESS;
        }

        dailyReportService.dailyReportAutoSubmit(date);



        return ReturnT.SUCCESS;
    }

    private String getYearMonth(String param) {

        if(StringUtils.isNotBlank(param)){
            return param;
        }

        return LocalDate.now().minusDays(1).toString();
    }
}
