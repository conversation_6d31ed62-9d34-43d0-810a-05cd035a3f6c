package com.wantwant.sfa.backend.Task;


import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.mapper.NotifyMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class DailyTask {

    @Resource
    private SettingServiceImpl settingService;

    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private NotifyMapper notifyMapper;

    @XxlJob("DailyAutoPushTask")
    @Transactional
    public ReturnT<String> DailyAutoPushTask(String param) {

        log.info("日报自动推送定时任务开始..param:{}", param);

        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        String dailyDate = LocalDateTimeUtils.formatTime(yesterday, LocalDateTimeUtils.yyyy_MM_dd);
        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        // 获取所有有人岗位的员工号（战区-区域经理）
        List<String> employeeIdList = sfaEmployeeInfoMapper.queryAllEmployeeIdList();

        // 获取配置的推送员工号
        String dailyPushEmployee = settingService.getValue(DictCodeConstants.DAILY_PUSH_EMPLOYEE);
        if (Objects.nonNull(dailyPushEmployee)) {
            employeeIdList.addAll(Arrays.asList(dailyPushEmployee.split(",")));
        }

        employeeIdList.forEach(employeeId -> {
            NotifyPO po = new NotifyPO();
            po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
            po.setCode(MessageFormat.format("/dailyRecords?theDate={0}&employeeId={1}&dailyMode=push", dailyDate, employeeId));
            po.setCreateTime(nowDateTime);
            po.setCreateBy("-1");
            po.setUpdateTime(nowDateTime);
            po.setUpdateBy("-1");
            po.setEmployeeId(employeeId);
            String title = MessageFormat.format("{0}日报数据推送，点击查看！", dailyDate);
            po.setTitle(title);
            po.setContent(title);
            notifyMapper.insert(po);
        });

        return ReturnT.SUCCESS;
    }

}
