package com.wantwant.sfa.backend.Task;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.display.dto.DisplayProcessInfoDTO;
import com.wantwant.sfa.backend.display.dto.DisplayStatisticsDTO;
import com.wantwant.sfa.backend.display.dto.DisplayWaitDTO;
import com.wantwant.sfa.backend.display.request.DisplayAuditRequest;
import com.wantwant.sfa.backend.display.vo.CompanyQuotaVO;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.NotifyContentMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayInfoMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayProcessMapper;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.display.DisplayRulePO;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.service.DisplayInfoService;
import com.wantwant.sfa.backend.service.DisplayProcessService;
import com.wantwant.sfa.backend.service.DisplayRuleService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 特陈定时任务
 *
 * @date 12/6/22 6:39 PM
 * @version 1.0
 */
@Component
@Slf4j
public class DisplayTask {

    @Resource
    private DisplayInfoMapper displayInfoMapper;

    @Autowired
    private DisplayInfoService service;

    @Resource
    private DisplayProcessMapper displayProcessMapper;

    @Resource
    private NotifyService notifyService;

    @Resource
    private NotifyContentMapper notifyContentMapper;

    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;

    @Resource
    private DisplayProcessService displayProcessService;

    @Resource
    private DisplayRuleService displayRuleService;

    private String displaySendTitleTemplate = "{0}营运未审核明细";

    private String displaySendMessageTemplate = "{0} 总申请量{1} 已审核{2} 未审核{3} 进度{4}%";


    /**
     * 次月15号发送
     * 上月申请且运营未审核数据拉一份报表清单推送消息至leo
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 12/6/22 6:42 PM
     */
    @XxlJob("displaySendMessageTask")
    @Transactional
    public ReturnT<String> sendMessage(String param){
        XxlJobLogger.log("【陈列发送leo】start..");
        String lastMonthStr = getLastMonth(param);
        //获取上月1～15申请并且运营未审核
        List<DisplayWaitDTO> waitList = displayInfoMapper.listWaitLastMonth(lastMonthStr);
        DisplayStatisticsDTO statistics= displayInfoMapper.getStatisticsLastMonth(lastMonthStr);

        XxlJobLogger.log("【陈列发送leo】运营未审核statistics:{}",statistics);
        XxlJobLogger.log("【陈列发送leo】运营未审核waitList:{}",waitList);

        String title = MessageFormat.format(displaySendTitleTemplate,lastMonthStr);
        String message = MessageFormat.format(displaySendMessageTemplate,lastMonthStr,statistics.getTotal(),statistics.getReviewed(),statistics.getUnapproved(),statistics.getRate());

        if (CommonUtil.ListUtils.isNotEmpty(waitList)){
            NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.DISPLAY_SEND.getType(), title, "00272473", message);
            waitList.forEach(w -> {
                NotifyContentEntity notifyContent = new NotifyContentEntity();
                BeanUtils.copyProperties(w, notifyContent);
                notifyContent.setTemplateId(notifyPO.getTemplateId());
                //额外字段
                HashMap<String,String> map = new HashMap<>();
                map.put("departmentName",w.getDepartmentName());
                map.put("applicationNo",w.getApplicationNo());
                String json = JSONObject.toJSONString(map);
                int count = notifyContentMapper.selectCount(new QueryWrapper<NotifyContentEntity>().eq("additional", json));
                if (count < 1){
                    notifyContent.setAdditional(json);
                    notifyContentMapper.insert(notifyContent);
                }
            });
        }
        XxlJobLogger.log("【陈列发送leo】end..");
        return ReturnT.SUCCESS;
    }

    /**
     * 每月25日凌晨3点，除营运待审核的有规则特陈,驳回待审核；
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 12/7/22 4:12 PM
     */
    @XxlJob("displayWithRuleRejectProcessTask")
    @Transactional
    public ReturnT<String> displayWithRuleRejectProcessTask(String param){
        XxlJobLogger.log("【陈列月底驳回】start..");
        List<DisplayProcessInfoDTO> displayProcessList = displayProcessMapper.selectCurrentMonthProcess(1);
        XxlJobLogger.log("【陈列月底驳回】数据:{}",displayProcessList);
        displayProcessService.updateRejectProcess(displayProcessList,"过期定时驳回");
        XxlJobLogger.log("【陈列月底驳回】end..");
        return ReturnT.SUCCESS;
    }

    /**
     * 每月25日凌晨3点，除营运待审核的无规则特陈,驳回待审核；
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 12/7/22 4:12 PM
     */
    @XxlJob("displayWithOutRuleRejectProcessTask")
    @Transactional
    public ReturnT<String> displayWithOutRuleRejectProcessTask(String param){
        XxlJobLogger.log("【陈列月底驳回】start..");
        List<DisplayProcessInfoDTO> displayProcessList = displayProcessMapper.selectCurrentMonthProcess(2);
        XxlJobLogger.log("【陈列月底驳回】数据:{}",displayProcessList);
        displayProcessService.updateRejectProcess(displayProcessList,"过期定时驳回");
        XxlJobLogger.log("【陈列月底驳回】end..");
        return ReturnT.SUCCESS;
    }


    /**
     * 每月1号凌晨3点执行，营运自动审核；
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 12/7/22 4:12 PM
     */
    @XxlJob("displayOperationAutoProcess")
    @Transactional
    public ReturnT<String> operationAutoProcess(String param){
        log.info("【陈列营运自动审核】start..");
        List<DisplayProcessInfoDTO> displayProcessList = displayProcessMapper.selectLastMonthOperationProcess();
        log.info("【陈列营运自动审核】数据:{}",displayProcessList);
        displayProcessList.forEach(e -> {
            DisplayAuditRequest displayAuditRequest = new DisplayAuditRequest();
            displayAuditRequest.setProcessDetailId(e.getDetailId());
            displayAuditRequest.setProcessResult(2);
            // 获取分公司code
            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, e.getOrganizationId()).last("limit 1"));
            // 获取分公司剩余额度
            CompanyQuotaVO companyQuotaVO = service.queryCompanyQuota(viewEntity.getOrgId2());
            displayAuditRequest.setComment("系统自动通过");
            displayAuditRequest.setEmployeeId("ROOT");

            try {
                displayProcessService.audit(displayAuditRequest,companyQuotaVO);
            } catch (Exception ex) {
                log.info("【陈列营运自动审核】异常原因:{}",ex.getMessage());
            }
        });

        log.info("【陈列营运自动审核】end..");
        return ReturnT.SUCCESS;
    }



    /**
     * 次月16日凌晨3点，商企自动驳回；
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 12/7/22 4:12 PM
     */
    @XxlJob("displayBusinessRejectProcessTask")
    @Transactional
    public ReturnT<String> displayBusinessRejectProcessTask(String param){
        XxlJobLogger.log("【陈列商企审核月底驳回】start..");
        List<DisplayProcessInfoDTO> displayProcessList = displayProcessMapper.selectLastMonthBusinessProcess();
        XxlJobLogger.log("【陈列商企审核月底驳回】数据:{}",displayProcessList);
        displayProcessService.updateRejectProcess(displayProcessList,"过期商企审核定时驳回");
        XxlJobLogger.log("【陈列商企审核月底驳回】end..");
        return ReturnT.SUCCESS;
    }


    /**
     * 次月16号统一发放一次申请时间为1-31号 营运管理审核通过的数据
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 12/7/22 4:12 PM
     */
    @XxlJob("displayGrantTask")
    public ReturnT<String> displayGrantTask(String param){
        XxlJobLogger.log("【陈列15号发放上月】start..");
        //上月发起营运审核通过且未被现场集合标记异常未发放的数据
        List<DisplayProcessInfoDTO> list = displayProcessMapper.listPassLastMonth(param);
        XxlJobLogger.log("【陈列15号发放上月list:{}",list);
        log.info("陈列15号发放上月list:{}",list);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("displayGrantTask");
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>(list.size());
        list.stream().forEach(d -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    displayProcessService.autoQuotaGrant(d);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("特陈额度发放失败申请编号{}",d.getApplicationNo(),e);
                }
            });
            completableFutures.add(future);
        });
        CompletableFuture.allOf(completableFutures.stream().toArray(CompletableFuture[]::new)).join();
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        XxlJobLogger.log("【陈列15号发放上月】end..");
        return ReturnT.SUCCESS;
    }

    @XxlJob("displayUnGrantTag")
    public ReturnT<String> displayUnGrantTag(String param){
        log.info("display unGrant tag start..");
        // 查询未发放的额度
        List<Integer> detailIds = displayProcessMapper.selectUnGrantList(param);
        if(CollectionUtils.isEmpty(detailIds)){
            return ReturnT.SUCCESS;
        }
        // 将is_grant设置为3
        displayInfoMapper.updateUnGrant(detailIds);

        return ReturnT.SUCCESS;
    }



    /**
     * 特陈规则每月26号系统驳回
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 5/9/23 9:45 AM
     */
    @XxlJob("displayRuleRejectTask")
    public ReturnT<String> displayRuleRejectTask(String param){
        log.info("特陈规则26号驳回执行");
        List<DisplayRulePO> list = displayRuleService.list(new QueryWrapper<DisplayRulePO>().in("status", Arrays.asList(0, 1, 3)));
        list.forEach(d -> {
            if (0 == d.getStatus()) {
                d.setComment("超时未申请");
            }else{
                d.setComment("超时未审核");
            }
            d.setStatus(3);
        });
        displayRuleService.updateBatchById(list);
        log.info("特陈规则26号驳回执行数据:{}",list.stream().map(DisplayRulePO::getId).map(String::valueOf).collect(Collectors.joining(",")) );
        return ReturnT.SUCCESS;
    }


    /**
     * 获取上月
     */
    private String getLastMonth(String param) {
        if (CommonUtil.StringUtils.isNotBlank(param)){
            return param;
        }else {
            SimpleDateFormat df =new SimpleDateFormat("yyyy-MM");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -1);
            return df.format(calendar.getTime());
        }
    }

}
