package com.wantwant.sfa.backend.Task;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.interview.dto.EhrResignDTO;
import com.wantwant.sfa.backend.interview.dto.JobPositionChangeDto;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.task.impl.OffBoardTask;
import com.wantwant.sfa.backend.mapper.interview.InterviewSearchMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 第三方离职处理检查
 * @Auther: zhengxu
 * @Date: 2023/04/26/上午10:21
 */
@Component
@Slf4j
public class EhrResignCheckTask {
    @Autowired
    private InterviewSearchMapper interviewSearchMapper;
    @Autowired
    private OffBoardTask offBoardTask;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;

    @XxlJob("ehrResignCheck")
    @Transactional
    public ReturnT<String> execute(String param) {
        LocalDate executeDate = getDate(param);
        log.info("【ehr resign check】executeDate:{}",executeDate);
        // 获取所有第三方待离职未处理，并且建议离职日期小于今天的
        List<EhrResignDTO> list = interviewSearchMapper.resignCheck(executeDate);
        if(CollectionUtils.isEmpty(list)){
            log.info("【ehr resign check】未发现待离职的任务");
            return ReturnT.SUCCESS;
        }

        log.info("【ehr resign check】待离职的任务:{}",list);

        list.forEach(e -> {
            // 设置流程状态
            SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(e.getInterviewProcessId());
            if(Objects.isNull(sfaInterviewProcessModel)){
                throw new ApplicationException("面试流程信息获取失败");
            }

            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(sfaInterviewProcessModel.getInterviewRecordId());
            if(Objects.isNull(sfaInterviewProcessRecordModel)){
                throw new ApplicationException("面试记录获取失败");
            }

            // 更新流程
            sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.PASS.getResultCode());
            sfaInterviewProcessRecordModel.setProcessDate(new Date());
            sfaInterviewProcessRecordModel.setProcessUserName("系统自动办理");
            sfaInterviewProcessRecordModel.setComment("逾期未处理，系统自动执行");
            sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
            // 设置流程状态
            sfaInterviewProcessModel.setProcessResult(ProcessResult.PASS.getResultCode());
            sfaInterviewProcessModel.setOffTime(
                    Date.from(executeDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);


            // 离职计划
            JobPositionChangeDto jobPositionChangeDto = new JobPositionChangeDto();
            jobPositionChangeDto.setId(e.getResignId());
            jobPositionChangeDto.setProcessUserId("ROOT");
            jobPositionChangeDto.setProcessUserName("系统自动办理");
            jobPositionChangeDto.setExecuteDate(executeDate.atStartOfDay());
            offBoardTask.execute(jobPositionChangeDto);
        });



        return ReturnT.SUCCESS;
    }

    private LocalDate getDate(String param) {
        if(StringUtils.isNotBlank(param)){
            return LocalDate.parse(param);
        }

        return LocalDate.now();
    }
}
