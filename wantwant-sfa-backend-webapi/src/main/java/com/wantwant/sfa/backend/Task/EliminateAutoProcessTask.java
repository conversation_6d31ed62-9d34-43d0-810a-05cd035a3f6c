package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.estimate.model.SfaEstimateApprovalModel;
import com.wantwant.sfa.backend.estimate.model.SfaEstimateApprovalRecordModel;
import com.wantwant.sfa.backend.mapper.estimate.SfaEstimateApprovalMapper;
import com.wantwant.sfa.backend.mapper.estimate.SfaEstimateApprovalRecordMapper;
import com.wantwant.sfa.backend.model.estimate.EstimateDetailModel;
import com.wantwant.sfa.backend.model.estimate.EstimateModel;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/17/下午1:51
 */
@Component
@Slf4j
public class EliminateAutoProcessTask {
    @Autowired
    private SfaEstimateApprovalMapper sfaEstimateApprovalMapper;
    @Autowired
    private SfaEstimateApprovalRecordMapper sfaEstimateApprovalRecordMapper;

    @XxlJob("eliminateProcess")
    @Transactional
    public ReturnT<String> eliminateAutoProcess(String param){

        log.info("【eliminate auto process】start");

        String yearMonth = getYearMonth(param);

        log.info("【eliminate auto process】yearMonth :{}",yearMonth);

        List<SfaEstimateApprovalModel> sfaEstimateApprovalModels = sfaEstimateApprovalMapper.selectList(new QueryWrapper<SfaEstimateApprovalModel>()
                .eq("month", yearMonth)
                .eq("process_step", 1)
                .eq("is_current", 1)
                .eq("status", 1)
                .eq("result", 0)
                .ne("sale_estimate_no", "0")
        );

        if(CollectionUtils.isEmpty(sfaEstimateApprovalModels)){
            log.info("【eliminate auto process】process size :{}",0);
        }else{
            log.info("【eliminate auto process】process size :{}",sfaEstimateApprovalModels.size());
        }

        sfaEstimateApprovalModels.forEach(e -> {

            List<SfaEstimateApprovalRecordModel> recordModelList = sfaEstimateApprovalRecordMapper.selectList(new QueryWrapper<SfaEstimateApprovalRecordModel>()
                    .eq("estimate_approval_id", e.getId())
                    .eq("status", 1)
            );

            if(!CollectionUtils.isEmpty(recordModelList)){
                EstimateModel estimateModel = new EstimateModel();
                estimateModel.setSaleEstimateNo(e.getSaleEstimateNo());
                estimateModel.setStatus(2);
                BigDecimal estimateQuantity = recordModelList.stream().map(SfaEstimateApprovalRecordModel::getEstimateQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                estimateModel.setAuditQuantity(estimateQuantity);
                BigDecimal amount = recordModelList.stream().map(SfaEstimateApprovalRecordModel::getEstimatePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                estimateModel.setAuditAmount(amount);
                estimateModel.setUpdator("月底系统自动审核");
                List<EstimateDetailModel> items = new ArrayList<>();
                recordModelList.forEach(r -> {
                    EstimateDetailModel estimateDetailModel = new EstimateDetailModel();
                    estimateDetailModel.setSku(r.getSku());
                    estimateDetailModel.setAuditQuantity(r.getEstimateQuantity());
                    estimateDetailModel.setAuditAmount(r.getEstimatePrice());
                    items.add(estimateDetailModel);

                    r.setAuditPrice(r.getEstimatePrice());
                    r.setAuditQuantity(r.getEstimateQuantity());
                    sfaEstimateApprovalRecordMapper.updateById(r);

                });
                estimateModel.setItems(items);
            }

            // 更新状态
            e.setResult(1);
            e.setUpdateTime(LocalDateTime.now());
            e.setUpdateUserId("ROOT");
            sfaEstimateApprovalMapper.updateById(e);
        });

        return ReturnT.SUCCESS;
    }

    private String getYearMonth(String param) {
        if(StringUtils.isNotBlank(param)){
            return param;
        }

        return LocalDate.now().toString().substring(0,7);
    }
}
