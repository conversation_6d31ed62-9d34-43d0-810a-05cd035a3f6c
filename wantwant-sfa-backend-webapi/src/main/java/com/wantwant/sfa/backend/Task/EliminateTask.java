package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.interview.service.IEliminateBigTableService;
import com.wantwant.sfa.backend.interview.service.impl.EliminateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/18/上午9:45
 */
@Component
@Slf4j
public class EliminateTask {
    @Autowired
    private IEliminateBigTableService eliminateBigTableService;

    @Autowired
    private EliminateService eliminateService;

    @XxlJob("eliminateListExecute")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> eliminateListExecute(String param) {
        // 获取执行日期
        String executeDate = getExecuteDate(param);

        log.info("【eliminate list execute】executeDate:{}",executeDate);

        List<Long> memberKeys = Optional.ofNullable(eliminateBigTableService.selectReplaceMemberKey(executeDate)).orElse(new ArrayList<>());
        log.info("【eliminate list execute】汰换人员数量:{}",memberKeys.size());

        if(CollectionUtils.isEmpty(memberKeys)){
            return ReturnT.SUCCESS;
        }

        for (Long eliminateMemberKey : memberKeys) {
            try {
                eliminateService.eliminate(eliminateMemberKey);
            } catch (Exception e) {
                log.info("【eliminate process error】memberKey:{},msg:{}",eliminateMemberKey,e);
            }
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob("eliminateAutoProcess")
    @Transactional
    public ReturnT<String> eliminateAutoProcess(String param) {
        // 获取执行日期
        String executeDate = getExecuteDate(param);

        log.info("【eliminate auto execute】executeDate:{}",executeDate);
        // 获取还未处理的汰换记录
        List<Integer> recordIds =  Optional.ofNullable(eliminateService.getNotProcessRecords(executeDate)).orElse(new ArrayList<>());
        log.info("【eliminate auto execute】自动处理数量:{}",recordIds.size());

        if(CollectionUtils.isEmpty(recordIds)){
            return ReturnT.SUCCESS;
        }

        // 自动执行记录到人资
        for (Integer recordId : recordIds) {
            try {
                eliminateService.autoEliminateProcess(recordId);
            } catch (Exception e) {
                log.info("【eliminate auto process error】recordId:{},msg:{}",recordId,e);
            }
        }

        return ReturnT.SUCCESS;
    }



    private String getExecuteDate(String param) {

        if(StringUtils.isNotBlank(param)){
            return param;
        }

        return LocalDate.now().minusMonths(1).toString().substring(0,7);
    }
}
