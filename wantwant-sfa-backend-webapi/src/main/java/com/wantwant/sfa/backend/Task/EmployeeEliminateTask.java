package com.wantwant.sfa.backend.Task;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.interview.model.ResignApplyModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.process.EliminateAuditProcess;
import com.wantwant.sfa.backend.interview.request.ResignValidRequest;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.interview.SfaApplyResignMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketPositionMapper;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.notify.model.EliminateNotifyDetailModel;
import com.wantwant.sfa.backend.notify.model.NotifyDetailModel;
import com.wantwant.sfa.backend.notify.template.impl.EliminateNotifyContent;
import com.wantwant.sfa.backend.notify.template.impl.ProbationNotifyContent;
import com.wantwant.sfa.backend.service.ApplyMemberService;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.transaction.service.ITransactionService;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import com.wantwant.sfa.backend.valid.service.IValidService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/11/30/上午9:27
 */
@Component
@Slf4j
public class EmployeeEliminateTask {
    @Autowired
    private ReplacingMapper replacingMapper;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private SfaApplyResignMapper sfaApplyResignMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyContentMapper notifyContentMapper;
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private EliminateAuditProcess eliminateAuditProcess;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private IAuditService auditService;
    @Autowired
    private ITransactionService transactionService;

    private String titleTemplate = "{0}汰换信息";

    private String messageTemplate = "您好，{0}，系统提醒您次月1号即将被汰换清单如下，烦请进行查看";


    @XxlJob("autoEliminate")
    @Transactional
    public ReturnT<String> autoEliminate(String param){
        XxlJobLogger.log("autoEliminate task start..");

        // 获取所有需要自动汰换的记录ID
        List<Integer> recordIds = sfaInterviewProcessRecordMapper.selectAutoEliminate();
        if(CollectionUtils.isEmpty(recordIds)){
            XxlJobLogger.log("autoEliminate size :0");
            return ReturnT.SUCCESS;
        }
        XxlJobLogger.log("autoEliminate size :{}",recordIds.size());


        recordIds.forEach(e -> {
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(e);

            SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(sfaInterviewProcessRecordModel.getInterviewProcessId());

            ResignValidRequest request = new ResignValidRequest();
            request.setComment("系统自动离职");
            request.setProcessRecordId(sfaInterviewProcessRecordModel.getId());
            request.setOffTime(new Date());
            request.setPerson(sfaInterviewProcessRecordModel.getProcessUserId());
            request.setType(1);

            eliminateAuditProcess.accept(sfaInterviewProcessModel,request);

        });





        return ReturnT.SUCCESS;
    }


    @XxlJob("employeeEliminate")
    @Transactional
    public ReturnT<String> eliminate(String yearMonth){
        log.info("employee eliminate task start..");
        if(StringUtils.isBlank(yearMonth)){
            LocalDate now = LocalDate.now();
            LocalDate excuteDate = now.minusMonths(1);
            yearMonth = excuteDate.toString().substring(0, 7);
        }


        log.info("employee eliminate excute date:{}",yearMonth);

        List<EliminateEmployeeInfoModel> lastMonthEliminateEmployee = replacingMapper.getLastMonthEliminateEmployee(yearMonth);
        if(CollectionUtils.isEmpty(lastMonthEliminateEmployee)){
            log.info("employee eliminate 当月汰换人数:0人");
            return ReturnT.SUCCESS;
        }else{
            log.info("employee eliminate 当月汰换人数:{}人",lastMonthEliminateEmployee.size());
        }


        String finalYearMonth = yearMonth;
        lastMonthEliminateEmployee.forEach(e -> {
            SfaCustomer sfaCustomer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", e.getMemberKey()));
            if(Objects.isNull(sfaCustomer)){
                log.info("employee eliminate获取客户信息失败,memberKey:{}",e);
                return;
            }
            String positionId = sfaCustomer.getPositionId();

            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>()
                    .eq("position_id", positionId)
                    .eq("mobile",sfaCustomer.getMobileNumber())
            );
            if(sfaEmployeeInfoModel.getPostType() == 2 && (sfaEmployeeInfoModel.getType() == 1 || sfaEmployeeInfoModel.getType() == 2)){
                log.info("兼职不做处理,memberKey:{}",sfaCustomer.getMemberKey());
                return;
            }


            if(sfaEmployeeInfoModel.getEmployeeStatus() != EmployeeStatus.ONBOARD.getType()){
                log.info("会员memberKey:{}已处理过,当前状态employeeStatus:{}",sfaCustomer.getMemberKey(),sfaEmployeeInfoModel.getEmployeeStatus());
                return;
            }

            // 检查是否已处理过
            SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", sfaEmployeeInfoModel.getApplicationId()));
            if(Objects.isNull(sfaInterviewProcessModel)){
                log.info("未找到面试流程");
                return;
            }

            if(sfaInterviewProcessModel.getProcessType() != ProcessType.DO_ONBOARD.getProcessCode()){
                log.info("会员已处理过，memberKey:{}",sfaCustomer.getMemberKey());
                return;
            }

            // 检查是否处于异动中并且是人资审核
            int count = transactionService.checkTransaction(sfaEmployeeInfoModel.getId());
            if(count > 0){
                log.info("会员正在异动中，memberKey:{}",sfaCustomer.getMemberKey());
                return;
            }

            // 关闭非人资操作，审核中的异动
            transactionService.closeTransactionProcess(sfaEmployeeInfoModel.getId());

            // 创建流程信息
            createProcessInfo(positionId);
            // 发送消息
            sendMessage(finalYearMonth,e,sfaEmployeeInfoModel);
        });

        return ReturnT.SUCCESS;
    }



    private void sendMessage(String finalYearMonth, EliminateEmployeeInfoModel model, SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        String day = DateUtil.format(new Date(), "yyyy-MM年-dd日");
        String title = MessageFormat.format(titleTemplate,day);
        String message = MessageFormat.format(messageTemplate,day);


        String companyCode = sfaEmployeeInfoModel.getCompanyCode();
        String areaCode = sfaEmployeeInfoModel.getAreaCode();
        // 分公司
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", companyCode).eq("channel", 3));
        if(Objects.nonNull(companyPosition) && StringUtils.isNotBlank(companyPosition.getEmployeeId())){
            doSendMessage(sfaEmployeeInfoModel, title, message, companyPosition.getEmployeeId(),model.getZwPerformance(),model.getDifferenceAmount());
        }

        // 大区
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", areaCode).eq("channel", 3));
        if(Objects.nonNull(areaPosition) && StringUtils.isNotBlank(areaPosition.getEmployeeId())){
            doSendMessage(sfaEmployeeInfoModel, title, message, areaPosition.getEmployeeId(),model.getZwPerformance(),model.getDifferenceAmount());
        }
    }

    private void doSendMessage(SfaEmployeeInfoModel sfaEmployeeInfoModel, String title, String message, String employeeId, BigDecimal zwPerformance, BigDecimal differenceAmount) {
        NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.ELIMINATE.getType(), title, employeeId, message);

        EliminateNotifyContent eliminateNotifyContent = new EliminateNotifyContent();

        EliminateNotifyDetailModel eliminateNotifyDetailModel = new EliminateNotifyDetailModel();
        eliminateNotifyDetailModel.setTemplateId(notifyPO.getTemplateId());
        eliminateNotifyDetailModel.setAreaName(sfaEmployeeInfoModel.getAreaName());
        eliminateNotifyDetailModel.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
        eliminateNotifyDetailModel.setDepartmentName(sfaEmployeeInfoModel.getDepartmentName());
        eliminateNotifyDetailModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());


        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());

        eliminateNotifyDetailModel.setPosition(PositionEnum.getPositionName(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition()));


        eliminateNotifyDetailModel.setStatus(EmployeeStatus.findNameByType(sfaEmployeeInfoModel.getEmployeeStatus()));
        if(Objects.nonNull(zwPerformance)){
            eliminateNotifyDetailModel.setAchievement(zwPerformance.toString());
        }
        if(Objects.nonNull(differenceAmount)){
            eliminateNotifyDetailModel.setTargetDistance(differenceAmount.toString());
        }
        eliminateNotifyDetailModel.setOnBoardTime(LocalDateTimeUtils.formatTime(sfaEmployeeInfoModel.getOnboardTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
        eliminateNotifyDetailModel.setMobile(sfaEmployeeInfoModel.getMobile());
        NotifyContentEntity notifyContentEntity = eliminateNotifyContent.buildNotifyContent(eliminateNotifyDetailModel);
        notifyContentMapper.insert(notifyContentEntity);
    }

    private void createProcessInfo(String positionId) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("position_id", positionId));
        if(Objects.isNull(sfaEmployeeInfoModel)){
            return;
        }
        // 创建离职申请
        ResignApplyModel resignApplyModel = saveResign(sfaEmployeeInfoModel);
        XxlJobLogger.log("创建离职申请");

        // 设置审核记录为离职审核
        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new QueryWrapper<SfaInterviewProcessModel>().eq("application_id", sfaEmployeeInfoModel.getApplicationId()));
        sfaInterviewProcessModel.setProcessType(ProcessType.APPLY_RESIGN.getProcessCode());
        sfaInterviewProcessModel.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
        XxlJobLogger.log("设置主流程为离职待办理");
        // 获取当前的记录表
        List<SfaInterviewProcessRecordModel> sfaInterviewProcessRecordModels = sfaInterviewProcessRecordMapper.selectList(new QueryWrapper<SfaInterviewProcessRecordModel>().eq("interview_process_id", sfaInterviewProcessModel.getId())
                .eq("process_type", ProcessType.DO_ONBOARD.getProcessCode())
                .orderByDesc("id")
        );

        if(CollectionUtils.isEmpty(sfaInterviewProcessRecordModels)){
            XxlJobLogger.log("入职记录获取失败");
            return;
        }

        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordModels.get(0);
        // 创建一条离职审核记录
        SfaInterviewProcessRecordModel validOffRecord = new SfaInterviewProcessRecordModel();
        BeanUtils.copyProperties(sfaInterviewProcessRecordModel,validOffRecord,"id","next_process_id","processUserId","processUserName","processDate","comment");
        // 选择审核人

        validOffRecord.setResignApplyId(resignApplyModel.getId());
        validOffRecord.setProcessType(ProcessType.APPLY_RESIGN.getProcessCode());
        validOffRecord.setProcessResult(ProcessResult.NOT_PROCESS.getResultCode());
        validOffRecord.setOrganizationId(null);
        validOffRecord.setCreateTime(new Date());
        validOffRecord.setPrevProcessId(sfaInterviewProcessRecordModel.getId());

        String departmentCode = sfaEmployeeInfoModel.getDepartmentCode();
        String areaCode = sfaEmployeeInfoModel.getAreaCode();

        String zw_hr_employee_id = configMapper.getValueByCode("zw_hr_employee_id");


        SelectAuditDto selectAuditDto = new SelectAuditDto();
        selectAuditDto.setChannel(3);
        selectAuditDto.setStandbyEmployeeId(zw_hr_employee_id);
        selectAuditDto.setCurrentOrganizationId(areaCode);
        // 创建新的审核人
        CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);
        validOffRecord.setProcessUserId(auditPerson.getEmployeeId());
        validOffRecord.setProcessUserName(auditPerson.getEmployeeName());

        sfaInterviewProcessRecordMapper.insert(validOffRecord);
        XxlJobLogger.log("创建离职审核通过记录");
        sfaInterviewProcessRecordModel.setNextProcessId(validOffRecord.getId());
        sfaInterviewProcessRecordModel.setResignApplyId(resignApplyModel.getId());

        // 设置流程表中记录当前流程ID为离职办理流程的ID
        sfaInterviewProcessModel.setInterviewRecordId(validOffRecord.getId());
        sfaInterviewProcessRecordMapper.updateById(validOffRecord);
        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);
        sfaInterviewProcessModel.setResignId(resignApplyModel.getId());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        // 设置离职申请记录中流程记录ID为离职办理流程的ID
        resignApplyModel.setProcessRecordId(validOffRecord.getId());


        sfaApplyResignMapper.updateById(resignApplyModel);

        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaInterviewProcessModel.getApplicationId());

        applyMemberService.syn(
                applyMemberPo,
                null,
                sfaInterviewProcessModel.getProcessType(),
                sfaInterviewProcessModel.getProcessResult(),
                StringUtils.EMPTY,
                applyMemberPo.getHighestEducation(),
                applyMemberPo.getIdCardNum(),
                applyMemberPo.getBirthDate(),
                sfaInterviewProcessModel.getRecommendOnboardTime(),
                null);

    }


    private ResignApplyModel saveResign(SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        ResignApplyModel resignApplyModel = new ResignApplyModel();
        resignApplyModel.setMobile(sfaEmployeeInfoModel.getMobile());
        resignApplyModel.setStatus(1);
        resignApplyModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        resignApplyModel.setEmployeeId(sfaEmployeeInfoModel.getEmployeeId());
        resignApplyModel.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
        resignApplyModel.setCreateUserId("0");
        resignApplyModel.setCreateUserName("系统自动申请");
        resignApplyModel.setCreateTime(LocalDateTime.now());
        resignApplyModel.setResignType(3);
        sfaApplyResignMapper.insert(resignApplyModel);
        return resignApplyModel;
    }
}
