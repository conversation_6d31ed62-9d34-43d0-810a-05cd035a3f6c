package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.organizationGoal.OrganizationGoalExcelMapper;
import com.wantwant.sfa.backend.mapper.organizationGoal.OrganizationGoalMapper;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.organizationGoal.OrganizationGoalExcelPO;
import com.wantwant.sfa.backend.organizationGoal.request.DepartmentGoalQueryRequest;
import com.wantwant.sfa.backend.organizationGoal.vo.CompanyGoalDetail;
import com.wantwant.sfa.backend.organizationGoal.vo.DepartmentGoalDetail;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分公司/营业所目标设置通知
 *
 * @date 3/27/23 4:34 PM
 * @version 1.0
 */
@Component
@Slf4j
public class GoalTask {


    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private OrganizationGoalExcelMapper organizationGoalExcelMapper;

    @Resource
    private OrganizationGoalMapper organizationGoalMapper;

    @Autowired
    private NotifyService notifyService;


    @XxlJob("goalSendMessageTask")
    @Transactional
    public ReturnT<String> sendMessage(String param){
        log.info("目标设置通知");
        int monthValue = LocalDate.now().getMonthValue();
        //提报时间段
        OrganizationGoalExcelPO excelPO = organizationGoalExcelMapper.selectOne(new QueryWrapper<OrganizationGoalExcelPO>()
                .eq("date_format(effective_date,'%Y-%m')", LocalDateTimeUtils.formatNow("yyyy-MM"))
                .eq("state", 1).eq("is_delete", 0));
        if (Objects.nonNull(excelPO) && null != excelPO.getStartDate() && null != excelPO.getDepartmentEndDate()){
            LocalDate now = LocalDate.now();
            List<NotifyPO> notifyPOS = new ArrayList<>();
            DepartmentGoalQueryRequest request = new DepartmentGoalQueryRequest();
            request.setEffectiveDate(LocalDateTimeUtils.formatNow("yyyy-MM"));
            if((now.isAfter(excelPO.getStartDate()) || now.isEqual(excelPO.getStartDate())) && (now.isBefore(excelPO.getDepartmentEndDate()) || now.isEqual(excelPO.getDepartmentEndDate()))){
                //总监
                List<DepartmentGoalDetail> list = organizationGoalMapper.listDepartmentGoal1(request);
                Set<String> companySet = list.stream().filter(d -> d.getId() == null && d.getCid() != null).map(DepartmentGoalDetail::getCompanyOrganizationId).collect(Collectors.toSet());
                List<String> empList = organizationMapper.getEmployeeIdBySet(companySet);
                empList.stream().filter(e -> CommonUtil.StringUtils.isNotBlank(e)).forEach(e -> {
                    NotifyPO po = new NotifyPO();
                    po.setTitle("营业所目标设置"+monthValue+"月");
                    po.setType(1);
                    po.setContent("营业所目标设置"+monthValue+"月");
                    po.setCode("/ZwTargetSet?tab=branchTarget");
                    po.setEmployeeId(e);
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                });
            }
            if ((now.isAfter(excelPO.getStartDate()) || now.isEqual(excelPO.getStartDate())) && (now.isBefore(excelPO.getCompanyEndDate()) || now.isEqual(excelPO.getCompanyEndDate()))){
                //督导
                request.setEffectiveDate(LocalDateTimeUtils.formatNow("yyyy-MM"));
                List<CompanyGoalDetail> companyList = organizationGoalMapper.listCompanyGoal(request);
                Set<String> areaSet = companyList.stream().filter(d -> d.getId() == null || d.getTransAmount1().compareTo(BigDecimal.ZERO) <= 0).map(CompanyGoalDetail::getAreaOrganizationId).collect(Collectors.toSet());
                List<String> empList = organizationMapper.getEmployeeIdBySet(areaSet);
                empList.stream().filter(f -> CommonUtil.StringUtils.isNotBlank(f)).forEach(e -> {
                    NotifyPO po = new NotifyPO();
                    po.setTitle("分公司目标设置"+monthValue+"月");
                    po.setType(1);
                    po.setContent("分公司目标设置"+monthValue+"月");
                    po.setCode("/ZwTargetSet?tab=companyTarget");
                    po.setEmployeeId(e);
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                });
            }
            if (CommonUtil.ListUtils.isNotEmpty(notifyPOS)) {
                notifyService.saveBatch(notifyPOS);
            }
        }
        return ReturnT.SUCCESS;
    }


}
