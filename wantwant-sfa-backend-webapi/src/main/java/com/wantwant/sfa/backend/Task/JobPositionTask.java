package com.wantwant.sfa.backend.Task;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.interview.dto.JobPositionChangeDto;
import com.wantwant.sfa.backend.interview.dto.JobPositionTaskDto;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.interview.task.impl.OffBoardTask;
import com.wantwant.sfa.backend.interview.task.impl.OnBoardTask;
import com.wantwant.sfa.backend.interview.task.impl.PositionTransactionTask;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaJobPositionTaskMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/03/下午3:05
 */
@Component
@Slf4j
public class JobPositionTask {

    @Autowired
    private SfaJobPositionTaskMapper sfaJobPositionTaskMapper;
    @Autowired
    private OnBoardTask onBoardTask;
    @Autowired
    private OffBoardTask offBoardTask;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private PositionTransactionTask positionTransactionTask;

    @XxlJob("jobPosition")
    @Transactional
    public ReturnT<String> jobPosition(String param) {
        LocalDate executeDate = getExecuteDate(param);
        log.info("【JobPositionTask】executeDate:{}",executeDate.toString());

        // 获取所有未执行的任务
        List<SfaJobPositionTask> sfaJobPositionTasks = sfaJobPositionTaskMapper.selectList(new QueryWrapper<SfaJobPositionTask>().eq("status", 0)
                .le("execute_date", executeDate.toString()));

        if(CollectionUtils.isEmpty(sfaJobPositionTasks)){
            log.info("【JobPositionTask】无可执行的任务");
        }

        // 执行入职任务
        sfaJobPositionTasks.stream().filter(f -> f.getType() == 1).forEach(e -> {
            JobPositionChangeDto jobPositionChangeDto = new JobPositionChangeDto();
            jobPositionChangeDto.setExecuteDate(e.getExecuteDate().atStartOfDay());
            jobPositionChangeDto.setProcessUserId(e.getProcessUserId());
            jobPositionChangeDto.setProcessUserName(e.getProcessUserName());
            jobPositionChangeDto.setId(e.getApplyId().longValue());
            jobPositionChangeDto.setActualJoiningCompany(e.getActualJoiningCompany());
            jobPositionChangeDto.setJoiningCompany(e.getJoiningCompany());
            jobPositionChangeDto.setContractCompany(e.getContractCompany());
            jobPositionChangeDto.setEmployeeId(e.getEmployeeId());
            try {
                ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(e.getApplyId());
                jobPositionChangeDto.setEmployeeName(applyMemberPo.getUserName());
                jobPositionChangeDto.setMobile(applyMemberPo.getUserMobile());
                onBoardTask.execute(jobPositionChangeDto);
                e.setStatus(1);
                e.setProcessTime(LocalDateTime.now());
                sfaJobPositionTaskMapper.updateById(e);
            } catch (Exception ex) {
                log.info("job postion err ex:{},stackTace:{},message:{}",ex,ex.getStackTrace(),ex.getMessage());
                // 当前任务设置为失败
                e.setStatus(2);
                e.setErrMsg(ex.getMessage());
                e.setProcessTime(LocalDateTime.now());
                sfaJobPositionTaskMapper.updateById(e);
            }
        });


        // 执行离职任务
        sfaJobPositionTasks.stream().filter(f -> f.getType() == 2).forEach(e -> {
            JobPositionChangeDto jobPositionChangeDto = new JobPositionChangeDto();
            jobPositionChangeDto.setExecuteDate(e.getExecuteDate().atStartOfDay());
            jobPositionChangeDto.setProcessUserId(e.getProcessUserId());
            jobPositionChangeDto.setProcessUserName(e.getProcessUserName());
            jobPositionChangeDto.setId(e.getResignId().longValue());

            try {
                offBoardTask.execute(jobPositionChangeDto);
                e.setStatus(1);
                e.setProcessTime(LocalDateTime.now());
                sfaJobPositionTaskMapper.updateById(e);
            } catch (Exception ex) {
                // 当前任务设置为失败
                e.setStatus(2);
                e.setErrMsg(ex.getMessage());
                e.setProcessTime(LocalDateTime.now());
                sfaJobPositionTaskMapper.updateById(e);
            }
        });


        return ReturnT.SUCCESS;
    }

    private LocalDate getExecuteDate(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now();
        }

        return LocalDate.parse(param);
    }
}
