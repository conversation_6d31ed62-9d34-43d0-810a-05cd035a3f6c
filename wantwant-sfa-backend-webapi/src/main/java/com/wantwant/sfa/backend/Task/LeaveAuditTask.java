package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.leave.request.LeaveAuditRequest;
import com.wantwant.sfa.backend.leave.service.ILeaveService;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveCancelInfoMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.List;
import java.util.Map;

import static com.wantwant.sfa.backend.leave.service.impl.LeaveService.ATTENDANCE_DAYS;

@Component
@Slf4j
public class LeaveAuditTask {
    @Autowired
    private SfaLeaveCancelInfoMapper sfaLeaveCancelInfoMapper;

    @Autowired
    private ILeaveService iLeaveService;


    /*
    *
    */
    @XxlJob("leaveAuditTask")
    public ReturnT<String> leaveAudit(String param){
        log.info("考勤自动驳回定时任务开始..");

        LocalDateTime attendanceStartDate = null;
        LocalDateTime attendanceEndDate = null;
        LocalDateTime currentDateTime = LocalDateTime.now();

        attendanceEndDate = LocalDateTime.of(currentDateTime.getYear(), currentDateTime.getMonth(),ATTENDANCE_DAYS, 0, 0,0);
        if(attendanceEndDate.getMonth() == Month.JANUARY) {
            attendanceStartDate = attendanceEndDate.withYear(attendanceEndDate.getYear() - 1).withMonth(12);
        }else {
            attendanceStartDate = attendanceEndDate.withMonth(attendanceEndDate.getMonthValue() - 1);
        }
        List<Map<String, String>> list = sfaLeaveCancelInfoMapper.getLeaveExpireEmployeeInfo(attendanceStartDate,attendanceEndDate);
        log.info("list: {}", list);
        for (Map<String, String> map: list) {
            LeaveAuditRequest request = new LeaveAuditRequest();
            request.setResult(2);
            request.setReason("跨周期未审批，系统自动驳回");
            request.setBusinessNum(map.get("businessNum"));
            request.setPerson(map.get("person"));
            try{
                iLeaveService.leaveAudit(request);
            }catch (Exception e) {
                log.info(e.getMessage());
            }
        }
        log.info("考勤自动驳回定时任务end..");
        return ReturnT.SUCCESS;

    }
}
