package com.wantwant.sfa.backend.Task;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.employee.vo.ParentVo;
import com.wantwant.sfa.backend.leave.entity.SfaLeave;
import com.wantwant.sfa.backend.leave.entity.SfaLeaveRecord;
import com.wantwant.sfa.backend.leave.service.impl.NewLeaveRecordService;
import com.wantwant.sfa.backend.leave.service.impl.NewLeaveService;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveRecordMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LeaveTask {

    @Autowired
    private NewLeaveService newLeaveService;
    @Autowired
    private NewLeaveRecordService newLeaveRecordService;
    @Autowired
    private SfaLeaveMapper sfaLeaveMapper;
    @Autowired
    private SfaLeaveRecordMapper sfaLeaveRecordMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @XxlJob("leaveAutoTask")
    @Transactional
    public ReturnT<String> leaveAuto(String param) {

        log.info("请假更新审核人定时任务开始..param:{}", param);

        // 所有待审核的请假
        List<SfaLeave> leaveAuditList = sfaLeaveMapper.queryLeaveAuditList();
        if (CollectionUtils.isEmpty(leaveAuditList)) {
            log.info("无待审核的请假.");
            return ReturnT.SUCCESS;
        }

        List<SfaLeaveRecord> sfaLeaveRecordList = sfaLeaveRecordMapper.selectList(new LambdaQueryWrapper<SfaLeaveRecord>().in(SfaLeaveRecord::getLeaveId, leaveAuditList.stream().map(SfaLeave::getId).collect(Collectors.toList())).eq(SfaLeaveRecord::getOperatorType, Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_0)).eq(SfaLeaveRecord::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(sfaLeaveRecordList) || sfaLeaveRecordList.size() != leaveAuditList.size()) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "待审核记录获取失败");
        }
        Map<Long, SfaLeaveRecord> sfaLeaveRecordMap = sfaLeaveRecordList.stream().collect(Collectors.toMap(SfaLeaveRecord::getLeaveId, v -> v));

        // 查询所有待审核的请假的申请人的上级
        List<Integer> employeeInfoIds = leaveAuditList.stream().map(SfaLeave::getApplyEmployeeInfoId).collect(Collectors.toList());
        List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds(employeeInfoIds);
        Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));

        List<SfaLeaveRecord> sfaLeaveRecordUpdateList = new ArrayList<>();
        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();

        leaveAuditList.stream().forEach(leaveAudit -> {
            SfaLeave sfaLeave = new SfaLeave();
            boolean updateFlg = false;
            boolean autoFlg = false;
            if (Objects.isNull(leaveAudit.getAuditEmployeeInfoId()) && !CollectionUtils.isEmpty(parentMap.get(leaveAudit.getApplyEmployeeInfoId()))) {
                sfaLeave.setAuditEmployeeInfoId(parentMap.get(leaveAudit.getApplyEmployeeInfoId()).get(0).getParentEmployeeInfoId());
                updateFlg = true;
            } else if (Objects.nonNull(leaveAudit.getAuditEmployeeInfoId()) && CollectionUtils.isEmpty(parentMap.get(leaveAudit.getApplyEmployeeInfoId()))) {
                sfaLeave.setAuditEmployeeInfoId(null);
                updateFlg = true;
            } else if (Objects.nonNull(leaveAudit.getAuditEmployeeInfoId()) && !CollectionUtils.isEmpty(parentMap.get(leaveAudit.getApplyEmployeeInfoId())) && !leaveAudit.getAuditEmployeeInfoId().equals(parentMap.get(leaveAudit.getApplyEmployeeInfoId()).get(0).getParentEmployeeInfoId())) {
                sfaLeave.setAuditEmployeeInfoId(parentMap.get(leaveAudit.getApplyEmployeeInfoId()).get(0).getParentEmployeeInfoId());
                updateFlg = true;
            } else {
                sfaLeave.setAuditEmployeeInfoId(leaveAudit.getAuditEmployeeInfoId());
            }
            if (!leaveAudit.getLeaveStartTime().toLocalDate().isAfter(nowDate) && Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_TYPE_ITEMVALUE_0).equals(leaveAudit.getLeaveType())) {
                sfaLeave.setLeaveStatus(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_STATUS_ITEMVALUE_1));
                sfaLeaveMapper.updateAttendanceMonthAlreadyLeaveHours(leaveAudit.getAttendanceStartDate(), leaveAudit.getAttendanceEndDate(), leaveAudit.getApplyEmployeeInfoId(), leaveAudit.getLeaveHours(), 1);
                autoFlg = true;
            } else {
                sfaLeave.setLeaveStatus(leaveAudit.getLeaveStatus());
            }
            if (updateFlg || autoFlg) {
                sfaLeave.setId(leaveAudit.getId());
                sfaLeave.setUpdateUserId("-1");
                sfaLeave.setUpdateUserName("leaveAutoTask");
                sfaLeave.setUpdateTime(nowDateTime);
                sfaLeaveMapper.updateLeaveById(sfaLeave);

                if (updateFlg) {
                    SfaLeaveRecord leaveRecord = sfaLeaveRecordMap.get(sfaLeave.getId());
                    if (Objects.nonNull(sfaLeave.getAuditEmployeeInfoId())) {
                        leaveRecord.setOperatorEmployeeId(parentMap.get(leaveAudit.getApplyEmployeeInfoId()).get(0).getParentEmployeeId());
                    } else {
                        leaveRecord.setOperatorEmployeeId(null);
                    }
                    leaveRecord.setOperatorType(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_0));
                    leaveRecord.setOperatorTime(nowDateTime);
                    leaveRecord.setUpdateUserId("-1");
                    leaveRecord.setUpdateUserName("leaveAutoTask");
                    leaveRecord.setUpdateTime(nowDateTime);
                    sfaLeaveRecordUpdateList.add(leaveRecord);
                }
                if (autoFlg) {
                    SfaLeaveRecord autoRecord = sfaLeaveRecordMap.get(sfaLeave.getId());
                    if (Objects.nonNull(sfaLeave.getAuditEmployeeInfoId())) {
                        autoRecord.setOperatorEmployeeId(parentMap.get(leaveAudit.getApplyEmployeeInfoId()).get(0).getParentEmployeeId());
                    } else {
                        autoRecord.setOperatorEmployeeId("-1");
                    }
                    autoRecord.setOperatorType(Integer.valueOf(DictCodeConstants.CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_1));
                    autoRecord.setOperatorTime(nowDateTime);
                    autoRecord.setOperatorReason("超时未审核，系统自动通过");
                    autoRecord.setUpdateUserId("-1");
                    autoRecord.setUpdateUserName("leaveAutoTask");
                    autoRecord.setUpdateTime(nowDateTime);
                    sfaLeaveRecordUpdateList.add(autoRecord);
                }
            }
        });

        newLeaveRecordService.saveOrUpdateBatch(sfaLeaveRecordUpdateList);
//        newLeaveService.saveOrUpdateBatch(sfaLeaveList);

        return ReturnT.SUCCESS;
    }

}
