package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/10/上午9:23
 */
@Component
@Slf4j
public class MarketVisitCheckTask {


    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;

    @XxlJob("marketVisitCheck")
    @Transactional
    public ReturnT<String> check(String param){
        XxlJobLogger.log("【市场走访检查】start..");
        LocalDateTime  checkTime = getDate(param);
        XxlJobLogger.log("【市场走访检查】执行日期:{}",checkTime.toString());
        // 获取市场走访超时的记录
        List<Integer> ids = sfaInterviewProcessRecordMapper.selectMarketVisitOvertime(checkTime.toString());
        if(CollectionUtils.isEmpty(ids)){
            XxlJobLogger.log("【市场走访检查】暂无过期条目");
            return ReturnT.SUCCESS;
        }

        XxlJobLogger.log("【市场走访检查】过期条目数:{}",ids.size());

        ids.forEach(id -> {
            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(id);
            if(Objects.nonNull(sfaInterviewProcessRecordModel)){
                sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.FAILED.getResultCode());
                sfaInterviewProcessRecordModel.setProcessDate(new Date());
                sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);

                Integer interviewProcessId = sfaInterviewProcessRecordModel.getInterviewProcessId();
                SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(interviewProcessId);
                if(Objects.nonNull(sfaInterviewProcessModel)){
                    sfaInterviewProcessModel.setProcessResult(ProcessResult.FAILED.getResultCode());
                    sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
                }
            }
        });

        return ReturnT.SUCCESS;
    }

    private LocalDateTime getDate(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDateTime.now();
        }

        return LocalDate.parse(param).atStartOfDay();
    }
}
