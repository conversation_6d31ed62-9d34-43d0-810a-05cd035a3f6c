package com.wantwant.sfa.backend.Task;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.domain.notify.DO.NotifyDO;
import com.wantwant.sfa.backend.domain.notify.service.INotifyPushService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.meeting.MeetingInfoMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.meeting.MeetingInfoPO;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.meeting.MeetingScheduleMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.meeting.entity.MeetingScheduleEntity;
import com.wantwant.sfa.backend.util.GeTuiUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.time.temporal.TemporalAdjusters;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/18/下午4:02
 */
@Component
@Slf4j
public class MeetingInfoTask {

    @Autowired
    private GeTuiUtil geTuiUtil;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Resource
    private INotifyPushService notifyPushService;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private MeetingScheduleMapper meetingScheduleMapper;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Resource
    private MeetingInfoMapper meetingInfoMapper;
    @Resource
    private RedisUtil redisUtil;
    private static final String NEXT_ORG_KEY = "sfa:next:org:";
    @Resource
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @XxlJob("meetingNotice")
    @Transactional
    public ReturnT<String> meetingNotice(String param){

        LocalDate currentDate = getCurrentDate(param);

        int dayOfMonth = currentDate.getDayOfMonth();
        
        LocalDate startDate = currentDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate endDate = currentDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));


        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = Optional.ofNullable(ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeName, "代理督导").last("limit 1")))
                .orElse(new CeoBusinessOrganizationPositionRelation());

        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().in(SfaPositionRelationEntity::getPositionTypeId, 1, 12,2,11)
                .eq(SfaPositionRelationEntity::getStatus, 1).ne(SfaPositionRelationEntity::getEmpId,ceoBusinessOrganizationPositionRelation.getEmployeeId()).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            log.info("【meeting notice】无需提醒的岗位");
            return ReturnT.SUCCESS;
        }


        List<SfaPositionRelationEntity> sendNoticePositionList = new ArrayList<>();
        // 筛选出下一级有人的组织
        positionRelationEntityList.stream().forEach(e -> {
            // 获取直属下级
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationCode());
            if(!CollectionUtils.isEmpty(nextOrgCode)){

                AtomicInteger managerCount = new AtomicInteger(0);
                AtomicInteger cityManagerCount = new AtomicInteger(0);

                nextOrgCode.forEach(f -> {
                    String organizationType = organizationMapper.getOrganizationType(f);
                    if(organizationType.equals("province") || organizationType.equals("company")){
                        managerCount.getAndIncrement();
                    }else if(organizationType.equals("department")){
                        cityManagerCount.getAndIncrement();
                    }
                });


                saveSchedule(startDate,endDate,e.getOrganizationCode(),e.getEmployeeInfoId(),2,managerCount.get(),cityManagerCount.get());
                sendNoticePositionList.add(e);
            }

        });

        if(CollectionUtils.isEmpty(sendNoticePositionList)){
            log.info("【meeting notice】无需提醒的岗位");
            return ReturnT.SUCCESS;
        }

        List<String> empList = sendNoticePositionList.stream().filter(f -> StringUtils.isNotBlank(f.getEmpId()) && !f.equals("00500349")).map(SfaPositionRelationEntity::getEmpId).distinct().collect(Collectors.toList());
        empList = empList.stream().distinct().collect(Collectors.toList());
        NotifyDO notifyDO = new NotifyDO();
        notifyDO.setEmpIds(empList);
        notifyDO.setType(3);
        notifyDO.setTitle("您好，今日需开周会，请至会议管理预约会议，已预约请忽略");
        notifyDO.setCode("weekMeetingRoute");
        notifyDO.setContent("您好，今日需开周会，请至会议管理预约会议，已预约请忽略");
        notifyDO.setCreateBy("-1");
        notifyDO.setUpdateBy("-1");
        notifyPushService.saveBatch(notifyDO);

        return ReturnT.SUCCESS;
    }


    @XxlJob("meetingDailyNotice")
    @Transactional
    public ReturnT<String> meetingDailyNotice(String param){

        LocalDate currentDate = getCurrentDate(param);

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = Optional.ofNullable(ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeName, "代理督导").last("limit 1")))
                .orElse(new CeoBusinessOrganizationPositionRelation());


        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().in(SfaPositionRelationEntity::getPositionTypeId, 1, 12,11,2)
                .eq(SfaPositionRelationEntity::getStatus, 1).ne(SfaPositionRelationEntity::getEmpId,ceoBusinessOrganizationPositionRelation.getEmployeeId()).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            log.info("【meeting notice】无需提醒的岗位");
            return ReturnT.SUCCESS;
        }


        List<SfaPositionRelationEntity> sendNoticePositionList = new ArrayList<>();
        // 筛选出下一级有人的组织
        positionRelationEntityList.stream().forEach(e -> {
            // 获取直属下级
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationCode());
            if(!CollectionUtils.isEmpty(nextOrgCode)){

                AtomicInteger managerCount = new AtomicInteger(0);
                AtomicInteger cityManagerCount = new AtomicInteger(0);

                nextOrgCode.forEach(f -> {
                    String organizationType = organizationMapper.getOrganizationType(f);
                    if(organizationType.equals("province") || organizationType.equals("company")){
                        managerCount.getAndIncrement();
                    }else if(organizationType.equals("department")){
                        cityManagerCount.getAndIncrement();
                    }
                });

                saveSchedule(currentDate,currentDate,e.getOrganizationCode(),e.getEmployeeInfoId(),1,managerCount.get(),cityManagerCount.get());
                sendNoticePositionList.add(e);
            }
        });

        if(CollectionUtils.isEmpty(sendNoticePositionList)){
            log.info("【meeting notice】无需提醒的岗位");
            return ReturnT.SUCCESS;
        }


        List<String> empList = sendNoticePositionList.stream().filter(f -> StringUtils.isNotBlank(f.getEmpId()) && !f.equals("00500349")).map(SfaPositionRelationEntity::getEmpId).distinct().collect(Collectors.toList());
        empList = empList.stream().distinct().collect(Collectors.toList());
        NotifyDO notifyDO = new NotifyDO();
        notifyDO.setEmpIds(empList);
        notifyDO.setType(3);
        notifyDO.setTitle("您好，今日需开日会，请至会议管理预约会议，已预约请忽略");
        notifyDO.setCode("dailyMeetingRoute");
        notifyDO.setContent("您好，今日需开日会，请至会议管理预约会议，已预约请忽略");
        notifyDO.setCreateBy("-1");
        notifyDO.setUpdateBy("-1");
        notifyPushService.saveBatch(notifyDO);

        return ReturnT.SUCCESS;
    }


    @XxlJob("meetingMonthNotice")
    @Transactional
    public ReturnT<String> meetingMonthNotice(String param){

        LocalDate currentDate = getCurrentDate(param);

        LocalDate startDate = currentDate.with(TemporalAdjusters.firstDayOfNextMonth());


        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = Optional.ofNullable(ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeName, "代理督导").last("limit 1")))
                .orElse(new CeoBusinessOrganizationPositionRelation());


        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().in(SfaPositionRelationEntity::getPositionTypeId, 1, 12,11,2)
                .eq(SfaPositionRelationEntity::getStatus, 1).ne(SfaPositionRelationEntity::getEmpId,ceoBusinessOrganizationPositionRelation.getEmployeeId()).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            log.info("【meeting notice】无需提醒的岗位");
            return ReturnT.SUCCESS;
        }

        Map<String,Integer> map = new HashMap<>();

        List<SfaPositionRelationEntity> sendNoticePositionList = new ArrayList<>();
        // 筛选出下一级有人的组织
        positionRelationEntityList.stream().forEach(e -> {
            int day = 20;
            if(e.getPositionTypeId() == 12 || e.getPositionTypeId() == 1){
                day = 15;
            }
            LocalDate endDate = LocalDate.of(startDate.getYear(), startDate.getMonthValue(), day);
            // 获取直属下级
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationCode());
            if(!CollectionUtils.isEmpty(nextOrgCode)){
                AtomicInteger managerCount = new AtomicInteger(0);
                AtomicInteger cityManagerCount = new AtomicInteger(0);

                nextOrgCode.forEach(f -> {
                    String organizationType = organizationMapper.getOrganizationType(f);
                    if(organizationType.equals("province") || organizationType.equals("company")){
                        managerCount.getAndIncrement();
                    }else if(organizationType.equals("department")){
                        cityManagerCount.getAndIncrement();
                    }
                });

                saveSchedule(startDate,endDate,e.getOrganizationCode(),e.getEmployeeInfoId(),3,managerCount.get(),cityManagerCount.get());
                sendNoticePositionList.add(e);
            }
            map.put(e.getEmpId(),day);

        });

        map.forEach((key,value) -> {
            if(!key.equals("00500349")){
                List<String> empList = new ArrayList<>();
                empList.add(key);
                NotifyDO notifyDO = new NotifyDO();
                notifyDO.setEmpIds(empList);
                notifyDO.setType(3);
                notifyDO.setTitle("请于本月1 - "+value+"号组织召开【月/季】会,已召开请忽略");
                notifyDO.setCode("monthMeetingRoute");
                notifyDO.setContent("请于本月1 - "+value+"号组织召开【月/季】会,已召开请忽略");
                notifyDO.setCreateBy("-1");
                notifyDO.setUpdateBy("-1");
                notifyPushService.saveBatch(notifyDO);
            }
        });


        return ReturnT.SUCCESS;
    }




    @XxlJob("meetingComplete")
    @Transactional
    public ReturnT<String> autoComplete(String param){
        LocalDate currentDay = getCurrentDay(param);

        List<MeetingInfoPO> meetingInfoPOS = meetingInfoMapper.selectList(new LambdaQueryWrapper<MeetingInfoPO>().likeRight(MeetingInfoPO::getStartTime, currentDay.toString()).isNull(MeetingInfoPO::getActualEndTime).eq(MeetingInfoPO::getStatus, 0).eq(MeetingInfoPO::getIsDelete, 0));
        if(CollectionUtils.isEmpty(meetingInfoPOS)){
            log.info("【meeting auto complete】无可操作的会议");
            return ReturnT.SUCCESS;
        }

        meetingInfoPOS.forEach(e -> {
            LocalDateTime actualStartTime = e.getActualStartTime();
            if(Objects.isNull(actualStartTime)){
                e.setActualStartTime(LocalDateTime.now());
            }
            String category = e.getCategory();
            if("日会".equals(category)){
                e.setStatus(2);
            }
            e.setTag(1);
            e.setActualEndTime(LocalDateTime.now());
            meetingInfoMapper.updateById(e);
        });

        return ReturnT.SUCCESS;
    }

    @XxlJob("meetingEndReminder")
    @Transactional
    public ReturnT<String> meetingEndReminder(String param) {

        log.info("会议超时提醒推送开始..param:{}", param);

        List<MeetingInfoPO> list = meetingInfoMapper.selectList(new LambdaQueryWrapper<MeetingInfoPO>()
                .le(MeetingInfoPO::getActualStartTime, LocalDateTime.now().minusHours(6))
                .isNull(MeetingInfoPO::getActualEndTime)
                .eq(MeetingInfoPO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(list)) {
            log.info("【meetingEndReminder】无可操作的会议");
            return ReturnT.SUCCESS;
        }
        try {
            List<String> organizationIdList = list.stream().map(MeetingInfoPO::getOrganizationId).collect(Collectors.toList());
            Map<String, CeoBusinessOrganizationEntity> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, organizationIdList)
            ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, v -> v));
            Map<Integer, String> businessGroupMap = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>())
                    .stream().collect(Collectors.toMap(SfaBusinessGroupEntity::getId, SfaBusinessGroupEntity::getBusinessGroupName));

            list.forEach(e -> {
                String payload = "{\"title\":\"会议超时提醒\",\"infoId\":\"" + e.getInfoId()
                        + "\",\"businessGroup\":\"" + organizationMap.get(e.getOrganizationId()).getBusinessGroup()
                        + "\",\"businessGroupName\":\"" + businessGroupMap.get(organizationMap.get(e.getOrganizationId()).getBusinessGroup())
                        + "\",\"type\":605}";
                geTuiUtil.AppPushToSingleSync(e.getLeaderId(), "会议超时提醒", "您好，您主持的【\n" +
                        e.getSubclass() + "】会议时长已超过6小时，"
                        + "\n如会议已结束请点击进入操作结束，未结束请忽略！！", payload, 1);
                if (!e.getLeaderId().equals(e.getCreateBy())) {
                    geTuiUtil.AppPushToSingleSync(e.getCreateBy(), "会议超时提醒", "您好，您创建的【\n" +
                            e.getSubclass() + "】会议时长已超过6小时，"
                            + "\n如会议已结束请点击进入操作结束，未结束请忽略！！", payload, 1);
                }
            });
        } catch (Exception e) {
            log.error("会议超时提醒推送异常：", e);
        }
        return ReturnT.SUCCESS;
    }

    private LocalDate getCurrentDay(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now().minusDays(1);
        }
        return LocalDate.parse(param);
    }


    // 保存schedule
    private void saveSchedule(LocalDate startDate, LocalDate endDate, String organizationCode, Integer employeeInfoId, int type,int manangerCount,int cityManagerCount) {
        MeetingScheduleEntity meetingScheduleEntity = new MeetingScheduleEntity();
        meetingScheduleEntity.setStartDate(startDate);
        meetingScheduleEntity.setEndDate(endDate);
        meetingScheduleEntity.setEmployeeInfoId(employeeInfoId);
        meetingScheduleEntity.setOrganizationId(organizationCode);
        meetingScheduleEntity.setType(type);
        meetingScheduleEntity.setCityManagerCount(cityManagerCount);
        meetingScheduleEntity.setManagerCount(manangerCount);
        meetingScheduleMapper.insert(meetingScheduleEntity);
    }

    private LocalDate getCurrentDate(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now();
        }
        return LocalDate.parse(param);
    }
}
