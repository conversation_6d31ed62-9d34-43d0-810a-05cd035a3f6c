package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.entity.MessageTaskEntity;
import com.wantwant.sfa.backend.entity.MessageTaskInstanceEntity;
import com.wantwant.sfa.backend.mapper.MessageTaskInstanceMapper;
import com.wantwant.sfa.backend.mapper.MessageTaskMapper;
import com.wantwant.sfa.backend.service.InfoCenterTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/24/下午2:32
 */
@Component
@Slf4j
public class MessageTask {
    @Autowired
    private MessageTaskInstanceMapper messageTaskInstanceMapper;
    @Autowired
    private InfoCenterTaskService infoCenterTaskService;

    @XxlJob("messageTask")
    @Transactional
    public ReturnT<String> excute(String param) {
        // 获取执行时间
        LocalDateTime date = getTime(param);
        XxlJobLogger.log("message task start,date:{}",date.toString());
        List<MessageTaskInstanceEntity> messageTaskInstanceEntities = messageTaskInstanceMapper.selectList(new QueryWrapper<MessageTaskInstanceEntity>().le("publish_time", date).eq("finish", 0));

        if(CollectionUtils.isEmpty(messageTaskInstanceEntities)){
            return ReturnT.SUCCESS;
        }

        messageTaskInstanceEntities.forEach(e -> {
            infoCenterTaskService.doPublish(e.getTemplateId());
        });

        return ReturnT.SUCCESS;
    }

    private LocalDateTime getTime(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDateTime.now();
        }

        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return  LocalDate.parse(param, format).atStartOfDay();

    }
}
