package com.wantwant.sfa.backend.Task;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.model.ResignApplyModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.interview.SfaApplyResignMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionApplyMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionProcessMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionProcessRecordMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.notify.model.PositionTransactionNotifyModel;
import com.wantwant.sfa.backend.notify.template.impl.PositionTransactionNotifyContent;
import com.wantwant.sfa.backend.notify.template.impl.ProbationNotifyContent;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionProcessRecordEntity;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 人员入职/离职定时任务
 * @Auther: zhengxu
 * @Date: 2022/07/07/下午2:44
 */
@Component
@Slf4j
public class MobilizationTask {

    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyContentMapper notifyContentMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private SfaApplyResignMapper sfaApplyResignMapper;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private SfaTransactionApplyMapper sfaTransactionApplyMapper;
    @Autowired
    private SfaTransactionProcessMapper sfaTransactionProcessMapper;
    @Autowired
    private SfaTransactionProcessRecordMapper sfaTransactionProcessRecordMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private IAuditService auditService;

    private String titleTemplate = "人员变更通知内容{0}";

    private String messageTemplate = "您好，{0}，系统提醒您人员变更清单如下，烦请进行处理，如已处理请忽略。";



    @XxlJob("onboardMessage")
    @Transactional
    public ReturnT<String> onboardMessage(String day){
        XxlJobLogger.log("onboardMessage start..");
        // 获取定时任务日期
        LocalDate date = getDate(day);
        XxlJobLogger.log("当前执行日期:{}",date.toString());

        // 获取所有今日入职的人员清单
        List<SfaInterviewProcessRecordModel> sfaInterviewProcessRecordModels = sfaInterviewProcessRecordMapper.selectList(new QueryWrapper<SfaInterviewProcessRecordModel>()
                .eq("process_type", 6)
                .eq("process_result", 1)
                .ge("process_date", date.toString())
        );


        if(CollectionUtils.isEmpty(sfaInterviewProcessRecordModels)){
            XxlJobLogger.log("今日入职人数:0人");
            return ReturnT.SUCCESS;
        }else{
            sfaInterviewProcessRecordModels =  sfaInterviewProcessRecordModels.stream().filter(e -> Objects.isNull(e.getResignApplyId())).collect(Collectors.toList());
            XxlJobLogger.log("今日入职人数:" + sfaInterviewProcessRecordModels.size()+"人");
        }

        sfaInterviewProcessRecordModels.forEach(e -> {
            SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(e.getInterviewProcessId());
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", sfaInterviewProcessModel.getApplicationId()));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                XxlJobLogger.log("员工信息获取失败,applicationId:{}",sfaInterviewProcessModel.getApplicationId());
                return;
            }

            String executeTime = StringUtils.EMPTY;
            if(Objects.nonNull(sfaInterviewProcessModel.getOnboardTime())){
                executeTime = DateUtil.format(sfaInterviewProcessModel.getOnboardTime(),"yyyy-MM-dd");
            }
            String adviceTime = StringUtils.EMPTY;
            if(Objects.nonNull(sfaInterviewProcessModel.getRecommendOnboardTime())){
                adviceTime = LocalDateTimeUtils.formatTime(sfaInterviewProcessModel.getRecommendOnboardTime(),LocalDateTimeUtils.yyyy_MM_dd);
            }

            List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));

            // 发送消息
            sendMessage(adviceTime,executeTime,sfaEmployeeInfoModel,positionRelationEntityList,"入职完成");
        });

        XxlJobLogger.log("onboardMessage end..");
        return ReturnT.SUCCESS;
    }


    @XxlJob("positionTransaction")
    @Transactional
    public ReturnT<String> positionTransactionMessage(String day){
        XxlJobLogger.log("positionTransactionMessage start..");
        // 获取定时任务日期
        LocalDate date = getDate(day);
        XxlJobLogger.log("当前执行日期:{}",date.toString());

        // 获取所有今日异动人员清单
        List<SfaTransactionProcessRecordEntity> recordEntities = sfaTransactionProcessRecordMapper.selectList(new QueryWrapper<SfaTransactionProcessRecordEntity>()
                .eq("process_type", 1)
                .eq("process_result", 1)
                .eq("execute_process_date", date.toString())
        );


        if(CollectionUtils.isEmpty(recordEntities)){
            XxlJobLogger.log("今日异动人数:0人");
            return ReturnT.SUCCESS;
        }else{
            XxlJobLogger.log("今日入职人数:" + recordEntities.size()+"人");
        }

        recordEntities.forEach(e -> {
            SfaTransactionProcessEntity transactionProcessEntity = sfaTransactionProcessMapper.selectById(e.getTransactionProcessId());
            SfaTransactionApplyEntity sfaTransactionApplyEntity = sfaTransactionApplyMapper.selectById(transactionProcessEntity.getTransactionApplyId());
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(sfaTransactionApplyEntity.getEmployeeInfoId());
            if(Objects.isNull(sfaEmployeeInfoModel)){
                XxlJobLogger.log("员工信息获取失败,applicationId:{}",sfaEmployeeInfoModel.getApplicationId());
                return;
            }

            // 发送消息
            String adviceTime = StringUtils.EMPTY;
            if(Objects.nonNull(sfaTransactionApplyEntity.getAdviceExecuteDate())){
                adviceTime = sfaTransactionApplyEntity.getAdviceExecuteDate().toString();
            }


            List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));


            sendMessage(adviceTime,e.getExecuteProcessDate(),sfaEmployeeInfoModel,positionRelationEntityList,"异动完成");
        });

        XxlJobLogger.log("positionTransactionMessage end..");
        return ReturnT.SUCCESS;
    }


    @XxlJob("offBoardMessage")
    @Transactional
    public ReturnT<String> offBoardMessage(String day){
        XxlJobLogger.log("offBoardMessage start..");
        // 获取定时任务日期
        LocalDate date = getDate(day);
        XxlJobLogger.log("当前执行日期:{}",date.toString());

        // 获取所有今日离职的人员清单
        List<SfaInterviewProcessRecordModel> sfaInterviewProcessRecordModels = sfaInterviewProcessRecordMapper.selectList(new QueryWrapper<SfaInterviewProcessRecordModel>().eq("process_type", 8).eq("process_result", 1).ge("process_date", date.toString()));


        if(CollectionUtils.isEmpty(sfaInterviewProcessRecordModels)){
            XxlJobLogger.log("今日离职人数:0人");
            return ReturnT.SUCCESS;
        }else{
            XxlJobLogger.log("今日离职人数:" + sfaInterviewProcessRecordModels.size()+"人");
        }

        sfaInterviewProcessRecordModels.forEach(e -> {
            SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(e.getInterviewProcessId());
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", sfaInterviewProcessModel.getApplicationId()));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                XxlJobLogger.log("员工信息获取失败,applicationId:{}",sfaInterviewProcessModel.getApplicationId());
                return;
            }

            // 获取人资办理的信息，拿取离职申请信息
            List<SfaInterviewProcessRecordModel> hrInterviewProcessRecords = sfaInterviewProcessRecordMapper.selectList(new QueryWrapper<SfaInterviewProcessRecordModel>().eq("interview_process_id", e.getInterviewProcessId()).eq("process_type", 6).orderByAsc("id"));
            if(CollectionUtils.isEmpty(hrInterviewProcessRecords)){
                XxlJobLogger.log("离职审核信息获取失败,applicationId:{}",sfaInterviewProcessModel.getApplicationId());
                return;
            }

            SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = hrInterviewProcessRecords.get(0);
            Integer resignApplyId = sfaInterviewProcessRecordModel.getResignApplyId();
            ResignApplyModel resignApplyModel = sfaApplyResignMapper.selectById(resignApplyId);
            if(Objects.isNull(resignApplyModel)){
                XxlJobLogger.log("离职审核信息获取失败,applicationId:{}",sfaInterviewProcessModel.getApplicationId());
                return;
            }

            String executeTime = StringUtils.EMPTY;
            if(Objects.nonNull(sfaInterviewProcessModel.getOffTime())){
                executeTime = DateUtil.format(sfaInterviewProcessModel.getOffTime(),"yyyy-MM-dd");
            }
            String adviceTime = StringUtils.EMPTY;
            if(Objects.nonNull(resignApplyModel.getAdviceDepartureTime())){
                adviceTime = resignApplyModel.getAdviceDepartureTime().toString();
            }


            // 发送消息
            List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
            sendMessage(adviceTime,executeTime,sfaEmployeeInfoModel,positionRelationEntityList,"离职完成");
        });

        XxlJobLogger.log("offBoardMessage end..");
        return ReturnT.SUCCESS;
    }



    // 发送入职信息
    private void sendMessage(String adviceTime, String executeTime,SfaEmployeeInfoModel sfaEmployeeInfoModel , List<SfaPositionRelationEntity> positionList,String status) {
        if(CollectionUtils.isEmpty(positionList)){
            return;
        }

        String day = DateUtil.format(new Date(), "yyyy年-MM月-dd日");
        String title = MessageFormat.format(titleTemplate,day);
        String message = MessageFormat.format(messageTemplate,day);




        positionList.forEach(e -> {
            SelectAuditDto selectAuditDto = new SelectAuditDto();
            selectAuditDto.setChannel(3);
            selectAuditDto.setCurrentOrganizationId(e.getParentOrganizationCode());
            selectAuditDto.setBusinessGroup(e.getBusinessGroup());
            selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_senior_hr_employee_id"));
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = auditService.chooseAuditPerson(selectAuditDto);

            if(Objects.nonNull(ceoBusinessOrganizationPositionRelation) && StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())){
                doSendMessage(sfaEmployeeInfoModel, title, message, ceoBusinessOrganizationPositionRelation.getEmployeeId(),adviceTime,executeTime,status);
            }
        });


    }




    private void doSendMessage(SfaEmployeeInfoModel sfaEmployeeInfoModel, String title, String message, String employeeId, String adviceTime, String executeTime,String status) {
        // 保存消息记录
        NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.EMP_TRANSACTION.getType(), title, employeeId, message);
        PositionTransactionNotifyContent positionTransactionNotifyContent = new PositionTransactionNotifyContent();

        PositionTransactionNotifyModel positionTransactionNotifyModel = new PositionTransactionNotifyModel();
        positionTransactionNotifyModel.setTemplateId(notifyPO.getTemplateId());
        positionTransactionNotifyModel.setAreaName(sfaEmployeeInfoModel.getAreaName());
        positionTransactionNotifyModel.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
        positionTransactionNotifyModel.setDepartmentName(sfaEmployeeInfoModel.getDepartmentName());
        positionTransactionNotifyModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());


        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
        positionTransactionNotifyModel.setPosition(PositionEnum.getPositionName(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition()));

        positionTransactionNotifyModel.setStatus(status);
        positionTransactionNotifyModel.setExecuteTime(executeTime);
        positionTransactionNotifyModel.setAdviceTime(adviceTime);
        positionTransactionNotifyModel.setMobile(sfaEmployeeInfoModel.getMobile());
        NotifyContentEntity notifyContentEntity = positionTransactionNotifyContent.buildNotifyContent(positionTransactionNotifyModel);
        notifyContentMapper.insert(notifyContentEntity);
    }

    private LocalDate getDate(String day) {
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(StringUtils.isNotBlank(day)){
            return  LocalDate.parse(day, format);
        }
        return LocalDate.now();
    }
}
