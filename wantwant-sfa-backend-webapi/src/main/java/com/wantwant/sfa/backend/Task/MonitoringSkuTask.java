package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.MonitoringSkuService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Slf4j
@Component
public class MonitoringSkuTask {

  @Autowired private MonitoringSkuService monitoringSkuService;

  /** 监控旺铺与大数据Sku定时任务 */
  @XxlJob("monitoringSkuTasks")
  public ReturnT<String> monitoringSkuTasks(String s) {
    LocalDate date = LocalDate.now();
    log.info("execute MonitoringSkuTask date:{}", date.toString());
    monitoringSkuService.sleMonitoringSkuList();
    return ReturnT.SUCCESS;
  }
}
