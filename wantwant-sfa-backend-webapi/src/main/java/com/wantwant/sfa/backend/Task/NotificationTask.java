package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.estimate.service.IEstimateCheckService;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.review.service.IReviewReportSearchService;
import com.wantwant.sfa.backend.review.vo.ReviewReportPointVo;
import com.wantwant.sfa.backend.service.NotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/13/上午8:45
 */
@Component
@Slf4j
public class NotificationTask {
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private IReviewReportSearchService reviewReportSearchService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private IEstimateCheckService estimateCheckService;

    @XxlJob("agentNotify")
    @Transactional
    public ReturnT<String> agentNotify(String param) {
        String executeDate = getExecuteDate(param);
        log.info("【agent notify task】start,executeDate:{}",executeDate);

        // 获取所有大区人员信息
        List<CeoBusinessOrganizationPositionRelation> areaPositionList = ceoBusinessOrganizationPositionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("channel", 3)
                .eq("position_type_id", 1)
                .isNotNull("employee_id")
                .ne("employee_id", "")
        );

        if(!CollectionUtils.isEmpty(areaPositionList)){
            areaPositionList.forEach(e -> {
                // 检查销售预估
                checkEstimate(e.getEmployeeId(),executeDate);

                // 检查月报助手
                checkReviewReport(e.getEmployeeId(),executeDate);
            });
        }



        // 获取所有分公司人员信息
        List<CeoBusinessOrganizationPositionRelation> companyPositionList = ceoBusinessOrganizationPositionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("channel", 3)
                .eq("position_type_id", 2)
                .isNotNull("employee_id")
                .ne("employee_id", "")
        );

        if(!CollectionUtils.isEmpty(companyPositionList)){
            companyPositionList.forEach(e -> {
                // 检查销售预估
                checkEstimate(e.getEmployeeId(),executeDate);
                // 检查月报助手
                checkReviewReport(e.getEmployeeId(),executeDate);
            });

        }


        // 获取所有区域经理人员信息
        // 获取所有分公司人员信息
        List<CeoBusinessOrganizationPositionRelation> departmentPositionList = ceoBusinessOrganizationPositionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("channel", 3)
                .eq("position_type_id", 10)
                .isNotNull("employee_id")
                .ne("employee_id", "")
        );

        if(!CollectionUtils.isEmpty(departmentPositionList)){
            // 检查销售预估
            departmentPositionList.forEach(e -> {
                // 检查销售预估
                checkEstimate(e.getEmployeeId(),executeDate);
            });

        }

        return ReturnT.SUCCESS;
    }

    private void checkEstimate(String employeeId, String executeDate) {
        int count = estimateCheckService.getAgentCount(employeeId,executeDate);
        if(count > 0){
            List<NotifyPO> notifyPOS = new ArrayList<>();
            // 发送消息
            NotifyPO po = new NotifyPO();
            po.setTitle("销售预估提报通知"+executeDate);
            po.setType(1);
            po.setContent("销售预估提报通知"+executeDate);
            po.setCode("/ReportHelper");
            po.setEmployeeId(employeeId);
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);

            notifyService.saveBatch(notifyPOS);
        }
    }

    private void checkReviewReport(String employeeId, String executeDate) {
        ReviewReportPointVo reviewReportPoint = reviewReportSearchService.getReviewReportPoint(employeeId, executeDate, RequestUtils.getBusinessGroup());
        if(Objects.isNull(reviewReportPoint)){
            return;
        }

        int uncommittedCount = reviewReportPoint.getUncommittedCount();
        if(uncommittedCount > 0){
            List<NotifyPO> notifyPOS = new ArrayList<>();
            // 发送消息
            NotifyPO po = new NotifyPO();
            po.setTitle("月度复盘报告提交通知"+executeDate);
            po.setType(1);
            po.setContent("月度复盘报告提交通知"+executeDate);
            po.setCode("/ReportHelper");
            po.setEmployeeId(employeeId);
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);

            notifyService.saveBatch(notifyPOS);
        }
    }

    private String getExecuteDate(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now().toString().substring(0,7);
        }

        return param;
    }

}
