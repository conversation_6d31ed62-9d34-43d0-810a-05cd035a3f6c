package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.organization.vo.OrganizationTreeVO;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/26/下午2:52
 */
@Component
@Slf4j
public class OrganizationTask {

    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    private static final String NEXT_ORG_KEY = "sfa:next:org:";


    @XxlJob("settingNexOrg")
    public ReturnT<String> execute(String param) {

        List<SfaBusinessGroupEntity> sfaBusinessGroupEntities = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>().eq(SfaBusinessGroupEntity::getDeleteFlag, 0));

        List<CompletableFuture> completableFutures = new ArrayList<>();
        // 获取总异常数


        sfaBusinessGroupEntities.forEach(e -> {
            CompletableFuture<Void> alertCountFuture = CompletableFuture.supplyAsync(() -> {

                String zbCode = organizationMapper.getZbOrganizationIdByBusinessGroup(e.getId());
                List<String> zbNext=new ArrayList<>();
                // 总部数据
                List<String> areaCodes = organizationMapper.selectAreaOrganization(e.getId());
                settingNextOrg(areaCodes,zbNext);
                redisUtil.set(NEXT_ORG_KEY+zbCode,zbNext);

                // 战区
                commonNextSetting(areaCodes);

                // 大区
                List<String> vareaCodes = organizationMapper.selectOrganizationByType("varea", 3, e.getId());
                commonNextSetting(vareaCodes);

                // 省区
                List<String> provinceCodes = organizationMapper.selectOrganizationByType("province", 3, e.getId());
                commonNextSetting(provinceCodes);

                // 分公司
                List<String> companyCodes = organizationMapper.selectOrganizationByType("company", 3, e.getId());
                commonNextSetting(companyCodes);
                return null;
            });
            completableFutures.add(alertCountFuture);
        });

        // 等待所有任务执行完
        CompletableFuture.allOf(completableFutures.stream().toArray(CompletableFuture[]::new)).join();


        return ReturnT.SUCCESS;
    }

    private void commonNextSetting(List<String> areaCodes) {
        List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationPositionRelations = ceoBusinessOrganizationPositionRelationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .in(CeoBusinessOrganizationPositionRelation::getOrganizationId, areaCodes)
                .isNotNull(CeoBusinessOrganizationPositionRelation::getEmployeeId)
                .ne(CeoBusinessOrganizationPositionRelation::getEmployeeId, "")
        );
        ceoBusinessOrganizationPositionRelations.forEach(area -> {
            List<String> areaNext=new ArrayList<>();
            List<OrganizationTreeVO> children = organizationMapper.getChildren(area.getOrganizationId(), 3);
            if(!CollectionUtils.isEmpty(children)){
                List<String> nextOrgCodes = children.stream().map(OrganizationTreeVO::getRegions).collect(Collectors.toList());
                settingNextOrg(nextOrgCodes,areaNext);
                redisUtil.set(NEXT_ORG_KEY+area.getOrganizationId(),areaNext);
            }
        });
    }

    private void settingNextOrg(List<String> orgCodes,List<String> children) {
        if(CollectionUtils.isEmpty(orgCodes)){
            return;
        }

        orgCodes.forEach(e -> {
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, e)
            );
            if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                String employeeId = ceoBusinessOrganizationPositionRelation.getEmployeeId();
                String employeeName = ceoBusinessOrganizationPositionRelation.getEmployeeName();
                if(StringUtils.isBlank(employeeId) || "代理督导".equals(employeeName)){
                    if(ceoBusinessOrganizationPositionRelation.getPositionTypeId() != 10 && ceoBusinessOrganizationPositionRelation.getPositionTypeId() != 3){
                        List<CeoBusinessOrganizationPositionRelation> ceoBusinessOrganizationPositionRelations = ceoBusinessOrganizationPositionRelationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationParentId, ceoBusinessOrganizationPositionRelation.getOrganizationId()));
                        if(!CollectionUtils.isEmpty(ceoBusinessOrganizationPositionRelations)){
                            List<String> nextOrgCodes = ceoBusinessOrganizationPositionRelations.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
                            settingNextOrg(nextOrgCodes,children);
                        }
                    }
                }else{
                    children.add(ceoBusinessOrganizationPositionRelation.getOrganizationId());
                }
            }

        });
    }
}
