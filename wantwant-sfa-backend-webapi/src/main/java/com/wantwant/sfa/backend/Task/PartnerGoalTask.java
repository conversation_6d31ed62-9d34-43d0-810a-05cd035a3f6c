package com.wantwant.sfa.backend.Task;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.wantwant.commons.core.util.HttpUtil;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.application.NotifyApplication;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.employee.vo.ParentVo;
import com.wantwant.sfa.backend.enums.TargetSetEnum;
import com.wantwant.sfa.backend.mainProduct.request.MainProductGoalPushWp;
import com.wantwant.sfa.backend.mainProduct.request.MainProductGoalPushWpRequest;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.mainProduct.QuarterMainProductMapper;
import com.wantwant.sfa.backend.mapper.organizationGoal.QuarterOrganizationGoalExcelMapper;
import com.wantwant.sfa.backend.model.EmployeeGoalDetailPo;
import com.wantwant.sfa.backend.model.EmployeeGoalLogPO;
import com.wantwant.sfa.backend.model.EmployeeGoalPo;
import com.wantwant.sfa.backend.model.mainProduct.QuarterMainProductPO;
import com.wantwant.sfa.backend.model.organizationGoal.QuarterOrganizationGoalExcelPO;
import com.wantwant.sfa.backend.notify.dto.NotifyPushRequest;
import com.wantwant.sfa.backend.organizationGoal.vo.EmpGoalVO;
import com.wantwant.sfa.backend.service.impl.EmployeeGoalDetailServiceImpl;
import com.wantwant.sfa.backend.service.impl.EmployeeGoalLogServiceImpl;
import com.wantwant.sfa.backend.service.impl.EmployeeGoalServiceImpl;
import com.wantwant.sfa.backend.service.impl.RealtimeDataServiceImpl;
import com.wantwant.sfa.backend.util.GoalUtils;
import com.wantwant.sfa.common.base.DateTimeUtility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PartnerGoalTask {

    @Autowired
    private EmployeeGoalServiceImpl employeeGoalServiceImpl;
    @Autowired
    private EmployeeGoalLogServiceImpl employeeGoalLogServiceImpl;
    @Autowired
    private RealtimeDataServiceImpl realtimeDataServiceImpl;

    @Autowired
    private EmployeeGoalMapper employeeGoalMapper;
    @Autowired
    private EmployeeGoalLogMapper employeeGoalLogMapper;
    @Autowired
    private QuarterOrganizationGoalExcelMapper quarterOrganizationGoalExcelMapper;
    @Autowired
    private SettingsMapper settingsMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Resource
    private EmployeeGoalDetailMapper employeeGoalDetailMapper;
    @Resource
    private EmployeeGoalDetailServiceImpl employeeGoalDetailServiceImpl;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private QuarterMainProductMapper quarterMainProductMapper;

    @Resource
    private NotifyApplication notifyApplication;

    @Value("${URL.ROOT.pushWpMainProduct:}")
    private String pushWpMainProduct;

    @XxlJob("partnerGoalAuditTask")
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> partnerGoalAudit(String param) {

        log.info("合伙人目标设置自动审核定时任务开始..param:{}", param);

        List<EmpGoalVO> auditList = realtimeDataServiceImpl.auditPartnerGoalList();
        if (CollectionUtils.isEmpty(auditList)) {
            log.info("无待审核的记录.");
            return ReturnT.SUCCESS;
        }

        List<EmployeeGoalPo> employeeGoalPoList = employeeGoalMapper.selectList(new LambdaQueryWrapper<EmployeeGoalPo>()
                .in(EmployeeGoalPo::getId, auditList.stream().map(EmpGoalVO::getId).collect(Collectors.toList()))
                .eq(EmployeeGoalPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalPo::getRange, 0)
                .eq(EmployeeGoalPo::getIsDelete, 0));
        if (CollectionUtils.isEmpty(employeeGoalPoList) || employeeGoalPoList.size() != auditList.size()) {
            throw new ApplicationException("待审核记录不存在");
        }
        Map<Integer, EmployeeGoalPo> employeeGoalPoMap = employeeGoalPoList.stream().collect(Collectors.toMap(EmployeeGoalPo::getId, v -> v));

        List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>()
                .in(EmployeeGoalLogPO::getGoalId, auditList.stream().map(EmpGoalVO::getId).collect(Collectors.toList()))
                .eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalLogPO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(employeeGoalLogPOList) || employeeGoalLogPOList.size() != auditList.size()) {
            throw new ApplicationException("待审核履历不存在");
        }
        Map<Integer, EmployeeGoalLogPO> employeeGoalLogPOMap = employeeGoalLogPOList.stream().collect(Collectors.toMap(EmployeeGoalLogPO::getGoalId, v -> v));

        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();
        Map<Integer, Integer> deadlineMap = getDeadline();
        Integer days = Integer.valueOf(settingsMapper.getSfaSettingsByCode("partner_goal_set_deadline_days"));

        List<EmployeeGoalPo> employeeGoalPoUpdateList = new ArrayList<>();
        List<EmployeeGoalLogPO> employeeGoalLogPOUpdateList = new ArrayList<>();

        auditList.stream().forEach(audit -> {
            Integer deadlineDay = deadlineMap.get(audit.getBusinessGroup());
            if (deadlineDay != null) {
                LocalDate deadline = LocalDate.now().withDayOfMonth(deadlineDay);
                if (audit.getOnJobTime().plusDays(days).isAfter(deadline)) {
                    deadline = audit.getOnJobTime().plusDays(days);
                }
                if (nowDate.isAfter(deadline)) {
                    EmployeeGoalPo employeeGoalPo = employeeGoalPoMap.get(audit.getId());
                    employeeGoalPo.setGoalStatus(DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1);
//                    employeeGoalPo.setUpdatedBy("-1");
//                    employeeGoalPo.setUpdatedName("partnerGoalAuditTask");
                    employeeGoalPo.setUpdatedTime(nowDateTime);
                    employeeGoalPoUpdateList.add(employeeGoalPo);

                    EmployeeGoalLogPO employeeGoalLogPO = employeeGoalLogPOMap.get(audit.getId());
                    employeeGoalLogPO.setGoalStatus(DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1);
                    employeeGoalLogPO.setReason("超时未审核，系统自动通过");
//                    employeeGoalLogPO.setUpdatedBy("-1");
//                    employeeGoalLogPO.setUpdatedName("partnerGoalAuditTask");
                    employeeGoalLogPO.setUpdatedTime(nowDateTime);
                    employeeGoalLogPOUpdateList.add(employeeGoalLogPO);
                }
            }
        });
        if (!CollectionUtils.isEmpty(employeeGoalPoUpdateList)) {
            employeeGoalServiceImpl.updateBatchById(employeeGoalPoUpdateList);
            employeeGoalLogServiceImpl.updateBatchById(employeeGoalLogPOUpdateList);
        }


        return ReturnT.SUCCESS;
    }

    @XxlJob("partnerGoalAutoTask")
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> partnerGoalAuto(String param) {

        log.info("合伙人目标设置自动定时任务开始..param:{}", param);

        List<EmpGoalVO> auditList = realtimeDataServiceImpl.auditPartnerGoalList();
        if (CollectionUtils.isEmpty(auditList)) {
            log.info("无待审核的记录.");
            return ReturnT.SUCCESS;
        }

        List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>()
                .in(EmployeeGoalLogPO::getGoalId, auditList.stream().map(EmpGoalVO::getId).collect(Collectors.toList()))
                .eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalLogPO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(employeeGoalLogPOList) || employeeGoalLogPOList.size() != auditList.size()) {
            throw new ApplicationException("待审核履历不存在");
        }
        Map<Integer, EmployeeGoalLogPO> employeeGoalLogPOMap = employeeGoalLogPOList.stream().collect(Collectors.toMap(EmployeeGoalLogPO::getGoalId, v -> v));

        List<EmployeeGoalLogPO> employeeGoalLogPOUpdateList = new ArrayList<>();
        LocalDateTime nowDateTime = LocalDateTime.now();
        Map<Integer, List<EmpGoalVO>> auditMap = auditList.stream().collect(Collectors.groupingBy(EmpGoalVO::getBusinessGroup));
        auditMap.forEach((businessGroup, needAuditList) -> {

            // 查询所有合伙人的上级
            List<Integer> employeeInfoIds = needAuditList.stream().map(EmpGoalVO::getEmployeeInfoId).collect(Collectors.toList());
            List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds1(employeeInfoIds, businessGroup);
            Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));

            needAuditList.stream().forEach(audit -> {
                List<ParentVo> parentVos = parentMap.get(audit.getEmployeeInfoId());
                if (parentVos != null && parentVos.size() > 1) {
                    if (employeeGoalLogPOMap.get(audit.getId()) != null) {
                        // 当前审批人不在上级列表中
                        List<String> parentIdList = parentVoList.stream().map(ParentVo::getParentEmployeeId).collect(Collectors.toList());
                        if (!parentIdList.contains(employeeGoalLogPOMap.get(audit.getId()).getUpdatedBy())) {
                            EmployeeGoalLogPO employeeGoalLogPO = employeeGoalLogPOMap.get(audit.getId());
                            employeeGoalLogPO.setUpdatedBy(parentVos.get(1).getParentEmployeeId());
                            employeeGoalLogPO.setUpdatedName(parentVos.get(1).getParentEmployeeName());
                            employeeGoalLogPO.setUpdatedTime(nowDateTime);
                            employeeGoalLogPOUpdateList.add(employeeGoalLogPO);
                        }
                    }
                } else {
                    if (employeeGoalLogPOMap.get(audit.getId()) != null && employeeGoalLogPOMap.get(audit.getId()).getUpdatedBy() != null) {
                        EmployeeGoalLogPO employeeGoalLogPO = employeeGoalLogPOMap.get(audit.getId());
                        employeeGoalLogPO.setUpdatedBy(null);
                        employeeGoalLogPO.setUpdatedName(null);
                        employeeGoalLogPO.setUpdatedTime(nowDateTime);
                        employeeGoalLogPOUpdateList.add(employeeGoalLogPO);
                    }
                }
            });
        });

        if (!CollectionUtils.isEmpty(employeeGoalLogPOUpdateList)) {
            employeeGoalLogServiceImpl.updateBatchById(employeeGoalLogPOUpdateList);
        }
        return ReturnT.SUCCESS;
    }

    private Map<Integer, Integer> getDeadline() {
        List<QuarterOrganizationGoalExcelPO> list = quarterOrganizationGoalExcelMapper.selectList(new LambdaQueryWrapper<QuarterOrganizationGoalExcelPO>()
                .eq(QuarterOrganizationGoalExcelPO::getYear, LocalDate.now().getYear())
                .eq(QuarterOrganizationGoalExcelPO::getQuarter, LocalDate.now().get(IsoFields.QUARTER_OF_YEAR))
                .eq(QuarterOrganizationGoalExcelPO::getState, 1)
                .eq(QuarterOrganizationGoalExcelPO::getIsDelete, 0)
        );
        if (CollectionUtils.isEmpty(list)) {
            throw new ApplicationException("请先设置每月目标截止日期");
        }
        return list.stream().collect(Collectors.toMap(QuarterOrganizationGoalExcelPO::getBusinessGroupId, QuarterOrganizationGoalExcelPO::getDay));
    }

    /**
     * 主推品推送旺铺 每隔五分钟查一次
     *
     * @param param null
     * @return
     */
    @XxlJob("mainProductGoalPushTask")
    public ReturnT<String> mainProductGoalPushTask(String param) {
        log.info("mainProductGoalPushTask..param:{}", param);

        List<EmployeeGoalDetailPo> detailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>()
                .eq(EmployeeGoalDetailPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1)
                .eq(EmployeeGoalDetailPo::getPushStatus, 0)
                .eq(EmployeeGoalDetailPo::getIsDelete, 0));
        if (CollectionUtils.isEmpty(detailPoList)) {
            log.info("mainProductGoalPushTask 无待推送的记录.");
            return ReturnT.SUCCESS;
        }

        // 分批处理，每批100条
        List<List<EmployeeGoalDetailPo>> batchList = Lists.partition(detailPoList, 100);
        log.info("mainProductGoalPushTask 总共{}条记录，分{}批处理", detailPoList.size(), batchList.size());
        
        List<EmployeeGoalDetailPo> successPushList = new ArrayList<>();
        
        for (int i = 0; i < batchList.size(); i++) {
            List<EmployeeGoalDetailPo> batch = batchList.get(i);
            log.info("mainProductGoalPushTask 处理第{}批，共{}条记录", i + 1, batch.size());
            
            try {
                List<MainProductGoalPushWp> list = new ArrayList<>();
                for (EmployeeGoalDetailPo detailPo : batch) {
                    MainProductGoalPushWp pushWp = new MainProductGoalPushWp();
                    pushWp.setProductId(String.valueOf(detailPo.getMainId()));
                    SfaBusinessGroupEntity entity = sfaBusinessGroupMapper.selectById(detailPo.getBusinessGroupId());
                    if (entity != null) {
                        pushWp.setProductGroup(entity.getBusinessGroupCode());
                    }
                    pushWp.setGoalValue(detailPo.getSaleGoal());
                    pushWp.setMemberKey(detailPo.getMemberKey());
                    // 目标类型: 1-月份、2-季度
                    Integer goalType = detailPo.getRange() == 0 ? 1 : 2;
                    pushWp.setGoalType(goalType);
                    if (goalType.equals(2)) {
                        String des = TargetSetEnum.TargetQuarter.getDesByCode(String.valueOf(detailPo.getQuarter()));
                        pushWp.setGoalPeriod(detailPo.getYear() + des);
                    } else {
                        // 月度-具体月(YYYY-MM)或者具体季度名称(2025第二季度)
                        String yearMonth = "";
                        if (Objects.nonNull(detailPo.getEffectiveDate())) {
                            yearMonth = detailPo.getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        }
                        pushWp.setGoalPeriod(yearMonth);
                    }
                    list.add(pushWp);
                }
                
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                
                MainProductGoalPushWpRequest request = new MainProductGoalPushWpRequest();
                request.setMainProductGoalList(list);
                
                String reqStr = JSON.toJSONString(request);
                log.info("mainProductGoalPushTask 第{}批推送请求 req:{}", i + 1, reqStr);
                
                if (StringUtils.isEmpty(pushWpMainProduct)) {
                    throw new ApplicationException("推送地址未配置");
                }
                
                String resStr = HttpUtil.postJsonData(pushWpMainProduct, reqStr);
                log.info("mainProductGoalPushTask 第{}批推送响应 res:{}", i + 1, resStr);
                
                // 获取返回报文中code的值，如果==0，则推送成功-更新推送状态：
                JSONObject jsonObject = JSON.parseObject(resStr);
                Integer code = Integer.parseInt(jsonObject.get("code").toString());
                if (code.equals(0)) {
                    successPushList.addAll(batch);
                    log.info("mainProductGoalPushTask 第{}批推送成功", i + 1);
                } else {
                    log.warn("mainProductGoalPushTask 第{}批推送失败，响应码：{}", i + 1, code);
                }
                
            } catch (Exception e) {
                log.error("mainProductGoalPushTask 第{}批推送异常:", i + 1, e);
                // 继续处理下一批，不中断整个任务
            }
        }
        
        // 批量更新推送成功的记录
        if (!CollectionUtils.isEmpty(successPushList)) {
            for (EmployeeGoalDetailPo detailPo : successPushList) {
                detailPo.setPushStatus(1);
                detailPo.setUpdatedTime(LocalDateTime.now());
            }
            List<List<EmployeeGoalDetailPo>> updateList = Lists.partition(successPushList, 1000);
            for (List<EmployeeGoalDetailPo> employeeGoalDetailPos : updateList) {
                employeeGoalDetailServiceImpl.updateBatchById(employeeGoalDetailPos);
            }
            log.info("mainProductGoalPushTask 成功推送并更新{}条记录", successPushList.size());
        }
        
        return ReturnT.SUCCESS;
    }


    /**
     * 自动审核
     *
     * @param param
     * @return
     */
    @XxlJob("mainProductPartnerGoalAuditTask")
    public ReturnT<String> mainProductPartnerGoalAuditTask(String param) {

        log.info("mainProductPartnerGoalAuditTask..param:{}", param);

        List<EmployeeGoalDetailPo> detailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>()
                .eq(EmployeeGoalDetailPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalDetailPo::getIsDelete, 0));
        if (CollectionUtils.isEmpty(detailPoList)) {
            log.info("mainProductPartnerGoalAuditTask 无待审核的记录.");
            return ReturnT.SUCCESS;
        }

        Map<Integer, EmployeeGoalDetailPo> employeeGoalPoMap = detailPoList.stream().collect(Collectors.toMap(EmployeeGoalDetailPo::getId, v -> v));

        List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>()
                .in(EmployeeGoalLogPO::getGoalId, detailPoList.stream().map(EmployeeGoalDetailPo::getId).collect(Collectors.toList()))
                .eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalLogPO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(employeeGoalLogPOList) || employeeGoalLogPOList.size() != detailPoList.size()) {
            throw new ApplicationException("待审核履历不存在");
        }
        Map<Integer, EmployeeGoalLogPO> employeeGoalLogPOMap = employeeGoalLogPOList.stream().collect(Collectors.toMap(EmployeeGoalLogPO::getGoalId, v -> v));

        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();
        Integer days = Integer.valueOf(settingsMapper.getSfaSettingsByCode("partner_goal_set_deadline_days"));

        List<EmployeeGoalDetailPo> employeeGoalPoUpdateList = new ArrayList<>();
        List<EmployeeGoalLogPO> employeeGoalLogPOUpdateList = new ArrayList<>();
        Map<Integer, Integer> deadlineMap = getDeadline();
        detailPoList.stream().forEach(audit -> {
            Integer deadlineDay = deadlineMap.get(audit.getBusinessGroupId());
            if (deadlineDay != null) {
                LocalDate deadline = LocalDate.now().withDayOfMonth(deadlineDay);
                if (audit.getOnJobTime() != null && audit.getOnJobTime().plusDays(days).isAfter(deadline)) {
                    deadline = audit.getOnJobTime().plusDays(days);
                }
                if (nowDate.isAfter(deadline)) {
                    EmployeeGoalDetailPo employeeGoalPo = employeeGoalPoMap.get(audit.getId());
                    employeeGoalPo.setGoalStatus(DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1);
                    employeeGoalPo.setUpdatedTime(nowDateTime);
                    employeeGoalPoUpdateList.add(employeeGoalPo);

                    EmployeeGoalLogPO employeeGoalLogPO = employeeGoalLogPOMap.get(audit.getId());
                    employeeGoalLogPO.setGoalStatus(DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1);
                    employeeGoalLogPO.setReason("超时未审核，系统自动通过");
                    employeeGoalLogPO.setUpdatedTime(nowDateTime);
                    employeeGoalLogPOUpdateList.add(employeeGoalLogPO);
                }
            }
        });
        if (!CollectionUtils.isEmpty(employeeGoalPoUpdateList)) {
            employeeGoalDetailServiceImpl.updateBatchById(employeeGoalPoUpdateList);
            employeeGoalLogServiceImpl.updateBatchById(employeeGoalLogPOUpdateList);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 只是更新日志 的审核人
     * (该任务主要为处理部分岗位移动后，日志中审核人数据不正确，需要重新更正)
     *
     * @param param
     * @return
     */
    @XxlJob("mainProductPartnerGoalAutoTask")
    public ReturnT<String> mainProductPartnerGoalAutoTask(String param) {

        log.info("mainProductPartnerGoalAutoTask..param:{}", param);
        List<EmployeeGoalDetailPo> detailPoList = employeeGoalDetailMapper.selectList(new LambdaQueryWrapper<EmployeeGoalDetailPo>()
                .eq(EmployeeGoalDetailPo::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalDetailPo::getIsDelete, 0));
        if (CollectionUtils.isEmpty(detailPoList)) {
            log.info("mainProductPartnerGoalAutoTask 无待审核的记录.");
            return ReturnT.SUCCESS;
        }
        List<Integer> ids = detailPoList.stream().map(EmployeeGoalDetailPo::getId).collect(Collectors.toList());
        List<EmployeeGoalLogPO> employeeGoalLogPOList = employeeGoalLogMapper.selectList(new LambdaQueryWrapper<EmployeeGoalLogPO>()
                .in(EmployeeGoalLogPO::getGoalId, ids)
                .eq(EmployeeGoalLogPO::getGoalStatus, DictCodeConstants.CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0)
                .eq(EmployeeGoalLogPO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(employeeGoalLogPOList) || employeeGoalLogPOList.size() != detailPoList.size()) {
            throw new ApplicationException("mainProductPartnerGoalAutoTask待审核履历不存在");
        }
        Map<Integer, EmployeeGoalLogPO> employeeGoalLogPOMap = employeeGoalLogPOList.stream().collect(Collectors.toMap(EmployeeGoalLogPO::getGoalId, v -> v));

        List<EmployeeGoalLogPO> employeeGoalLogPOUpdateList = new ArrayList<>();
        LocalDateTime nowDateTime = LocalDateTime.now();
        Map<Integer, List<EmployeeGoalDetailPo>> auditMap = detailPoList.stream().collect(Collectors.groupingBy(EmployeeGoalDetailPo::getBusinessGroupId));
        auditMap.forEach((businessGroup, needAuditList) -> {

            // 查询所有合伙人的上级
            List<Integer> employeeInfoIds = needAuditList.stream().map(EmployeeGoalDetailPo::getEmployeeInfoId).collect(Collectors.toList());
            List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds1(employeeInfoIds, businessGroup);
            Map<Integer, List<ParentVo>> parentMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getEmployeeInfoId));

            needAuditList.stream().forEach(audit -> {
                List<ParentVo> parentVos = parentMap.get(audit.getEmployeeInfoId());
                if (parentVos != null && parentVos.size() > 1) {
                    if (employeeGoalLogPOMap.get(audit.getId()) != null) {
                        // 当前审批人不在上级列表中
                        List<String> parentIdList = parentVoList.stream().map(ParentVo::getParentEmployeeId).collect(Collectors.toList());
                        if (!parentIdList.contains(employeeGoalLogPOMap.get(audit.getId()).getUpdatedBy())) {
                            EmployeeGoalLogPO employeeGoalLogPO = employeeGoalLogPOMap.get(audit.getId());
                            employeeGoalLogPO.setUpdatedBy(parentVos.get(1).getParentEmployeeId());
                            employeeGoalLogPO.setUpdatedName(parentVos.get(1).getParentEmployeeName());
                            employeeGoalLogPO.setUpdatedTime(nowDateTime);
                            employeeGoalLogPOUpdateList.add(employeeGoalLogPO);
                        }
                    }
                } else {
                    if (employeeGoalLogPOMap.get(audit.getId()) != null && employeeGoalLogPOMap.get(audit.getId()).getUpdatedBy() != null) {
                        EmployeeGoalLogPO employeeGoalLogPO = employeeGoalLogPOMap.get(audit.getId());
                        employeeGoalLogPO.setUpdatedBy(null);
                        employeeGoalLogPO.setUpdatedName(null);
                        employeeGoalLogPO.setUpdatedTime(nowDateTime);
                        employeeGoalLogPOUpdateList.add(employeeGoalLogPO);
                    }
                }
            });
        });

        if (!CollectionUtils.isEmpty(employeeGoalLogPOUpdateList)) {
            employeeGoalLogServiceImpl.updateBatchById(employeeGoalLogPOUpdateList);
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 合计目标设置超时自动通知直接上级
     *
     * @param param
     * @return
     */
    @XxlJob("setGoalTimeOutNotice")
    public ReturnT<String> setGoalTimeOutNotice(String param) {
        log.info("setGoalTimeOutNotice job..param:{}", param);
        // 获取当前时间年月
        LocalDateTime localDateTime = LocalDateTime.now();
        int month = localDateTime.getMonthValue();
        String yearMonth = localDateTime.getYear() + "-" + month;
        if (month < 10) {
            yearMonth = localDateTime.getYear() + "-0" + month;
        }

        List<EmpGoalVO> needSetGoalList = realtimeDataServiceImpl.queryNeedSetGoalList(yearMonth);
        if (CollectionUtils.isEmpty(needSetGoalList)) {
            log.info("没有需要通知设置目标的数据.");
            return ReturnT.SUCCESS;
        }
        Integer days = Integer.valueOf(settingsMapper.getSfaSettingsByCode("partner_goal_set_deadline_days"));
        Map<Integer, Integer> deadlineMap = getDeadline();
        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = nowDateTime.toLocalDate();

        for (EmpGoalVO vo : needSetGoalList) {
            if (MapUtils.isEmpty(deadlineMap)){
                log.info("没有查询到截止日期配置-不处理.");
                continue;
            }
            // 判断截止日期
            Integer deadlineDay = deadlineMap.get(vo.getBusinessGroup());
            if (deadlineDay == null) {
                log.info("没有查询到对应业务组截止日期配置-不处理.");
                continue;
            }
            LocalDate deadline = LocalDate.now().withDayOfMonth(deadlineDay);
            if (vo.getOnJobTime().plusDays(days).isAfter(deadline)) {
                deadline = vo.getOnJobTime().plusDays(days);
            }
            if (!nowDate.isAfter(deadline)) {
                log.info("目标设置没有超时-不处理");
                continue;
            }
            NotifyPushRequest req = new NotifyPushRequest();
            req.setEmpId(vo.getEmployeeId());
            req.setContent("合伙人目标待设置");
            req.setTitle("【合伙人目标待设置】");
            req.setType(2);
            req.setCode("");
            notifyApplication.push(req);
        }
        return ReturnT.SUCCESS;
    }
}
