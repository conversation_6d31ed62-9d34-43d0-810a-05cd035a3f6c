package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.employee.vo.BusinessPersonVo;
import com.wantwant.sfa.backend.mapper.NotifyMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Component
@Slf4j
public class PayrollAndPerformanceNotifyTask {

    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private NotifyMapper notifyMapper;

    @XxlJob("PayrollAndPerformanceNotifyTask")
    @Transactional
    public ReturnT<String> PayrollAndPerformanceNotify(String param) {

        log.info("薪资绩效通知定时任务开始..param:{}", param);

        LocalDateTime nowDateTime = LocalDateTime.now();
        String lastMonth = LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));

        List<BusinessPersonVo> businessPersonVoList = sfaEmployeeInfoMapper.queryAllBusinessPerson();

        businessPersonVoList.forEach(businessPersonVo -> {
            String employeeId = businessPersonVo.getEmployeeId();
            String employeeInfoId = String.valueOf(businessPersonVo.getEmployeeInfoId());

            String code1 = MessageFormat.format("/salaryQuery?theYearMon={0}&employeeInfoId={1}&organizationId={2}", lastMonth, employeeInfoId, businessPersonVo.getOrganizationId());
            String title1 = MessageFormat.format("{0}「薪资账单」数据已生成，请查看！", lastMonth);
            notify(code1, title1, employeeId, nowDateTime);

            String code2 = MessageFormat.format("/performanceRating?theYearMon={0}&employeeInfoId={1}&organizationId={2}", lastMonth, employeeInfoId, businessPersonVo.getOrganizationId());
            String title2 = MessageFormat.format("{0}「绩效奖金」预估数据已生成，请查看！", lastMonth);
            notify(code2, title2, employeeId, nowDateTime);
        });

        return ReturnT.SUCCESS;
    }

    private void notify(String code, String title, String employeeId, LocalDateTime nowDateTime) {
        NotifyPO po = new NotifyPO();
        po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
        po.setCode(code);
        po.setTitle(title);
        po.setContent(title);
        po.setEmployeeId(employeeId);
        po.setCreateTime(nowDateTime);
        po.setCreateBy("-1");
        po.setUpdateTime(nowDateTime);
        po.setUpdateBy("-1");
        notifyMapper.insert(po);
    }

}
