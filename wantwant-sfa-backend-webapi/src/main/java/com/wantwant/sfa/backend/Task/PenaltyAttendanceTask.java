package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyAttendanceRegularEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyRegularEntity;
import com.wantwant.sfa.backend.activityQuota.model.AbnormalAttendanceModel;
import com.wantwant.sfa.backend.activityQuota.service.IAbnormalAttendanceService;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyAttendanceRegularDetailVo;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyAttendanceRegularMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyRegularMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/20/下午2:12
 */
@Component
@Slf4j
public class PenaltyAttendanceTask {

    @Autowired
    private PenaltyRegularMapper penaltyRegularMapper;
    @Autowired
    private PenaltyAttendanceRegularMapper penaltyAttendanceRegularMapper;
    @Autowired
    private IAbnormalAttendanceService abnormalAttendanceService;
    @Autowired
    private IPenaltyService penaltyService;

    @XxlJob("penaltyAttendanceTask")
    public ReturnT<String> execute(String param) {

        XxlJobLogger.log("【考勤扣款】start..");
        // 获取当月
        LocalDate month = getMonth(param);
        XxlJobLogger.log("【考勤扣款】执行日期:{}",month.toString());
        // 获取考勤扣款规则
        PenaltyRegularEntity entity = penaltyRegularMapper.selectById(1);
        if(entity.getStatus() != 1 || entity.getDeleteFlag() == 1){
            XxlJobLogger.log("【考勤扣款】规则未启用");
            return ReturnT.SUCCESS;
        }

        List<PenaltyAttendanceRegularEntity> penaltyAttendanceRegularEntities = penaltyAttendanceRegularMapper.selectList(new QueryWrapper<PenaltyAttendanceRegularEntity>()
                .eq("delete_flag", 0)
        );

        if(CollectionUtils.isEmpty(penaltyAttendanceRegularEntities)){
            XxlJobLogger.log("【考勤扣款】未配置相应规则");
            return ReturnT.SUCCESS;
        }

        List<AbnormalAttendanceModel> abnormalAttendanceModels = abnormalAttendanceService.selectAttendanceRateList(month.toString().substring(0, 7));
        if(CollectionUtils.isEmpty(abnormalAttendanceModels)){
            XxlJobLogger.log("【考勤扣款】考勤记录为空");
            return ReturnT.SUCCESS;
        }

        penaltyService.attendancePenalty(2,penaltyAttendanceRegularEntities,abnormalAttendanceModels);

        return ReturnT.SUCCESS;
    }



    private LocalDate getMonth(String param) {
        if(StringUtils.isBlank(param)){

            return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());

        }else{
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return  LocalDate.parse(param, format);
        }
    }
}
