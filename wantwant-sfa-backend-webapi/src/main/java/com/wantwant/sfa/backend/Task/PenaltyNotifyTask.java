package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyRegularEntity;
import com.wantwant.sfa.backend.arch.entity.DeptEmployeeRelationEntity;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyRegularMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.notify.model.PenaltyModel;
import com.wantwant.sfa.backend.notify.template.impl.PenaltyNotifyContent;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.NotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/09/下午5:53
 */
@Component
@Slf4j
public class PenaltyNotifyTask {
    @Autowired
    private PenaltyMapper penaltyMapper;
    @Autowired
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Autowired
    private PenaltyRegularMapper penaltyRegularMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private NotifyContentMapper notifyContentMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private IAuditService auditService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private DeptEmployeeRelationMapper deptEmployeeRelationMapper;

    @XxlJob("penaltyNotify")
    @Transactional
    public ReturnT<String> penaltyNotify(String param){

        log.info("【penalty notify process】start");

        String yearMonth = getYearMonth(param);

        log.info("【penalty notify process】yearMonth :{}",yearMonth);

        // 获取当前时间的上周一
        LocalDate date =  LocalDate.parse(yearMonth);
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        LocalDate lastMonday = date.minusDays(dayOfWeek.getValue() - 1).minusWeeks(1L);
        // 获取本周一
        LocalDate monday = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        List<PenaltyEntity> penaltyEntities = penaltyMapper.selectList(new LambdaQueryWrapper<PenaltyEntity>().ge(PenaltyEntity::getCreateTime, lastMonday).lt(PenaltyEntity::getCreateTime, monday).eq(PenaltyEntity::getDeleteFlag,0).in(PenaltyEntity::getStatus,1,3));
        if(CollectionUtils.isEmpty(penaltyEntities)){
            log.info("【penalty notify】no date");
            return ReturnT.SUCCESS;
        }


        String title = lastMonday.toString() + "~" + monday.minusDays(1L).toString()  + " 扣罚清单";


        List<DeptEmployeeRelationEntity> deptEmployeeRelationEntities = deptEmployeeRelationMapper.selectList(new LambdaQueryWrapper<DeptEmployeeRelationEntity>()
                .in(DeptEmployeeRelationEntity::getDeptId, 63)
                .eq(DeptEmployeeRelationEntity::getDeleteFlag, 0)
        );

        for (PenaltyEntity e : penaltyEntities) {
            // 实际扣罚组织
            String organizationId = e.getActualPenaltyOrganizationId();

            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, organizationId));
            // 内容信息
            PenaltyModel model = new PenaltyModel();
            model.setPenaltyDate(LocalDateTimeUtils.formatTime(e.getCreateTime(),LocalDateTimeUtils.yyyy_MM_dd));
            PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectById(e.getPenaltyRegularId());
            model.setPenaltyItemName(regularEntity.getRegularName());
            model.setQuota(e.getPenaltyAmount().toString());
            model.setType("组织");
            model.setRemark(e.getRemark());
            model.setAreaName(viewEntity.getOrgName3());
            model.setVareaName(viewEntity.getVirtualAreaName());
            model.setProvinceName(viewEntity.getProvinceName());
            model.setCompanyName(viewEntity.getOrgName2());
            model.setDepartmentName(viewEntity.getDepartmentName());

            SfaPositionRelationEntity positionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getOrganizationCode, organizationId).eq(SfaPositionRelationEntity::getDeleteFlag, 0).orderByDesc(SfaPositionRelationEntity::getId).last("limit 1"));
            if(Objects.isNull(positionRelationEntity)){
                continue;
            }
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(positionRelationEntity.getEmployeeInfoId());

            model.setMobile(sfaEmployeeInfoModel.getMobile());
            model.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId, positionRelationEntity.getPositionId()));
            String employeeId = ceoBusinessOrganizationPositionRelation.getEmployeeId();
            if(StringUtils.isNotBlank(employeeId)){
                // 保存消息记录
                NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.PENALTY.getType(), title,employeeId, title);
                model.setTemplateId(notifyPO.getTemplateId());

                PenaltyNotifyContent penaltyNotifyContent = new PenaltyNotifyContent();
                NotifyContentEntity notifyContentEntity = penaltyNotifyContent.buildNotifyContent(model);
                notifyContentMapper.insert(notifyContentEntity);
            }

            String organizationParentId = organizationMapper.getOrganizationParentId(organizationId);
            Integer businessGroupById = organizationMapper.getBusinessGroupById(organizationParentId);
            // 上级信息
            SelectAuditDto selectAuditDto = new SelectAuditDto();
            selectAuditDto.setBusinessGroup(businessGroupById);
            selectAuditDto.setChannel(3);
            selectAuditDto.setCurrentOrganizationId(organizationParentId);
            String work_report_standby = configMapper.getValueByCode("work_report_standby");
            selectAuditDto.setStandbyEmployeeId(work_report_standby);

            CeoBusinessOrganizationPositionRelation parent = auditService.chooseAuditPerson(selectAuditDto);
            if(Objects.nonNull(parent) && org.apache.commons.lang.StringUtils.isNotBlank(parent.getEmployeeId())){
                // 保存消息记录
                NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.PENALTY.getType(), title,parent.getEmployeeId(), title);
                model.setTemplateId(notifyPO.getTemplateId());

                PenaltyNotifyContent penaltyNotifyContent = new PenaltyNotifyContent();
                NotifyContentEntity notifyContentEntity = penaltyNotifyContent.buildNotifyContent(model);
                notifyContentMapper.insert(notifyContentEntity);
            }

            String regularName = regularEntity.getRegularName();
            if(regularName.contains("未参训")  || regularName.contains("未考试") || regularName.contains("考试不及格") || regularName.contains("考试扣罚")){
                // 给培训发送消息
                if(!CollectionUtils.isEmpty(deptEmployeeRelationEntities)){
                    deptEmployeeRelationEntities.forEach(d -> {
                        // 保存消息记录
                        NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.PENALTY.getType(), title,d.getEmployeeId(), title);
                        model.setTemplateId(notifyPO.getTemplateId());

                        PenaltyNotifyContent penaltyNotifyContent = new PenaltyNotifyContent();
                        NotifyContentEntity notifyContentEntity = penaltyNotifyContent.buildNotifyContent(model);
                        notifyContentMapper.insert(notifyContentEntity);
                    });
                }
            }
        }


        return ReturnT.SUCCESS;
    }

    private String getYearMonth(String param) {
        if(StringUtils.isNotBlank(param)){
            return param;
        }

        return LocalDate.now().toString();
    }
}
