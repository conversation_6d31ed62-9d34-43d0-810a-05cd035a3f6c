package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.activityQuota.dto.PersonPenaltyDTO;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyBatchLogEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyRegularEntity;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyBigTableService;
import com.wantwant.sfa.backend.activityQuota.service.impl.PenaltyService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyBatchLogMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyRegularMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/05/下午2:37
 */
@Component
@Slf4j
public class PenaltyTask {
    @Resource
    private IPenaltyBigTableService penaltyBigTableService;
    @Resource
    private PenaltyService penaltyService;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private PenaltyRegularMapper penaltyRegularMapper;
    @Resource
    private PenaltyBatchLogMapper penaltyBatchLogMapper;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;

    private String visitPenalty = "拜访未达标扣罚";

    private String reportWrite = "报告未提交扣罚";

    private String reportRead = "报告未被阅读扣罚";

    private String completePenalty = "通关打卡异常扣罚";

    private String meetingJoinPenalty = "会议未参加扣罚";

    private String meetingCreatePenalty = "会议未召开扣罚";

    private String inventoryPenalty = "盘点不达标扣罚";


    // 每日扣罚
    @XxlJob("dailyPenaltyProcess")
    @Transactional
    public ReturnT<String> dailyPenaltyProcess(String param) {

        String theYearMonth = getDate(param);
        log.info("【daily penalty process】date:{}", theYearMonth);

        List<PersonPenaltyDTO> personPenaltyDTOS = penaltyBigTableService.selectDailyPenalty(theYearMonth);
        if (CollectionUtils.isEmpty(personPenaltyDTOS)) {
            return ReturnT.SUCCESS;
        }


        personPenaltyDTOS.stream().forEach(e -> {

            PenaltyBatchLogEntity penaltyBatchLogEntity = penaltyBatchLogMapper.selectOne(new LambdaQueryWrapper<PenaltyBatchLogEntity>().eq(PenaltyBatchLogEntity::getTheYearMonth, theYearMonth)
                    .eq(PenaltyBatchLogEntity::getMemberKey, e.getMemberKey())
                    .eq(PenaltyBatchLogEntity::getPartTime, e.getPartTime())
                    .eq(PenaltyBatchLogEntity::getType,1)
                    .eq(PenaltyBatchLogEntity::getOrganizationCode, e.getOrganizationCode())
                    .eq(PenaltyBatchLogEntity::getDeleteFlag, 0));
            if (Objects.isNull(penaltyBatchLogEntity)) {
                penaltyBatchLogEntity = new PenaltyBatchLogEntity();
                BeanUtils.copyProperties(e, penaltyBatchLogEntity);
                penaltyBatchLogEntity.setType(1);
            }
            penaltyBatchLogEntity.setTheYearMonth(theYearMonth);


            String organizationCode = e.getOrganizationCode();
            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, organizationCode));


            if (Objects.nonNull(viewEntity)) {
                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
                String organizationType = organizationMapper.getOrganizationType(organizationCode);
                if(organizationType.equals("area")){
                    ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                            .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationCode));
                }else{
                    String virtualAreaId = viewEntity.getVirtualAreaId();
                    ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                            .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, virtualAreaId));
                }

                Integer businessGroupById = ceoBusinessOrganizationPositionRelation.getBusinessGroup();

                List<Long> penaltyResults = new ArrayList<>();
                // 拜访/日报/通过只看主岗
                if (StringUtils.isNotBlank(e.getPartTime()) && e.getPartTime().equals("主岗")) {

                    BigDecimal visitPenaltyAmount = e.getVisitPenaltyAmount();
                    // 拜访扣罚
                    if (visitPenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getVisitPenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, visitPenalty)
                                .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                                .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                        if (Objects.nonNull(regularEntity)) {
                            penaltyBatchLogEntity.setVisitPenaltyAmount(visitPenaltyAmount);
                            penaltyBatchLogEntity.setProcessPenalty(1);
                            List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, visitPenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                            if (!CollectionUtils.isEmpty(penaltyIds)) {
                                penaltyResults.addAll(penaltyIds);
                            }
                        }
                    }
                    // 日报填写扣罚
                    BigDecimal reportWpenatlyAmount = e.getReportWpenatlyAmount();
                    if (reportWpenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getReportWpenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, reportWrite)
                                .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                                .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                        if (Objects.nonNull(regularEntity)) {
                            penaltyBatchLogEntity.setProcessPenalty(1);
                            penaltyBatchLogEntity.setReportWpenatlyAmount(reportWpenatlyAmount);
                            List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, reportWpenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                            if (!CollectionUtils.isEmpty(penaltyIds)) {
                                penaltyResults.addAll(penaltyIds);
                            }
                        }
                    }

                    // 日报阅读
                    BigDecimal reportRpenatlyAmount = e.getReportRpenatlyAmount();
                    if (reportRpenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getReportRpenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, reportRead)
                                .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                                .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                        if (Objects.nonNull(regularEntity)) {
                            penaltyBatchLogEntity.setProcessPenalty(1);
                            penaltyBatchLogEntity.setReportRpenatlyAmount(reportRpenatlyAmount);
                            List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, reportRpenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                            if (!CollectionUtils.isEmpty(penaltyIds)) {
                                penaltyResults.addAll(penaltyIds);
                            }
                        }
                    }

                    // 通关异常扣罚
                    BigDecimal completePenaltyAmount = e.getCompletePenaltyAmount();
                    if (completePenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getCompletePenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, completePenalty)
                                .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                                .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                        if (Objects.nonNull(regularEntity)) {
                            penaltyBatchLogEntity.setProcessPenalty(1);
                            penaltyBatchLogEntity.setCompletePenaltyAmount(completePenaltyAmount);
                            List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, completePenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                            if (!CollectionUtils.isEmpty(penaltyIds)) {
                                penaltyResults.addAll(penaltyIds);
                            }
                        }
                    }
                }

                // 会议未参加扣罚
                BigDecimal meetApenatlyAmount = e.getMeetApenatlyAmount();
                if (meetApenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getMeetApenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, meetingJoinPenalty)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity)) {
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setMeetApenatlyAmount(meetApenatlyAmount);
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, meetApenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

                // 会议为召开扣罚
                BigDecimal meetHpenaltyAmount = e.getMeetHpenaltyAmount();
                if (meetHpenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getMeetHpenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, meetingCreatePenalty)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity)) {
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setMeetHpenaltyAmount(meetHpenaltyAmount);
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, meetHpenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(penaltyResults)) {
                    List<String> collect = penaltyResults.stream().map(String::valueOf).collect(Collectors.toList());
                    penaltyBatchLogEntity.setPenaltyIds(String.join(",", collect));
                }


                if (Objects.nonNull(penaltyBatchLogEntity.getId())) {
                    penaltyBatchLogMapper.updateById(penaltyBatchLogEntity);
                } else {
                    penaltyBatchLogMapper.insert(penaltyBatchLogEntity);
                }
            }


        });


        return ReturnT.SUCCESS;
    }

    // 每周扣罚，执行时间为每周一
    @XxlJob("weeklyPenaltyProcess")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> weeklyPenaltyProcess(String param) {
        String theYearMonth = getDate(param);

        String date = theYearMonth.substring(0, 7);

        LocalDate today = LocalDate.parse(theYearMonth);

        // 获取当月的第一天
        LocalDate firstDayOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());

        // 计算当月第一天的星期几（默认星期天为一周的第一天）
        DayOfWeek firstDayOfWeek = firstDayOfMonth.getDayOfWeek();

        // 计算从当月第一天到今天的天数差
        long daysDifference = today.toEpochDay() - firstDayOfMonth.toEpochDay();

        // 计算今天是当月的第几周（考虑第一周可能不完整）
        int weekOfMonth = (int) Math.ceil((double) (daysDifference + (firstDayOfWeek.getValue() - DayOfWeek.MONDAY.getValue() + 7) % 7 + 1) / 7);

        log.info("【weekly penalty process】date:{},week:{}", theYearMonth, weekOfMonth);


        List<PersonPenaltyDTO> personPenaltyDTOS = penaltyBigTableService.selectWeeklyPenalty(date, weekOfMonth);
        if (CollectionUtils.isEmpty(personPenaltyDTOS)) {
            return ReturnT.SUCCESS;
        }

        String week = date + "-" + weekOfMonth;

        for (PersonPenaltyDTO e : personPenaltyDTOS) {

            PenaltyBatchLogEntity penaltyBatchLogEntity = penaltyBatchLogMapper.selectOne(new LambdaQueryWrapper<PenaltyBatchLogEntity>().eq(PenaltyBatchLogEntity::getTheYearMonth, week)
                    .eq(PenaltyBatchLogEntity::getMemberKey, e.getMemberKey())
                    .eq(PenaltyBatchLogEntity::getPartTime, e.getPartTime())
                    .eq(PenaltyBatchLogEntity::getType,2)
                    .eq(PenaltyBatchLogEntity::getOrganizationCode, e.getOrganizationCode())
                    .eq(PenaltyBatchLogEntity::getDeleteFlag, 0));
            if (Objects.isNull(penaltyBatchLogEntity)) {
                penaltyBatchLogEntity = new PenaltyBatchLogEntity();
                BeanUtils.copyProperties(e, penaltyBatchLogEntity);
                penaltyBatchLogEntity.setType(2);
            }
            penaltyBatchLogEntity.setTheYearMonth(week);


            List<Long> penaltyResults = new ArrayList<>();

            String organizationCode = e.getOrganizationCode();
            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, organizationCode));
            if (Objects.isNull(viewEntity)) {
                continue;
            }

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
            String organizationType = organizationMapper.getOrganizationType(organizationCode);
            if(organizationType.equals("area")){
                ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationCode));
            }else{
                String virtualAreaId = viewEntity.getVirtualAreaId();
                ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, virtualAreaId));
            }

            Integer businessGroupById = organizationMapper.getBusinessGroupById(organizationCode);


            // 拜访/日报/通过只看主岗
            String partTime = e.getPartTime();
            if (StringUtils.isNotBlank(partTime) && partTime.equals("主岗")) {

                BigDecimal visitPenaltyAmount = e.getVisitPenaltyAmount();
                // 拜访扣罚
                if (visitPenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getVisitPenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, visitPenalty)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity)) {
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setVisitPenaltyAmount(visitPenaltyAmount);
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, visitPenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }
                // 周报填写扣罚
                BigDecimal reportWpenatlyAmount = e.getReportWpenatlyAmount();
                if (reportWpenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getReportWpenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, reportWrite)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity)) {
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setReportWpenatlyAmount(reportWpenatlyAmount);
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, reportWpenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

                // 周报阅读
                BigDecimal reportRpenatlyAmount = e.getReportRpenatlyAmount();
                if (reportRpenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getReportRpenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, reportRead)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity)) {
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setReportRpenatlyAmount(reportRpenatlyAmount);
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, reportRpenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

            }

            // 会议未参加扣罚
            BigDecimal meetApenatlyAmount = e.getMeetApenatlyAmount();
            if (meetApenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getMeetApenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, meetingJoinPenalty)
                        .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                        .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                if (Objects.nonNull(regularEntity)) {
                    penaltyBatchLogEntity.setProcessPenalty(1);
                    penaltyBatchLogEntity.setMeetApenatlyAmount(meetApenatlyAmount);
                    List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, meetApenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                    if (!CollectionUtils.isEmpty(penaltyIds)) {
                        penaltyResults.addAll(penaltyIds);
                    }
                }
            }

            // 会议为召开扣罚
            BigDecimal meetHpenaltyAmount = e.getMeetHpenaltyAmount();
            if (meetHpenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getMeetHpenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, meetingCreatePenalty)
                        .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                        .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                if (Objects.nonNull(regularEntity)) {
                    penaltyBatchLogEntity.setProcessPenalty(1);
                    penaltyBatchLogEntity.setMeetHpenaltyAmount(meetHpenaltyAmount);
                    List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, meetHpenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                    if (!CollectionUtils.isEmpty(penaltyIds)) {
                        penaltyResults.addAll(penaltyIds);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(penaltyResults)) {
                List<String> collect = penaltyResults.stream().map(String::valueOf).collect(Collectors.toList());
                penaltyBatchLogEntity.setPenaltyIds(String.join(",", collect));
            }

            if (Objects.nonNull(penaltyBatchLogEntity.getId())) {
                penaltyBatchLogMapper.updateById(penaltyBatchLogEntity);
            } else {
                penaltyBatchLogMapper.insert(penaltyBatchLogEntity);
            }

        }

        return ReturnT.SUCCESS;
    }

    // 每月扣罚,
    @XxlJob("monthPenaltyProcess")
    @Transactional
    public ReturnT<String> monthPenaltyProcess(String param) {
        String date = getDate(param).substring(0, 7);
        log.info("【month penalty process】date:{}", date);

        List<PersonPenaltyDTO> personPenaltyDTOS = penaltyBigTableService.selectMonthPenalty(date);
        if (CollectionUtils.isEmpty(personPenaltyDTOS)) {
            return ReturnT.SUCCESS;
        }


        for (PersonPenaltyDTO e : personPenaltyDTOS) {
            PenaltyBatchLogEntity penaltyBatchLogEntity = penaltyBatchLogMapper.selectOne(new LambdaQueryWrapper<PenaltyBatchLogEntity>().eq(PenaltyBatchLogEntity::getTheYearMonth, date)
                    .eq(PenaltyBatchLogEntity::getMemberKey, e.getMemberKey())
                    .eq(PenaltyBatchLogEntity::getPartTime, e.getPartTime())
                    .eq(PenaltyBatchLogEntity::getOrganizationCode, e.getOrganizationCode())
                    .eq(PenaltyBatchLogEntity::getType,3)
                    .eq(PenaltyBatchLogEntity::getDeleteFlag, 0));
            if (Objects.isNull(penaltyBatchLogEntity)) {
                penaltyBatchLogEntity = new PenaltyBatchLogEntity();
                BeanUtils.copyProperties(e, penaltyBatchLogEntity);
                penaltyBatchLogEntity.setType(3);
            }

            String managerRoleIds = "5,6,7,8,9";
            boolean isManager = false;
            if(StringUtils.isNotBlank(e.getPosRoleId()) && managerRoleIds.contains(e.getPosRoleId())){
                isManager = true;
            }

            penaltyBatchLogEntity.setTheYearMonth(date);

            String organizationCode = e.getOrganizationCode();
            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, organizationCode));
            if (Objects.isNull(viewEntity)) {
                continue;
            }

            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
            String organizationType = organizationMapper.getOrganizationType(organizationCode);
            if(organizationType.equals("area")){
                ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationCode));
            }else{
                String virtualAreaId = viewEntity.getVirtualAreaId();
                ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, virtualAreaId));
            }



            Integer businessGroupById = organizationMapper.getBusinessGroupById(organizationCode);

            List<Long> penaltyResults = new ArrayList<>();

            // 拜访/月报/盘点只看主岗
            if (e.getPartTime().equals("主岗")) {

                BigDecimal visitPenaltyAmount = e.getVisitPenaltyAmount();
                // 拜访扣罚
                if (visitPenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getVisitPenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, visitPenalty)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity) && isManager) {
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setVisitPenaltyAmount(visitPenaltyAmount);
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, visitPenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }
                // 月报填写扣罚
                BigDecimal reportWpenatlyAmount = e.getReportWpenatlyAmount();
                if (reportWpenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getReportWpenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, reportWrite)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity) && isManager) {
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, reportWpenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setReportWpenatlyAmount(reportWpenatlyAmount);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

                // 月报阅读
                BigDecimal reportRpenatlyAmount = e.getReportRpenatlyAmount();
                if (reportRpenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getReportRpenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, reportRead)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity) && isManager) {
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, reportRpenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setReportRpenatlyAmount(reportRpenatlyAmount);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

                // 盘点
                BigDecimal checkPantly = e.getCheckPantly();
                if (checkPantly.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getCheckPantly()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                    PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, inventoryPenalty)
                            .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                            .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                    if (Objects.nonNull(regularEntity)) {
                        List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, checkPantly, regularEntity.getId(), "系统自动扣罚", null);
                        penaltyBatchLogEntity.setProcessPenalty(1);
                        penaltyBatchLogEntity.setCheckPantly(checkPantly);
                        if (!CollectionUtils.isEmpty(penaltyIds)) {
                            penaltyResults.addAll(penaltyIds);
                        }
                    }
                }

            }

            // 会议未参加扣罚
            BigDecimal meetApenatlyAmount = e.getMeetApenatlyAmount();
            if (meetApenatlyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getMeetApenatlyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, meetingJoinPenalty)
                        .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                        .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                if (Objects.nonNull(regularEntity) && isManager) {
                    List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, meetApenatlyAmount, regularEntity.getId(), "系统自动扣罚", null);
                    penaltyBatchLogEntity.setProcessPenalty(1);
                    penaltyBatchLogEntity.setMeetApenatlyAmount(meetApenatlyAmount);
                    if (!CollectionUtils.isEmpty(penaltyIds)) {
                        penaltyResults.addAll(penaltyIds);
                    }
                }
            }

            // 会议为召开扣罚
            BigDecimal meetHpenaltyAmount = e.getMeetHpenaltyAmount();
            if (meetHpenaltyAmount.compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(penaltyBatchLogEntity.getId()) || Optional.ofNullable(penaltyBatchLogEntity.getMeetHpenaltyAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0)) {
                PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new LambdaQueryWrapper<PenaltyRegularEntity>().eq(PenaltyRegularEntity::getRegularName, meetingCreatePenalty)
                        .eq(PenaltyRegularEntity::getBusinessGroup, businessGroupById)
                        .eq(PenaltyRegularEntity::getDeleteFlag, 0).last("limit 1"));
                if (Objects.nonNull(regularEntity) && isManager) {
                    List<Long> penaltyIds = penaltyService.savePenaltyLogMapper(ceoBusinessOrganizationPositionRelation, meetHpenaltyAmount, regularEntity.getId(), "系统自动扣罚", null);
                    penaltyBatchLogEntity.setProcessPenalty(1);
                    penaltyBatchLogEntity.setMeetHpenaltyAmount(meetHpenaltyAmount);
                    if (!CollectionUtils.isEmpty(penaltyIds)) {
                        penaltyResults.addAll(penaltyIds);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(penaltyResults)) {
                List<String> collect = penaltyResults.stream().map(String::valueOf).collect(Collectors.toList());
                penaltyBatchLogEntity.setPenaltyIds(String.join(",", collect));
            }


            if (Objects.nonNull(penaltyBatchLogEntity.getId())) {
                penaltyBatchLogMapper.updateById(penaltyBatchLogEntity);
            } else {
                penaltyBatchLogMapper.insert(penaltyBatchLogEntity);
            }

        }

        return ReturnT.SUCCESS;
    }


    private String getDate(String param) {
        if (StringUtils.isNotBlank(param)) {
            return param;
        } else {
            return LocalDate.now().minusDays(1L).toString();
        }
    }
}
