package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.service.IRealtimeDataService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class PerformanceNoticeOrganizationTask {

    @Autowired
    private IRealtimeDataService realtimeDataService;

    @Autowired
    private NotifyService notifyService;

    /**
     * 0 0 1 5,6,7,8,9,10 1,4,7,10 ? 每季度第5,6,7,8,9,10天凌晨1点触发。
     *
     * 绩效奖金评分设置 大区，分公司 消息通知
     *
     * @param
     * @return: void
     * @author: Gu
     * @date: 3/15
     */
    @XxlJob("performanceNoticeOrganizationTask")
    public ReturnT<String> performanceNoticeOrganizationTask(String param){
        log.info("大区，分公司绩效奖金评分设置通知开始 查上个季度");
        String examineQuarter="";//得到的考核季度
        //查上个季度
        //得到月份
        Double mon = Double.valueOf(LocalDate.now().toString().substring(5, 7));
        //得到季度
        Integer quarter= Integer.valueOf((int) Math.ceil(mon / Double.valueOf("3")));
        //如果季度是1季度，算上个季度，要得到上一年的四季度
        //否则当前季度-1
        if(quarter==1){
            Integer year = Integer.valueOf(LocalDate.now().toString().substring(0, 4));
            examineQuarter=String.valueOf(year - 1)+"-04";
        }else{
            examineQuarter=LocalDate.now().toString().substring(0,5)+"0"+ String.valueOf(quarter-1);
        }

        long start = System.currentTimeMillis();
        /*-- 绩效评分找到没有评分的数据
        -- 根据这些数据找到要哪些人要评
        -- 根据这些人看如果有人离职，则找出它的上级人员工号*/
        List<String> completedCompany = realtimeDataService.getCompletedEmployeeOrganization(examineQuarter);
        List<NotifyPO> notifyPOS = new ArrayList<>();
        if(CommonUtil.ListUtils.isNotEmpty(completedCompany)){
            String finalExamineQuarter = examineQuarter;
            completedCompany.forEach(f ->{
                NotifyPO po = new NotifyPO();
                po.setTitle(finalExamineQuarter +"季度绩效奖金评分通知(截止日期：10号)");
                po.setType(1);
                po.setContent(finalExamineQuarter +"季度绩效奖金评分通知(截止日期：10号)");
                po.setCode("/performanceRating");
                po.setEmployeeId(f);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
        long end = System.currentTimeMillis();
        log.info("绩效奖金评分设置通知结束，耗时{}",end-start);

        return ReturnT.SUCCESS;
    }


}
