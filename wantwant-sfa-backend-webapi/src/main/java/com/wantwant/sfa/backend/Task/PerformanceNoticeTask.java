package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.service.IRealtimeDataService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Configuration
@Slf4j
public class PerformanceNoticeTask {


    @Autowired
    private IRealtimeDataService realtimeDataService;

    @Autowired
    private NotifyService notifyService;

    /**
     * 绩效奖金评分设置 区域经理
     * 0 0 1 5,6,7,8,9,10 * ?
     *
     * @param
     * @return: void
     * @author: Gu
     * @date: 3/15
     */
//    @Scheduled(cron = "${task.PerformanceNoticeTask.cron}")
    public void configureTasks() {
        log.info("区域经理绩效奖金评分设置通知开始");
        int monthValue = LocalDate.now().getMonthValue()-1;
        long start = System.currentTimeMillis();
        /*-- 绩效评分找到没有评分的数据
        -- 根据这些数据找到要哪些人要评
        -- 根据这些人看如果有人离职，则找出它的上级人员工号*/
        List<String> completedCompany = realtimeDataService.getCompletedEmployee();
        List<NotifyPO> notifyPOS = new ArrayList<>();
        if(CommonUtil.ListUtils.isNotEmpty(completedCompany)){
            completedCompany.forEach(f ->{
                NotifyPO po = new NotifyPO();
                po.setTitle(monthValue+"月绩效奖金评分通知(截止日期：10号)");
                po.setType(1);
                po.setContent(monthValue+"月绩效奖金评分通知(截止日期：10号)");
                po.setCode("/performanceRating");
                po.setEmployeeId(f);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
        long end = System.currentTimeMillis();
        log.info("绩效奖金评分设置通知结束，耗时{}",end-start);
    }
}
