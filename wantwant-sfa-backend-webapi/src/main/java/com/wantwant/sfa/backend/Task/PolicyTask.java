package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.policy.service.IPolicyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDate;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/26/上午11:34
 */
@Component
@Slf4j
public class PolicyTask {
    @Autowired
    private IPolicyService policyService;

    @XxlJob("copyPolicy")
    @Transactional
    public ReturnT<String> copyPolicy(String param) {
        // 获取执行日期
        String yearMonth = getYearMonth(param);

        log.info("【policy copy】yearMonth:{}",yearMonth);
        policyService.copyPolicy(yearMonth);


        // 发放政策消息
        policyService.sendNotify(yearMonth);

        return ReturnT.SUCCESS;
    }

    private String getYearMonth(String param) {
        if(StringUtils.isNotBlank(param)){
            return param;
        }

        return LocalDate.now().toString().substring(0,7);
    }


    @XxlJob("sendPolicyNotify")
    @Transactional
    public ReturnT<String> sendPolicyNotify(String param){
        // 获取执行日期
        String yearMonth = getYearMonth(param);

        log.info("【policy notify】yearMonth:{}",yearMonth);

        policyService.sendPolicyNotify(param);

        return ReturnT.SUCCESS;
    }
}
