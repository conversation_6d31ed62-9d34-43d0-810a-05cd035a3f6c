package com.wantwant.sfa.backend.Task;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.interview.dto.InterviewProbationPassDTO;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.service.InterviewProbationService;
import com.wantwant.sfa.backend.interview.strategy.impl.ZWResignStrategyImpl;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketPositionMapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketRegionRelationMapper;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.notify.entity.NotifyContentEntity;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.notify.model.NotifyDetailModel;
import com.wantwant.sfa.backend.notify.template.impl.ProbationNotifyContent;
import com.wantwant.sfa.backend.service.ApplyMemberService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.wantwant.sfa.backend.interview.service.InterviewService.ZW_HR_EMPLOYEE_ID_CODE;

/**
 * @Description: 试岗检测。
 * @Auther: zhengxu
 * @Date: 2021/11/06/下午6:35
 */
@Component
@Slf4j
public class ProbationCheckTask {
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaCustomerMapper sfaCustomerMapper;
    @Autowired
    private CeoOrderHeaderMapper ceoOrderHeaderMapper;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private ROOTConnectorUtil rootConnectorUtil;
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private ZWResignStrategyImpl zwResignStrategy;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyContentMapper notifyContentMapper;
    @Autowired
    private SmallMarketRegionRelationMapper smallMarketRegionRelationMapper;

    private String titleTemplate = "人员变更通知内容{0}";

    private String messageTemplate = "您好，{0}，系统提醒您人员变更清单如下，烦请进行处理，如已处理请忽略。";
    @Autowired
    private InterviewProbationService interviewProbationService;



    @Transactional
    @XxlJob("probationCheckTask")
    public ReturnT<String> check(String param){

        log.info("试岗检查定时任务start..");

        String probation_achievement = configMapper.getValueByCode("probation_achievement");
        // 如果为空 设置默认值为2000
        if(StringUtils.isBlank(probation_achievement)){
            probation_achievement = "2000";
        }

        BigDecimal achievement = new BigDecimal(probation_achievement);

        List<SfaInterviewProcessRecordModel> sfaInterviewProcessRecordModels = sfaInterviewProcessRecordMapper.selectList(new QueryWrapper<SfaInterviewProcessRecordModel>()
                .eq("process_type", ProcessType.PROBATION.getProcessCode())
                .eq("process_result", ProcessResult.PROCESSING.getResultCode())
        );


        Date today = new Date();

        if(!CollectionUtils.isEmpty(sfaInterviewProcessRecordModels)){
            sfaInterviewProcessRecordModels.forEach( e -> {
                doCheck(e,today,achievement);
            });
        }
        log.info("试岗检查定时任务end..");
        return ReturnT.SUCCESS;
    }

    @Transactional(propagation = Propagation.NESTED)
    public void doCheck(SfaInterviewProcessRecordModel model, Date today, BigDecimal achievement){
        // 获取过程ID
        Integer interviewProcessId = model.getInterviewProcessId();
        // 获取流程ID
        SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(interviewProcessId);
        // 获取申请ID
        Integer applicationId = sfaInterviewProcessModel.getApplicationId();
        // 获取员工记录表
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("application_id", applicationId));
        // 获取申请信息
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(applicationId);
        // 获取客户信息
        SfaCustomer customer = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>()
                .eq("position_id", sfaEmployeeInfoModel.getPositionId())
                .eq("channel",3)
        );
        if(Objects.isNull(customer)){
            return;
        }
        Long memberKey = customer.getMemberKey();

        // 销售额
        Float orderAmountByMemberKey = ceoOrderHeaderMapper.getOrderAmountByMemberKey(memberKey,DateUtil.format(sfaInterviewProcessModel.getProbationStartDay(),"yyyy-MM-dd"));

        // V7.9.0根据组织获取组织对应的目标金额，如果没有配置，则为默认
        BigDecimal probationCheckTarget = sfaInterviewProcessRecordMapper.selectProbationTargetPrice(applyMemberPo.getCompanyOrganizationId());
        if(Objects.nonNull(probationCheckTarget)){
            achievement = probationCheckTarget;
        }

        log.info("【probation check】 achievement:{}",achievement.toString());

        // 无销售业绩
        if(Objects.isNull(orderAmountByMemberKey) || orderAmountByMemberKey < achievement.floatValue()){
            Date probationEndDay = sfaInterviewProcessModel.getProbationEndDay();
            if(probationEndDay.before(today)){
                // 试岗未通过
                failedProcess(sfaInterviewProcessModel,model,customer.getMemberKey(),sfaEmployeeInfoModel);
                applyMemberService.syn(applyMemberPo,null, sfaInterviewProcessModel.getProcessType(), sfaInterviewProcessModel.getProcessResult(),"体验失败"
                        ,applyMemberPo.getHighestEducation(),applyMemberPo.getIdCardNum(),applyMemberPo.getBirthDate(),sfaInterviewProcessModel.getRecommendOnboardTime(),null);

                CeoBusinessOrganizationPositionRelation positionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq("position_id", customer.getPositionId())
                        .eq("channel", 3)
                );

                // 回收活动额度
//                zwResignStrategy.closeActivity(positionRelation,sfaEmployeeInfoModel.getBranchCode());
                // 发送试岗通知信息
                sendMessage(sfaEmployeeInfoModel,false);
            }
        }
        else{
            // 试岗通过操作
            InterviewProbationPassDTO dto = new InterviewProbationPassDTO();
            dto.setOrderCompletionTime(LocalDateTime.now());
            dto.setMemberKey(memberKey);
            interviewProbationService.passProbation(dto);
        }
    }

    private void sendMessage(SfaEmployeeInfoModel sfaEmployeeInfoModel,boolean pass) {

        String day = DateUtil.format(new Date(), "yyyy年-MM月-dd日");
        String title = MessageFormat.format(titleTemplate,day);
        String message = MessageFormat.format(messageTemplate,day);


        String companyCode = sfaEmployeeInfoModel.getCompanyCode();
        String areaCode = sfaEmployeeInfoModel.getAreaCode();
        // 分公司
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", companyCode).eq("channel", 3));
        if(Objects.nonNull(companyPosition) && StringUtils.isNotBlank(companyPosition.getEmployeeId())){
            doSendMessage(sfaEmployeeInfoModel, title, message, companyPosition.getEmployeeId(),pass);
        }

        // 大区
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", areaCode).eq("channel", 3));
        if(Objects.nonNull(areaPosition) && StringUtils.isNotBlank(areaPosition.getEmployeeId())){
            doSendMessage(sfaEmployeeInfoModel, title, message, areaPosition.getEmployeeId(),pass);
        }

    }

    private void doSendMessage(SfaEmployeeInfoModel sfaEmployeeInfoModel, String title, String message, String employeeId,boolean pass) {
        NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.EMP_TRANSACTION.getType(), title, employeeId, message);
        ProbationNotifyContent probationNotifyContent = new ProbationNotifyContent();

        NotifyDetailModel notifyDetailModel = new NotifyDetailModel();
        notifyDetailModel.setTemplateId(notifyPO.getTemplateId());
        notifyDetailModel.setAreaName(sfaEmployeeInfoModel.getAreaName());
        notifyDetailModel.setDepartmentName(sfaEmployeeInfoModel.getDepartmentName());
        notifyDetailModel.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
        notifyDetailModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        Integer type = sfaEmployeeInfoModel.getType();
        if(type == 1 && sfaEmployeeInfoModel.getPostType() == 1){
            notifyDetailModel.setPosition("全职合伙人");
        }else if(type == 1 && sfaEmployeeInfoModel.getPostType() == 2){
            notifyDetailModel.setPosition("兼职合伙人");
        }else{
            notifyDetailModel.setPosition("企业合伙人");
        }

        if(pass){
            notifyDetailModel.setStatus("试岗通过");
        }else{
            notifyDetailModel.setStatus("试岗失败");
        }

        notifyDetailModel.setMobile(sfaEmployeeInfoModel.getMobile());
        NotifyContentEntity notifyContentEntity = probationNotifyContent.buildNotifyContent(notifyDetailModel);
        notifyContentMapper.insert(notifyContentEntity);
    }



    private void failedProcess(SfaInterviewProcessModel sfaInterviewProcessModel, SfaInterviewProcessRecordModel record, Long memberKey, SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        record.setProcessResult(ProcessResult.FAILED.getResultCode());
        record.setProcessDate(new Date());
        sfaInterviewProcessRecordMapper.updateById(record);

        sfaInterviewProcessModel.setProcessResult(ProcessResult.FAILED.getResultCode());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

        Integer applicationId = sfaInterviewProcessModel.getApplicationId();

        // 获取对应岗位ID
        String positionId = sfaEmployeeInfoModel.getPositionId();
        // 将对应小标关闭
        smallMarketRegionRelationMapper.closeSmallMarket(sfaEmployeeInfoModel.getId());



        // 关闭客户
        SfaCustomer sc = sfaCustomerMapper.selectOne(new QueryWrapper<SfaCustomer>().eq("memberKey", memberKey));
        sc.setIsFrozen(1);
        sfaCustomerMapper.updateById(sc);

        // 修改员工状态
        sfaEmployeeInfoModel.setEmployeeStatus(EmployeeStatus.PROBATION_FAILED.getType());
        sfaEmployeeInfoMapper.updateById(sfaEmployeeInfoModel);

        log.info("【旺铺汰换接口】调用closeMember,memberKey:{}",memberKey.toString());
        try {
            log.info("【旺铺汰换接口】 start..");
            rootConnectorUtil.closeMember(memberKey.toString(),3, LocalDateTimeUtils.formatTime(LocalDateTime.now(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
        } catch (Exception e) {
            log.error("旺铺汰换接口调用失败:memberKey={}",memberKey);
            e.printStackTrace();
        }
    }


}
