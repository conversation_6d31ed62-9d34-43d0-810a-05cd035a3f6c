package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.review.service.IReviewReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDate;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/23/上午9:54
 */
@Component
@Slf4j
public class ReviewReportTask {
    @Autowired
    private IReviewReportService reviewReportService;

    @XxlJob("reviewReport")
    @Transactional
    public ReturnT<String> execute(String param) {
        log.info("【review report generate】start..");
        String yearMonth = getYearMonth(param);
        log.info("【review report generate】execute date:{}",yearMonth);

        reviewReportService.generateReport(yearMonth);

        return ReturnT.SUCCESS;
    }

    private String getYearMonth(String param) {
        if(StringUtils.isBlank(param)){
            LocalDate excuteDate = LocalDate.now();
            return excuteDate.toString().substring(0, 7);
        }

        return param;
    }
}
