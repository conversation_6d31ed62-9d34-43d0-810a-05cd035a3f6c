package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.mapper.SfaSupplierContractManagementMpper;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.SfaSupplierContractManagementModel;
import com.wantwant.sfa.backend.service.IRealtimeDataService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class SupplierMessageTask {

    @Autowired
    private IRealtimeDataService realtimeDataService;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private SettingsMapper settingsMapper;

    @Autowired
    private SfaSupplierContractManagementMpper sfaSupplierContractManagementMpper;

    /**
     * 供应商合同管理消息提示
     * 定时任务设置每天下午5点
     * 距离合同到期增加3次提醒，分别在：90天、60天、30天
     */
    //@Scheduled(cron = "${task.SupplierMessageTask.cron}")
    @XxlJob("supplierMessageTask")
    public ReturnT<String> configureTasks(String param) {
        log.info("供应商合同管理消息提示通知开始");
        //找到供应商合同到期为 90天、60天、30天 的 合同
        List<SfaSupplierContractManagementModel> sfaSupplierContractManagementModels = sfaSupplierContractManagementMpper.selectList(new QueryWrapper<SfaSupplierContractManagementModel>().eq("expiration_reminder_day", 30)
                .or().eq("expiration_reminder_day", 60).or().eq("expiration_reminder_day", 90));

        //long start = System.currentTimeMillis();
        //找到供应商合同 提示人员
        String supplierMessage = settingsMapper.getSfaSettingsByCode("sfa_supplier_message");
        List<String> supplierMessagePerson = Arrays.asList(supplierMessage.split(","));
        List<NotifyPO> notifyPOS = new ArrayList<>();
        if(CommonUtil.ListUtils.isNotEmpty(supplierMessagePerson)&&CommonUtil.ListUtils.isNotEmpty(sfaSupplierContractManagementModels)){
            supplierMessagePerson.forEach(s ->{
                sfaSupplierContractManagementModels.forEach(m ->{
                    NotifyPO po = new NotifyPO();
                    po.setTitle(m.getSupplierName()+"合同【"+m.getExpirationReminderDay()+"】"+"天到期提醒");
                    po.setType(1);
                    po.setContent(m.getSupplierName()+"合同【"+m.getExpirationReminderDay()+"】"+"天到期提醒");
                    po.setCode("/SupplierManagement?tab=supplierContract");
                    po.setEmployeeId(s);
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                });
            });
            notifyService.saveBatch(notifyPOS);
        }
        //long end = System.currentTimeMillis();
        //log.info("供应商合同管理消息提示，耗时{}",end-start);
        return ReturnT.SUCCESS;
    }
}
