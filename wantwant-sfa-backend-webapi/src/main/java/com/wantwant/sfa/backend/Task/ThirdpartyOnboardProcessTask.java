package com.wantwant.sfa.backend.Task;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.interview.InterviewSearchMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.service.ApplyMemberService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/21/下午4:04
 */
@Component
@Slf4j
public class ThirdpartyOnboardProcessTask {
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private InterviewSearchMapper interviewSearchMapper;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;

    @XxlJob("thirdpartyOnboardProcess")
    @Transactional
    public ReturnT<String> excute(String param) {
        XxlJobLogger.log("【第三方入职办理流程检查】start..");

        List<Integer> interviewProcessIds = interviewSearchMapper.selectEhrOnboardRecord();

        if(CollectionUtils.isEmpty(interviewProcessIds)){
            XxlJobLogger.log("暂无ehr系统待处理的入职");
            return ReturnT.SUCCESS;
        }

        interviewProcessIds.forEach(e -> {
            SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectById(e);
            if(Objects.nonNull(sfaInterviewProcessModel)){
                ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaInterviewProcessModel.getApplicationId());

                SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = sfaInterviewProcessRecordMapper.selectById(sfaInterviewProcessModel.getInterviewRecordId());

                if(Objects.nonNull(applyMemberPo) && Objects.nonNull(sfaInterviewProcessRecordModel)){

                    LocalDateTime recommendOnboardTime = sfaInterviewProcessModel.getRecommendOnboardTime();

                    Date createTime = sfaInterviewProcessRecordModel.getCreateTime();

                    boolean flag = checkDate(recommendOnboardTime,createTime);

                    if(flag){
                        log.info("【第三方入职办理检查】过期流程ID:{},记录ID:{}",sfaInterviewProcessModel.getId(),sfaInterviewProcessRecordModel.getId());
                        sfaInterviewProcessModel.setProcessResult(ProcessResult.PROCESSING.getResultCode());
                        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);

                        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.PROCESSING.getResultCode());
                        sfaInterviewProcessRecordMapper.updateById(sfaInterviewProcessRecordModel);

                        // 同步ehr
                        applyMemberService.syn(
                                applyMemberPo,
                                sfaInterviewProcessRecordModel.getBranchCode(),
                                sfaInterviewProcessModel.getProcessType(),
                                sfaInterviewProcessModel.getProcessResult(),
                                StringUtils.EMPTY,
                                applyMemberPo.getHighestEducation(),
                                applyMemberPo.getIdCardNum(),
                                applyMemberPo.getBirthDate(),
                                sfaInterviewProcessModel.getRecommendOnboardTime(),
                                null);
                    }

                }
            }
        });



        return ReturnT.SUCCESS;
    }

    private boolean checkDate(LocalDateTime recommendOnboardTime, Date createTime) {
        log.info("【第三方入职办理检查】推荐入职时间:{},提交时间:{}",recommendOnboardTime,createTime.toString());
        Instant instant = createTime.toInstant();
        LocalDateTime now = LocalDateTime.now();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        // 建议入职时间在提交时间之前按照提交时间+2天未处理直接转回人资
        if(Objects.isNull(recommendOnboardTime) || recommendOnboardTime.isBefore(localDateTime)){
            localDateTime= localDateTime.plusDays(2);
            if(localDateTime.isBefore(now) || localDateTime.isEqual(now)){
                return true;
            }
        }
        // 建议入职时间在提交时间之后按照建议入职时间+2天未处理直接转回人资
        else{
            recommendOnboardTime= recommendOnboardTime.plusDays(2);
            if(recommendOnboardTime.isBefore(now) || recommendOnboardTime.isEqual(now)){
                return true;
            }
        }

        return false;
    }
}
