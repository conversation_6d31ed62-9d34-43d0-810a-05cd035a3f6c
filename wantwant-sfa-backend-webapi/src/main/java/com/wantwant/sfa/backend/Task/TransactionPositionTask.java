package com.wantwant.sfa.backend.Task;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.service.selector.JobTransferSelector;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.mapper.interview.SfaJobPositionTaskMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionActionMapper;
import com.wantwant.sfa.backend.mapper.transaction.SfaTransactionApplyMapper;
import com.wantwant.sfa.backend.transaction.dto.TransactionAuditDto;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionActionEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.enums.TransactionActionEnum;
import com.wantwant.sfa.backend.transaction.process.impl.MainPartTimeTransferProcess;
import com.wantwant.sfa.backend.transaction.process.impl.PositionAttributeActionProcess;
import com.wantwant.sfa.backend.transaction.process.impl.PositionTransactionActionProcess;
import com.wantwant.sfa.backend.transaction.request.PositionTransactionProcessRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/06/20/下午3:41
 */
@Component
@Slf4j
public class TransactionPositionTask {
    @Autowired
    private SfaJobPositionTaskMapper sfaJobPositionTaskMapper;
    @Autowired
    private SfaTransactionApplyMapper sfaTransactionApplyMapper;
    @Autowired
    private PositionAttributeActionProcess positionAttributeActionProcess;
    @Autowired
    private PositionTransactionActionProcess positionTransactionActionProcess;
    @Autowired
    private MainPartTimeTransferProcess mainPartTimeTransferProcess;
    @Autowired
    private SfaTransactionActionMapper sfaTransactionActionMapper;
    @Resource
    private JobTransferSelector jobTransferSelector;

    @XxlJob("transactionTask")
    @Transactional
    public ReturnT<String> transactionTask(String param) {
        LocalDate executeDate = getExecuteDate(param);
        log.info("【transactionTask】executeDate:{}",executeDate.toString());

        // 获取所有未执行的任务
        List<SfaJobPositionTask> sfaJobPositionTasks = sfaJobPositionTaskMapper.selectList(new QueryWrapper<SfaJobPositionTask>()
                .eq("status", 0).eq("type",3)
                .le("execute_date", executeDate.toString()));

        if(CollectionUtils.isEmpty(sfaJobPositionTasks)){
            log.info("【transactionTask】无可执行的任务");
        }
        // 执行异动任务
        sfaJobPositionTasks.forEach(e -> {

            try {
                Long transactionId = e.getTransactionId();
                SfaTransactionApplyEntity sfaTransactionApplyEntity = sfaTransactionApplyMapper.selectById(transactionId);

                String version = sfaTransactionApplyEntity.getVersion();
                // 老版本异动
                if("V1.0.0".equals(version)){
                    Integer type = sfaTransactionApplyEntity.getType();
                    Integer subType = sfaTransactionApplyEntity.getSubType();


                    TransactionAuditDto transactionAuditDto = new TransactionAuditDto();
                    transactionAuditDto.setSfaTransactionApplyEntity(sfaTransactionApplyEntity);
                    transactionAuditDto.setExecuteDate(LocalDateTimeUtils.formatTime(e.getExecuteDate().atStartOfDay(),LocalDateTimeUtils.yyyy_MM_dd));
                    transactionAuditDto.setEmployeeInfoId(sfaTransactionApplyEntity.getEmployeeInfoId());

                    TransactionActionEnum actionEnum = TransactionActionEnum.getActionId(type, subType);
                    switch (actionEnum){
                        case POSITION_ATTRIBUTE_ACTION:
                            positionAttributeActionProcess.doPositionAttributeAction(transactionAuditDto);
                            break;
                        case POSITION_TRANSACTION_ACTION:
                            PositionTransactionProcessRequest request = initPositionTransactionActionRequest(e,sfaTransactionApplyEntity);
                            positionTransactionActionProcess.accept(sfaTransactionApplyEntity,request);
                            break;
                        case MAIN_PART_TIME_TRANSFER_ACTION:
                            PositionTransactionProcessRequest mainPartTimeTransferRequest = initPositionTransactionActionRequest(e,sfaTransactionApplyEntity);
                            mainPartTimeTransferProcess.accept(sfaTransactionApplyEntity,mainPartTimeTransferRequest);
                            break;
                    }

                    sfaTransactionApplyEntity.setExecuteDate(LocalDate.now());
                    sfaTransactionApplyMapper.updateById(sfaTransactionApplyEntity);

                    // 更新时间
                    e.setStatus(1);
                    e.setProcessTime(LocalDateTime.now());
                    sfaJobPositionTaskMapper.updateById(e);
                }else{
                    JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(sfaTransactionApplyEntity.getTransactionType());
                    ProcessUserDO processUserDO = new ProcessUserDO();
                    processUserDO.setEmployeeId("SYS");
                    processUserDO.setEmployeeName("系统自动");
                    sfaTransactionApplyEntity.setCurrentTime(executeDate);
                    jobTransferSelector.process(jobTransfer,sfaTransactionApplyEntity,processUserDO);

                    // 设置action执行时间
                    SfaTransactionActionEntity sfaTransactionActionEntity = new SfaTransactionActionEntity();
                    sfaTransactionActionEntity.setExecuteTime(LocalDateTime.now());
                    sfaTransactionActionMapper.update(sfaTransactionActionEntity,new LambdaQueryWrapper<SfaTransactionActionEntity>()
                            .eq(SfaTransactionActionEntity::getTransactionId,e.getApplyId())
                            .eq(SfaTransactionActionEntity::getDeleteFlag,0).isNull(SfaTransactionActionEntity::getExecuteTime)
                    );



                    e.setStatus(1);
                    e.setProcessTime(LocalDateTime.now());
                    sfaJobPositionTaskMapper.updateById(e);
                }

            } catch (Exception ex) {
                e.setStatus(2);
                e.setErrMsg(ex.getMessage());
                e.setProcessTime(LocalDateTime.now());
                sfaJobPositionTaskMapper.updateById(e);
            }


        });

        return ReturnT.SUCCESS;
    }


    private PositionTransactionProcessRequest initPositionTransactionActionRequest(SfaJobPositionTask sfaJobPositionTask,SfaTransactionApplyEntity sfaTransactionApplyEntity) {
        PositionTransactionProcessRequest request = new PositionTransactionProcessRequest();
        String currentPositionEndDate = sfaJobPositionTask.getCurrentPositionEndDate();
        if(StringUtils.isNotBlank(currentPositionEndDate)){
            request.setCurrentPositionEndDate(currentPositionEndDate.substring(0,10));
        }
        request.setLaborContract(sfaJobPositionTask.getLabourContract());
        request.setExecuteDate(sfaJobPositionTask.getExecuteDate().toString());
        request.setContractCompany(sfaJobPositionTask.getContractCompany());
        request.setPerson(sfaJobPositionTask.getProcessUserId());
        request.setEmpId(sfaJobPositionTask.getEmployeeId());
        request.setSocialInsuranceProvince(sfaJobPositionTask.getSocialInsuranceProvince());
        request.setSocialInsuranceCity(sfaJobPositionTask.getSocialInsuranceCity());
        request.setSocialInsuranceDistrict(sfaJobPositionTask.getSocialInsuranceDistrict());
        request.setJoiningCompany(sfaJobPositionTask.getJoiningCompany());
        request.setActualJoiningCompany(sfaJobPositionTask.getActualJoiningCompany());
        return request;
    }

    private LocalDate getExecuteDate(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now();
        }

        return LocalDate.parse(param);
    }
}
