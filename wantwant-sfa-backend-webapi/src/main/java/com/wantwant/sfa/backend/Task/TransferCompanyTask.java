package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.transaction.model.TransferCompanyModel;
import com.wantwant.sfa.backend.transaction.service.ITransactionCheckService;
import com.wantwant.sfa.backend.transaction.service.ITransactionService;
import com.wantwant.sfa.backend.transaction.service.ITransferCompanyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/23/上午9:44
 */
@Component
@Slf4j
public class TransferCompanyTask {

    @Autowired
    private ITransferCompanyService transferCompanyService;
    @Autowired
    private ITransactionCheckService transactionCheckService;
    @Autowired
    private ITransactionService transactionService;

    @XxlJob("transferCompanyTask")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> execute(String param) {
        // 获取日期
        String theMonth = getMonth(param);
        log.info("【transfer company task】thMonth:{}",theMonth);

        // 从大数据获取需要执行的memberKey
        List<Long> memberKeys = transferCompanyService.selectMemberKey(theMonth);
        if(CollectionUtils.isEmpty(memberKeys)){
            log.info("【transfer company task】无需调整入职公司的数据");
            return ReturnT.SUCCESS;
        }

        // 对memberKey进行检查，过滤离职的，离职流程中的，异动流程中的
        TransferCompanyModel transferCompanyModel = transactionCheckService.checkTransferCompanyMemberKey(memberKeys);
        log.info("【transfer company task】checkModel:{}",transferCompanyModel);

        List<Long> executeMemberKeyList = transferCompanyModel.getExecuteMemberKeyList();
        if(!CollectionUtils.isEmpty(executeMemberKeyList)){
            transactionService.initJoiningCompanyTransaction(executeMemberKeyList);
        }


        return ReturnT.SUCCESS;
    }

    private String getMonth(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now().toString().substring(0,7);
        }
        return param;
    }
}
