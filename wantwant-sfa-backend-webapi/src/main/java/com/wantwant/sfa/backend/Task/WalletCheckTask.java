package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.transaction.model.TransferCompanyModel;
import com.wantwant.sfa.backend.wallet.service.IWalletNotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/14/下午7:37
 */
@Component
@Slf4j
public class WalletCheckTask {
    @Resource
    private IWalletNotifyService walletNotifyService;


    @XxlJob("walletCheck")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ReturnT<String> execute(String param) {
        // 获取日期
        log.info("start wallet check...");

        walletNotifyService.check();


        return ReturnT.SUCCESS;
    }
}
