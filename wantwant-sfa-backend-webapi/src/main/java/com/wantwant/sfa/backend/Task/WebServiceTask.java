package com.wantwant.sfa.backend.Task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.wantwant.commons.cons.StatusCode;
import com.wantwant.commons.core.util.HttpUtil;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.emp.WpSfaEmpInfo;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.emp.WpSfaEmpServiceImplService;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.leave.CommonReturnType;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.leave.LeaveResult;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.leave.WpLeaveResultServiceImplService;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.overtime.OvertimeResult;
import com.wantwant.sfa.backend.integrate.iwantwant.webservice.wb.overtime.WpOvertimeResultServiceImplService;
import com.wantwant.sfa.backend.mapper.webService.SfaEmpLeavlMapper;
import com.wantwant.sfa.backend.mapper.webService.SfaEmpMapper;
import com.wantwant.sfa.backend.mapper.webService.SfaOvertimeMapper;
import com.wantwant.sfa.backend.model.webService.SfaEmpLeavelModel;
import com.wantwant.sfa.backend.model.webService.SfaEmpModel;
import com.wantwant.sfa.backend.model.webService.SfaEmpOvertimeModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 爱旺旺webService定时任务
 * @Auther: zhengxu
 * @Date: 2021/09/23/上午9:29
 */
@Configuration
@Slf4j
public class WebServiceTask {
    @Value("${webService.WPLeave.url}")
    private String WP_EMP_LEAVE;
    @Value("${webService.WPOvertime.url}")
    private String WP_OVER_TIME;
    @Autowired
    private SfaEmpLeavlMapper sfaEmpLeavlMapper;
    @Autowired
    private SfaOvertimeMapper sfaOvertimeMapper;


    private List<LeaveResult> getLeaveList(){
        try {
            String result =  HttpUtil.get(WP_EMP_LEAVE);
            log.info(result.substring(0,50));
            Response<JSONArray> re = JSONObject.parseObject(result,Response.class);
            if(StatusCode.SUCCESS.getCode() == re.getCode()){
                JSONArray reStr = re.getData();
                if(Objects.isNull(reStr)){
                    return Lists.newArrayList();
                }
                return JSONArray.parseArray(reStr.toJSONString(), LeaveResult.class);
            }else{
                return Lists.newArrayList();
            }
        } catch (IOException e) {
            log.error("调用爱旺旺接口异常！！！！");
            return Lists.newArrayList();
        }
    }

    private List<OvertimeResult> getOvertimeList(){
        try {
            String result =  HttpUtil.get(WP_OVER_TIME);
            log.info(result.substring(0,50));
            Response<JSONArray> re = JSONObject.parseObject(result,Response.class);
            if(StatusCode.SUCCESS.getCode() == re.getCode()){
                JSONArray reStr = re.getData();
                return JSONArray.parseArray(reStr.toJSONString(), OvertimeResult.class);
            }else{
                return Lists.newArrayList();
            }
        } catch (IOException e) {
            log.error("调用爱旺旺接口异常！！！！");
            return Lists.newArrayList();
        }
    }

    /**
     * 同步爱旺旺员工请假信息
     */
//    @Scheduled(cron = "${task.syn.leave}")
    public void synLeave(){
        List<LeaveResult> results = getLeaveList();
        results.forEach(e -> {
            SfaEmpLeavelModel sfaEmpLeavelModel = convertLeavelModel(e);
            List<SfaEmpLeavelModel> sfaEmpLeavelModels = sfaEmpLeavlMapper.selectList(new QueryWrapper<SfaEmpLeavelModel>().eq("emp_id", sfaEmpLeavelModel.getEmpId())
                    .eq("start_date", sfaEmpLeavelModel.getStartDate())
                    .eq("start_time", sfaEmpLeavelModel.getStartTime())
                    .eq("end_time", sfaEmpLeavelModel.getEndTime())
                    .eq("end_date", sfaEmpLeavelModel.getEndDate()));
            if(CollectionUtils.isEmpty(sfaEmpLeavelModels)){
                sfaEmpLeavlMapper.insert(sfaEmpLeavelModel);
            }
        });
        log.info("[同步爱旺旺员工请假信息webService]结束");
    }

    /**
     * 同步爱旺旺加班信息
     */
//    @Scheduled(cron = "${task.syn.overtime}")
        public void synOvertime(){
        List<OvertimeResult> results = getOvertimeList();
        log.info("[同步爱旺旺加班信息webService]开始同步");
        results.forEach(e -> {
            SfaEmpOvertimeModel sfaEmpOvertimeModel = convertOverTImeModel(e);
            List<SfaEmpOvertimeModel> sfaEmpOvertimeModels = sfaOvertimeMapper.selectList(new QueryWrapper<SfaEmpOvertimeModel>().eq("emp_id", sfaEmpOvertimeModel.getEmpId())
                    .eq("start_date", sfaEmpOvertimeModel.getStartDate())
                    .eq("start_time", sfaEmpOvertimeModel.getStartTime())
                    .eq("end_time", sfaEmpOvertimeModel.getEndTime())
                    .eq("end_date", sfaEmpOvertimeModel.getEndDate()));
            if(CollectionUtils.isEmpty(sfaEmpOvertimeModels)){
                sfaOvertimeMapper.insert(sfaEmpOvertimeModel);
            }
        });
        log.info("[同步爱旺旺加班信息webService]结束");
    }

    private SfaEmpOvertimeModel convertOverTImeModel(OvertimeResult e) {
        SfaEmpOvertimeModel model = new SfaEmpOvertimeModel();
        model.setEmpId(e.getXmCode());
        model.setEmpName(e.getXmName());
        model.setStartDate(e.getKsrq());
        model.setStartTime(e.getKssj());
        model.setEndDate(e.getJsrq());
        model.setEndTime(e.getJssj());
        if(StringUtils.isNotBlank(e.getJbss())){
            model.setOverTime(Float.valueOf(e.getJbss()));
        }
        return model;
    }

    private SfaEmpLeavelModel convertLeavelModel(LeaveResult e) {
        SfaEmpLeavelModel result = new SfaEmpLeavelModel();
        result.setEmpId(e.getXmCode());
        result.setEmpName(e.getXmName());
        result.setStartDate(e.getKsrq());
        result.setStartTime(e.getKssj());
        result.setEndDate(e.getJsrq());
        result.setEndTime(e.getJssj());
        result.setType(e.getJb());
        if(StringUtils.isNotBlank(e.getQjss())){
            result.setLeaveTime(Float.valueOf(e.getQjss()));
        }


        return result;
    }

    private SfaEmpModel convertEmpModel(WpSfaEmpInfo e) {
        String empId = e.getEmpId();
        if(StringUtils.isBlank(empId)){
            return null;
        }

        SfaEmpModel model = new SfaEmpModel();
        model.setEmpId(e.getEmpId());
        model.setEmpName(e.getEmpName());
        model.setJb(e.getOrgName6());
        model.setKsqr(e.getOrgName7());
        model.setKssj(e.getOrgName8());
        if(e.getStatus().trim().equals("在职")){
            model.setStatus(0);
        }else{
            model.setStatus(1);
        }

        model.setEmpOnBoardDate(e.getEmpOnboardDate());
        model.setEmpTermDate(e.getEmpTermDate());
        return model;
    }
}
