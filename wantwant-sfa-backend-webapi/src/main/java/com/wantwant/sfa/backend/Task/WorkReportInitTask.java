package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportCalendarMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportTaskMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.workReport.PO.WorkReportCalendarPO;
import com.wantwant.sfa.backend.workReport.dto.WorkReportBuildDTO;
import com.wantwant.sfa.backend.workReport.entity.SfaWorkReportEntity;
import com.wantwant.sfa.backend.workReport.entity.SfaWorkReportTaskEntity;
import com.wantwant.sfa.backend.workReport.service.IWorkReportProcessService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/16/下午1:50
 */
@Component
@Slf4j
public class WorkReportInitTask {

    @Autowired
    private WorkReportMapper workReportMapper;

    @Autowired
    private WorkReportTaskMapper workReportTaskMapper;

    @Autowired
    private IWorkReportProcessService workReportProcessService;

    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper positionRelationMapper;

    @Autowired
    private NotifyService notifyService;

    @Resource
    private WorkReportCalendarMapper workReportCalendarMapper;
    /** 
     * 每周五执行
     */
    @XxlJob("workReportInit")
    public ReturnT<String> workReportInit(String param) {
        log.info("【work report init】start...");
        LocalDate endDate = getDate(param);

        WorkReportCalendarPO workReportCalendarPO = workReportCalendarMapper.selectOne(new LambdaQueryWrapper<WorkReportCalendarPO>().eq(WorkReportCalendarPO::getEndDate, endDate).eq(WorkReportCalendarPO::getPushFlag, 1).eq(WorkReportCalendarPO::getDeleteFlag, 0));
        if(Objects.isNull(workReportCalendarPO)){
            log.info("【work report init】无可创建的周期");
            return ReturnT.SUCCESS;
        }

        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new QueryWrapper<SfaWorkReportEntity>()
                .eq("start_date", workReportCalendarPO.getStartDate())
                .eq("report_type",1)
                .eq("delete_flag", 0)
        );
        if(Objects.nonNull(sfaWorkReportEntity)){
            log.info("【work report init】已创建过");
            return ReturnT.SUCCESS;
        }

        sfaWorkReportEntity = new SfaWorkReportEntity();
        sfaWorkReportEntity.setCreateTime(LocalDateTime.now());
        sfaWorkReportEntity.setDeleteFlag(0);
        sfaWorkReportEntity.setReportType(1);
        sfaWorkReportEntity.setStartDate(workReportCalendarPO.getStartDate());
        sfaWorkReportEntity.setEndDate(workReportCalendarPO.getEndDate());
        sfaWorkReportEntity.setMonth(Integer.parseInt(workReportCalendarPO.getMonth()));
        sfaWorkReportEntity.setWeeks(workReportCalendarPO.getWeeks());


        workReportMapper.insert(sfaWorkReportEntity);


        return ReturnT.SUCCESS;
    }

    /**
     * 每周二08:31执行
     *
     * 添加未填写的周报
     */
    @XxlJob("addNotFillTask")
    public ReturnT<String> addNotFillTask(String param) {
        return doFillTask(param,1);
    }


    @XxlJob("addNotFillTask2")
    public ReturnT<String> addNotFillTask2(String param) {
        return doFillTask(param,2);
    }

    private ReturnT<String> doFillTask(String param,int type) {
        log.info("【work report addNotFillTask】start...");
        LocalDate date = getDate(param);

        // 根据日期获取当前周报
        LocalDate first = date.minusWeeks(1).with(DayOfWeek.FRIDAY);
        LocalDate endDate = first.plusDays(6);

        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new QueryWrapper<SfaWorkReportEntity>()
                .eq("start_date", first)
                .eq("report_type",1)
                .eq("delete_flag", 0)
        );
        if(Objects.isNull(sfaWorkReportEntity)){
            log.info("【work report addUnFillTask】获取当前周报失败！");
            return ReturnT.SUCCESS;
        }
        //查询所有未提交组织
        // 填写状态fillStatus  默认置 2:未填写
        List<WorkReportBuildDTO> list = workReportTaskMapper.selectNotSubmit(sfaWorkReportEntity.getWorkId(),type);
        log.info("获取工作周报未提交数量:{}",list.size());
        if (CommonUtil.ListUtils.isNotEmpty(list)){
            list.forEach(dto -> {
                if (dto.getTaskId() != null){
                    SfaWorkReportTaskEntity sfaWorkReportTaskEntity = new SfaWorkReportTaskEntity();
                    sfaWorkReportTaskEntity.setIsSubmit(1);
                    sfaWorkReportTaskEntity.setTaskId(dto.getTaskId());
                    workReportTaskMapper.updateById(sfaWorkReportTaskEntity);
                }else{
                    try {
                        workReportProcessService.build(dto);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("工作周报生成失败:{}",dto,e);
                    }
                }
            });
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 每周二8:32
     */
    @XxlJob("readWorkReport")
    public ReturnT<String> readWorkReport(String param){
        log.info("查看周报查看推送");
        LocalDate now = LocalDate.now().plusDays(-7);
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        String dateStr = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(now);
        //查询当前周报
        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new QueryWrapper<SfaWorkReportEntity>()
                .le("start_date",dateStr).ge("end_date",dateStr)
                .eq("report_type",1).eq("delete_flag",0)
                .orderByDesc("work_id")
                .last("limit 1"));
        log.info("周报信息:{}",sfaWorkReportEntity);
        if (Objects.nonNull(sfaWorkReportEntity)){
            List<CeoBusinessOrganizationPositionRelation> positionRelations = positionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .in("position_type_id", Arrays.asList(1, 2,11,12))
                    .eq("channel", 3).isNotNull("employee_id"));
            //总部的推送 只推00272473、00441211、00443203、00462947、00462947
            List<String> zbList = Arrays.asList("00272473", "00441211", "00443203", "00462947","00441208");
            List<NotifyPO> notifyPOS = new ArrayList<>();
            Integer weeks = sfaWorkReportEntity.getWeeks();
            Integer month = sfaWorkReportEntity.getMonth();
            String formatContent = MessageFormat.format("【{0}月第{1}周】下属周报填写已截止，点击操作查看", month, weeks);
            positionRelations.forEach(p -> {
                NotifyPO po = new NotifyPO();
                po.setTitle(formatContent);
                po.setType(1);
                po.setContent(formatContent);
                po.setCode("/WorkStatement?tab=1");
                po.setEmployeeId(p.getEmployeeId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                po.setCreateTime(LocalDateTime.now());
                po.setUpdateTime(LocalDateTime.now());
                notifyPOS.add(po);
            });
            zbList.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle(formatContent);
                po.setType(1);
                po.setContent(formatContent);
                po.setCode("/WorkStatement?tab=1");
                po.setEmployeeId(e);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                po.setCreateTime(LocalDateTime.now());
                po.setUpdateTime(LocalDateTime.now());
                notifyPOS.add(po);

            });
            notifyService.saveBatch(notifyPOS);
        }
        return ReturnT.SUCCESS;
    }



    private LocalDate getDate(String param) {
        if(StringUtils.isBlank(param)){
            return LocalDate.now().minusDays(1L);
        }

        return LocalDate.parse(param);
    }
}
