package com.wantwant.sfa.backend.abnormalLogin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.abnormalLogin.api.AbnormalLoginApi;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginAuditRequest;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginCommitRequest;
import com.wantwant.sfa.backend.abnormalLogin.request.AbnormalLoginRequest;
import com.wantwant.sfa.backend.abnormalLogin.service.AbnormalLoginService;
import com.wantwant.sfa.backend.abnormalLogin.vo.AbnormalLoginListVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Api(tags = "异常登录API")
@RestController
@Slf4j
public class AbnormalLoginController implements AbnormalLoginApi {

    private static final String ABNORMALLOGIN_AUDIT_LOCK = "abnormalLogin:audit:lock";

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AbnormalLoginService abnormalLoginService;

    @ApiOperation(value = "异常登录提交", notes = "异常登录提交")
    @Override
    public Response abnormalLoginCommit(AbnormalLoginCommitRequest request) {
        abnormalLoginService.abnormalLoginCommit(request);
        return Response.success();
    }

    @ApiOperation(value = "异常登录列表", notes = "异常登录列表")
    @Override
    public Response<IPage<AbnormalLoginListVo>> abnormalLoginPage(AbnormalLoginRequest request) {
        return Response.success(abnormalLoginService.abnormalLoginPage(request));
    }

    @ApiOperation(value = "异常登录明细列表", notes = "异常登录明细列表")
    @Override
    public Response<List<AbnormalLoginListVo>> abnormalLoginList(@RequestBody AbnormalLoginRequest request) {
        return Response.success(abnormalLoginService.abnormalLoginList(request));
    }

    @ApiOperation(value = "异常登录更多列表", notes = "异常登录更多列表")
    @Override
    public Response<IPage<AbnormalLoginListVo>> abnormalLoginMore(@RequestBody AbnormalLoginRequest request) {
        return Response.success(abnormalLoginService.abnormalLoginMore(request));
    }

    @ApiOperation(value = "异常登录列表-下载", notes = "异常登录列表-下载")
    @Override
    public void abnormalLoginDownload(AbnormalLoginRequest request, HttpServletRequest req, HttpServletResponse res) {
        abnormalLoginService.abnormalLoginDownload(request, req, res);
    }

    @ApiOperation(value = "异常登录稽核", notes = "异常登录稽核")
    @Override
    public Response abnormalLoginAudit(AbnormalLoginAuditRequest request) {
        if (!redisUtil.setLockIfAbsent(ABNORMALLOGIN_AUDIT_LOCK, String.valueOf(request.getId()), 3, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            abnormalLoginService.abnormalLoginAudit(request);
        } finally {
            redisUtil.unLock(ABNORMALLOGIN_AUDIT_LOCK, String.valueOf(request.getId()));
        }
        return Response.success();
    }
}
