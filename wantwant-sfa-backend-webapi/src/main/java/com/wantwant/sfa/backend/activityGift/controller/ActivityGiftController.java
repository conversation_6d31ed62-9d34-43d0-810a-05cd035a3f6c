package com.wantwant.sfa.backend.activityGift.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityGift.api.ActivityApi;
import com.wantwant.sfa.backend.activityGift.request.ActivityGiftPageRequest;
import com.wantwant.sfa.backend.activityGift.request.EmployeeIdRequest;
import com.wantwant.sfa.backend.activityGift.vo.ActivityGIftInfo;
import com.wantwant.sfa.backend.service.ActivityGiftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class ActivityGiftController implements ActivityApi {
    @Autowired
    private ActivityGiftService activityGiftService;
    @Override
    public Response<Page<ActivityGIftInfo>> pageResponse(ActivityGiftPageRequest activityGiftPageRequest){
        return activityGiftService.pageResponse(activityGiftPageRequest);
    }

    @Override
    public Response<List<String>> overAmount() {
        return activityGiftService.getOverAmounts();
    }

    @Override
    public Response<List<Map<String, Object>>> getChannel(EmployeeIdRequest employeeIdRequest) {
        return activityGiftService.getChannel(employeeIdRequest);
    }

}
