package com.wantwant.sfa.backend.activityQuota.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.ActivityQuotaApi;
import com.wantwant.sfa.backend.activityQuota.model.QuotaInfo;
import com.wantwant.sfa.backend.activityQuota.model.UpdateBatchSaleAmountModel;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.service.IActivityQuotaLogService;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaControlVo;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaVo;
import com.wantwant.sfa.backend.activityQuota.vo.RetrieveQuotaVo;
import com.wantwant.sfa.backend.activityQuota.service.ActivityQuotaService;
import com.wantwant.sfa.backend.arch.entity.DepartEntity;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.interview.request.QuotaValidRequest;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.order.request.BranchQuotaInfoVo;
import com.wantwant.sfa.backend.order.request.RetriveQuotaInfoRequest;
import com.wantwant.sfa.backend.order.request.RetriveQuotaRequest;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/11/26/下午2:42
 */
@RestController
@Slf4j
public class ActivityQuotaController implements ActivityQuotaApi {
    private static final String ORG_QUOTA_CONFIG_LOCK = "activity:quota:config:lock";
    private static final String QUOTA_DISTRIBUTE_LOCK = "activity:quota:distribute:lock";
    @Autowired
    private ActivityQuotaService activityQuotaService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private IActivityQuotaLogService activityQuotaLogService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private IPenaltyService penaltyService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @Override
    public Response applyQuota(ApplyQuotaRequest request) {
        activityQuotaService.apply(request);
        return Response.success();
    }

    @Override
    public Response cancel(CancelQuotaRequest request) {
        activityQuotaService.cancel(request);
        return Response.success();
    }



    @Override
    public Response quotaConfig(QuotaConfigRequest request) {
        log.info("【活动额度配置】参数request:{}",request);

        if(!redisUtil.setLockIfAbsent(ORG_QUOTA_CONFIG_LOCK, request.getOrganizationId(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        int businessGroup = RequestUtils.getBusinessGroup();

        try {
            // 获取组织类型
            String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
            String organizationParentId = StringUtils.EMPTY;
            if(organizationType.equals("area")){
                organizationParentId = organizationMapper.getOrganizationByTypeAndProduct("zb",businessGroup);
            }else if(organizationType.equals("company")){
                String provinceCode = organizationMapper.getOrganizationParentId(request.getOrganizationId());
                String vareaCode = organizationMapper.getOrganizationParentId(provinceCode);
                organizationParentId = organizationMapper.getOrganizationParentId(vareaCode);
            }


            // 获取配置的额度
            BigDecimal quota = request.getQuota();
            BigDecimal subQuota = request.getSubQuota();
            BigDecimal addQuota = request.getAddQuota();
            if(Objects.isNull(quota) && Objects.isNull(subQuota) && Objects.isNull(addQuota)){
                throw new ApplicationException("未设置额度");
            }


            // 总部直接设置部门信息
            if(organizationType.equals("zb")){

                DepartEntity deptEntity = deptMapper.selectOne(new QueryWrapper<DepartEntity>().eq("dept_code", request.getDeptCode()).eq("delete_flag", 0));
                if(Objects.isNull(deptEntity)){
                    throw new ApplicationException("部门不存在");
                }

                activityQuotaService.setQuota(request.getOrganizationId(),organizationParentId,quota,addQuota,subQuota,request.getActivityType(),request.getActivityTime(),request.getPerson(),false,request.getApplyType(), deptEntity.getDeptCode(), deptEntity.getDeptName(),request.getRemark(),businessGroup,1);
            }else{
                // V8.2.0不在传入费用类型，这里按先进先出获取费用信息
                Map<Integer, LinkedList<QuotaInfo>> surplus = activityQuotaService.getQuotaInfo(request.getActivityTime(), organizationParentId,businessGroup);

                if(CollectionUtils.isEmpty(surplus)){
                    throw new ApplicationException("没有可配置的额度");
                }

                // 获取扣罚额度
                BigDecimal penaltyAmount = Optional.ofNullable(penaltyService.getTotalPenaltyAmount(organizationParentId, 1)).orElse(BigDecimal.ZERO);

                BigDecimal reduce = surplus.values().stream().flatMap(Collection::stream).map(QuotaInfo::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(penaltyAmount);
                if(reduce.compareTo(request.getAddQuota()) < 0){
                    throw new ApplicationException("额度不足");
                }


                Iterator<Map.Entry<Integer, LinkedList<QuotaInfo>>> iterator = surplus.entrySet().iterator();
                // 需要发送的额度
                AtomicReference<BigDecimal> needSendQuota = new AtomicReference(addQuota);
                while(iterator.hasNext()){
                    Map.Entry<Integer,  LinkedList<QuotaInfo>> next = iterator.next();
                    Integer applyType = next.getKey();
                    LinkedList<QuotaInfo> values = next.getValue();

                    for(QuotaInfo value: values){
                        // 发放额度拆分
                        if(needSendQuota.get().compareTo(BigDecimal.ZERO)  > 0){
                            // 需要发送的额度
                            BigDecimal surplusQuota = needSendQuota.get();
                            // 部门额度
                            BigDecimal deptQuota = value.getQuota();
                            // 发送额度
                            BigDecimal sendQuota = BigDecimal.ZERO;

                            Integer boundary = value.getBoundary();

                            // 部门额度大于等于需要发送的额度则直接发放
                            if(deptQuota.compareTo(surplusQuota) >= 0){
                                sendQuota = surplusQuota;
                                needSendQuota.set(BigDecimal.ZERO);
                            }else{
                                sendQuota = deptQuota;
                                needSendQuota.set(surplusQuota.subtract(sendQuota));
                            }

                            // 设置额度
                            activityQuotaService.setQuota(request.getOrganizationId(),organizationParentId,quota,sendQuota,subQuota,request.getActivityType(),request.getActivityTime(),request.getPerson(), false, applyType,value.getDeptCode(),value.getDeptName(),request.getRemark(),businessGroup,boundary);

                            // 追缴罚款
                            penaltyService.pressPenalty(request.getOrganizationId(),applyType,false);

                            // 临期售后调用旺铺增减分公司额度接口
                            if(organizationType.equals("company") && (applyType == 27 || applyType == 14)){
                                UpdateBatchSaleAmountModel model = new UpdateBatchSaleAmountModel();
                                model.setAmount(sendQuota);
                                model.setApplyType(applyType);
                                model.setFlag(1);
                                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroup);
                                model.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
                                model.setCompanyName(organizationMapper.getOrganizationName(request.getOrganizationId()));
                                model.setYearMonthStart(LocalDateTimeUtils.formatNow("yyyy-MM"));
                                model.setYearMonthEnd(LocalDateTimeUtils.formatNow("yyyy-MM"));
                                activityQuotaConnectorUtil.updateBatchSaleAmountForSfa(model);
                            }

                        }
                    }
                }

            }



        }finally {
            redisUtil.unLock(ORG_QUOTA_CONFIG_LOCK,request.getOrganizationId());
        }
        return Response.success();
    }

    @Override
    public Response passValid(QuotaValidRequest request) {

        activityQuotaService.valid(request,1);

        return Response.success();
    }


    @Override
    public Response faliedValid(QuotaValidRequest request) {
        activityQuotaService.valid(request,2);
        return Response.success();
    }

    @Override
    public Response<RetrieveQuotaVo> retrieveQuotaInfo(RetriveQuotaInfoRequest request) {
        log.info("【活动额度回收信息】参数:{}",request);
        RetrieveQuotaVo vo = activityQuotaService.retrieveQuotaInfo(request);

        return Response.success(vo);
    }


    @Override
    public Response retriveQuota(RetriveQuotaRequest request) {
        log.info("【活动额度回收】参数request:{}",request);
        activityQuotaService.retriveQuota(request);
        return Response.success();
    }

    @Override
    public Response<BranchQuotaInfoVo> getBranchQuotaInfo(BranchQuotRequest request) {
        BranchQuotaInfoVo vo = activityQuotaService.getBranchQuotaInfo(request);
        return Response.success(vo);
    }

    @Override
    public Response quotaDistribute(QuotaDistributeRequest request) {
        log.info("【额度发放】request:{}",request);

        if(!redisUtil.setLockIfAbsent(QUOTA_DISTRIBUTE_LOCK, request.getCompanyCode(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try{
            activityQuotaService.quotaDistribute(request);
        }finally {
            redisUtil.unLock(QUOTA_DISTRIBUTE_LOCK,request.getCompanyCode());
        }

        return Response.success();
    }

    @Override
    public Response<QuotaVo> selectQuota(String month, String organizationId, Integer applyType,String deptCode) {
        log.info("【额度查询】month:{},organizationId:{},applyType:{},deptCode:{}",applyType,organizationId,applyType,deptCode);

        QuotaVo vo = activityQuotaLogService.selectQuota(month,organizationId,applyType,deptCode);
        return Response.success(vo);
    }

    @Override
    public Response<QuotaControlVo> selectDistributeQuota(QuotaControlRequest request) {
        log.info("【select quota control】request",request);

        QuotaControlVo vo =  activityQuotaService.selectDistributeQuota(request);

        return Response.success(vo);
    }

    @Override
    public Response SetQuotaPermission(SetQuotaPermissionRequest request) {


        activityQuotaService.setQuotaPermission(request);


        return Response.success();
    }

    @Override
    public Response<String> getDefaultDeptCode() {
        return Response.success(configMapper.getValueByCode("quota_default_dept_code"));
    }


    private String getZBorganizationId() {
        int channel = RequestUtils.getChannel();
        if(channel == 1){
            return "ZB";
        }else if(channel == 2){
            return "ZB_S";
        }else{
            return "ZB_Z";
        }
    }
}
