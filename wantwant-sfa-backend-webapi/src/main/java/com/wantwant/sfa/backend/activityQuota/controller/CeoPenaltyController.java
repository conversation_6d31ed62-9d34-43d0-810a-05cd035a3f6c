package com.wantwant.sfa.backend.activityQuota.controller;

import com.gexin.fastjson.JSONArray;
import com.wantwant.sfa.backend.activityQuota.api.CeoPenaltyApi;
import com.wantwant.sfa.backend.activityQuota.api.OrgPenaltyRequest;
import com.wantwant.sfa.backend.activityQuota.request.CeoPenaltyRequest;
import com.wantwant.sfa.backend.activityQuota.request.PersonalPenaltyRequest;
import com.wantwant.sfa.backend.activityQuota.request.RewardPenaltyDeductionRequest;
import com.wantwant.sfa.backend.activityQuota.service.ICeoPenaltyService;
import com.wantwant.sfa.backend.activityQuota.vo.RewardPenaltyDeductionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.wantwant.commons.web.response.Response;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/04/上午10:14
 */
@RestController
@Slf4j
public class CeoPenaltyController implements CeoPenaltyApi {
    @Autowired
    private ICeoPenaltyService ceoPenaltyService;

    @Override
    public Response<List<String>> process(List<CeoPenaltyRequest> list) {
        log.info("【ceo penalty process】request:{}",list);

        List<String> process = ceoPenaltyService.process(list);

        return Response.success(process);
    }

    @Override
    public Response<List<String>> orgProcess(@Valid List<OrgPenaltyRequest> list) {
        log.info("【org penalty process】request:{}",list);

        List<String> process =  ceoPenaltyService.orgProcess(list);
        return  Response.success(process);
    }

    @Override
    public Response<List<String>> personalPenalty(@NotEmpty(message = "缺少扣罚信息") List<PersonalPenaltyRequest> list) {
        log.info("【personal penalty】request:{}",list);

        List<String> process =  ceoPenaltyService.personalPenalty(list);

        return Response.success(process);
    }
    
}
