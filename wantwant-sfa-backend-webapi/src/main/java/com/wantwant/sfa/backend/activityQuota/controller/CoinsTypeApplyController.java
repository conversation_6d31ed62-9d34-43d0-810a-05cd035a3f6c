package com.wantwant.sfa.backend.activityQuota.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.CoinsTypeApplyApi;
import com.wantwant.sfa.backend.activityQuota.request.CoinsTypeApplyApprovalRequest;
import com.wantwant.sfa.backend.activityQuota.request.CoinsTypeApplyRejectRequest;
import com.wantwant.sfa.backend.activityQuota.request.CostTypeApplyRequest;
import com.wantwant.sfa.backend.activityQuota.request.CostTypeTriggerStatusRequest;
import com.wantwant.sfa.backend.activityQuota.service.ICoinsTypeApplyService;
import com.wantwant.sfa.backend.activityQuota.vo.CoinsApplyProcessVO;
import com.wantwant.sfa.backend.activityQuota.vo.CoinsTypeApplyInfoVo;
import com.wantwant.sfa.backend.activityQuota.vo.CoinsTypeApplyPermissionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/17/上午11:27
 */
@RestController
@Slf4j
public class CoinsTypeApplyController implements CoinsTypeApplyApi {

    @Autowired
    private ICoinsTypeApplyService costTypeApplyService;

    @Override
    public Response apply(CostTypeApplyRequest costTypeApplyRequest) {
        log.info("【coins type apply】request:{}",costTypeApplyRequest);

        costTypeApplyService.apply(costTypeApplyRequest);

        return Response.success();
    }


    @Override
    public Response approval(CoinsTypeApplyApprovalRequest coinsTypeApplyApprovalRequest) {
        log.info("【coins type apply approval】request:{}",coinsTypeApplyApprovalRequest);


        costTypeApplyService.approval(coinsTypeApplyApprovalRequest);

        return Response.success();
    }

    @Override
    public Response reject(CoinsTypeApplyRejectRequest coinsTypeApplyRejectRequest) {
        log.info("【coins type apply reject】request:{}",coinsTypeApplyRejectRequest);

        costTypeApplyService.reject(coinsTypeApplyRejectRequest);

        return Response.success();
    }

    @Override
    public Response<CoinsTypeApplyInfoVo> getCoinsTypeApplyInfo(Long applyId,String person) {
        log.info("【coins type apply info】applyId:{}",applyId);

        CoinsTypeApplyInfoVo vo = costTypeApplyService.getCoinsTypeApplyInfo(applyId,person);

        return Response.success(vo);
    }

    @Override
    public Response<List<CoinsApplyProcessVO>> getProcessVo(Long applyId) {
        log.info("【coins type apply process】applyId:{}\",applyId");

        List<CoinsApplyProcessVO> list = costTypeApplyService.getProcessVo(applyId);

        return Response.success(list);
    }

    @Override
    public Response<CoinsTypeApplyPermissionVo> getPermission(String person) {
        log.info("【coins type apply permission】person:{}",person);

        CoinsTypeApplyPermissionVo vo = costTypeApplyService.getPermission(person);
        return Response.success(vo);
    }

    @Override
    public Response triggerStatus(@Valid CostTypeTriggerStatusRequest costTypeTriggerStatusRequest) {
        log.info("【coins type status trigger】request:{}",costTypeTriggerStatusRequest);
        costTypeApplyService.triggerStatus(costTypeTriggerStatusRequest);

        return Response.success();
    }
}
