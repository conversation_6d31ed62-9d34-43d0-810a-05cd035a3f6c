package com.wantwant.sfa.backend.activityQuota.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.CostTypeApi;
import com.wantwant.sfa.backend.activityQuota.request.CostTypeRequest;
import com.wantwant.sfa.backend.activityQuota.service.ICostTypeService;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/06/上午10:50
 */
@RestController
@Slf4j
public class CostTypeController implements CostTypeApi {
    @Autowired
    private ICostTypeService costTypeService;

    @Override
    public Response<List<CostDepartmentVo>> getCostDepartment(Integer costType) {
        log.info("【get cost department】costType:{}",costType);
        return Response.success(costTypeService.getCostDepartment(costType));
    }

    @Override
    public Response<Page<CostTypeVo>> costTypeList(CostTypeRequest request) {
        log.info("【cost type list】request:{}",request);


        Page<CostTypeVo> page = costTypeService.costTypeList(request);
        return Response.success(page);
    }

    @Override
    public void costTypeExport(CostTypeRequest request) {
        log.info("【cost type export】request:{}",request);

        costTypeService.costTypeExport(request);
    }

    @Override
    public Response<List<CostCategoryVo>> costCategoryList(Integer mainCategoryId) {


        return Response.success(costTypeService.costCategoryList(mainCategoryId));
    }

    @Override
    public Response<CoinsTypeVo> getCoinsConfig(String applyType) {
        log.info("【coins config】applyType:{}",applyType);

        CoinsTypeVo vo = costTypeService.getCoinsConfig(applyType);

        return Response.success(vo);
    }

    @Override
    public Response<List<CoinsClassTypeVo>> getClassType() {
        List<CoinsClassTypeVo> list = costTypeService.getClassType();
        return Response.success(list);
    }
}
