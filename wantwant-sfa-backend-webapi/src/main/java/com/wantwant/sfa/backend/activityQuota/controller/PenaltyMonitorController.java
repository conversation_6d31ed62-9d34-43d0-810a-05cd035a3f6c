package com.wantwant.sfa.backend.activityQuota.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gexin.fastjson.JSONArray;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.PenaltyMonitorApi;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyDetailVo;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyMonitorVO;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyVo;
import com.wantwant.sfa.backend.activityQuota.vo.RewardPenaltyDeductionVO;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/17/上午10:41
 */
@RestController
@Slf4j
public class PenaltyMonitorController implements PenaltyMonitorApi {

    @Autowired
    private IPenaltyService penaltyService;
    @Autowired
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;

    @Override
    public Response<Page<PenaltyVo>> selectPenalty(PenaltyQueryRequest request) {
        log.info("【penalty select】request:{}",request);
        Page<PenaltyVo> page = penaltyService.selectPenalty(request);

        return Response.success(page);
    }

    @Override
    public Response<IPage<PenaltyMonitorVO>> selectPenaltyMonitor(@Valid PenaltyMonitorSearchRequest penaltyMonitorSearchRequest) {
        log.info("【select penalty monitor】request:{}", JSONObject.toJSONString(penaltyMonitorSearchRequest));
        penaltyMonitorSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        IPage<PenaltyMonitorVO> page =  penaltyService.selectPenaltyMonitor(penaltyMonitorSearchRequest);

        return Response.success(page);
    }

    @Override
    public void exportPenaltyMonitor(@Valid PenaltyMonitorSearchRequest penaltyMonitorSearchRequest) {
        penaltyMonitorSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        penaltyService.exportPenaltyMonitor(penaltyMonitorSearchRequest);
    }

    @Override
    public Response<List<PenaltyDetailVo>> getPenaltyDetail(Long penaltyId) {
        log.info("【penalty detail】penaltyId:{}",penaltyId);

        List<PenaltyDetailVo> list = penaltyService.getPenaltyDetail(penaltyId);

        return Response.success(list);
    }

    @Override
    public void exportPenalty(PenaltyQueryRequest request) {
        log.info("【penalty select】export:{}",request);
        penaltyService.exportPenalty(request);
    }

    @Override
    public Response updateStatus(UpdateStatusRequest request) {
        log.info("【penalty update status】 request:{}",request);

        penaltyService.updateStatus(request);

        return Response.success();
    }

    @Override
    public Response batchUpdateStatus(UpdateStatusBatchRequest request) {
        log.info("【penalty update status batch】 request:{}",request);

        penaltyService.batchUpdateStatus(request);

        return  Response.success();
    }

    @Override
    public Response<List<String>> upload(MultipartFile file, String person) {

        List<String> errMsg = penaltyService.upload(file, person);

        return Response.success(errMsg);
    }

    @Override
    public Response<Boolean> checkCanProcess(String person) {

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person, RequestUtils.getLoginInfo());

        RoleEmployeeRelationEntity roleEmployeeRelationEntity = roleEmployeeRelationMapper.selectOne(new QueryWrapper<RoleEmployeeRelationEntity>()
                .eq("employee_id", person)
                .eq("position_id",personInfo.getPositionId())
                .eq("role_id", 46)
                .eq("delete_flag", 0)
        );
        if(Objects.isNull(roleEmployeeRelationEntity)){
            return Response.success(false);
        }
        return Response.success(true);
    }


    @Override
    public Response rewardPenaltyDeduction(RewardPenaltyOperateRequest rewardPenaltyOperateRequest) {
        log.info("【reward penalty deduction】request:{}", JSONObject.toJSONString(rewardPenaltyOperateRequest));

        penaltyService.rewardPenaltyDeduction(rewardPenaltyOperateRequest);

        return Response.success();
    }
}
