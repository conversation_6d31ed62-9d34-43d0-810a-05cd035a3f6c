package com.wantwant.sfa.backend.activityQuota.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.PenaltyRegularApi;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyRegularService;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyAttendanceRegular;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyCategoryVo;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyEliminateRegularVo;
import com.wantwant.sfa.backend.activityQuota.vo.PenaltyRegularVo;
import com.wantwant.sfa.backend.util.PageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/11/下午3:43
 */
@RestController
@Slf4j
public class PenaltyRegularController implements PenaltyRegularApi {
    @Autowired
    private IPenaltyRegularService penaltyRegularService;

    @Override
    public Response create(@Valid CPenaltyRequest request) {
        log.info("【create penalty regular】request:{}",request);
        penaltyRegularService.create(request);
        return Response.success();
    }

    @Override
    public Response update(@Valid UPenaltyRequest request) {
        log.info("【update penalty regular】request:{}",request);
        penaltyRegularService.update(request);
        return Response.success();
    }

    @Override
    public Response<Page<PenaltyRegularVo>> getPenaltyRegularList(PenaltyRegularSearchRequest penaltyRegularSearchRequest) {
        log.info("【penalty regular list】request:{}",penaltyRegularSearchRequest);
        Page<PenaltyRegularVo> page = penaltyRegularService.getPenaltyRegularList(penaltyRegularSearchRequest);
        return Response.success(page);
    }

    @Override
    public void penaltyRegularExport(PenaltyRegularSearchRequest request) {
        log.info("【penalty regular export】request:{}",request);
        penaltyRegularService.penaltyRegularExport(request);
    }

    @Override
    public Response<List<PenaltyCategoryVo>> getPenaltyCategory() {
        List<PenaltyCategoryVo> list = penaltyRegularService.getPenaltyCategory();
        return Response.success(list);
    }

    @Override
    public Response<PenaltyEliminateRegularVo> getPenaltyEliminateInfo() {
        PenaltyEliminateRegularVo vo = penaltyRegularService.getPenaltyEliminateInfo();
        return Response.success(vo);
    }

    @Override
    public Response<PenaltyAttendanceRegular> getPenaltyAttendanceInfo() {
        PenaltyAttendanceRegular vo = penaltyRegularService.getPenaltyAttendanceInfo();
        return Response.success(vo);
    }

    @Override
    public Response switchRegular(@Valid PenaltySwitchRequest penaltyCloseRequest) {
        log.info("【switch regular】request:{}",penaltyCloseRequest);
        penaltyRegularService.switchRegular(penaltyCloseRequest);
        return Response.success();
    }

    @Override
    public Response updatePenaltyEliminateRegular(@Valid PenaltyEliminateRegularEditRequest request) {
        log.info("【update penalty eliminate regular】 request:{}",request);
        penaltyRegularService.updatePenaltyEliminateRegular(request);
        return Response.success();
    }

    @Override
    public Response updateAttendanceRegular(@Valid PenaltyAttendanceRegularRequest request) {
        log.info("【update attendance regular】request:{}",request);

        penaltyRegularService.updateAttendanceRegular(request);

        return Response.success();
    }

    @Override
    public Response<PenaltyRegularVo> getRegular(Long id) {
        log.info("【get reuglar info】 id :{}",id);

        PenaltyRegularVo vo = penaltyRegularService.getPenaltyRegularById(id);

        return Response.success(vo);
    }

    @Override
    public Response<List<PenaltyRegularVo>> getAllRegular() {
        List<PenaltyRegularVo> list = penaltyRegularService.getAllRegular();
        return Response.success(list);
    }
}
