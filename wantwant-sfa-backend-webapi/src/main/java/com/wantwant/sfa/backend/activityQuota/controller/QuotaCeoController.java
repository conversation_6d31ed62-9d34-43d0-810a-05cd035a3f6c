package com.wantwant.sfa.backend.activityQuota.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.QuotaCeoApi;
import com.wantwant.sfa.backend.activityQuota.request.CeoQuotaApplyRequest;
import com.wantwant.sfa.backend.activityQuota.request.CeoQuotaDistributeRequest;
import com.wantwant.sfa.backend.activityQuota.service.ActivityQuotaService;
import com.wantwant.sfa.backend.activityQuota.service.CeoQuotaLogService;
import com.wantwant.sfa.backend.activityQuota.service.ICeoQuotaService;
import com.wantwant.sfa.backend.activityQuota.service.impl.CeoQuotaService;
import com.wantwant.sfa.backend.activityQuota.vo.QuotaSurplusVo;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.service.FreeSampleRecordService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/04/上午11:50
 */
@RestController
@Slf4j
public class QuotaCeoController implements QuotaCeoApi {
    private static final String CEO_QUOTA_APPLY_LOCK = "ceo:quota:apply:memberKey:";
    private static final String QUOTA_DISTRIBUTE_LOCK = "activity:quota:distribute:lock";

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CeoQuotaLogService ceoQuotaLogService;
    @Autowired
    private ActivityQuotaService activityQuotaService;
    @Autowired
    private FreeSampleRecordService freeSampleRecordService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @Override
    public Response quotaApply(CeoQuotaApplyRequest request) {
        log.info("ceo quota apply request:{}",request);

        if(!redisUtil.setLockIfAbsent(CEO_QUOTA_APPLY_LOCK,request.getApplyMemberKey().toString(),30, TimeUnit.SECONDS)){
            return Response.error("请勿重复操作!");
        }

        try{
            String err = ceoQuotaLogService.quotaApply(request);
            if(StringUtils.isNotBlank(err)){
                return Response.error(err);
            }
        }finally{
            redisUtil.unLock(CEO_QUOTA_APPLY_LOCK,request.getApplyMemberKey().toString());
        }

        return Response.success();
    }

    @Override
    public Response<QuotaSurplusVo> quotaSearch(String companyName,String businessGroupCode) {
        log.info("【ceo quota search】companyName:{},businessGroupCode:{}",companyName,businessGroupCode);

        // 查询分公司code
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new QueryWrapper<SfaBusinessGroupEntity>().eq("business_group_code", businessGroupCode).eq("delete_flag", 0));
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("业务组CODE获取失败");
        }
        // 获取分公司的CODE
        String companyCode = organizationMapper.getOrganizationIdByName(companyName, 3, sfaBusinessGroupEntity.getId());



        QuotaSurplusVo quotaSurplusVo = freeSampleRecordService.quotaSearch(companyCode);
        return Response.success(quotaSurplusVo);
    }

    @Override
    public Response distribute(CeoQuotaDistributeRequest ceoQuotaDistributeRequest) {
        log.info("【ceo quota distribute】request:{}",ceoQuotaDistributeRequest);

        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new QueryWrapper<SfaBusinessGroupEntity>().eq("business_group_code", ceoQuotaDistributeRequest.getBusinessGroupCode()).eq("delete_flag", 0));
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("业务组CODE获取失败");
        }
        // 获取分公司的CODE
        String organizationId = organizationMapper.getOrganizationIdByName(ceoQuotaDistributeRequest.getCompanyName(), 3, sfaBusinessGroupEntity.getId());


        if(!redisUtil.setLockIfAbsent(QUOTA_DISTRIBUTE_LOCK, organizationId, 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try{
            activityQuotaService.ceoQuotaDistribute(ceoQuotaDistributeRequest);
        }finally {
            redisUtil.unLock(QUOTA_DISTRIBUTE_LOCK,organizationId);
        }




        return Response.success();
    }
}
