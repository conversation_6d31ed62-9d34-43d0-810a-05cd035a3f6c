package com.wantwant.sfa.backend.activityQuota.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.QuotaPurseApi;
import com.wantwant.sfa.backend.activityQuota.enums.AreaTypeCodeEnum;
import com.wantwant.sfa.backend.activityQuota.request.BusinessQuotaRequest;
import com.wantwant.sfa.backend.activityQuota.request.OrgQuotaRequest;
import com.wantwant.sfa.backend.activityQuota.request.PurseSearchRequest;
import com.wantwant.sfa.backend.activityQuota.service.IQuotaPurseService;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/09/上午10:34
 */
@RestController
@Slf4j
public class QuotaPurseController implements QuotaPurseApi {
    @Autowired
    private IQuotaPurseService quotaPurseService;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;

    @Override
    public Response<Page<AreaPurseVo>> selectAreaPurse(PurseSearchRequest request) {
        log.info("【大区收支查询】request:{}",request);
        getRegioIds(request);
        Page<AreaPurseVo>  page = quotaPurseService.selectAreaPurse(request);

        return Response.success(page);
    }

    @Override
    public void areaPurseExport(PurseSearchRequest request) {
        log.info("【大区收支导出】request:{}",request);
        getRegioIds(request);
        quotaPurseService.areaPurseExport(request);
    }

    private void getRegioIds(PurseSearchRequest request) {
        if(request.getAreaTypeCode().equals(AreaTypeCodeEnum.AREA.getCode())){
            LambdaQueryWrapper<CeoBusinessOrganizationViewEntity> entityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            entityLambdaQueryWrapper.eq(CeoBusinessOrganizationViewEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .eq(CeoBusinessOrganizationViewEntity::getChannel,RequestUtils.getChannel())
                    .in(CollUtil.isNotEmpty(request.getAreaOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId3,request.getAreaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getVareaOrganizationIds()),CeoBusinessOrganizationViewEntity::getVirtualAreaId,request.getVareaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getProvinceOrganizationIds()),CeoBusinessOrganizationViewEntity::getProvinceId,request.getProvinceOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getCompanyOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId2,request.getCompanyOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getDepartmentIds()),CeoBusinessOrganizationViewEntity::getDepartmentId,request.getDepartmentIds())
                    .eq(CeoBusinessOrganizationViewEntity::getOrganizationType,AreaTypeCodeEnum.AREA.getCode());
            List<CeoBusinessOrganizationViewEntity> ceoBusinessOrganizationViewEntities = ceoBusinessOrganizationViewMapper.selectList(entityLambdaQueryWrapper);
            if(CollUtil.isNotEmpty(ceoBusinessOrganizationViewEntities)){
                List<String> ids = ceoBusinessOrganizationViewEntities.stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList());
                request.setAreaOrganizationIds(ids);
            }
        }else if(request.getAreaTypeCode().equals(AreaTypeCodeEnum.VAREA.getCode())){
            LambdaQueryWrapper<CeoBusinessOrganizationViewEntity> entityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            entityLambdaQueryWrapper.eq(CeoBusinessOrganizationViewEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .eq(CeoBusinessOrganizationViewEntity::getChannel,RequestUtils.getChannel())
                    .in(CollUtil.isNotEmpty(request.getAreaOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId3,request.getAreaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getVareaOrganizationIds()),CeoBusinessOrganizationViewEntity::getVirtualAreaId,request.getVareaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getProvinceOrganizationIds()),CeoBusinessOrganizationViewEntity::getProvinceId,request.getProvinceOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getCompanyOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId2,request.getCompanyOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getDepartmentIds()),CeoBusinessOrganizationViewEntity::getDepartmentId,request.getDepartmentIds())
                    .eq(CeoBusinessOrganizationViewEntity::getOrganizationType,AreaTypeCodeEnum.VAREA.getCode());
            List<CeoBusinessOrganizationViewEntity> ceoBusinessOrganizationViewEntities = ceoBusinessOrganizationViewMapper.selectList(entityLambdaQueryWrapper);
            if(CollUtil.isNotEmpty(ceoBusinessOrganizationViewEntities)){
                List<String> ids = ceoBusinessOrganizationViewEntities.stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList());
                request.setVareaOrganizationIds(ids);
            }
        }else if(request.getAreaTypeCode().equals(AreaTypeCodeEnum.PROVINCE.getCode())){
            LambdaQueryWrapper<CeoBusinessOrganizationViewEntity> entityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            entityLambdaQueryWrapper.eq(CeoBusinessOrganizationViewEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .eq(CeoBusinessOrganizationViewEntity::getChannel,RequestUtils.getChannel())
                    .in(CollUtil.isNotEmpty(request.getAreaOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId3,request.getAreaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getVareaOrganizationIds()),CeoBusinessOrganizationViewEntity::getVirtualAreaId,request.getVareaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getProvinceOrganizationIds()),CeoBusinessOrganizationViewEntity::getProvinceId,request.getProvinceOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getCompanyOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId2,request.getCompanyOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getDepartmentIds()),CeoBusinessOrganizationViewEntity::getDepartmentId,request.getDepartmentIds())
                    .eq(CeoBusinessOrganizationViewEntity::getOrganizationType,AreaTypeCodeEnum.PROVINCE.getCode());
            List<CeoBusinessOrganizationViewEntity> ceoBusinessOrganizationViewEntities = ceoBusinessOrganizationViewMapper.selectList(entityLambdaQueryWrapper);
            if(CollUtil.isNotEmpty(ceoBusinessOrganizationViewEntities)){
                List<String> ids = ceoBusinessOrganizationViewEntities.stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList());
                request.setProvinceOrganizationIds(ids);
            }
        }else if(request.getAreaTypeCode().equals(AreaTypeCodeEnum.COMPANY.getCode())){
            LambdaQueryWrapper<CeoBusinessOrganizationViewEntity> entityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            entityLambdaQueryWrapper.eq(CeoBusinessOrganizationViewEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .eq(CeoBusinessOrganizationViewEntity::getChannel,RequestUtils.getChannel())
                    .in(CollUtil.isNotEmpty(request.getAreaOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId3,request.getAreaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getVareaOrganizationIds()),CeoBusinessOrganizationViewEntity::getVirtualAreaId,request.getVareaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getProvinceOrganizationIds()),CeoBusinessOrganizationViewEntity::getProvinceId,request.getProvinceOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getCompanyOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId2,request.getCompanyOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getDepartmentIds()),CeoBusinessOrganizationViewEntity::getDepartmentId,request.getDepartmentIds())
                    .eq(CeoBusinessOrganizationViewEntity::getOrganizationType,AreaTypeCodeEnum.COMPANY.getCode());
            List<CeoBusinessOrganizationViewEntity> ceoBusinessOrganizationViewEntities = ceoBusinessOrganizationViewMapper.selectList(entityLambdaQueryWrapper);
            if(CollUtil.isNotEmpty(ceoBusinessOrganizationViewEntities)){
                List<String> ids = ceoBusinessOrganizationViewEntities.stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList());
                request.setCompanyOrganizationIds(ids);
            }
        }else if(request.getAreaTypeCode().equals(AreaTypeCodeEnum.DEPARTMENT.getCode())){
            LambdaQueryWrapper<CeoBusinessOrganizationViewEntity> entityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            entityLambdaQueryWrapper.eq(CeoBusinessOrganizationViewEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .eq(CeoBusinessOrganizationViewEntity::getChannel,RequestUtils.getChannel())
                    .in(CollUtil.isNotEmpty(request.getAreaOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId3,request.getAreaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getVareaOrganizationIds()),CeoBusinessOrganizationViewEntity::getVirtualAreaId,request.getVareaOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getProvinceOrganizationIds()),CeoBusinessOrganizationViewEntity::getProvinceId,request.getProvinceOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getCompanyOrganizationIds()),CeoBusinessOrganizationViewEntity::getOrgId2,request.getCompanyOrganizationIds())
                    .in(CollUtil.isNotEmpty(request.getDepartmentIds()),CeoBusinessOrganizationViewEntity::getDepartmentId,request.getDepartmentIds())
                    .eq(CeoBusinessOrganizationViewEntity::getOrganizationType,AreaTypeCodeEnum.DEPARTMENT.getCode());
            List<CeoBusinessOrganizationViewEntity> ceoBusinessOrganizationViewEntities = ceoBusinessOrganizationViewMapper.selectList(entityLambdaQueryWrapper);
            if(CollUtil.isNotEmpty(ceoBusinessOrganizationViewEntities)){
                List<String> ids = ceoBusinessOrganizationViewEntities.stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList());
                request.setDepartmentIds(ids);
            }
        }
    }

    @Override
    public Response<Page<CompanyPurseVo>> selectCompanyPurse(PurseSearchRequest request) {
        log.info("【分公司收支查询】request:{}",request);
        Page<CompanyPurseVo>  page = quotaPurseService.selectCompanyPurse(request);

        return Response.success(page);
    }

    @Override
    public void companyPurseExport(PurseSearchRequest request) {
        log.info("【分公司收支导出】request:{}",request);
        quotaPurseService.companyPurseExport(request);
    }

    @Override
    public Response<Page<CeoPurseVo>> selectBusinessPurse(PurseSearchRequest request) {
        log.info("【业务收支查询】request:{}",request);

        Page<CeoPurseVo> page = quotaPurseService.selectBusinessPurse(request);
        return Response.success(page);
    }

    @Override
    public void businessPurseExport(PurseSearchRequest request) {
        log.info("【业务收支导出】request:{}",request);
        quotaPurseService.businessPurseExport(request);
    }

    @Override
    public Response<Page<AreaQuotaVo>> selectAreaQuota(OrgQuotaRequest request) {
        log.info("【大区额度总览】request:{}",request);
        Page<AreaQuotaVo> page = quotaPurseService.selectAreaQuota(request);
        return Response.success(page);
    }

    @Override
    public void areaQuotaExport(OrgQuotaRequest request) {
        quotaPurseService.areaQuotaExport(request);
    }

    @Override
    public Response<Page<CompanyQuotaVo>> selectCompanyQuota(OrgQuotaRequest request) {
        log.info("【分公司额度总览】request:{}",request);
        Page<CompanyQuotaVo> page = quotaPurseService.selectCompanyQuota(request);
        return Response.success(page);
    }

    @Override
    public void companyQuotaExport(OrgQuotaRequest request) {
        quotaPurseService.companyQuotaExport(request);
    }

    @Override
    public Response<Page<CeoQuotaVo>> selectBusinessQuota(BusinessQuotaRequest request) {
        log.info("【业务别总览】request:{}",request);
        Page<CeoQuotaVo> page = quotaPurseService.selectBusinessQuota(request);
        return Response.success(page);
    }

    @Override
    public void businessQuotaExport(BusinessQuotaRequest request) {
        quotaPurseService.businessQuotaExport(request);
    }


    @Override
    public Response<PursePermissionVo> getPermission(String organizationId,String person) {
        log.info("【额度权限获取】organizationId:{},person:{}",organizationId,person);
        PursePermissionVo vo = quotaPurseService.getPermission(organizationId,person);
        return Response.success(vo);
    }

    @Override
    public Response<Page<QuotaCollectionVo>> selectQuota(OrgQuotaRequest request) {
        log.info("【新大区额度总览】request:{}",request);
        Page<QuotaCollectionVo> page = quotaPurseService.selectQuota(request);
        return Response.success(page);
    }

    @Override
    public void selectQuotaExport(OrgQuotaRequest request, HttpServletResponse response) {
        log.info("【新大区额度总览导出】request:{}",request);
        quotaPurseService.selectQuotaExport(request,response);
    }

}
