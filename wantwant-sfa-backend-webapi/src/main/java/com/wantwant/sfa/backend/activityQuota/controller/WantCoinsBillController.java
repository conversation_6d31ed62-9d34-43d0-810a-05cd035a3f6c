package com.wantwant.sfa.backend.activityQuota.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.api.WantCoinsBillApi;
import com.wantwant.sfa.backend.activityQuota.request.CeoBillDetailRequest;
import com.wantwant.sfa.backend.activityQuota.request.WantCoinsApplyTypeBillRequest;
import com.wantwant.sfa.backend.activityQuota.request.WantCoinsBillRequest;
import com.wantwant.sfa.backend.activityQuota.request.WantCoinsDetailRequest;
import com.wantwant.sfa.backend.activityQuota.service.ICeoQuotaService;
import com.wantwant.sfa.backend.activityQuota.service.IWantCoinsBillService;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/14/上午10:10
 */
@RestController
@Slf4j
public class WantCoinsBillController implements WantCoinsBillApi {
    @Autowired
    private IWantCoinsBillService wantCoinsBillService;
    @Autowired
    private ICeoQuotaService ceoQuotaService;

    @Override
    public Response<List<WantCoinsYearBillVo>> getYearBill(String organizationId, String deptCode) {

        log.info("【get year bill】organizationId:{},deptCode:{}",organizationId,deptCode);

        List<WantCoinsYearBillVo> list = wantCoinsBillService.getYearBill(organizationId,deptCode);

        return Response.success(list);
    }

    @Override
    public Response<List<WantCoinsCeoYearBillVo>> getCeoBill(String organizationId) {
        log.info("【get ceo year bill】organizationId:{}",organizationId);

        List<WantCoinsCeoYearBillVo> ceoBill = wantCoinsBillService.getCeoBill(organizationId);

        return Response.success(ceoBill);
    }


    @Override
    public Response<List<WantCoinsRatioVo>> getCoinsRatio(int type, String year, String month, String organizationId, String deptCode) {

        log.info("【get coins ratio】type:{},year:{},month:{},organizationId:{},deptCode:{}",type,year,month,organizationId,deptCode);

        List<WantCoinsRatioVo> list = wantCoinsBillService.getCoinsRation(type,year,month,organizationId,deptCode);

        return Response.success(list);
    }

    @Override
    public Response<List<WantCoinsRatioVo>> getCeoCoinsRatio(int type, String year, String month, String organizationId, String deptCode) {

        log.info("【get ceo coins ratio】type:{},year:{},month:{},organizationId:{},deptCode:{}",type,year,month,organizationId,deptCode);

        List<WantCoinsRatioVo> list = ceoQuotaService.getCeoCoinsRatio( type, year,  month, organizationId, deptCode);

        return Response.success(list);
    }

    @Override
    public Response<WantCoinsDetailWrapsVO> getWantCoinsDetail(WantCoinsBillRequest wantCoinsBillRequest) {

        log.info("【get want coins detail】request:{}",wantCoinsBillRequest);

        WantCoinsDetailWrapsVO vo= wantCoinsBillService.getWantCoinsDetail(wantCoinsBillRequest);

        return Response.success(vo);
    }

    @Override
    public Response<WantCoinsCeoDetailWrapsVo> getWantCoinsCeoDetail(WantCoinsDetailRequest wantCoinsDetailRequest) {
        log.info("【get want coins ceo detail】request:{}",wantCoinsDetailRequest);

        WantCoinsCeoDetailWrapsVo vo = ceoQuotaService.getWantCoinsCeoDetail(wantCoinsDetailRequest);
        return Response.success(vo);
    }

    @Override
    public void exportWantCoinsDetail(WantCoinsBillRequest wantCoinsBillRequest) {
        log.info("【export want coins detail】request:{}",wantCoinsBillRequest);

        wantCoinsBillService.exportWantCoinsDetail(wantCoinsBillRequest);
    }

    @Override
    public Response<Page<WantCoinsCoinsTypeBillVo>> getWantCoinsCoinsTypeBill(WantCoinsApplyTypeBillRequest request) {
        log.info("【get want coins type bill】request:{}",request);

        Page<WantCoinsCoinsTypeBillVo> page = wantCoinsBillService.getWantCoinsCoinsTypeBill(request);

        return Response.success(page);
    }

    @Override
    public Response<List<WantCoinsCoinsTypeCeoBillVo>> getWantCoinsCoinsTypeCeoBill(WantCoinsApplyTypeBillRequest request) {
        log.info("【want coins type ceo bill】request{}",request);

        List<WantCoinsCoinsTypeCeoBillVo> list = wantCoinsBillService.getWantCoinsCoinsTypeCeoBill(request);

        return Response.success(list);
    }

    @Override
    public void exportWantCoinsCeoDetail(WantCoinsDetailRequest wantCoinsDetailRequest) {
        log.info("【export want coins ceo detail】request:{}",wantCoinsDetailRequest);

        ceoQuotaService.exportWantCoinsCeoDetail(wantCoinsDetailRequest);
    }

    @Override
    public Response<Page<CeoBillDetailVo>> selectCeoBillDetail(CeoBillDetailRequest request) {
        log.info("【select ceo bill detail】request:{}",request);

        Page<CeoBillDetailVo> page = wantCoinsBillService.selectCeoBillDetail(request);
        return Response.success(page);
    }

    @Override
    public void exportCeoBillDetail(CeoBillDetailRequest request) {
        log.info("【export ceo bill detail】request:{}",request);
        wantCoinsBillService.exportCeoBillDetail(request);
    }


}
