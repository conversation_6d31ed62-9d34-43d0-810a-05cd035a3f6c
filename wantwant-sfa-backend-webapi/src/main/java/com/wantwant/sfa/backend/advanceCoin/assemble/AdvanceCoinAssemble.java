package com.wantwant.sfa.backend.advanceCoin.assemble;

import com.wantwant.sfa.backend.advanceCoin.request.AdvanceCoinApplicationRequest;
import com.wantwant.sfa.backend.advanceCoin.request.AuditCommand;
import com.wantwant.sfa.backend.domain.advanceCoin.DO.AdvanceAuditCommand;
import com.wantwant.sfa.backend.domain.advanceCoin.DO.AdvanceCoinDO;
import com.wantwant.sfa.backend.domain.advanceCoin.DO.value.Attachment;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrganizationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/10/上午8:49
 */
@Mapper(componentModel = "spring")
public interface AdvanceCoinAssemble {

    @Mapping(target = "attachmentList", resultType = Attachment.class)
    AdvanceCoinDO convertToAdVanceCoinDO(AdvanceCoinApplicationRequest request);


    AdvanceAuditCommand convertToAdvanceCommand(AuditCommand auditCommand);
}
