package com.wantwant.sfa.backend.advanceCoin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.advanceCoin.api.AdvanceCoinApi;
import com.wantwant.sfa.backend.advanceCoin.assemble.AdvanceCoinAssemble;
import com.wantwant.sfa.backend.advanceCoin.request.*;
import com.wantwant.sfa.backend.advanceCoin.vo.*;
import com.wantwant.sfa.backend.application.AdvanceCoinApplication;
import com.wantwant.sfa.backend.domain.emp.DO.EmpDetailDO;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.domain.wallet.service.IWalletDomainService;
import com.wantwant.sfa.backend.wallet.vo.OrganizationQuotaVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/09/下午4:11
 */
@RestController
@Slf4j
public class AdvanceCoinController implements AdvanceCoinApi {
    @Resource
    private AdvanceCoinApplication advanceCoinApplication;
    @Resource
    private AdvanceCoinAssemble advanceCoinAssemble;
    @Resource
    private IEmpService empService;

    @Override
    public Response apply(@Valid AdvanceCoinApplicationRequest advanceCoinApplicationRequest) {

        log.info("【advance coin apply】request:{}", JSONObject.toJSONString(advanceCoinApplicationRequest));

        advanceCoinApplication.apply(advanceCoinAssemble.convertToAdVanceCoinDO(advanceCoinApplicationRequest),advanceCoinApplicationRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response<List<RepaymentScheduleVO>> selectRepaymentSchedule(@Valid RepaymentQuery repaymentQuery) {
        log.info("【select repayment schedule】query:{}",JSONObject.toJSONString(repaymentQuery));
        List<RepaymentScheduleVO> list = advanceCoinApplication.selectRepaymentSchedule(repaymentQuery);
        return Response.success(list);
    }

    @Override
    public Response<IPage<AdvanceCoinApplyVO>> selectApply(@Valid AdvanceCoinQuery advanceCoinQuery) {
        log.info("【select apply list】query:{}",JSONObject.toJSONString(advanceCoinQuery));
        IPage<AdvanceCoinApplyVO> page = advanceCoinApplication.selectApply(advanceCoinQuery);
        return Response.success(page);
    }

    @Override
    public void exportApply(@Valid AdvanceCoinQuery advanceCoinQuery) {
        log.info("【export apply list】query:{}",JSONObject.toJSONString(advanceCoinQuery));
        advanceCoinApplication.exportApply(advanceCoinQuery);
    }

    @Override
    public Response<AdvanceCoinDetailVO> getAdvanceCoinDetail(Long applyId,String person) {
        log.info("【get advance coin detail】applyId:{}",applyId);

        AdvanceCoinDetailVO advanceCoinDetailVO = advanceCoinApplication.getAdvanceCoinDetail(applyId,person);
        if(Objects.isNull(advanceCoinDetailVO)){
            Response.success(advanceCoinDetailVO);
        }

        return Response.success(advanceCoinDetailVO);
    }

    @Override
    public Response audit(@Valid AuditCommand auditCommand) {
        log.info("【advance apply audit】request:{}",JSONObject.toJSONString(auditCommand));

        advanceCoinApplication.audit(advanceCoinAssemble.convertToAdvanceCommand(auditCommand),auditCommand.getPerson());

        return Response.success();
    }

    @Override
    public Response<IPage<AdvancePaymentVO>> paymentList(@Valid PaymentQuery paymentQuery) {
        log.info("【advance payment search】request:{}",JSONObject.toJSONString(paymentQuery));

        IPage<AdvancePaymentVO> page = advanceCoinApplication.paymentList(paymentQuery);

        return Response.success(page);
    }

    @Override
    public void exportPayment(@Valid PaymentQuery paymentQuery) {
        log.info("【export payment】query:{}",JSONObject.toJSONString(paymentQuery));
        advanceCoinApplication.exportPayment(paymentQuery);
    }

    @Override
    public Response<AdvancePaymentDetailVO> getPayment(Long paymentId) {
        log.info("【get payment】paymentId:{}",paymentId);

        AdvancePaymentDetailVO advancePaymentDetailVO = advanceCoinApplication.getPayment(paymentId);

        // 申请人信息
        EmpDetailDO empDetailDO = empService.selectEmpDetailById(advancePaymentDetailVO.getEmployeeInfoId());
        if(Objects.nonNull(empDetailDO)){
            advancePaymentDetailVO.setMemberKey(empDetailDO.getMemberKey());
            advancePaymentDetailVO.setApplyUserName(empDetailDO.getEmployeeName());
            advancePaymentDetailVO.setEmployeeStatus(empDetailDO.getEmployeeStatus());
            LocalDate onBoardDate = empDetailDO.getOnBoardDate();
            if(Objects.nonNull(onBoardDate)){
                advancePaymentDetailVO.setOnBoardDate(onBoardDate.toString());
            }
            advancePaymentDetailVO.setWorkDate(empDetailDO.getOnboardDays());
            advancePaymentDetailVO.setAvatar(empDetailDO.getAvatar());
            advancePaymentDetailVO.setPosition(empDetailDO.getPositionName());
        }
        return Response.success(advancePaymentDetailVO);
    }

    @Override
    public Response<List<AuditTraceVO>> getAuditTrace(Long applyId) {
        log.info("【get audit trace】applyId:{}",applyId);

        List<AuditTraceVO> list = advanceCoinApplication.getAuditTrace(applyId);

        return Response.success(list);
    }
}
