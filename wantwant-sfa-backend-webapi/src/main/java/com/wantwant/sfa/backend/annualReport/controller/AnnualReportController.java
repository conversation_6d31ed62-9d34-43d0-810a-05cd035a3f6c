package com.wantwant.sfa.backend.annualReport.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.annualReport.api.AnnualReportApi;
import com.wantwant.sfa.backend.annualReport.dto.AnnualReportInfoDto;
import com.wantwant.sfa.backend.annualReport.req.AddAnnualReportBurySpotReq;
import com.wantwant.sfa.backend.annualReport.req.AnnualReportInfoReq;
import com.wantwant.sfa.backend.annualReport.service.IAnnualReportService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.annualReport.controller
 * @Description:
 * @Date: 2025/1/14 13:51
 */
@RestController
public class AnnualReportController implements AnnualReportApi {
    @Resource
    private IAnnualReportService annualReportService;
    @Override
    public Response<AnnualReportInfoDto> queryAnnualReportInfo(AnnualReportInfoReq req) {
        return Response.success(annualReportService.queryAnnualReportInfo(req));
    }

    @Override
    public Response addAnnualReportBurySpot(AddAnnualReportBurySpotReq req) {
        annualReportService.addAnnualReportBurySpot(req);
        return Response.success();
    }
}
