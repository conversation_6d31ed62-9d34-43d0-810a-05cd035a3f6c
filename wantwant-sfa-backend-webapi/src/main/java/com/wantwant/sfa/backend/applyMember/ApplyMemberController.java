package com.wantwant.sfa.backend.applyMember;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.applyMember.dto.*;
import com.wantwant.sfa.backend.applyMember.request.ApplyRevertRequest;
import com.wantwant.sfa.backend.applyMember.request.RealAuthRequest;
import com.wantwant.sfa.backend.applyMember.vo.*;
import com.wantwant.sfa.backend.interview.process.OpenAccountProcess;
import com.wantwant.sfa.backend.interview.request.RecruitmentSourceRequest;
import com.wantwant.sfa.backend.realName.model.RealNameModel;
import com.wantwant.sfa.backend.service.ApplyMemberService;
import com.wantwant.sfa.backend.util.AliOSSServiceUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.swing.text.Position;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 造旺报名接口相关
 *
 * @date 2021-11-04 14:32
 * @version 1.0
 */
@Api(tags = "造旺报名接口相关")
@RestController
@RequestMapping("/apply")
@Slf4j
public class ApplyMemberController {

    private static final String INDUCTION_LOCK = "induction:lock";
    @Autowired
    private ApplyMemberService applyMemberService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private OpenAccountProcess openAccountProcess;

    @Resource
    private AliOSSServiceUtils aliOSSServiceUtils;

    @PostMapping("/revert")
    @ApiOperation(value = "撤回报名", notes = "撤回报名")
    public Response revert(@RequestBody ApplyRevertRequest request) {

        applyMemberService.revert(request.getMobile());

        return Response.success();
    }

    @PostMapping("/selectPosition")
    @ApiOperation(value = "获取可报名岗位", notes = "获取可报名岗位")
    public Response<List<PositionVO>> selectPosition(@RequestBody @Valid PositionSearchRequest positionSearchRequest){
        List<PositionVO> list = applyMemberService.selectPosition(positionSearchRequest);
        return Response.success(list);
    }

    /**
    * 简历上传
    *
    * @param request
    * @return: com.wantwant.commons.web.response.Response
    * @date: 2021-11-05 14:37
    */
    @PostMapping("/uploadFile")
    @ApiOperation(value = "简历上传", notes = "上传文件")
    public Response<String> uploadFile(MultipartHttpServletRequest request) {
        return Response.success(aliOSSServiceUtils.uploadFile(request));
    }

    /**
    * 添加头像
    *
    * @param request
    * @return: com.wantwant.commons.web.response.Response
    * @date: 2021-12-10 15:15
    */
    @ApiOperation(value = "添加头像", notes = "添加头像")
    @PostMapping("/addAvatar")
    public Response<Integer> addAvatar(@RequestBody @Valid List<AvatarRequest> request) {
        return applyMemberService.addAvatar(request);
    }

    /**
    * 报名接口
    *
    * @param request 保存参数
    * @return: com.wantwant.commons.web.response.Response
    * @date: 2021-11-04 16:24
    */
    @PostMapping("/signUp")
    @ApiOperation(value = "报名", notes = "报名")
    public Response signUp(@RequestBody @Valid ApplyMemberRequest request) {
        log.info("【造旺报名】request:{}", request);
        return applyMemberService.signUp(request);
    }

    @PostMapping("/signup/pageInfo")
    @ApiOperation(value = "报名", notes = "报名")
    Response<Long> signupPageScanInfoCommit(@Validated @RequestBody SignupPageInfo request) {
        log.info("【造旺报名】signupPageScanInfoCommit:{}", request);
        return Response.success(applyMemberService.commitSignupPageInfo(request));
    }


    /**
     * 造旺报名三级地
     *
     * @param
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.applyMember.vo.ApplyRegionTreeVO>
     * @date: 3/1/22 11:21 AM
     */
    @GetMapping("/getRegionTree")
    @ApiOperation(value = "造旺报名三级地", notes = "造旺报名三级地")
    public Response<List<ApplyRegionTreeVO>> getRegionTree(){
        return Response.success(applyMemberService.getRegionTree());
    }

    @GetMapping("/getRegionTreeForId")
    @ApiOperation(value = "印尼报名三级地", notes = "印尼报名三级地")
    public Response<List<ApplyRegionTreeVO>> getRegionTreeForId() {
        return Response.success(applyMemberService.getRegionTreeForId());
    }
    /**
     * 清空造旺报名三级地
     *
     * @param
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.applyMember.vo.ApplyRegionTreeVO>>
     * @date: 3/1/22 4:26 PM
     */
    @PutMapping("/clearRegionTree")
    @ApiOperation(value = "清空造旺报名三级地", notes = "清空造旺报名三级地")
    public Response<List<ApplyRegionTreeVO>> clearRegionTree(){
        applyMemberService.clearRegionTree();
        return Response.success();
    }

    @PutMapping("/clearRegionTreeForId")
    @ApiOperation(value = "清空印尼报名三级地", notes = "清空印尼报名三级地")
    public void clearRegionTreeForId() {
        applyMemberService.clearRegionTreeForId();
    }
    /**
    * 分页查询入职人员
    *
    * @param queryDto
    * @return:
    *     com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.wantwant.sfa.backend.applyMember.vo.ApplyMemberVO>
    * @date: 2021-11-06 11:04
    */
    @ApiOperation(notes = "分页查询入职人员", value = "分页查询入职人员")
    @PostMapping("/queryMemberPage")
    public Page<ApplyMemberVO> queryMemberPage(@RequestBody ApplyMemberQueryDTO queryDto) {
        return applyMemberService.queryMemberPage(queryDto);
    }

    /**
     * 入职
     *
     * @param dto
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 2021-11-08 15:12
     */
    @ApiOperation(notes = "入职", value = "入职")
    @PutMapping("/induction")
    public Response induction(@Valid @RequestBody InductionInfoDTO dto) {
        log.info("ApplyMemberController induction: {}",dto);
        if(!redisUtil.setLockIfAbsent(INDUCTION_LOCK,dto.getMobileNumber(),20, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }

        try{
            applyMemberService.induction(dto);
        }finally {
            redisUtil.unLock(INDUCTION_LOCK,dto.getMobileNumber());
        }

        return Response.success();
    }

    /**
     * 未通过
     *
     * @param processId
     * @param dto
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 2021-11-08 15:12
     */
    @ApiOperation(notes = "未通过", value = "未通过")
    @PutMapping("/cancel/{processId}")
    public Response<Integer> cancel(@ApiParam(value = "流程ID", required = true)@PathVariable("processId")@NotNull(message = "id不能为空") Integer processId,
                                    @Valid @RequestBody CancelInfoDTO dto) {
        return Response.success(applyMemberService.cancel(processId, dto));
    }

    /**
     * 入职导入模版下载
     *
     * @param request
     * @param response
     * @date: 2021-11-20 20:58
     */
    @ApiOperation(notes = "入职导入模版下载", value = "入职导入模版下载")
    @GetMapping("/importMemberTemplate")
    public void importMemberTemplate(HttpServletRequest request, HttpServletResponse response) {
        RecruitsImportDTO dto = new RecruitsImportDTO(1, "示例模版", "15316219988", "2021-12-20", "2021-11-20", "旺旺", "00440000", 0, "");
        List<RecruitsImportDTO> list = new ArrayList<>();
        list.add(dto);
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), RecruitsImportDTO.class, list);
        try {
            String fileName = "入职导入模版下载";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            fileName = new String(fileName.getBytes("UTF-8"));
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader(
                    "Content-disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.close();
            wb.close();
        } catch (IOException e) {
            throw new ApplicationException("导出失败，请稍后重试！");
        }
    }


    /**
     * 根据流程id批处理入职
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 2021-11-08 16:32
     */
    @ApiOperation(notes = "入职批处理", value = "入职批处理")
    @PutMapping("/batch")
    public Response<List<String>> batch(@Valid @RequestBody ApplyMemberBatchRequest request) {
        return Response.success(applyMemberService.batch(request));
    }

    /** ————————离职———————— */

    /**
     * 分页查询离职人员
     *
     * @param queryDto
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.wantwant.sfa.backend.applyMember.vo.ApplyResignVO>
     * @date: 2021-11-20 11:29
     */
    @ApiOperation(notes = "分页查询离职人员", value = "分页查询离职人员")
    @PostMapping("/queryResignPage")
    public Page<ApplyResignVO> queryResignPage(@RequestBody ApplyMemberQueryDTO queryDto) {
        return applyMemberService.queryResignPage(queryDto);
    }

    /**
     * 分页查询已离职人员
     *
     * @param resignEmpSearchDTO
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.wantwant.sfa.backend.applyMember.vo.ApplyResignVO>
     * @date: 2021-11-20 11:29
     */
    @ApiOperation(notes = "分页查询离已职人员", value = "分页查询已离职人员")
    @PostMapping("/resignEmpList")
    public Page<ResignEmpVO> resignEmpList(@RequestBody ResignEmpSearchDTO resignEmpSearchDTO) {
        return applyMemberService.resignEmpList(resignEmpSearchDTO);
    }

    /**
     * 离职
     *
     * @param id
     * @param dto
     * @return: com.wantwant.commons.web.response.Response
     * @date: 2021-11-20 17:02
     */
    @ApiOperation(notes = "离职",value = "离职")
    @PutMapping("/resign/{id}")
    public Response resign(@ApiParam(value = "离职流程ID",required = true) @PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                           @Valid @RequestBody ResignInfoDTO dto){
        applyMemberService.resign(id, dto);
        return Response.success();
    }

    /**
     * 导入离职信息
     * 返回失败信息
     *
     * @param file
     * @param processUserId
     * @return: com.wantwant.commons.web.response.Response<java.util.List<java.lang.String>>
     * @date: 2021-11-22 10:46
     */
    @ApiOperation(notes = "导入离职信息",value = "导入离职信息")
    @PostMapping("/importResign")
    public Response<List<String>> importResign(@RequestParam(value = "file") MultipartFile file,
                                               @ApiParam(value = "操作人ID",required = true) @RequestParam(value = "processUserId") String processUserId){
        return Response.success(applyMemberService.importResign(file,processUserId));
    }

    /**
     * 根据流程id批处理离职
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 2021-11-22 14:21
     */
    @ApiOperation(notes = "离职批处理", value = "离职批处理")
    @PutMapping("/batchResign")
    public Response<List<String>> batchResign(@Valid @RequestBody ApplyMemberBatchRequest request) {
        return Response.success(applyMemberService.batchResign(request));
    }

    /**
     * 离职导入模版下载
     *
     * @param request
     * @param response
     * @date: 2021-11-20 20:58
     */
    @ApiOperation(notes = "离职导入模版下载",value = "离职导入模版下载")
    @GetMapping("/importResignTemplate")
    public void importResignTemplate(HttpServletRequest request, HttpServletResponse response){
        ResignImportDTO dto = new ResignImportDTO(0,"00440000","示例模版","15316219988","2021-11-20");
        List<ResignImportDTO> list = new ArrayList<>();
        list.add(dto);
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), ResignImportDTO.class, list);
        try {
            String fileName = "离职导入模版下载";
            if(wb instanceof HSSFWorkbook){
                fileName=fileName+".xls";
            }else{
                fileName=fileName+".xlsx";
            }
            fileName = new String(fileName.getBytes("UTF-8"));
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.close();
            wb.close();
        } catch (IOException e) {
            throw new ApplicationException("导出失败，请稍后重试！");
        }
    }

    /**
     * 离职审核
     *
     * @param queryDto
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.wantwant.sfa.backend.applyMember.vo.ApplyResignVO>
     * @date: 2021-11-20 11:29
     */
    @ApiOperation(notes = "在职人员列表", value = "在职人员列表,可操作离职")
    @PostMapping("/queryResignApproval")
    public Page<ApplyApprovalVO> queryResignApproval(@RequestBody ResignApprovalQueryDTO queryDto) {
        return applyMemberService.queryResignApproval(queryDto);
    }



    /**
     * 公司岗位关键字提示
     *
     * @param key
     * @param type
     * @return: com.wantwant.commons.web.response.Response<java.util.List<java.lang.String>>
     * @date: 2021-12-23 21:55
     */
    @ApiOperation(notes = "公司岗位关键字提示",value = "公司岗位关键字提示")
    @GetMapping("/keywordTips")
    public Response<List<String>> keywordTips(@ApiParam(value = "key") @RequestParam(value = "key", required = false) String key,
                                              @ApiParam(value = "类型(1:公司,2:岗位)") @RequestParam(value = "type") Integer type){
        return Response.success(applyMemberService.keywordTips(key,type));
    }

    @ApiOperation(notes = "根据手机号查询报名信息", value = "根据手机号查询报名信息")
    @GetMapping("/info")
    public Response<ApplyMemberIDCardInfoVO> employeeInfo(@ApiParam(value = "mobile") @RequestParam(value = "mobile") String mobile) {
        log.info("employeeInfo: {}", mobile);
        return Response.success(applyMemberService.queryApplyInfo(mobile));
    }

    /**
     * 更新报名时，录入的图片信息
     *
     * @param request 身份证相关图片
     * @date: 2021-11-22 10:46
     */
    @ApiOperation(notes = "导入离职信息", value = "导入离职信息")
    @PostMapping("/updateIDCardPic")
    public Response<Boolean> updateEmployeeIDCardPicture(@RequestBody ApplyMemberIDCardInfoRequest request) {
        log.info("updateEmployeeIDCardPicture: {}", request);
        return Response.success(applyMemberService.updateIDCardPicture(request));
    }


    @ApiOperation(notes = "实名信息认证", value = "实名信息认证")
    @PostMapping("/realAuth")
    public Response realAuth(@Validated @RequestBody RealAuthRequest realAuthRequest){
        log.info("【real auth】request:{}",realAuthRequest);
        applyMemberService.realAuth(realAuthRequest);
        return Response.success();
    }

    @ApiOperation(notes = "管理岗实名信息认证", value = "管理岗实名信息认证")
    @PostMapping("/managerRealAuth")
    public Response managerRealAuth(@Validated @RequestBody RealNameModel model){
        log.info("【realname manager auth】request:{}",model);
        return applyMemberService.managerRealAuth(model);
    }

    @ApiOperation(notes = "修改广告商", value = "修改广告商")
    @PostMapping("/modifyRecruitmentSource")
    public Response modifyRecruitmentSource(@Validated @RequestBody RecruitmentSourceRequest request){
        log.info("【modify recruitment source】request:{}",request);
        applyMemberService.modifyRecruitmentSource(request);
        return Response.success();
    }

    /**
     * 同步数据
     *
     * @param memberKey
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 2021-11-08 15:12
     */
    @ApiOperation(notes = "同步数据", value = "同步数据")
    @PutMapping("/syncOrgAndPersonInfo/{memberKey}")
    public Response<Integer> syncOrgAndPersonInfo(@ApiParam(value = "人员memberKey", required = true)@PathVariable("memberKey")@NotNull(message = "memberKey不能为空") Long memberKey) {
        openAccountProcess.syncMemberInfoToWP(memberKey);
        return Response.success();
    }
}
