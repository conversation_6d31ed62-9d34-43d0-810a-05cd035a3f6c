package com.wantwant.sfa.backend.arch.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.AccountApi;
import com.wantwant.sfa.backend.arch.request.*;
import com.wantwant.sfa.backend.arch.service.IAccountService;
import com.wantwant.sfa.backend.arch.vo.AccountInfoVo;
import com.wantwant.sfa.backend.arch.vo.AccountVo;
import com.wantwant.sfa.backend.arch.vo.QuitTaskInfoVo;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/22/下午7:06
 */
@RestController
@Slf4j
public class AccountController implements AccountApi {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private ICheckCustomerService iCheckCustomerService;

    @Override
    public Response<Page<AccountVo>> selectList(SAccountRequest request) {
        Page<AccountVo> page =  accountService.selectList(request);

        return Response.success(page);
    }

    @Override
    public Response create(CAccountRequest request) {

        accountService.create(request);

        return Response.success();
    }

    @Override
    public Response<QuitTaskInfoVo> quitTaskInfo(String employeeId) {
        log.info("【quit task info】employeeId: {}",employeeId);
        QuitTaskInfoVo taskInfo = accountService.quitTaskInfo(employeeId);
        return Response.success(taskInfo);
    }

    @Override
    public Response quit(@Valid AccountQuitRequest request) {
        log.info("【account quit】request:{}",request);

        accountService.quit(request);

        return Response.success();
    }

    @Override
    public Response edit(CAccountRequest request) {

        accountService.edit(request);

        return Response.success();
    }

    @Override
    public Response<AccountInfoVo> getAccountInfo(String employeeId,String positionId) {

        AccountInfoVo accountInfoVo = accountService.getAccountInfo(employeeId,positionId);

        return Response.success(accountInfoVo);
    }

    @Override
    public Response<AccountInfoVo> getAccountInfoById(String employeeId) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = iCheckCustomerService.getPersonInfo(employeeId, loginInfo);
        AccountInfoVo accountInfoVo = accountService.getAccountInfo(employeeId,personInfo.getPositionId());
        return Response.success(accountInfoVo);
    }

    @Override
    public Response addRoles(ModifyRolesRequest modifyRolesRequest) {
        log.info("【add roles】request:{}",modifyRolesRequest);
        accountService.addRoles(modifyRolesRequest);
        return Response.success();
    }

    @Override
    public Response deleteRoles(ModifyRolesRequest modifyRolesRequest) {
        log.info("【delete roles】request:{}",modifyRolesRequest);
        accountService.deleteRoles(modifyRolesRequest);
        return Response.success();
    }

    @Override
    public Response copyRoles(@Valid CopyRolesRequest request) {
        log.info("【copy roles】request:{}",request);
        accountService.copyRoles(request);
        return Response.success();
    }
}
