package com.wantwant.sfa.backend.arch.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.ArchApi;
import com.wantwant.sfa.backend.arch.service.IArchService;
import com.wantwant.sfa.backend.arch.vo.ArchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/15/下午12:02
 */
@RestController
@Slf4j
public class ArchController implements ArchApi {

    @Autowired
    private IArchService archService;

    @Override
    public Response<ArchVo> getArch(String deptCode) {
        log.info("【get arch】deptCode:{}",deptCode);

        ArchVo vo = archService.getArch(deptCode);

        return Response.success(vo);
    }
}
