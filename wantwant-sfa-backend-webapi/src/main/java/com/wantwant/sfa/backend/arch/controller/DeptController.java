package com.wantwant.sfa.backend.arch.controller;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.DeptApi;
import com.wantwant.sfa.backend.arch.request.CDeptRequest;
import com.wantwant.sfa.backend.arch.request.DeptOperatorRequest;
import com.wantwant.sfa.backend.arch.request.EDeptRequest;
import com.wantwant.sfa.backend.arch.request.SDeptRequest;
import com.wantwant.sfa.backend.arch.service.IDeptService;
import com.wantwant.sfa.backend.arch.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午4:19
 */
@RestController
@Slf4j
public class DeptController implements DeptApi {
    @Autowired
    private IDeptService deptService;

    @Override
    public Response createDept(CDeptRequest cDeptRequest) {
        log.info("【create dept】request:{}",cDeptRequest);

        deptService.createDept(cDeptRequest);

        return Response.success();
    }

    @Override
    public Response editDept(EDeptRequest eDeptRequest) {
        log.info("【update dept】request:{}",eDeptRequest);

        deptService.update(eDeptRequest);

        return Response.success();
    }

    @Override
    public Response delete(@Valid DeptOperatorRequest deptOperatorRequest) {
        log.info("【delete dept】request:{}",deptOperatorRequest);

        deptService.delete(deptOperatorRequest);

        return Response.success();
    }

    @Override
    public Response<DeptInfoVo> getDeptInfo(int id) {
        log.info("【get deptInfo 】id:{}",id);

        DeptInfoVo deptInfo = deptService.getDeptInfo(id);
        return Response.success(deptInfo);
    }

    @Override
    public Response<List<DeptVo>> selectDeptList(SDeptRequest request) {
        List<DeptVo> list = deptService.selectDeptList(request);
        return Response.success(list);
    }

    @Override
    public Response<List<DeptSelectVo>> departSelect(Integer deptId) {
        List<DeptSelectVo> list = deptService.departSelect(deptId);
        return Response.success(list);
    }

    @Override
    public Response<List<DeptSelectVo>> departSelectByEmpId(String empId) {
        List<DeptSelectVo> list = deptService.departSelectByEmpId(empId);
        return Response.success(list);
    }

    @Override
    public Response<List<UserVo>> getUserListByDeptCode(String deptCode) {
        log.info("【get User List by dept code】deptCode:{}",deptCode);

        List<UserVo> list = deptService.getUserListByDeptCode(deptCode);
        return Response.success(list);
    }

    @Override
    public Response<List<DeptPositionVo>> getPositionListByDeptId(Integer deptId) {
        log.info("getPositionListByDeptId: {}", deptId);
        return Response.success(deptService.getPositionListByDeptId(deptId));
    }
}
