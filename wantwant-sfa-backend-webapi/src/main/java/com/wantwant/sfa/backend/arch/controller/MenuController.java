package com.wantwant.sfa.backend.arch.controller;

import com.google.gson.JsonObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.MenuApi;
import com.wantwant.sfa.backend.arch.request.CustomMenuRequest;
import com.wantwant.sfa.backend.arch.service.IMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/23/下午4:03
 */
@RestController
@Slf4j
public class MenuController implements MenuApi {
    @Autowired
    private IMenuService menuService;

    @Override
    public Response<Object> getMenu(String person) {
        Object menu = menuService.getMenu(person);
        return Response.success(menu);
    }


    @Override
    public Response customMenu(@Valid CustomMenuRequest customMenuRequest) {
        log.info("【custom menu】request:{}",customMenuRequest);

        menuService.customMenu(customMenuRequest);

        return Response.success();
    }

    @Override
    public Response<Boolean> checkGuidanceFinish(String empId) {
        Boolean flag = menuService.checkGuidanceFinish(empId);
        return Response.success(flag);
    }

    @Override
    public Response finishGuide(String empId) {

        menuService.finishGuide(empId);

        return Response.success();
    }
}
