package com.wantwant.sfa.backend.arch.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.PositionApi;
import com.wantwant.sfa.backend.arch.request.CPositionRequest;
import com.wantwant.sfa.backend.arch.request.PositionOperatorRequest;
import com.wantwant.sfa.backend.arch.request.PositionSearchRequest;
import com.wantwant.sfa.backend.arch.request.UPositionRequest;
import com.wantwant.sfa.backend.arch.service.IPositionService;
import com.wantwant.sfa.backend.arch.vo.PositionDetailVo;
import com.wantwant.sfa.backend.arch.vo.PositionSelectVo;
import com.wantwant.sfa.backend.arch.vo.PositionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/08/下午6:53
 */
@RestController
@Slf4j
public class PositionController implements PositionApi {
    @Autowired
    private IPositionService positionService;

    @Override
    public Response create(@Valid CPositionRequest positionRequest) {
        log.info("【position create】request:{}",positionRequest);

        positionService.create(positionRequest);

        return Response.success();
    }

    @Override
    public Response update(@Valid UPositionRequest positionRequest) {
        log.info("【position update】request:{}",positionRequest);


        positionService.modify(positionRequest);

        return Response.success();
    }

    @Override
    public Response<PositionDetailVo> detail(Long positionId) {
        return Response.success(positionService.detail(positionId));
    }

    @Override
    public Response<List<PositionVo>> selectList(PositionSearchRequest positionSearchRequest) {
        log.info("【position search】request:{}",positionSearchRequest);

        List<PositionVo> list = positionService.selectList(positionSearchRequest);

        return Response.success(list);
    }

    @Override
    public Response<List<PositionSelectVo>> selector() {
        return Response.success(positionService.selector());
    }

    @Override
    public Response deletePosition(PositionOperatorRequest positionOperatorRequest) {
        log.info("【delete position】request:{}",positionOperatorRequest);

        positionService.deletePosition(positionOperatorRequest);

        return  Response.success();
    }
}
