package com.wantwant.sfa.backend.arch.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.ResourcesApi;
import com.wantwant.sfa.backend.arch.request.MenuModuleRequest;
import com.wantwant.sfa.backend.arch.service.IMenuService;
import com.wantwant.sfa.backend.arch.service.IRoleResourcesRelationService;
import com.wantwant.sfa.backend.arch.vo.ModuleVo;
import com.wantwant.sfa.backend.arch.vo.ResourcesVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/21/下午7:47
 */
@RestController
@Slf4j
public class ResourcesController implements ResourcesApi {
    @Autowired
    private IRoleResourcesRelationService roleResourcesRelationService;


    @Override
    public Response<List<ResourcesVo>> getResources(Integer terminal, Integer roleId) {

        List<ResourcesVo> list = roleResourcesRelationService.getResources(terminal,roleId);

        return Response.success(list);
    }

    @Override
    public Response<List<ModuleVo>> getMenuModule(@Valid MenuModuleRequest request) {
        log.info("【get menu module】request:{}",request);
        List<ModuleVo> list = roleResourcesRelationService.getMenuModule(request);
        return Response.success(list);
    }



    @Override
    public Response<Integer> getPromptCount(String person) {
        log.info("【get menu prompt count】person:{}",person);

        int count = roleResourcesRelationService.getPromptCount(person);
        return Response.success(count);
    }
}
