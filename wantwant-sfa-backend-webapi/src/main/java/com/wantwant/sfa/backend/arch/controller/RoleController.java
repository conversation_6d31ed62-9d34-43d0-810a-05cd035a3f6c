package com.wantwant.sfa.backend.arch.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.api.RoleApi;
import com.wantwant.sfa.backend.arch.request.CRoleRequest;
import com.wantwant.sfa.backend.arch.request.DRoleRequest;
import com.wantwant.sfa.backend.arch.request.ERoleRequest;
import com.wantwant.sfa.backend.arch.request.SRoleRequest;
import com.wantwant.sfa.backend.arch.service.IRoleService;
import com.wantwant.sfa.backend.arch.vo.EmployeeRoleVo;
import com.wantwant.sfa.backend.arch.vo.RoleInfoVo;
import com.wantwant.sfa.backend.arch.vo.RoleSelectVo;
import com.wantwant.sfa.backend.arch.vo.RoleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:10
 */
@RestController
@Slf4j
public class RoleController implements RoleApi {
    @Autowired
    private IRoleService roleService;

    @Override
    public Response create(CRoleRequest cRoleRequest) {
        log.info("【create role】request:{}",cRoleRequest);
        roleService.create(cRoleRequest);
        return Response.success();
    }

    @Override
    public Response edit(ERoleRequest eRoleRequest) {
        log.info("【edit role】request:{}",eRoleRequest);
        roleService.edit(eRoleRequest);
        return Response.success();
    }

    @Override
    public Response<List<RoleVo>> selectRoles(SRoleRequest request) {
        log.info("【select roles】request:{}",request);

        List<RoleVo> list = roleService.selectRoles(request);
        return Response.success(list);
    }

    @Override
    public Response<RoleInfoVo> roleDetails(Integer id) {
        log.info("【get role detail】 id:{}",id);

        RoleInfoVo roleInfoVo = roleService.getRoleDetail(id);
        return Response.success(roleInfoVo);
    }

    @Override
    public Response deleteRole(@Valid DRoleRequest request) {
        log.info("【delete role】request:{}",request);

        roleService.deleteRole(request);
        return Response.success();
    }

    @Override
    public Response<List<RoleSelectVo>> getRoleSelect(Integer terminal) {
        return Response.success(roleService.getRoleSelect(terminal));
    }

    @Override
    public Response<List<EmployeeRoleVo>> selectEmployeeRoles(String employeeId) {
        return Response.success(roleService.selectEmployeeRoles(employeeId));
    }
}
