package com.wantwant.sfa.backend.attendance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.attendance.api.AttendanceApi;
import com.wantwant.sfa.backend.attendance.request.*;
import com.wantwant.sfa.backend.attendance.service.IAttendanceService;
import com.wantwant.sfa.backend.attendance.vo.*;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.base.annotation.GlobalTimezone;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 考勤接口相关
 *
 * @date 2022-05-18 14:32
 * @version 1.0
 */
@Api(tags = "考勤列表")
@RestController
@Slf4j
public class AttendanceController implements AttendanceApi {

    private static final String ATTENDANCE_AUDIT_LOCK = "attendance:audit:lock";

    private static final String ATTENDANCE_AUDIT_CHECK = "attendance:audit:check";
    @Autowired
    private IAttendanceService attendanceService;

    @Autowired
    private RedisUtil redisUtil;


    @Override
    public Response<IPage<AttendanceListVo>> getAttendanceList(AttendanceListRequest request) {
        log.info("AttendanceController getAttendanceList :{}", request);
        if (RequestUtils.getChannel() != 3) {
            throw new ApplicationException("渠道不正确，请传入造旺渠道");
        }
        return Response.success(attendanceService.getAttendanceInfoList(request));
    }

    @Override
    public Response<AttendanceDetailVo> getAttendanceDetail(AttendanceDetailRequest request) {
        log.info("AttendanceController getAttendanceDetail :{}", request);
        if(request.getMemberKey() == null) {
            throw new ApplicationException("合伙人标识为空");
        }
        if(request.getAttendanceDate() == null) {
            throw new ApplicationException("考勤日期为空");
        }
        return Response.success(attendanceService.getAttendanceDetail(request));
    }

    @Override
    public Response attendanceAudit(AttendanceAuditRequest request) {
        log.info("attendanceAudit getAttendanceDetail :{}", request);
        if (request.getStatus() != 1 && request.getStatus() != 2) {
            throw new ApplicationException("稽核状态传入不正确");
        }
        if(!redisUtil.setLockIfAbsent(ATTENDANCE_AUDIT_LOCK,request.getMemberKey(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        try{
            attendanceService.attendanceAudit(request);
        }finally {
            redisUtil.unLock(ATTENDANCE_AUDIT_LOCK,request.getMemberKey());
        }
        return Response.success();
    }

    @Override
    public Response attendanceAuditCheck(AttendanceAuditCheckRequest request) {
        if(!redisUtil.setLockIfAbsent(ATTENDANCE_AUDIT_CHECK,request.getPerson(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        try{
            attendanceService.attendanceAuditCheck(request);
        }finally {
            redisUtil.unLock(ATTENDANCE_AUDIT_CHECK,request.getPerson());
        }
        return Response.success();
    }

    @Override
    public Response<AttendanceAuditCheckVo> attendanceAuditCheckJurisdiction(AttendanceCheckRequest request) {
        return  Response.success(attendanceService.attendanceAuditCheckJurisdiction(request));
    }

    @Override
    public Response<List<AttendanceWeeklyDetailVo>> attendanceWeeklyList(String startDate, String endDate, Integer employeeInfoId) {
        log.info("startDate:{} endDate: {}", startDate, endDate);
        return Response.success(attendanceService.attendanceWeeklyList(startDate, endDate,employeeInfoId));
    }

    @Override
    public Response<AttendanceVisitInfoVo> latestVisitInfo(Long attendanceId) {
        log.info("latestVisitInfo attendanceId:{}", attendanceId);
        return Response.success(attendanceService.getLatestVisitInfo(attendanceId));
    }

    @Override
    @GlobalTimezone
    public Response<IPage<AttendanceListV2Vo>> getAttendanceV2List(AttendanceListV2Request request) {
        log.info("getAttendanceV2List:{}", request);
        return Response.success(attendanceService.getAttendanceV2List(request));
    }

    @Override
    @GlobalTimezone
    public Response<AttendanceListV2Vo> getAttendanceV2Detail(Long attendanceId) {
        log.info("getAttendanceV2Detail:{}", attendanceId);
        return Response.success(attendanceService.getAttendanceV2Detail(attendanceId));
    }

    @Override
    public void getAttendanceV2ListExport(AttendanceListV2Request request, HttpServletResponse response) {
        log.info("getAttendanceV2ListExport:{}", request);
        attendanceService.exportAttendanceV2List(request, response);
    }

    @Override
    public Response attendanceInitTest() {
        attendanceService.attendanceInitTask(null);
        return Response.success();
    }

    @Override
    public Response batchCheck(List<AttendanceAuditCheckRequest> list) {
        if (CollectionUtils.isEmpty(list)||list.get(0)==null){
            return Response.error("数据不能为空");
        }
        if (list.size()>100){
            return Response.error("批量处理最大支持100条");
        }
        String person=list.get(0).getPerson();
        if(!redisUtil.setLockIfAbsent(ATTENDANCE_AUDIT_CHECK,person,15, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        try{
            attendanceService.batchAuditCheck(list);
        }finally {
            redisUtil.unLock(ATTENDANCE_AUDIT_CHECK,person);
        }
        return Response.success();
    }

    @Override
    public Response<List<AttendanceAuditCheckVo>> batchJurisdiction(List<AttendanceCheckRequest> list) {
        return  Response.success(attendanceService.batchJurisdiction(list));
    }


}
