package com.wantwant.sfa.backend.authorization.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.authorization.api.AuthorizeCustomerApi;
import com.wantwant.sfa.backend.authorization.request.*;
import com.wantwant.sfa.backend.authorization.service.IAuthorizeCustomerService;
import com.wantwant.sfa.backend.authorization.vo.AuthorizationProcessVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午4:22
 */
@RestController
@Slf4j
public class AuthorizeCustomerController implements AuthorizeCustomerApi {
    @Autowired
    private IAuthorizeCustomerService authorizationCustomerService;
    @Autowired
    private RedisUtil redisUtil;

    private static final String VERIFY_LOCK = "authorization:verify:lock";

    @Override
    public Response createProcess(CreateProcessRequest request) {
        log.info("【客户授权申请】request:{}",request);
        authorizationCustomerService.createProcess(request);

        return Response.success();
    }

    @Override
    public Response revert(@Valid RevertRequest revertRequest) {
        log.info("【客户授权撤回】revertRequest:{}",revertRequest);

        authorizationCustomerService.revert(revertRequest);

        return Response.success();
    }

    @Override
    public Response authNotify(NotifyRequest request) {
        log.info("【通知审核照片】request:{}",request);
        authorizationCustomerService.authNotify(request);
        return Response.success();
    }


    @Override
    public Response  verify(VerifyRequest request) {
        log.info("【客户授权审核通过】request:{}",request);

        if(!redisUtil.setLockIfAbsent(VERIFY_LOCK,request.getApplyId().toString(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }


        try{
            authorizationCustomerService.verify(request);
        }finally {
            redisUtil.unLock(VERIFY_LOCK,request.getApplyId().toString());
        }

        return Response.success();
    }


    @Override
    public Response reject(RejectRequest request) {
        log.info("【客户授权审核驳回】request:{}",request);
        authorizationCustomerService.reject(request);
        return Response.success();
    }

    @Override
    public Response<List<AuthorizationProcessVo>> getRecord(Integer applyId) {
        log.info("【authorization get record】applyId:{}",applyId);
        List<AuthorizationProcessVo> list = authorizationCustomerService.getRecord(applyId);
        return Response.success(list);
    }

    @Override
    public Response uploadContract(ContractUploadRequest request) {
        log.info("【上传合同】request:{}",request);
        authorizationCustomerService.uploadContract(request);
        return Response.success();
    }
}
