package com.wantwant.sfa.backend.authorization.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.authorization.api.CustomerAuthorizationApi;
import com.wantwant.sfa.backend.authorization.request.AuthorizationDetailRequest;
import com.wantwant.sfa.backend.authorization.request.AuthorizationSearchRequest;
import com.wantwant.sfa.backend.authorization.request.ManageRequest;
import com.wantwant.sfa.backend.authorization.service.IAuthorizationSearchService;
import com.wantwant.sfa.backend.authorization.vo.*;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/下午5:37
 */
@RestController
@Slf4j
public class CustomerAuthorizationController implements CustomerAuthorizationApi {
    @Autowired
    private IAuthorizationSearchService authorizationSearchService;

    @Autowired
    private SettingServiceImpl settingService;

    @Override
    public Response<AuthorizationInfoVo> getAuthorizationByCustomerId(String customerId) {
        log.info("【获取用户授权信息】customerId:{}",customerId);
        AuthorizationInfoVo vo = authorizationSearchService.getAuthorizationByCustomerId(customerId);
        return Response.success(vo);
    }

    @Override
    public Response<List<String>> getCustomerLevel() {
        List<String> list = authorizationSearchService.selectCustomerLevel();
        return Response.success(list);
    }

    @Override
    public Response<String> getDraftContracts(String customerId) {
        log.info("【获取用户初版合同】customerId:{}",customerId);
        String contracts = authorizationSearchService.getDraftContract(customerId);
        return Response.success(contracts);
    }

    @Override
    public Response<Page<AuthorizationVo>> authorizationList(AuthorizationSearchRequest request) {
        log.info("【审核列表】request:{}",request);

        Page<AuthorizationVo> page = authorizationSearchService.selectAuthorizationList(request);


        return Response.success(page);
    }

    @Override
    public Response<AuthorizationDetailVo> getAuthorizationDetail(AuthorizationDetailRequest request) {
        log.info("【审核明细】request:{}",request);

        AuthorizationDetailVo vo = authorizationSearchService.getAuthorizationDetail(request);

        return Response.success(vo);
    }

    @Override
    public Response<AuthorizationVerifyRecordVo> getVerifyRecord(Long verifyId) {
        log.info("【审核】verifyId:{}",verifyId);

        AuthorizationVerifyRecordVo vo = authorizationSearchService.getVerifyRecord(verifyId);

        return Response.success(vo);
    }

    @Override
    public Response<AuthorizationContractInfoVo> getAuthorizationContractInfo(Long verifyId) {
        log.info("【上传正式合同页面】verifyId:{}",verifyId);

        AuthorizationContractInfoVo vo = authorizationSearchService.getAuthorizationContractInfo(verifyId);

        return Response.success(vo);
    }

    @Override
    public Response<Boolean> closePermissions(String employeeId) {
        Boolean b = false;
        String empId = settingService.getValue("authorization_close");
        if (empId.contains(employeeId)){
            b=true;
        }else {
            b=false;
        }
        return Response.success(b);
    }

    @Override
    public Response<Integer> close(Long verifyId,String employeeId) {
        return Response.success(authorizationSearchService.close(verifyId,employeeId));
    }
}
