package com.wantwant.sfa.backend.barcode.controller;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.barcode.api.BarcodeApplyApi;
import com.wantwant.sfa.backend.barcode.dto.BarcodeApplyDTO;
import com.wantwant.sfa.backend.barcode.dto.BarcodeSkuDTO;
import com.wantwant.sfa.backend.barcode.dto.BarcodeVerificationDTO;
import com.wantwant.sfa.backend.barcode.request.BarcodeApplyRequest;
import com.wantwant.sfa.backend.barcode.request.BarcodeSkuRequest;
import com.wantwant.sfa.backend.barcode.request.BarcodeVerificationRequest;
import com.wantwant.sfa.backend.barcode.service.IBarcodeApplyService;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;


import java.util.ArrayList;
import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/04/下午1:57
 */
@RestController
@Slf4j
public class BarcodeController implements BarcodeApplyApi {
    @Autowired
    private IBarcodeApplyService barcodeApplyService;


    @Override
    public Response apply(BarcodeApplyRequest request) {

        log.info("【barcode apply】request:{}",request);
        barcodeApplyService.apply(convertApplyDTO(request));
        return Response.success();
    }

    private BarcodeApplyDTO convertApplyDTO(BarcodeApplyRequest request) {
        BarcodeApplyDTO dto = new BarcodeApplyDTO();
        BeanUtils.copyProperties(request,dto);

        List<BarcodeSkuRequest> purchaseSku = request.getBarcodeSkuList();

        List<BarcodeSkuDTO> purchaseStoreDTO = new ArrayList<>();
        purchaseSku.forEach(e -> {
            BarcodeSkuDTO barcodeSkuDTO = new BarcodeSkuDTO();
            BeanUtils.copyProperties(e,barcodeSkuDTO);
            purchaseStoreDTO.add(barcodeSkuDTO);
        });

        dto.setBarcodeSkuList(purchaseStoreDTO);
        return dto;
    }

    @Override
    public Response verification(BarcodeVerificationRequest request) {
        log.info("【barcode verification】request:{}",request);

        barcodeApplyService.verification(convertVerificationDTO(request));

        return Response.success();
    }

    private BarcodeVerificationDTO convertVerificationDTO(BarcodeVerificationRequest request) {
        BarcodeVerificationDTO barcodeVerificationDTO = new BarcodeVerificationDTO();
        BeanUtils.copyProperties(request,barcodeVerificationDTO);
        return barcodeVerificationDTO;
    }
}
