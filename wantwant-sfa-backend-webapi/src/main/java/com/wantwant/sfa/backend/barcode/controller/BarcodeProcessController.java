package com.wantwant.sfa.backend.barcode.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.barcode.api.BarcodeProcessApi;
import com.wantwant.sfa.backend.barcode.request.BarcodeApprovalRequest;
import com.wantwant.sfa.backend.barcode.service.IBarcodeProcessService;
import com.wantwant.sfa.backend.barcode.vo.BarcodeApplyVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/07/上午10:57
 */
@RestController
@Slf4j
public class BarcodeProcessController implements BarcodeProcessApi {

    @Autowired
    private IBarcodeProcessService barcodeProcessService;
    @Autowired
    private RedisUtil redisUtil;

    private static final String BARCODE_APPROVAL_LOCK = "sfa:barcode:approval";

    private static final String BARCODE_REJECT_LOCK = "sfa:barcode:reject";

    private static final String BARCODE_CLOSE_LOCK = "sfa:barcode:close";

    @Override
    public Response approval(BarcodeApprovalRequest barcodeApprovalRequest) {
        log.info("【barcode audit approval 】request:{}",barcodeApprovalRequest);

        if(!redisUtil.setLockIfAbsent(BARCODE_APPROVAL_LOCK,barcodeApprovalRequest.getApplyId().toString(),5, TimeUnit.SECONDS)){
            throw new ApplicationException("当前流程正在审核中,请勿重复操作");
        }

        try {
            barcodeProcessService.approval(barcodeApprovalRequest);
        } finally {
            redisUtil.unLock(BARCODE_APPROVAL_LOCK,barcodeApprovalRequest.getApplyId().toString());
        }

        return Response.success();
    }

    @Override
    public Response reject(BarcodeApprovalRequest barcodeApprovalRequest) {
        log.info("【barcode audit reject 】request:{}",barcodeApprovalRequest);

        if(!redisUtil.setLockIfAbsent(BARCODE_REJECT_LOCK,barcodeApprovalRequest.getApplyId().toString(),5, TimeUnit.SECONDS)){
            throw new ApplicationException("当前流程正在审核中,请勿重复操作");
        }

        try {
            barcodeProcessService.reject(barcodeApprovalRequest);
        } finally {
            redisUtil.unLock(BARCODE_REJECT_LOCK,barcodeApprovalRequest.getApplyId().toString());
        }

        return Response.success();
    }



    @Override
    public Response close(BarcodeApprovalRequest barcodeApprovalRequest) {
        log.info("【barcode audit close 】request:{}",barcodeApprovalRequest);

        if(!redisUtil.setLockIfAbsent(BARCODE_CLOSE_LOCK,barcodeApprovalRequest.getApplyId().toString(),5, TimeUnit.SECONDS)){
            throw new ApplicationException("当前流程正在审核中,请勿重复操作");
        }

        try {
            barcodeProcessService.close(barcodeApprovalRequest);
        } finally {
            redisUtil.unLock(BARCODE_CLOSE_LOCK,barcodeApprovalRequest.getApplyId().toString());
        }

        return Response.success();
    }
}
