package com.wantwant.sfa.backend.barcode.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.barcode.api.BarcodeProcessSearchApi;
import com.wantwant.sfa.backend.barcode.request.ApprovalListRequest;
import com.wantwant.sfa.backend.barcode.request.BarcodeApplyBaseRequest;
import com.wantwant.sfa.backend.barcode.service.IBarcodeSearchService;
import com.wantwant.sfa.backend.barcode.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/04/06/下午4:08
 */
@RestController
@Slf4j
public class BarcodeProcessSearchController implements BarcodeProcessSearchApi {
    @Autowired
    private IBarcodeSearchService barcodeSearchService;

    @Override
    public Response<Page<BarcodeApplyVo>> selectApprovalList(ApprovalListRequest approvalListRequest) {
        log.info("【barcode approval list】request:{}",approvalListRequest);
        Page<BarcodeApplyVo> page = barcodeSearchService.selectApprovalList(approvalListRequest);

        return Response.success(page);
    }

    @Override
    public void exportApprovalList(ApprovalListRequest approvalListRequest) {
        log.info("【barcode approval list】request:{}",approvalListRequest);
        barcodeSearchService.selectApprovalListExport(approvalListRequest);
    }

    @Override
    public Response<BarcodeApplyTimesVo> getApplyTimes(Long applyId) {
        log.info("【barcode approval get times】applyId:{}",applyId);

        BarcodeApplyTimesVo vo = barcodeSearchService.getApplyTimes(applyId);

        return Response.success(vo);
    }

    @Override
    public Response<List<String>> getCostType() {
        List<String> list = barcodeSearchService.selectCostType();
        return Response.success(list);
    }

    @Override
    public Response<BarcodeApplyBaseInfoVo> getBarcodeApplyBaseInfo(BarcodeApplyBaseRequest barcodeApplyBaseRequest) {
        log.info("【barcode apply base】request:{}",barcodeApplyBaseRequest);
        BarcodeApplyBaseInfoVo barcodeApplyBaseInfoVo = barcodeSearchService.getBarcodeApplyBaseInfo(barcodeApplyBaseRequest);
        return Response.success(barcodeApplyBaseInfoVo);
    }

    @Override
    public Response<List<BarcodeProcessVo>> getProcessRecord(String applyNo, int eventType, Integer times) {
        log.info("【barcode process record】applyNo:{},times:{}",applyNo,times);
        List<BarcodeProcessVo> list = barcodeSearchService.getBarcodeProcessRecord(applyNo,eventType,times);
        return Response.success(list);
    }

    @Override
    public Response<List<BarcodeProcessVo>> getCurrentApplyProcessRecord(String applyNo) {
        log.info("【barcode process record】applyNo:{}",applyNo);

        List<BarcodeProcessVo> list = barcodeSearchService.getCurrentApplyProcessRecord(applyNo);

        return Response.success(list);
    }

    @Override
    public Response<BarcodeAuditVo> getAuditInfo(String applyNo, int eventType, Integer times) {
        log.info("【barcode audit info】applyNo:{},times:{}",applyNo,times);
        BarcodeAuditVo barcodeAuditVo = barcodeSearchService.getAuditInfo(applyNo,  eventType, times);
        return Response.success(barcodeAuditVo);
    }
}
