package com.wantwant.sfa.backend.bsp.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.bsp.api.BspApi;
import com.wantwant.sfa.backend.bsp.request.BspModifyRequest;
import com.wantwant.sfa.backend.bsp.request.BspRangeRequest;
import com.wantwant.sfa.backend.bsp.service.IBspService;
import com.wantwant.sfa.backend.bsp.vo.BspVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/16/下午5:50
 */
@RestController
@Slf4j
public class BspController implements BspApi {
    @Autowired
    private IBspService bspService;

    @Override
    public Response<List<BspVo>> selectBspVo(BspRangeRequest bspRangeRequest) {
        log.info("【bsp search】request:{}",bspRangeRequest);
        List<BspVo> list = bspService.selectBspVo(bspRangeRequest);
        return Response.success(list);
    }

    @Override
    public Response modify(BspModifyRequest request) {
        log.info("【bsp modify】request:{}",request);
        bspService.modify(request);
        return Response.success();
    }
}
