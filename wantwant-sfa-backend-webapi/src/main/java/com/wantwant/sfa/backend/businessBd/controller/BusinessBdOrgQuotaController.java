package com.wantwant.sfa.backend.businessBd.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.businessBd.BusinessBdOrgQuotaAppService;
import com.wantwant.sfa.backend.businessBd.api.BusinessBdOrgQuotaApi;
import com.wantwant.sfa.backend.businessBd.request.QuotaAdjustRequest;
import com.wantwant.sfa.backend.businessBd.vo.QuotaControlVO;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 业务BD组织额度管控Controller
 */
@RestController
@Slf4j
public class BusinessBdOrgQuotaController implements BusinessBdOrgQuotaApi {
    
    @Resource
    private BusinessBdOrgQuotaAppService businessBdOrgQuotaAppService;


    @Override
    public Response<QuotaControlVO> adjustQuota(@Valid QuotaAdjustRequest request) {
        log.info("调整组织额度，请求参数：{}", request);

        return businessBdOrgQuotaAppService.adjustQuota(request);
    }

} 