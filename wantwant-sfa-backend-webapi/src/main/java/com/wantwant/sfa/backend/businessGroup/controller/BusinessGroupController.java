package com.wantwant.sfa.backend.businessGroup.controller;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.businessGroup.api.BusinessGroupApi;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupInfoUpdateRequest;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupSearchRequest;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupSortUpdateRequest;
import com.wantwant.sfa.backend.businessGroup.service.IBusinessGroupService;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupSearchVO;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupVo;
import com.wantwant.sfa.backend.organization.vo.BusinessOrganizationVo;
import com.wantwant.sfa.backend.position.service.IPositionRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/06/06/上午9:15
 */
@RestController
@Slf4j
public class BusinessGroupController implements BusinessGroupApi {

    @Autowired
    private IBusinessGroupService businessGroupService;


    @Override
    public Response<List<BusinessGroupVo>> list() {
        List<BusinessGroupVo> list = businessGroupService.list();
        return Response.success(list);
    }

    @Override
    public Response<List<BusinessOrganizationVo>> selectBusinessOrganization(String person,boolean filterBusinessGroup) {
        log.info("【select business organization】person:{}",person);
        List<BusinessOrganizationVo> list = businessGroupService.selectBusinessOrganization(person,filterBusinessGroup);
        return Response.success(list);
    }

    @Override
    public Response<String> getCurrentBusinessGroupName() {
        log.info("【get current business group name】");
        String businessGroupName = businessGroupService.getCurrentBusinessGroupName();
        return Response.success(businessGroupName);
    }

    @Override
    public Response<List<BusinessGroupSearchVO>> getFilterBusinessGroupName(BusinessGroupSearchRequest request) {
        log.info("【getFilterBusinessGroupName】 request:{}", request);
        List<BusinessGroupSearchVO> list = businessGroupService.getFilterBusinessGroupName(request);
        return Response.success(list);
    }

    @Override
    public Response updateBusinessGroupInfo(BusinessGroupInfoUpdateRequest request) {
        log.info("【updateBusinessGroupInfo】 request:{}", request);
        businessGroupService.updateBusinessGroupInfo(request);
        return Response.success();
    }

    @Override
    public Response updateBusinessGroupSort(BusinessGroupSortUpdateRequest request) {
        log.info("【updateBusinessGroupSort】 request:{} ", request);
        businessGroupService.updateBusinessGroupSort(request);
        return Response.success();
    }
}
