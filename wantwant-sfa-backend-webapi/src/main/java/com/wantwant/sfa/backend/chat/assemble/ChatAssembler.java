package com.wantwant.sfa.backend.chat.assemble;

import com.wantwant.sfa.backend.chat.request.ChatEventRequest;
import com.wantwant.sfa.backend.chat.request.ChatRequest;
import com.wantwant.sfa.backend.domain.chat.DO.ChatDO;
import com.wantwant.sfa.backend.domain.chat.enums.ChatEventEnum;
import com.wantwant.sfa.backend.domain.chat.event.ChatEvent;
import com.wantwant.sfa.backend.policy.controller.assemble.PolicyAssemble;
import com.wantwant.sfa.backend.policy.dto.PolicyRegularDTO;
import com.wantwant.sfa.backend.taskManagement.request.TaskAssignRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午9:19
 */
@Mapper(componentModel = "spring")
public interface ChatAssembler {

    ChatAssembler INSTANCE = Mappers.getMapper(ChatAssembler.class);

    @Mapping(target = "users", resultType = TaskAssignRequest.class)
    ChatDO toDO(ChatRequest chatRequest);

    @Mapping(target = "chatEventEnum", source = "eventEnum")
    ChatEvent toEvent(ChatEventRequest chatEventRequest, ChatEventEnum eventEnum);
}
