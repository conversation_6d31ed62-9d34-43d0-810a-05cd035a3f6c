package com.wantwant.sfa.backend.chat.controller;

import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.ChatApplication;
import com.wantwant.sfa.backend.chat.api.ChatAPI;
import com.wantwant.sfa.backend.chat.assemble.ChatAssembler;
import com.wantwant.sfa.backend.chat.request.ChatEventRequest;
import com.wantwant.sfa.backend.chat.request.ChatRequest;
import com.wantwant.sfa.backend.chat.request.ChatSearchRequest;
import com.wantwant.sfa.backend.chat.vo.ChatVo;
import com.wantwant.sfa.backend.domain.chat.enums.ChatEventEnum;
import com.wantwant.sfa.backend.domain.chat.event.ChatEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/04/上午9:16
 */
@RestController
@Slf4j
public class ChatController implements ChatAPI {
    @Resource
    private ChatApplication chatApplication;
    @Resource
    private ChatAssembler chatAssembler;

    @Override
    public Response save(ChatRequest chatRequest) {
        log.info("【chat save】request:{}", JSONObject.toJSONString(chatRequest));
        chatApplication.saveChat(chatAssembler.toDO(chatRequest));


        return Response.success();
    }

    @Override
    public Response<List<ChatVo>> list(@Valid ChatSearchRequest chatSearchRequest) {
        log.info("【chat search】request:{}", JSONObject.toJSONString(chatSearchRequest));

        List<ChatVo> list = chatApplication.list(chatSearchRequest);
        return Response.success(list);
    }

    @Override
    public Response delete(ChatEventRequest chatEventRequest) {
        log.info("【chat delete】chatEventRequest:{}", JSONObject.toJSONString(chatEventRequest));

        ChatEvent chatEvent = chatAssembler.toEvent(chatEventRequest, ChatEventEnum.DELETE_EVENT);
        chatApplication.deleteChat(chatEvent);

        return Response.success();
    }
}
