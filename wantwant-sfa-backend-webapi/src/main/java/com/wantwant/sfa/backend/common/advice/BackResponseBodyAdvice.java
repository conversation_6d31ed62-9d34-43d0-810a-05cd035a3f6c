//package com.wantwant.sfa.backend.common.advice;
//
//import com.wantwant.commons.core.advice.CustomerResponseBodyAdvice;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.MediaType;
//import org.springframework.http.server.ServerHttpRequest;
//import org.springframework.http.server.ServerHttpResponse;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
//
///**
// * <AUTHOR>
// * @create 2020/4/7 下午2:32
// */
//@ControllerAdvice
//public class BackResponseBodyAdvice implements ResponseBodyAdvice {
//
//    @Override
//    public boolean supports(MethodParameter methodParameter, Class aClass) {
//        return CustomerResponseBodyAdvice.supports(methodParameter,aClass);
//    }
//
//    @Override
//    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
//        return CustomerResponseBodyAdvice.beforeBodyWrite(o,methodParameter,mediaType,aClass,serverHttpRequest,serverHttpResponse);
//    }
//}