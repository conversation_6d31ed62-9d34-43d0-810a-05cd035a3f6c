package com.wantwant.sfa.backend.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/******************************************
 *                                        *
 *                  线程池配置              *
 *                                        *
 ******************************************/


@Configuration
public class SFAExecutorConfiguration {

  /******************************************
   *                                        *
   *        离线下载线程池配置                 *
   *                                        *
   ******************************************/

  @Bean(name = "OfflineExport")
  public AsyncTaskExecutor OfflineExportExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("OfflineExport-Executor");
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(10);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    return executor;
  }

  @Bean(name = "geTuiSmsSend")
  public AsyncTaskExecutor geTuiSmsSend() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("geTuiSmsSend-Executor");
    executor.setCorePoolSize(10);
    executor.setMaxPoolSize(20);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    return executor;
  }

  @Bean(name = "geTuiAppPushToSingle")
  public AsyncTaskExecutor geTuiAppPushToSingle() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("geTuiAppPushToSingle-Executor");
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(10);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    return executor;
  }

  @Bean(name = "geTuiStopTask")
  public AsyncTaskExecutor geTuiStopTask() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("geTuiStopTask-Executor");
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(10);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    return executor;
  }



  @Bean(name = "updateCustomerLabel")
  public AsyncTaskExecutor updateCustomerLabel() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("updateCustomerLabel-Executor");
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(10);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    return executor;
  }








}
