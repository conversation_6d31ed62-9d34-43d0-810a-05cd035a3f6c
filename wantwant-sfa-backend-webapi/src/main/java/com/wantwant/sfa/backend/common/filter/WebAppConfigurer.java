//package com.wantwant.sfa.backend.common.filter;
//
//import com.wantwant.commons.core.filter.ServerInterceptor;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
//
//@Configuration
//public class WebAppConfigurer extends WebMvcConfigurationSupport {
//
//  @Override
//  public void addInterceptors(InterceptorRegistry registry) {
//    registry.addInterceptor(new ServerInterceptor()).addPathPatterns("/**");
//    super.addInterceptors(registry);
//  }
//
//  @Override
//  protected void addCorsMappings(CorsRegistry registry) {
//    registry.addMapping("/**")
//            .allowedMethods("*")
//            .allowedOrigins("*")
//            .allowedHeaders("*");
//    super.addCorsMappings(registry);
//  }
//}