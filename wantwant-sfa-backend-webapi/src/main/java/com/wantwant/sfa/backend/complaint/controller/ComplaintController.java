package com.wantwant.sfa.backend.complaint.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.complaint.api.ComplaintApi;
import com.wantwant.sfa.backend.complaint.request.*;
import com.wantwant.sfa.backend.complaint.service.IComplaintService;
import com.wantwant.sfa.backend.complaint.service.IComplaintTaskService;
import com.wantwant.sfa.backend.complaint.vo.ComplaintResultVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/23/上午11:27
 */
@RestController
@Slf4j
public class ComplaintController implements ComplaintApi {
    @Autowired
    private IComplaintService complaintService;
    @Autowired
    private IComplaintTaskService complaintTaskService;
    @Autowired
    private RedisUtil redisUtil;

    private String COMPLAINT_APPLY_LOCK = "complaint:apply";

    private String COMPLAINT_TASK_SUBMIT = "complaint:task:submit";

    private String COMPLAINT_HR_CONFIRM = "complaint:task:hr:confirm";

    private String COMPLAINT_FINANCE_SUBMIT = "complaint:task:finance:submit";

    private String COMPLAINT_FINANCE_REJCT = "complaint:task:finance:reject";

    @Override
    public Response apply(ComplaintApplyRequest request) {
        log.info("【薪资申诉申请】request:{}",request);
        if (!redisUtil.setLockIfAbsent(COMPLAINT_APPLY_LOCK, request.getApplyId().toString(), 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("请勿重复提交");
        }

        try{
            complaintService.apply(request);
        }finally {
            redisUtil.unLock(COMPLAINT_APPLY_LOCK,request.getApplyId().toString());
        }

        return Response.success();
    }

    @Override
    public Response submit(ComplaintProcessRequest request) {
        log.info("【薪资申诉经办/会办提交】request:{}",request);
        if (!redisUtil.setLockIfAbsent(COMPLAINT_TASK_SUBMIT, request.getTaskId().toString(), 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("请勿重复提交");
        }

        try{
            complaintTaskService.submit(request);
        }finally{
            redisUtil.unLock(COMPLAINT_TASK_SUBMIT,request.getTaskId().toString());
        }

        return Response.success();
    }

    @Override
    public Response hrConfirm(ComplaintHrConfirmRequest request) {
        log.info("【薪资申诉人资确认】request:{}",request);
        if (!redisUtil.setLockIfAbsent(COMPLAINT_HR_CONFIRM, request.getTaskId().toString(), 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("请勿重复提交");
        }

        try{
            complaintTaskService.hrConfirm(request);
        }finally{
            redisUtil.unLock(COMPLAINT_HR_CONFIRM,request.getTaskId().toString());
        }

        return  Response.success();
    }

    @Override
    public Response financeConfirm(FinanceConfirmRequest request) {
        log.info("【薪资申诉财务确认】request:{}",request);
        if (!redisUtil.setLockIfAbsent(COMPLAINT_FINANCE_SUBMIT, request.getTaskId().toString(), 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("请勿重复提交");
        }

        try{
            complaintTaskService.financeConfirm(request);
        }finally{
            redisUtil.unLock(COMPLAINT_FINANCE_SUBMIT,request.getTaskId().toString());
        }

        return Response.success();
    }

    @Override
    public Response financeReject(FinanceRejectRequest financeRejectRequest) {
        log.info("【薪资确认财务驳回】request:{}",financeRejectRequest);
        if (!redisUtil.setLockIfAbsent(COMPLAINT_FINANCE_REJCT, financeRejectRequest.getTaskId().toString(), 5, TimeUnit.SECONDS)) {
            throw new ApplicationException("请勿重复提交");
        }

        try{
            complaintTaskService.financeReject(financeRejectRequest);
        }finally{
            redisUtil.unLock(COMPLAINT_FINANCE_REJCT,financeRejectRequest.getTaskId().toString());
        }

        return Response.success();
    }
}
