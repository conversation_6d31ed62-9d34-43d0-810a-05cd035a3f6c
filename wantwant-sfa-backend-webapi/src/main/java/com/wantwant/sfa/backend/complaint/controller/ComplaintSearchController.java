package com.wantwant.sfa.backend.complaint.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.complaint.api.ComplaintSearchApi;
import com.wantwant.sfa.backend.complaint.request.ComplaintListSearchRequest;
import com.wantwant.sfa.backend.complaint.service.IComplaintSearchService;
import com.wantwant.sfa.backend.complaint.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/24/下午1:27
 */
@RestController
@Slf4j
public class ComplaintSearchController implements ComplaintSearchApi {
    @Autowired
    private IComplaintSearchService complaintSearchService;

    @Override
    public Response<ComplaintResultVo> getComplaintResult(int applyId) {
        log.info("【查询申诉结果】applyId：{}",applyId);
        ComplaintResultVo vo = complaintSearchService.getComplaintResult(applyId);
        return Response.success(vo);
    }

    @Override
    public Response<ComplaintApplyUserInfoVo> getApplyUserInfo(Long applyId) {
        log.info("【获取申诉人信息】applyId：{}",applyId);
        ComplaintApplyUserInfoVo vo = complaintSearchService.getApplyUserInfo(applyId);
        return Response.success(vo);
    }

    @Override
    public Response<ComplaintTaskHistoryVo> getTaskHistory(Long taskId) {
        log.info("【申诉会办记录查询】taskId：{}",taskId);
        ComplaintTaskHistoryVo vo = complaintSearchService.getTaskHistory(taskId);
        return Response.success(vo);
    }

    @Override
    public Response<ComplaintAdjustVo> getAdjustInfo(Long taskId) {
        log.info("【申诉金额调整查询】taskId：{}",taskId);
        ComplaintAdjustVo vo = complaintSearchService.getAdjustInfo(taskId);
        return Response.success(vo);
    }

    @Override
    public Response<List<ComplaintTagVo>> getTagList() {
        List<ComplaintTagVo> list =  complaintSearchService.getTagList();
        return Response.success(list);
    }

    @Override
    public Response<List<ComplaintPermissionGroupVo>> getPermissionGroup(Long taskId) {
        log.info("【申诉权限组获取】taskId：{}",taskId);
        List<ComplaintPermissionGroupVo> list = complaintSearchService.getPermissionGroup(taskId);
        return Response.success(list);
    }

    @Override
    public Response<List<ComplaintPendingVo>> selectComplaintPending(ComplaintListSearchRequest request) {
        List<ComplaintPendingVo> list = complaintSearchService.selectComplaintPending(request);
        return Response.success(list);
    }

    @Override
    public Response<List<ComplaintProcessingVo>> selectComplaintProcessing(ComplaintListSearchRequest request) {
        List<ComplaintProcessingVo> list = complaintSearchService.selectComplaintProcessing(request);
        return Response.success(list);
    }

    @Override
    public Response<Page<ComplaintFinishedVo>> selectComplaintFinished(ComplaintListSearchRequest request) {
        Page<ComplaintFinishedVo> page = complaintSearchService.selectComplaintFinished(request);
        return Response.success(page);
    }
}
