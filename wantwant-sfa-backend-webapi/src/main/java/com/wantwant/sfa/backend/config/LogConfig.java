package com.wantwant.sfa.backend.config;

import com.wantwant.sfa.common.architecture.log.AccessLogStrategySelector;
import com.wantwant.sfa.sales.management.api.service.DemoApi;
import com.wantwant.sfa.sales.management.api.service.WorkflowServiceApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashSet;

@Configuration
public class LogConfig {
    @Bean
    public AccessLogStrategySelector accessLogStrategySelector() {
        return new AccessLogStrategySelector(new HashSet<>(Arrays.asList(
                DemoApi.class,
                WorkflowServiceApi.class)));
    }
}
