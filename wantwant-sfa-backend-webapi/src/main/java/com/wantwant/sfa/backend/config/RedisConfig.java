package com.wantwant.sfa.backend.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.PostConstruct;

/**
 * redis序列化配置
 */
@Configuration
public class RedisConfig {

  @Autowired
  private RedisTemplate redisTemplate;


  private static RedisTemplate redisTemplateSta;

  @Bean
  public RedisTemplate<Object, Object> redisTemplate() {
    redisTemplate.setKeySerializer(new StringRedisSerializer());
    redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
    //value hashmap序列化
    redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
    return redisTemplate;
  }


  /**
   * 骚操作 极度不推荐 请勿在启动完成前使用
   */
  @PostConstruct
  private void init() {
    redisTemplateSta = redisTemplate;
  }

  public static RedisTemplate getRedis() {
    return redisTemplateSta;
  }
}