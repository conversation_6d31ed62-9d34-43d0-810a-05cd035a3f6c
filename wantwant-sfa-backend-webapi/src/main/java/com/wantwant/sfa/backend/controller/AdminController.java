package com.wantwant.sfa.backend.controller;

import com.alibaba.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.service.AutomaticVisitTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/sa")
public class AdminController {

    @Autowired
    private AutomaticVisitTaskService automaticVisitTaskService;

    @GetMapping("/version")
    public Response version(){
        return Response.success(System.getenv());
    }


    @PostMapping("/postTest")
    public Response postTest(@RequestBody JSONObject jsonObject){
        return Response.success(jsonObject);
    }


    @GetMapping("/planVisit")
    public Response planVisit(){
        automaticVisitTaskService.automaticVisit();
        return Response.success();
    }
}
