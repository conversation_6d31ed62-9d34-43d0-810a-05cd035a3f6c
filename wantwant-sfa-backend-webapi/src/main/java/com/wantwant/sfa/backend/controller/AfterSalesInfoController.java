package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.afterSales.request.*;
import com.wantwant.sfa.backend.afterSales.vo.*;
import com.wantwant.sfa.backend.service.AfterSalesInfoService;
import com.wantwant.sfa.backend.service.AfterSalesProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 售后单相关接口
 *
* @since 2022-11-22
*/
@Api(tags = "售后单信息相关接口")
@RestController
@RequestMapping("/afterSales")
public class AfterSalesInfoController {

	@Autowired
	private AfterSalesInfoService afterSalesInfoService;

	@Resource
	private AfterSalesProcessService afterSalesProcessService;

	/**
	 * 保存售后单信息返回审批信息
	 * 提供旺铺-张浩然
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.afterSales.vo.AfterSalesProcessVO>
	 * @date: 11/23/22 9:51 AM
	 */
	@ApiOperation(value = "保存售后单信息返回审批信息")
	@PostMapping
	public Response<AfterSalesProcessVO> saveAfterSales(@Valid @RequestBody AfterSalesSaveRequest request) {
		return Response.success(afterSalesInfoService.saveAfterSales(request));
	}

	/**
	 * 售后单撤回
	 * 提供旺铺-张浩然
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 11/23/22 9:54 AM
	 */
	@ApiOperation(value = "售后撤回")
	@PostMapping(value = "/cancel")
	public Response<Integer> cancel(@Valid @RequestBody AfterSalesCancelRequest request) {
		return Response.success(afterSalesInfoService.cancel(request));
	}

	/**
	 * 客服审批结果
	 * 提供旺铺-徐藤
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 11/23/22 10:34 AM
	 */
	@ApiOperation(value = "客服审批结果")
	@PostMapping(value = "/customerServiceAudit")
	public Response customerServiceAudit(@Valid @RequestBody CustomerServiceAuditRequest request) {
		return afterSalesInfoService.customerServiceAudit(request);
	}

	/**
	 * 售后审批列表
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.afterSales.vo.AfterSalesInfoVO>>
	 * @date: 11/24/22 11:20 AM
	 */
	@ApiOperation(notes = "售后审批列表", value = "售后审批列表")
	@GetMapping("/queryByPage")
	public Response<IPage<AfterSalesInfoVO>> queryByPage(AfterSalesQueryRequest request) {
		return Response.success(afterSalesInfoService.queryByPage(request));
	}

	/**
	 * 售后审批详情
	 *
	 * @param id
	 * @param employeeId
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.afterSales.vo.AfterSalesInfoVO>
	 * @date: 11/24/22 2:22 PM
	 */
	@ApiOperation(notes = "售后审批详情", value = "售后审批详情")
	@GetMapping("/details/{id}/{employeeId}")
	public Response<AfterSalesInfoVO> details(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
											  @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
		return Response.success(afterSalesInfoService.details(id,employeeId));
	}

	/**
	 * 额度查询调用旺铺实时查询(废弃)
	 * 调用旺铺-张浩然
	 *
	 * @param applicationNo
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.afterSales.vo.QuotaVO>
	 * @date: 04/07/23 5:28 PM
	 */
	@Deprecated
	@ApiOperation(notes = "额度查询", value = "额度查询")
	@GetMapping("/queryQuota/{applicationNo}")
	public Response<List<QuotaVO>> queryQuota(@PathVariable("applicationNo") @NotNull(message = "applicationNo不能为空") String applicationNo) {
		return Response.success(afterSalesInfoService.queryQuota(applicationNo));
	}

	/**
	 * 售后单审批
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 11/23/22 5:04 PM
	 */
	@ApiOperation(notes = "售后单审批", value = "售后单审批")
	@PutMapping("/audit")
	public Response audit(@Valid @RequestBody AfterSalesAuditRequest request) {
		return afterSalesProcessService.audit(request);
	}

	/**
	 * 旺金币额度
	 */
    @ApiOperation(notes = "旺金币额度", value = "旺金币额度")
    @GetMapping("/orgQuotaSurplus/{id}")
    public Response<OrgQuotaSurplusVO> quotaSurplus(@PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
        return Response.success(afterSalesInfoService.quotaSurplus(id));
    }

    /**
     * 合伙人售后额度
	 * 调用旺铺-张浩然
     */
	@ApiOperation(notes = "合伙人售后额度", value = "合伙人售后额度")
	@GetMapping("/queryEmpQuota/{id}")
	public Response<List<AfterSalesEmpQuotaVO>> queryEmpQuota(@PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
		return Response.success(afterSalesInfoService.queryEmpQuota(id));
	}


	@ApiOperation(notes = "售后驳回清单", value = "售后驳回清单")
	@GetMapping("/queryRejected/{date}")
	public Response<List<AfterSalesRejectVO>> queryRejected(@PathVariable("date") @NotNull(message = "date不能为空") String date) {
		return Response.success(afterSalesInfoService.queryRejected(date));
	}


}
