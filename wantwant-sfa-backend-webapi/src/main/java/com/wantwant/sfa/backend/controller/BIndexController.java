package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.BIndex.api.BIndexApi;
import com.wantwant.sfa.backend.BIndex.request.*;
import com.wantwant.sfa.backend.BIndex.vo.*;
import com.wantwant.sfa.backend.Task.PerformanceNoticeTask;
import com.wantwant.sfa.backend.service.IBIndexService;
import com.wantwant.sfa.backend.service.IndicatorsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description: //模块目的、功能描述
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源 @Time 2021-5-14 13:59:53
 */
@Slf4j
@RestController
public class BIndexController implements BIndexApi {

  @Autowired IBIndexService ibIndexService;

  @Autowired IndicatorsService indicatorsService;

  @Autowired
  private PerformanceNoticeTask performanceNoticeTask;

  @Override
  public Response<BIndexVo> bIndexDateInfo(BIndexDateRequest request) {
    log.info("start BIndexController bIndexDateInfo request:{}", request);
    BIndexVo vo = ibIndexService.getBIndexDateInfo(request);
    return Response.success(vo);
  }

  @Override
  public Response<BIndexNextListTreeVo> nextBIndexDataTree(NextBIndexDateRequest request) {
    BIndexNextListTreeVo vo = ibIndexService.nextBIndexDataTree(request);
    return Response.success(vo);
  }

  @Override
  public Response<BIndexNextListVo> nextBIndexData(NextBIndexDateRequest request) {
    BIndexNextListVo vo = ibIndexService.nextBIndexData(request);
    return Response.success(vo);
  }

  @Override
  public Response<BIndexDateListVo> nextBIndexDatalineChart(NextBIndexDateRequest request) {
    BIndexDateListVo vo = ibIndexService.nextBIndexDatalineChart(request);
    return Response.success(vo);
  }

  @Override
  public Response<IndicatorsVo> indicatorsDateInfo(IndicatorsDateRequest request) {
    return  Response.success(indicatorsService.indicatorsDateInfo(request));
  }

  @Override
  public Response<BIndexNextListTreeVo> indicatorsDataTree(NextIndicatorsDateRequest request) {

    return Response.success(indicatorsService.indicatorsDataTree(request));
  }

  @Override
  public Response<BIndexNextListVo> nextIndicatorsData(NextIndicatorsDateRequest request) {
    return Response.success(indicatorsService.nextIndicatorsData(request));
  }

  @Override
  public Response<BIndexDateListVo> nextIndicatorsDatalineChart(NextIndicatorsDateRequest request) {
    return Response.success(indicatorsService.nextIndicatorsDatalineChart(request));
  }

  @Override
  public Response<List<BindexConfigurationVo>> dynamicField(BindexConfigurationRequest request) {
    return Response.success(indicatorsService.dynamicField(request));
  }

  @Override
  public Response<List<BindexV2OrgConfigurationInfoVo>> getOrganizationIndicators(String organizationId, int type, String month,Integer isMonthly,Integer isQuarter,Integer isQuarterMonth,int isIndex,String person) throws IllegalAccessException {
    log.info("getOrganizationIndicators organizationName:{},type:{},month:{},isIndex:{}",organizationId,type,month,isIndex);
    return Response.success(indicatorsService.queryOrganizationIndicators(organizationId, type, month,isMonthly,isQuarter,isQuarterMonth,isIndex,1,person));
  }

  @Override
  public Response<List<BindexV2OrgConfigurationInfoVo>> getOrganizationIndicatorsFiscalYear(String organizationId, String year,String person,Integer fiscalYearSpecialModel) throws IllegalAccessException {
    log.info("getOrganizationIndicators organizationName:{},year:{}, fiscalYearSpecialModel:{}",organizationId,year, fiscalYearSpecialModel);
    return Response.success(indicatorsService.getOrganizationIndicatorsFiscalYear(organizationId, year,2,person,fiscalYearSpecialModel));
  }

  @Override
  public Response<List<BindexV2OrgConfigurationInfoVo>> getOrganizationAllIndicators(String organizationId, int type, String month,Integer isMonthly,Integer isQuarter,Integer isQuarterMonth, int isIndex,String person) throws IllegalAccessException {
    log.info("getOrganizationAllIndicators organizationName:{},type:{},month:{},isQuarter:{},isQuarterMonth:{},isIndex:{},person:{}",organizationId,type,month,isQuarter,isQuarterMonth,isIndex,person);
    return Response.success(indicatorsService.queryOrganizationIndicators(organizationId, type, month,isMonthly,isQuarter,isQuarterMonth,isIndex,2,person));
  }

  @Override
  public Response<BindexV2NextOrganizationVo> getOrganizationAllNextOrganization(String organizationId, String month,Integer isMonthly,Integer isQuarter,Integer isQuarterMonth,String indexFieldValue,String person) throws IllegalAccessException {
    log.info("getOrganizationAllIndicators organizationName:{},month:{},indexValue:{}",organizationId,month,isQuarter,indexFieldValue);
    return Response.success(indicatorsService.getOrganizationAllNextOrganization(organizationId, month, isMonthly,isQuarter,isQuarterMonth, indexFieldValue, person));
  }



  @Override
  public Response<String> getIndexExist(String indexFieldValue) {
    return Response.success(indicatorsService.getIndexExist(indexFieldValue));
  }

  @Override
  public void performanceTask() {
    performanceNoticeTask.configureTasks();
  }

  @Override
  public Response<Integer> performanceAboutExpire(String person, int day) {
    return Response.success(indicatorsService.performanceAboutExpire(person,day));
  }
}
