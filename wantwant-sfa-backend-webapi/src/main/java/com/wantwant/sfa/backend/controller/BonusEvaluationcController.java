package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.bonusEvaluation.api.BonusEvaluationApi;
import com.wantwant.sfa.backend.bonusEvaluation.request.*;
import com.wantwant.sfa.backend.bonusEvaluation.vo.*;
import com.wantwant.sfa.backend.service.BonusEvaluationService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

@RestController
public class BonusEvaluationcController implements BonusEvaluationApi {

  @Autowired private BonusEvaluationService bonusEvaluationService;

  @Override
  public Response<Page<BranchBonusEvaluationVo>> getBranchBonusEvaluationList(
      @Valid BonusEvaluationRequest request) {
    return Response.success(bonusEvaluationService.getBranchBonusEvaluationList(request));
  }

  @Override
  public Response<Page<CompanyBonusEvaluationVo>> getCompanyBonusEvaluationList(
      @Valid BonusEvaluationRequest request) {
    return Response.success(bonusEvaluationService.getCompanyBonusEvaluationList(request));
  }

  @Override
  public Response<Integer> updateBonusEvaluationList(@Valid BonusEvaluationUpdateRequest request) {
    return Response.success(bonusEvaluationService.updateBonusEvaluation(request));
  }

  @ApiOperation(value = "绩效评定列表", notes = "绩效评定列表")
  @Override
  public Response<Page<PerformanceEvaluationVo>> performanceEvaluatioList(PerformanceEvaluationRequest request) {
    return  Response.success(bonusEvaluationService.performanceEvaluatioList(request));
  }

  @ApiOperation(value = "绩效奖金查询列表", notes = "绩效奖金查询列表")
  @Override
  public Response<PerformanceEvaluationListVo> performanceEvaluatioDetailList(PerformanceEvaluationRequest request) throws ParseException {
    return  Response.success(bonusEvaluationService.performanceEvaluatioDetailList(request));
  }

  @ApiOperation(value = "绩效奖金-提成明细-sku列表", notes = "绩效奖金-提成明细-sku列表")
  @Override
  public Response<List<PerformanceSkuDetailVo>> querySkuListPerformanceDetail(PerformanceSkuDetailPageRequest request) {
    return Response.success(bonusEvaluationService.querySkuListPerformanceDetail(request));
  }

  @ApiOperation(value = "绩效奖金-提成明细", notes = "绩效奖金-提成明细")
  @Override
  public Response<IPage<PerformanceSkuDetailVo>> querySkuPerformanceDetail(PerformanceSkuDetailPageRequest request) {
    return Response.success(bonusEvaluationService.querySkuPerformanceDetail(request));
  }

  @ApiOperation(value = "绩效奖金-提成明细-下载", notes = "绩效奖金-提成明细-下载")
  @Override
  public void downLoadSkuPerformanceDetail(@RequestBody PerformanceSkuDetailPageRequest request, HttpServletRequest req, HttpServletResponse res) {
    bonusEvaluationService.downLoadSkuPerformanceDetail(request, req, res);
  }

  @Override
  public Response<Integer> updatePerformanceEvaluatio(PerformanceEvaluationScoreRequest request)
  {
    return  Response.success(bonusEvaluationService.updatebonusEvaluation(request));
  }

  @Override
  public void performanceEvaluatioOrganizationTask() {
     bonusEvaluationService.performanceEvaluatioOrganizationTask();
  }

  @Override
  public void performanceEvaluatioTask() {
    bonusEvaluationService.performanceEvaluatioTask();
  }

  @Override
  public Response<PerformanceEvalustionMattersVo> performanceEvaluatioMatters(String employeeId,Integer type) {
    PerformanceEvalustionMattersVo performanceEvalustionMattersVo =null;
    if(type!=3){
       performanceEvalustionMattersVo = bonusEvaluationService.performanceEvaluatioMatters(employeeId, type);
    }
    return Response.success(performanceEvalustionMattersVo);
  }

  @Override
  public Response<PerformanceEvaluationDynamicVo> performanceEvaluatioHeader(PerformanceEvaluationHeaderRequest request) {

    return Response.success(bonusEvaluationService.performanceEvaluatioHeader(request));
  }

  @Override
  public Response<List<Map<String, Object>>> performanceEvaluatioDynamicList(PerformanceEvaluationRequest request) {
    return Response.success(bonusEvaluationService.performanceEvaluatioDynamicList(request));
  }

  @Override
  public Response<List<PerformanceAssessmentRulesVo>> performanceEvaluatioAssessmentRule(String startMonth, String endMonth, Integer type) {
    List<PerformanceAssessmentRulesVo> performanceAssessmentRulesVos = bonusEvaluationService.performanceEvaluatioAssessmentRule(startMonth, endMonth, type);
    return Response.success(performanceAssessmentRulesVos);
  }

  @Override
  public Response<Page<SectionTargetVo>> getSectionTargetByPerson(String person) {
    Page<SectionTargetVo> sectionTarget = bonusEvaluationService.getSectionTargetByPerson(person);
    return Response.success(sectionTarget);
  }

  @Override
  public Response<Page<SectionTargetVo>> getSectionTargetAll(String person, String sectionName, String targetContent) {
    Page<SectionTargetVo> sectionTarget = bonusEvaluationService.getSectionTargetAll(person, sectionName, targetContent);
    return Response.success(sectionTarget);
  }

  @Override
  public Response<PerformanceEvaluationTopVo> performanceEvaluatioNotifyTop(PerformanceEvaluationTopRequest request){
    return  Response.success(bonusEvaluationService.performanceEvaluatioNotifyTop(request));
  }

}
