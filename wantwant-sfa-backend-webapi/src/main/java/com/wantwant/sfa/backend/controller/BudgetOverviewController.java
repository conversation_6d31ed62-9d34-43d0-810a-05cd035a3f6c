package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.budget.vo.BudgetOverviewVO;
import com.wantwant.sfa.backend.service.BudgetOverviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
*
* @since 2023-02-27
*/
@Api(tags = "预算总览相关接口")
@RestController
@RequestMapping("/budgetOverview")
public class BudgetOverviewController {

	@Autowired
	private BudgetOverviewService budgetOverviewService;

	@ApiOperation(value = "预算总览")
	@GetMapping(value = "/listOverview")
	private Response<List<BudgetOverviewVO>> listOverview(){
		return Response.success(budgetOverviewService.listOverview());
	}


}
