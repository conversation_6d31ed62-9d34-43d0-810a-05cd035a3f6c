package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.business.request.BusinessAuditRequest;
import com.wantwant.sfa.backend.business.request.BusinessRequest;
import com.wantwant.sfa.backend.business.vo.BusinessDetailVO;
import com.wantwant.sfa.backend.business.vo.BusinessVO;
import com.wantwant.sfa.backend.service.BusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * BD建档相关
 *
 * @date 3/24/22 1:27 PM
 * @version 1.0
 */
@Slf4j
@Api(tags = "BD建档相关")
@RestController
@RequestMapping("/business")
public class BusinessController {

    @Autowired
    private BusinessService businessService;

    /**
     * BD列表
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.BD.vo.BusinessVO>>
     * @date: 3/24/22 2:07 PM
     */
    @ApiOperation(value = "BD列表", notes = "BD列表")
    @GetMapping(value = "/queryApprovalByPage")
    public Response<IPage<BusinessVO>> queryApprovalByPage(BusinessRequest query) {
        return Response.success(businessService.queryApprovalByPage(query));
    }


    /**
     * BD审批
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Boolean>
     * @date: 3/24/22 9:29 PM
     */
    @ApiOperation(notes = "BD审批", value = "BD审批")
    @PutMapping("/audit")
    public Response<Boolean> audit(@Valid @RequestBody BusinessAuditRequest request) {
        return Response.success(businessService.audit(request));
    }

    /**
     * BD导出
     *
     * @param query
     * @return: void
     * @date: 3/25/22 1:02 PM
     */
    @ApiOperation(value = "BD导出", notes = "BD导出")
    @GetMapping(value = "/export")
    public void exportList(BusinessRequest query) {
        businessService.exportList(query);
    }


}
