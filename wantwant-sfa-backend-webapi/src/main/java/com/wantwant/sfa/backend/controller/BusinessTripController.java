package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.businessTrip.request.*;
import com.wantwant.sfa.backend.businessTrip.vo.BusinessTripBaseEmployeeInfoVO;
import com.wantwant.sfa.backend.businessTrip.vo.BusinessTripDetailVO;
import com.wantwant.sfa.backend.businessTrip.vo.BusinessTripInfoVO;
import com.wantwant.sfa.backend.businessTrip.vo.GuidePlaceVO;
import com.wantwant.sfa.backend.service.BusinessTripService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Api(tags = "出差申请相关接口")
@RestController
@RequestMapping("/businessTrip")
public class BusinessTripController {

	@Resource
	private BusinessTripService businessTripService;

	@ApiOperation(value = "出差申请单保存")
	@PostMapping
	public Response<Map<String,Integer>> save(@Valid @RequestBody TripSaveRequest request) {
		return Response.success(businessTripService.saveTrip(request));
	}


	@ApiOperation(value = "行程明细保存")
	@PostMapping("/saveItinerary")
	public Response<Integer> saveItinerary(@Valid @RequestBody ItinerarySaveRequest request) {
		return Response.success(businessTripService.saveItinerary(request));
	}

	@ApiOperation(value = "行程明细修改")
	@PutMapping("/updateItinerary/{itineraryId}")
	public Response<Integer> updateItinerary(@PathVariable("itineraryId") @NotNull(message = "itineraryId不能为空！") Integer itineraryId,
											 @Valid @RequestBody ItinerarySaveRequest request) {
		return Response.success(businessTripService.updateItinerary(itineraryId,request));
	}

	@ApiOperation(value = "行程明细删除")
	@DeleteMapping("/deleteItinerary/{itineraryId}")
	public Response<Integer> deleteItinerary(@PathVariable("itineraryId") @NotNull(message = "itineraryId不能为空！") Integer itineraryId) {
		return Response.success(businessTripService.deleteItinerary(itineraryId));
	}


	@ApiOperation(value = "费用明细保存")
	@PostMapping("/saveCost")
	public Response<Integer> saveCost(@Valid @RequestBody CostSaveRequest request) {
		return Response.success(businessTripService.saveCost(request));
	}

	@ApiOperation(value = "费用明细修改")
	@PutMapping("/updateCost/{costId}")
	public Response<Integer> updateCost(@PathVariable("costId") @NotNull(message = "costId不能为空！") Integer costId,
											 @Valid @RequestBody CostSaveRequest request) {
		return Response.success(businessTripService.updateCost(costId,request));
	}

	@ApiOperation(value = "费用明细删除")
	@DeleteMapping("/deleteCost/{costId}")
	public Response<Integer> deleteCost(@PathVariable("costId") @NotNull(message = "costId不能为空！") Integer costId) {
		return Response.success(businessTripService.deleteCost(costId));
	}


	@ApiOperation(value = "申请单流程发起")
	@PutMapping("/saveProcess")
	public Response<Integer> saveProcess(@Valid @RequestBody TripDetailRequest request) {
		return Response.success(businessTripService.saveProcess(request));
	}

	@ApiOperation(notes = "出差申请单列表", value = "出差申请单列表")
	@PostMapping("/queryByPage")
	public Response<IPage<BusinessTripInfoVO>> queryByPage(@RequestBody TripQueryRequest request) {
		return Response.success(businessTripService.queryByPage(request));
	}

	@ApiOperation(notes = "待出差申请单列表", value = "待出差申请单列表")
	@PostMapping("/queryPendingByPage")
	public Response<IPage<BusinessTripInfoVO>> queryPendingByPage(@RequestBody TripQueryRequest request) {
		return Response.success(businessTripService.queryPendingByPage(request));
	}

	@ApiOperation(value = "待审批导出", notes = "待审批导出")
	@PostMapping(value = "/exportPendingList")
	public void exportPendingList(@RequestBody TripQueryRequest request, HttpServletResponse response) {
		businessTripService.exportPendingList(request,response);
	}

	@ApiOperation(notes = "申请单详情", value = "申请单详情")
	@GetMapping("/{tripId}/{employeeId}")
	public Response<BusinessTripInfoVO> getTripByTripId(@PathVariable("tripId") @NotNull(message = "tripId不能为空！") Integer tripId,
											  @PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
		return Response.success(businessTripService.getTripByTripId(tripId,employeeId));
	}

	@ApiOperation(notes = "申请单删除", value = "申请单删除")
	@DeleteMapping("/{tripId}")
	public Response<Integer> deleteTripByTripId(@PathVariable("tripId") @NotNull(message = "tripId不能为空！") Integer tripId) {
		return Response.success(businessTripService.deleteTripByTripId(tripId));
	}

	@ApiOperation(notes = "申请单取消", value = "申请单取消")
	@PutMapping("/{tripId}")
	public Response<Integer> cancelTripByTripId(@PathVariable("tripId") @NotNull(message = "tripId不能为空！") Integer tripId) {
		return Response.success(businessTripService.cancelTripByTripId(tripId));
	}

	@ApiOperation(value = "列表导出", notes = "列表导出")
	@PostMapping(value = "/exportList")
	public void exportList(@RequestBody TripQueryRequest request, HttpServletResponse response) {
		businessTripService.exportList(request,response);
	}

	@ApiOperation(value = "高德关键字搜索", notes = "高德关键字搜索")
	@GetMapping(value = "/guidePlace")
	public Response<List<GuidePlaceVO>> guidePlace(@ApiParam(value = "关键字", required = true) @RequestParam(value = "keyWords") String keywords) {
		return businessTripService.guidePlace(keywords);
	}

	@ApiOperation(notes = "详情页面", value = "详情页面")
	@GetMapping("/details/{tripId}/{employeeId}")
	public Response<BusinessTripDetailVO> details(@PathVariable("tripId") @NotNull(message = "tripId不能为空！") Integer tripId,
												  @PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
		return Response.success(businessTripService.details(tripId,employeeId));
	}

	@ApiOperation(notes = "审批", value = "审批")
	@PostMapping("/audit")
	public Response audit(@Valid @RequestBody TripAuditRequest request) {
		return businessTripService.audit(request);
	}

	@ApiOperation(notes = "报销流程关联申请单下拉", value = "报销流程关联申请单下拉")
	@PostMapping("/getTripListForApply")
	public Response<List<BusinessTripInfoVO>> getTripListForApply(@RequestBody @Validated TripListForApplyRequest request) {
		return Response.success(businessTripService.getTripListForApply(request));
	}
	@ApiOperation(notes = "获取出差申请单填报人基础信息", value = "获取出差申请单填报人基础信息")
	@PostMapping("/baseInfo")
	public Response<List<BusinessTripBaseEmployeeInfoVO>> getBaseEmployeeInfo(@RequestBody @Validated BusinessTripBaseEmployeeInfoRequest request){
		return Response.success(businessTripService.getBaseEmployeeInfo(request));
	}
	@ApiOperation(notes = "获取代理人白名单列表", value = "获取代理人白名单列表")
	@GetMapping("/getWhiteListForProxy/{employeeId}")
	public Response<Boolean> getWhiteListForProxy(@PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId){
		return Response.success(businessTripService.getWhiteListForProxy(employeeId));
	}

}
