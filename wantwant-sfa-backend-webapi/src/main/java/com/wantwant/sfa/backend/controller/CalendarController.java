package com.wantwant.sfa.backend.controller;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.calendar.request.CalendarConfigRequest;
import com.wantwant.sfa.backend.calendar.request.CalendarRequest;
import com.wantwant.sfa.backend.calendar.request.CanlendarInfoRequest;
import com.wantwant.sfa.backend.calendar.vo.CalendarInfoListVo;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.service.CalendarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作日历
 *
 * @version 1.0
 * @date 5/9/22 4:27 PM
 */
@Slf4j
@Api(tags = "工作日历")
@RestController
@RequestMapping("/calendar")
public class CalendarController {

    @Autowired
    CalendarService calendarService;

    /**
     * 节假日接口
     *
     * @param
     * @return:
     * @date: 5/9/22 5:00 PM
     */
    @GetMapping("/getHolidays")
    @ApiOperation(value = "节假日", notes = "节假日")
    public Response<List<String>> getHolidays() {
        return Response.success(calendarService.getHolidays());
    }

    /**
     * 周末接口
     *
     * @param
     * @return:
     * @date: 5/9/22 5:00 PMW
     */
    @GetMapping("/getWeekend")
    @ApiOperation(value = "周末", notes = "周末")
    public Response<List<String>> getWeekend() {
        return Response.success(calendarService.getWeekend());
    }



    /**
     * 日历删除接口
     *
     * @param
     * @return:
     * @date: 5/9/22 5:00 PM
     */
    @PostMapping("/remove")
    @ApiOperation(value = "日历删除", notes = "日历删除")
    public Response Remove(@RequestBody CalendarRequest request) {
        return calendarService.getRemove(request);
    }

    /**
     * 日历新增接口
     *
     * @param
     * @return:
     * @date: 5/9/22 5:00 PM
     */
    @PostMapping("/add")
    @ApiOperation(value = "日历新增", notes = "日历新增")
    public Response getAdd(@RequestBody CalendarRequest request) {
        return calendarService.getAdd(request);
    }


    @PostMapping("/personal/list")
    @ApiOperation(value = "日历列表", notes = "日历列表")
    public Response<List<CalendarInfoListVo>> getCalendarInfoList(@Validated @RequestBody CanlendarInfoRequest request) {
        log.info("getCalendarInfoList :", request);
        if(request.getBusinessGroup() == null) {
            request.setBusinessGroup(RequestUtils.getBusinessGroup());
        }
        return Response.success(calendarService.getCalendarPersonalInfoList(request));
    }

    @PostMapping("/personal/insert")
    @ApiOperation(value = "日历插入/修改", notes = "日历插入/修改")
    public Response getCalendarInsertAndUpdate(@Validated @RequestBody List<CalendarConfigRequest> request) {
        log.info("getCalendarInsertAndUpdate :", request);
        calendarService.getCalendarInsertAndUpdate(request);
        return Response.success();
    }



}
