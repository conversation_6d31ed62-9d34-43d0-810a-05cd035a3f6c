package com.wantwant.sfa.backend.controller;

import com.wantwant.sfa.backend.captcha.api.CaptchaApi;
import com.wantwant.sfa.backend.captcha.request.CaptchaCheckRequest;
import com.wantwant.sfa.backend.captcha.request.CaptchaRequest;
import com.wantwant.sfa.backend.service.CaptchaService;
import com.wantwant.commons.web.response.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.regex.Pattern;

/**
 * @Author: luxiaoyin
 * @Date: 2020/7/15
 * @Package: com.wantwant.sfa.backend.controller
 */
@RestController
public class CaptchaController implements CaptchaApi {
    @Autowired
    private CaptchaService captchaService;

    @Override
    public Response add(CaptchaRequest captchaRequest) {
        if (!Pattern.matches("^[1]([3-9])[0-9]{9}$", captchaRequest.getMobile())) {
            return Response.error("不正确的手机号!");
        }
        return captchaService.addCaptcha(captchaRequest.getMobile());
    }

    @Override
    public Response check(CaptchaCheckRequest captchaCheckRequest) {
        if (!Pattern.matches("^[1]([3-9])[0-9]{9}$", captchaCheckRequest.getMobile())) {
            return Response.error("不正确的手机号!");
        }
        if(captchaService.check(captchaCheckRequest.getMobile(),captchaCheckRequest.getCode())){
            return Response.success();
        }else{
            return Response.error("验证码错误，或已过期！！");
        }
    }

    @Override
    public Response<String> get(CaptchaRequest captchaRequest){
        if (!Pattern.matches("^[1]([3-9])[0-9]{9}$", captchaRequest.getMobile())) {
            return Response.error("不正确的手机号!");
        }
        String code = captchaService.get(captchaRequest.getMobile());
        return Response.success(code);
    }

    @Override
    public Response<String> test(CaptchaRequest captchaRequest) {
        if (!Pattern.matches("^[1]([3-9])[0-9]{9}$", captchaRequest.getMobile())) {
            return Response.error("不正确的手机号!");
        }
        String code = captchaService.test(captchaRequest.getMobile());
        return Response.success(code);
    }
}
