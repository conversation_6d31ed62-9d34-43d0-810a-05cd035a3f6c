package com.wantwant.sfa.backend.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.businessGroup.service.impl.BusinessGroupService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.api.CommonApi;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.common.model.RegionModel;
import com.wantwant.sfa.backend.common.request.RegionRequest;
import com.wantwant.sfa.backend.common.request.RegionSearchRequest;
import com.wantwant.sfa.backend.common.request.RegionV2Request;
import com.wantwant.sfa.backend.common.service.IRegionI18nService;
import com.wantwant.sfa.backend.common.service.IRegionService;
import com.wantwant.sfa.backend.common.vo.BusinessAreaVo;
import com.wantwant.sfa.backend.common.vo.InternationalRegionVO;
import com.wantwant.sfa.backend.common.vo.OrgArchitectureVo;
import com.wantwant.sfa.backend.common.vo.OrgVo;
import com.wantwant.sfa.backend.dict.entity.SfaDictCode;
import com.wantwant.sfa.backend.dict.request.DictCodeRequest;
import com.wantwant.sfa.backend.dict.service.impl.DictCodeServiceImpl;
import com.wantwant.sfa.backend.dict.vo.DictCodeVo;
import com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.SfaPositionEmpMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.positionRegion.vo.RegionVo;
import com.wantwant.sfa.backend.service.ICustomerTypeService;
import com.wantwant.sfa.backend.service.impl.CustomerTypeServiceImpl;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.architecture.controller.interceptor.ControllerHandlerInterceptor;
import com.wantwant.sfa.common.architecture.log.AccessLogContext;
import com.wantwant.sfa.common.base.CommonConstant;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import model.CustomerType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: luxiaoyin
 * @Date: 2020/5/7
 * @Package: com.wantwant.sfa.backend.controller
 */
@RestController
@Slf4j
public class CommonController implements CommonApi {
    @Autowired
    ICustomerTypeService customerTypeService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private IRegionService regionService;
    @Autowired
    private IRegionI18nService regionI18nService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper relationMapper;
    @Autowired
    private SfaPositionEmpMapper sfaPositionEmpMapper;

    @Resource
    private BusinessGroupService businessGroupService;

    @Autowired
    private SettingServiceImpl settingService;
    @Autowired
    private DictCodeServiceImpl dictCodeServiceImpl;

    @Override
    public Response<List<DictCodeVo>> getListByClassCd(DictCodeRequest request) {
        List<DictCodeVo> list = new ArrayList<>();
        List<SfaDictCode> sfaDictCodeList = dictCodeServiceImpl.getListByClassCd(request.getClassCd());
        com.wantwant.sfa.backend.util.BeanUtils.copyProperties(sfaDictCodeList, list, SfaDictCode.class, DictCodeVo.class);
        return Response.success(list);
    }

    @Override
    public Response refresh(){
        settingService.fresh();
        customerTypeService.fresh();
        dictCodeServiceImpl.fresh();
        return Response.success();
    }

    @Override
    public Response<List<RegionVo>> list(RegionRequest request) {
        log.info("【获取省市区信息】request:{}",request);

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        // 获取组织类型
        String orgType = loginInfo.getOrganizationType();

        request.setBusinessGroup(loginInfo.getBusinessGroup());
//        if(!orgType.equals("zb")){
//            // 获取登陆人组织信息
//            List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new QueryWrapper<SfaPositionRelationEntity>()
//                    .eq("business_group", loginInfo.getBusinessGroup())
//                    .eq("position_type_id", loginInfo.getPositionTypeId())
//                    .eq("emp_id", request.getPerson())
//                    .eq("delete_flag", 0)
//                    .eq("status", 1)
//            );
//            if(CollectionUtils.isEmpty(positionRelationEntityList)){
//                throw new ApplicationException("组织信息获取失败");
//            }
//            List<String> sfaOrganizationIds = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getOrganizationCode).distinct().collect(Collectors.toList());
//            if (orgType.equals("varea") || orgType.equals("province")) {
//                sfaOrganizationIds =positionRelationEntityList.stream().map(SfaPositionRelationEntity::getAreaCode).distinct().collect(Collectors.toList());
//            }
//            List<OrganizationRelationModel> organizationRelationModels = organizationBindRelationMapper.selectList(new QueryWrapper<OrganizationRelationModel>()
//                    .in("sfa_org_code", sfaOrganizationIds).eq("status", 1));
//            List<String> orgCodes = organizationRelationModels.stream().map(OrganizationRelationModel::getOrgCode).distinct().collect(Collectors.toList());
//            request.setOrganizationIds(orgCodes);
//        }

        RegionModel region = new RegionModel();
        BeanUtils.copyProperties(request,region);
        region.setOrgType(loginInfo.getOrganizationType());

        Integer level = region.getLevel();
        region.setLevel(level + 1);
        List<RegionVo> regionVos=null;
        AccessLogContext accessLogContext = ControllerHandlerInterceptor.accessLogContextThreadLocal.get();
        if (!CommonConstant.REGION_INDONESIA.equals(accessLogContext.getRegion())) {
            regionVos = regionService.selectList(region);
        }else {
            regionVos = regionI18nService.selectList(region);
        }
        return Response.success(regionVos);
    }

    @Override
    public Response<List<RegionVo>> commonList(RegionV2Request request) {

        RegionModel region = new RegionModel();
        region.setLevel(request.getLevel());
        region.setMemberKey(request.getMemberKey());
        region.setParentCode(request.getParentCode());

        List<RegionVo> regionVos = regionService.selectListByMemberKey(region);
        return Response.success(regionVos);
    }

    @Override
    public Response<List<OrgVo>> list(String person, Integer notContainDepartment) {
        int notDepartment = 0;
        if(notContainDepartment != null) {
            notDepartment = notContainDepartment.intValue();
        }

        LoginModel loginModel = RequestUtils.getLoginInfo();

        // 根据对应工号、线别、岗位类型找到组织
        List<String> orgLists  = organizationMapper.getEmployeeOrganizationId(person, loginModel);
        if(CollectionUtils.isEmpty(orgLists)) {
            log.info("find organization error: {}", loginModel);
            return null;
        }
        List<OrgVo> list = organizationMapper.selectTree(orgLists, loginModel.getOrganizationType(), notDepartment, loginModel.getBusinessGroup(), loginModel.getChannel());

        return Response.success(list);
    }

    @Override
    public Response<OrgVo> list(String orgCode) {
        LoginModel loginModel = RequestUtils.getLoginInfo();
        // 根据对应工号、线别、岗位类型找到组织
        List<String> orgLists  = Arrays.asList(orgCode);
        if(CollectionUtils.isEmpty(orgLists)) {
            log.info("find organization error: {}", loginModel);
            return null;
        }
        String organizationType = organizationMapper.getOrganizationType(orgCode);
        List<OrgVo> list = organizationMapper.selectTree(orgLists,organizationType, 0, loginModel.getBusinessGroup(), loginModel.getChannel());
        //list包含上级信息-->剔除
        OrgVo traverse = new OrgVo();
        if(CollectionUtil.isNotEmpty(list)){
            //递归取值
            traverse = traverse(list, orgCode);
        }

        return Response.success(traverse);
    }

    private OrgVo traverse(List<OrgVo> list,String orgCode){
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        for(OrgVo children:list){
            if(orgCode.equals(children.getOrgCode())){
                return children;
            }else {
                return traverse(children.getChildren(),orgCode);
            }
        }
        return null;
    }

    @Override
    public Response<List<OrgVo>> allList(Integer notContainDepartment) {
        int notDepartment = 0;
        if(notContainDepartment != null) {
            notDepartment = notContainDepartment.intValue();
        }

        LoginModel loginModel = RequestUtils.getLoginInfo();

        List<OrgVo> list = organizationMapper.selectTree(null, null, notDepartment, loginModel.getBusinessGroup(), loginModel.getChannel());

        return Response.success(list);
    }

    @Override
    public Response<OrgArchitectureVo> architecturelist(String person,String organizationId, String employeeMessage) {
        log.info("start CommonController architecturelist person:{},organizationId:{},employeeMessage:{}",person,organizationId,employeeMessage);
        LoginModel loginModel = RequestUtils.getLoginInfo();
        if (StringUtils.isNotBlank(person) && StringUtils.isBlank(organizationId)) {
            List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(person, RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                organizationId=employeeOrganizationId.get(0);
            }
        }
        String departmentManagerEmpId = businessGroupService.getDepartmentManagerEmpId(loginModel.getBusinessGroup());
        EmployeeInfoVO employeeInfo = sfaPositionRelationMapper.selectZbEmployee(departmentManagerEmpId, loginModel.getBusinessGroup());
        OrgArchitectureVo orgArchitectureVo = new OrgArchitectureVo();
        if (Objects.nonNull(employeeInfo)) {
            orgArchitectureVo.setOrgEmployeeName(employeeInfo.getEmployeeName());
            orgArchitectureVo.setOrgCode(organizationMapper.selectOrganizationIdBybussinonGroup(loginModel.getBusinessGroup()));
            orgArchitectureVo.setOrgName("总部");
            orgArchitectureVo.setOrgUrl(employeeInfo.getAvatar());
            if (employeeMessage.contains("蔡") || employeeMessage.contains("旺") || employeeMessage.contains("祖") ||
                    employeeMessage.contains("蔡旺祖") || employeeMessage.contains("蔡旺") || employeeMessage.contains("旺祖")) {
                employeeMessage = "";
            }
        }

        //如果是总部返回先返回总部这一层数据
        List<OrgArchitectureVo> list = organizationMapper.selectArchitectureTree(organizationId, loginModel.getOrganizationType(), employeeMessage, loginModel.getBusinessGroup(), loginModel.getChannel());
        orgArchitectureVo.setChildren(list);

        return Response.success(orgArchitectureVo);
    }

    @Override
    public Response<List<OrgArchitectureVo>> architectureBranchlist(String person, String organizationId, String employeeMessage) {
        log.info("start CommonController architectureBranchlist person:{},organizationId:{},employeeMessage:{}",person,organizationId,employeeMessage);
        LoginModel loginModel = RequestUtils.getLoginInfo();

        List<OrgArchitectureVo> list = organizationMapper.selectArchitectureBranchTree(organizationId, loginModel.getOrganizationType(), employeeMessage, loginModel.getBusinessGroup(), loginModel.getChannel());

        return Response.success(list);
    }

    @Override
    public Response<List<BusinessAreaVo>> businessArea(Long memberKey) {
        log.info("【根据memberKey获取省市】memberKey:{}", memberKey);

        List<BusinessAreaVo> list = regionService.selectBusinessArea(memberKey);
        return Response.success(list);
    }

    @Override
    public Response<List<OrgVo>> orgList(String employeeId) {

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("employee_id", employeeId)
                .eq("channel", RequestUtils.getChannel())
        );
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("员工工号不存在");
        }

        List<OrgVo> list = organizationMapper.selectTreeByEmpId(employeeId,ceoBusinessOrganizationPositionRelation.getPositionTypeId(),RequestUtils.getChannel());

        return Response.success(list);
    }

    @Override
    public Response<List<InternationalRegionVO>> selectInternationalRegion(RegionSearchRequest regionSearchRequest) {
        log.info("select international region request:{}", JacksonHelper.toJson(regionSearchRequest,false));

        List<InternationalRegionVO> list = Optional.ofNullable(regionService.selectInternationalRegion(regionSearchRequest)).orElse(Collections.emptyList());

        return Response.success(list);
    }

    @Override
	public Response<List<CustomerType>> customerType() {

		List<CustomerType> list = (List<CustomerType>) redisUtil.get(CustomerTypeServiceImpl.CUSTOMER_TYPE);
		
		if(CollectionUtils.isEmpty(list)) {
			list = customerTypeService.getList();
		}
		
		return Response.success(list);
	}
}
