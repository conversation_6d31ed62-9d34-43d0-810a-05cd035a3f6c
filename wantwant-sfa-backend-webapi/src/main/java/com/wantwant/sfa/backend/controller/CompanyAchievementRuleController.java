package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.marketAndPersonnel.request.CompanyAchievementRuleRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyAchievementRuleVO;
import com.wantwant.sfa.backend.service.CompanyAchievementRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 绩效规则相关接口
 *
 * @date 4/19/22 2:42 PM
 * @version 1.0
 */
@Api(tags = "绩效规则相关接口")
@RestController
@RequestMapping("/companyAchievementRule")
public class CompanyAchievementRuleController {

	@Autowired
	private CompanyAchievementRuleService service;

	/**
	 * 绩效规则列表
	 *
	 * @param yyyyMM
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyAchievementRuleVO>>
	 * @date: 4/19/22 3:21 PM
	 */
	@ApiOperation(value = "绩效规则列表", notes = "绩效规则列表")
	@GetMapping(value = "/queryByList")
	public Response<List<CompanyAchievementRuleVO>> queryRuleByList(@ApiParam(value = "考核月份(yyyy-MM)") @RequestParam() String yyyyMM,
																	@ApiParam(value = "岗位(1:合伙人,2:总监)",required = true) @RequestParam() Integer position){
		return Response.success(service.queryRuleByList(yyyyMM,position));
	}

	/**
	 * 新增
	 *
	 * @param request
	 * @return: int
	 * @date: 4/19/22 3:55 PM
	 */
	@ApiOperation(value = "新增数据")
	@PostMapping
	public Response<Integer> saveRule(@Valid @RequestBody CompanyAchievementRuleRequest request) {
		return Response.success(service.saveRule(request));
	}

	/**
	 * 根据ID修改数据
	 *
	 * @param id
	 * @param request
	 * @return: int
	 * @date: 4/19/22 7:23 PM
	 */
	@ApiOperation(value = "根据ID修改数据")
	@PutMapping(value = "/{id}")
	public Response<Integer> updateRule(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,@Valid @RequestBody CompanyAchievementRuleRequest request) {
		return Response.success(service.updateRule(id,request));
	}

}
