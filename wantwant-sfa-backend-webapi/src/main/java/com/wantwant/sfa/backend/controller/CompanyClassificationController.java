package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.marketAndPersonnel.request.CompanyClassRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyClassVO;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyClassificationVO;
import com.wantwant.sfa.backend.service.CompanyClassificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 分公司考核分类相关接口
 *
 * <AUTHOR>
 * @date 4/18/22 2:22 PM
 * @version 1.0
 */
@Api(tags = "分公司考核分类相关接口")
@RestController
@RequestMapping("/companyClassification")
public class CompanyClassificationController {

	@Autowired
	private CompanyClassificationService service;

	/**
	 * 根据Id获取分公司分类信息
	 *
	 * @param organizationId
	 * @param effectiveDate
	 * @return: java.lang.String
	 * @date: 4/18/22 3:05 PM
	 */
	@ApiOperation(value = "获取分公司分类信息", notes = "BD列表")
	@GetMapping(value = "/{organizationId}")
	public Response<String> getCompanyById(@PathVariable("organizationId") @NotNull(message = "organizationId不能为空") String organizationId,
										   @ApiParam(value = "生效日期(yyyy-MM)") @RequestParam(value = "effectiveDate", required = false) String effectiveDate){
		return Response.success(service.getCompanyById(organizationId,effectiveDate));
	}

	/**
	 * 初始化分公司分类(12个月)
	 *
	 * @param
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 4/18/22 7:35 PM
	 */
	@GetMapping(value = "/initial")
	public Response<Integer> initialCompany(Integer type){
		return Response.success(service.initialCompany(type));
	}

	/**
	 * 分公司考核分类列表
	 *
	 * @param classificationLast
	 * @param classification
	 * @param classificationNext
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyClassificationVO>>
	 * @date: 4/18/22 9:31 PM
	 */
	@ApiOperation(value = "分公司考核分类列表", notes = "分公司考核分类列表")
	@GetMapping(value = "/queryByList")
	public Response<List<CompanyClassificationVO>> queryCompanyByList(@ApiParam(value = "上月考核分类") @RequestParam() String classificationLast,
																	  @ApiParam(value = "本月考核分类") @RequestParam() String classification,
																	  @ApiParam(value = "下月考核分类") @RequestParam() String classificationNext){
		return Response.success(service.queryCompanyByList(classificationLast,classification,classificationNext));
	}

	/**
	 * 根据分公司ID修改
	 *
	 * @param organizationId 分公司id
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 4/18/22 10:56 PM
	 */
	@ApiOperation(notes = "根据分公司ID修改", value = "根据分公司ID修改")
	@PutMapping("update/{organizationId}")
	public Response<Integer> updateByOrganizationId(@ApiParam(value = "报名ID", required = true) @PathVariable("organizationId") @NotNull(message = "organizationId不能为空") String organizationId,
													@Valid @RequestBody CompanyClassRequest request) {
		return Response.success(service.updateByOrganizationId(organizationId,request));
	}

	/**
	 * 批量导入
	 *
	 * @param file
	 * @param updatedBy
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<java.lang.String>>
	 * @date: 4/19/22 11:28 AM
	 */
	@ApiOperation(notes = "批量导入", value = "批量导入")
	@PostMapping("/import")
	public Response<List<String>> importCompany(@RequestParam(value = "file") MultipartFile file,
												@ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy){
		return Response.success(service.importCompany(file,updatedBy));
	}

	/**
	 * 分公司分类下拉框
	 *
	 * @param classification
	 * @param effectiveDate
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.marketAndPersonnel.vo.CompanyClassVO>>
	 * @date: 4/20/22 10:35 AM
	 */
	@ApiOperation(value = "分公司分类下拉框", notes = "分公司分类下拉框")
	@GetMapping(value = "/select")
	public Response<List<CompanyClassVO>> selectCompany(@ApiParam(value = "考核分类") @RequestParam() String classification,
														@ApiParam(value = "生效日期(yyyy-MM)") @RequestParam() String effectiveDate){
		return Response.success(service.selectCompany(classification,effectiveDate));
	}



}
