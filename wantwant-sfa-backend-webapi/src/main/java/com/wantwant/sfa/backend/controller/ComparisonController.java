package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.request.ComparisonForAreaRequest;
import com.wantwant.sfa.backend.customer.request.ComparisonForPartnerRequest;
import com.wantwant.sfa.backend.customer.request.ComparisonForProportionRequest;
import com.wantwant.sfa.backend.customer.request.ComparisonForSkuRequest;
import com.wantwant.sfa.backend.customer.vo.*;
import com.wantwant.sfa.backend.service.ComparisonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "总督导同期看板API")
public class ComparisonController {

    @Resource
    private ComparisonService comparisonService;

    @ApiOperation(value = "总督导同期报告", notes = "总督导同期报告", httpMethod = "POST")
    @PostMapping("/comparison/queryAreaOrNext")
    Response<List<ComparisonVO>> queryComparisonForArea(@RequestBody @Validated ComparisonForAreaRequest request) {
        return Response.success(comparisonService.queryComparison(request));
    }

    @ApiOperation(value = "合伙人同期报告", notes = "合伙人同期报告", httpMethod = "POST")
    @PostMapping("/comparison/queryPartner")
    Response<IPage<ComparisonForPartnerVO>> queryComparisonForPartner(@RequestBody ComparisonForPartnerRequest request) {
        return Response.success(comparisonService.queryComparisonForPartner(request));
    }

    @ApiOperation(value = "合伙人同期报告-导出", notes = "合伙人同期报告-导出", httpMethod = "POST")
    @PostMapping("/export/comparison/queryPartner")
    void exportComparisonForPartner(@RequestBody ComparisonForPartnerRequest request) {
        comparisonService.exportComparisonForPartner(request);
    }

    @ApiOperation(value = "sku同期报告", notes = "sku同期报告", httpMethod = "POST")
    @PostMapping("/comparison/querySku")
    Response<IPage<ComparisonSkuAnalyse>> queryComparisonForSku(@RequestBody ComparisonForSkuRequest request) {
        return Response.success(comparisonService.queryComparisonForSku(request));
    }

    @ApiOperation(value = "sku同期报告-导出", notes = "sku同期报告", httpMethod = "POST")
    @PostMapping("/comparison/exportSku")
    void exportComparisonForSku(@RequestBody ComparisonForSkuRequest request) {
        comparisonService.exportComparisonForSku(request);
    }


    @ApiOperation(value = "spu同期报告", notes = "spu同期报告", httpMethod = "POST")
    @PostMapping("/comparison/querySpu")
    Response<IPage<ComparisonSpuAnalyse>> queryComparisonForSpu(@RequestBody ComparisonForSkuRequest request) {
        return Response.success(comparisonService.queryComparisonForSpu(request));
    }

    @ApiOperation(value = "spu同期报告-导出", notes = "spu同期报告-导出", httpMethod = "POST")
    @PostMapping("/comparison/exportSpu")
    void exporComparisonForSpu(@RequestBody ComparisonForSkuRequest request) {
        comparisonService.exporComparisonForSpu(request);
    }

    @ApiOperation(value = "产品线同期报告", notes = "产品线同期报告", httpMethod = "POST")
    @PostMapping("/comparison/queryLine")
    Response<IPage<ComparisonLineAnalyse>> queryComparisonForLine(@RequestBody ComparisonForSkuRequest request) {
        return Response.success(comparisonService.queryComparisonForLine(request));
    }


    @ApiOperation(value = "产品线同期报告-导出", notes = "产品线同期报告-导出", httpMethod = "POST")
    @PostMapping("/comparison/exportLine")
    void exportComparisonForLine(@RequestBody ComparisonForSkuRequest request) {
        comparisonService.exportComparisonForLine(request);
    }

    @ApiOperation(value = "商品数据分析", notes = "商品数据分析", httpMethod = "POST")
    @PostMapping("/comparison/queryProportion")
    Response<List<ComparisonProportionListVO>> queryComparisonForProportion(@RequestBody ComparisonForProportionRequest request) {
        return Response.success(comparisonService.queryComparisonForProportion(request));
    }



    @ApiOperation(value = "总督导同期报告导出", notes = "总督导同期报告导出", httpMethod = "POST")
    @PostMapping("/comparison/exportAreaOrNext")
    void queryComparisonForAreaExport(@RequestBody ComparisonForAreaRequest request) {
        comparisonService.queryComparisonForAreaExport(request);
    }



}
