package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.request.*;
import com.wantwant.sfa.backend.customer.vo.*;
import com.wantwant.sfa.backend.service.CustomerApproveService;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.concurrent.TimeUnit;

/**
 * 客户审批相关Controller
 *
 * @date 2022-02-18 20:01
 * @version 1.0
 */
@Slf4j
@Api(tags = "客户审批相关")
@RestController
@RequestMapping("/customerApprove")
public class CustomerApproveController {

    private static final String CUSTOMER_INFO_AUDIT_LOCK = "customerInfoAudit:lock";

    @Autowired
    private CustomerApproveService service;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 客户审核列表
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.customer.vo.CustomerApproveVO>>
     * @date: 2022-02-19 13:14
     */
    @ApiOperation(value = "客户审核列表", notes = "客户审核列表")
    @GetMapping(value = "/queryByPage")
    public Response<IPage<CustomerApproveVO>> queryCustomerApproveByPage(CustomerApproveRequest query) {
        return Response.success(service.queryCustomerApproveByPage1(query));
    }


    @ApiOperation(value = "陈列客户审核列表", notes = "陈列客户审核列表")
    @GetMapping(value = "/queryDisplayByPage")
    public Response<IPage<DisplayCustomerApproveVO>> queryDisplayCustomerApproveByPage(DisplayCustomerApproveRequest query) {
        return Response.success(service.queryDisplayCustomerApproveByPage(query));
    }
    /**
     * 客户审核列表导出
     *
     * @param query
     * @return: void
     * @date: 2022-02-19 20:47
     */
    @ApiOperation(value = "客户审核列表导出", notes = "客户审核列表导出")
    @GetMapping(value = "/export")
    public void exportList(CustomerApproveRequest query) {
        service.exportList(query);
    }

    /**
     * 客户审核列表导出
     *
     * @param query
     * @return: void
     * @date: 2022-02-19 20:47
     */
    @ApiOperation(value = "陈列客户审核列表导出", notes = "客户审核列表导出")
    @GetMapping(value = "/displayExport")
    public void displayExportList(DisplayCustomerApproveRequest query) {
        service.displayExportList(query);
    }
    /**
     * 根据ID审核详情
     *
     * @param customerId 客户编号
     * @return: com.wantwant.sfa.backend.customer.vo.CustomerApproveVO
     * @date: 2022-02-19 17:12
     */
    @ApiOperation(value = "审核详情",notes = "审核详情")
    @GetMapping(value = "/{customerId}")
    public Response<CustomerApproveDetailVO> getCustomerApproveById(@PathVariable("customerId") @NotNull(message = "id不能为空") String customerId, @RequestParam("person") @NotNull(message = "id不能为空") String person) {
        return Response.success(service.getCustomerApproveById(customerId, person));
    }

    /**
     * 陈列客户ID审核详情
     *
     * @param displayCustomerId 陈列客户编号
     * @return: com.wantwant.sfa.backend.customer.vo.CustomerApproveVO
     * @date: 2022-02-19 17:12
     */
    @ApiOperation(value = "陈列客户审核详情",notes = "陈列客户审核详情")
    @GetMapping(value = "/displayCustomer/{displayCustomerId}")
    public Response<DisplayCustomerApproveDetailVO> getDisplayCustomerApproveById(@PathVariable("displayCustomerId") @NotNull(message = "id不能为空") String displayCustomerId, @RequestParam("person") @NotNull(message = "id不能为空") String person) {
        return Response.success(service.getDisplayCustomerApproveById(displayCustomerId, person));
    }

    /**
     * 客户审批
     *
     * @date 2/20/22 8:51 PM
     * @version 1.0
     */
    @ApiOperation(notes = "客户审批", value = "客户审批")
    @PutMapping("/audit")
    public Response customerAudit(@Valid @RequestBody CustomerAuditRequest request) {
        request.setSource(0);
        log.info("客户审批接口入参：{}", request);
        if (request.getCustomerStatus() != 2 && request.getCustomerStatus() != 3) {
            throw new ApplicationException("传入客户审批状态不正确");
        }

        if(!redisUtil.setLockIfAbsent(CUSTOMER_INFO_AUDIT_LOCK, request.getCustomerId(),10, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！");
        }
        try {
            service.customerInfoAudit(request);
        } finally {
            redisUtil.unLock(CUSTOMER_INFO_AUDIT_LOCK, request.getCustomerId());
        }
        return Response.success();
    }

    /**
     * 陈列客户审批
     *
     * @date 2/20/22 8:51 PM
     * @version 1.0
     */
    @ApiOperation(notes = "陈列客户审批", value = "客户审批")
    @PutMapping("/displayCustomer/audit")
    public Response displayCustomerAudit(@Valid @RequestBody DisplayCustomerAuditRequest request) {
        request.setSource(0);
        log.info("客户审批接口入参：{}", request);
        if (request.getCustomerStatus() != 2 && request.getCustomerStatus() != 3) {
            throw new ApplicationException("传入客户审批状态不正确");
        }
        if(!redisUtil.setLockIfAbsent(CUSTOMER_INFO_AUDIT_LOCK, request.getCustomerId(),10, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！");
        }
        try {
            //审核方法
            service.displayCustomerAudit(request);
        } finally {
            redisUtil.unLock(CUSTOMER_INFO_AUDIT_LOCK, request.getCustomerId());
        }
        return Response.success();
    }


    @ApiOperation(notes = "陈列客户提审",value = "陈列客户提审")
    @PostMapping("/displayApprove")
    public Response displayApprove(@RequestBody DisplayCustomerRequest request){

        service.displayApprove(request);

        return Response.success();
    }


    @ApiOperation(notes = "客户审核上级",value = "客户审核上级")
    @GetMapping("/checkCustomerAuditor")
    public Response<CheckCustomerAuditor> checkCustomerAuditor(@RequestParam("customerId") @NotNull(message = "id不能为空") String customerId) {
        return Response.success(service.checkCustomerAuditor(customerId));
    }





}
