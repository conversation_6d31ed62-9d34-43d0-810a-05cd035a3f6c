package com.wantwant.sfa.backend.controller;

import com.google.common.collect.Maps;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.annotation.Exclude;
import com.wantwant.sfa.backend.enums.CustomerLabelEnum;
import com.wantwant.sfa.backend.labels.api.CustomerLabelApi;
import com.wantwant.sfa.backend.labels.request.CustomerLabelTableRequest;
import com.wantwant.sfa.backend.labels.service.CustomerLabelTableService;
import com.wantwant.sfa.backend.labels.vo.CustomerLabelVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.Map;


@Slf4j
@RestController
public class CustomerLabelController implements CustomerLabelApi {

    @Autowired
    private CustomerLabelTableService customerLabelTableService;

    @Override
    public Response<Page<CustomerLabelVo>> customerLabels(CustomerLabelTableRequest request) {
        return customerLabelTableService.getTable(request);
    }

    @Override
    public Response<Map<String, String>> lifecycleInfo() {
        Map<String,String> result = Maps.newHashMap();
        for (Field field : CustomerLabelEnum.class.getFields()) {
            if(null == field.getAnnotation(Exclude.class)){
                result.put(field.getName(),CustomerLabelEnum.valueOf(field.getName()).getInfo());
            }
        }
        return Response.success(result);
    }

    @Override
    public void Export(CustomerLabelTableRequest request) {
        try{
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        Assert.notNull(servletRequestAttributes,"系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        Workbook wb = customerLabelTableService.exportToWb(request);
            String fileName = "客户分析列表" + ObjectUtils.getDisplayString(request.getStartDate())+"-"+ObjectUtils.getDisplayString(request.getEndDate());
            if(wb instanceof HSSFWorkbook){
                fileName=fileName+".xls";
            }else{
                fileName=fileName+".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident") ) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            log.error("导出失败",e);
            throw new ApplicationException("导出失败！！！");
        }
    }
}
