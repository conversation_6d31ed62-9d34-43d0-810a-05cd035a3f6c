package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.vo.CustomerBusinessVO;
import com.wantwant.sfa.backend.customer.vo.PartnerInfoVO;
import com.wantwant.sfa.backend.dataModify.request.DataModifyQueryRequest;
import com.wantwant.sfa.backend.dataModify.request.DataModifySaveRequest;
import com.wantwant.sfa.backend.dataModify.request.ModifyAuditRequest;
import com.wantwant.sfa.backend.dataModify.request.QueryPartnerRequest;
import com.wantwant.sfa.backend.dataModify.vo.DataModifyDetailVO;
import com.wantwant.sfa.backend.dataModify.vo.DataModifyVO;
import com.wantwant.sfa.backend.service.DataModifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 资料修改相关接口
 *
* @since 2022-06-08
*/
@Api(tags = "资料修改相关接口")
@RestController
@RequestMapping("/dataModify")
public class DataModifyController {

	@Autowired
	private DataModifyService dataModifyService;

	/**
	 * 提交处理
	 * 旺铺调用入口
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 6/8/22 5:32 PM
	 */
	@ApiOperation(value = "提交处理")
	@PostMapping
	public Response<Integer> saveAndProcess(@Valid @RequestBody List<DataModifySaveRequest> request) {
		return Response.success(dataModifyService.saveAndProcess(request));
	}

	/**
	 * 获取合伙人信息
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.customer.vo.CustomerBusinessVO>
	 * @date: 6/22/22 9:47 AM
	 */
	@ApiOperation(notes = "获取合伙人信息", value = "获取合伙人信息")
	@GetMapping("/queryPartnerInfo")
	public Response<PartnerInfoVO> queryPartnerInfo(QueryPartnerRequest request) {
		return Response.success(dataModifyService.queryPartnerInfo(request));
	}

	/**
	 * 资料审核列表
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.dataModify.vo.DataModifyVO>>
	 * @date: 6/9/22 2:19 PM
	 */
	@ApiOperation(notes = "资料审核列表", value = "资料审核列表")
	@PostMapping("/queryByPage")
	public Response<IPage<DataModifyVO>> queryByPage(@RequestBody DataModifyQueryRequest request) {
		return Response.success(dataModifyService.queryByPage(request));
	}

	/**
	 * 修改审批
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 6/9/22 10:44 PM
	 */
	@ApiOperation(notes = "修改审批", value = "修改审批")
	@RequestMapping(value = "/audit",method = {RequestMethod.POST,RequestMethod.PUT})
	public Response audit(@Valid @RequestBody ModifyAuditRequest request) {
		return dataModifyService.audit(request);
	}

	/**
	 * 审批详情
	 *
	 * @param applicationNo
	 * @param employeeId
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.dataModify.vo.DataModifyDetailVO>
	 * @date: 6/10/22 6:58 PM
	 */
	@ApiOperation(notes = "审批详情", value = "审批详情")
	@GetMapping("/auditDetails/{applicationNo}/{employeeId}")
	public Response<DataModifyDetailVO> auditDetails(@PathVariable("applicationNo") @NotNull(message = "applicationNo不能为空") String applicationNo,
													 @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
		return Response.success(dataModifyService.auditDetails(applicationNo,employeeId));
	}

	/**
	 * 历史审批详情
	 *
	 * @param userId
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.dataModify.vo.DataModifyDetailVO>>
	 * @date: 6/10/22 7:57 PM
	 */
	@ApiOperation(notes = "历史审批详情", value = "历史审批详情")
	@GetMapping("/auditDetailsHistory/{applicationNo}/{userId}")
	public Response<List<DataModifyDetailVO>> auditDetailsHistory(@PathVariable("applicationNo") @NotNull(message = "applicationNo不能为空") String applicationNo,
																  @PathVariable("userId") @NotNull(message = "userId不能为空") String userId) {
		return Response.success(dataModifyService.auditDetailsHistory(applicationNo,userId));
	}

	/**
	 * 获取业务信息
	 *
	 * @param memberKey
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.customer.vo.CustomerBusinessVO>
	 * @date: 6/11/22 11:02 AM
	 */
	@ApiOperation(notes = "获取业务信息", value = "获取业务信息")
	@GetMapping("/businessInfo/{memberKey}")
	public Response<CustomerBusinessVO> businessInfo(@PathVariable("memberKey") @NotNull(message = "memberKey不能为空") String memberKey) {
		return Response.success(dataModifyService.businessInfo(memberKey));
	}


}
