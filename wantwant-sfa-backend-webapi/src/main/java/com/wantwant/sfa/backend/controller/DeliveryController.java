package com.wantwant.sfa.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.delivery.api.DeliveryApi;
import com.wantwant.sfa.backend.delivery.request.DeliveryAbnormalListRequest;
import com.wantwant.sfa.backend.delivery.request.DeliveryAbnormalTasksRequest;
import com.wantwant.sfa.backend.delivery.request.UpdateReceivedAtRequest;
import com.wantwant.sfa.backend.delivery.vo.OrderLogisticsVo;
import com.wantwant.sfa.backend.delivery.vo.OrderDeliveryVo;
import com.wantwant.sfa.backend.service.IDeliveryService;

import lombok.extern.slf4j.Slf4j;


@RestController
@Slf4j
public class DeliveryController implements DeliveryApi {
	
	@Autowired
	private IDeliveryService deliveryService;

	@Override
	public Response executeDeliveryAbnormalTasks(DeliveryAbnormalTasksRequest request) {
		log.info("start DeliveryController executeDeliveryAbnormalTasks request:{}",request);
		deliveryService.deliveryAbnormal(request.getDate());
		return Response.success();

	}

	@Override
	public Response<Page<OrderLogisticsVo>> logisticsAbnormalList(DeliveryAbnormalListRequest request) {
		Page<OrderLogisticsVo> page = deliveryService.logisticsAbnormalList(request);
		return Response.success(page);
	}

	@Override
	public Response<Page<OrderDeliveryVo>> deliveryAbnormalList(DeliveryAbnormalListRequest request) {
		Page<OrderDeliveryVo> page = deliveryService.deliveryAbnormalList(request);
		return Response.success(page);
	}

	@Override
	public Response updateLogisticsReceivedAt(UpdateReceivedAtRequest request) {
		if(request.getAbnormalId() == null || request.getReceivedAt() == null) {
			throw new ApplicationException("请选择正确的收货时间");
		}
		deliveryService.updateLogisticsReceivedAt(request);
		return Response.success();
	}


}
