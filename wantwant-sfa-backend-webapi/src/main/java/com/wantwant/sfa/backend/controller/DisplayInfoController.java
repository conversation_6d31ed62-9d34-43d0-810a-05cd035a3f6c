package com.wantwant.sfa.backend.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.util.PoiPublicUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.Task.DisplayTask;
import com.wantwant.sfa.backend.afterSales.vo.OrgQuotaSurplusVO;
import com.wantwant.sfa.backend.display.dto.DisplayProcessInfoDTO;
import com.wantwant.sfa.backend.display.request.*;
import com.wantwant.sfa.backend.display.vo.*;
import com.wantwant.sfa.backend.service.DisplayInfoService;
import com.wantwant.sfa.backend.service.DisplayProcessService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
* 特陈相关接口
*
* @since 2022-05-18
*/
@Slf4j
@Api(tags = "特陈相关接口")
@RestController
@RequestMapping("/display")
public class DisplayInfoController {

	@Autowired
	private DisplayInfoService service;

	@Autowired
	private DisplayProcessService processService;

	@Autowired
	private DisplayTask displayTask;


	/**
	 * 根据申请编号更新或保存特陈数据
	 *
	 * @param request
	 * @return: int
	 * @date: 5/18/22
	 */
	@ApiOperation(value = "根据申请编号更新或保存特陈数据")
	@PostMapping(value = "/saveOrUpdate")
	public Response<Integer> saveOrUpdate(@Valid @RequestBody DisplayInfoRequest request) {
		return Response.success(service.saveOrUpdateByNo(request));
	}

	/**
	 * 根据memberKey获取分公司剩余额度
	 * 提供旺铺-王苏斌,徐藤
	 *
	 * @param memberKey
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.SurplusQuotaVO>
	 * @date: 3/8/23 6:27 PM
	 */
	@ApiOperation(value = "获取分公司剩余额度")
	@GetMapping(value = "/querySurplusQuota/{memberKey}")
	public Response<SurplusQuotaVO> querySurplusQuota(@PathVariable("memberKey") @NotNull(message = "memberKey不能为空") String memberKey){
		return Response.success(service.querySurplusQuota(memberKey));
	}

	/**
	 * 根据申请编号获取审批节点
	 * 提供旺铺-王苏斌
	 *
	 * @param applicationNo
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.DisplayProcessInfoVO>
	 * @date: 7/10/23 7:00 PM
	 */
    @ApiOperation(value = "根据申请编号获取审批节点")
    @GetMapping(value = "/processInfo/{applicationNo}")
    public Response<DisplayProcessInfoVO> processInfo(@PathVariable("applicationNo") @NotNull(message = "applicationNo不能为空") String applicationNo){
        return Response.success(service.processInfo(applicationNo));
    }

	/**
	 * 特陈取消
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 7/21/22 9:46 AM
	 */
	@ApiOperation(value = "特陈取消")
	@PostMapping(value = "/cancel")
	public Response<Integer> cancel(@Valid @RequestBody DisplayCancelRequest request) {
		return Response.success(service.cancel(request));
	}

	/**
	 * 获取分公司额度
	 *
	 * @param companyOrganizationId 分公司OrganizationId
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.CompanyQuotaVO>
	 * @date: 5/19/22 3:11 PM
	 */
	@ApiOperation(value = "获取分公司额度")
	@GetMapping(value = "/queryCompanyQuota/{companyOrganizationId}")
	public Response<CompanyQuotaVO> queryCompanyQuota(@PathVariable("companyOrganizationId") @NotNull(message = "organizationId不能为空") String companyOrganizationId){
		return Response.success(service.queryCompanyQuota(companyOrganizationId));
	}

	/**
	 * 获取分公司/合伙人/陈列客户额度
	 * 有规则
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.QuotaVO>
	 * @date: 9/20/22 3:00 PM
	 */
	@ApiOperation(value = "获取分公司/合伙人/陈列客户额度")
	@GetMapping(value = "/queryQuota")
	public Response<QuotaVO> queryQuota(QuotaRequest request){
		return Response.success(service.queryQuota(request));
	}

	/** 
	 * 无规则显示费用分析
	 */
	@ApiOperation(value = "费用分析")
	@PostMapping(value = "/costAnalysis")
	public Response<CostAnalysisInfoVO> costAnalysis(@RequestBody @Valid QuotaRequest request){
		return Response.success(service.costAnalysis(request));
	}

	/**
	 * 特称审批列表
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.display.vo.DisplayInfoVO>>
	 * @date: 5/19/22 6:05 PM
	 */
	@ApiOperation(notes = "特陈审批列表", value = "特陈审批列表")
	@GetMapping("/queryByPage")
	public Response<IPage<DisplayInfoVO>> queryByPage(DisplayQueryRequest request) {
		return Response.success(service.queryByPage(request));
	}

	/**
	 * 特陈审批列表导出
	 *
	 * @param request
	 * @return: void
	 * @date: 6/1/22 9:34 AM
	 */
	@ApiOperation(value = "特陈审批列表导出", notes = "特陈审批列表导出")
	@GetMapping(value = "/export")
	public void exportList(DisplayQueryRequest request) {
		service.exportList(request);
	}

	/**
	 * 特陈审批
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Boolean>
	 * @date: 5/19/22 10:54 PM
	 */
	@ApiOperation(notes = "特陈审批", value = "特陈审批")
	@RequestMapping(value = "/audit",method = {RequestMethod.POST,RequestMethod.PUT})
	public Response audit(@Valid @RequestBody DisplayAuditRequest request) {
		log.info("特陈审批:{}",request);
		DisplayProcessInfoDTO processInfo = processService.getDisplayProcessByProcessId(request.getProcessDetailId());
		//审批通过校验分公司额度
		if (Objects.nonNull(processInfo)) {
			CompanyQuotaVO companyQuotaVO = null;
			if (processInfo.getRuleType() == 1) {
				companyQuotaVO = service.queryCompanyQuota(processInfo.getCompanyOrganizationId());
			}
			return processService.audit(request,companyQuotaVO);
		}else {
			return Response.error("当前数据已处理！");
		}
	}

	@ApiOperation(notes = "特陈额度修改", value = "特陈额度修改")
	@PostMapping(value = "/updateQuota")
	public Response<Integer> updateQuota(@Valid @RequestBody UpdateQuotaRequest quotaRequest) {
		log.info("特陈额度修改:{}",quotaRequest);
		return Response.success(processService.updateQuota(quotaRequest));
	}

	/**
	 * 特陈批量驳回
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 7/10/23 7:40 PM
	 */
	@ApiOperation(notes = "特陈批量驳回", value = "特陈批量驳回")
	@PostMapping("/batchRejection")
	public Response batchRejection(@Valid @RequestBody BatchRejectionRequest request) {
		log.info("特陈批量驳回:{}",request);
		return processService.batchRejection(request);
	}

	/**
	 * 特陈批量审批
     *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.DisplayBatchVO>
	 * @date: 8/8/22 11:42 AM
	 */
	@ApiOperation(notes = "特陈批量审批", value = "特陈批量审批")
	@PutMapping("/batchAudit")
	public Response<DisplayBatchVO> batchAudit(@Valid @RequestBody BatchAuditRequest request) {
//		return Response.success(processService.batchAudit(request));
		displayTask.displayGrantTask("");
		return Response.success();
	}

	/**
	 * 特陈批量审批异常导出
	 *
	 * @param failList
	 * @return: void
	 * @date: 8/8/22 2:34 PM
	 */
	@ApiOperation(notes = "特陈批量审批异常导出", value = "特陈批量审批异常导出")
	@PostMapping("/exportFail")
	public void exportFailList(@RequestBody List<DisplayBatchFailVO> failList,HttpServletResponse response){
		EasyPoiUtil.exportExcel(failList,null,"sheet",DisplayBatchFailVO.class,"审批异常.xls",response);
	}

	/**
	 * 审批详情
	 *
	 * @param id
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.DisplayAuditDetailVO>
	 * @date: 5/21/22 10:14 AM
	 */
	@ApiOperation(notes = "审批详情", value = "审批详情")
	@GetMapping("/auditDetails/{id}/{employeeId}")
	public Response<DisplayAuditDetailVO> auditDetails(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
													   @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
		return Response.success(service.auditDetails(id,employeeId));
	}

	/**
	 * 历史审批详情
	 *
	 * @param id
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.display.vo.DisplayAuditDetailVO>
	 * @date: 5/21/22 10:14 AM
	 */
	@ApiOperation(notes = "历史审批详情", value = "历史审批详情")
	@GetMapping("/auditDetailsHistory/{id}")
	public Response<List<DisplayAuditDetailVO>> auditDetailsHistory(@PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
		return Response.success(processService.auditDetailsHistory(id));
	}

	/**
	 * 获取岗位信息
	 * 1:大区主管,2:区域经理,3:客服,4:业务支持,5:运营
	 *
	 * @param employeeId
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 7/7/22 10:31 AM
	 */
	@ApiOperation(notes = "获取岗位信息", value = "获取岗位信息")
	@GetMapping("/queryPosition/{employeeId}")
	public Response<Integer> queryPosition(@PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
		return Response.success(service.queryPosition(employeeId));
	}

	/**
	 * 总部稽核异常标记陈列不存在
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 2/27/23 1:51 PM
	 */
	@ApiOperation(notes = "总部稽核异常标记陈列不存在", value = "总部稽核异常标记陈列不存在")
	@PostMapping("/anomaly")
	public Response anomaly(@Valid @RequestBody CheckAnomalyRequest request) {
		return processService.anomaly(request);
	}


	@ApiOperation(notes = "旺金币额度", value = "旺金币额度")
	@GetMapping("/quotaSurplus/{id}")
	public Response<OrgQuotaSurplusVO> quotaSurplus(@PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
		return Response.success(service.quotaSurplus(id));
	}

	@GetMapping("/testExport")
	public void testExport(HttpServletRequest request, HttpServletResponse response) {
		//测试数据
		List<ExportVO> list = new ArrayList<>();
		ExportVO vo =new ExportVO();
		vo.setUserName("测试1");
		vo.setAmount(new BigDecimal("100"));
		vo.setDate(DateUtil.format(new Date(), "yyyy-MM年-dd日"));
		vo.setRank(1);
		List<ExportSkuVO> skuList = new ArrayList<>();
		skuList.add(new ExportSkuVO("","主推1",new BigDecimal(12),BigDecimal.ZERO,BigDecimal.ZERO));
		skuList.add(new ExportSkuVO("","主推2",new BigDecimal(20),BigDecimal.ZERO,BigDecimal.ZERO));
		vo.setSku(skuList);
		list.add(vo);
		ExportVO vo1 =new ExportVO();
		vo1.setUserName("测试2");
		vo1.setAmount(new BigDecimal("110"));
		vo1.setDate(DateUtil.format(new Date(), "yyyy-MM年-dd日"));
		vo1.setRank(2);
		list.add(vo1);

		//转成List<Map>
		List<Map<String, Object>> list1 = new ArrayList<Map<String, Object>>();
		list.forEach(e -> {
			Map<String, Object> valMap = BeanUtils.beanToMap(e);
			List<ExportSkuVO> sku = e.getSku();
			if (CommonUtil.ListUtils.isNotEmpty(sku)) {
				sku.forEach(k -> {
					valMap.put(k.getName()+"yj", k.getAmount());
					valMap.put(k.getName()+"mb", k.getTarget());
					valMap.put(k.getName()+"dc", k.getRate());
				});
			}
			list1.add(valMap);
		});
		//把我们构造好的bean对象放到params就可以了
		/*Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "测试"), entity,list1);
		EasyPoiUtil.downLoadExcel("导出.xls",response,workbook);*/
		//解析ExportVO属性上@Excel注解
		List<ExcelExportEntity> entity1 = new ArrayList<>();
		Field[] fields = PoiPublicUtil.getClassFields(ExportVO.class);
		for (Field field : fields) {
			if (field.getAnnotation(Excel.class) != null){
				Excel excel = field.getAnnotation(Excel.class);
				if (StringUtils.isNotBlank(excel.name())) {
					//属性类型是List做特殊处理 -是否主推list
					if(field.getType() == java.util.List.class){
						for (int i = 0; i < skuList.size(); i++) {
							ExportSkuVO v = skuList.get(i);
							ExcelExportEntity yj = new ExcelExportEntity("业绩",v.getName()+"yj");
							yj.setGroupName(v.getName());
							yj.setOrderNum(Integer.parseInt(excel.orderNum()) + i * 3);//导出顺序 5,8
							entity1.add(yj);
							ExcelExportEntity mb = new ExcelExportEntity("目标",v.getName()+"mb");
							mb.setGroupName(v.getName());
							mb.setOrderNum(Integer.parseInt(excel.orderNum()) + i * 3 + 1); //6,9
							entity1.add(mb);
							ExcelExportEntity dc = new ExcelExportEntity("业绩达成率",v.getName()+"dc");
							dc.setGroupName(v.getName());
							dc.setOrderNum(Integer.parseInt(excel.orderNum()) + i * 3 + 2); //7,10
							entity1.add(dc);
						}
					}else{
						ExcelExportEntity excelEntity = new ExcelExportEntity(excel.name(),field.getName());
						excelEntity.setOrderNum(Integer.parseInt(excel.orderNum()));
						if (StringUtils.isNotEmpty(excel.groupName())) {
							excelEntity.setGroupName(excel.groupName());
						}
						entity1.add(excelEntity);
					}
				}
			}
		}

		Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "测试"), entity1,list1);
		EasyPoiUtil.downLoadExcel("导出.xls",response,workbook);
	}


	@PostMapping("/check")
	@ApiOperation(notes = "稽核审核", value = "稽核审核")
	public Response check(@Valid @RequestBody DisplayCheck displayCheck){
		log.info("【display info check】request:{}", JSONArray.toJSONString(displayCheck));

		processService.check(displayCheck);

		return Response.success();
	}


}
