package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.displayRule.request.*;
import com.wantwant.sfa.backend.displayRule.vo.DisplayRuleVO;
import com.wantwant.sfa.backend.service.DisplayRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
* 特陈规则相关接口
 *
* @since 2023-05-05
*/
@Api(tags = "特陈规则相关接口")
@RestController
@RequestMapping("/displayRule")
public class DisplayRuleController {

    @Autowired
    private DisplayRuleService displayRuleService;

    /**
     * 按仓接收特陈规则
     * 提供旺铺-王苏斌
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 5/6/23 9:51 AM
     */
    @ApiOperation(value = "按仓接收特陈规则")
    @PostMapping(value = "/receive")
    public Response<Integer> receive(@Valid @RequestBody List<RuleSaveRequest> request) {
        int dayOfMonth = LocalDate.now().getDayOfMonth();
        if (dayOfMonth > 31){
            return Response.error("请在每月1～10号推送！");
        }else{
            return Response.success(displayRuleService.receive(request));
        }
    }

    /**
     * 特陈规则列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.displayRule.vo.DisplayRuleVO>>
     * @date: 5/6/23 4:19 PM
     */
    @ApiOperation(notes = "特陈规则列表", value = "特陈规则列表")
    @GetMapping("/queryByPage")
    public Response<IPage<DisplayRuleVO>> queryByPage(RuleQueryRequest request) {
        return Response.success(displayRuleService.queryByPage(request));
    }

    /**
     * 特陈规则详情
     *
     * @param id
     * @param employeeId
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.displayRule.vo.DisplayRuleVO>
     * @date: 5/8/23 10:31 AM
     */
    @ApiOperation(notes = "特陈规则详情", value = "特陈规则详情")
    @GetMapping("/details/{id}/{employeeId}")
    public Response<DisplayRuleVO> details(@PathVariable("id") @NotNull(message = "id不能为空") Long id,
                                           @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
        return Response.success(displayRuleService.details(id,employeeId));
    }

    /**
     * 特陈规则提交
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 5/8/23 1:38 PM
     */
    @ApiOperation(notes = "特陈规则提交", value = "特陈规则提交")
    @PostMapping(value = "/submit")
    public Response submit(@Valid @RequestBody RuleSubmitRequest request){
        displayRuleService.submit(request);
        return Response.success();
    }

    /**
     * 特陈规则审批
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 5/8/23 2:59 PM
     */
    @ApiOperation(notes = "特陈规则审批", value = "特陈规则审批")
    @PutMapping("/audit")
    public Response audit(@Valid @RequestBody RuleAuditRequest request) {
        displayRuleService.audit(request);
        return Response.success();
    }

    /**
     * 分公司历史审批通过列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.displayRule.vo.DisplayRuleVO>>
     * @date: 5/8/23 3:29 PM
     */
    @ApiOperation(notes = "分公司历史审批通过列表", value = "分公司历史审批通过列表")
    @GetMapping("/listCompanyPass")
    public Response<List<DisplayRuleVO>> listCompanyPass(RuleCompanyQueryRequest request) {
        return Response.success(displayRuleService.listCompanyPass(request));
    }


}
