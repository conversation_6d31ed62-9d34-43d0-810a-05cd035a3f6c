package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.EmpApplication;
import com.wantwant.sfa.backend.employee.api.EmployeeInfoApi;
import com.wantwant.sfa.backend.employee.request.*;
import com.wantwant.sfa.backend.employee.vo.*;
import com.wantwant.sfa.backend.employeeInfo.service.IEmployeeInfoService;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.service.IEmployeeService;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/08/下午2:32
 */
@RestController
@Slf4j
public class EmployeeInfoController implements EmployeeInfoApi {

    @Autowired
    private IEmployeeService employeeService;
    @Autowired
    private IEmployeeInfoService employeeInfoService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Resource
    private EmpApplication empApplication;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Response<EmployeeBusinessVo> selectEmployeeByMobile(String mobile) {
        EmployeeBusinessVo employeeBusinessVo = employeeService.selectEmployeeByMobile(mobile);
        return Response.success(employeeBusinessVo);
    }

    @Override
    public Response<List<EmployeeBusinessVo>> selectEmployeeByMobileAndOrgId(String key, String organizationId) {
        String organizationType = organizationMapper.getOrganizationType(organizationId);

        List<EmployeeBusinessVo> list = employeeService.selectEmployeeByKey(key,organizationId);
        return Response.success(list);
    }

    @Override
    public Response<CompanyManagerInfo> selectCompanyManagerByMemberKey(Long memberKey) {
        CompanyManagerInfo companyManagerInfo = employeeService.selectCompanyManagerByMemberKey(memberKey);
        return Response.success(companyManagerInfo);
    }

    @Override
    public Response<List<RecommendVo>> selectRecommendCeoList(RecommendCeoSearchRequest request) {
        log.info("【推荐合伙人列表查询】request:{}",request);
        List<RecommendVo> list = employeeInfoService.selectRecommendCeoList(request);
        return Response.success(list);
    }



    @Override
    public Response<RecommendDetailVo> getRecommendDetailVo(Integer applyId) {
        log.info("【推荐合伙人详情】applyId:{}",applyId);
        RecommendDetailVo vo = employeeInfoService.getRecommendDetailVo(applyId);
        return Response.success(vo);
    }

    @Override
    public Response<Page<CeoEmployeeInfoVo>> selectCeoEmployeeInfo(CeoEmployeeRequest request) {
        Page<CeoEmployeeInfoVo> page = employeeInfoService.selectCeoEmployeeInfo(request);
        return Response.success(page);
    }

    @Override
    public Response<List<BusinessBDPersonnelVo>> selectBusinessBDPersonnel(String departmentCode) {
        log.info("【select business bd personnel】departmentCode:{}",departmentCode);

        List<BusinessBDPersonnelVo> list = employeeInfoService.selectBusinessBDPersonnel(departmentCode);

        return Response.success(list);
    }

    @Override
    public Response<List<PartTimePositionVo>> queryPartTime(Integer employeeInfoId) {
        log.info("【query part time】employeeInfoId:{}",employeeInfoId);
        return Response.success(employeeInfoService.queryPartTime(employeeInfoId));
    }

    @Override
    public Response<List<ConsultVo>> selectConsult(Long memberKey) {
        log.info("【select consult】memberKey:{}",memberKey);
        return Response.success(employeeInfoService.selectConsult(memberKey));
    }

    @Override
    public Response<AuditPersonVo> selectAuditPerson(@Valid AuditPersonSearchRequest auditPersonSearchRequest) {
        log.info("【select audit person】request:{}",auditPersonSearchRequest);
        AuditPersonVo auditPersonVo = employeeInfoService.selectAuditPerson(auditPersonSearchRequest);
        return Response.success(auditPersonVo);
    }

    @Override
    public Response<List<OrgVo>> selectOrgByEmpId(@Valid CeoOrgSearchRequest ceoOrgSearchRequest) {
        log.info("【select org empId】request:{}",ceoOrgSearchRequest);
        List<OrgVo> list = employeeInfoService.selectOrgByEmpId(ceoOrgSearchRequest);
        return Response.success(list);
    }

    @ApiOperation(value = "根据组织获取所有下级", notes = "根据组织获取所有下级")
    @Override
    public Response<IPage<ChildVo>> queryChildListByParentOrg(ChildRequest request) {
        log.info("queryChildListByParentOrg request:{}", request);
        return Response.success(employeeInfoService.queryChildListByParentOrg(request));
    }

    @ApiOperation(value = "业务组岗位类型查询")
    @Override
    public Response<List<BusinessGroupPositionTypeVo>> queryBusinessGroupPositionType(BusinessGroupPositionTypeRequest request) {
        log.info("queryBusinessGroupPositionType request:{}", request);
        return Response.success(employeeInfoService.queryBusinessGroupPositionType(request));
    }

    @Override
    public Response<List<BusinessGroupPositionTypeVo>> queryBusinessGroupPositionTypeIncludeBd(BusinessGroupPositionTypeRequest request) {
        log.info("queryBusinessGroupPositionTypeIncludeBd request:{}", request);
        return Response.success(employeeInfoService.queryBusinessGroupPositionTypeIncludeBd(request));
    }

    @Override
    public Response<List<PositionTypeBusinessGroupDto>> queryPositionTypeBusinessGroup(PositionTypeBusinessGroupRequest request) {
        return Response.success(employeeInfoService.queryPositionTypeBusinessGroup(request));
    }

    @Override
    public Response<List<PositionTypeBusinessGroupDto>> queryPositionTypeBusinessGroupIncludeBd(PositionTypeBusinessGroupRequest request) {
        return Response.success(employeeInfoService.queryPositionTypeBusinessGroupIncludeBd(request));
    }

    @Override
    public Response<EmpDetailVO> getEmpDetail(Integer employeeInfoId) {
        log.info("【get emp detail】employeeInfoId:{}",employeeInfoId);
        EmpDetailVO empDetail = empApplication.getEmpDetail(employeeInfoId);
        return Response.success(empDetail);
    }

    @Override
    public Response<List<AssociateObjVO>> getEmpName(int type, String name,String person) {
        log.info("【get emp name】type:{},name:{},person:{}",type,name,person);
        List<AssociateObjVO> associate = empApplication.getAssociate(type, name,person);
        return Response.success(associate);
    }

    @Override
    public Response<String> getMobileByEmpId(String empId) {
        String mobile = employeeInfoService.getMobileByEmpId(empId);
        return Response.success(mobile);
    }

    @Override
    public Response<List<OrgVo>> getPrimaryOrgPathByEmployeeId(String employeeId) {
        log.info("getPrimaryOrgPathByEmployeeId employeeId:{}",employeeId);
        List<OrgVo> list = employeeInfoService.getPrimaryOrgPathByEmployeeId(employeeId);
        return Response.success(list);
    }
}
