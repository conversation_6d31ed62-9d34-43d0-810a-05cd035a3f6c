package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.marketAndPersonnel.request.SalaryQueryRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.request.SalaryUpdateRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.request.StructureQueryRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.request.StructureUpdateRequest;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.SalaryVO;
import com.wantwant.sfa.backend.marketAndPersonnel.vo.StructureVO;
import com.wantwant.sfa.backend.model.marketAndPersonnel.ManagerSalaryModel;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import com.wantwant.sfa.backend.service.EmployeeSalaryStructureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 薪资方案相关接口
 *
 * @date 4/20/22 12:01 PM
 * @version 1.0
 */
@Api(tags = "薪资方案相关接口")
@RestController
@RequestMapping("/employeeSalary")
@Slf4j
public class EmployeeSalaryController {

    @Autowired
    private EmployeeSalaryService salaryService;

    @Autowired
    private EmployeeSalaryStructureService structureService;

    @GetMapping(value = "/save")
    public Response<Integer> listStructure(Integer id,String createdBy){
        return Response.success(salaryService.addSalaryByEmpId(null,id,0,createdBy,null,1));
    }

    /**
     * 合伙人底薪和奖金包列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.marketAndPersonnel.vo.StructureVO>>
     * @date: 4/20/22 6:26 PM
     */
    @ApiOperation(value = "合伙人底薪和奖金包列表", notes = "合伙人底薪和奖金包列表")
    @GetMapping(value = "/listStructure")
    public Response<List<StructureVO>> listStructure(StructureQueryRequest request){
        return Response.success(structureService.listStructure(request));
    }

    /**
     * 根据ID修改底薪和奖金包
     *
     * @param id
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 4/20/22 8:42 PM
     */
    @ApiOperation(notes = "根据ID修改底薪和奖金包", value = "根据ID修改底薪和奖金包")
    @PutMapping("updateStructure/{id}")
    public Response<Integer> updateStructure(@ApiParam(value = "ID", required = true) @PathVariable("id") @NotNull(message = "Id不能为空") Integer id,
                                             @Valid @RequestBody StructureUpdateRequest request) {
        return Response.success(structureService.updateStructure(id,request));
    }

    /**
     * 批量导入底薪和奖金包
     *
     * @param file
     * @param updatedBy
     * @return: com.wantwant.commons.web.response.Response<java.util.List<java.lang.String>>
     * @date: 4/20/22 9:24 PM
     */
    @ApiOperation(notes = "批量导入底薪和奖金包", value = "批量导入底薪和奖金包")
    @PostMapping("/importStructure")
    public Response<List<String>> importStructure(@RequestParam(value = "file") MultipartFile file,
                                                @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy){
        return Response.success(structureService.importStructure(file,updatedBy));
    }

    /**
     * 分页合伙人薪资方案
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.wantwant.sfa.backend.marketAndPersonnel.vo.SalaryVO>>
     * @date: 4/21/22 11:20 AM
     */
    @ApiOperation(notes = "分页合伙人薪资方案", value = "分页合伙人薪资方案")
    @GetMapping("/querySalaryPage")
    public Response<IPage<SalaryVO>> querySalaryPage(SalaryQueryRequest query) {
        return Response.success(salaryService.querySalaryPage(query));
    }


    /**
     * 根据ID修改合伙人薪资
     *
     * @param id
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 4/21/22 2:34 PM
     */
    @ApiOperation(notes = "根据ID修改合伙人薪资", value = "根据ID修改合伙人薪资")
    @PutMapping("updateSalary/{id}")
    public Response<Integer> updateSalary(@ApiParam(value = "ID", required = true) @PathVariable("id") @NotNull(message = "Id不能为空") Integer id,
                                          @Valid @RequestBody SalaryUpdateRequest request) {
        return Response.success(salaryService.updateSalary(id,request));
    }

    /**
     * 批量导入合伙人薪资
     *
     * @param file
     * @param updatedBy
     * @return: com.wantwant.commons.web.response.Response<java.util.List<java.lang.String>>
     * @date: 4/20/22 9:24 PM
     */
    @ApiOperation(notes = "批量导入合伙人薪资", value = "批量导入合伙人薪资")
    @PostMapping("/importSalary")
    public Response<List<String>> importSalary(@RequestParam(value = "file") MultipartFile file,
                                               @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy){
        return Response.success(salaryService.importSalary(file,updatedBy));
    }

    /**
     * 批量导入总监薪资
     *
     * @param file
     * @param person
     * @return:
     * @date: 11/24/22 19:24 PM
     */
    @ApiOperation(notes = "批量导入区域经理及以上薪资", value = "批量导入总监薪资")
    @PostMapping("/manager/importManagerSalary")
    public Response<List<String>> importManagerSalary(@RequestParam(value = "file") MultipartFile file,@ApiParam(value = "操作类型1 查询 2导入", required = true) @RequestParam(value = "type") int type,
                                               @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "person") String person){
        log.info("importManagerSalary type:{} person:{}",type, person);
        if(type != 1 && type != 2) {
            throw new ApplicationException("请传入正确的类型");
        }
        return Response.success(salaryService.importManagerSalary(file,person,type));
    }


    @ApiOperation(notes = "区域经理及以上薪资列表查询", value = "总监薪资列表查询")
    @GetMapping("/manager/salaryList")
    public Response<List<ManagerSalaryModel>> getManagerSalaryList(@ApiParam(value = "选择月份", required = true) @RequestParam(value = "month") String month) {
        log.info("getManagerSalaryList month: {}",month);
        return Response.success(salaryService.getManagerSalaryList(month));
    }

    @ApiOperation(notes = "区域经理及以上薪资列表导出", value = "总监薪资列表导出")
    @GetMapping("/manager/salaryListExport")
    public void exportManagerSalaryList(@ApiParam(value = "选择月份", required = true) @RequestParam(value = "month") String month, HttpServletResponse response) {
        log.info("getManagerSalaryList month: {}",month);
        salaryService.exportManagerSalaryList(month,response);
    }








}
