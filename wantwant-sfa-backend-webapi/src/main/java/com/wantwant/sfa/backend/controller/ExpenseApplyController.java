package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.businessTrip.request.TripAuditRequest;
import com.wantwant.sfa.backend.businessTrip.request.TripQueryRequest;
import com.wantwant.sfa.backend.expenseApply.request.ApplyDetailRequest;
import com.wantwant.sfa.backend.expenseApply.request.ApplySaveRequest;
import com.wantwant.sfa.backend.expenseApply.vo.AdditionalVO;
import com.wantwant.sfa.backend.expenseApply.vo.ExpenseDetailVO;
import com.wantwant.sfa.backend.expenseApply.vo.ExpenseInfoVO;
import com.wantwant.sfa.backend.expenseApply.vo.InteriorMealMeetingInfoVo;
import com.wantwant.sfa.backend.service.ExpenseApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
*
* @since 2023-09-18
*/
@Api(tags = "报销申请相关接口")
@RestController
@RequestMapping("/expenseApply")
public class ExpenseApplyController {

    @Resource
    private ExpenseApplyService expenseApplyService;

    @ApiOperation(value = "报销申请单保存")
    @PostMapping
    public Response<Map<String,Integer>> save(@Valid @RequestBody ApplySaveRequest request) {
        return Response.success(expenseApplyService.saveExpense(request));
    }

    @ApiOperation(value = "报销单明细保存")
    @PostMapping("/saveDetail")
    public Response<Integer> saveDetail(@Valid @RequestBody ApplyDetailRequest request) {
        return Response.success(expenseApplyService.saveDetail(request));
    }

    @ApiOperation(notes = "报销单列表", value = "报销单列表")
    @PostMapping("/queryByPage")
    public Response<IPage<ExpenseInfoVO>> queryByPage(@RequestBody TripQueryRequest request) {
        return Response.success(expenseApplyService.queryByPage(request));
    }

    @ApiOperation(value = "列表导出", notes = "列表导出")
    @PostMapping(value = "/exportList")
    public void exportList(@RequestBody TripQueryRequest request, HttpServletResponse response) {
        expenseApplyService.exportList(request,response);
    }

    @ApiOperation(notes = "报销申详情", value = "报销申详情")
    @GetMapping("/{applyId}/{employeeId}")
    public Response<ExpenseInfoVO> getApplyById(@PathVariable("applyId") @NotNull(message = "applyId不能为空！") Integer applyId,
                                                   @PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
        return Response.success(expenseApplyService.getApplyById(applyId,employeeId));
    }

    @ApiOperation(notes = "报销申删除", value = "报销申删除")
    @DeleteMapping("/{applyId}")
    public Response<Integer> deleteApplyById(@PathVariable("applyId") @NotNull(message = "applyId不能为空！") Integer applyId) {
        return Response.success(expenseApplyService.deleteApplyById(applyId));
    }

    @ApiOperation(notes = "报销申取消", value = "报销申取消")
    @PutMapping("/{applyId}")
    public Response<Integer> cancelApplyById(@PathVariable("applyId") @NotNull(message = "applyId不能为空！") Integer applyId) {
        return Response.success(expenseApplyService.cancelApplyById(applyId));
    }

    @ApiOperation(notes = "详情页面", value = "详情页面")
    @GetMapping("/details/{applyId}/{employeeId}")
    public Response<ExpenseDetailVO> details(@PathVariable("applyId") @NotNull(message = "applyId不能为空！") Integer applyId,
                                             @PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
        return Response.success(expenseApplyService.details(applyId,employeeId));
    }

    @ApiOperation(notes = "流程撤回", value = "流程撤回")
    @PostMapping("/withdraw")
    public Response withdraw(@Valid @RequestBody TripAuditRequest request) {
        return expenseApplyService.withdraw(request);
    }


    @ApiOperation(notes = "审批", value = "审批")
    @PostMapping("/audit")
    public Response audit(@Valid @RequestBody TripAuditRequest request) {
        return expenseApplyService.audit(request);
    }

    @ApiOperation(notes = "获取身份信息", value = "获取身份信息")
    @GetMapping("/additional/{employeeId}")
    public Response<AdditionalVO> getAdditional(@PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
        return Response.success(expenseApplyService.getAdditional(employeeId));
    }

    @ApiOperation(notes = "获取内部聚餐关联的会议信息", value = "获取内部聚餐关联的会议信息")
    @GetMapping("/queryInteriorMealMeetingInfos/{applyId}")
    public Response<List<InteriorMealMeetingInfoVo>> queryInteriorMealMeetingInfos(@PathVariable("applyId") @NotNull(message = "applyId不能为空！") Integer applyId){
        return Response.success(expenseApplyService.queryInteriorMealMeetingInfos(applyId));
    }


}
