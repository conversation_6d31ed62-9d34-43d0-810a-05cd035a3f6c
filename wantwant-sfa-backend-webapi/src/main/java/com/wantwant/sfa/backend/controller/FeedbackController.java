package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.arch.vo.DeptVo;
import com.wantwant.sfa.backend.feedback.request.*;
import com.wantwant.sfa.backend.feedback.vo.*;
import com.wantwant.sfa.backend.model.feedback.FeedbackQualityConfigPO;
import com.wantwant.sfa.backend.service.FeedbackInfoService;
import com.wantwant.sfa.backend.service.FeedbackQualityService;
import com.wantwant.sfa.backend.service.PenaltyDeductionService;
import com.wantwant.sfa.backend.util.CommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
* 问题反馈信息相关接口
*
* @since 2022-09-06
*/
@Api(tags = "问题反馈信息相关接口")
@RestController
@RequestMapping("/feedback")
public class FeedbackController {

    @Autowired
    private FeedbackInfoService feedbackInfoService;

    @Autowired
    private FeedbackQualityService feedbackQualityService;

    /**
     * 问题反馈
     * 提供旺铺-崔博文
     *
     * @param request
     * @return: int
     * @date: 9/06/22
     */
    @ApiOperation(value = "问题反馈")
    @PostMapping("/save")
    public Response<Integer> saveOrUpdate(@Valid @RequestBody FeedbackSaveRequest request) {
        return Response.success(feedbackInfoService.saveOrUpdateByNo(request));
    }

    /**
     * 问题反馈复议
     * 提供旺铺-崔博文
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 12/5/23 10:50 AM
     */
    @ApiOperation(value = "问题反馈复议")
    @PostMapping("/repeat")
    public Response<Integer> reconsideration(@Valid @RequestBody FeedbackRepeatSaveRequest request) {
        return Response.success(feedbackInfoService.repeat(request));
    }

    /**
     * 用户评分
     * 提供旺铺-崔博文
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 12/27/22 2:33 PM
     */
    @ApiOperation(value = "用户评分")
    @PostMapping("/userRating")
    public Response<Integer> userRating(@Valid @RequestBody FeedbackUserRatingRequest request) {
        return Response.success(feedbackInfoService.userRating(request));
    }

    /**
     * 用户催单
     * 提供旺铺-崔博文
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 4/4/23 5:02 PM
     */
    @ApiOperation(value = "用户催单")
    @PostMapping("/userUrge")
    public Response<Integer> userUrge(@Valid @RequestBody FeedbackUserUrgeRequest request) {
        return Response.success(feedbackInfoService.userUrge(request));
    }

    /**
     * 给提问点赞/关注或取消
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 12/30/22 4:04 PM
     */
    @ApiOperation(value = "给提问点赞/关注或取消")
    @PostMapping("/like")
    public Response like(@Valid @RequestBody FeedbackLikeRequest request) {
        feedbackInfoService.like(request);
        return Response.success();
    }

    /**
     * 经办部门下拉框
     *
     * @param employeeId
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.arch.vo.DeptVo>>
     * @date: 12/29/22 6:16 PM
     */
    @ApiOperation(value = "经办部门下拉框")
    @GetMapping("/listDept")
    public Response<List<DeptVo>> listDept(@ApiParam(value = "organizationId", required = true) @RequestParam(value = "organizationId") String organizationId,
                                           @ApiParam(value = "employeeId", required = false) @RequestParam(value = "employeeId",required = false) String employeeId) {
        return Response.success(feedbackInfoService.listDept(organizationId,employeeId));
    }

    /**
     * 问题分类下拉框
     *
     * @param category
     * @param subclass
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.feedback.vo.FeedbackProblemVO>>
     * @date: 12/29/22 6:43 PM
     */
    @ApiOperation(value = "问题分类下拉框")
    @GetMapping("/listProblem")
    public Response<List<FeedbackProblemVO>> listProblem(@ApiParam(value = "问题大类", required = false) @RequestParam(value = "category") String category,
                                                         @ApiParam(value = "问题小类", required = false) @RequestParam(value = "subclass") String subclass,
                                                         @ApiParam(value = "业务分类(0:问题,1:投诉)", required = false) @RequestParam(value = "type",defaultValue = "0") Integer type) {
        return Response.success(feedbackInfoService.listProblem(category,subclass,type));
    }

    /**
     * 查询列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.feedback.vo.FeedbackInfoVO>>
     * @date: 12/29/22 1:27 PM
     */
    @ApiOperation(notes = "查询列表", value = "查询列表")
    @PostMapping("/queryByPage")
    public Response<IPage<FeedbackInfoVO>> queryByPage(@RequestBody @Validated FeedbackQueryRequest request) {
        return Response.success(feedbackInfoService.queryByPage(request));
    }

    /**
     * 列表导出
     *
     * @param request
     * @return: void
     * @date: 12/29/22 4:45 PM
     */
    @ApiOperation(value = "列表导出", notes = "列表导出")
    @PostMapping(value = "/exportList")
    public void exportList(@RequestBody FeedbackQueryRequest request, HttpServletResponse response) {
        feedbackInfoService.exportList(request,response);
    }

    /**
     * 待处理问题
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.feedback.vo.FeedbackInfoVO>>
     * @date: 12/30/22 6:21 PM
     */
    @ApiOperation(notes = "待处理问题", value = "待处理问题")
    @PostMapping("/queryWaitByPage")
    public Response<IPage<FeedbackInfo1VO>> queryWaitByPage(@RequestBody FeedbackQuery1Request request) {
        return Response.success(feedbackInfoService.queryWaitByPage(request));
    }

    @ApiOperation(notes = "待处理问题", value = "待处理问题")
    @PostMapping("/queryWaitByPage1")
    public Response<Integer> queryWaitByPage1(@RequestBody FeedbackQuery1Request request) {
        return Response.success(feedbackInfoService.queryTaskCount(request.getEmployeeId(),null));
    }

    /**
     * 问题反馈详情
     *
     * @param id
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.feedback.vo.FeedbackInfoVO>
     * @date: 11/24/22 2:22 PM
     */
    @ApiOperation(notes = "问题反馈详情", value = "问题反馈详情")
    @GetMapping("/details/{id}/{employeeId}/{isReply}")
    public Response<FeedbackInfoVO> details(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                                            @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId,
                                            @ApiParam(value = "显示未回复(0:显示,1:不显示)", required = true) @PathVariable("isReply") @NotNull(message = "isReply不能为空") Integer isReply) {
        return Response.success(feedbackInfoService.details(id,employeeId,isReply));
    }

    /**
     * 上级部门下拉框
     *
     * @param organizationId
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.arch.vo.DeptVo>>
     * @author: zhouxiaowen
     * @date: 12/5/23 6:57 PM
     */
    @ApiOperation(notes = "修改经办上级部门下拉框", value = "修改经办上级部门下拉框")
    @GetMapping("/parentDeptList/{organizationId}/{employeeId}")
    public Response<List<DeptVo>> parentDeptList(@PathVariable("organizationId") @NotNull(message = "organizationId不能为空") String organizationId,
                                                 @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
        return Response.success(feedbackInfoService.parentDeptList(organizationId,employeeId));
    }

    /**
     * 修改经办上级部门
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 12/6/23 9:15 AM
     */
    @ApiOperation(value = "修改经办上级部门")
    @PutMapping("/modifyDept")
    public Response modifyDept(@Valid @RequestBody FeedbackModifyDeptRequest request) {
        feedbackInfoService.modifyDept(request);
        return Response.success();
    }

    /**
     * 问题反馈管理组工号
     *
     * @param
     * @return: com.wantwant.commons.web.response.Response<java.lang.String>
     * @date: 1/12/23 6:40 PM
     */
    @ApiOperation(notes = "问题反馈管理组工号", value = "问题反馈管理组工号")
    @GetMapping("/manageEmp")
    public Response<String> manageEmp() {
        return Response.success(feedbackInfoService.manageEmp());
    }

    /**
     * 问题回复
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 12/30/22 5:10 PM
     */
    @ApiOperation(value = "问题回复")
    @PostMapping("/reply")
    public Response reply(@Valid @RequestBody FeedbackReplyRequest request) {
        feedbackInfoService.reply(request);
        return Response.success();
    }

    /**
     * 投诉专区列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.feedback.vo.ComplaintInfoVO>>
     * @date: 3/7/23 3:34 PM
     */
    @ApiOperation(notes = "投诉专区列表", value = "投诉专区列表")
    @PostMapping("/queryComplaintByPage")
    public Response<IPage<ComplaintInfoVO>> queryComplaintByPage(@RequestBody ComplainQueryRequest request) {
        return Response.success(feedbackInfoService.queryComplaintByPage(request));
    }

    /**
     * 提交调查记录
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 3/7/23 4:38 PM
     */
    @ApiOperation(value = "提交调查记录")
    @PostMapping("/submitRecords")
    public Response submitRecords(@Valid @RequestBody SubmitRecordsRequest request) {
        feedbackInfoService.submitRecords(request);
        return Response.success();
    }

    /**
     * 投诉专区列表导出
     *
     * @param request
     * @param response
     * @return: void
     * @date: 3/7/23 6:49 PM
     */
    @ApiOperation(value = "投诉专区列表导出", notes = "投诉专区列表导出")
    @PostMapping(value = "/exportComplaintList")
    public void exportComplaintList(@RequestBody ComplainQueryRequest request, HttpServletResponse response) {
        feedbackInfoService.exportComplaintList(request,response);
    }

    /**
     * 是否显示投诉tab
     *
     * @param employeeId
     * @return: com.wantwant.commons.web.response.Response<java.lang.Boolean>
     * @date: 3/13/23 3:33 PM
     */
    @ApiOperation(value = "是否显示投诉tab", notes = "是否显示投诉tab")
    @GetMapping("/isComplaint/{employeeId}")
    public Response<Boolean> isComplaint(@PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
        return Response.success(feedbackInfoService.isComplaint(employeeId));
    }


    @ApiOperation(notes = "质检组工号", value = "质检组工号")
    @GetMapping("/qualityEmp")
    public Response<String> qualityEmp() {
        return Response.success(feedbackInfoService.qualityEmp());
    }

    /**
     * 质检列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.feedback.vo.FeedbackQualityVO>>
     * @date: 7/19/23 11:53 AM
     */
    @ApiOperation(notes = "质检列表", value = "质检列表")
    @PostMapping("/queryQualityByPage")
    public Response<IPage<FeedbackQualityVO>> queryQualityByPage(@RequestBody @Validated QualityQueryRequest request) {
        return Response.success(feedbackQualityService.queryQualityByPage(request));
    }

    /**
     * 质检配置项
     *
     * @param
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.model.feedback.FeedbackQualityConfigPO>>
     * @date: 7/19/23 11:55 AM
     */
    @ApiOperation(notes = "质检配置项", value = "质检配置项")
    @GetMapping("/queryQualityConfig")
    public Response<List<FeedbackQualityConfigPO>> queryQualityConfig() {
        return Response.success(feedbackQualityService.queryQualityConfig());
    }

    /**
     * 质检详情
     *
     * @param id
     * @param employeeId
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.feedback.vo.FeedbackQualityVO>
     * @date: 7/19/23 1:34 PM
     */
    @ApiOperation(notes = "质检详情", value = "质检详情")
    @GetMapping("/qualityDetails/{id}/{employeeId}")
    public Response<FeedbackQualityVO> qualityDetails(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                                            @PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
        return Response.success(feedbackQualityService.qualityDetails(id,employeeId));
    }

    /**
     * 质检提交
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 7/19/23 2:25 PM
     */
    @ApiOperation(notes = "质检提交", value = "质检提交")
    @PostMapping("/qualitySubmit")
    public Response qualitySubmit(@Valid @RequestBody FeedbackQualitySubmitRequest request) {
        feedbackQualityService.qualitySubmit(request);
        return Response.success();
    }

    @ApiOperation(notes = "质检导出", value = "质检导出")
    @PostMapping(value = "/exportQualityList")
    public void exportQualityList(@RequestBody QualityQueryRequest request, HttpServletResponse response) {
        feedbackQualityService.exportQualityList(request,response);
    }


    @ApiOperation(notes = "报表", value = "报表")
    @GetMapping("/report")
    public Response<FeedbackReportVO> report(FeedbackQueryReportRequest request) {
        return Response.success(feedbackInfoService.report(request));
    }

    @Autowired
    private PenaltyDeductionService penaltyDeductionService;
    @GetMapping(value = "/penaltyDeduction/{param}")
    public void penaltyDeduction(@PathVariable("param") String param) {
        LocalDate now = LocalDate.now();
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        penaltyDeductionService.doPenaltyDeduction(now);
    }


}
