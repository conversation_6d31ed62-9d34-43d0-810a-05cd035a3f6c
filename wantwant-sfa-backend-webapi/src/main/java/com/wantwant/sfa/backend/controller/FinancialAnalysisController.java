package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.pagination.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.afterSales.request.FinancialAnalysisRequest;
import com.wantwant.sfa.backend.mainProduct.vo.MainProductGoalVO;
import com.wantwant.sfa.backend.service.FinancialAnalysisService;
import com.wantwant.sfa.backend.vo.FinancialAnalysisHistoryAndFreightVO;
import com.wantwant.sfa.backend.vo.FinancialAnalysisVO;
import com.wantwant.sfa.backend.vo.ProductAnalysisVO;
import com.wantwant.sfa.backend.vo.WarehouseAnalysisVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "财务费用分析")
@RestController
@RequestMapping("/financialAnalysis")
public class FinancialAnalysisController {


    @Autowired
    private FinancialAnalysisService financialAnalysisService;


    @ApiOperation(value = "财务费用分析 商品-政策", notes = "财务费用分析 商品-政策", httpMethod = "POST")
    @PostMapping("/queryFinancialByGoodsAndPolicy")
    public Response<FinancialAnalysisVO> queryFinancial(@RequestBody FinancialAnalysisRequest request) {
        return Response.success(financialAnalysisService.queryByPage(request));
    }

    @ApiOperation(value = "财务费用分析 历史", notes = "财务费用分析 历史", httpMethod = "POST")
    @PostMapping("/queryFinancialByHistory")
    public Response<FinancialAnalysisHistoryAndFreightVO> queryFinancialByHistoryAndFreight(@RequestBody FinancialAnalysisRequest request) {
        return Response.success(financialAnalysisService.queryFinancialByHistoryAndFreight(request));
    }

    @ApiOperation(value = "财务费用分析 运费", notes = "财务费用分析 运费", httpMethod = "POST")
    @PostMapping("/queryFinancialByFreight")
    public Response<List<WarehouseAnalysisVO>> queryFinancialByFreight(@RequestBody FinancialAnalysisRequest request) {
        return Response.success(financialAnalysisService.queryFinancialByFreight(request));
    }


    @ApiOperation(value = "财务费用分析 商品-SKU", notes = "财务费用分析 商品-SKU", httpMethod = "POST")
    @PostMapping("/queryFinancialBySKU")
    public Response<Page<ProductAnalysisVO>> queryFinancialBySKU(@RequestBody FinancialAnalysisRequest request) {
        return Response.success(financialAnalysisService.queryFinancialBySKU(request));
    }

    @ApiOperation(value = "财务费用分析导出 商品-政策", notes = "财务费用分析导出 商品-政策", httpMethod = "POST")
    @PostMapping("/queryFinancialByGoodsAndPolicy/export")
    public void exportFinancial(@RequestBody FinancialAnalysisRequest request) {
        financialAnalysisService.exportFinancial(request);
    }

    @ApiOperation(value = "财务费用分析导出 商品-SKU", notes = "财务费用分析导出 商品-SKU", httpMethod = "POST")
    @PostMapping("/queryFinancialBySKU/export")
    public void exportFinancialBySKU(@RequestBody FinancialAnalysisRequest request) {
        financialAnalysisService.exportFinancialBySKU(request);
    }

    @ApiOperation(value = "财务费用分析导出 历史", notes = "财务费用分析导出 历史", httpMethod = "POST")
    @PostMapping("/queryFinancialByHistory/export")
    public void exportFinancialByHistoryAndFreight(@RequestBody FinancialAnalysisRequest request) {
        financialAnalysisService.exportFinancialByHistoryAndFreight(request);
    }

    @ApiOperation(value = "财务费用分析导出 运费", notes = "财务费用分析导出 运费", httpMethod = "POST")
    @PostMapping("/queryFinancialByFreight/export")
    public void exportFinancialByFreight(@RequestBody FinancialAnalysisRequest request) {
        financialAnalysisService.exportFinancialByFreight(request);
    }

}
