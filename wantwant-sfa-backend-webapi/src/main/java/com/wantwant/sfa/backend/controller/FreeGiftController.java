package com.wantwant.sfa.backend.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.freeGift.api.FreeGiftApi;
import com.wantwant.sfa.backend.freeGift.request.FreeGiftListRequest;
import com.wantwant.sfa.backend.freeGift.vo.*;
import com.wantwant.sfa.backend.freeSample.request.VisitIdRequest;
import com.wantwant.sfa.backend.mapper.FreeGiftRecordMapper;
import com.wantwant.sfa.backend.service.FreeGiftRecordService;
import com.wantwant.sfa.backend.service.impl.FreeGiftRecordServiceImpl;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
public class FreeGiftController implements FreeGiftApi {

    @Autowired
    private FreeGiftRecordService freeGiftRecordService;

    @Autowired
    private FreeGiftRecordMapper freeGiftRecordMapper;

    @Autowired
    private SettingServiceImpl settingServiceImpl;

    @Autowired
    private FreeGiftRecordServiceImpl freeGiftRecordServiceImpl;

    @Value("${export.gift.max.count}")
    private Long maxCount = 10000l;


    @Override
    public Response<Page<VisitFreeGiftPage>> getVisitFreeGift(VisitIdRequest request) {
        return Response.success(freeGiftRecordService.getVisitFreeGift(request));
    }

    @Override
    public Response<Page<VisitBangStickPageVo>> getVisitFreeBangStick(VisitIdRequest request) {
        return Response.success(freeGiftRecordService.getVisitFreeBangStick(request));
    }

    @Override
    public Response<Page<FreeGiftPage>> list(FreeGiftListRequest request) {
        Page<FreeGiftPage> result = freeGiftRecordService.getPage(request);
        return Response.success(result);
    }

    @Override
    public void export(FreeGiftListRequest freeGiftListRequest) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        Long giftCounts = freeGiftRecordMapper.selectGiftCount(freeGiftListRequest);
        if (maxCount < giftCounts) {
            throw new ApplicationException("数量过多！");
        } else {
            String firstOrderAmount = settingServiceImpl.getValue("first_order_amount");
            List<String> firstOrderList = freeGiftRecordMapper.getFirstOrderList(firstOrderAmount);

            List<FreeGiftExport> freeGiftExport = freeGiftRecordService.exportExcel(freeGiftListRequest);

            List<FreeGiftPage> record = new ArrayList<>();
            //循环遍历，将派赠导出 信息返回类  转换 赠品列表返回类 中  为调用公用方法使用
            for (FreeGiftExport giftExport : freeGiftExport) {
                FreeGiftPage freeGiftPage = new FreeGiftPage();
                BeanUtils.copyProperties(giftExport, freeGiftPage);
                record.add(freeGiftPage);
            }
            //调用公有方法
            record = freeGiftRecordServiceImpl.giftIsPotentilPublic(record, firstOrderList);
            //将 赠品列表返回类  转换 派赠导出 信息返回类中  为调用公用方法使用
            List<FreeGiftExport> giftExportlist = new ArrayList<>();
            BeanUtils.copyProperties(record, giftExportlist, FreeGiftPage.class, FreeGiftExport.class);

            String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
            String name = "赠品列表" + sheetName;
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName),
                    FreeGiftExport.class, giftExportlist);
            response.setContentType("application/vnd.ms-excel");
            try {
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
                        .encode(name + ".xls", "utf-8"));
                OutputStream outputStream = response.getOutputStream();
                workbook.write(outputStream);
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                response.setStatus(500);
            }
        }
    }
}