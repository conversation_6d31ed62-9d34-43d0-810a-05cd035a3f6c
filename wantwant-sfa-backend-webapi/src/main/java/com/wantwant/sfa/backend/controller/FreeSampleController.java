package com.wantwant.sfa.backend.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.freeSample.api.FreeSampleApi;
import com.wantwant.sfa.backend.freeSample.request.*;
import com.wantwant.sfa.backend.freeSample.response.FreeSampleBranchDetailRes;
import com.wantwant.sfa.backend.freeSample.response.FreeSampleCompanyDetailRes;
import com.wantwant.sfa.backend.freeSample.response.FreeSampleDetailRes;
import com.wantwant.sfa.backend.freeSample.vo.*;
import com.wantwant.sfa.backend.service.FreeSampleRecordService;
import com.wantwant.sfa.backend.service.OfflineExportService;
import com.wantwant.sfa.backend.util.CommonUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

@Slf4j
@RestController
public class FreeSampleController implements FreeSampleApi {
    @Autowired
    private FreeSampleRecordService freeSampleRecordService;
    @Autowired
    private OfflineExportService offlineExportService;

    @Override
    public Response<Page<VisitFreeSamplePage>> get(VisitIdRequest request) {
        return Response.success(freeSampleRecordService.getVisitFreeSample(request));
    }

    @Override
    public Response<Page<FreeSamplePage>> list(FreeSampleListRequest request) {
        Page<FreeSamplePage> result = freeSampleRecordService.getPage(request);
        return Response.success(result);
    }

    @Override
    public void export(FreeSampleListRequest req) {
        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        List<FreeSampleExport> record = freeSampleRecordService.exportExcel(req);
        String sheetName =
                LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        String name = "试吃列表" + sheetName;
        Workbook workbook =
                ExcelExportUtil.exportExcel(
                        new ExportParams(null, sheetName), FreeSampleExport.class, record);
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name + ".xls", "utf-8"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public Response exportAsync(FreeSampleListRequest req) {
        return offlineExportService.export(
                req.getEmployeeId(),
                "",
                "freeSampleRecordServiceImpl",
                "exportToWb",
                "",
                req,
                FreeSampleListRequest.class);
    }

    @Override
    public Response<FreeSampleQuotaManagementVo> quotaManagementlist(
            FreeSampleQuotaManagementRequest request) {
        FreeSampleQuotaManagementVo quotaManagement =
                freeSampleRecordService.getQuotaManagement(request);
        return Response.success(quotaManagement);
    }

    /**
     * 大区试吃分发明细
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.response.FreeSampleDetailRes>
     * @date: 2021-12-29 11:02
     */
    @Override
    public Response<FreeSampleDetailRes> areaDetail(FreeSampleDetailRequest request) {
        log.info("大区试吃明细request:{}", request);
        return Response.success(freeSampleRecordService.areaDetail(request));
    }

    /**
     * 分公司试吃分发明细
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.response.FreeSampleCompanyDetailRes>
     * @date: 2021-12-29 11:03
     */
    @Override
    public Response<FreeSampleCompanyDetailRes> companyDetail(
            FreeSampleCompanyDetailRequest request) {
        return Response.success(freeSampleRecordService.companyDetail(request));
    }

    /**
     * 业务试吃分发明细
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.response.FreeSampleBranchDetailRes>
     * @date: 2021-12-29 11:03
     */
    @Override
    public Response<FreeSampleBranchDetailRes> branchDetail(FreeSampleBranchDetailRequest request) {
        return Response.success(freeSampleRecordService.branchDetail(request));
    }

    /**
     * 分页查询分公司待审批
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.vo.CompanyAuditVO>
     * @date: 2021-11-30 14:49
     */
    @ApiOperation(notes = "分页查询分公司待审批", value = "分页查询分公司待审批")
    @PostMapping("/freeSample/queryCompanyAudit")
    public Response<IPage<QuotaApplyInfoVO>> queryCompanyAuditByPage(@RequestBody CompanyAuditQueryRequest query) {

        return Response.success(freeSampleRecordService.queryCompanyAuditByPage(query));
    }

    /**
     * 分公司待审批同意
     *
     * @param id
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @author: zhouxiaowen
     * @date: 2021-12-01 18:43
     */
    @ApiOperation(notes = "分公司待审批同意", value = "分公司待审批同意")
    @PutMapping("/freeSample/approve/{id}")
    public Response companyAuditApprove(
            @PathVariable("id") @NotEmpty(message = "id不能为空!") Integer id,
            @Validated @RequestBody CompanyAuditRequest request) {
        freeSampleRecordService.companyAuditApprove(id, request);
        return Response.success();
    }

    /**
     * 分公司待审批驳回
     *
     * @param id
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @author: zhouxiaowen
     * @date: 2021-12-01 18:43
     */
    @ApiOperation(notes = "分公司待审批驳回", value = "分公司待审批驳回")
    @PutMapping("/freeSample/reject/{id}")
    public Response companyAuditReject(
            @PathVariable("id") @NotEmpty(message = "id不能为空!") Integer id,
            @Validated @RequestBody CompanyAuditRequest request) {
        freeSampleRecordService.companyAuditReject(id, request);
        return Response.success();
    }

    /**
     * 分页查询额度明细
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.vo.CompanyAuditVO>
     * @date: 2021-11-30 14:49
     */
    @ApiOperation(notes = "分页查询额度明细", value = "分页查询额度明细")
    @PostMapping("/freeSample/queryQuotaDetail")
    public Response<IPage<QuotaDetailVO>> queryQuotaDetailByPage(@RequestBody @Validated QuotaDetailQueryRequest query) {
        log.info("queryQuotaDetailByPage request: {}", query);

        return Response.success(freeSampleRecordService.queryQuotaDetailByPage(query));
    }

    /**
     * 导出额度明细
     *
     * @param query
     */
    @ApiOperation(notes = "导出额度明细", value = "导出额度明细")
    @PostMapping("/freeSample/exportQuotaDetail")
    public void exportQuotaDetail(@RequestBody QuotaDetailQueryRequest query) {
        log.info("queryQuotaDetailByPage request: {}", query);
        freeSampleRecordService.exportQuotaDetail(query);
    }

    /**
     * 分页查询额度明细
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.vo.CompanyAuditVO>
     * @date: 2021-11-30 14:49
     */
    @ApiOperation(notes = "分页查询额度明细新", value = "分页查询额度明细新")
    @PostMapping("/freeSample/queryQuotaDetailNew")
    public Response<IPage<QuotaDetailVO>> queryQuotaDetailByPageNew(@RequestBody @Validated QuotaDetailQueryRequest query) {
        log.info("queryQuotaDetailByPage request: {}", query);
        int businessGroup = RequestUtils.getBusinessGroup();
        query.setBusinessGroup(businessGroup);
        return Response.success(freeSampleRecordService.queryQuotaDetailByPageNew(query));
    }

    /**
     * 导出额度明细
     *
     * @param query
     */
    @ApiOperation(notes = "导出额度明细新", value = "导出额度明细新")
    @PostMapping("/freeSample/exportQuotaDetailNew")
    public void exportQuotaDetailNew(@RequestBody QuotaDetailQueryRequest query) {
        log.info("queryQuotaDetailByPage request: {}", query);
        int businessGroup = RequestUtils.getBusinessGroup();
        query.setBusinessGroup(businessGroup);
        freeSampleRecordService.exportQuotaDetailNew(query);
    }

    /**
     * 分页查询额度额度管理
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.freeSample.vo.CompanyAuditVO>
     * @date: 2021-11-30 14:49
     */
    @ApiOperation(notes = "分页查询额度管理", value = "分页查询额度管理")
    @PostMapping("/freeSample/queryQuotaManager")
    public Response<IPage<QuotaDetailVO>> queryQuotaManagerByPage(@RequestBody @Validated QuotaManagerRequest request) {
        log.info("queryQuotaDetailByPage request: {}", request);
        return Response.success(freeSampleRecordService.queryQuotaManagerByPage(request));
    }

    /**
     * 查询费用类型
     */
    @ApiOperation(notes = "查询费用类型", value = "查询费用类型")
    @GetMapping("/freeSample/queryCostType")
    public Response<List<Object>> queryCostType() {
        return Response.success(freeSampleRecordService.queryCostType());
    }
}
