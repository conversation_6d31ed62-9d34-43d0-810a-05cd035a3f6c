package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.indicator.api.IndicatorApi;
import com.wantwant.sfa.backend.indicator.request.IndicatorModifyRequest;
import com.wantwant.sfa.backend.indicator.request.IndicatorQueryRequest;
import com.wantwant.sfa.backend.indicator.vo.IndicatorTypeVo;
import com.wantwant.sfa.backend.indicator.vo.IndicatorVo;

import com.wantwant.sfa.backend.info.vo.OrganizationVo;
import com.wantwant.sfa.backend.service.IndicatorService;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description: 核心指标controller
 * @Auther: zhengxu
 * @Date: 2021/07/14/上午11:18
 */
@RestController
public class IndicatorController implements IndicatorApi {
    @Autowired
    private IndicatorService indicatorService;

    @Override
    public Response addIndicator(IndicatorModifyRequest request) {
        if(checkIndicatorHasInsert(request)){
            return Response.error("当前大区相关月份的核心指标已设置");
        }
        indicatorService.insert(request);
        return Response.success();
    }

    private boolean checkIndicatorHasInsert(IndicatorModifyRequest request) {
        return indicatorService.checkIndicatorHasInsert(request);
    }

    @Override
    public Response modifyIndicator(IndicatorModifyRequest request) {
        String regionId = request.getRegionId();
        String validStartTime = request.getValidStartTime();
        if(StringUtils.isNotBlank(regionId) || StringUtils.isNotBlank(validStartTime)){
            if(checkIndicatorHasInsert(request)){
                return Response.error("当前大区相关月份的核心指标已设置");
            }
        }


        indicatorService.update(request);
        return Response.success();
    }

    @Override
    public Response<Page<IndicatorVo>> selectIndicatorList(IndicatorQueryRequest request) {
        Page<IndicatorVo> list = indicatorService.getList(request);
        return Response.success(list);
    }

    @Override
    public Response<List<IndicatorTypeVo>> selectIndicatorTypes() {
        List<IndicatorTypeVo> indicatorTypeList = indicatorService.selectIndicatorType();
        return Response.success(indicatorTypeList);
    }

    @Override
    public Response deleteIndicator(int id) {
        indicatorService.delete(id);
        return Response.success();
    }

    @Override
    public Response<List<OrganizationVo>> getOrganization(String employeeId) {
        List<OrganizationVo> list = indicatorService.getOrganizationVo(employeeId);
        return Response.success(list);
    }

}
