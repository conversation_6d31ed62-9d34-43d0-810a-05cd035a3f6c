package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.labels.service.CustomerLabelJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
public class LabelController {
    @Autowired
    private CustomerLabelJobService customerLabelJobService;

    @GetMapping("/get")
    public Response get(String startTime, Integer days) {
//        customerLabelJobService.customerLabelsJobByDay(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)), days);
        return Response.success();
    }
}
