package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.loginUser.request.LoginBody;
import com.wantwant.sfa.backend.loginUser.request.LoginToken;
import com.wantwant.sfa.backend.loginUser.request.PasswordChangeRequest;
import com.wantwant.sfa.backend.loginUser.vo.LoginUserOrgVO;
import com.wantwant.sfa.backend.loginUser.vo.LoginUserVO;
import com.wantwant.sfa.backend.service.LoginUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "用户登录相关接口")
@RestController
@RequestMapping("/login")
public class LoginUserController {

    @Resource
    private LoginUserService loginUserService;


    @ApiOperation(value = "登录")
    @PostMapping
    public Response<String> login(@Valid @RequestBody LoginBody loginBody) {
        return loginUserService.login(loginBody);
    }

    @ApiOperation(value = "获取用户信息")
    @PostMapping("/getInfo")
    public Response<LoginUserVO> getInfo(@Valid @RequestBody LoginToken LoginToken) {
        return Response.success(loginUserService.getInfo(LoginToken));
    }

    @ApiOperation(value = "发送短信验证码")
    @PostMapping("/sendCode/{mobile}")
    public Response sendSMSCode(@PathVariable String mobile) {
        loginUserService.sendSMSCode(mobile);
        return Response.success();
    }

    /**
     * 区域经理
     * map/user/getUserOrg
     *
     * @date 11/9/22 4:25 PM
     * @version 1.0
     */
    @ApiOperation(value = "区域经理组织渠道")
    @GetMapping("/getUserOrg")
    public Response<List<LoginUserOrgVO>> getUserOrg(@RequestParam(value = "account") String account) {
        return Response.success(loginUserService.getUserOrg(account));
    }

    @ApiOperation(value = "修改密码")
    @PostMapping("/passwordChange")
    public Response<String> passwordChange(@Valid @RequestBody PasswordChangeRequest password,HttpServletRequest request) {
        return loginUserService.passwordChange(password,request.getHeader("Authorization"));
    }




    @GetMapping("/init")
    public void init() {
        loginUserService.initAccount();
    }

    @ApiOperation(value = "获取4位数字验证码(纯数字)")
    @GetMapping("/numericCode/{account}")
    public Response<String> getNumericCode(@PathVariable String account) {
        String code = loginUserService.generateNumericCaptcha(account);
        return Response.success(code);
    }

}
