package com.wantwant.sfa.backend.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.Task.PartnerGoalTask;
import com.wantwant.sfa.backend.mainProduct.request.*;
import com.wantwant.sfa.backend.mainProduct.vo.*;
import com.wantwant.sfa.backend.realData.request.EmployeeSetGoalRes;
import com.wantwant.sfa.backend.service.InfoCentreService;
import com.wantwant.sfa.backend.service.MainProductService;
import com.wantwant.sfa.backend.service.OrganizationProductGoalService;
import com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 主推品目标相关接口
 *
 * @since 2022-08-19
 */
@Api(tags = "主推品目标相关接口")
@RestController
@RequestMapping("/mainProduct")
@Slf4j
public class MainProductController {

    @Autowired
    private MainProductService mainProductService;

    @Autowired
    private OrganizationProductGoalService organizationProductGoalService;

    @Resource
    private InfoCentreService infoCentreService;

    /**
     * 根据关键字获取sku信息
     *
     * @param key 关键字
     * @return: com.wantwant.commons.web.response.Response<java.util.List < com.wantwant.sfa.backend.mainProduct.vo.MainProductSkuVO>>
     * @date: 8/19/22 3:24 PM
     */
    @ApiOperation(value = "根据关键字获取sku信息")
    @GetMapping("/querySkuInfo")
    public Response<List<MainProductSkuVO>> querySkuInfo(@ApiParam(value = "key") @RequestParam(value = "key", required = false) String key) {
        return Response.success(mainProductService.querySkuInfo(key));
    }

    @ApiOperation(value = "获取产品组下的spu以及关联的sku信息")
    @GetMapping("/querySpuInfo")
    public Response<List<MainProductSpuVO>> querySpuInfo() {
        return Response.success(mainProductService.querySpuInfo());
    }

    /**
     * 根据ID获取主推品信息
     *
     * @param id
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 8/19/22 5:01 PM
     */
    @ApiOperation(value = "根据ID获取主推品")
    @GetMapping("/detail")
    public Response<MainProductVO> getMainProduct(@RequestParam(value = "id") Integer id) {
        return Response.success(mainProductService.getMainProduct(id));
    }


    /**
     * 分公司主推品目标导入模板
     *
     * @param request
     * @param response
     * @return: void
     * @date: 8/24/22 9:24 AM
     */
    @ApiOperation(value = "分公司主推品目标导入模板")
    @GetMapping("/template")
    @Deprecated
    public void template(HttpServletRequest request, HttpServletResponse response) {
        organizationProductGoalService.template(request, response);
    }

    /**
     * 导入分公司主推品目标(废弃)
     *
     * @param file
     * @param effectiveDate
     * @param updatedBy
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
     * @date: 8/22/22 2:16 PM
     */
    @ApiOperation(value = "导入分公司主推品目标")
    @PostMapping("/importCompanyProductGoal")
    @Deprecated
    public Response<ImportMsgVO> importCompanyProductGoal(@RequestParam(value = "file") MultipartFile file,
                                                          @ApiParam(value = "生效日期yyyy-MM", required = true) @RequestParam(value = "effectiveDate") String effectiveDate,
                                                          @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {
        //主推品目标：营运组、商企组
        String processName = infoCentreService.getProcessName(updatedBy);
        if (!processName.contains("营运管理部") && !processName.contains("商品企划部")) {
            throw new ApplicationException("当前用户不能修改！");
        }
        LocalDate parse;
        try {
            parse = LocalDate.parse(effectiveDate + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            throw new ApplicationException("请传入yyyy-MM格式日期！");
        }
        return organizationProductGoalService.importCompanyProductGoal(file, parse, updatedBy);
    }

    /**
     * 分公司主推品目标导出(废弃)
     *
     * @param request
     * @param response
     * @return: void
     * @date: 8/23/22 2:11 PM
     */
    @ApiOperation(value = "分公司主推品目标导出")
    @GetMapping(value = "/exportOrganizationGoal")
    @Deprecated
    public void exportOrganizationGoal(ProductQueryRequest request, HttpServletResponse response) {
        organizationProductGoalService.exportOrganizationGoal(request, response);
    }

    /*新目标管理*/

    /**
     * 新增主推品(废弃)
     *
     * @param request 需要保存数据
     * @return int
     * @since 2022-08-19
     */
    @Deprecated
    @ApiOperation(value = "新增主推品(废弃)")
    @PostMapping
    public Response<Integer> saveMainProduct(@Valid @RequestBody MainProductOperateRequest request) {
        return Response.success(mainProductService.saveMainProduct(request));
    }

    /**
     * 修改主推品(废弃)
     *
     * @param id
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 3/23/23 4:39 PM
     */
    @Deprecated
    @ApiOperation(value = "根据ID修改主推品(废弃)")
    @PutMapping("/{id}")
    public Response<Integer> updateMainProduct(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                                               @Valid @RequestBody MainProductOperateRequest request) {
        return Response.success(mainProductService.updateMainProduct(id, request));
    }

    /**
     * 根据ID删除主推品(废弃)
     *
     * @param id
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 8/24/22 2:42 PM
     */
    @Deprecated
    @ApiOperation(value = "根据ID删除主推品(废弃)")
    @DeleteMapping("/{id}/{updatedBy}")
    public Response<Integer> deleteMainProduct(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                                               @PathVariable("updatedBy") @NotNull(message = "操作人id不能为空") String updatedBy) {
        return Response.success(mainProductService.deleteMainProduct(id, updatedBy));
    }

    /**
     * 主推品目标列表(废弃)
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.mainProduct.vo.MainProductGoalVO>
     * @date: 3/23/23 3:50 PM
     */
    @Deprecated
    @ApiOperation(value = "主推品目标列表(废弃)")
    @GetMapping("/queryByPage")
    public Response<MainProductGoalVO> queryByPage(ProductQueryRequest request) {
        return Response.success(organizationProductGoalService.queryByPage(request));
    }

    /**
     * 修改分公司主推品目标(废弃)
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 3/23/23 6:53 PM
     */
    @Deprecated
    @ApiOperation(value = "修改分公司主推品目标(废弃)")
    @PutMapping("/updateOrganizationGoal")
    public Response<Integer> updateOrganizationGoal(@Valid @RequestBody OrganizationGoalOperateRequest request) {
        return Response.success(organizationProductGoalService.updateOrganizationGoal(request));
    }

    /**
     * 新增或修改大区主推品目标(废弃)
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 3/23/23 6:53 PM
     */
    @Deprecated
    @ApiOperation(value = "新增或修改大区主推品目标(废弃)")
    @PutMapping("/updateAreaGoal")
    public Response<Integer> updateAreaGoal(@Valid @RequestBody AreaGoalOperateRequest request) {
        return Response.success(organizationProductGoalService.updateAreaGoal(request));
    }

    /**
     * 删除组织主推品目标
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 3/23/23 6:54 PM
     */
    @ApiOperation(value = "删除组织主推品目标")
    @DeleteMapping("/deleteOrganizationGoal")
    public Response<Integer> deleteOrganizationGoal(@Valid @RequestBody OrganizationGoalOperateRequest request) {
        return Response.success(organizationProductGoalService.deleteOrganizationGoal(request));
    }

    /**
     * 主推品目标提交(废弃)
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 3/24/23 9:46 AM
     */
    @Deprecated
    @ApiOperation(notes = "主推品目标提交(废弃)", value = "主推品目标提交")
    @PostMapping("/submit")
    public Response submit(@Valid @RequestBody SubmitRequest request) {
        organizationProductGoalService.submit(request);
        return Response.success();
    }

    /*feature_V8.3.0新目标管理*/

    /**
     * 主推品目标导入模板
     */
    @ApiOperation(value = "主推品目标导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        organizationProductGoalService.importTemplate(request, response);
    }

    /**
     * 导入主推品目标
     */
    @ApiOperation(value = "导入主推品目标")
    @PostMapping("/import")
    public Response<Integer> importGoal(@RequestParam(value = "file") MultipartFile file,
                                        @ApiParam(value = "年", required = true) @RequestParam(value = "year") Integer year,
                                        @ApiParam(value = "季度", required = true) @RequestParam(value = "quarter") Integer quarter,
                                        @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {

        return organizationProductGoalService.importGoal(file, year, quarter, updatedBy);
    }

    /**
     * 主推品目标
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.mainProduct.vo.QuarterMainProductGoalVO>
     * @date: 6/14/23 10:02 AM
     */
    @ApiOperation(value = "主推品目标")
    @GetMapping("/quarterList")
    public Response<QuarterMainProductGoalVO> quarterList(QuarterProductQueryRequest request) {
        return Response.success(organizationProductGoalService.quarterList(request));
    }

    /**
     * 新增季度主推品
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 6/14/23 4:28 PM
     */
    @ApiOperation(value = "新增季度主推品")
    @PostMapping("/saveQuarter")
    public Response<Integer> saveQuarter(@Valid @RequestBody QuarterMainProductOperateRequest request) {
        return Response.success(mainProductService.saveQuarter(request));
    }

    @ApiOperation(value = "新增组织/合伙人主推品")
    @PostMapping("/batchSaveQuarter")
    public Response<Integer> batchSaveQuarter(@Valid @RequestBody List<QuarterMainProductOperateRequest> list) {
        log.info("batchSaveQuarter req:{}", JSON.toJSONString(list));
        return Response.success(mainProductService.batchSaveQuarter(list));
    }

    /**
     * 根据ID修改季度主推品
     *
     * @param id
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 6/14/23 6:22 PM
     */
    @ApiOperation(value = "根据ID修改季度主推品")
    @PutMapping("/updateQuarter/{id}")
    public Response<Integer> updateQuarter(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                                           @Valid @RequestBody QuarterMainProductOperateRequest request) {
        return Response.success(mainProductService.updateQuarter(id, request));
    }

    /**
     * 根据ID删除季度主推品
     *
     * @param id
     * @param updatedBy
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 6/14/23 6:49 PM
     */
    @ApiOperation(value = "根据ID删除季度主推品")
    @DeleteMapping("/deleteQuarter/{id}/{updatedBy}")
    public Response<Integer> deleteQuarter(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
                                           @PathVariable("updatedBy") @NotNull(message = "操作人id不能为空") String updatedBy) {
        return Response.success(mainProductService.deleteQuarter(id, updatedBy));
    }

    /**
     * 保存战区主推品目标
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 6/15/23 10:09 AM
     */
    @ApiOperation(value = "保存战区主推品目标")
    @PostMapping("/saveQuarterArea")
    public Response<Integer> saveQuarterArea(@Valid @RequestBody QuarterAreaGoalOperateRequest request) {
        return Response.success(organizationProductGoalService.saveQuarterArea(request));
    }

    /**
     * 保存标杆分公司主推品目标设置
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 6/15/23 1:18 PM
     */
    @ApiOperation(value = "保存标杆分公司主推品目标设置")
    @PostMapping("/saveQuarterCompany")
    public Response<Integer> saveQuarterCompany(@Valid @RequestBody QuarterCompanyOperateRequest request) {
        return Response.success(organizationProductGoalService.saveQuarterCompany(request));
    }

    /**
     * 删除标杆分公司主推品目标设置
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 6/15/23 3:28 PM
     */
    @ApiOperation(value = "删除标杆分公司主推品目标设置")
    @PutMapping("/deleteQuarterCompany")
    public Response<Integer> deleteQuarterCompany(@Valid @RequestBody QuarterCompanyDeleteRequest request) {
        return Response.success(organizationProductGoalService.deleteQuarterCompany(request));
    }

    /**
     * 季度主推品目标提交
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 6/15/23 3:55 PM
     */
    @ApiOperation(notes = "季度主推品目标提交", value = "季度主推品目标提交")
    @PostMapping("/submitQuarter")
    public Response submitQuarter(@Valid @RequestBody SubmitQuarterRequest request) {
        organizationProductGoalService.submitQuarter(request);
        return Response.success();
    }

    @ApiOperation(notes = "获取当月/当季主推品目标", value = "获取当月/当季主推品目标")
    @GetMapping("/getTargetDetailByDate")
    public Response<CurrentTargetDataRes> getTargetDetailByDate(@RequestParam(value = "date") String date,
                                                                @RequestParam(value = "positionId",required = false) String positionId) {
        return Response.success(mainProductService.getTargetDetailByDate(date,positionId));
    }

    @ApiOperation(notes = "获取当月/当季主推品列头", value = "获取当月/当季主推品列头")
    @GetMapping("/getTargetTitle")
    public Response<List<String>> getTargetTitle(@RequestParam(value = "date") String date) {
        HashMap<Integer,String> map=mainProductService.getTargetTitle(date);
        if (MapUtils.isNotEmpty(map)){
            return Response.success(new ArrayList<>(map.values()));
        }
        return Response.success(new ArrayList<>());
    }


    @ApiOperation(notes = "获取已经申请未审核的目标", value = "获取已经申请未审核的目标")
    @GetMapping("/getEmployeeSetGoalById")
    public Response<EmployeeSetGoalRes> getEmployeeSetGoalById(@RequestParam(value = "id") String id) {
        return Response.success(mainProductService.getEmployeeSetGoalById(id));
    }


    @Resource
    private PartnerGoalTask task;

    @ApiOperation(notes = "test", value = "test")
    @GetMapping("/mainProductGoalPushTask")
    public void mainProductGoalPushTask() {
        task.setGoalTimeOutNotice("date");
    }


}
