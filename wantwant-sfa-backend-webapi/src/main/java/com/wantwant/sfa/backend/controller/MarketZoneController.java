package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.marketZone.service.IMarketZoneService;
import com.wantwant.sfa.backend.organizationGoal.request.MarketZoneFoldRequest;
import com.wantwant.sfa.backend.organizationGoal.request.MarketZoneRequest;
import com.wantwant.sfa.backend.realData.vo.MarketInfoVo;
import com.wantwant.sfa.backend.realData.vo.MarketZoneVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/23/下午3:25
 */
@Slf4j
@Api(tags = "市场空间API")
@RestController
@RequestMapping("/marketZone")
public class MarketZoneController {

    @Resource
    private IMarketZoneService marketZoneService;


    @ApiOperation(value = "查询市场空间")
    @PostMapping("/search")
    public Response<MarketZoneVo> searchMarketZone(@RequestBody MarketZoneRequest marketZoneRequest){
        log.info("【market zone search】request:{}",marketZoneRequest);

        MarketZoneVo marketZoneVo =  marketZoneService.searchMarketZone(marketZoneRequest);

        return Response.success(marketZoneVo);
    }

    @ApiOperation(value = "查询市场空间折叠模式")
    @PostMapping("/searchFoldModel")
    public Response<List<MarketInfoVo>> searchMarketZoneFoldModel(@RequestBody MarketZoneFoldRequest marketZoneRequest){
        log.info("【market zone fold search】request:{}",marketZoneRequest);


        List<MarketInfoVo> list = marketZoneService.searchMarketZoneFold(marketZoneRequest);

        return Response.success(list);
    }

    @ApiOperation(value = "导出市场空间")
    @PostMapping("/export")
    public void exportMarketZone(@RequestBody MarketZoneRequest marketZoneRequest){
        log.info("【market zone export】request:{}",marketZoneRequest);

        marketZoneService.exportMarketZone(marketZoneRequest);
    }
}
