package com.wantwant.sfa.backend.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.meeting.request.*;
import com.wantwant.sfa.backend.meeting.vo.*;
import com.wantwant.sfa.backend.service.meeting.IMeetingDailyFileService;
import com.wantwant.sfa.backend.service.meeting.IMeetingPromiseService;
import com.wantwant.sfa.backend.service.meeting.MeetingInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 线下会议信息相关接口
*/
@Slf4j
@Api(tags = "线下会议信息相关接口")
@RestController
@RequestMapping("/meetingInfo")
public class MeetingInfoController {

	@Autowired
	private MeetingInfoService meetingInfoService;
	@Resource
	private IMeetingPromiseService meetingPromiseService;
	@Resource
	private IMeetingDailyFileService meetingDailyFileService;

	@ApiOperation(notes = "检查会议路由", value = "检查会议路由")
	@GetMapping("/checkMeetingRoute")
	public Response<MeetingInfoRouteVO> checkMeetingRoute(@ApiParam(value = "操作人工号", required = true) @RequestParam String person,
														  @ApiParam(value = "日期,格式yyyy-MM-dd",required = false)@RequestParam(required = false) String date,
														  @ApiParam(value = "会议类型",required = true)@RequestParam String category,
														  @ApiParam(value = "组织ID",required = false)@RequestParam(required = false) String organizationId,
														  @ApiParam(value = "任务ID",required = false)@RequestParam(required = false) Long taskId){
		log.info("【check meeting route】person:{},date:{},category:{}",person,date,category);

		MeetingInfoRouteVO meetingInfoRouteVO = meetingInfoService.checkMeetingRoute(person,date,category,organizationId,taskId);
		return Response.success(meetingInfoRouteVO);
	}

	@ApiOperation(notes = "开启/结束会议", value = "开启/结束会议")
	@PostMapping("/operate")
	public Response operate(@RequestBody @Valid MeetingOperateRequest meetingOperateRequest){
		log.info("【check meeting operate】request:{}",meetingOperateRequest);

		meetingInfoService.operate(meetingOperateRequest);
		return Response.success();
	}

	@ApiOperation(notes = "会议稽核", value = "会议稽核")
	@PostMapping("/audit")
	public Response audit(@RequestBody @Valid MeetingAuditRequest meetingAuditRequest){
		log.info("【meeting audit】request:{}",JSONObject.toJSONString(meetingAuditRequest));

		meetingInfoService.audit(meetingAuditRequest);

		return Response.success();
	}

	@ApiOperation(notes = "获取会议小红点", value = "获取会议小红点")
	@GetMapping("/getRedPoint")
	public Response<MeetingRedPointVO> getRedPoint(@ApiParam(value = "操作人工号", required = true) @RequestParam String person){
		MeetingRedPointVO meetingRedPointVO = meetingInfoService.getRedPoint(person);
		return Response.success(meetingRedPointVO);
	}


	@ApiOperation(notes = "会议列表", value = "会议列表")
	@PostMapping("/queryByPage")
	public Response<IPage<MeetingInfoVO>> queryByPage(@Valid @RequestBody MeetingQueryRequest request) {
		return Response.success(meetingInfoService.queryByPage(request));
	}

	@ApiOperation(value = "列表导出", notes = "列表导出")
	@PostMapping(value = "/exportList")
	public void exportList(@RequestBody MeetingQueryRequest request, HttpServletResponse response) {
		meetingInfoService.exportList(request,response);
	}

	@ApiOperation(value = "会议发起")
	@PostMapping("/save")
	public Response<Integer> save(@Valid @RequestBody MeetingSaveRequest request) {
		return Response.success(meetingInfoService.saveInfo(request));
	}

	@ApiOperation(notes = "会议删除", value = "会议删除")
	@DeleteMapping("/{infoId}")
	public Response<Integer> deleteMeeting(@PathVariable("infoId") @NotNull(message = "infoId不能为空！") Integer infoId) {
		return Response.success(meetingInfoService.deleteMeeting(infoId));
	}

	@ApiOperation(notes = "取消或终止会议", value = "取消或终止会议")
	@PutMapping("/{infoId}/{status}/{employeeId}")
	public Response<Integer> cancel(@PathVariable("infoId") @NotNull(message = "infoId不能为空！") Integer infoId,
									@ApiParam(value = "操作状态(1:已取消,3:终止)", required = true) @PathVariable("status") @NotNull(message = "操作状态不能为空！") Integer status,
									@PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
		return Response.success(meetingInfoService.cancel(infoId,status,employeeId));
	}

	@ApiOperation(notes = "会议详情", value = "会议详情")
	@GetMapping("/{infoId}/{employeeId}")
	public Response<MeetingDetailVO> getDetailByInfoId(@PathVariable("infoId") @NotNull(message = "infoId不能为空！") Integer infoId,
													   @PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
		return Response.success(meetingInfoService.getDetailByInfoId(infoId,employeeId));
	}

	@ApiOperation(value = "保存会议纪要")
	@PostMapping("/saveSummary")
	public Response<Integer> saveSummary(@Valid @RequestBody MeetingSummarySaveRequest request) {
		return Response.success(meetingInfoService.saveSummary(request));
	}

	@ApiOperation(value = "弹窗", notes = "弹窗")
	@GetMapping(value = "/list/{employeeId}")
	public Response<List<MeetingInfoVO>> listByEmpId(@PathVariable("employeeId") @NotNull(message = "登录人工号不能为空！") String employeeId) {
		return Response.success(meetingInfoService.listByEmpId(employeeId));
	}

	@ApiOperation(notes = "参加会议", value = "参加会议")
	@PostMapping("/attend")
	public Response<Integer> attend(@Valid @RequestBody AttendMeetingRequest request) {
		return Response.success(meetingInfoService.attend(request));
	}

	@ApiOperation(notes = "设置批注", value = "设置批注")
	@PostMapping("/setNotes")
	public Response<Integer> setNotes(@Valid @RequestBody NotesRequest request) {
		return Response.success(meetingInfoService.setNotes(request));
	}

	@ApiOperation(notes = "签到", value = "签到")
	@PostMapping("/checkIn")
	public Response<Integer> checkIn(@Valid @RequestBody CheckInRequest request) {
		return Response.success(meetingInfoService.checkIn(request));
	}

	@ApiOperation(value = "根据组织获取地址")
	@GetMapping("/address")
	public Response<List<OrganizationAddressVO>> getAddress(@ApiParam(value = "organizationId",required = true) @RequestParam String organizationId){
		log.info("【get address】organizationId:{}",organizationId);
		return Response.success(meetingInfoService.getAddress(organizationId));
	}

	@ApiOperation(value = "上传会议照片")
	@PostMapping("/uploadMeeting")
	public Response uploadMeetingPhoto(@RequestBody @Valid MeetingPhotoRequest meetingPhotoRequest){
		log.info("【upload meeting photo】request:{}",meetingPhotoRequest);

		meetingInfoService.uploadMeetingPhoto(meetingPhotoRequest);
		return Response.success();
	}

	@ApiOperation(value = "会议可选参会人")
	@GetMapping("/selectEmp")
	public Response<MeetingSelectEmpVo> selectEmpVos(@ApiParam(value = "会议主办人", required = true) @RequestParam String person,
													 @ApiParam(value = "查询类型:1.按组织 2.按合伙人 3.按岗位",required = false)@RequestParam(required = false)Integer searchType,
												     @ApiParam(value = "查询关键字 ",required = false)@RequestParam(required = false)String key,
													 @ApiParam(value = "会议日期 ",required = false)@RequestParam(required = false)String day,
													 @ApiParam(value = "是否过滤合伙人")boolean filterCeo,
													 @ApiParam(value = "产品组", required = true)Integer businessGroup){
		log.info("【meeting select emp】person:{},key:{},day{},businessGroup{}",person,key,day,businessGroup);
		MeetingSelectEmpVo meetingSelectEmpVo = meetingInfoService.selectEmpVos(person,searchType,key,day,filterCeo,businessGroup);
		return Response.success(meetingSelectEmpVo);
	}


	@ApiOperation(value = "会议可选参会人")
	@GetMapping("/selectEmpForWeb")
	public Response<MeetingSelectEmpVo> selectEmpVosForWeb(@ApiParam(value = "会议主办人", required = true) @RequestParam String person,
													 @ApiParam(value = "查询类型:1.按组织 2.按合伙人 3.按岗位",required = false)@RequestParam(required = false)Integer searchType,
													 @ApiParam(value = "查询关键字 ",required = false)@RequestParam(required = false)String key,
													 @ApiParam(value = "会议日期 ",required = false)@RequestParam(required = false)String day,
													 @ApiParam(value = "是否过滤合伙人")boolean filterCeo,
													 @ApiParam(value = "产品组", required = true)Integer businessGroup){
		log.info("【meeting select emp】person:{},key:{},day{},businessGroup:{}",person,key,day,businessGroup);
		MeetingSelectEmpVo meetingSelectEmpVo = meetingInfoService.selectEmpVosForWeb(person,searchType,key,day,filterCeo,businessGroup);
		return Response.success(meetingSelectEmpVo);
	}


	@ApiOperation(value = "根据会议ID获取已选择的人员列表")
	@GetMapping("/getSelectedEmp/{infoId}")
	public Response<List<MeetingEmpVo>> getSelectedEmp(@PathVariable("infoId") @NotNull(message = "infoId不能为空！") Integer infoId){
		log.info("【meeting select emp】infoId:{}",infoId);
		List<MeetingEmpVo> list = meetingInfoService.getSelectedEmp(infoId);
		return Response.success(list);
	}


	@ApiOperation(value = "获取会议承诺信息")
	@GetMapping("/selectMeetingPromise")
	public Response<List<MeetingPromiseVO>> selectMeetingPromise(@ApiParam(value = "会议主办人", required = true) @RequestParam String person,
													 @ApiParam(value = "infoId ",required = true)@RequestParam(required = true)Integer infoId){
		log.info("【select meeting promise】infoId:{},person:{}",infoId,person);

		List<MeetingPromiseVO> list = meetingInfoService.selectMeetingPromise(infoId,person);

		return Response.success(list);
	}


	@ApiOperation(value = "业绩承诺分配")
	@PostMapping("/promiseAllocate")
	public Response promiseAllocate(@RequestBody @Valid MeetingPromiseRequest meetingPromiseRequest){

		log.info("【meeting promise】request:{}",meetingPromiseRequest);

		meetingPromiseService.promiseAllocate(meetingPromiseRequest);

		return Response.success();
	}


	@ApiOperation(value = "分配合伙人查询")
	@GetMapping("/searchAllocateCeo")
	public Response<List<CeoVO>> searchAllocateCeo(@ApiParam(value = "营业所code", required = true) @RequestParam String departmentCode,
												   @ApiParam(value = "1，已建档  2，未建档，3，不确定", required = true) @RequestParam Integer type,
											       @ApiParam(value = "合伙人手机号或姓名",required = false)@RequestParam(required = false)String key){
		log.info("【meeting search allocated ceo】departmentCode:{},key:{},type:{}",departmentCode,key,type);

		List<CeoVO> list = meetingPromiseService.searchAllocateCeo(departmentCode,key,type);

		return Response.success(list);
	}

	@ApiOperation(value = "获取承诺分配")
	@GetMapping("/selectPromiseAllocated")
	public Response<List<MeetingAllocatedVO>> selectPromiseAllocated(@ApiParam(value = "承诺ID ",required = true)@RequestParam(required = false)Long promiseId,
																	 @ApiParam(value = "操作人工号", required = true) @RequestParam String person){
		log.info("【search meeting allocated 】promiseId:{},person:{}",promiseId,person);

		List<MeetingAllocatedVO> list = meetingPromiseService.selectPromiseAllocated(promiseId,person);

		return Response.success(list);
	}

	@ApiOperation(value = "保存批注")
	@PostMapping("/saveAnnotation")
	public Response saveAnnotation(@Valid @RequestBody MeetingInfoAnnotationRequest meetingInfoAnnotationRequest){

		log.info("【save annotation】request:{}",meetingInfoAnnotationRequest);

		meetingPromiseService.saveAnnotation(meetingInfoAnnotationRequest);
		return Response.success();
	}


	@ApiOperation(value = "保存分配批注")
	@PostMapping("/saveAllocatedAnnotation")
	public Response saveAllocatedAnnotation(@RequestBody MeetingAllocatedAnnotationRequest meetingAllocatedAnnotationRequest) {
		log.info("【save allocated annotation】request:{}", meetingAllocatedAnnotationRequest);
		meetingPromiseService.saveAllocatedAnnotation(meetingAllocatedAnnotationRequest);
		return Response.success();
	}

	@ApiOperation(value = "获取日会文件")
	@GetMapping("/getDailyMeeting")
	public Response<List<DailyMeetingVO>> getDailyMeeting(@ApiParam(value = "infoId ",required = true)@RequestParam(required = true)Integer infoId,
														  @ApiParam(value = "模式:1.平铺 2.下拉模式",required = true)@RequestParam(required = true)Integer model,
														  @ApiParam(value = "父组织ID", required = false) @RequestParam(required = false) String parentOrgCode){
		log.info("【get daily meeting】infoId:{},parentOrgCode:{},model:{}",infoId,parentOrgCode,model);

		List<DailyMeetingVO> list = meetingDailyFileService.getDailyMeeting(infoId,model,parentOrgCode);

		return Response.success(list);
	}

	@ApiOperation(value = "获取日会数据时间")
	@GetMapping("/getDailyFileDate")
	public Response<DailyMeetingFileVO> getDailyFileDate(@ApiParam(value = "infoId ",required = true)@RequestParam(required = true)Integer infoId){

		log.info("【get daily meeting file】infoId:{}",infoId);

		DailyMeetingFileVO dailyMeetingFileVO = meetingDailyFileService.getDailyFileDate(infoId);

		return Response.success(dailyMeetingFileVO);
	}

	@ApiOperation(value = "保存聚餐资料")
	@PostMapping("/saveDinnerInfo")
	public Response saveDinnerInfo(@Validated @RequestBody MeetingDinnerRequest meetingDinnerRequest){
		log.info("【save dinner info】request:{}", JSONObject.toJSONString(meetingDinnerRequest));

		meetingInfoService.saveDinnerInfo(meetingDinnerRequest);

		return Response.success();
	}

	@ApiOperation(value = "获取销售数据")
	@GetMapping("/saleDate/{infoId}")
	public Response<List<SaleDateVO>> getSaleDate(@PathVariable Integer infoId){
		log.info("【get sale date】infoId:{}",infoId);
		DailyMeetingFileVO dailyMeetingFileVO = meetingDailyFileService.getDailyFileDate(infoId);

		List<SaleDateVO> list = meetingInfoService.getSaleDate(infoId,dailyMeetingFileVO);

		return Response.success(list);
	}

	@ApiOperation(value = "根据会议获取直属下级信息")
	@GetMapping("/subordinateUser/{infoId}")
	public Response<List<SubordinateUserVo>> getSubordinateUser(@PathVariable Integer infoId){
		log.info("【get subordinate user date】infoId:{}",infoId);

		List<SubordinateUserVo> list = meetingInfoService.getSubordinateUser(infoId);

		return Response.success(list);
	}


	@ApiOperation(value = "获取聚餐信息")
	@GetMapping("/dinner/{infoId}")
	public Response<MeetingDinnerVO> getDinnerInfo(@PathVariable Integer infoId){
		log.info("【get dinner】infoId:{}",infoId);
		MeetingDinnerVO meetingDinnerVO = meetingInfoService.getDinnerInfo(infoId);
		return Response.success(meetingDinnerVO);
	}

	@ApiOperation(value = "获取最后一次打卡点")
	@GetMapping("/checkInPosition/{infoId}/{empId}")
	public Response<CheckInPositionVO> getCheckInPosition(@PathVariable Integer infoId,@PathVariable String empId){
		log.info("【check in position】infoId:{},empId:{}",infoId,empId);

		CheckInPositionVO checkInPositionVO = meetingInfoService.getCheckInPosition(infoId,empId);
		return Response.success(checkInPositionVO);
	}

	@ApiOperation(value = "检查会议是否可开始")
	@GetMapping("/checkMeetingStart/{infoId}")
	public Response<MeetingStartCheckVO> checkMeetingStart(@PathVariable Integer infoId){
		MeetingStartCheckVO meetingStartCheckVO  = meetingInfoService.checkMeetingStart(infoId);
		return Response.success(meetingStartCheckVO);
	}


	@ApiOperation(value = "获取会议通知")
	@GetMapping("/notice/{empId}")
	public Response<List<MeetingNoticeVO>> notice(@PathVariable String empId){
		List<MeetingNoticeVO> list = meetingInfoService.notice(empId);
		return Response.success(list);
	}
}
