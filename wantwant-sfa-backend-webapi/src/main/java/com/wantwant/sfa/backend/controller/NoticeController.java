package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.NotifyApplication;
import com.wantwant.sfa.backend.monitoringSku.vo.SkuVo;
import com.wantwant.sfa.backend.notice.api.NoticeApi;
import com.wantwant.sfa.backend.notice.request.CeoNotifyRequest;
import com.wantwant.sfa.backend.notice.request.MandatoryReviewRequest;
import com.wantwant.sfa.backend.notice.vo.MandatoryNoticeVO;
import com.wantwant.sfa.backend.notice.vo.NoticeVo;
import com.wantwant.sfa.backend.service.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/07/21/上午11:34
 */
@RestController
public class NoticeController implements NoticeApi {
    @Autowired
    private NoticeService noticeService;
    @Resource
    private NotifyApplication notifyApplication;

    @Override
    public Response<NoticeVo> MonitoringSkuList(String employId) {
        return Response.success(noticeService.selectAuditoList(employId));
    }

    @Override
    public Response<List<MandatoryNoticeVO>> getMandatoryNotice(String empId) {
        List<MandatoryNoticeVO> list = notifyApplication.getMandatoryNotice(empId);
        return Response.success(list);
    }

    @Override
    public Response read(@Valid MandatoryReviewRequest mandatoryReviewRequest) {
        notifyApplication.read(mandatoryReviewRequest.getId(),mandatoryReviewRequest.getPerson());
        return Response.success();
    }

    @Override
    public Response ceoNotify(CeoNotifyRequest ceoNotifyRequest) {
        notifyApplication.ceoNotify(ceoNotifyRequest);

        return Response.success();
    }
}
