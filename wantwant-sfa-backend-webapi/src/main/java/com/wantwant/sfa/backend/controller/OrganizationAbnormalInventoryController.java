package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.abnormal.request.*;
import com.wantwant.sfa.backend.abnormal.request.overview.InventoryConditionRequest;
import com.wantwant.sfa.backend.abnormal.vo.*;
import com.wantwant.sfa.backend.abnormal.vo.overview.*;
import com.wantwant.sfa.backend.service.OrganizationAbnormalInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
*
* @since 2023-08-21
*/
@Api(tags = "异常库存相关接口")
@RestController
@RequestMapping("/abnormalInventory")
public class OrganizationAbnormalInventoryController {

	@Autowired
	private OrganizationAbnormalInventoryService abnormalInventoryService;

	@ApiOperation(notes = "物料下拉框", value = "物料下拉框")
	@GetMapping("/listSkuInfo")
	public Response<List<LineVO>> listSkuInfo(@ApiParam("物料名称") @RequestParam(value = "key",required = false) String skuName) {
		return Response.success(abnormalInventoryService.listSkuInfo(skuName));
	}

	@ApiOperation(notes = "异常库存查询列表", value = "异常库存查询列表")
	@GetMapping("/queryReportByPage")
	public Response<IPage<OrgAbnormalInventoryReportVO>> queryReportByPage(ReportQueryRequest request) {
		return Response.success(abnormalInventoryService.queryReportByPage(request));
	}

	@ApiOperation(value = "异常库存查询列表导出", notes = "异常库存查询列表导出")
	@GetMapping(value = "/exportReportList")
	public void exportReportList(ReportQueryRequest request, HttpServletResponse response) {
		abnormalInventoryService.exportReportList(request,response);
	}

	@ApiOperation(notes = "异常库存申请列表", value = "异常库存申请列表")
	@GetMapping("/queryApplyByPage")
	public Response<IPage<OrgAbnormalInventoryVO>> queryApplyByPage(QueryRequest request) {
		return Response.success(abnormalInventoryService.queryApplyByPage(request));
	}

	@ApiOperation(value = "异常库存申请列表导出", notes = "异常库存申请列表导出")
	@GetMapping(value = "/exportApplyList")
	public void exportApplyList(QueryRequest request, HttpServletResponse response) {
		abnormalInventoryService.exportApplyList(request,response);
	}

	@ApiOperation(notes = "异常库存审批列表", value = "异常库存审批列表")
	@GetMapping("/queryAuditByPage")
	public Response<IPage<OrgAbnormalInventoryAuditVO>> queryAuditByPage(QueryRequest request) {
		return Response.success(abnormalInventoryService.queryAuditByPage(request));
	}

	@ApiOperation(value = "异常库存审批列表导出", notes = "异常库存审批列表导出")
	@GetMapping(value = "/exportAuditByPage")
	public void exportAuditByPage(QueryRequest request, HttpServletResponse response) {
		abnormalInventoryService.exportAuditByPage(request,response);
	}

	@ApiOperation(notes = "异常库存详情", value = "异常库存详情")
	@GetMapping("/details/{id}/{employeeId}")
	public Response<OrgAbnormalInventoryVO> details(@PathVariable("id") @NotNull(message = "id不能为空") Integer id,
													@PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
		return Response.success(abnormalInventoryService.details(id,employeeId));
	}

	@ApiOperation(notes = "异常库存申请详情", value = "异常库存申请详情")
	@PostMapping("/applyDetails")
	public Response<OrgAbnormalInventoryModifyVO> applyDetails(@Valid @RequestBody ModifyRequest request) {
		return Response.success(abnormalInventoryService.applyDetails(request));
	}

	@ApiOperation(notes = "异常库存申请提交", value = "异常库存申请提交")
	@PostMapping("/applySubmit")
	public Response<Integer> applySubmit(@Valid @RequestBody InventorySubmitRequest request) {
		return Response.success(abnormalInventoryService.applySubmit(request));
	}

	@ApiOperation(notes = "异常库存审核详情", value = "异常库存审核详情")
	@GetMapping("/auditDetails/{id}")
	public Response<OrgAbnormalInventoryVO> auditDetails(@PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
		return Response.success(abnormalInventoryService.auditDetails(id));
	}

	@ApiOperation(notes = "异常库存审批", value = "异常库存审批")
	@PostMapping("/audit")
	public Response audit(@Valid @RequestBody AuditRequest request) {
		return abnormalInventoryService.audit(request);
	}

	/*总览*/
    @ApiOperation(notes = "异常库存的品项分布图", value = "异常库存的品项分布图")
    @GetMapping("/itemDistribution/{employeeId}")
    public Response<ItemDistributionResVO> itemDistribution(@PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
        return Response.success(abnormalInventoryService.itemDistribution(employeeId));
    }

	@ApiOperation(notes = "异常库存的货龄和仓库分布图", value = "异常库存的货龄和仓库分布图")
	@GetMapping("/channelDistribution/{employeeId}")
	public Response<List<ChannelDistributionVO>> channelDistribution(@PathVariable("employeeId") @NotNull(message = "登录人工号") String employeeId) {
		return Response.success(abnormalInventoryService.channelDistribution(employeeId));
	}

	@ApiOperation(notes = "分公司待处理库存箱数占比", value = "分公司待处理库存箱数占比")
	@PostMapping("/organizationInventory")
	public Response<InventoryInfoVO> organizationInventory(@Valid @RequestBody InventoryConditionRequest request) {
		return Response.success(abnormalInventoryService.organizationInventory(request));
	}

	@ApiOperation(notes = "分公司Top20", value = "分公司Top20")
	@PostMapping("/companyTop")
	public Response<List<CompanyTopInfoVO>> companyTop(@Valid @RequestBody InventoryConditionRequest request) {
		return Response.success(abnormalInventoryService.companyTop(request));
	}

	@ApiOperation(notes = "品项Top20", value = "品项Top20")
	@PostMapping("/itemTop")
	public Response<List<ItemTopInfoVO>> itemTop(@Valid @RequestBody InventoryConditionRequest request) {
		return Response.success(abnormalInventoryService.itemTop(request));
	}

	@ApiOperation(notes = "处理进度", value = "处理进度")
	@PostMapping("/skuProgress")
	public Response<List<SkuProgressVO>> skuProgress(@Valid @RequestBody InventoryConditionRequest request) {
		return Response.success(abnormalInventoryService.skuProgress(request));
	}


}
