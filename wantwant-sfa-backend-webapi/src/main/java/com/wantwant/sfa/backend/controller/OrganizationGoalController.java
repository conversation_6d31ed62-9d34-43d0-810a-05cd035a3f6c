package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.Task.GoalTask;
import com.wantwant.sfa.backend.mainProduct.request.OrgGoalBatchSaveRequest;
import com.wantwant.sfa.backend.mainProduct.request.ProductQueryRequest;
import com.wantwant.sfa.backend.mainProduct.request.QuarterProductQueryRequest;
import com.wantwant.sfa.backend.organizationGoal.request.*;
import com.wantwant.sfa.backend.organizationGoal.vo.*;
import com.wantwant.sfa.backend.service.InfoCentreService;
import com.wantwant.sfa.backend.service.OrganizationGoalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
* 组织目标相关接口
* @since 2022-08-23
*/
@Api(tags = "组织目标相关接口")
@RestController
@RequestMapping("/organizationGoal")
public class OrganizationGoalController {

	@Resource
	private OrganizationGoalService organizationGoalService;

	@Resource
	private InfoCentreService infoCentreService;

	@Autowired
	private GoalTask goalTask;

	@ApiOperation(value = "预警目标导入模板")
	@GetMapping("/template")
	public void template(HttpServletRequest request, HttpServletResponse response) {
		organizationGoalService.template(request, response);
	}

	@ApiOperation(value = "预警目标导入（月度）")
	@PostMapping("/import")
	public Response<Integer> importGoal(@RequestParam(value = "file") MultipartFile file,
										@ApiParam(value = "生效日期yyyy-MM", required = true) @RequestParam(value = "effectiveDate") String effectiveDate,
										@ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {
		//其它目标：营运组
//		String processName = infoCentreService.getProcessName(updatedBy);
//		if (!processName.contains("营运管理")){
//			throw new ApplicationException("当前用户不能修改！");
//		}
		LocalDate parse;
		try {
			parse = LocalDate.parse(effectiveDate+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		} catch (Exception e) {
			throw new ApplicationException("请传入yyyy-MM格式日期！");
		}
		return organizationGoalService.importGoal(file,parse,updatedBy);
	}

	@ApiOperation(value = "预警目标导入（季度）")
	@PostMapping("/quarterImport")
	public Response<Integer> quarterImport(@RequestParam(value = "file") MultipartFile file,
										   @ApiParam(value = "年", required = true) @RequestParam(value = "year") Integer year,
										   @ApiParam(value = "季度", required = true) @RequestParam(value = "quarter") Integer quarter,
										   @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {
		return organizationGoalService.quarterImport(file, year, quarter, updatedBy);
	}

	@ApiOperation(value = "预警目标查询（月度）")
	@GetMapping("/page")
	public Response<IPage<OrganizationGoalVO>> pageGoal(GoalQueryRequest request) {
		//预警目标：营运组
//		String processName = infoCentreService.getProcessName(request.getEmployeeId());
//		if (!processName.contains("营运管理部")){
//			throw new ApplicationException("当前用户不能修改！");
//		}
		return Response.success(organizationGoalService.pageGoal(request));
	}

	@ApiOperation(value = "预警目标查询（季度）")
	@GetMapping("/quarterPage")
	public Response<IPage<OrganizationGoalVO>> quarterPage(QuarterGoalQueryRequest request) {
		return Response.success(organizationGoalService.quarterPage(request));
	}

	@ApiOperation(value = "预警目标导出（月度）")
	@GetMapping("/export")
	public void exportGoal(GoalQueryRequest request,HttpServletResponse response) {
		organizationGoalService.exportGoal(request,response);
	}

	@ApiOperation(value = "预警目标导出（季度）")
	@GetMapping("/quarterExport")
	public void quarterExport(QuarterGoalQueryRequest request, HttpServletResponse response) {
		organizationGoalService.quarterExport(request, response);
	}

	@ApiOperation(value = "预警目标修改（月度）")
	@PostMapping("/modifyGoal")
	public Response modifyOtherGoal(@RequestBody GoalModifyRequest request){
		//其它目标：营运组
//		String processName = infoCentreService.getProcessName(request.getUpdatedBy());
//		if (!processName.contains("营运管理")){
//			throw new ApplicationException("当前用户不能修改！");
//		}

		return organizationGoalService.updateGoal(request);
	}

	@ApiOperation(value = "预警目标修改（季度）")
	@PostMapping("/quarterModify")
	public Response quarterModify(@RequestBody GoalModifyRequest request) {
		return organizationGoalService.quarterModify(request);
	}

	/*新营业所目标*/

	/**
	 * 目标说明
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 3/24/23 10:35 AM
	 */
	@ApiOperation(value = "目标说明")
	@GetMapping("/goalShow")
	public Response<DepartGoalShowVO> goalShow(DepartmentGoalQueryRequest request){
		return Response.success(organizationGoalService.goalShow(request));
	}

	/**
	 * 设置分公司/营业所目标
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 1/29/23 4:30 PM
	 */
	@ApiOperation(notes = "设置分公司/营业所目标", value = "设置分公司/营业所目标")
	@PostMapping("/setDepartmentGoal")
	public Response<Integer> setDepartmentGoal(@Valid @RequestBody DepartmentSetRequest request) {
		return Response.success(organizationGoalService.setDepartmentGoal(request));
	}

	@GetMapping("/queryTaskCount")
	public Response<Integer> queryTaskCount(@RequestParam("employeeId") String employeeId,@RequestParam("organizationId") String organizationId){

		goalTask.sendMessage("");
		return Response.success(0);
	}

	/*新目标管理*/

	/**
	 * 全品项目标导入模板(废弃)
	 *
	 * @param request
	 * @param response
	 * @return: void
	 * @date: 3/22/23 10:06 AM
	 */
	@Deprecated
	@ApiOperation(value = "全品项目标导入模板(废弃)")
	@GetMapping("/allItemsTemplate")
	public void allItemsTemplate(HttpServletRequest request, HttpServletResponse response) {
		organizationGoalService.allItemsTemplate(request, response);
	}

	/**
	 * 全品项目标导入(废弃)
	 *
	 * @param file
	 * @param effectiveDate
	 * @param updatedBy
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
	 * @date: 3/22/23 11:09 AM
	 */
	@Deprecated
	@ApiOperation(value = "全品项目标导入(废弃)")
	@PostMapping("/allItemsImport")
	public Response<Integer> allItemsImport(@RequestParam(value = "file") MultipartFile file,
												@ApiParam(value = "生效日期yyyy-MM", required = true) @RequestParam(value = "effectiveDate") String effectiveDate,
												@ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {
		LocalDate parse;
		try {
			parse = LocalDate.parse(effectiveDate+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		} catch (Exception e) {
			throw new ApplicationException("请传入yyyy-MM格式日期！");
		}
		return organizationGoalService.allItemsImport(file,parse,updatedBy);
	}

	/**
	 * 设置提报日期(废弃)
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 3/22/23 4:01 PM
	 */
	@Deprecated
	@ApiOperation(notes = "设置提报日期(废弃)", value = "设置提报日期")
	@PostMapping("/allItemsSubmit")
	public Response allItemsSubmit(@Valid @RequestBody AllItemsSubmitRequest request) {
		organizationGoalService.allItemsSubmit(request);
		return Response.success();
	}

	/**
	 * 全品项目标列表(废弃)
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.organizationGoal.vo.AllItemsGoalInfoVO>
	 * @date: 3/22/23 5:10 PM
	 */
	@Deprecated
	@ApiOperation(value = "全品项目标列表(废弃)")
	@GetMapping("/allItemsList")
	public Response<AllItemsGoalInfoVO> allItemsList(ProductQueryRequest request) {
		return Response.success(organizationGoalService.allItemsList(request));
	}

	/**
	 * 目标查询列表
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.organizationGoal.vo.OrganizationGoalListVO>>
	 * @date: 3/27/23 11:15 AM
	 */
	@ApiOperation(value = "目标查询")
	@GetMapping("/queryByPage")
	public Response<IPage<OrganizationGoalListVO>> queryByPage(OrgGoalQueryRequest request) {
		return Response.success(organizationGoalService.queryByPage(request));
	}

	/*feature_V8.4.0-20230613新目标设置*/

	/**
	 * 总业绩目标
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.organizationGoal.vo.TotalPerformanceGoalInfoVO>
	 * @date: 6/13/23 2:24 PM
	 */
	@ApiOperation(value = "总业绩目标")
	@GetMapping("/totalPerformanceGoal")
	public Response<TotalPerformanceGoalInfoVO> totalPerformanceGoalList(TotalPerformanceRequest request) {
		return Response.success(organizationGoalService.totalPerformanceGoalList(request));
	}

	/**
	 * 总业绩目标导入模板
	 *
	 * @param request
	 * @param response
	 * @return: void
	 * @date: 6/13/23 3:37 PM
	 */
	@ApiOperation(value = "总业绩目标导入模板")
	@GetMapping("/totalPerformanceTemplate")
	public void totalPerformanceTemplate(HttpServletRequest request, HttpServletResponse response) {
		organizationGoalService.totalPerformanceTemplate(request, response);
	}

	/**
	 * 总业绩目标导入
	 *
	 * @param file
	 * @param year
	 * @param quarter
	 * @param updatedBy
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 6/13/23 3:41 PM
	 */
	@ApiOperation(value = "总业绩目标导入")
	@PostMapping("/totalPerformanceImport")
	public Response<Integer> totalPerformanceImport(@RequestParam(value = "file") MultipartFile file,
											@ApiParam(value = "年", required = true) @RequestParam(value = "year") Integer year,
											@ApiParam(value = "季度", required = true) @RequestParam(value = "quarter") Integer quarter,
											@ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {
		return organizationGoalService.totalPerformanceImport(file,year,quarter,updatedBy);
	}

	/**
	 * 总业绩目标确认
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 6/13/23 5:06 PM
	 */
	@ApiOperation(notes = "总业绩目标确认", value = "总业绩目标确认")
	@PostMapping("/totalPerformanceConfirm")
	public Response totalPerformanceConfirm(@Valid @RequestBody TotalPerformanceConfirmRequest request) {
		organizationGoalService.totalPerformanceConfirm(request);
		return Response.success();
	}

	/**
	 * 区域目标
	 *
	 * @param request
	 * @return: java.util.List<com.wantwant.sfa.backend.organizationGoal.vo.VareaGoalDetail>
	 * @date: 6/16/23 1:40 PM
	 */
	@ApiOperation(value = "区域目标")
	@GetMapping("/areaGoal")
	public Response<List<VareaGoalDetail>> areaGoal(QuarterProductQueryRequest request){
		return Response.success(organizationGoalService.areaGoal(request));
	}

	/**
	 * 省区目标
	 *
	 * @param request
	 * @return: java.util.List<com.wantwant.sfa.backend.organizationGoal.vo.VareaGoalDetail>
	 * @date: 6/19/23 7:42 PM
	 */
	@ApiOperation(value = "省区目标")
	@GetMapping("/provinceGoal")
	public Response<List<VareaGoalDetail>> provinceGoal(QuarterProductQueryRequest request){
		return Response.success(organizationGoalService.provinceGoal(request));
	}

	/**
	 * 分公司目标
	 *
	 * @param request
	 * @return: java.util.List<com.wantwant.sfa.backend.organizationGoal.vo.VareaGoalDetail>
	 * @date: 6/20/23 9:38 AM
	 */
	@ApiOperation(value = "分公司目标")
	@GetMapping("/companyGoal")
	public Response<List<VareaGoalDetail>> companyGoal(QuarterProductQueryRequest request){
		return Response.success(organizationGoalService.companyGoal(request));
	}

	/**
	 * 营业所目标
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.organizationGoal.vo.VareaGoalDetail>>
	 * @date: 6/20/23 3:59 PM
	 */
	@ApiOperation(value = "营业所目标")
	@GetMapping("/departmentGoal")
	public Response<List<VareaGoalDetail>> departmentGoal(QuarterProductQueryRequest request){
		return Response.success(organizationGoalService.newDepartmentGoal(request));
	}

	/**
	 * 季度目标设置截止日期
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 6/20/23 5:19 PM
	 */
	@ApiOperation(value = "季度目标设置截止日期")
	@GetMapping("/goalSetDay")
	public Response<Integer> goalSetDay(QuarterProductQueryRequest request){
		return Response.success(organizationGoalService.goalSetDay(request));
	}

	/**
	 * 提交区域目标
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 6/19/23 10:54 AM
	 */
	@ApiOperation(value = "提交目标")
	@PostMapping("/submitAreaGoal")
	public Response<Integer> submitAreaGoal(@Valid @RequestBody OrgGoalBatchSaveRequest request){
		return Response.success(organizationGoalService.submitOrgGoal(request));
	}


}
