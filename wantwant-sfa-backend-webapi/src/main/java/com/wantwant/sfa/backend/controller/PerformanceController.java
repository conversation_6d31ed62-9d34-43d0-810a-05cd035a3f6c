package com.wantwant.sfa.backend.controller;

import com.wantwant.sfa.backend.application.PerformanceApplication;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.performance.vo.PerformanceModuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.performance.api.PerformanceApi;
import com.wantwant.sfa.backend.performance.request.ExecutePerformanceTaskRequest;
import com.wantwant.sfa.backend.performance.request.PerformanceByPosIdRequest;
import com.wantwant.sfa.backend.performance.vo.PerformanceByPosIdVo;
import com.wantwant.sfa.backend.service.PerformanceService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: luxiaoyin
 * @Date: 2020/5/26
 * @Package: com.wantwant.sfa.backend.controller
 */
@RestController
public class PerformanceController implements PerformanceApi {
    @Autowired
    private PerformanceService performanceService;
    @Resource
    private PerformanceApplication performanceApplication;

    @Override
    public Response<PerformanceByPosIdVo> getByPosId(PerformanceByPosIdRequest performanceByPosIdRequest) {
        return performanceService.getByPosId(performanceByPosIdRequest);
    }
	@Override
	public Response executePerformanceTask(ExecutePerformanceTaskRequest request) {
		performanceService.computePerformanceTask(request.getDate());
		return Response.success();
	}

    @Override
    public Response<List<PerformanceModuleVO>> getPerformanceModule(String routeModule,String date) {
        List<PerformanceModuleVO> performanceModuleVOS = performanceApplication.selectConfigModule(RequestUtils.getLoginInfo().getPositionTypeId(), routeModule,date);
        return Response.success(performanceModuleVOS);
    }

}
