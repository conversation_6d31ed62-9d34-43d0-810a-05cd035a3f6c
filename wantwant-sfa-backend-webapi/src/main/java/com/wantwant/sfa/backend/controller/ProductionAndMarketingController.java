package com.wantwant.sfa.backend.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.mapper.ProductionMapper;
import com.wantwant.sfa.backend.productionAndMarketing.api.ProductionAndMarketingApi;
import com.wantwant.sfa.backend.productionAndMarketing.request.*;
import com.wantwant.sfa.backend.productionAndMarketing.vo.*;
import com.wantwant.sfa.backend.realData.vo.SafetyStockConditionVo;
import com.wantwant.sfa.backend.realData.vo.SafetyStockLogVo;
import com.wantwant.sfa.backend.realData.vo.SafetyStockVo;
import com.wantwant.sfa.backend.realData.vo.SkuSpecificationVo;
import com.wantwant.sfa.backend.productionAndMarketing.service.ProductionAndMarketingService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class ProductionAndMarketingController implements ProductionAndMarketingApi {

  @Autowired private ProductionAndMarketingService productionAndMarketingService;

  @Autowired private RedisUtil redisUtil;

  public static final String LOCK_HEAD_SALES_FORECAST = "salesForecast";

  @Autowired private ProductionMapper productionMapper;

  @Override
  public Response<Page<ProductionAndMarketingVo>> getProductionAndMarketingList(
      ProductionAndMarketingRequest request) {
    return Response.success(productionAndMarketingService.getProductionAndMarketingList(request));
  }

  @Override
  public Response<ProductionGroupbyVo> getProductionAndMarketingscreening() {
    return Response.success(productionAndMarketingService.getProductionAndMarketingScreening());
  }

  @Override
  public Response<Integer> salesForecastImport(MultipartHttpServletRequest request) {
    log.info("start ProductionAndMarketingController salesForecastImport request:{}", request);

    try {
      String s = productionAndMarketingService.insertByExcel(request);
      return Response.success(1, "导入成功");
    } catch (ApplicationException e) {
      return Response.error(2000, e.getMessage());
    }
  }

  @Override
  public Response<Page<InventoryVo>> getInventoryList(InventoryRequest request) {
    return Response.success(productionAndMarketingService.getInventoryList(request));
  }

  @Override
  public Response<List<SkuSpecificationVo>> getInventorySkuSpecification(String endDate, String month, String organizationId,String person) {
    return Response.success(productionAndMarketingService.getInventorySkuSpecification(endDate,month,organizationId,person));
  }


  @Override
  public Response<InventoryGroupbyVo> getInventoryscreening() {
    return Response.success(productionAndMarketingService.getInventoryScreening());
  }

  @Override
  public Response<Page<InventoryDetailsVo>> WarehouseStorage(WarehouseStorageRequest request) {
    return Response.success(productionAndMarketingService.WarehouseStorageData(request));
  }

  @Override
  public Response<IPage<RealTimeInventoryVo>> queryRealTimeInventoryList(RealTimeInventoryRequest request) {
    return Response.success(productionAndMarketingService.queryRealTimeInventoryList(request));
  }

  @Override
  public void downRealTimeInventoryList(RealTimeInventoryRequest request, HttpServletResponse res, HttpServletRequest req) {
    productionAndMarketingService.downRealTimeInventoryList(request,res,req);
  }

  @Override
  public Response<RealTimeInventoryEnums> queryRealTimeInventoryEnums(RealTimeInventoryRequest request) {
    return Response.success(productionAndMarketingService.queryRealTimeInventoryEnums(request));
  }

  @Override
  public Response<List<RealTimeInventoryExceptionLockLibraryVo>> queryRealTimeInventoryExceptionLockLibraryList(RealTimeInventoryExceptionLockLibraryRequest request) {
    return Response.success(productionAndMarketingService.queryRealTimeInventoryExceptionLockLibraryList(request));
  }

  @Override
  public Response<InventoryDetailGroupEnumsVo> queryInventoryDetailEnums(InventoryDetailsQueryRequest request) {
    return Response.success(productionAndMarketingService.queryInventoryDetailEnums(request));
  }

  @Override
  public Response<IPage<NormalInventoryDetailVo>> queryNormalInventoryDetails(InventoryDetailsQueryRequest request) {
    return Response.success(productionAndMarketingService.queryNormalInventoryDetails(request));
  }

  @Override
  public Response<IPage<AbnormalInventoryDetailVo>> queryAbnormalInventoryDetails(InventoryDetailsQueryRequest request) {
    return Response.success(productionAndMarketingService.queryAbnormalInventoryDetails(request));
  }

  @Override
  public Response<List<RealTimeInventorySkuInfoEnumsVo>> queryInventoryQuickLookEnumsList(InventoryQuickLookRequest request) {
    return Response.success(productionAndMarketingService.queryInventoryQuickLookEnumsList(request));
  }

  @Override
  public Response<IPage<InventoryQuickLookVo>> queryInventoryQuickLookList(InventoryQuickLookRequest request) {
    return Response.success(productionAndMarketingService.queryInventoryQuickLookList(request));
  }

  @Override
  public void downInventoryQuickLookList(InventoryQuickLookRequest request, HttpServletRequest req, HttpServletResponse res) {
      productionAndMarketingService.downInventoryQuickLookList(request,req,res);
  }

  @Override
  public Response<IPage<InventoryQuickLookDetailsVo>> inventoryQuickLookDetailsQuery(InventoryQuickLookDetailsRequest request) {
    return Response.success(productionAndMarketingService.inventoryQuickLookDetailsQuery(request));
  }

  @Override
  public Response<List<String>> queryProductLineName() {
    return Response.success(productionAndMarketingService.queryProductLineName());
  }

  @Override
  public void exportTransportMonitoring(WarehouseStorageRequest request) {
    productionAndMarketingService.WarehouseStorageDataExport(request);
  }

  @Override
  public Response<Page<ShelvesRateDetailVo>> getShelvesRateDetail(ShelvesRateRequest request) {
    return Response.success(productionAndMarketingService.getShelvesRateDetail(request));
  }

  @Override
  public Response<Page<ShelvesRateMonitoringVo>> getShelvesRateMonitoring(ShelvesMonitoringRequest request) {
    return Response.success(productionAndMarketingService.getShelvesRateMonitoring(request));
  }

  @Override
  public Response<List<String>> selectNationLabel(String month) {
    return Response.success(productionAndMarketingService.selectNationLabel(month));
  }

  @Override
  public Response<IPage<StorageServiceAchievementVo>> getStorageServiceAchievement(StorageServiceAchievementRequest request) {
    return Response.success(productionAndMarketingService.getStorageServiceAchievement(request));
  }

  @Override
  public Response<List<String>> selectDeliveryCompanyName() {
    return Response.success(productionAndMarketingService.selectDeliveryCompanyName());
  }

  @Override
  public Response<Page<CompanyRecruitmentVo>> getCompanyRecruitmentList(CompanyRecruitmentRequest request) {
    return Response.success(productionAndMarketingService.getCompanyRecruitmentList(request));
  }

  @Override
  public void getCompanyRecruitmentExport(@Valid CompanyRecruitmentRequest request) {

    ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    Assert.notNull(servletRequestAttributes,"系统错误！！！！");
    HttpServletResponse response = servletRequestAttributes.getResponse();
    request.setIsPage(0);
    List<CompanyRecruitmentVo> list = productionAndMarketingService.getCompanyRecruitmentExportList(request);
    String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
    Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CompanyRecruitmentVo.class, list);
    String fileName = "导出-固定成本列表";
    try {
      if(wb instanceof HSSFWorkbook){
        fileName = fileName+".xls";
      }else{
        fileName = fileName+".xlsx";
      }
      String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
      // 针对IE或者以IE为内核的浏览器：
      if (userAgent.contains("msie") || userAgent.contains("trident") ) {
        fileName = URLEncoder.encode(fileName, "UTF-8");
      } else {
        // 非IE浏览器的处理：
        fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
      }
      response.setContentType("application/octet-stream");
      response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
      OutputStream outputStream = response.getOutputStream();
      (wb).write(outputStream);
      outputStream.flush();
      outputStream.close();
    } catch (IOException e) {
      response.setStatus(500);
    }
  }

  @Override
  public Response<List<String>> getCompanyRecruitmentSource() {
    return Response.success(productionAndMarketingService.getCompanyRecruitmentSource());
  }

  @Override
  public Response WarehouseReminder(WarehouseReminderRequest request) {
    productionAndMarketingService.WarehouseReminder(request);
    return Response.success();
  }

  @Override
  public Response NoticeGoods(List<NoticeGoodsRequest> request) {
    productionAndMarketingService.NoticeGoods(request);
    return Response.success();
  }



  @Override
  public Response<IPage<PublicityVo>> queryPublicity(PublicityRequest request) {
    return Response.success(productionAndMarketingService.queryPublicity(request));
  }
  @Override
  public Response<PublicityEnumsDto> queryPublicityEnums() {
    return Response.success(productionAndMarketingService.queryPublicityEnums());
  }
  @Override
  public void queryPublicityExport(PublicityRequest request,HttpServletRequest req,HttpServletResponse res) {
    productionAndMarketingService.queryPublicityExport(request,req,res);
  }

  @Override
  public Response<List<Map<String, Object>>> queryWarehouse(String employeeId) {
    return Response.success(productionAndMarketingService.queryWarehouse(employeeId));
  }

  @Override
  public Response<Page<SafetyStockVo>> querySafetyStock(SafetyStockRequest request) {
    return Response.success(productionAndMarketingService.querySafetyStock(request));
  }

  @Override
  public Response safetyStockInert(SafetyStockInsertRequest request) {
    productionAndMarketingService.safetyStockInert(request);
    return Response.success();
  }

  @Override
  public Response<SafetyStockLogVo> safetyStockLog(safetyStockLogRequest request) {
    return Response.success(productionAndMarketingService.safetyStockLog(request));
  }

  @Override
  public Response<SafetyStockConditionVo> safetyStockCondition() {
    return Response.success(productionAndMarketingService.safetyStockCondition());
  }

  @Override
  public void safetyStockExport(SafetyStockRequest request) {
    productionAndMarketingService.safetyStockExport(request);
  }
}
