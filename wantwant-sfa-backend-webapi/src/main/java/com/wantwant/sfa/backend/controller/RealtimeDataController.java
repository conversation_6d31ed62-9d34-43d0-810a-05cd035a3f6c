package com.wantwant.sfa.backend.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.info.vo.SkuSpecificationNewVo;
import com.wantwant.sfa.backend.mainProduct.request.CurrentTargetDataRes;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.RealtimeMapper;
import com.wantwant.sfa.backend.organizationGoal.request.EmployeeGoalQueryRequest;
import com.wantwant.sfa.backend.organizationGoal.vo.EmpGoalVO;
import com.wantwant.sfa.backend.organizationGoal.vo.EmployeeGoalLogVO;
import com.wantwant.sfa.backend.realData.request.*;
import com.wantwant.sfa.backend.realData.vo.*;
import com.wantwant.sfa.backend.service.IRealtimeDataService;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import com.wantwant.sfa.backend.util.RealTimeUtils;
import com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO;
import com.wantwant.sfa.common.base.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.beans.IntrospectionException;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 总部、总督导、区域总监实时数据
 *
 * @date 2022-02-16 16:56
 * @version 1.0
 */
@Slf4j
@Api(tags = "实时数据相关接口")
@RestController
@RequestMapping("/realtimeData")
public class RealtimeDataController {

    @Autowired
    private IRealtimeDataService service;

    @Autowired private OrganizationMapper organizationMapper;

    @Autowired
    private SettingServiceImpl settingService;

    @Autowired private RealtimeMapper realtimeMapper;

    @Resource
    private RealTimeUtils realTimeUtils;

    /**
     * 根据组织ID获取数据
     *
     * @param organizationId 组织id
     * @param yearMonth      年月
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.realData.vo.RealtimeDataVO>
     * @date: 2022-02-18 19:40
     */
    @ApiOperation(value = "根据组织ID获取数据", notes = "根据组织ID获取数据")
    @GetMapping()
    public Response<RealtimeDataVO> queryRealtimeData(@ApiParam(name = "organizationId", value = "组织ID", required = true) @RequestParam(value = "organizationId") String organizationId,
                                                      @ApiParam(name = "yearMonth", value = "年月(2022-02)", required = true) @RequestParam(value = "yearMonth") String yearMonth,
                                                      @ApiParam(name = "employeeId", value = "工号") @RequestParam(value = "employeeId") String employeeId) {
        return Response.success(service.queryRealtimeData(organizationId, yearMonth, employeeId));
    }

    /**
     * 根据组织ID获取下级数据
     *
     * @param organizationId   组织id
     * @param yearMonth        年月
     * @param organizationName 组织名称
     * @return: com.wantwant.commons.web.response.Response<java.util.List < com.wantwant.sfa.backend.realData.vo.RealtimeDataVO>>
     * @date: 2022-02-18 19:40
     */
    @ApiOperation(value = "根据组织ID获取下级数据", notes = "根据组织ID获取下级数据")
    @GetMapping(value = "/queryChild")
    public Response<List<RealtimeDataVO>> queryChildRealtimeData(@ApiParam(name = "organizationId", value = "组织ID", required = true) @RequestParam(value = "organizationId") String organizationId,
                                                                 @ApiParam(name = "yearMonth", value = "年月(2022-02)", required = true) @RequestParam(value = "yearMonth") String yearMonth,
                                                                 @ApiParam(name = "organizationName", value = "组织名称") @RequestParam(value = "organizationName", required = false) String organizationName) {
        return Response.success(service.queryChildRealtimeData(organizationId, yearMonth, organizationName));
    }

    /**
     * 合伙人目标列表(弃用)
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage < com.wantwant.sfa.backend.realData.vo.EmployeeGoalVO>>
     * @author: zhouxiaowen
     * @date: 2022-02-18 19:54
     */
    @Deprecated
    @ApiOperation(value = "合伙人目标列表(弃用)", notes = "合伙人目标列表(弃用)")
    @GetMapping(value = "/queryEmpGoalByPage")
    public Response<IPage<EmployeeGoalVO>> queryEmpGoalByPage(EmployeeGoalRequest query) {
        return Response.success(service.queryEmpGoalByPage(query));
    }

    /**
     * 新合伙人目标列表
     *
     * @param query
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.organizationGoal.vo.EmployeeGoalInfo>
     * @date: 8/24/22 7:19 PM
     */
    @ApiOperation(value = "新合伙人目标列表", notes = "新合伙人目标列表")
    @GetMapping(value = "/pageEmpGoal")
    public Response<IPage<EmpGoalVO>> pageEmpGoalNew(EmployeeGoalQueryRequest query) {
        return Response.success(service.pageEmpGoalNew(query));
    }

    /**
     * 设置合伙人目标
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @author: zhouxiaowen
     * @date: 2022-02-18 19:54
     */
    @ApiOperation(notes = "设置合伙人目标", value = "设置合伙人目标")
    @PutMapping("/setGoal")
    public Response setGoal(@Valid @RequestBody EmployeeSetGoalRequest request) {
        service.setGoal(request);
        return Response.success();
    }

    /**
     * 审核合伙人目标
     *
     */
    @ApiOperation(notes = "审核合伙人目标", value = "审核合伙人目标")
    @PutMapping("/auditGoal")
    public Response auditGoal(@Valid @RequestBody EmployeeAuditGoalRequest request) {
        service.auditGoal(request);
        return Response.success();
    }

    /**
     * 合伙人目标分配历史
     *
     * @param id
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.organizationGoal.vo.EmployeeGoalLogVO>>
     * @date: 8/26/22 6:39 PM
     */
    @ApiOperation(notes = "合伙人目标分配历史", value = "合伙人目标分配历史")
    @GetMapping("/listEmpGoalLog/{id}")
    public Response<List<EmployeeGoalLogVO>> listEmpGoalLog(@PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
        return Response.success(service.listEmpGoalLog(id));
    }

    /**
     * 合伙人目标导入模板
     *
     * @param organizationId 登录人id
     * @param response
     * @return: void
     * @date: 8/24/22 9:24 AM
     */
    @ApiOperation(value = "合伙人目标导入模板")
    @GetMapping("/employeeGoalTemplate")
    public void template(@RequestParam("organizationId") String organizationId, HttpServletResponse response) {
        service.template(organizationId, response);
    }

    /**
     * 导入合伙人目标
     *
     * @param file
     * @param effectiveDate
     * @param updatedBy
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
     * @date: 8/25/22 10:44 AM
     */
    @ApiOperation(value = "导入合伙人目标")
    @PostMapping("/importEmployeeGoal")
    public Response<ImportMsgVO> importEmployeeGoal(@RequestParam(value = "file") MultipartFile file,
                                                    @ApiParam(value = "生效日期yyyy-MM", required = true) @RequestParam(value = "effectiveDate") String effectiveDate,
                                                    @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "updatedBy") String updatedBy) {
        LocalDate parse;
        try {
            parse = LocalDate.parse(effectiveDate+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            throw new ApplicationException("请传入yyyy-MM格式日期！");
        }
        return service.importEmployeeGoal(file, parse,updatedBy);
    }

    /**
     * 合伙人目标列表导出
     *
     * @param request
     * @param response
     * @return: void
     * @date: 8/26/22 7:31 PM
     */
    @ApiOperation(value = "合伙人目标列表导出")
    @GetMapping("/exportEmployeeGoal")
    public void exportEmployeeGoal(EmployeeGoalQueryRequest request, HttpServletResponse response) {
        service.exportEmployeeGoal(request,response);
    }

    @ApiOperation(value = "根据组织ID获取招聘与管理与商品线占比分析", notes = "根据组织ID获取招聘与管理与商品线占比分析")
    @GetMapping("/queryRrecruitmentManagementData")
    public Response<RrecruitmentManagementDataVo> queryRrecruitmentManagementData(
            TradeGoodReq request) {
        return Response.success(service.queryRrecruitmentManagementData(request));
    }

    @ApiOperation(value = "根据组织ID获取招聘数据", notes = "根据组织ID获取招聘数据")
    @GetMapping("/queryRrecruitmentData")
    public Response<Page<RecruitmentDate>> queryRrecruitmentData(
            TradeGoodReq request) {
        return Response.success(service.queryRrecruitmentData(request));
    }


    @ApiOperation(value = "获取趋势分析数据", notes = "获取趋势分析数据")
    @GetMapping("/queryTrendAnalysisData")
    public Response<List<TrendAnalysisDataVo>> queryTrendAnalysisData(TradeGoodReq request) {
        return Response.success(service.queryTrendAnalysisData(request));
    }

    @ApiOperation(value = "新排行榜-首页", notes = "新排行榜-首页")
    @PostMapping("/queryRankingList")
    public Response<RealTimeRankingListVo> queryRankingList(
            @RequestBody @Validated RealTimeRankingListReq request) {
        return Response.success(
                service.queryRankingList(request));
    }
    @ApiOperation(value = "新排行榜-详情页列表", notes = "新排行榜-详情页列表")
    @PostMapping("/queryRankingListDetail")
    public Response<IPage<RealTimeRankingListDetailVo>> queryRankingListDetail(
            @RequestBody @Validated RealTimeRankingListDetailReq request) {
        return Response.success(
                service.queryRankingListDetail(request));
    }

    @ApiOperation(value = "根据上级获取下级数据", notes = "根据上级获取下级数据")
    @GetMapping("/getNextRealTimeData")
    public Response<Page<NextRealTimeDataVo>> queryNextRealTimeData(TradeGoodReq request) {
        return Response.success(service.queryNextRealTimeData(request));
    }


    @ApiOperation(value = "获取趋势分析new", notes = "获取趋势分析new")
    @PutMapping("/getPerformanceTrends")
    public Response<List<PerformanceTrendsAllVo>> getPerformanceTrends(
            @RequestBody PerformanceTrendsRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.getPerformanceTrends(request));
    }

    @ApiOperation(value = "获取业绩趋势对比-全部-上月", notes = "获取趋势分析new-全部-上月")
    @PutMapping("/getPerformanceTrends/contrast")
    public Response<List<PerformanceTrendsAllVo>> getPerformanceTrendsContrast(
            @RequestBody PerformanceTrendsRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.getPerformanceTrendsContrast(request));
    }

    @ApiOperation(value = "获取线别名称", notes = "获取线别名称")
    @PutMapping("/getLineName")
    public Response<List<String>> getLineName(
            @Valid @RequestBody LineRequest request) {
        return Response.success(service.getLineName(request));
    }


    @ApiOperation(value = "报表中心/业绩影响因素趋势分析", notes = "报表中心/业绩影响因素趋势分析")
    @GetMapping(value = "/performanceInfluenceFactors")
    public Response<PerformanceInfluenceRes> performanceInfluenceFactors(@ApiParam(name = "organizationId", value = "组织ID") @RequestParam(value = "organizationId",required = false) String organizationId,
                                                                         @ApiParam(name = "areaOrganizationId", value = "大区组织ID") @RequestParam(value = "areaOrganizationId",required = false) String areaOrganizationId,
                                                                         @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam(value = "partnerType",required = false) Integer partnerType,
                                                                         @ApiParam(name = "person", value = "工号") @RequestParam(value = "person",required = false) String person) {
        if (org.apache.commons.lang3.StringUtils.isBlank(organizationId)) {
            List<String> employeeOrganizationId =
                    organizationMapper.getEmployeeOrganizationId(person, RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                organizationId = employeeOrganizationId.get(0);
            }
        }
        PerformanceInfluenceRes res = new PerformanceInfluenceRes();
        List<PerformanceInfluenceVO> performanceInfluenceVOS = service.performanceInfluenceFactors(organizationId, areaOrganizationId,partnerType,person);
        performanceInfluenceVOS.forEach(p -> BeanUtils.defaultValue(p));
        res.setData(performanceInfluenceVOS);
        List<PerformanceInfluenceVO> avgList = service.performanceAvg();
        if (!organizationId.contains("ZB")) {
            List<PerformanceInfluenceVO> avgMonthList = avgList.stream().filter(e->e.getTimeTypeId() == 0).collect(Collectors.toList());
            //业绩目标达成
            res.setSaleGoal(avgMonthList.stream().map(a -> a.getSaleGoalAchievementRate()).collect(Collectors.toList()));
            res.setSaleGoalGrowthRate(avgMonthList.stream().map(a -> a.getSaleGoalAchievementGrowthRate()).collect(Collectors.toList()));
            //人均业绩
            res.setAvgGmv(avgMonthList.stream().map(a -> a.getAvgGmv()).collect(Collectors.toList()));
            res.setAvgGmvGrowthRate(avgMonthList.stream().map(a -> a.getAvgGmvGrowthRate()).collect(Collectors.toList()));
            //在岗目标达成率 （在岗率）
            res.setOnJobNumsSaleGoal(avgMonthList.stream().map(a -> a.getOnJobComplementNumsAchievementRate()).collect(Collectors.toList()));
            res.setOnJobNumsSaleGoalGrowthRate(avgMonthList.stream().map(a -> a.getOnJobComplementNumsAchievementGrowthRate()).collect(Collectors.toList()));
            //用人费用率
            res.setEmploymentExpensesRate(avgMonthList.stream().map(a -> a.getEmploymentExpensesRate()).collect(Collectors.toList()));
            res.setEmploymentSaleGoalGrowthRate(avgMonthList.stream().map(a -> a.getEmplYearOnYearAchievementGrowthRate()).collect(Collectors.toList()));
            //离职率
            res.setDepartureNumsRate(avgMonthList.stream().map(a -> a.getDepartureNumsRate()).collect(Collectors.toList()));
            res.setDepartureSaleGoalGrowthRate(avgMonthList.stream().map(a -> a.getDepartureYearOnYearAchievementGrowthRate()).collect(Collectors.toList()));

            //季度
            List<PerformanceInfluenceVO> avgSeasonList = avgList.stream().filter(e->e.getTimeTypeId() == 1).collect(Collectors.toList());
            //业绩目标达成
            res.setSeasonSaleGoal(avgSeasonList.stream().map(a -> a.getSaleGoalAchievementRate()).collect(Collectors.toList()));
            res.setSeasonSaleGoalGrowthRate(avgSeasonList.stream().map(a -> a.getSaleGoalAchievementGrowthRate()).collect(Collectors.toList()));
            //人均业绩
            res.setSeasonAvgGmv(avgSeasonList.stream().map(a -> a.getAvgGmv()).collect(Collectors.toList()));
            res.setSeasonAvgGmvGrowthRate(avgSeasonList.stream().map(a -> a.getAvgGmvGrowthRate()).collect(Collectors.toList()));
            //在岗目标达成率
            res.setSeasonOnJobNumsSaleGoal(avgSeasonList.stream().map(a -> a.getOnJobComplementNumsAchievementRate()).collect(Collectors.toList()));
            res.setSeasonOnJobNumsSaleGoalGrowthRate(avgSeasonList.stream().map(a -> a.getOnJobComplementNumsAchievementGrowthRate()).collect(Collectors.toList()));
            //用人费用率
            res.setSeasonEmploymentExpensesRate(avgSeasonList.stream().map(a -> a.getEmploymentExpensesRate()).collect(Collectors.toList()));
            res.setSeasonEmploymentSaleGoalGrowthRate(avgSeasonList.stream().map(a -> a.getEmplYearOnYearAchievementGrowthRate()).collect(Collectors.toList()));
            //离职率
            res.setSeasonDepartureNumsRate(avgSeasonList.stream().map(a -> a.getDepartureNumsRate()).collect(Collectors.toList()));
            res.setSeasonDepartureSaleGoalGrowthRate(avgSeasonList.stream().map(a -> a.getDepartureYearOnYearAchievementGrowthRate()).collect(Collectors.toList()));
        }
        return Response.success(res);
    }


    @ApiOperation(value = "报表中心/业绩影响因素趋势分析", notes = "报表中心/业绩影响因素趋势分析")
    @GetMapping(value = "/performanceInfluenceFactors/monthlyReport")
    public Response<PerformanceInfluenceMonthlyReportRes> performanceInfluenceFactorsMonthlyReport(@ApiParam(name = "organizationId", value = "组织ID") @RequestParam(value = "organizationId",required = false) String organizationId,
                                                                         @ApiParam(name = "areaOrganizationId", value = "大区组织ID") @RequestParam(value = "areaOrganizationId",required = false) String areaOrganizationId,
                                                                         @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam(value = "partnerType",required = false) Integer partnerType,
                                                                                                   @ApiParam(name = "yearMonth",value="查询日期") @RequestParam(value="yearMonth",required = false) String yearMonth,
                                                                                                   @ApiParam(name = "person",value="工号") @RequestParam(value="person",required = false) String person) {
        if (StringUtils.isBlank(organizationId)) {
            List<String> employeeOrganizationId =
                    organizationMapper.getEmployeeOrganizationId(person, RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                organizationId = employeeOrganizationId.get(0);
            }
        }
        PerformanceInfluenceMonthlyReportRes res = new PerformanceInfluenceMonthlyReportRes();
        //最后赋值list
        ArrayList<PerformanceInfluenceMonthlyReportVo> performanceInfluenceMonthlylist = new ArrayList<>();
        //过滤了月度数据list
        ArrayList<PerformanceInfluenceMonthlyReportVo> list = new ArrayList<>();
        //调用老的接口 业绩影响因素趋势分析 需要过滤出 月度数据
        List<PerformanceInfluenceVO> performanceInfluence = service.performanceInfluenceFactors(organizationId, areaOrganizationId,partnerType,person);
        List<PerformanceInfluenceVO> performanceInfluenceYear = service.performanceInfluenceFactorsYear(organizationId, areaOrganizationId,partnerType,person);

        if(StringUtils.isNotBlank(yearMonth)){
            LocalDate lastMonth = LocalDate.parse(yearMonth + "-01");
            // 过滤下日期
            performanceInfluence = performanceInfluence.stream().filter(f -> f.getTimeTypeId() == 1 || (f.getTimeTypeId() == 0 && (LocalDate.parse(f.getTheYearMon()+"-01").isBefore(lastMonth) || LocalDate.parse(f.getTheYearMon()+"-01").isEqual(lastMonth)))).collect(Collectors.toList());
        }

        //list.forEach(p -> BeanUtils.defaultValue(p));
        if ("DNN_Z".equals(organizationId)){
            PerformanceInfluenceVO vo1 = new PerformanceInfluenceVO();
            vo1.setTheYearMon("2022-01");
            BeanUtils.defaultValue(vo1);
            PerformanceInfluenceVO vo2 = new PerformanceInfluenceVO();
            vo2.setTheYearMon("2022-02");
            BeanUtils.defaultValue(vo2);
            PerformanceInfluenceVO vo3 = new PerformanceInfluenceVO();
            vo3.setTheYearMon("2022-03");
            BeanUtils.defaultValue(vo3);
            PerformanceInfluenceVO vo4 = new PerformanceInfluenceVO();
            vo4.setTheYearMon("2022-04");
            BeanUtils.defaultValue(vo4);
            PerformanceInfluenceVO vo5 = new PerformanceInfluenceVO();
            vo5.setTheYearMon("2022-05");
            BeanUtils.defaultValue(vo5);
            PerformanceInfluenceVO vo6 = new PerformanceInfluenceVO();
            vo6.setTheYearMon("2022-06");
            BeanUtils.defaultValue(vo6);
            PerformanceInfluenceVO vo7 = new PerformanceInfluenceVO();
            vo7.setTheYearMon("2022-07");
            BeanUtils.defaultValue(vo7);
            performanceInfluence.add(0,vo1);
            performanceInfluence.add(1,vo2);
            performanceInfluence.add(2,vo3);
            performanceInfluence.add(3,vo4);
            performanceInfluence.add(4,vo5);
            performanceInfluence.add(5,vo6);
            performanceInfluence.add(6,vo7);
        }
        if ("ZN_Z".equals(organizationId)){
            PerformanceInfluenceVO vo1 = new PerformanceInfluenceVO();
            vo1.setTheYearMon("2022-01");
            BeanUtils.defaultValue(vo1);
            PerformanceInfluenceVO vo2 = new PerformanceInfluenceVO();
            vo2.setTheYearMon("2022-02");
            BeanUtils.defaultValue(vo2);
            PerformanceInfluenceVO vo3 = new PerformanceInfluenceVO();
            vo3.setTheYearMon("2022-03");
            BeanUtils.defaultValue(vo3);
            PerformanceInfluenceVO vo4 = new PerformanceInfluenceVO();
            vo4.setTheYearMon("2022-04");
            BeanUtils.defaultValue(vo4);
            PerformanceInfluenceVO vo5 = new PerformanceInfluenceVO();
            vo5.setTheYearMon("2022-05");
            BeanUtils.defaultValue(vo5);
            PerformanceInfluenceVO vo6 = new PerformanceInfluenceVO();
            vo6.setTheYearMon("2022-06");
            BeanUtils.defaultValue(vo6);
            performanceInfluence.add(0,vo1);
            performanceInfluence.add(1,vo2);
            performanceInfluence.add(2,vo3);
            performanceInfluence.add(3,vo4);
            performanceInfluence.add(4,vo5);
            performanceInfluence.add(5,vo6);
        }
        //找出月度数据
        List<PerformanceInfluenceVO> collect = performanceInfluence.stream().filter(p -> p.getTimeTypeId() == 0).collect(Collectors.toList());
        BeanUtils.copyProperties(collect,list,PerformanceInfluenceVO.class, PerformanceInfluenceMonthlyReportVo.class);
        //performanceInfluence 拿出有几个年份
        Map<String, List<PerformanceInfluenceMonthlyReportVo>> yearCollect = list.stream().filter(p -> StringUtils.isNotBlank(p.getYear())).collect(Collectors.groupingBy(PerformanceInfluenceMonthlyReportVo::getYear));
        //因为map排序，把key日期变成倒序，用下面方法变成正序使用
        LinkedHashMap<String, List<PerformanceInfluenceMonthlyReportVo>> result = new LinkedHashMap<>();
        yearCollect.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x->result.put(x.getKey(),x.getValue()));
        for(Map.Entry<String, List<PerformanceInfluenceMonthlyReportVo>> year:result.entrySet()){
            PerformanceInfluenceMonthlyReportVo performanceInfluenceMonthlyReportVo = new PerformanceInfluenceMonthlyReportVo();
            //为了放入合计数据list
            ArrayList<PerformanceInfluenceMonthlyReportVo> listTotal = new ArrayList<>();
            //根据月度数据模糊查找出包含这个年份  计算合计（盘价业绩 目标达成率)
            /*List<PerformanceInfluenceMonthlyReportVo> collect1 = list.stream().filter(c -> c.getTheYearMon().contains(year.getKey())).collect(Collectors.toList());
            BigDecimal gmv = collect1.stream().collect(CollectorUtil.summingBigDecimal(PerformanceInfluenceMonthlyReportVo::getGmv));
            BigDecimal saleGoal = collect1.stream().collect(CollectorUtil.summingBigDecimal(PerformanceInfluenceMonthlyReportVo::getSaleGoal));
            BigDecimal saleGoalAchievementRate=null;
            if(saleGoal.compareTo(BigDecimal.ZERO)==0){
                saleGoalAchievementRate=BigDecimal.ZERO;
            }else{
                //业绩/目标 = 目标达成率
                saleGoalAchievementRate=gmv.divide(saleGoal,2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            }*/
            performanceInfluenceMonthlyReportVo.setTheYearMon(year.getKey()+"年合计");
            List<PerformanceInfluenceVO> collectYear = performanceInfluenceYear.stream().filter(p -> p.getYear().equals(year.getKey())).collect(Collectors.toList());
            if(CommonUtil.ListUtils.isNotEmpty(collectYear)){
                PerformanceInfluenceVO performanceInfluenceVO = collectYear.get(0);

                performanceInfluenceMonthlyReportVo.setGmv(performanceInfluenceVO.getGmv());
                performanceInfluenceMonthlyReportVo.setSaleGoalAchievementRate(performanceInfluenceVO.getSaleGoalAchievementRate());
                performanceInfluenceMonthlyReportVo.setSaleGoalAchievementGrowthRate(performanceInfluenceVO.getSaleGoalAchievementGrowthRate());
                performanceInfluenceMonthlyReportVo.setYearOnYearAchievementGrowthRate(performanceInfluenceVO.getYearOnYearAchievementGrowthRate());
            }
            //listTotal.add(performanceInfluenceMonthlyReportVo);
            listTotal.addAll(year.getValue());
            performanceInfluenceMonthlyReportVo.setList(listTotal);
            performanceInfluenceMonthlylist.add(performanceInfluenceMonthlyReportVo);
        }
        res.setData(performanceInfluenceMonthlylist);
        List<PerformanceInfluenceVO> avgList = service.performanceAvg();
        if (!"ZB_Z".equals(organizationId)) {
            List<PerformanceInfluenceVO> avgMonthList = avgList.stream().filter(e->e.getTimeTypeId() == 0).collect(Collectors.toList());
            //业绩目标达成
            res.setSaleGoal(avgMonthList.stream().map(a -> a.getSaleGoalAchievementRate()).collect(Collectors.toList()));
            res.setSaleGoalGrowthRate(avgMonthList.stream().map(a -> a.getSaleGoalAchievementGrowthRate()).collect(Collectors.toList()));
        }
        return Response.success(res);
    }

    @ApiOperation(value = "报表中心/导出业绩影响因素趋势分析", notes = "报表中心/导出业绩影响因素趋势分析")
    @GetMapping(value = "/export/performanceInfluenceFactors")
    public void exportPerformanceInfluenceFactors(@ApiParam(name = "organizationId", value = "组织ID") @RequestParam(value = "organizationId",required = false) String organizationId,
                                                  @ApiParam(name = "areaOrganizationId", value = "大区组织ID") @RequestParam(value = "areaOrganizationId",required = false) String areaOrganizationId,
                                                  @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam(value = "partnerType",required = false) Integer partnerType,
                                                  @ApiParam(name = "person",value="工号") @RequestParam(value="person",required = false) String person,
                                                  HttpServletResponse response) {
        if (StringUtils.isBlank(organizationId)) {
            List<String> employeeOrganizationId =
                    organizationMapper.getEmployeeOrganizationId(person, RequestUtils.getLoginInfo());
            if (CommonUtil.ListUtils.isNotEmpty(employeeOrganizationId)) {
                organizationId = employeeOrganizationId.get(0);
            }
        }
        List<PerformanceInfluenceVO> list = service.performanceInfluenceFactors(organizationId,areaOrganizationId, partnerType,"");
        EasyPoiUtil.exportExcel(list,null,"sheet1",PerformanceInfluenceVO.class,"业绩影响因素趋势分析.xls",response);
    }

    @ApiOperation(value = "报表中心/合伙人招聘复盘", notes = "报表中心/合伙人招聘复盘")
    @GetMapping(value = "/recruitmentProcess")
    public Response<IPage<RecruitmentProcessVO>> recruitmentProcess(RecruitmentProcessRequest request) {
        return Response.success(service.recruitmentProcess(request));
    }

    @ApiOperation(value = "报表中心/导出合伙人招聘复盘", notes = "报表中心/导出合伙人招聘复盘")
    @GetMapping(value = "/export/recruitmentProcess")
    public void exportRecruitmentProcess(RecruitmentProcessRequest request,HttpServletResponse response) {
        List<RecruitmentProcessVO> list = service.recruitmentProcessList(request);
        EasyPoiUtil.exportExcel(list,null,"sheet1",RecruitmentProcessVO.class,"合伙人招聘复盘.xls",response);
    }

    @ApiOperation(value = "报表中心/业务核心指标报表", notes = "报表中心/业务核心指标报表")
    @GetMapping(value = "/indicators")
    public Response<IPage<IndicatorsVO>> indicators(RecruitmentProcessRequest request) {
        return Response.success(service.indicators(request));
    }

    @ApiOperation(value = "报表中心/业务核心指标报表查询下级", notes = "报表中心/业务核心指标报表")
    @GetMapping(value = "/indicatorsChild")
    public Response<List<IndicatorsVO>> indicatorsChild(RecruitmentProcessRequest request) {
        return Response.success(service.indicatorsChild(request));
    }

    @ApiOperation(value = "报表中心/导出业务核心指标报表", notes = "报表中心/导出业务核心指标报表")
    @GetMapping(value = "/export/indicators")
    public void exportIndicators(RecruitmentProcessRequest request,HttpServletResponse response) {
        service.exportIndicators(request,response);
    }


    @ApiOperation(value = "实时数据营业所图", notes = "实时数据营业所图")
    @PostMapping("/getBranch/figure")
    public Response<CustomerPortraitVo> getBranchFigure(@RequestBody @Validated BranchTradeRequest request) throws IllegalAccessException, IntrospectionException, InvocationTargetException {
        log.info("start RealtimeDataController getBranchFigure request:{}",request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.getBranchFigure(request));
    }

    @ApiOperation(value = "合伙人小标地", notes = "合伙人小标地")
    @PostMapping("/getBranch/queryMarket")
    public Response<List<CustomerMarketVo>> queryMarket(@RequestBody @Validated BranchTradeRequest request)  {
        log.info("start RealtimeDataController queryMarket request:{}",request);
        return Response.success(service.queryCustomerRanking(request));
    }


    @ApiOperation(value = "报表中心-销售报表-销售报表-产品线趋势分析", notes = "报表中心-销售报表-销售报表-产品线趋势分析")
    @PostMapping("/reportForms/productLineAnalysis/query")
    public Response<List<ProductLineAnalysisQueryVo>> queryProductLineAnalysis(@RequestBody @Validated ProductLineAnalysisQueryRequest request)  {
        log.info("start RealtimeDataController pageQueryProductLineAnalysis request:{}",request);
        return Response.success(service.queryProductLineAnalysis(request));
    }
    @ApiOperation(value = "报表中心-销售报表-销售报表-产品线趋势分析下载", notes = "报表中心-销售报表-销售报表-产品线趋势分析下载")
    @PostMapping("/reportForms/productLineAnalysis/export")
    public void exportProductLineAnalysis(@RequestBody @Validated ProductLineAnalysisQueryRequest request,HttpServletRequest req,HttpServletResponse res)  {
        log.info("start RealtimeDataController exportProductLineAnalysis request:{}",request);
        service.exportProductLineAnalysis(request,req,res);
    }
    @ApiOperation(value = "报表中心-销售报表-销售报表-产品线趋势分析下拉", notes = "报表中心-销售报表-销售报表-产品线趋势分析下拉")
    @PostMapping("/reportForms/productLineAnalysis/selectList")
    public Response<List<ProductLineAnalysisSelectVo>> productLineAnalysisSelectList(@RequestBody @Validated ProductLineAnalysisSelectRequest request){
        log.info("start RealtimeDataController productLineAnalysisSelectList request{}",request);
        return Response.success(service.productLineAnalysisSelectList(request));
    }

    @ApiOperation(value = "根据组织和时间查询sku三级联动", notes = "根据组织和时间查询sku三级联动")
    @PostMapping("/queryProductInfoLinkedList")
    public Response<List<ProductInfoLinkedVo>> queryProductInfoLinkedList(@RequestBody @Validated ProductInfoLinkedRequest request){
        log.info("start RealtimeDataController queryProductInfoLinkedList request{}",request);
        return Response.success(service.queryProductInfoLinkedList(request));
    }

    @ApiOperation(value = "报表中心-销售报表-销售报表-产品线趋势对比分析", notes = "报表中心-销售报表-销售报表-产品线趋势对比分析")
    @PostMapping("/reportForms/productLineContrastAnalysis/query")
    public Response<IPage<ProductLineContrastAnalysisQueryVo>> queryProductLineContrastAnalysis(@RequestBody @Validated ProductLineContrastAnalysisQueryRequest request){
        log.info("start RealtimeDataController productLineContrastAnalysisSelectList request{}",request);
        return Response.success(service.queryProductLineContrastAnalysis(request));
    }

    @ApiOperation(value = "报表中心-销售报表-销售报表-产品线趋势对比分析导出", notes = "报表中心-销售报表-销售报表-产品线趋势对比分析导出")
    @PostMapping("/reportForms/productLineContrastAnalysis/export")
    public void exportProductLineContrastAnalysis(@RequestBody @Validated ProductLineContrastAnalysisQueryRequest request,HttpServletRequest req,HttpServletResponse res){
        log.info("start RealtimeDataController exportProductLineContrastAnalysis request{}",request);
        service.exportProductLineContrastAnalysis(request,req,res);
    }

    @ApiOperation(value = "报表中心-销售报表-销售报表-产品线趋势对比分析数据", notes = "报表中心-销售报表-销售报表-产品线趋势对比分析数据")
    @PostMapping("/reportForms/productLineContrastAnalysis/Info")
    public Response<List<ProductLineContrastAnalysisInfoVo>> queryProductLineContrastAnalysisInfo(@RequestBody @Validated ProductLineContrastAnalysisInfoRequest request){
        log.info("start RealtimeDataController queryProductLineContrastAnalysisInfo request{}",request);
        return Response.success(service.queryProductLineContrastAnalysisInfo(request));
    }

    @ApiOperation(value = "人员达成趋势分析-合伙人达成分析",notes = "人员达成趋势分析-合伙人达成分析")
    @GetMapping(value = "/personnelAchievement")
    public Response<List<PersonnelVO>> personnelAchievement(@ApiParam(name = "yearMonth", value = "年月") @RequestParam("yearMonth") String yearMonth,
                                                  @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId,
                                                            @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam("partnerType") String partnerType){
        return Response.success(service.personnelAchievement(yearMonth,organizationId, partnerType));
    }

    @ApiOperation(value = "人员达成趋势分析-合伙人达成每月对比",notes = "人员达成趋势分析-合伙人达成每月对比")
    @GetMapping(value = "/personnelAchievementMonth")
    public Response<List<PersonnelMonthVO>> personnelAchievementMonth(@ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId,
                                                            @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam("partnerType") String partnerType){
        return Response.success(service.personnelAchievementMonth(organizationId, partnerType));
    }

    @ApiOperation(value = "人员达成趋势分析-组织对比的趋势表",notes = "人员达成趋势分析-组织对比的趋势表")
    @GetMapping(value = "/organizationAchievementMonth")
    public Response<List<OrganizationMonthVO>> organizationAchievementMonth(@ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId,
                                                                      @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam("partnerType") String partnerType){
        return Response.success(service.organizationAchievementMonth(organizationId, partnerType));
    }

    @ApiOperation(value = "人员达成趋势分析-合伙人达成分析导出",notes = "人员达成趋势分析-合伙人达成分析导出")
    @GetMapping(value = "/export/personnelAchievement")
    public void exportPersonnelAchievement(@ApiParam(name = "yearMonth", value = "年月") @RequestParam("yearMonth") String yearMonth,
                                           @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId,
                                           @ApiParam(name = "partnerType", value = "合伙人类型") @RequestParam("partnerType") String partnerType){
        service.exportPersonnelAchievement(yearMonth,organizationId, partnerType);
    }

    @ApiOperation(value = "人员达成趋势分析-组织人员达成分析",notes = "人员达成趋势分析-组织人员达成分析")
    @GetMapping(value = "/organizationAchievement")
    public Response<List<OrganizationAchVO>> organizationAchievement(@ApiParam(name = "yearMonth", value = "年月") @RequestParam("yearMonth") String yearMonth,
                                                                     @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId){
        return Response.success(service.organizationAchievement(yearMonth,organizationId));
    }

    @ApiOperation(value = "人员达成趋势分析-组织人员达成分析导出",notes = "人员达成趋势分析-组织人员达成分析导出")
    @GetMapping(value = "/export/organizationAchievement")
    public void exportOrganizationAchievement(@ApiParam(name = "yearMonth", value = "年月") @RequestParam("yearMonth") String yearMonth,
                                           @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId){
        service.exportOrganizationAchievement(yearMonth,organizationId);
    }

    @ApiOperation(value = "核心指标预警产品信息",notes = "核心指标预警产品信息")
    @PostMapping("/getWeeklyProduct")
    public Response<WeeklyProductVO> getWeeklyProduct(@Valid @RequestBody WeeklyRequest request) {
        return Response.success(service.getWeeklyProduct(request));
    }

    /**
     * 核心指标1～4
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.realData.vo.WeeklyProductVO>
     * @date: 4/23/23 2:17 PM
     */
    @ApiOperation(value = "核心指标1～4",notes = "核心指标1～4")
    @PostMapping("/getIndicators")
    public Response<IndicatorDataVO> getIndicators(@Valid @RequestBody WeeklyRequest request) {
        return Response.success(service.getIndicators(request));
    }

    /**
     * 线别SPU模块
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.realData.vo.IndicatorDataVO>
     * @date: 7/26/23 5:03 PM
     */
    @ApiOperation(value = "线别SPU模块",notes = "线别SPU模块")
    @PostMapping("/getWeeklyLineSpu")
    public Response<WeeklyLineSpuVO> getWeeklyLineSpu(@Valid @RequestBody WeeklyRequest request) {
        return Response.success(service.getWeeklyLineSpu(request));
    }

    /**
     *  lineSpu颜色
     */
    @ApiOperation(value = "lineSpu颜色",notes = "lineSpu颜色")
    @GetMapping("/lineSpuColor")
    public Object lineSpuColor() {
        String value = settingService.getValue("line_spu_color");
        return JSONObject.parse(value);
    }


    /**
     * 合伙人情况
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.realData.vo.EmpRankingVO>
     * @date: 5/9/23 4:47 PM
     */
    @ApiOperation(value = "合伙人情况",notes = "合伙人情况")
    @PostMapping("/getEmpRanking")
    public Response<List<EmpRankingVO>> getEmpRanking(@Valid @RequestBody WeeklyRequest request){
        return Response.success(service.getEmpRanking(request));
    }

    @ApiOperation(value = "实时数据-主推品",notes = "实时数据-主推品")
    @GetMapping(value = "/mainProducts")
    public Response<MainProductsDataAllVo> getMainProductsList(
            @ApiParam(name = "dateTypeId", value = "时间类型 10:自然月,11:自然季,2:财务年") @RequestParam("dateTypeId") String dateTypeId,
            @ApiParam(name = "yearMonth", value = "对应展示自然月(2024-05)/自然季(2024-Q2)/财务年(2024)") @RequestParam("yearMonth") String yearMonth,
            @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId){
        return Response.success(service.getMainProductsList(dateTypeId,yearMonth,organizationId));
    }

    @ApiOperation(value = "实时数据-主推品",notes = "实时数据-主推品")
    @GetMapping(value = "/mainProducts/export")
    @Deprecated
    public void getMainProductsListExport(@ApiParam(name = "yearMonth", value = "年月") @RequestParam("yearMonth") String yearMonth,
                                          @ApiParam(name = "dateTypeId", value = "时间类型 10:自然月,11:自然季,2:财务年") @RequestParam("dateTypeId") String dateTypeId,
                                          @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId){

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes,"系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        XSSFWorkbook xssfWorkbook = service.getMainProductsListExport(dateTypeId,yearMonth,organizationId);
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        try {
            String fileName = "导出-业绩明细列表";
            fileName=fileName+".xlsx";
            /*}*/
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident") ) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @ApiOperation(value = "获取陈列产品数据", notes = "获取陈列产品数据")
    @GetMapping("/specialChen")
    public Response<SpecialChenVo> querySpecialChenData(TradeGoodReq request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.querySpecialChenData(request));
    }


    @ApiOperation(value = "实时数据-业务查询",notes = "实时数据-业务查询")
    @GetMapping(value = "/realtime/value")
    public Response<List<RealtimeOrganizationVo>> getMainProductsList(
            @ApiParam(name = "employeeId", value = "员工工号") @RequestParam(value ="employeeId",required = false) String employeeId,
            @ApiParam(name = "yearMonth", value = "年月") @RequestParam(value = "yearMonth",required = false) String yearMonth,
            @ApiParam(name = "dateTypeId", value = "时间类型") @RequestParam(value = "dateTypeId",required = false) String dateTypeId,
                                                                      @ApiParam(name = "year", value = "年份") @RequestParam(value = "year",required = false) String year,
                                                               @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId,
                                                               @ApiParam(name = "value", value = "查询内容") @RequestParam("value") String value){
        return Response.success(service.getRealtimeValue(employeeId,dateTypeId,yearMonth,year,organizationId,value));
    }


    @ApiOperation(value = "实时数据-兼岗组织",notes = "实时数据-兼岗组织")
    @GetMapping(value = "/realtime/partJob/Organization")
    public Response<List<CeoBusinessOrganizationEntity>> getPartJobOrganization(@ApiParam(name = "person", value = "工号") @RequestParam("person") String person){
        return Response.success(service.getPartJobOrganization(person));
    }

    @ApiOperation(value = "实时数据-主推品列表下级",notes = "实时数据-主推品列表下级")
    @PostMapping(value = "/realtime/mainProduct/list")
    public Response<List<RealtimeMainProductVo>> getMainProductList(@RequestBody @Validated RealtimeMainProductNewRequest request){
        return Response.success(service.getMainProductListNew(request));
    }

    @ApiOperation(value = "实时数据-主推品列表平铺",notes = "实时数据-主推品列表平铺")
    @PostMapping(value = "/realtime/mainProduct/tileList")
    public Response<IPage<RealtimeMainProductVo>> getMainProductTileList(@RequestBody @Validated RealtimeMainProductTileNewRequest request){
        return Response.success(service.getMainProductTileList(request));
    }

    @ApiOperation(value = "实时数据-主推品列表平铺下载",notes = "实时数据-主推品列表平铺下载")
    @PostMapping(value = "/realtime/mainProduct/downTileList")
    public void downTileList(@RequestBody @Validated RealtimeMainProductTileNewRequest request,HttpServletRequest req,HttpServletResponse res){
        service.downTileList(request,req,res);
    }

    @ApiOperation(value = "报表中心-仓储理论实际",notes = "报表中心-仓储理论实际")
    @PostMapping(value = "/warehouseTheory")
    public Response<IPage<WarehouseTheoryVo>> queryWarehouseTheoryList(@RequestBody WarehouseTheoryReq  request){
        return  Response.success(service.queryWarehouseTheoryList(request));
    }

    @ApiOperation(value = "报表中心-仓储理论实际",notes = "报表中心-仓储理论实际")
    @PostMapping(value = "/export/warehouseTheory")
    public void exportWarehouseTheoryList(@RequestBody WarehouseTheoryReq  request){
        service.exportWarehouseTheoryList(request);
    }

    @ApiOperation(value = "sku下级" , notes = "sku下级")
    @GetMapping("/zwBigData/querySKULine")
    Response<List<SkuSpecificationNewVo>> querySKULine(@ApiParam(name = "theYearMon", value = "年月") @RequestParam("theYearMon") String theYearMon){
        return  Response.success(service.querySKULine(theYearMon));
    };


    @ApiOperation(value = "月报-主推品",notes = "月报-主推品")
    @GetMapping(value = "/mainProducts/monthly")
    public Response<MainProductsDataAllVo> getMainProductsMonthly(@ApiParam(name = "yearMonth", value = "年月") @RequestParam("yearMonth") String yearMonth,
                                                               @ApiParam(name = "organizationId", value = "组织id") @RequestParam("organizationId") String organizationId,
                                                               @ApiParam(name = "monthlyType", value = "月报类型数据(1.月报;2.季报;3.季报的月报)") @RequestParam(value = "monthlyType",defaultValue = "1") Integer monthlyType){
        return Response.success(service.getMainProductsList(yearMonth,organizationId,monthlyType));
    }


    @ApiOperation(value = "月报-主推品列表下级",notes = "月报-主推品列表下级")
    @PostMapping(value = "/mainProduct/monthly/list")
    public Response<List<RealtimeMainProductVo>> getMainProducMonthlytList(@RequestBody RealtimeMainProductRequest request){
        return Response.success(service.getMainProductList(request));
    }

    @ApiOperation(value = "获取业务数据明细列表分公司数据", notes = "获取业务数据明细列表分公司数据")
    @GetMapping("/getResultsDate/business/company")
    public Response<Page<TeamResultsVo>> getResultsDateBusinessCompany(
            TradeGoodReq request) {
        return Response.success(service.getResultsDateBusinessCompany(request));
    }


    @ApiOperation(value = "月报-获取在岗人数-团队达成", notes = "月报获取在岗人数-团队达成")
    @GetMapping("/getResultsDate/business/monthly")
    public Response<Page<TeamResultsVo>> getResultsDateBusinessMonthly(
            TradeGoodReq request) {
        return Response.success(service.getResultsDateBusinessMonthly(request));
    }

    @ApiOperation(value = "月报-获取在岗人数-团队达成平铺模式", notes = "月报获取在岗人数-团队达成平铺模式")
    @GetMapping("/getResultsDate/business/monthly/tileMode")
    public Response<IPage<TeamResultsVo>> getResultsDateBusinessMonthlyTileMode(
            TradeGoodReq request) {
        return Response.success(service.getResultsDateBusinessMonthlyTileMode(request));
    }

    @ApiOperation(value = "月报-异常人员", notes = "月报-异常人员")
    @GetMapping("/getabnormal/person")
    public Response<AbnormalVo> getResultsDateAbnormalPerson(
            TradeGoodReq request) {
        return Response.success(service.getResultsDateAbnormalPerson(request));
    }

    @ApiOperation(value = "更新异常人员", notes = "更新异常人员")
    @PostMapping("/updateAbnormal/person")
    public Response<AtomicReference<Integer>> updateResultsDateAbnormalPerson(
          @RequestBody AbnormalPersonRequest request) {
        service.updateResultsDateAbnormalPerson(request);
        return Response.success();
    }

    @ApiOperation(value = "评价人平均评分",notes = "评价人平均评分")
    @PostMapping(value = "/EvaluatorList/avg")
    public Response<MarkContentAvgVo> getEvaluatorListAvg(@RequestBody @Validated MarkRequest request){
        return Response.success(service.getEvaluatorListAvg(request));
    }


    @ApiOperation(value = "评价人列表",notes = "评价人列表")
    @PostMapping(value = "/EvaluatorList")
    public Response<IPage<MarkVo>> getEvaluatorList(@RequestBody @Validated MarkRequest request){
        return Response.success(service.getEvaluatorList(request));
    }



    @ApiOperation(value = "根据组织ID获取业绩数据(年节90天)", notes = "根据组织ID获取业绩数据")
    @PostMapping("/queryResultsDate/year")
    public Response<RealTimeYearVo> queryResultsDateYear(
            @RequestBody @Validated  RealTimeYearRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.queryResultsDateYear(request));
    }


    @ApiOperation(value = "获取业务数据明细列表(年节90天)", notes = "获取业务数据明细列表(年节90天)")
    @PostMapping("/getResultsDate/business/year")
    public Response<Page<NextResultsYearVo>> getResultsDateBusinessYear(
            @RequestBody @Validated  RealTimeYearRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.getResultsDateBusinessYear(request));
    }

    @ApiOperation(value = "获取商品数据与产品线", notes = "获取商品数据与产品线")
    @PostMapping("/queryGoodsProductLineData/year")
    public Response<IPage<CommodityYearSkuLineVo>> queryGoodsProductLineDataYear(@RequestBody @Validated RealTimeYearRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(service.queryGoodsProductLineDataYear(request));
    }


    @ApiOperation(value = "实时数据/导出陈列产品数据", notes = "实时数据/导出陈列产品数据")
    @GetMapping(value = "/export/specialChen")
    public void exportSpecialChenData(TradeGoodReq request,
                                      HttpServletResponse response) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setIsPage(0);
        SpecialChenVo specialChenVo = service.querySpecialChenData(request);
        List<SpecialChenProductVo> specialChenList = specialChenVo.getSpecialChenList();
        EasyPoiUtil.exportExcel(specialChenList,null,"sheet1",SpecialChenProductVo.class,"导出陈列产品数据.xls",response);
    }


    @ApiOperation(value = "实时数据/导出商品数据sku数据", notes = "实时数据/导出商品数据sku数据")
    @GetMapping(value = "/queryGoodsProductLineData/year/sku/export")
    public void exportGoodsProductLineDataYearSku(RealTimeYearRequest request,
                                      HttpServletResponse response) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        IPage<CommodityYearSkuLineVo> result = service.queryGoodsProductLineDataYear(request);
        List<CommodityYearSkuVo> skuList = new ArrayList<>();
        if(CommonUtil.ListUtils.isNotEmpty(result.getRecords())){
            List<CommodityYearSkuLineVo> records = result.getRecords();
            BeanUtils.copyProperties(records,skuList,CommodityYearSkuLineVo.class,CommodityYearSkuVo.class);
            EasyPoiUtil.exportExcel(skuList,null,"sheet1",CommodityYearSkuVo.class,"导出商品数据sku数据.xls",response);
        }
    }

    @ApiOperation(value = "实时数据/导出商品数据spu数据", notes = "实时数据/导出商品数据spu数据")
    @GetMapping(value = "/queryGoodsProductLineData/year/spu/export")
    public void exportGoodsProductLineDataYearSpu(RealTimeYearRequest request,
                                                  HttpServletResponse response) {
        request.setPage(null);
        request.setRows(null);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        IPage<CommodityYearSkuLineVo> result = service.queryGoodsProductLineDataYear(request);
        ArrayList<CommodityYearSpuVo> spuList = new ArrayList<>();
        if(CommonUtil.ListUtils.isNotEmpty(result.getRecords())){
            BeanUtils.copyProperties(result.getRecords(),spuList,CommodityYearSkuLineVo.class,CommodityYearSpuVo.class);
            EasyPoiUtil.exportExcel(spuList,null,"sheet1",CommodityYearSpuVo.class,"导出商品数据spu数据.xls",response);
        }
    }

    @ApiOperation(value = "实时数据/导出商品数据line数据", notes = "实时数据/导出商品数据line数据")
    @GetMapping(value = "/queryGoodsProductLineData/year/line/export")
    public void exportGoodsProductLineDataYearLine(RealTimeYearRequest request,
                                                  HttpServletResponse response) {
        request.setPage(null);
        request.setRows(null);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        IPage<CommodityYearSkuLineVo> result = service.queryGoodsProductLineDataYear(request);
        ArrayList<CommodityYearLineVo> lineList = new ArrayList<>();
            if(CommonUtil.ListUtils.isNotEmpty(result.getRecords())){
                BeanUtils.copyProperties(result.getRecords(),lineList,CommodityYearSkuLineVo.class,CommodityYearLineVo.class);
                EasyPoiUtil.exportExcel(lineList,null,"sheet1",CommodityYearLineVo.class,"导出商品数据spu数据.xls",response);
            }
    }

    @ApiOperation(value = "获取业务数据明细列表(年节90天)", notes = "获取业务数据明细列表(年节90天)")
    @GetMapping("/getResultsDate/business/year/export")
    public void getResultsDateBusinessYearExport(
            RealTimeYearRequest request,
            HttpServletResponse response) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<NextResultsYearVo> result = service.getResultsDateBusinessYearExport(request);
        EasyPoiUtil.exportExcel(result,null,"sheet1",NextResultsYearVo.class,"导出商品数据spu数据.xls",response);
    }

    @ApiOperation(value = "获取业绩数据编号", notes = "获取业绩数据编号")
    @PostMapping("/queryPerformance/serialNumber")
    public Response<Map<String,String>> queryPerformanceSerialNumber() {
        return Response.success(service.queryPerformanceSerialNumber());
    }


    @ApiOperation(value = "获取业绩数据", notes = "获取业绩数据")
    @PostMapping("/queryPerformance")
    public Response<PerformanceDateVo> queryPerformanceDate(@RequestBody TradeGoodNewReq request) {
        return Response.success(service.queryPerformanceDate(request));
    }

    @ApiOperation(value = "获取业绩数据-同岗位比对", notes = "获取业绩数据-同岗位比对")
    @PostMapping("/querySamePostPerformance")
    public Response<IPage<QuerySamePostPerformanceVo>> querySamePostPerformance(@RequestBody QuerySamePostPerformanceRequest request) {
        return Response.success(service.querySamePostPerformance(request));
    }
    @ApiOperation(value = "获取业绩数据-同岗位比对下载", notes = "获取业绩数据-同岗位比对下载")
    @PostMapping("/querySamePostPerformance/down")
    public void querySamePostPerformanceDown(@RequestBody QuerySamePostPerformanceRequest request,HttpServletRequest req,HttpServletResponse res) {
        service.querySamePostPerformanceDown(request,req,res);
    }

    @ApiOperation(value = "获取业绩数据快照", notes = "获取业绩数据快照")
    @PostMapping("/queryPerformanceDateSnapshot")
    public Response<PerformanceDateVo> queryPerformanceDateSnapshot(@RequestBody TradeGoodNewReq request) {
        return Response.success(service.queryPerformanceDateSnapshot(request));
    }


    @ApiOperation(value = "获取全组业绩数据", notes = "获取全组业绩数据")
    @PostMapping("/queryAllGroupPerformanceDate")
    public Response<List<GroupPerformanceVo>> queryAllGroupPerformanceDate(@RequestBody @Validated AllGroupPerformanceRequest request) {
        return Response.success(service.queryAllGroupPerformanceDate(request));
    }

    @ApiOperation(value = "合伙人趋势查询", notes = "合伙人趋势查询")
    @PostMapping("/selectCeoTrend")
    public Response<CeoMonthTrendVo> selectCeoTrend(@Valid @RequestBody CeoTrendSearchReq request){
        return Response.success(service.selectCeoTrend(request));
    }

    /**
     * 实时数据
     * 业务数据明细 4 个接口
     */

    @ApiOperation(value = "获取业务数据明细-下拉模式", notes = "获取业务数据明细-下拉模式")
    @PostMapping("/queryPerformanceoductDate")
    public Response<List<ServiceDataDetailVo>> queryGoodsPrqueryPerformanceoductDate(@RequestBody TradeGoodNewReq request) {
        return Response.success(service.queryGoodsPrqueryPerformanceoductDate(request));
    }

    @ApiOperation(value = "获取业务数据明细（分页）", notes = "获取业务数据明细（分页）")
    @PostMapping("/queryPerformanceoductDateByPage")
    public Response<IPage<ServiceDataDetailVo>> queryGoodsPrqueryPerformanceoductDateByPage(@RequestBody TradeGoodNewReq request) {
        return Response.success(service.queryGoodsPrqueryPerformanceoductDateByPage(request));
    }


    @ApiOperation(value = "获取业务数据明细（分页）导出", notes = "获取业务数据明细（分页）导出")
    @PostMapping("/queryPerformanceoductDateByPage/export")
    public void queryGoodsPrqueryPerformanceoductDateByPageExport(@RequestBody TradeGoodNewReq request) {
        service.queryGoodsPrqueryPerformanceoductDateByPageExport(request);
    }


    @ApiOperation(value = "获取业务数据明细导出-下拉模式", notes = "获取业务数据明细导出-下拉模式")
    @PostMapping("/queryPerformanceoductDate/export")
    public void queryPerformanceoductDateExport(@RequestBody TradeGoodNewReq request, HttpServletResponse response) {
        service.queryPerformanceoductDateExport(request,response);
    }


    /**
     * 实时数据
     * 产品信息 4 个接口
     */

    @ApiOperation(value = "获取商品数据与产品线", notes = "获取商品数据与产品线")
    @PostMapping("/queryGoodsProductDate")
    public Response<CommodityNewVo> queryGoodsProductDate(@RequestBody TradeGoodNewReq request) {
        return Response.success(service.queryGoodsProductDate(request));
    }

    @ApiOperation(value = "导出商品数据与产品线sku数据", notes = "导出商品数据与产品线sku数据")
    @PostMapping(value = "/queryGoodsProductDate/sku/export")
    public void exportQueryGoodsProductDateSku(@RequestBody TradeGoodNewReq request,
                                                  HttpServletResponse response) {
        log.info("start RealtimeDataController exportQueryGoodsProductDateSku request:{}", request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        List<CommodityDataNewVo> sku = realtimeMapper.queryGoodsProductSku(request);
        if(CommonUtil.ListUtils.isNotEmpty(sku)){
            if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
                List<CommodityDataNewSkuVo> skuList = new ArrayList<>();
                BeanUtils.copyProperties(sku, skuList, CommodityDataNewVo.class, CommodityDataNewSkuVo.class);
                EasyPoiUtil.exportExcel(skuList, realTimeUtils.queryTitle(request.getDateTypeId(), request.getYearMonth(), RequestUtils.getRegion()), "SKU", CommodityDataNewSkuVo.class, "导出商品数据sku数据.xls", Boolean.TRUE, response);
            } else {
                List<CommodityDataNewSkuEnVo> skuList = new ArrayList<>();
                BeanUtils.copyProperties(sku, skuList, CommodityDataNewVo.class, CommodityDataNewSkuEnVo.class);
                EasyPoiUtil.exportExcel(skuList, realTimeUtils.queryTitle(request.getDateTypeId(), request.getYearMonth(), RequestUtils.getRegion()), "SKU", CommodityDataNewSkuEnVo.class, "导出商品数据sku数据.xls", Boolean.TRUE, response);
            }

        }
    }

    @ApiOperation(value = "导出商品数据与产品线spu数据", notes = "导出商品数据与产品线spu数据")
    @PostMapping(value = "/queryGoodsProductDate/spu/export")
    public void exportQueryGoodsProductDateSpu(@RequestBody TradeGoodNewReq request,
                                                  HttpServletResponse response) {
        log.info("start RealtimeDataController exportQueryGoodsProductDateSpu request:{}", request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        List<CommodityDataNewVo> spu = realtimeMapper.queryGoodsProductSpu(request);

        if(CommonUtil.ListUtils.isNotEmpty(spu)){
            if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
                List<CommodityDataNewSpuVo> spuList = new ArrayList<>();
                BeanUtils.copyProperties(spu, spuList, CommodityDataNewVo.class, CommodityDataNewSpuVo.class);
                EasyPoiUtil.exportExcel(spuList, realTimeUtils.queryTitle(request.getDateTypeId(), request.getYearMonth(), RequestUtils.getRegion()), "SPU", CommodityDataNewSpuVo.class, "导出商品数据sku数据.xls", Boolean.TRUE, response);
            } else {
                List<CommodityDataNewSpuEnVo> spuList = new ArrayList<>();
                BeanUtils.copyProperties(spu, spuList, CommodityDataNewVo.class, CommodityDataNewSpuEnVo.class);
                EasyPoiUtil.exportExcel(spuList, realTimeUtils.queryTitle(request.getDateTypeId(), request.getYearMonth(), RequestUtils.getRegion()), "SPU", CommodityDataNewSpuEnVo.class, "导出商品数据sku数据.xls", Boolean.TRUE, response);
            }

        }
    }

    @ApiOperation(value = "导出商品数据与产品线line数据", notes = "导出商品数据与产品线line数据")
    @PostMapping(value = "/queryGoodsProductDate/line/export")
    public void exportQueryGoodsProductDateLine(@RequestBody TradeGoodNewReq request,
                                                   HttpServletResponse response) {
        log.info("start RealtimeDataController exportQueryGoodsProductDateLine request:{}", request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        request.setYearMonthYoy(realTimeUtils.querySameTerm(request.getDateTypeId(), request.getYearMonth()));
        List<CommodityDataNewVo> line = realtimeMapper.queryGoodsProductLine(request);
        if(CommonUtil.ListUtils.isNotEmpty(line)){
            if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
                List<CommodityDataNewLineVo> lineList = new ArrayList<>();
                BeanUtils.copyProperties(line, lineList, CommodityDataNewVo.class, CommodityDataNewLineVo.class);
                EasyPoiUtil.exportExcel(lineList, realTimeUtils.queryTitle(request.getDateTypeId(), request.getYearMonth(), RequestUtils.getRegion()), "Line", CommodityDataNewLineVo.class, "导出商品数据sku数据.xls", Boolean.TRUE, response);
            } else {
                List<CommodityDataNewLineEnVo> lineList = new ArrayList<>();
                BeanUtils.copyProperties(line, lineList, CommodityDataNewVo.class, CommodityDataNewLineEnVo.class);
                EasyPoiUtil.exportExcel(lineList, realTimeUtils.queryTitle(request.getDateTypeId(), request.getYearMonth(), RequestUtils.getRegion()), "Line", CommodityDataNewLineEnVo.class, "导出商品数据sku数据.xls", Boolean.TRUE, response);

            }
        }
    }



    @ApiOperation(value = "新运营趋势分析",notes = "新运营趋势分析")
    @PostMapping(value = "/operateTendency/analyze")
    public Response<List<PerformanceOperateDateVo>> getOperateTendencyAnalyze(@RequestBody @Validated PerformanceOperateDateRequest request){
        return Response.success(service.getOperateTendencyAnalyze(request));
    }

    @ApiOperation(value = "新运营趋势分析数据导出", notes = "新运营趋势分析数据导出")
    @PostMapping(value = "/operateTendency/analyze/export")
    public void getOperateTendencyAnalyzeExport(@RequestBody PerformanceOperateDateRequest request,
                                                HttpServletResponse httpServletResponse,HttpServletRequest httpServletRequest) {
        service.downOperateTendencyAnalyze(request,httpServletResponse,httpServletRequest);
    }
    @ApiOperation(value = "新人员趋势分析",notes = "新人员趋势分析")
    @PostMapping(value = "/personnelTendency/analyze")
    public Response<List<PerformancePersonnelDateVo>> getPersonnelTendencyAnalyze(@RequestBody @Validated PerformancePersonnelDateRequest request){
        return Response.success(service.getPersonnelTendencyAnalyze(request));
    }
    @ApiOperation(value = "新人员趋势分析数据导出",notes = "新人员趋势分析数据导出")
    @PostMapping(value = "/personnelTendency/analyze/export")
    public void exportPersonnelTendencyAnalyze(@RequestBody PerformancePersonnelDateRequest request,
                                               HttpServletResponse httpServletResponse,HttpServletRequest httpServletRequest){
        service.exportPersonnelTendencyAnalyze(request,httpServletResponse,httpServletRequest);
    }


    @ApiOperation(value = "新运营趋势分析快照-月报使用",notes = "新运营趋势分析快照-月报使用")
    @PostMapping(value = "/operateTendency/analyzeSnapshot")
    public Response<List<PerformanceOperateDateVo>> getOperateTendencyAnalyzeSnapshot(@RequestBody PerformanceOperateDateRequest request){
        return Response.success(service.getOperateTendencyAnalyzeSnapshot(request));
    }





    @ApiOperation(value = "业务BD列表查询",notes = "业务BD列表查询")
    @PostMapping(value = "/businessDevelopment/query")
    public Response<IPage<BusinessDevelopmentVo>> queryBusinessDevelopment(@Valid @RequestBody BusinessDevelopmentRequest request){
        return Response.success(service.queryBusinessDevelopment(request));
    }
    @ApiOperation(value = "业务BD列表导出",notes = "业务BD列表导出")
    @PostMapping(value = "/businessDevelopment/down")
    public void downBusinessDevelopment(@Valid @RequestBody BusinessDevelopmentRequest req, HttpServletRequest request, HttpServletResponse response){
        service.downBusinessDevelopment(req,request,response);
    }

    @ApiOperation(value = "预订单看板-列表查询",notes = "预订单看板-列表查询")
    @PostMapping(value = "/advanceOrderBoard/queryPageList")
    public Response<IPage<AdvanceOrderBoardPageListVo>> queryAdvanceOrderPageList(@RequestBody @Validated AdvanceOrderBoardPageListRequest request){
        return Response.success(service.queryAdvanceOrderPageList(request));
    }

    @ApiOperation(value = "预订单看板-列表下载",notes = "预订单看板-列表下载")
    @PostMapping(value = "/advanceOrderBoard/downLoadPageList")
    public void downAdvanceOrderPageList(@RequestBody @Validated AdvanceOrderBoardPageListRequest request,HttpServletRequest req,HttpServletResponse res){
        service.downAdvanceOrderPageList(request,req,res);
    }

    @ApiOperation(value = "预订单看板-详情列表查询",notes = "预订单看板-详情列表查询")
    @PostMapping(value = "/advanceOrderBoard/queryDetailPageList")
    public Response<IPage<AdvanceOrderBoardPageDetailListVo>> queryAdvanceOrderDetailPageList(@RequestBody @Validated AdvanceOrderBoardPageDetailListRequest request){
        return Response.success(service.queryAdvanceOrderDetailPageList(request));
    }

    @ApiOperation(value = "预订单看板-详情列表下载",notes = "预订单看板-详情列表下载")
    @PostMapping(value = "/advanceOrderBoard/downLoadDetailPageList")
    public void downAdvanceOrderDetailPageList(@RequestBody @Validated AdvanceOrderBoardPageDetailListRequest request,HttpServletRequest req,HttpServletResponse res){
        service.downAdvanceOrderDetailPageList(request,req,res);
    }
    @ApiOperation(value = "预订单看板-枚举列表",notes = "预订单看板-枚举列表")
    @PostMapping(value = "/advanceOrderBoard/getAdvanceOrderBoardEnums")
    public Response<AdvanceOrderBoardEnumsVo> getAdvanceOrderBoardEnums(@RequestBody @Validated AdvanceOrderBoardEnumsRequest request){
        return Response.success(service.getAdvanceOrderBoardEnums(request));
    }

    @ApiOperation(value = "库存盘点-趋势枚举列表",notes = "库存盘点-趋势枚举列表")
    @PostMapping(value = "/inventoryCheck/queryCheckTrendInfo")
    public Response<List<PartnerInventoryCheckTrendQueryInfoVo>> queryPartnerInventoryCheckTrendInfo(@RequestBody @Validated PartnerInventoryCheckTrendRequest request){
        return Response.success(service.queryPartnerInventoryCheckTrendInfo(request));
    }

    @ApiOperation(value = "库存盘点-趋势列表",notes = "库存盘点-趋势列表")
    @PostMapping(value = "/inventoryCheck/queryCheckTrendList")
    public Response<List<PartnerInventoryCheckTrendVo>> queryPartnerInventoryCheckTrendList(@RequestBody @Validated PartnerInventoryCheckTrendRequest request){
        return Response.success(service.queryPartnerInventoryCheckTrendList(request));
    }

    @ApiOperation(value = "库存盘点-列表",notes = "库存盘点-列表")
    @PostMapping(value = "/inventoryCheck/queryCheckList")
    public Response<IPage<PartnerInventoryCheckListVo>> queryPartnerInventoryCheckList(@RequestBody @Validated PartnerInventoryCheckListRequest request){
        return Response.success(service.queryPartnerInventoryCheckList(request));
    }
    @ApiOperation(value = "库存盘点-列表导出",notes = "库存盘点-列表导出")
    @PostMapping(value = "/inventoryCheck/downCheckList")
    public void downPartnerInventoryCheckList(@RequestBody @Validated PartnerInventoryCheckListRequest request,HttpServletResponse res,HttpServletRequest req){
        service.downPartnerInventoryCheckList(request,res,req);
    }

    @ApiOperation(value = "合伙人达成-列表",notes = "合伙人达成-列表")
    @PostMapping(value = "/partnerGoalAttainment/queryCheckList")
    public Response<IPage<PartnerGoalAttainmentVo>> queryPartnerGoalAttainmentList(@RequestBody @Validated PartnerGoalAttainmentRequest request){
        return Response.success(service.queryPartnerGoalAttainmentList(request));
    }
    @ApiOperation(value = "合伙人达成-列表导出",notes = "合伙人达成-列表导出")
    @PostMapping(value = "/partnerGoalAttainment/downCheckList")
    public void downPartnerGoalAttainmentList(@RequestBody @Validated PartnerGoalAttainmentRequest request,HttpServletRequest req,HttpServletResponse res){
        service.downPartnerGoalAttainmentList(request,req,res);
    }
    @ApiOperation(value = "合伙人达成-订单明细列表",notes = "合伙人达成-订单明细列表")
    @PostMapping(value = "/partnerGoalAttainment/queryOrderList")
    public Response<IPage<PartnerGoalAttainmentOrderVo>> partnerGoalAttainmentOrderList(@RequestBody @Validated PartnerGoalAttainmentOrderRequest request){
        return Response.success(service.partnerGoalAttainmentOrderList(request));
    }
    @ApiOperation(value = "合伙人达成-拜访记录列表",notes = "合伙人达成-拜访记录列表")
    @PostMapping(value = "/partnerGoalAttainment/queryVisitRecords")
    public Response<IPage<PartnerGoalAttainmentVisitRecordVo>> partnerGoalAttainmentVisitRecords(@RequestBody @Validated PartnerGoalAttainmentVisitRecordRequest request){
        return Response.success(service.partnerGoalAttainmentVisitRecords(request));
    }
}
