package com.wantwant.sfa.backend.controller;


import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.rebase.request.*;
import com.wantwant.sfa.backend.rebase.service.IRebaseService;
import com.wantwant.sfa.backend.rebase.vo.*;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Api(tags = "spu任务返利")
@RestController
@RequestMapping("/rebase")
public class RebaseController {

    private static final String REBASE_TASK_APPLY_LOCK = "rebase:task:apply:lock";
    private static final String REBASE_ADD_RULES_LOCK = "rebase:add:rules:lock";
    private static final String REBASE_DISTRIBUTE_RULES_LOCK = "rebase:distribute:rules:lock";


    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IRebaseService iRebaseService;

    @ApiOperation(value = "新增spu返利任务", notes = "新增spu返利任务")
    @PostMapping("/add")
    public Response addRebaseTask(@RequestBody @Validated RebaseApplyRequest request) {
        log.info("addRebaseTask request:{}", request);
        if(!redisUtil.setLockIfAbsent(REBASE_TASK_APPLY_LOCK,request.getTargetId(),10, TimeUnit.SECONDS)){
            return Response.error("请勿重复操作!");
        }
        try{
            iRebaseService.addRebaseTask(request);
        }finally{
            redisUtil.unLock(REBASE_TASK_APPLY_LOCK,request.getTargetId());
        }
        return Response.success();
    }

    @ApiOperation(value="spu任务列表", notes = "spu任务列表")
    @PostMapping("/task/list")
    public Response<List<RebaseTaskListVo>> getSPUTaskList(@RequestBody @Validated RebaseTaskListRequest request) {
        log.info("getSPUTaskList request:{}", request);
        return Response.success(iRebaseService.getTaskList(request));
    }

    @ApiOperation(value = "营业所新增返利规则", notes = "营业所新增返利规则")
    @PostMapping("/rule/add")
    public Response addTaskRules(@RequestBody @Validated RebaseRuleAddRequest request) {
        log.info("addTaskRules: {}", request.getDepartmentId());
        if(RequestUtils.getLoginInfo().getPositionTypeId().intValue() == 7) {
            throw new ApplicationException("仅限营业所及以上各级业务主管设置");
        }
        if(!redisUtil.setLockIfAbsent(REBASE_ADD_RULES_LOCK, request.getDepartmentId() ,10, TimeUnit.SECONDS)){
            return Response.error("请勿重复操作!");
        }
        try{
            iRebaseService.addTaskRules(request);
        }finally{
            redisUtil.unLock(REBASE_ADD_RULES_LOCK, request.getDepartmentId());
        }
        return Response.success();
    }


    @ApiOperation(value = "返利规则列表", notes = "返利规则列表")
    @PostMapping("/rule/list")
    public Response<List<RebaseRuleDetailVo>> ruleList(@RequestBody @Validated RebaseRuleDetailRequest request) {
        log.info("ruleDetail: {}", request);
        return Response.success(iRebaseService.getRulesList(request)) ;
    }

    @ApiOperation(value = "合伙人和规则绑定详情", notes = "返利营业所及合伙人配置详情")
    @PostMapping("rule/detail")
    public Response<List<RebaseEmployeeRuleDetailVo>> ruleDetail(@RequestBody @Validated RebaseRuleDetailRequest request){
        log.info("ruleDetail:{}", request);
        return Response.success(iRebaseService.getRuleDetail(request));
    }

    @ApiOperation(value = "合伙人和规则绑定详情for App", notes = "返利营业所及合伙人配置详情for App")
    @PostMapping("rule/detailForApp")
    public Response<List<RebaseEmployeeRuleDetailForAppVo>> ruleDetailForApp(@RequestBody @Validated RebaseRuleDetailRequest request){
        log.info("ruleDetailForApp:{}", request);
        return Response.success(iRebaseService.getRuleDetailForApp(request));
    }



    @ApiOperation(value = "分配合伙人详情页", notes = "分配合伙人详情页")
    @PostMapping("rule/distribute/detail")
    public Response<List<RebaseRuleDistributeVo>> distributeDetail(@RequestBody @Validated RebaseRuleDetailRequest request){
        log.info("ruleDetail:{}", request);
        return Response.success(iRebaseService.getDistributeDetail(request));
    }

    @ApiOperation(value = "合伙人返利规则绑定", notes = "合伙人配置规则")
    @PostMapping("rule/distribute")
    public Response distributeRule(@RequestBody @Validated RebasePartnerDistributeRuleRequest request){
        log.info("distributeRule: {}", request);
        if(RequestUtils.getLoginInfo().getPositionTypeId().intValue() == 7) {
            throw new ApplicationException("仅限营业所及以上各级业务主管设置");
        }
        if(!redisUtil.setLockIfAbsent(REBASE_DISTRIBUTE_RULES_LOCK, request.getDepartmentId() ,5, TimeUnit.SECONDS)){
            return Response.error("规则提交中，请勿重复操作!");
        }
        try{
            iRebaseService.distributePartnerRule(request);
        }finally{
            redisUtil.unLock(REBASE_DISTRIBUTE_RULES_LOCK, request.getDepartmentId());
        }
        return Response.success();
    }
}
