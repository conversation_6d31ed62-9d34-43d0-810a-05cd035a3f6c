package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.receiptAndDelivery.api.ReceiptAndDeliveryApi;
import com.wantwant.sfa.backend.receiptAndDelivery.request.QueryDTO;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.DeliveryTimeVO;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.OrderDeliveryVO;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.ReceiptTimeResponse;
import com.wantwant.sfa.backend.receiptAndDelivery.vo.TransportTimeVO;
import com.wantwant.sfa.backend.service.IReceiptAndDeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 收发货时效
 *
 * <AUTHOR>
 * @date 2021-06-01 18:29
 * @version 1.0
 */
@RestController
@Slf4j
public class ReceiptAndDeliveryController implements ReceiptAndDeliveryApi {


	@Autowired
	private IReceiptAndDeliveryService iReceiptAndDeliveryService;


	@Override
	public void clearCache(String key) {
		iReceiptAndDeliveryService.clearCache(key);
	}

	/**
	 * 获取仓库发货时效
	 *
	 * @param
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.receiptAndDelivery.vo.DeliveryTimeResponse>
	 * @author: zhouxiaowen
	 * @date: 2021-06-01 18:30
	 */
	@Override
	public Response<List<DeliveryTimeVO>> getDeliveryTime() {
		List<DeliveryTimeVO> deliveryTimeVos = iReceiptAndDeliveryService.getDeliveryTime();
		return Response.success(deliveryTimeVos);
	}

	/**
	 * 获取客户收货时效
	 *
	 * @param
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.receiptAndDelivery.vo.ReceiptTimeVo>>
	 * @author: zhouxiaowen
	 * @date: 2021-06-04 14:10
	 */
	@Override
	public Response<ReceiptTimeResponse> getReceiptTime() {
		ReceiptTimeResponse receiptTimeResponse = iReceiptAndDeliveryService.getReceiptTime();
		return Response.success(receiptTimeResponse);
	}

	/**
	 * 获取作业时效
	 *
	 * @param
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.receiptAndDelivery.vo.DeliveryTimeVO>>
	 * @author: zhouxiaowen
	 * @date: 2021-07-14 18:42
	 */
	@Override
	public Response<List<DeliveryTimeVO>> getWorkTime() {
		List<DeliveryTimeVO> list = iReceiptAndDeliveryService.getWorkTime();
		return Response.success(list);
	}



	/**
	 * 订单出库(支付时间～发货时间)
	 *
	 * @param queryDTO
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.receiptAndDelivery.vo.DeliveryTimeVO>>
	 * @author: zhouxiaowen
	 * @date: 2021-07-19 10:23
	 */
	@Override
	public Response<List<OrderDeliveryVO>> orderDeliveryList(QueryDTO queryDTO) {
		List<OrderDeliveryVO> list = iReceiptAndDeliveryService.orderDeliveryList(queryDTO);
		return Response.success(list);
	}

	/**
	 * 仓库发货(推单时间～发货时间)
	 *
	 * @param queryDTO
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.receiptAndDelivery.vo.OrderDeliveryVO>>
	 * @author: zhouxiaowen
	 * @date: 2021-07-19 14:51
	 */
	@Override
	public Response<List<OrderDeliveryVO>> warehouseDeliveryList(QueryDTO queryDTO) {
		List<OrderDeliveryVO> list = iReceiptAndDeliveryService.warehouseDeliveryList(queryDTO);
		return Response.success(list);
	}

	/**
	 * 运输时效(发货时间～送达时间)
	 *
	 * @param queryDTO
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.receiptAndDelivery.vo.TransportTimeVO>>
	 * @author: zhouxiaowen
	 * @date: 2021-07-19 17:12
	 */
	@Override
	public Response<List<TransportTimeVO>> transportTimeList(QueryDTO queryDTO) {
		List<TransportTimeVO> list = iReceiptAndDeliveryService.transportTimeList(queryDTO);
		return Response.success(list);
	}

	/**
	 * 订单交付(送达时间～支付时间)
	 *
	 * @param queryDTO
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.receiptAndDelivery.vo.TransportTimeVO>>
	 * @author: zhouxiaowen
	 * @date: 2021-07-20 10:24
	 */
	@Override
	public Response<List<TransportTimeVO>> orderReceiptList(QueryDTO queryDTO) {
		List<TransportTimeVO> list = iReceiptAndDeliveryService.orderReceiptList(queryDTO);
		return Response.success(list);
	}
}
