package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.referralBonus.api.ReferralBonusApi;
import com.wantwant.sfa.backend.referralBonus.request.*;
import com.wantwant.sfa.backend.referralBonus.vo.*;
import com.wantwant.sfa.backend.service.DailyService;
import com.wantwant.sfa.backend.service.ReferralBonusService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
public class ReferralBonusController implements ReferralBonusApi {

    private static final String REFERRALBONUS_ISSUE_LOCK = "referralBonus:issue";

    @Autowired
    private ReferralBonusService  referralBonusService;

    @Autowired
    private DailyService dailyService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Response<Page<ReferralBonusDailyVo>> getReferralBonusDailyList(ReferralBonusDailyRequest request) {
        return Response.success(dailyService.getReferralBonusDailyList(request));
    }

    @Override
    public Response<List<String>> seleferralBonusRewardScheme() {
        return Response.success(dailyService.seleferralBonusRewardScheme());
    }

    @Override
    public Response referralIssueResultUpdate(ReferralBonusResultRequest request) {
        return Response.success(referralBonusService.referralIssueResultUpdate(request));
    }

    @Override
    public Response<Page<ReplacementVo>> getReplacementList(ReplacementRequest request) {
        return Response.success(referralBonusService.getReplacementList(request));
    }

    @Override
    public Response<Page<ButtonVo>> getButtonList(ButtonRequest request)
    {
        return Response.success(referralBonusService.getButtonList(request));
    }

    @Override
    public Response<List<String>> getButtonProjectList() {
        return Response.success(referralBonusService.getButtonProjectList());
    }
}
