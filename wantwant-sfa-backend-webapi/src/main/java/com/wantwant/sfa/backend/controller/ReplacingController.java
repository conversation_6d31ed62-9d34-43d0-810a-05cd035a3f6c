package com.wantwant.sfa.backend.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.replacing.api.ReplacingApi;
import com.wantwant.sfa.backend.replacing.request.*;
import com.wantwant.sfa.backend.replacing.vo.*;
import com.wantwant.sfa.backend.service.IReplacingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class ReplacingController implements ReplacingApi {

  @Autowired private IReplacingService replacingService;

  @Override
  public Response<ReplacingVo> replacingDateInfo(ReplacingRequest request) {
    log.info("start ReplacingController replacingDateInfo request:{}", request);
    ReplacingVo replacingVoPage = replacingService.selReplacingList(request);
    return Response.success(replacingVoPage);
  }

  @Override
  public Response<ReplacingDetailVo> replacingDateDtailsInfo(ReplacingRequest request) {
    log.info("start ReplacingController replacingDateDtailsInfo request:{}", request);
    ReplacingDetailVo replacingDetailVo = replacingService.selReplacingDateDtailsInfo(request);
    return Response.success(replacingDetailVo);
  }

  @Override
  public Response<ReplacingWarningVo> replacingWarningDateInfo(ReplacingWarningRequest request) {
    log.info("start ReplacingController replacingWarningDateInfo request:{}", request);
    ReplacingWarningVo replacingVoPage = replacingService.selReplacingWarningList(request);
    return Response.success(replacingVoPage);
  }

  @Override
  public Response<ReplacingWarningDetailVo> replacingDateWarningDtailsInfo(ReplacingWarningRequest request) {
    log.info("start ReplacingController replacingDateWarningDtailsInfo request:{}", request);
    ReplacingWarningDetailVo replacingDetailVo = replacingService.selReplacingDateWarningDtailsInfo(request);
    return Response.success(replacingDetailVo);
  }

  @Override
  public Response<Integer> replacingDateRuleSetUp(ReplacingRuleRequest request) {
    Integer count = replacingService.replacingDateRuleSetUp(request);
    return Response.success(count);
  }

  @Override
  public Response<List<ReplacingRuleDateVo>> replacingDateRuleSetUpList(
      ReplacingRuleDateRequest request) {
    return Response.success(replacingService.replacingDateRuleSetUpList(request));
  }

  @Override
  public Response<ReplacingCompanyVo> replacingDateCompnayDate() {
    return Response.success(replacingService.replacingDateCompnayDate());
  }

  @Override
  public Response<Integer> replacingDateRuleSetUpRedact(ReplacingRuleRequest request) {
    return Response.success(replacingService.replacingDateRuleSetUpRedact(request));
  }

  @Override
  public Response<Integer> replacingDateRuleSetUpDelete(ReplacingRuleDeleteRequest request) {

    return Response.success(replacingService.replacingDateRuleSetUpDelete(request));
  }

  @Override
  public Response<ReplacingLabelVo> replacingDateLabe(
      ReplacingRuleOrganiztaion replacingRuleOrganiztaion) {
    String organiztaionId = replacingRuleOrganiztaion.getOrganiztaionId();
    return Response.success(replacingService.replacingDateLabe(organiztaionId));
  }

  @Override
  public Response<Page<ReplacingCompanyRulesVo>> replacingDateCompanyRules(
      ReplacingRuleMemberkey peplacingRuleMemberkey) {
    String memberKey = peplacingRuleMemberkey.getMemberKey();
    return Response.success(replacingService.replacingDateCompanyRules(memberKey));
  }

  @Override
  public Response<List<String>> replacingDateZbRulesNumber() {
    return Response.success(replacingService.replacingDateZbRulesNumber());
  }

  @Override
  public Response<Integer> updateReplacingExamine(ReplacingExamineRequest request) {
    return Response.success(replacingService.updateReplacingExamine(request));
  }
}
