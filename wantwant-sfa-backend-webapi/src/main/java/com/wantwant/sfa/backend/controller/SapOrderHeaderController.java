package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.sap.request.*;
import com.wantwant.sfa.backend.sap.vo.*;
import com.wantwant.sfa.backend.service.SapOrderHeaderService;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
* sap订单相关接口
 *
* @since 2022-09-21
*/
@Api(tags = "sap订单相关接口")
@RestController
@RequestMapping("/sapOrder")
public class SapOrderHeaderController {

	@Autowired
	private SapOrderHeaderService sapOrderHeaderService;

	@Autowired
	private RedisUtil redisUtil;

	public static final String LOCK_SAP_COMMIT = "lock_sap_commit";

	/**
	 * 批量保存订单数据
	 *
	 * @param request
	 * @return: int
	 * @date: 10/11/22
	 */
	@ApiOperation(value = "批量保存订单数据返回保存成功订单号")
	@PostMapping(value = "/save")
	public Response<List<String>> saveOrder(@RequestBody List<SapOrderSaveRequest> request) {
		return Response.success(sapOrderHeaderService.saveOrder(request));
	}


	@ApiOperation(notes = "订单类型", value = "订单类型")
	@GetMapping("/orderTypeList")
	public Response<List<String>> orderTypeList() {
		return Response.success(sapOrderHeaderService.orderTypeList());
	}

	/**
	 * 常规订单(待处理)
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.sap.vo.SapOrderVO>>
	 * @date: 9/21/22 7:39 PM
	 */
	@ApiOperation(notes = "常规订单(待处理)", value = "常规订单(待处理)")
	@PostMapping("/waitList")
	public Response<List<SapOrderPendingVO>> waitList(@RequestBody SapOrderPendingQuery request) {
		return Response.success(sapOrderHeaderService.waitList(request));
	}

	/**
	 * 常规订单(待处理订单行信息)
	 *
	 * @param code
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.sap.vo.SapOrderLineDetailVO>>
	 * @date: 7/17/24 7:39 PM
	 */
	@ApiOperation(notes = "待处理-订单行信息", value = "待处理-订单行信息")
	@GetMapping("/orderLineList")
	public Response<List<SapOrderLineDetailVO>> getOrderLineDetailList(@RequestParam String code) {
		return Response.success(sapOrderHeaderService.getOrderLineDetailList(code));
	}

	/**
	 * 下一步合并订单
	 *
	 * @param request
	 * @return com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.sap.vo.SapMergeOrderVO>>
	 * @since 2022/11/29
	 */
	@ApiOperation(notes = "下一步合并订单", value = "下一步合并订单")
	@PostMapping("/mergeOrder")
	public Response<List<SapMergeOrderVO>> mergeOrder(@RequestBody SapOrderMergeQuery request) {
		return Response.success(sapOrderHeaderService.mergeOrder(request));
	}

	/**
	 * 提交sap
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.sap.vo.SapMergeOrderVO>>
	 * @date: 12/5/22 3:20 PM
	 */
	@ApiOperation(notes = "提交sap", value = "提交sap")
	@PostMapping("/submit")
	public Response<List<SapMergeOrderVO>> submit(@Valid @RequestBody SapMergeSubmitRequest request) {
		if(Objects.isNull(request.getId())) {
			throw new ApplicationException("提交数据为空");
		}
//		if(ids.size() > 1) {
//			throw new ApplicationException("只支持单条提交");
//		}
		List<SapMergeOrderVO> list = null;
		if(!redisUtil.setLockIfAbsent(LOCK_SAP_COMMIT,request.getId().toString(),120, TimeUnit.SECONDS)){
			return Response.error("请求正在处理中！～");
		}
		try {
			list = sapOrderHeaderService.submit(request);
		}finally {
			redisUtil.unLock(LOCK_SAP_COMMIT,request.getId().toString());
		}
		return Response.success(list);
	}

	/**
	 * 常规订单(已完成)
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.display.vo.DisplayInfoVO>>
	 * @date: 5/19/22 6:05 PM
	 */
	@ApiOperation(notes = "常规订单(已完成或者处理中)", value = "常规订单(已完成或处理中)")
	@PostMapping("/list")
	public Response<IPage<SapMergeOrderVO>> sapOrderList(@RequestBody SapOrderQuery request) {
	    
		if(request.getStatus() == null || (request.getStatus() != 0 && request.getStatus() != 1)) {
			throw new ApplicationException("请传入正确的订单状态");
		}
		return Response.success(sapOrderHeaderService.selectSapOrder(request));
	}

	/**
	 * sap订单合并后的订单行信息
	 *
	 * @param bstnk 采购订单编号
	 * @date: 5/19/22 6:05 PM
	 */
	@ApiOperation(notes = "造旺订单合并后订单详情(已完成或者处理中)", value = "造旺订单合并后订单详情")
	@GetMapping("/merge/detail")
	public Response<List<SapMergeOrderDetailVO>> sapOrderDetail(@RequestParam("bstnk") String bstnk) {

		return Response.success(sapOrderHeaderService.mergeOrderLineDetail(bstnk));
	}

	/**
	 * sap合并后的订单行与合并前的对应关系
	 *
	 * @param id 合并后订单号
	 * @date: 5/19/22 6:05 PM
	 */
	@ApiOperation(notes = "sap合并后的订单行与合并前的对应关系", value = "订单合并前后对应关系")
	@GetMapping("/origin/detail")
	public Response<List<SapOrderOriginDetailVO>> sapOrderOriginOrderInfo(@RequestParam("id") Integer id) {
		return Response.success(sapOrderHeaderService.mergeSapOrderOfOriginOrder(id));
	}

	/**
	 * 逻辑删除合并后的订单行
	 *
	 * @param request
	 * @date: 5/19/22 6:05 PM
	 */
	@ApiOperation(notes = "逻辑删除合并后的订单行信息", value = "逻辑删除合并后的订单行信息")
	@PostMapping("/update/orderLine")
	public Response sapOrderUpdateOrderLine(@RequestBody SapOrderUpdateMergeInfoRequest request) {
		if(request.getId() == null ) {
			throw new ApplicationException("请传入合并后的订单行id");
		}
		sapOrderHeaderService.udpateMergerOrderLine(request.getId());
		return Response.success();
	}

	/**
	 * sap订单异常信息
	 *
	 * @date: 4/11/23 6:05 PM
	 */
	@ApiOperation(notes = "sap订单异常信息", value = "sap订单异常信息")
	@GetMapping("/exception/detail")
	public Response<SapOrderExceptionInfoVO> sapOrderExceptionInfo(@ApiParam("1：手动点击重新检查，触发预处理逻辑 0：其它") @RequestParam("type") int type) {
		return Response.success(sapOrderHeaderService.sapOrderExceptionInfo(type));
	}

	/**
	 * 新增物料
	 *
	 * @param request
	 * @date: 5/19/22 6:05 PM
	 */
	@ApiOperation(notes = "新增物料", value = "新增物料")
	@PostMapping("/save/sku")
	public Response sapOrderSaveSku(@RequestBody SapConfigSkuRequest request) {
		sapOrderHeaderService.sapOrderSaveSku(request);
		return Response.success();
	}

	@ApiOperation(value = "SAP导出", notes = "SAP导出", httpMethod = "GET")
	@GetMapping("/export")
	void exportSapOrderByType(@RequestParam("type") int type,  @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate, HttpServletResponse response) {
		sapOrderHeaderService.exportSapByType(type, startDate, endDate, response);
	}
}
