package com.wantwant.sfa.backend.controller;

//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.service.VisitCustomerDetailService;
import com.wantwant.sfa.backend.service.VisitCustomerInventoryCheckService;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.visitCustomer.api.VisitDetailApi;
import com.wantwant.sfa.backend.visitCustomer.request.InventoryCheckRequest;
import com.wantwant.sfa.backend.visitCustomer.request.VisitAuditRequest;
import com.wantwant.sfa.backend.visitCustomer.request.VisitDetailRequest;
import com.wantwant.sfa.backend.visitCustomer.vo.VisitDetailVo;
import com.wantwant.sfa.backend.visitCustomer.vo.VisitInventoryCheckVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wantwant.commons.pagination.Page;

import java.util.concurrent.TimeUnit;


/**
 * @Description：拜访客户详情
 * @Author： chen
 * @Date 2022/5/19
 */
@Slf4j
@Api(tags = "拜访客户")
@RestController
@RequestMapping("/visitCustomer")
public class VisitCustomerDetailController implements VisitDetailApi {

    private static final String VISIT_AUDIT_LOCK = "visit:audit:lock";

    @Autowired
    private VisitCustomerDetailService visitCustomerDetailService;

    @Autowired
    private VisitCustomerInventoryCheckService inventoryCheckService;


    @Autowired
    private RedisUtil redisUtil;



    @Override
    public Response<VisitDetailVo> visitDetail(VisitDetailRequest request) {

        Response<VisitDetailVo> visitDetailVo = visitCustomerDetailService.getVisitCustomerDetialInfo(request);

        return visitDetailVo;
    }

    @Override
    public Response<Page<VisitInventoryCheckVO>> inventoryCheck(InventoryCheckRequest request) {
        Response<Page<VisitInventoryCheckVO>> pageResponse= inventoryCheckService.getInventoryCheckList(request);

        return pageResponse;
    }

    @Override
    public Response visitAudit(VisitAuditRequest request) {
        log.info("visitAudit request :{}", request);
        if(!redisUtil.setLockIfAbsent(VISIT_AUDIT_LOCK,request.getVisitId(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        try{
            visitCustomerDetailService.visitAudit(request);
        }finally {
            redisUtil.unLock(VISIT_AUDIT_LOCK,request.getVisitId());
        }
        return Response.success();    }


}
