package com.wantwant.sfa.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.Task.SupplierMessageTask;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.service.WarehouseService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.warehouse.request.*;
import com.wantwant.sfa.backend.warehouse.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 仓储管理
 *
 * @date 4/7/22 2:43 PM
 * @version 1.0
 */
@Slf4j
@Api(tags = "仓储管理")
@RestController
@RequestMapping("/warehouse")
public class WarehouseController {

    private String WAREHOUSE_AUDIT_LOCK = "warehouse:audit:apply";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private SettingsMapper settingsMapper;

    @Autowired
    private SupplierMessageTask supplierMessageTask;

    @ApiOperation(value = "费用管理-承运商枚举值", notes = "费用管理-承运商枚举值")
    @GetMapping(value = "/listDeliveryCompany")
    public Response<List<String>> listDeliveryCompany() {
        String deliveryCompany = settingsMapper.getSfaSettingsByCode("expense_deliveryCompany");
        List<String> list = new ArrayList<>();
        if (CommonUtil.StringUtils.isNotBlank(deliveryCompany)) {
            list = Arrays.asList(deliveryCompany.split(","));
        }
        return Response.success(list);
    }

    /**
     * 费用管理-仓库维度
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.warehouse.vo.ExpenseVO>>
     * @date: 4/7/22 4:43 PM
     */
    @Deprecated
    @ApiOperation(value = "费用管理-仓库维度", notes = "费用管理-仓库维度")
    @PostMapping(value = "/queryExpenseList")
    public Response<List<ExpenseVO>> queryExpenseByList(@RequestBody ExpenseRequest request) {
        List<ExpenseVO>  expenseVOS =null;
        if(request.getCostType()==4){
            expenseVOS = warehouseService.queryDrawingExpenseByList(request);
        }else if(request.getCostType()==0){
            expenseVOS = warehouseService.queryExpenseByList(request);
        }else{
            expenseVOS = warehouseService.querySingleBackByList(request);
        }
        return Response.success(expenseVOS);
    }

    /**
     * 费用管理-仓库维度
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.warehouse.vo.ExpenseVO>>
     * @date: 4/7/22 8:13 PM
     */
    @Deprecated
    @ApiOperation(value = "费用管理-仓库+省份", notes = "费用管理-仓库+省份")
    @PostMapping(value = "/queryProvinceExpenseByPage")
    public Response<IPage<ExpenseVO1>> queryProvinceExpenseByPage(@RequestBody ProvinceExpenseRequest request) {
        return Response.success(warehouseService.queryProvinceExpenseByPage(request));
    }

    /**
     * 费用管理导出
     *
     * @param request
     * @return: void
     * @date: 4/7/22 10:42 PM
     */
    @Deprecated
    @ApiOperation(value = "费用管理导出", notes = "费用管理导出")
    @PostMapping(value = "/exportExpense")
    public void exportExpense(@RequestBody ExpenseRequest request) {
        log.info(">>>method:{},\n>>>params:{}","费用管理导出",request);
        warehouseService.exportExpense(request);
    }

    /**
     * 订单异常列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.warehouse.vo.AbnormalOrderVO>>
     * @date: 4/8/22 8:20 PM
     */
    @ApiOperation(value = "订单异常列表", notes = "订单异常列表")
    @GetMapping(value = "/queryAbnormalOrderList")
    public Response<List<AbnormalOrderVO>> queryAbnormalOrderList(AbnormalRequest request) {
        return Response.success(warehouseService.queryAbnormalOrderList(request));
    }


    /**
     * 费用管理-SKU运费率
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List < com.wantwant.sfa.backend.warehouse.vo.ExpenseVO>>
     * @date: 4/7/22 4:43 PM
     */
    @ApiOperation(value = "费用管理-SKU运费率", notes = "费用管理-SKU运费率")
    @GetMapping(value = "/freightRateList")
    public Response<IPage<SkuFreightRateVo>> queryFreightRateList(SkuFreightRateRequest request) {
        return Response.success(warehouseService.queryFreightRateList(request));
    }


    /**
     * 费用管理-SKU运费率
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List < com.wantwant.sfa.backend.warehouse.vo.ExpenseVO>>
     * @date: 4/7/22 4:43 PM
     */
    @ApiOperation(value = "更新供应商合同管理状态值", notes = "更新供应商合同管理状态值")
    @GetMapping(value = "/updateSupplierManagementState")
    public void updateSupplierManagementState() {
         warehouseService.updateSupplierManagementState();
    }

    /**
     * 更新供应商合同管理
     *
     */
    @ApiOperation(value = "更新供应商合同管理", notes = "更新供应商合同管理")
    @PostMapping(value = "/update/supplierManagementList")
    public Response<Integer> updateSupplierManagementList(@Validated @RequestBody SupplierManagementUpdateRequest request) {
        return Response.success(warehouseService.updateSupplierManagementList(request));
    }

    /**
     * 根据供应商代码查出供应商名称
     * @return
     */
    @ApiOperation(value = "根据供应商代码查出供应商名称", notes = "根据供应商代码查出供应商名称")
    @GetMapping(value = "/getSupplierName")
    public Response<String> getSupplierName(@ApiParam(name = "供应商代码", value = "supplierCode") @RequestParam(value = "supplierCode",required = true) String supplierCode) {
        String deliveryCompany = warehouseService.getSupplierNameBySupplierCode(supplierCode);
        return Response.success(deliveryCompany);
    }


    /**
     * 供应商合同管理列表
     *
     */
    @ApiOperation(value = "供应商合同管理", notes = "供应商合同管理")
    @PostMapping(value = "/querySupplierManagementList")
    public Response<IPage<SupplierManagementVo>> querySupplierManagementList(@RequestBody @Validated SupplierManagementRequest request) {
        return Response.success(warehouseService.querySupplierManagementList(request));
    }

    /**
     * 导出供应商合同管理列表
     */
    @ApiOperation(value = "导出供应商合同管理列表", notes = "导出供应商合同管理列表")
    @PostMapping(value = "/export/querySupplierManagementList")
    public void exportQuerySupplierManagementList(@RequestBody @Validated SupplierManagementRequest request) {
         warehouseService.exportQuerySupplierManagementList(request);
    }

    /**
     * 运输价格管理对应公司名称
     *
     */
    @ApiOperation(value = "运输价格管理对应公司名称", notes = "运输价格管理对应公司名称")
    @PostMapping(value = "/queryTransportPriceCompanyName")
    public Response<List<String>> queryTransportPriceCompanyName() {
        return Response.success(warehouseService.queryTransportPriceCompanyName());
    }

    /**
     * 运输价格管理对应公司名称
     *
     */
    @ApiOperation(value = "模糊查询四级地地址", notes = "模糊查询四级地地址")
    @GetMapping(value = "/queryFourLevel")
    public Response<List<String>> queryFourLevel(@RequestParam String name) {
        return Response.success(warehouseService.queryFourLevel(name));
    }

    /**
     * 运输价格管理 快运
     *
     */
    @ApiOperation(value = "快运价格", notes = "快运价格")
    @PostMapping(value = "/queryTransportPriceManagementList")
    public Response<IPage<TransportPriceVo>> queryTransportPriceManagementList(@RequestBody TransportPriceRequest request) {
        return Response.success(warehouseService.queryTransportPriceManagementList(request));
    }

    /**
     * 运输价格管理导出 快运
     *
     */
    @ApiOperation(value = "快运价格-导出", notes = "快运价格-导出")
    @PostMapping(value = "/export/queryTransportPriceManagementList")
    public void exportQueryTransportPriceManagementList(@RequestBody TransportPriceRequest request) {
         warehouseService.exportTransportPriceManagementList(request);
    }

    /**
     * 新增运输价格管理
     *
     */
    @ApiOperation(value = "新增运输价格管理", notes = "新增运输价格管理")
    @PostMapping(value = "/add/transportPriceManagement")
    public Response<Integer> addTransportPriceManagement(@RequestBody @Validated TransportPriceUpdateRequest request) {
        return Response.success(warehouseService.addTransportPriceManagement(request));
    }

    /**
     * 固定成本列表
     *
     */
    @ApiOperation(value = "固定成本列表", notes = "固定成本列表")
    @PostMapping(value = "/queryFixedCostsList")
    public Response<IPage<FixedCosts>> queryFixedCostsList(@RequestBody @Validated FixedCostsRequest request) {
        return Response.success(warehouseService.queryFixedCostsList(request));
    }

    /**
     * 固定成本导出
     *
     */
    @ApiOperation(value = "固定成本导出", notes = "固定成本导出")
    @PostMapping(value = "/export/queryFixedCostsList")
    public void exportQueryFixedCostsList(@RequestBody @Validated FixedCostsRequest request) {
        warehouseService.exportQueryFixedCostsList(request);
    }

    /**
     * 固定成本继承
     *
     */
    @ApiOperation(value = "固定成本继承", notes = "固定成本继承")
    @PostMapping(value = "/inheritanceFixedCost")
    public void inheritanceFixedCost() {
        warehouseService.inheritanceFixedCost();
    }


    /**
     * 仓储导入/财务审核列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.warehouse.vo.ImportVO>>
     * @date: 6/22/22 5:18 PM
     */
    @ApiOperation(value = "仓储导入/财务审核列表", notes = "仓储导入/财务审核列表")
    @GetMapping(value = "/queryImportPage")
    public Response<IPage<ImportVO>> queryImportPage(ImportRequest request) {
        return Response.success(warehouseService.queryImportPage(request));
    }

    @ApiOperation(value = "删除导入", notes = "删除导入")
    @PostMapping(value = "/delete/{id}")
    public Response deleteImportPage(@PathVariable Integer id) {
        warehouseService.deleteImportWareHouseData(id);
        return Response.success();
    }


    /**
     * 仓储导入-固定成本模版下载
     *
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
     * @date: 6/23/22 10:28 PM
     */
    @ApiOperation(notes = "仓储导入-固定成本模版下载", value = "仓储导入-固定成本模版下载")
    @GetMapping("/fixedTemplate")
    public void fixedCostTemplate(HttpServletRequest request, HttpServletResponse response) {
        warehouseService.fixedCostTemplate(request, response);
    }

    /**
     * 仓储导入-运输价格模版下载
     *
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
     * @date: 6/23/22 10:28 PM
     */
    @ApiOperation(notes = "仓储导入-运输价格模版下载", value = "仓储导入-运输价格模版下载")
    @GetMapping("/transportTemplate")
    public void transportTemplate(HttpServletRequest request, HttpServletResponse response) {
        warehouseService.transportTemplate(request, response);
    }

    /**
     * 仓储导入-固定成本导入
     *
     * @param file
     * @param empId
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
     * @date: 6/23/22 10:28 PM
     */
    @ApiOperation(notes = "仓储导入-固定成本导入", value = "仓储导入-固定成本导入")
    @PostMapping("/importFixedCosts")
    public Response<ImportMsgVO> importFixedCosts(@RequestParam(value = "file") MultipartFile file,
                                                  @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "empId") String empId) {
        return warehouseService.importFixedCosts(file, empId);
    }

    /**
     * 仓储导入-运输价格导入
     *
     * @param file
     * @param empId
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO>
     * @date: 6/23/22 10:28 PM
     */
    @ApiOperation(notes = "仓储导入-运输价格导入和账单费用", value = "仓储导入-运输价格导入和账单费用")
    @PostMapping("/importTransport")
    public Response<ImportMsgVO> importTransport(@RequestParam(value = "file") MultipartFile file,
                                                 @ApiParam(value = "2:快运 3:零担 4:快递 5:账单运费", required = true) @RequestParam(value = "type") int type,
                                                 @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "empId") String empId) {
        if(type < 2 || type > 5) {
            throw new ApplicationException("导入类型传入不正确");
        }
        return warehouseService.importTransport(file, type,empId);
    }

    /**
     * 仓储导入-固定成本明细页
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.warehouse.vo.FixedCostVO>>
     * @date: 6/24/22 11:37 PM
     */
    @ApiOperation(notes = "仓储导入-固定成本明细页", value = "仓储导入-固定成本明细页")
    @GetMapping("/queryFixedDetailPage")
    public Response<IPage<FixedCostExportVO>> queryFixedDetailPage(QueryImportRequest request){
        return Response.success(warehouseService.queryFixedDetailPage(request));
    }

    /**
     * 仓储导入-固定成本明细页导出
     *
     * @param query
     * @return: void
     * @date: 6/26/22 1:02 PM
     */
    @ApiOperation(value = "仓储导入-固定成本明细页导出", notes = "仓储导入-固定成本明细页导出")
    @GetMapping(value = "/fixedDetailExport")
    public void fixedDetailExport(QueryImportRequest query) {
        warehouseService.fixedDetailExport(query);
    }

    @ApiOperation(value = "仓储导入-账单运费明细页导出", notes = "仓储导入-账单运费明细页导出")
    @GetMapping(value = "/transportCostExport")
    public void transportCostExport(QueryImportRequest query) {
        warehouseService.transportCostExport(query);
    }

    /**
     * 仓储导入-快运运输明细页
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.warehouse.vo.TransportVO>>
     * @date: 6/25/22 10:52 PM
     */
    @ApiOperation(notes = "仓储导入-快运运输价格明细页", value = "仓储导入-快运运输价格明细页")
    @GetMapping("/queryTransportDetailPage")
    public Response<IPage<TransportVO>> queryTransportDetailPage(QueryImportRequest request){
        return Response.success(warehouseService.queryTransportDetailPage(request));
    }


    @ApiOperation(notes = "仓储导入-零担运输价格明细页", value = "仓储导入-运输价格明细页")
    @GetMapping("/queryTransportStyle1DetailPage")
    public Response<IPage<TransportStyle1VO>> queryTransportStyle1DetailPage(QueryImportRequest request){
        return Response.success(warehouseService.queryTransportStyle1DetailPage(request));
    }

    @ApiOperation(notes = "仓储导入-快递运输价格明细页", value = "仓储导入-零担运输价格明细页")
    @GetMapping("/queryTransportStyle2DetailPage")
    public Response<IPage<TransportStyle2VO>> queryTransportStyle2DetailPage(QueryImportRequest request){
        return Response.success(warehouseService.queryTransportStyle2DetailPage(request));
    }

    @ApiOperation(notes = "仓储导入-账单运费价格明细页", value = "仓储导入-账单运费价格明细页")
    @GetMapping("/queryTransportCostDetailPage")
    public Response<IPage<TransportCostVO>> queryTransportStyleDetailPage(QueryImportRequest request){
        return Response.success(warehouseService.queryTransportCostDetailPage(request));
    }

    /**
     * 仓储导入-删除附件
     *
     * @param id
     * @return: com.wantwant.commons.web.response.Response
     * @date: 6/25/22 4:27 PM
     */
    @ApiOperation(value = "仓储导入-删除附件", notes = "仓储导入-删除附件")
    @DeleteMapping("/deleteFile/{id}")
    public Response deleteFile(@ApiParam(value = "附件id", required = true) @PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
        return warehouseService.deleteFile(id);
    }

    /**
     * 仓储导入-明细页提交
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response
     * @date: 6/25/22 11:30 PM
     */
    @ApiOperation(notes = "仓储导入-明细页提交", value = "仓储导入-明细页提交")
    @PostMapping("/submit")
    public Response submit(@Valid @RequestBody SubmitRequest request) {
        return Response.success(warehouseService.submit(request));
    }

    /**
     * 财务审批-固定成本审批
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Boolean>
     * @date: 3/24/22 9:29 PM
     */
    @ApiOperation(notes = "财务审批-固定成本审批", value = "财务审批-固定成本审批")
    @PutMapping("/transportAudit")
    public Response<Integer> transportAudit(@Valid @RequestBody TransportAuditRequest request) {
        log.info("transportAudit request:{}",request);
        if (!redisUtil.setLockIfAbsent(WAREHOUSE_AUDIT_LOCK, "", 120, TimeUnit.SECONDS)) {
            throw new ApplicationException("处理时间较长，请勿重新提交");
        }
        Integer result = null;
        try{
            result = warehouseService.transportAudit(request);
        }finally {
            redisUtil.unLock(WAREHOUSE_AUDIT_LOCK,"");
        }
        return Response.success(result);
    }


    @ApiOperation(value = "异常率-导出", notes = "异常率-导出")
    @PostMapping(value = "/export/exportAbnormalOrderList")
    public void exportAbnormalOrderList(@RequestBody AbnormalRequest request) {
        warehouseService.exportQueryAbnormalOrderList(request);
    }

    @ApiOperation(value = "仓库监控看板", notes = "仓库监控看板")
    @PostMapping(value = "/queryWarehouseMonitoring")
    public Response<List<WarehouseMonitoringVO>> queryWarehouseMonitoring(@RequestBody MonitoringRequest request) {
        return  Response.success(warehouseService.queryWarehouseMonitoring(request));
    }

    @ApiOperation(value = "运输监控看板", notes = "运输监控看板")
    @PostMapping(value = "/queryTransportMonitoring")
    public Response<List<TransportMonitoringVO>> queryTransportMonitoring(@RequestBody MonitoringRequest request) {
        return  Response.success(warehouseService.queryTransportMonitoring(request));
    }

    @ApiOperation(value = "查询承运商", notes = "查询承运商")
    @GetMapping(value = "/queryCarrierName")
    public Response<List<String>> queryCarrierName(@RequestParam String yearMon) {
        return  Response.success(warehouseService.queryCarrierName(yearMon));
    }


    @ApiOperation(value = "仓库监控-导出", notes = "仓库监控-导出")
    @PostMapping(value = "/export/WarehouseMonitoring")
    public void exportWarehouseMonitoring(@RequestBody WarehouseExportRequest request) {
        warehouseService.exportWarehouseMonitoring(request);
    }

    @ApiOperation(value = "运输监控-导出", notes = "运输监控-导出")
    @PostMapping(value = "/export/TransportMonitoring")
    public void exportTransportMonitoring(@RequestBody TransportExportRequest request) {
        warehouseService.exportTransportMonitoring(request);
    }

    @ApiOperation(value = "公路运输价格", notes = "公路运输价格")
    @PostMapping(value = "/queryTransportPriceManagementVO1List")
    public Response<IPage<TransportStyle1VO>> queryTransportPriceManagementListVO1(@RequestBody TransportPriceRequest request) {
        return Response.success(warehouseService.queryTransportPriceManagementListVO1(request));
    }

    @ApiOperation(value = "公路运输价格-导出", notes = "公路运输价格-导出")
    @PostMapping(value = "/export/queryTransportPriceManagementVO1List")
    public void exportTransportPriceManagementListVO1(@RequestBody TransportPriceRequest request) {
        warehouseService.exportTransportPriceManagementListVO1(request);
    }

    @ApiOperation(value = "快递价格", notes = "快递价格")
    @PostMapping(value = "/queryTransportPriceManagementVO2List")
    public Response<IPage<TransportStyle2VO>> queryTransportPriceManagementListVO2(@RequestBody TransportPriceRequest request) {
        return Response.success(warehouseService.queryTransportPriceManagementListVO2(request));
    }

    @ApiOperation(value = "快递价格-导出", notes = "快递价格-导出")
    @PostMapping(value = "/export/queryTransportPriceManagementVO2List")
    public void exportTransportPriceManagementListVO2(@RequestBody TransportPriceRequest request) {
       warehouseService.exportTransportPriceManagementListVO2(request);
    }


    @ApiOperation(value = "仓库名称", notes = "仓库名称")
    @GetMapping(value = "/queryNameList")
    public Response<List<WarehouseListVO>> queryNameList() {
        return  Response.success(warehouseService.queryNameList());
    }


   /* @ApiOperation(value = "供应商合同代码", notes = "供应商合同代码")
    @GetMapping(value = "/supplierMessageTask")
    public void SupplierMessageTask() {
       // return  Response.success(warehouseService.queryNameList());
        supplierMessageTask.configureTasks();

    }*/

}
