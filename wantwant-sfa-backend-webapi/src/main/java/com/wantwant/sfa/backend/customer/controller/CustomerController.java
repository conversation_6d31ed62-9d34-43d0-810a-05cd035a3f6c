package com.wantwant.sfa.backend.customer.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.api.CustomerApi;
import com.wantwant.sfa.backend.customer.request.*;
import com.wantwant.sfa.backend.customer.vo.*;
import com.wantwant.sfa.backend.service.CustomerManagementService;
import com.wantwant.sfa.backend.service.ICustomerProcessService;
import com.wantwant.sfa.backend.service.ICustomerService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.TimeUnit;

/** @Description 测试 <AUTHOR> @Date 2020/3/23 */
@RestController
@Slf4j
public class CustomerController implements CustomerApi {

  @Autowired private ICustomerService customerService;

  @Autowired private ICustomerProcessService customerProcessService;

  @Autowired private CustomerManagementService customerManagementService;

  @Autowired private RedisUtil redisUtil;

  public static final String LOCK_HEAD_PLAN_ADD = "addPlanLock";
  public static final String LOCK_HEAD_PLAN_CANCEL = "cancelPlanLock";
  public static final String LOCK_HEAD_PROCESS_VERIFIE = "processVerifieLock";

  /** 路线规划客户搜索 */
  @Override
  public Response<PlanCustomerListResponse> planCustomerList(PalnCustomerListRequest request) {
    log.info("start CustomerController planCustomerList request:{}", request);

    return Response.success(customerService.planCustomerList(request));
  }

  /** 设置拜访计划 */
  @Override
  public Response addMoreVisit(addMoreVisitRequest request) {
    String person = request.getPerson();
    log.info("start CustomerController addMoreVisit request:{},person:{}", request, person);

    LocalDate visitDate = LocalDate.parse(request.getVisitDate());

    if (visitDate.isBefore(LocalDate.now())) {
      throw new ApplicationException("不能设置之前的日期");
    } else if (request.getCustomerIdList().size() == 0) {
      throw new ApplicationException("未添加客户");
    } else if (StringUtils.isBlank(request.getEmployeeId())) {
      throw new ApplicationException("未添加业务员工号");
    }

    if (!redisUtil.setLockIfAbsent(LOCK_HEAD_PLAN_ADD, person, 5, TimeUnit.SECONDS)) {
      return Response.error("请求正在处理中！～");
    }

    try {

      customerService.addMoreVisit(request, person);

      return Response.success();

    } finally {
      redisUtil.unLock(LOCK_HEAD_PLAN_ADD, person);
    }
  }

  @Override
  public Response cancelMoreVisit(cancelMoreVisitRequest request) {
    String person = request.getPerson();
    log.info(
        "start CustomerController cancelMoreVisitRequest request:{},person:{}", request, person);

    LocalDate visitDate = LocalDate.parse(request.getVisitDate());
    String customerId = request.getCustomerId();

    if (visitDate.isBefore(LocalDate.now())) {
      throw new ApplicationException("不能取消之前的日期");
    } else if (StringUtils.isBlank(customerId)) {
      throw new ApplicationException("未添加客户");
    }

    if (!redisUtil.setLockIfAbsent(LOCK_HEAD_PLAN_CANCEL, customerId, 5, TimeUnit.SECONDS)) {
      return Response.error("请求正在处理中！～");
    }

    try {

      customerService.cancelMoreVisit(request, person);

      return Response.success();

    } finally {
      redisUtil.unLock(LOCK_HEAD_PLAN_CANCEL, customerId);
    }
  }

  @Override
  public Response<Page<ProcessListVo>> processList(ProcessListRequest request) {
    log.info(
        "start CustomerController processList request:{},person:{}", request, request.getPerson());
    Page<ProcessListVo> page = customerProcessService.processList(request);
    return Response.success(page);
  }

  @Override
  public Response<ProcessInfoVo> processInfo(ProcessInfoRequest request) {
    log.info(
        "start CustomerController processInfo request:{},person:{}", request, request.getPerson());
    ProcessInfoVo info = customerProcessService.processInfo(request);
    return Response.success(info);
  }

  @Override
  public Response processDismissed(ProcessVerifieRequest request) {
    log.info("start CustomerController processDismissed request:{}", request);
    String approvaId = String.valueOf(request.getApprovaId());

    if (!redisUtil.setLockIfAbsent(LOCK_HEAD_PROCESS_VERIFIE, approvaId, 5, TimeUnit.SECONDS)) {
      return Response.error("请求正在处理中！～");
    }

    try {
      customerProcessService.processDismissed(request);
      return Response.success();
    } finally {
      redisUtil.unLock(LOCK_HEAD_PROCESS_VERIFIE, approvaId);
    }
  }

  @Override
  public Response processApproved(ProcessVerifieRequest request) {
    log.info("start CustomerController processApproved request:{}", request);
    String approvaId = String.valueOf(request.getApprovaId());

    if (!redisUtil.setLockIfAbsent(LOCK_HEAD_PROCESS_VERIFIE, approvaId, 5, TimeUnit.SECONDS)) {
      return Response.error("请求正在处理中！～");
    }

    try {
      customerProcessService.processApproved(request);
      return Response.success();
    } finally {
      redisUtil.unLock(LOCK_HEAD_PROCESS_VERIFIE, approvaId);
    }
  }

  @Override
  public Response<Page<CustomerManagementVo>> getCustomerManagementList(
      CustomerManagementRequest request) {
    Page<CustomerManagementVo> customerManagementList =
        customerManagementService.getCustomerManagementList(request);
    return Response.success(customerManagementList);
  }

  @Override
  public Response<CustomerDetailsVo> getCustomerManagementDetails(CustomerDetailsRequest request) {
    CustomerDetailsVo customerManagementOrderVo =
        customerManagementService.getCustomerManagementOrderVo(request);
    return Response.success(customerManagementOrderVo);
  }

  @Override
  public Response<CustomerVisitVo> getVisitInfo(CustomerVisitInfoRequest request) {
    return Response.success(customerService.getVisitInfo(request));
  }
}
