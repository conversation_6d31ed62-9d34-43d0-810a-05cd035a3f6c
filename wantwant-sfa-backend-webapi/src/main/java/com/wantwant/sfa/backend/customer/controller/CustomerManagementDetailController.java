package com.wantwant.sfa.backend.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.dto.PerformanceComparisonDTO;
import com.wantwant.sfa.backend.customer.dto.ProductAnalysisDTO;
import com.wantwant.sfa.backend.customer.dto.ProductPerformanceResult;
import com.wantwant.sfa.backend.customer.request.*;
import com.wantwant.sfa.backend.customer.vo.CheckCustomerVO;
import com.wantwant.sfa.backend.customer.vo.TransferCustomerVO;
import com.wantwant.sfa.backend.service.ApplyForCustomerService;
import com.wantwant.sfa.backend.service.AuditCertificationService;
import com.wantwant.sfa.backend.service.CustomerManagementDetailService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.vo.*;
import com.wantwant.sfa.backend.zw.vo.PartnerInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date on 2022/1/18
 */
@Slf4j
@RestController
@Api(tags = "客户管理API")
public class CustomerManagementDetailController {

    private static final String CUSTOMER_TRANSFER_APPLY_LOCK = "customer transfer apply:lock";
    private static final String CUSTOMER_TRANSFER_AUDIT_LOCK = "customer transfer audit:lock";

    @Resource
    private CustomerManagementDetailService customerManagementDetailService;
    @Resource
    private ApplyForCustomerService applyForCustomerService;
    @Resource
    private AuditCertificationService auditCertificationService;

    @Autowired
    private RedisUtil redisUtil;


    @ApiOperation(value = "会员管理列表查询", notes = "会员管理列表查询", httpMethod = "POST")
    @PostMapping("/customerManage/queryMemberList")
    Response<Page<CustomerManagementDetailVO>> queryMemberList(@RequestBody CustomerManagementDetailRequest request) {
        return Response.success(customerManagementDetailService.queryCustomerManagementList(request));
    }

    @ApiOperation(value = "列表导出", notes = "列表导出")
    @PostMapping(value = "/customerManage/export")
    public void exportList(@RequestBody CustomerManagementDetailRequest req, HttpServletResponse response, HttpServletRequest request) {
        customerManagementDetailService.exportList(req, request, response);
    }

    @ApiOperation(value = "列表导出-回访导出", notes = "列表导出-回访导出")
    @PostMapping(value = "/customerManage/exportAttachCallBackInfo")
    public void exportListAttachCallBackInfo(@RequestBody CustomerManagementDetailRequest req, HttpServletResponse response, HttpServletRequest request) {
        customerManagementDetailService.exportAttachCallBackInfo(req, request, response);
    }

    @ApiOperation(value = "客户订单列表查询", notes = "客户订单列表查询", httpMethod = "POST")
    @PostMapping("/customerManage/queryOrderList")
    Response<Page<CustomerPartnerOrderVO>> queryOrderList(@RequestBody CustomerPartnerOrderRequest request) {
        return Response.success(customerManagementDetailService.queryCustomerOrderList(request));
    }

    @ApiOperation(value = "客户订单列表查询", notes = "客户订单列表查询", httpMethod = "POST")
    @PostMapping("/customerManage/queryOrderList/export")
    public void queryOrderListExport(@RequestBody CustomerPartnerOrderRequest request) {
        customerManagementDetailService.queryOrderListExport(request);
    }


    @ApiOperation(value = "合伙人负责区域和产品组信息", notes = "合伙人负责的行政区域", httpMethod = "GET")
    @GetMapping("/customerManage/partner/info")
    Response<PartnerInfoVo> getRegionList(@RequestParam("partnerMemberKey") String partnerMemberKey) {
        log.info("getRegionList: {}", partnerMemberKey);
        if (CommonUtil.StringUtils.isEmpty(partnerMemberKey)) {
            throw new ApplicationException("合伙人id输入错误！");
        }
        return Response.success(customerManagementDetailService.getPartnerInfoByMemberKey(partnerMemberKey));
    }

    @ApiOperation(value = "转移后负责人信息", notes = "转移后负责人信息", httpMethod = "GET")
    @GetMapping("/customerManage/TransferPartnerInfo")
    Response<List<PartnerInfoVo>> getTransferPartnerInfo(@RequestParam("partnerInfo") String partnerInfo, @RequestParam("customerId") String customerId) {
        log.info("TransferPartnerInfo partnerInfo : {} customerId:{}", partnerInfo, customerId);
        if (CommonUtil.StringUtils.isEmpty(partnerInfo) || CommonUtil.StringUtils.isEmpty(customerId)) {
            throw new ApplicationException("负责人或客户信息错误！");
        }
        return Response.success(customerManagementDetailService.getTransferPartnerInfo(partnerInfo, customerId));
    }

    @ApiOperation(value = "客户能够转移", notes = "客户是否能转移", httpMethod = "GET")
    @GetMapping("/customerManage/canTransfer")
    Response<String> customerCanTransfer(@RequestParam("customerId") String customerId) {
        log.info("customerCanTransfer: {}", customerId);
        if (CommonUtil.StringUtils.isEmpty(customerId)) {
            throw new ApplicationException("客户id为空");
        }
        return Response.success(customerManagementDetailService.customerCanTransfer(customerId));
    }

    @ApiOperation(value = "客户转移接口申请", notes = "客户转移接口申请", httpMethod = "POST")
    @PostMapping("/customerManage/customer/transfer/apply")
    Response transferCustomer(@Validated @RequestBody CustomerTransferRequest request) {
        log.info("transferCustomer: {}", request);
        if (!redisUtil.setLockIfAbsent(CUSTOMER_TRANSFER_APPLY_LOCK, request.getMemberKey(), 5, TimeUnit.SECONDS)) {
            return Response.error("当前正在处理中");
        }
        try {
            customerManagementDetailService.transferCustomerApply(request);
        } finally {
            redisUtil.unLock(CUSTOMER_TRANSFER_AUDIT_LOCK, request.getMemberKey());
        }
        return Response.success();
    }

    @ApiOperation(value = "客户转移接口审批", notes = "客户转移接口审批", httpMethod = "POST")
    @PostMapping("/customerManage/customer/transfer/audit")
    Response transferCustomerAudit(@Validated @RequestBody CustomerTransferAuditRequest request) {
        log.info("transferCustomerAudit: {}", request);
        if (request.getResult() != 1 && request.getResult() != 2) {
            throw new ApplicationException("传入审批状态不正确");
        }
        if (!redisUtil.setLockIfAbsent(CUSTOMER_TRANSFER_AUDIT_LOCK, request.getRequestId().toString(), 5, TimeUnit.SECONDS)) {
            return Response.error("当前正在处理中");
        }

        try {
            customerManagementDetailService.transferCustomerAudit(request);
        } finally {
            redisUtil.unLock(CUSTOMER_TRANSFER_AUDIT_LOCK, request.getRequestId().toString());
        }
        return Response.success();
    }


    @ApiOperation(value = "客户转移列表", notes = "客户转移申请列表", httpMethod = "POST")
    @PostMapping("/customerManage/customer/queryTransferList")
    Response<IPage<TransferCustomerVO>> queryTransferList(@RequestBody ApplyForListCustomerRequest request) {
        log.info("queryTransferList: {}", request);
        return Response.success(applyForCustomerService.queryTransferList(request));
    }

    @ApiOperation(value = "客户审核记录", notes = "客户审核记录", httpMethod = "POST")
    @PostMapping("/customerManage/customer/queryAuditList")
    Response<List<CheckCustomerVO>> queryAuditList(@RequestBody Map<String, String> map) {
        log.info("requestId: {}", map.get("requestId"));
        return Response.success(applyForCustomerService.queryAuditList(map.get("requestId")));
    }

    @ApiOperation(value = "客户稽核认证详情", notes = "客户稽核认证详情", httpMethod = "POST")
    @PostMapping("/customerManage/canAuditCertification")
    Response<CertificationVO> queryAuditCertification(@RequestBody AuditCertificationRequest request) {
        if (CommonUtil.StringUtils.isEmpty(request.getCustomerId()) || CommonUtil.StringUtils.isEmpty(request.getPerson())) {
            throw new ApplicationException("客户id为空或者登录id为空");
        }
        CertificationVO certificationVO = auditCertificationService.queryAuditCertification(request);
        return Response.success(certificationVO);
    }

    @ApiOperation(value = "客户稽核认证", notes = "客户稽核认证", httpMethod = "POST")
    @PostMapping("/customerManage/auditCertification")
    Response auditCertification(@RequestBody CertificationRequest request) {
        auditCertificationService.auditCertification(request);
        return Response.success();
    }


    @ApiOperation(value = "获取客户类型详情", notes = "获取客户类型详情", httpMethod = "GET")
    @GetMapping("/customerManage/queryCustomerType")
    Response<List<CustomerTypeVO>> queryCustomerType() {
        return Response.success(customerManagementDetailService.queryCustomerType());
    }


    @ApiOperation(value = "月目标确认接口", notes = "月目标确认接口", httpMethod = "POST")
    @PostMapping("/customerManage/monthTarget")
    Response monthTarget(@RequestBody MonthTargetRequest request) {
        log.info("monthTarget:request{}", request);
        customerManagementDetailService.monthTarget(request);
        return Response.success();
    }

    @ApiOperation(value = "客户基础信息", notes = "客户基础信息", httpMethod = "POST")
    @PostMapping("/customerManage/basicInfo")
    Response<BasicInfoVO> customerManageBasicInfo(@RequestBody CustomerPartnerInfoRequest request) {
        log.info("客户详情:{}", request);
        return Response.success(customerManagementDetailService.customerManageBasicInfo(request));
    }


    @ApiOperation(value = "客户业绩数据", notes = "客户业绩数据", httpMethod = "POST")
    @PostMapping("/customerManage/basicPerformance")
    Response<List<BasicPerformanceVO>> customerManageBasicPerformance(@RequestBody CustomerPartnerInfoRequest request) {
        log.info("客户详情:{}", request);
        return Response.success(customerManagementDetailService.customerManageBasicPerformance(request));
    }

    @ApiOperation(value = "客户业绩数据导出", notes = "客户业绩数据导出", httpMethod = "POST")
    @PostMapping("/customerManage/basicPerformance/export")
    public void customerManageBasicPerformanceExport(@RequestBody CustomerPartnerInfoRequest request) {
        customerManagementDetailService.customerManageBasicPerformanceExport(request);
    }


    @Deprecated
    @ApiOperation(value = "下级数据", notes = "下级数据", httpMethod = "POST")
    @PostMapping("/customerManage/nextCustomers")
    Response<Page<CustomerManagementDetailVO>> queryNextCustomers(@RequestBody CustomerManagementDetailRequest request) {
        log.info("下级数据:{}", request);
        return Response.success(customerManagementDetailService.queryNextCustomers(request));
    }

    @Deprecated
    @ApiOperation(value = "下级数据导出", notes = "下级数据导出", httpMethod = "POST")
    @PostMapping("/customerManage/nextCustomers/export")
    public void queryNextCustomersExport(@RequestBody CustomerManagementDetailRequest request) {
        customerManagementDetailService.queryNextCustomersExport(request);
    }

    @ApiOperation(value = "交易品相", notes = "交易品相", httpMethod = "POST")
    @PostMapping("/customerManage/queryTransaction")
    Response<Page<TransactionArticleVO>> queryTransactionArticle(@RequestBody TransactionArticleRequest request) {
        log.info("交易品相:{}", request);
        return Response.success(customerManagementDetailService.queryTransactionArticle(request));
    }

    @ApiOperation(value = "交易品相导出", notes = "交易品相导出", httpMethod = "POST")
    @PostMapping("/customerManage/queryTransaction/export")
    public void queryTransactionArticleExport(@RequestBody TransactionArticleRequest request) {
        customerManagementDetailService.queryTransactionArticleExport(request);
    }

    @ApiOperation(value = "新增回访记录/跟进记录", notes = "回访记录", httpMethod = "POST")
    @PostMapping("/customerManage/addCallBackInfo")
    public Response addCallBackInfo(@RequestBody @Validated CustomerManagementAddCallBackInfoReq request) {
        log.info("回访记录-新增回访记录/跟进记录:{}", request);
        customerManagementDetailService.addCallBackInfo(request);
        return Response.success();
    }

    @ApiOperation(value = "访问回访列表", notes = "回访记录", httpMethod = "POST")
    @PostMapping("/customerManage/queryCallBackList")
    public Response<List<CustomerManagementQueryCallBackInfoVO>> queryCallBackList(@RequestBody @Validated CustomerManagementQueryCallBackInfoReq request) {
        log.info("回访记录-回访记录列表:{}", request);
        return Response.success(customerManagementDetailService.queryCallBackList(request));
    }

    @ApiOperation(value = "删除回访记录", notes = "回访记录", httpMethod = "POST")
    @PostMapping("/customerManage/deleteCallBackInfo")
    public Response deleteCallBackInfo(@RequestBody @Validated CustomerManagementDeleteCallBackInfoReq request) {
        log.info("回访记录-删除回访记录:{}", request);
        customerManagementDetailService.deleteCallBackInfo(request);
        return Response.success();
    }

    @ApiOperation(value = "获取客户业绩信息", notes = "获取客户业绩信息", httpMethod = "GET")
    @GetMapping("/customerManage/performance/{customerId}/{model}/{yearMonth}")
    public Response<CustomerPerformanceVO> getCustomerPerformance(@PathVariable String customerId,
                                                                  @PathVariable @ApiParam(value = "模式(10:自然月,11:自然季,2:财务年)") Integer model,
                                                                  @PathVariable @ApiParam(value = "月份：财年2024 季度：2024-Q1 月份：2024-01") String yearMonth) {
        log.info("【customer performance】customerId:{} model:[{}] yearMonth:[{}]", customerId,model,yearMonth);

        CustomerPerformanceVO customerPerformanceVO = customerManagementDetailService.getCustomerPerformance(customerId,model,yearMonth);

        return Response.success(customerPerformanceVO);
    }

    @ApiOperation(value = "获取客户业绩趋势", notes = "获取客户业绩趋势", httpMethod = "GET")
    @GetMapping("/customerManage/performanceTrend/{customerId}/{model}/{yearMonth}")
    public Response<PerformanceComparisonDTO> getPerformanceTrend(@PathVariable String customerId,
                                                                  @PathVariable @ApiParam(value = "模式(10:自然月,11:自然季,2:财务年)",required = true) Integer model,
                                                                  @PathVariable @ApiParam(value = "月份：财年2024 季度：2024-Q1 月份：2024-01",required = true) String yearMonth) {
        PerformanceComparisonDTO performanceTrend = customerManagementDetailService.getPerformanceTrend(customerId, model,yearMonth);
        return Response.success(performanceTrend);
    }



    @ApiOperation(value = "获取客户sku/spu/产线占比", notes = "获取客户sku/spu/产线占比", httpMethod = "GET")
    @GetMapping("/customerManage/productProportion")
    public Response<ProductPerformanceResult> getProductPerformance( @ApiParam(value = "商品类型(0:SKU,1:SPU,2:LINE,3:大类)",required = true) @RequestParam(value = "commodityTypeId") Integer commodityTypeId,
                                                                     @ApiParam(value = "时间类型(10:自然月,11:自然季,2:财务年)" ,required = true)@RequestParam(value = "dateTypeId")  Integer dateTypeId,
                                                                     @ApiParam(value = "客户ID",required = true )@RequestParam(value = "customerId")  String customerId,
                                                                     @ApiParam(value = "月份：财年2024 季度：2024-Q1 月份：2024-01",required = true) String yearMonth
    ){
        ProductPerformanceResult productPerformanceResult = customerManagementDetailService.getProductPerformance(commodityTypeId, dateTypeId,customerId,yearMonth);
        return Response.success(productPerformanceResult);
    }

    @ApiOperation(value = "获取sku/spu/产线列表", notes = "获取sku/spu/产线列表", httpMethod = "GET")
    @GetMapping("/customerManage/getProductAnalysis")
    public Response<List<ProductAnalysisDTO>> getProductAnalysis(
             @ApiParam(value = "商品类型(0:SKU,1:SPU,2:LINE,3:大类)",required = true) @RequestParam(value = "commodityTypeId")  Integer commodityTypeId,
             @ApiParam(value = "时间类型(10:自然月,11:自然季,2:财务年)",required = true) @RequestParam(value = "dateTypeId") Integer dateTypeId,
             @ApiParam(value = "月份：财年2024 季度：2024-Q1 月份：2024-01",required = true) String yearMonth,
             @ApiParam(value = "客户ID")@RequestParam(value = "customerId",required = true)   String customerId,
             @ApiParam(value = "查询") @RequestParam(required =false,value = "key") String key
    ) {
        return Response.success(
                customerManagementDetailService.getProductAnalysis(
                        commodityTypeId, dateTypeId,customerId,yearMonth,key));
    }
}

