package com.wantwant.sfa.backend.customerMaintain.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customerMaintain.api.CustomerMaintainApi;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerAuthRequest;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerIdRequest;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerListRequest;
import com.wantwant.sfa.backend.customerMaintain.request.CustomerToPositionRequest;
import com.wantwant.sfa.backend.customerMaintain.request.SearchPositionListRequest;
import com.wantwant.sfa.backend.customerMaintain.request.VerifieRequest;
import com.wantwant.sfa.backend.customerMaintain.request.searchNameRequest;
import com.wantwant.sfa.backend.customerMaintain.vo.CustomerExport;
import com.wantwant.sfa.backend.customerMaintain.vo.CustomerListResponse;
import com.wantwant.sfa.backend.customerMaintain.vo.EmployeeInfoResponse;
import com.wantwant.sfa.backend.customerMaintain.vo.PositionListResponse;
import com.wantwant.sfa.backend.customerMaintain.vo.PositionVo;
import com.wantwant.sfa.backend.customerMaintain.vo.VerifieInfoResponse;
import com.wantwant.sfa.backend.service.ICustomerService;
import com.wantwant.sfa.backend.util.RedisUtil;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import lombok.extern.slf4j.Slf4j;


/**
 * @Description 客户管理
 * <AUTHOR>
 * @Date 2020/3/23
 **/
@RestController
@Slf4j
public class CustomerMaintainController implements CustomerMaintainApi {

    @Autowired
    private ICustomerService customerService;

    @Autowired
    private RedisUtil redisUtil;

    
    public static final String LOCK_HEAD_FROZEN = "frozenLock";
    public static final String LOCK_HEAD_VERIFIE = "verifieLock";
    public static final String LOCK_HEAD_CUSTOMERTOPOSITION = "customerToPosition";


    /**
     * 客户审核列表
     */
	@Override
	public Response<CustomerListResponse> customerList(CustomerListRequest request) {
		log.info("start CustomerMaintainController CustomerList request:{}",request);
		request.setChannel(1);
		return Response.success(customerService.customerList(request));
	}

	/**
	 * 业务上级
	 */
	@Override
	public Response<EmployeeInfoResponse> employeeInfo(CustomerIdRequest request) {
		log.info("start CustomerMaintainController EmployeeInfo request:{}",request);
		
		return Response.success(customerService.employeeInfoByCustomerId(request.getCustomerId()));
	}

	/**
	 * 审核详情
	 */
	@Override
	public Response<VerifieInfoResponse> verifieInfo(CustomerIdRequest request) {
		log.info("start CustomerMaintainController VerifieInfo request:{}",request);

		return Response.success(customerService.verifieInfo(request.getCustomerId()));
	}

	/**
	 * 审核、驳回
	 */
	@Override
	public Response verifie(VerifieRequest request) {
		log.info("start CustomerMaintainController verifie request:{}",request);
		//TODO
		String employeeId = request.getPerson();

		String customerId = request.getCustomerId();
		
        if(!redisUtil.setLockIfAbsent(LOCK_HEAD_VERIFIE,customerId,60, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

		try {

			if(request.getIsVerified() == null || request.getCustomerId() == null || (request.getIsVerified() != 1 && request.getIsVerified() !=2) ) {
				return Response.error(0, "参数错误");
			}else {
				if( request.getIsVerified() == 1 ) {
					//审核
					customerService.toApproved(request, employeeId);
				}else if( request.getIsVerified() == 2 ){
					//驳回
					Map<String, Object> param = new  HashMap<String, Object>();
					param.put("customerId", request.getCustomerId());
					param.put("key", request.getVerifieId());
					param.put("employeeID", employeeId);
					param.put("closeSuggestions", request.getSuggestions());
					param.put("posID", request.getPositionId());

					customerService.dismissedCustomer(param, employeeId);
					
				}
							
			}
			
			return Response.success();
			
		} finally {
			redisUtil.unLock(LOCK_HEAD_VERIFIE,customerId);
		}
	}

	/**
	 * 岗位搜索
	 */
	@Override
	public Response<PositionListResponse> searchPosition(searchNameRequest request) {
		log.info("start CustomerMaintainController SearchPosition request:{}",request);
		
		return Response.success(customerService.searchPosition(request.getSearchName() ));
	}

	/**
	 * 客户归属变更
	 */
	@Override
	public Response customerToPosition(CustomerToPositionRequest request) {
		log.info("start CustomerMaintainController CustomerToPosition request:{}",request);
		//TODO
		String person = request.getPerson();
		String customerId = request.getCustomerId();
		
        if(!redisUtil.setLockIfAbsent(LOCK_HEAD_CUSTOMERTOPOSITION,customerId,5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
        	customerService.customerToPosition(request,person);
    		return Response.success();
        	
        }catch (Exception e) {
        	return Response.error(1,e.getMessage());
		}finally {
			redisUtil.unLock(LOCK_HEAD_VERIFIE,customerId);
		}
        
	}

	/**
	 * 历史归属
	 */
	@Override
	public Response<PositionVo> customerToPositionLog(CustomerIdRequest request) {
		log.info("start CustomerMaintainController CustomerToPositionLog request:{}",request);
		
		if(StringUtils.isBlank(request.getCustomerId())) {
			throw new ApplicationException("客户编号不能为空");
		}
		PositionVo position = customerService.customerToPositionLog(request.getCustomerId());
		return Response.success(position);
	}

	/**
	 * 审核列表导出
	 */
	@Override
	public void export(CustomerListRequest request) {
		log.info("start CustomerMaintainController export request:{}",request);
		 ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
		            .getRequestAttributes();
		 HttpServletResponse response = servletRequestAttributes.getResponse();		 
		 request.setChannel(1);
		 List<CustomerExport> record = customerService.exportExcel(request);
		 String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

		  String name = "审核列表"+sheetName;
		  
	      Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null,sheetName),CustomerExport.class, record);
	        
	      response.setContentType("application/vnd.ms-excel");
	        
	      try {
	            response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder
	                .encode(name+".xlsx"));
	            OutputStream outputStream = response.getOutputStream();
	            workbook.write(outputStream);
	            outputStream.flush();
	            outputStream.close();
	        } catch (IOException e) {
	            response.setStatus(500);
	        }

	}

	/**
	 * 查询岗位
	 */
	@Override
	public Response<PositionListResponse> searchPositionTree() {
		log.info("start CustomerMaintainController SearchPositionTree");
		
		return Response.success(customerService.searchPositionTree(null));
	}

	@Override
	public Response<PositionListResponse> searchPositionList(SearchPositionListRequest request) {
		return Response.success(customerService.searchPositionTree(request.getPerson()));
	}

	@Override
	public Response importAuth(MultipartHttpServletRequest request) {
		log.info("start CustomerMaintainController importAuth");
		
		ImportParams params = new ImportParams();
		Map<String, MultipartFile> fileMap = request.getFileMap();
		
		try {
			
			for(Map.Entry<String,MultipartFile> entry :fileMap.entrySet()) {
				List<CustomerAuthRequest> res =  ExcelImportUtil.importExcel(entry.getValue().getInputStream(), CustomerAuthRequest.class, params);
				customerService.authImport(res);
			}

			return Response.success();
		}catch (Exception e) {
			 log.error("导入失败",e);
	         return Response.error("导入失败");
		}

	}


}
