package com.wantwant.sfa.backend.daily.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.daily.api.DailyApi;
import com.wantwant.sfa.backend.daily.request.DailyEmployeeRequest;
import com.wantwant.sfa.backend.daily.request.DailyInventoryRequest;
import com.wantwant.sfa.backend.daily.request.DailyNewCustomerPageRequest;
import com.wantwant.sfa.backend.daily.request.DailyOrderPageRequest;
import com.wantwant.sfa.backend.daily.request.DailyPerformancePageRequest;
import com.wantwant.sfa.backend.daily.request.DailyPerformanceRequest;
import com.wantwant.sfa.backend.daily.request.DailyPopupRequest;
import com.wantwant.sfa.backend.daily.request.DailySkuPageRequest;
import com.wantwant.sfa.backend.daily.service.DailyNewService;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeInfoVo;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeVo;
import com.wantwant.sfa.backend.daily.vo.DailyInventoryCrossVo;
import com.wantwant.sfa.backend.daily.vo.DailyOrderVo;
import com.wantwant.sfa.backend.daily.vo.DailyPerformanceVo;
import com.wantwant.sfa.backend.daily.vo.DailyPopupVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuCrossVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
public class DailyController implements DailyApi {

    @Autowired
    private DailyNewService dailyNewService;
    @Resource
    private RabbitMQSender rabbitMQSender;

    @ApiOperation(value = "日报弹窗", notes = "日报弹窗")
    @Override
    public Response<DailyPopupVo> dailyPopup(DailyPopupRequest request) {
        log.info("dailyPopup request:{}", request);
        return Response.success(dailyNewService.dailyPopup(request));
    }

    @ApiOperation(value = "日报查看", notes = "日报查看")
    @Override
    public Response dailyViewed(DailyPopupRequest request) {
        log.info("dailyViewed request:{}", request);
        dailyNewService.dailyViewed(request);
        try {
            if (request.getPerson().equals(request.getEmployeeId())) {
                JSONObject obj = new JSONObject();
                obj.put("nodeCode", "VIEW_SALES_DAILY");
                obj.put("person", request.getPerson());
                rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
            }
        } catch (Exception ex) {
            log.error("dailyViewed 发送MQ失败", ex);
        }
        return Response.success();
    }

    @ApiOperation(value = "每日人员信息", notes = "每日人员信息")
    @Override
    public Response<DailyEmployeeInfoVo> dailyEmployee(DailyEmployeeRequest request) {
        log.info("dailyEmployee request:{}", request);
        return Response.success(dailyNewService.dailyEmployee(request));
    }

    @ApiOperation(value = "个人日报数据", notes = "个人日报数据")
    @Override
    public Response<DailyPerformanceVo> dailyDetail(DailyPerformanceRequest request) {
        log.info("dailyDetail request:{}", request);
        return Response.success(dailyNewService.dailyDetail(request));
    }

    @ApiOperation(value = "个人日报数据列表", notes = "个人日报数据列表")
    @Override
    public Response<IPage<DailyPerformanceVo>> dailyList(DailyPerformancePageRequest request) {
        log.info("dailyList request:{}", request);
        return Response.success(dailyNewService.dailyList(request));
    }

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @Override
    public Response<IPage<DailyOrderVo>> dailyOrderList(DailyOrderPageRequest request) {
        log.info("dailyOrderList request:{}", request);
        return Response.success(dailyNewService.dailyOrderList(request));
    }

    @ApiOperation(value = "sku列表", notes = "sku列表")
    @Override
    public Response<IPage<DailySkuVo>> dailySkuList(DailySkuPageRequest request) {
        log.info("dailySkuList request:{}", request);
        return Response.success(dailyNewService.dailySkuList(request));
    }

    @ApiOperation(value = "sku组织交叉列表", notes = "sku组织交叉列表")
    @Override
    public Response<DailySkuCrossVo> dailySkuCross(DailySkuPageRequest request) {
        log.info("dailySkuCross request:{}", request);
        return Response.success(dailyNewService.dailySkuCross(request));
    }

    @ApiOperation(value = "新开客户列表", notes = "新开客户列表")
    @Override
    public Response<IPage<DailyEmployeeVo>> dailyNewCustomerList(DailyNewCustomerPageRequest request) {
        log.info("dailyNewCustomerList request:{}", request);
        return Response.success(dailyNewService.dailyNewCustomerList(request));
    }

    @ApiOperation(value = "库存数据", notes = "库存数据")
    @Override
    public Response<DailyInventoryCrossVo> dailyInventoryCross(DailyInventoryRequest request) {
        return Response.success(dailyNewService.dailyInventoryCross(request));
    }

}
