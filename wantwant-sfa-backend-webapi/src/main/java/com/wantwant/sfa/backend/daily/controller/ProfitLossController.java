package com.wantwant.sfa.backend.daily.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.budget.vo.BudgetOverviewGroupVO;
import com.wantwant.sfa.backend.budget.vo.BudgetOverviewVO;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupVo;
import com.wantwant.sfa.backend.daily.api.ProfitLossApi;
import com.wantwant.sfa.backend.daily.request.DailyWareHousingRequest;
import com.wantwant.sfa.backend.daily.request.SfaProfitLossForecastExamineRequest;
import com.wantwant.sfa.backend.daily.request.SupplementCostRequest;
import com.wantwant.sfa.backend.daily.vo.*;
import com.wantwant.sfa.backend.mapper.budget.BudgetOverviewMapper;
import com.wantwant.sfa.backend.realData.vo.MaterialCostVo;
import com.wantwant.sfa.backend.service.DayProfitLossService;
import com.wantwant.sfa.backend.service.ProfitLossForecastService;
import com.wantwant.sfa.backend.service.ProfitLossService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/** <AUTHOR> @Date 2021/3/22 */
@RestController
@Slf4j
public class ProfitLossController implements ProfitLossApi {

  @Autowired private ProfitLossService profitLossService;

  @Autowired private DayProfitLossService dayProfitLossService;

  @Autowired private RedisUtil redisUtil;

  @Autowired  private BudgetOverviewMapper budgetOverviewMapper;

  private static final String PROFITLOSS_AUDIT_CHECK = "profitLoss:audit:check";

  // 目标变更共用key
  public static final String LOCK_Profit_Loss = "lOCKProfitLoss";

  @Autowired
  private ProfitLossForecastService profitLossForecastService;

  @Override
  public Response<Integer> goalUpload(MultipartHttpServletRequest request) {
    LocalDate startDate;
    LocalDate endDate;

    String msg = "";

    try {
      startDate = LocalDate.parse(request.getParameter("startDate") + "-01");
      //			endDate =
      // LocalDate.parse(request.getParameter("endDate")+"-01").with(TemporalAdjusters.lastDayOfMonth());
      endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
    } catch (Exception e) {
      throw new ApplicationException("时间格式错误！");
    }

    //		if(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).isAfter(startDate)) {
    //			throw new ApplicationException("开始时间早于当前月份");
    //		}else if(startDate.isAfter(endDate)) {
    //			throw new ApplicationException("结束时间早于开始时间");
    //		}

    if (!redisUtil.setLockIfAbsent(LOCK_Profit_Loss, "1", 5, TimeUnit.SECONDS)) {
      return Response.error("请求正在处理中！～");
    }

    try {
      String person = request.getParameter("person");
      int type = Integer.parseInt(request.getParameter("type"));
      int isWarehousing = Integer.parseInt(request.getParameter("isWarehousing"));
      ImportParams params = new ImportParams();

      Map<String, MultipartFile> fileMap = request.getFileMap();

      try {
        int count = 0;
        for (String key : fileMap.keySet()) {

          MultipartFile multipartFile = fileMap.get(key);
          ProfitLossExcelVo goalExcel = new ProfitLossExcelVo();
          goalExcel.setName(key);
          goalExcel.setPerson(person);
          goalExcel.setType(type);
          goalExcel.setStartDate(startDate);
          goalExcel.setEndDate(endDate);
          goalExcel.setIsWarehousing(isWarehousing);
          List<ImportCostVo> costVO = null;
          List<ImportCostTravelExpensesVo> costTravelExpensesVO = null;
          List<ImportWarehousingVo> warehousingVo = null;

          List<ImportCostWareaVo> importCostWareaVo = null;
          List<ImportWareVo> importWareVo = null;
          if (type == 1) {
            costVO =
                ExcelImportUtil.importExcel(
                    multipartFile.getInputStream(), ImportCostVo.class, params);
            List<ImportCostVo> collectList =
                costVO.stream().filter(c -> c.getDate() != null).collect(Collectors.toList());
            Map<String, List<ImportCostVo>> collect =
                collectList.stream().collect(Collectors.groupingBy(ImportCostVo::getDate));
            for (Map.Entry<String, List<ImportCostVo>> col : collect.entrySet()) {
              String keyy = col.getKey();
              if (!keyy.equals(goalExcel.getStartDate().toString().substring(0, 7))) {
                msg = "成本表格中导入日期要与筛选条件日期相同";
                throw new ApplicationException("表格中导入日期要与筛选条件日期相同");
              }
            }
            count = profitLossService.insterByCost(goalExcel, collectList);
          } else if (type == 2) {
            if(isWarehousing==1){
              importCostWareaVo =
                      ExcelImportUtil.importExcel(
                              multipartFile.getInputStream(), ImportCostWareaVo.class, params);
              //过滤导入数据如果类型不为费用类型，或者有空刚报错
              List<ImportCostWareaVo> coll = importCostWareaVo.stream().filter(i -> null == i.getTemplateType() || i.getTemplateType() != type).collect(Collectors.toList());
              if(CommonUtil.ListUtils.isNotEmpty(coll)){
                throw new ApplicationException("导入内容的模版类型有误");
              }
              List<ImportCostWareaVo> collectList =
                      importCostWareaVo.stream()
                              .filter(c -> c.getDate() != null)
                              .collect(Collectors.toList());
              Map<String, List<ImportCostWareaVo>> collect =
                      collectList.stream()
                              .collect(Collectors.groupingBy(ImportCostWareaVo::getDate));
              for (Map.Entry<String, List<ImportCostWareaVo>> col : collect.entrySet()) {
                String keyy = col.getKey();
                if (!keyy.equals(goalExcel.getStartDate().toString().substring(0, 7))) {
                  msg = "费用表格中导入日期要与筛选条件日期相同";
                  throw new ApplicationException("表格中导入日期要与筛选条件日期相同");
                }
              }
              count =  profitLossService.insterByCostWare(goalExcel, collectList);
            }else{
              costTravelExpensesVO =
                      ExcelImportUtil.importExcel(
                              multipartFile.getInputStream(), ImportCostTravelExpensesVo.class, params);
              //过滤导入数据如果类型不为费用类型，或者有空刚报错
              List<ImportCostTravelExpensesVo> coll = costTravelExpensesVO.stream().filter(i -> null == i.getTemplateType() || i.getTemplateType() != type).collect(Collectors.toList());
              if(CommonUtil.ListUtils.isNotEmpty(coll)){
                throw new ApplicationException("导入内容的模版类型有误");
              }

              List<ImportCostTravelExpensesVo> collectList =
                      costTravelExpensesVO.stream()
                              .filter(c -> c.getDate() != null)
                              .collect(Collectors.toList());
              Map<String, List<ImportCostTravelExpensesVo>> collect =
                      collectList.stream()
                              .collect(Collectors.groupingBy(ImportCostTravelExpensesVo::getDate));
              for (Map.Entry<String, List<ImportCostTravelExpensesVo>> col : collect.entrySet()) {
                String keyy = col.getKey();
                if (!keyy.equals(goalExcel.getStartDate().toString().substring(0, 7))) {
                  msg = "费用表格中导入日期要与筛选条件日期相同";
                  throw new ApplicationException("表格中导入日期要与筛选条件日期相同");
                }
              }
              count = profitLossService.insterByCostTravelExpenses(goalExcel, collectList);
            }

          } else {
            if(isWarehousing==1){
              importWareVo =
                      ExcelImportUtil.importExcel(
                              multipartFile.getInputStream(), ImportWareVo.class, params);
              //过滤导入数据如果类型不为仓储类型，或者有空刚报错
              List<ImportWareVo> coll = importWareVo.stream().filter(i -> null == i.getTemplateType() || i.getTemplateType() != type).collect(Collectors.toList());
              if(CommonUtil.ListUtils.isNotEmpty(coll)){
                throw new ApplicationException("导入内容的模版类型有误");
              }

              List<ImportWareVo> collectList =
                      importWareVo.stream()
                              .filter(c -> c.getDate() != null)
                              .collect(Collectors.toList());
              Map<String, List<ImportWareVo>> collect =
                      collectList.stream().collect(Collectors.groupingBy(ImportWareVo::getDate));
              for (Map.Entry<String, List<ImportWareVo>> col : collect.entrySet()) {
                String keyy = col.getKey();
                if (!keyy.equals(goalExcel.getStartDate().toString().substring(0, 7))) {
                  msg = "仓储表格中导入日期要与筛选条件日期相同";
                  throw new ApplicationException("表格中导入日期要与筛选条件日期相同");
                }
              }
              count = profitLossService.insterByWare(goalExcel, collectList);
            }else{
              warehousingVo =
                      ExcelImportUtil.importExcel(
                              multipartFile.getInputStream(), ImportWarehousingVo.class, params);
              //过滤导入数据如果类型不为仓储类型，或者有空刚报错
              List<ImportWarehousingVo> coll = warehousingVo.stream().filter(i -> null == i.getTemplateType() || i.getTemplateType() != type).collect(Collectors.toList());
              if(CommonUtil.ListUtils.isNotEmpty(coll)){
                throw new ApplicationException("导入内容的模版类型有误");
              }
              List<ImportWarehousingVo> collectList =
                      warehousingVo.stream()
                              .filter(c -> c.getDate() != null)
                              .collect(Collectors.toList());
              Map<String, List<ImportWarehousingVo>> collect =
                      collectList.stream().collect(Collectors.groupingBy(ImportWarehousingVo::getDate));
              for (Map.Entry<String, List<ImportWarehousingVo>> col : collect.entrySet()) {
                String keyy = col.getKey();
                if (!keyy.equals(goalExcel.getStartDate().toString().substring(0, 7))) {
                  msg = "仓储表格中导入日期要与筛选条件日期相同";
                  throw new ApplicationException("表格中导入日期要与筛选条件日期相同");
                }
              }
              count = profitLossService.insterByWarehousingVo(goalExcel, collectList);
            }
          }
        }
        return Response.success(count, "导入成功" + count + "数据");
      } catch (Exception e) {
        log.error("导入失败", e);
        if (StringUtils.isNotBlank(msg)) {
          return Response.error("导入失败," + msg);
        }
        return Response.error("导入失败");
      }
    } finally {
      redisUtil.unLock(LOCK_Profit_Loss, "1");
    }
  }

  @Override
  public Response<List<ProfitLossListWareaVo>> selProfitLossWarehousing(DailyWareHousingRequest request) throws IllegalAccessException, NoSuchMethodException {
    List<ProfitLossListWareaVo> profitLossList = dayProfitLossService.selProfitLossWare(request);

    Map<Integer, BudgetOverviewVO> budgetMap = Maps.newHashMap();
    List<BudgetOverviewVO> budgetList = budgetOverviewMapper.selectListBudget(request.getStartTime(),request.getEndTime());
    //占比 = 金额/盘价业绩
    final BigDecimal pjAmount = budgetList.stream().filter(p -> 2 == p.getItemCode()).map(BudgetOverviewVO::getEstimateAmount23).reduce(BigDecimal.ZERO, BigDecimal::add);
    budgetList.forEach(b -> {
      if (null != pjAmount && pjAmount.compareTo(BigDecimal.ZERO) > 0
              && null != b.getEstimateAmount23() && b.getEstimateAmount23().compareTo(BigDecimal.ZERO) > 0 ){
        b.setEstimateRate23(b.getEstimateAmount23().divide(pjAmount,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
      }
      budgetMap.put(b.getItemCode(), b);
    });
    profitLossList.forEach(p -> {
      BudgetOverviewVO b1 = budgetMap.get(p.getSerialNumber());
      if (Objects.nonNull(b1)) {
        p.setEstimateAmount(b1.getEstimateAmount23());
        p.setEstimateRate(b1.getEstimateRate23());
        if(StringUtils.isNotBlank(b1.getItemDesign())){
          p.setName(b1.getItemName()+b1.getItemDesign());
        }else{
          p.setName(b1.getItemName());
        }
        p.setItemAddress(b1.getItemAddress());
        if(StringUtils.isNotBlank(b1.getItemAddress())){
          p.setIsSkip(1);
        }
        p.setItemType(b1.getItemType());
        p.setItemComment(b1.getItemComment());
        p.setLevel(b1.getLevel());
        p.setParentId(b1.getParentId());
        p.setBgColor(b1.getBgColor());
        p.setTab(b1.getTab());
      }
    });
    profitLossList.forEach(p -> p.setChildren(profitLossList.stream().filter(f -> p.getSerialNumber() == f.getParentId()).collect(Collectors.toList())));
    List<ProfitLossListWareaVo> list = profitLossList.stream().filter(b -> null != b.getLevel() && 1 == b.getLevel()).collect(Collectors.toList());

    return Response.success(list);
  }

  @Override
  public Response<List<ProfitLossGroupVo>> selProfitLossProductGroup(DailyWareHousingRequest request) throws IllegalAccessException, NoSuchMethodException {

    List<ProfitLossGroupVo> profitLossList =null;
    //如果小于2024 用20230901版本
    profitLossList = dayProfitLossService.selProfitLossProductGroupNew(request);

    int quarterMon=0;
    Double mon = Double.valueOf(request.getYearMonth().substring(5, 7));
    /*1季度是1月，2季度是4月，3季度是7月，4季度是10月，*/
    if(Integer.valueOf((int) Math.ceil(mon/Double.valueOf("3")))==1){
      quarterMon=1;
    }
    if(Integer.valueOf((int) Math.ceil(mon/Double.valueOf("3")))==2){
      quarterMon=2;
    }
    if(Integer.valueOf((int) Math.ceil(mon/Double.valueOf("3")))==3){
      quarterMon=3;
    }
    if(Integer.valueOf((int) Math.ceil(mon/Double.valueOf("3")))==4){
      quarterMon=4;
    }
    //根据月份得到季度  月份/3向上取整 得到季度
    Map<Integer, BudgetOverviewGroupVO> budgetMap = Maps.newHashMap();
    List<BudgetOverviewGroupVO> budgetList =null;
      budgetList = budgetOverviewMapper.selectListBudgetMonthNew(quarterMon);

    //占比 = 金额/盘价业绩
    //final BigDecimal pjAmount = budgetList.stream().filter(p -> 2 == p.getItemCode()).map(BudgetOverviewVO::getEstimateAmount23).reduce(BigDecimal.ZERO, BigDecimal::add);
    budgetList.forEach(b -> {
      budgetMap.put(b.getItemCode(), b);
    });
    profitLossList.forEach(p -> {
      BudgetOverviewGroupVO b1 = budgetMap.get(p.getSerialNumber());
      if (Objects.nonNull(b1)) {
        //全部
        p.setTotalEstimateRate(b1.getBudgetRate());
        p.setTotalDiscrepancy(publicSubtract(p.getTotalAccounted(),p.getTotalEstimateRate()));

        //A组
        p.setAaEstimateRate(b1.getBudgetRatea());
        p.setAaDiscrepancy( publicSubtract(p.getAaAccounted(),p.getAaEstimateRate()));

        //B组
        p.setBbEstimateRate(b1.getBudgetRateb());
        p.setBbDiscrepancy(publicSubtract(p.getBbAccounted(),p.getBbEstimateRate()));

        //C组
        p.setCcEstimateRate(b1.getBudgetRatec());
        p.setCcDiscrepancy( publicSubtract(p.getCcAccounted(),p.getCcEstimateRate()));

        //D组
        p.setDdEstimateRate(b1.getBudgetRated());
        p.setDdDiscrepancy(publicSubtract(p.getDdAccounted(),p.getDdEstimateRate()));

        //E组
        p.setEeEstimateRate(b1.getBudgetRatee());
        p.setEeDiscrepancy(publicSubtract(p.getEeAccounted(),p.getEeEstimateRate()));

        //F组
        p.setFfEstimateRate(b1.getBudgetRatef());
        p.setFfDiscrepancy(publicSubtract(p.getFfAccounted(),p.getFfEstimateRate()));

        if(StringUtils.isNotBlank(b1.getItemDesign())){
          p.setName(b1.getItemName()+b1.getItemDesign());
        }else{
          p.setName(b1.getItemName());
        }
        p.setItemAddress(b1.getItemAddress());
        if(StringUtils.isNotBlank(b1.getItemAddress())){
          p.setIsSkip(1);
        }
        p.setItemType(b1.getItemType());
        p.setItemComment(b1.getItemComment());
        p.setLevel(b1.getLevel());
        p.setParentId(b1.getParentId());
        p.setBgColor(b1.getBgColor());
        p.setTab(b1.getTab());
      }
    });
    List<ProfitLossGroupVo> finalProfitLossList = profitLossList;
    //List<ProfitLossGroupVo> list =new Array;
    ArrayList<ProfitLossGroupVo> profitList = new ArrayList<>();

      profitLossList.forEach(p -> p.setChildren(finalProfitLossList.stream().filter(f -> p.getSerialNumber() == f.getParentId()-500 && p.getSerialNumber()!=0).collect(Collectors.toList())));
      List<ProfitLossGroupVo>  list = profitLossList.stream().filter(b -> 1 == b.getLevel() || 0 == b.getLevel()).collect(Collectors.toList());
      List<ProfitLossGroupVo> collect = list.stream().filter(p -> p.getSerialNumber() == 2).collect(Collectors.toList());
      if(CommonUtil.ListUtils.isNotEmpty(collect)){
        ProfitLossGroupVo profitLossGroupVo = collect.get(0);
        //用财务去税 计算
        BigDecimal aaAccounted = profitLossGroupVo.getAaMoney().divide(profitLossGroupVo.getTotalMoney(), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal bbAccounted = profitLossGroupVo.getBbMoney().divide(profitLossGroupVo.getTotalMoney(), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal ccAccounted = profitLossGroupVo.getCcMoney().divide(profitLossGroupVo.getTotalMoney(), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal ddAccounted = profitLossGroupVo.getDdMoney().divide(profitLossGroupVo.getTotalMoney(), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal eeAccounted = profitLossGroupVo.getEeMoney().divide(profitLossGroupVo.getTotalMoney(), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal ffAccounted = profitLossGroupVo.getFfddMoney().divide(profitLossGroupVo.getTotalMoney(), 4, BigDecimal.ROUND_HALF_UP);

        list.forEach(l->{
          if(l.getSerialNumber()==0){
            l.setTotalAccounted(new BigDecimal(1).multiply(new BigDecimal(100)));
            l.setAaAccounted(aaAccounted.multiply(new BigDecimal(100)));
            l.setBbAccounted(bbAccounted.multiply(new BigDecimal(100)));
            l.setCcAccounted(ccAccounted.multiply(new BigDecimal(100)));
            l.setDdAccounted(ddAccounted.multiply(new BigDecimal(100)));
            l.setEeAccounted(eeAccounted.multiply(new BigDecimal(100)));
            l.setFfAccounted(ffAccounted.multiply(new BigDecimal(100)));
          }
        });
      }
      profitList.addAll(list);

    return Response.success(profitList);
  }

 public BigDecimal publicSubtract(BigDecimal left,BigDecimal right){
   if(null == left||null ==right){
      return null;
    } else {
      return left.subtract(right);
   }
  }

  @Override
  public Response<List<DayProfitLossVo>> selProfitLossWarehousingDay(DailyWareHousingRequest request) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
    List<DayProfitLossVo> dayProfitLossVos = dayProfitLossService.selProfitLossWareDay(request);
    Map<Integer, BudgetOverviewVO> budgetMap = Maps.newHashMap();
    List<BudgetOverviewVO> budgetList = budgetOverviewMapper.selectListOverview();
    budgetList.forEach(b -> budgetMap.put(b.getItemCode(),b));
    dayProfitLossVos.forEach(d -> {
      BudgetOverviewVO bo = budgetMap.get(d.getSerialNumber());
      if (Objects.nonNull(bo)){
        if(StringUtils.isNotBlank(bo.getItemDesign())){
          d.setName(bo.getItemName()+bo.getItemDesign());
        }else{
          d.setName(bo.getItemName());
        }
        d.setItemAddress(bo.getItemAddress());
        if(StringUtils.isNotBlank(bo.getItemAddress())){
          d.setIsSkip(1);
        }
        d.setItemType(bo.getItemType());
        d.setLevel(bo.getLevel());
        d.setItemComment(bo.getItemComment());
        d.setParentId(bo.getParentId());
        d.setBgColor(bo.getBgColor());
        d.setTab(bo.getTab());
      }
    });
    dayProfitLossVos.forEach(d -> d.setChildren(dayProfitLossVos.stream().filter(f -> d.getSerialNumber() == f.getParentId()).collect(Collectors.toList())));
    List<DayProfitLossVo> list = dayProfitLossVos.stream().filter(f -> 1 == f.getLevel()).collect(Collectors.toList());
    return Response.success(list);
  }

  @Override
  public Response<List<DayProfitLossVo>> selProfitLossWarehousingMonth(DailyWareHousingRequest request) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
    return Response.success(dayProfitLossService.selProfitLossWareMonth(request));
  }

  @Override
  public Response<IPage<SupplementCostVo>> selProfitLossSupplementCost(SupplementCostRequest request) {
    return Response.success(dayProfitLossService.selProfitLossSupplementCost(request));
  }

  @Override
  public Response<List<MaterialCostVo>> selProfitLossSupplementCostSku(SupplementCostRequest request) {
    return Response.success(dayProfitLossService.selProfitLossSupplementCostSku(request));
  }

  @Override
  public Response<Page<SfaProfitLossForecastExamineVo>> selProfitLossForecastExamine(SfaProfitLossForecastExamineRequest request) {
    return Response.success(profitLossForecastService.selProfitLossForecastExamine(request));
  }

  @Override
  public Response<Integer> updateProfitLossForecastExamine(SfaProfitLossForecastExamineRequest request) {
    if(!redisUtil.setLockIfAbsent(PROFITLOSS_AUDIT_CHECK,request.getEmployeeId(),5, TimeUnit.SECONDS)){
      return Response.error("当前正在处理中");
    }
    try{
      profitLossForecastService.updateProfitLossForecastExamine(request);
    }finally {
      redisUtil.unLock(PROFITLOSS_AUDIT_CHECK,request.getEmployeeId());
    }
    return  Response.success();
  }

  @Override
  public Response<List<BusinessGroupVo>> getBusionessGroup() {
    return Response.success(profitLossForecastService.getBusionessGroup());
  }

  @Override
  public Response<Integer> isFinance(String employeeId) {
    return Response.success(profitLossForecastService.isFinance(employeeId));
  }

  @Override
  public Response<Integer> getProfitLossExamine(String employeeId) {
    return Response.success(profitLossForecastService.getProfitLossExamine(employeeId));
  }

  @Override
  public void exportGoodsProductLineDataYearSku(SfaProfitLossForecastExamineRequest request, HttpServletResponse response) {
    request.setPage(null);
    request.setRows(null);
    Page<SfaProfitLossForecastExamineVo> result = profitLossForecastService.selProfitLossForecastExamine(request);
    List<SfaProfitLossForecastExamineVo> profitLossList = new ArrayList<>();
    if(CommonUtil.ListUtils.isNotEmpty(result.getList())){
      List<SfaProfitLossForecastExamineVo> records = result.getList();
      BeanUtils.copyProperties(records,profitLossList,SfaProfitLossForecastExamineVo.class,SfaProfitLossForecastExamineVo.class);
      EasyPoiUtil.exportExcel(profitLossList,null,"sheet1",SfaProfitLossForecastExamineVo.class,"导出商品数据sku数据.xls",response);
    }
  }
}
