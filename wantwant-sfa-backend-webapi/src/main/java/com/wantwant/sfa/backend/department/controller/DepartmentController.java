package com.wantwant.sfa.backend.department.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.department.api.DepartmentApi;
import com.wantwant.sfa.backend.department.vo.DepartmentVo;
import com.wantwant.sfa.backend.service.IDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 部门用controller。
 * @Auther: zhengxu
 * @Date: 2021/10/26/下午2:58
 */
@RestController
public class DepartmentController implements DepartmentApi {
    @Autowired
    private IDepartmentService departmentService;

    @Override
    public Response<List<DepartmentVo>> getDepartmentList() {
        List<DepartmentVo> list = departmentService.getDepartmentList();
        return Response.success(list);
    }
}
