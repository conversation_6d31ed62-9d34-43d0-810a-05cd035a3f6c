package com.wantwant.sfa.backend.directOperatedGroup;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.directOperatedGroup.controller.DirectOperatedGroupRealtimeDataApi;
import com.wantwant.sfa.backend.directOperatedGroup.req.PurchasedItemsReq;
import com.wantwant.sfa.backend.directOperatedGroup.req.SystemClientCooperationProgressReq;
import com.wantwant.sfa.backend.directOperatedGroup.service.DirectOperatedGroupRealtimeDataService;
import com.wantwant.sfa.backend.directOperatedGroup.vo.PurchasedItemsListEnumsVo;
import com.wantwant.sfa.backend.directOperatedGroup.vo.PurchasedItemsVo;
import com.wantwant.sfa.backend.directOperatedGroup.vo.SystemClientCooperationProgressVo;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.directOperatedGroup
 * @Description:
 * @Date: 2024/11/6 10:02
 */
@RestController
public class DirectOperatedGroupRealtimeDataController implements DirectOperatedGroupRealtimeDataApi {
    @Resource
    private DirectOperatedGroupRealtimeDataService directOperatedGroupRealtimeDataService;

    @Override
    public Response<IPage<SystemClientCooperationProgressVo>> systemClientCooperationProgressList(SystemClientCooperationProgressReq req){
        return Response.success(directOperatedGroupRealtimeDataService.systemClientCooperationProgressList(req));
    }

    @Override
    public void systemClientCooperationProgressDown(SystemClientCooperationProgressReq req, HttpServletRequest request, HttpServletResponse response){
        directOperatedGroupRealtimeDataService.systemClientCooperationProgressDown(req,request,response);
    }


    @Override
    public Response<IPage<PurchasedItemsVo>> purchasedItemsList(PurchasedItemsReq req) {
        return Response.success(directOperatedGroupRealtimeDataService.purchasedItemsList(req));
    }

    @Override
    public Response<PurchasedItemsListEnumsVo> purchasedItemsListEnums(PurchasedItemsReq req) {
        return Response.success(directOperatedGroupRealtimeDataService.purchasedItemsListEnums(req));
    }

    @Override
    public void purchasedItemsDown(PurchasedItemsReq req, HttpServletRequest request, HttpServletResponse response) {
        directOperatedGroupRealtimeDataService.purchasedItemsDown(req,request,response);
    }

}
