package com.wantwant.sfa.backend.employee.assemble;

import com.wantwant.sfa.backend.domain.emp.DO.BusinessTagDO;
import com.wantwant.sfa.backend.employee.request.BusinessTagRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/下午3:28
 */
@Mapper(componentModel = "spring")
public interface BusinessTagAssemble {


    BusinessTagDO convert2DO(BusinessTagRequest businessTagRequest);
}
