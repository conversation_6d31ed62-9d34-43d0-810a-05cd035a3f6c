package com.wantwant.sfa.backend.employee.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.BusinessTagApplication;
import com.wantwant.sfa.backend.employee.api.BusinessTagApi;
import com.wantwant.sfa.backend.employee.assemble.BusinessTagAssemble;
import com.wantwant.sfa.backend.employee.request.BusinessTagRequest;
import com.wantwant.sfa.backend.employee.request.DeleteBusinessTagRequest;
import com.wantwant.sfa.backend.employee.vo.BusinessTagVO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/14/下午2:07
 */
@RestController
public class BusinessTagController implements BusinessTagApi {
    @Resource
    private BusinessTagApplication businessTagApplication;
    @Resource
    private BusinessTagAssemble businessTagAssemble;

    @Override
    public Response<BusinessTagVO> getLastBusinessTag(Integer applyId, String person) {
        BusinessTagVO businessTagVO = businessTagApplication.getLastBusinessTag(applyId,null,person);
        return Response.success(businessTagVO);
    }

    @Override
    public Response<List<BusinessTagVO>> getHistoryTag(Integer applyId, String person) {
        List<BusinessTagVO> list = businessTagApplication.getHistoryTag(applyId,null,person);
        return Response.success(list);
    }

    @Override
    public Response<BusinessTagVO> getLastTagByMemberKey(Long memberKey, String person) {
        BusinessTagVO businessTagVO = businessTagApplication.getLastBusinessTag(null,memberKey,person);
        return Response.success(businessTagVO);
    }

    @Override
    public Response<List<BusinessTagVO>> getHistoryTagByMemberKey(Long memberKey, String person) {
        List<BusinessTagVO> list = businessTagApplication.getHistoryTag(null,memberKey,person);
        return Response.success(list);
    }

    @Override
    public Response addTag(@Valid BusinessTagRequest businessTagRequest) {
         businessTagApplication.addTag(businessTagAssemble.convert2DO(businessTagRequest),businessTagRequest.getPerson());
         return Response.success();
    }

    @Override
    public Response deleteTag(@Valid DeleteBusinessTagRequest deleteBusinessTagRequest) {
        businessTagApplication.deleteTag(deleteBusinessTagRequest.getTagId(),deleteBusinessTagRequest.getPerson());
        return Response.success();
    }
}
