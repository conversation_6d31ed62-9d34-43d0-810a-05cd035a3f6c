package com.wantwant.sfa.backend.employee.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.employee.api.EmployeeApi;
import com.wantwant.sfa.backend.employee.request.*;
import com.wantwant.sfa.backend.employee.vo.*;
import com.wantwant.sfa.backend.service.IEmployeeService;
import com.wantwant.sfa.backend.service.IEmployeeService2;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;


/**
 * @Description 测试
 * <AUTHOR>
 * @Date 2020/3/23
 **/
@RestController
@Slf4j
public class EmployeeController implements EmployeeApi {

    @Autowired
    private IEmployeeService employeeService;
    @Autowired
    private IEmployeeService2 employeeService2;
	
	@Override
	public Response<PositionListResponse> PositionList(PositionListRequest request) {
		request.setChannel(1);
		return  Response.success(employeeService.EmloyeeList(request));
	}

	@Override
	public Response<PositionListResponse> PositionListFor123(@RequestBody PositionListRequest request) {
		request.setChannel(RequestUtils.getChannel());
		return  Response.success(employeeService.EmloyeeList(request));
	}

	@Override
	public Response<EmployeeInfoVO> getEmpInfoById(String employeeId) {
		return Response.success(employeeService.getEmpInfoById(employeeId,RequestUtils.getChannel()));
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Response manualQuit(QuitRequest request) {
		String person = request.getPerson();		
		log.info("start EmployeeController manualQuit request:{},person:{}",request,person);
		
		employeeService.employeeQuit(request,person);
		return Response.success();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Response manualEntry(ManualEntryRequest request) {
		String person = request.getPerson();		
		log.info("start EmployeeController manualEntry request:{},person:{}",request,person);

		employeeService.employeeEntry(request);
		return Response.success();
	}

	@Override
	public void export(PositionListRequest request) {
		log.info("start EmployeeController export request:{}",request);
		 ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
		            .getRequestAttributes();
		 HttpServletResponse response = servletRequestAttributes.getResponse();		 
		 request.setChannel(1);
		 List<PositionExport> record = employeeService.exportExcel(request);
		 String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

		  String name = "岗位列表"+sheetName;
		  
	      Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null,sheetName),PositionExport.class, record);
	        
	      response.setContentType("application/vnd.ms-excel");
	        
	      try {
	            response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder
	                .encode(name+".xlsx"));
	            OutputStream outputStream = response.getOutputStream();
	            workbook.write(outputStream);
	            outputStream.flush();
	            outputStream.close();
	        } catch (IOException e) {
	            response.setStatus(500);
	        }

	}

	@Override
	public void exportFor123(PositionListRequest request) {
		log.info("start EmployeeController exportFor123 request:{}",request);
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
				.getRequestAttributes();
		HttpServletResponse response = servletRequestAttributes.getResponse();
		request.setChannel(RequestUtils.getChannel());
		List<PositionExport> record = employeeService.exportExcel(request);

		String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

		String name = "123组织岗位列表"+sheetName;

		Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null,sheetName),PositionExport.class, record);

		response.setContentType("application/vnd.ms-excel");

		try {
			response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder
					.encode(name+".xlsx"));
			OutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			outputStream.flush();
			outputStream.close();
		} catch (IOException e) {
			response.setStatus(500);
		}

	}

	/**
	 * 待变更记录
	 */
	@Override
	public Response<ChangeDetailVo> changeDetail(ChangeDetailRequest request) {
		log.info("start EmployeeController changeDetail request:{}",request);
		return Response.success(employeeService.getChangeDetail(request));
	}

	/**
	 * 123待变更记录
	 */
	@Override
	public Response<ChangeDetailVo> changeDetailFor123(ChangeDetailRequest request) {
		log.info("start EmployeeController changeDetailFor123 request:{}",request);
		return Response.success(employeeService.getChangeDetail(request));
	}

	/**
	 * 取消变更计划
	 */
	@Override
	public Response cancelChange(CancelChangeRequest request) {
		log.info("start EmployeeController cancelChange request:{}",request);
		employeeService.cancelChange(request);
		return Response.success();
	}

	/**
	 * 123取消变更计划
	 */
	@Override
	public Response cancelChangeFor123(CancelChangeRequest request) {
		log.info("start EmployeeController cancelChangeFor123 request:{}",request);
		employeeService.cancelChange(request);
		return Response.success();
	}

	/**
	 * 添加离职计划
	 */
	@Override
	public Response changeQuit(QuitRequest request) {
		log.info("start EmployeeController changeQuit request:{}",request);
		if(request.getChangeDate() == null) {
            throw new ApplicationException("离职日期不能为空！");
		}
		employeeService.changeQuit(request, 1);
		return Response.success();
	}

	/**
	 * 添加离职计划
	 */
	@Override
	public Response changeQuitFor123(QuitRequest request) {
		log.info("start EmployeeController changeQuitFor123 request:{}",request);
		if(request.getChangeDate() == null) {
			throw new ApplicationException("离职日期不能为空！");
		}
		employeeService.changeQuit(request, RequestUtils.getChannel());
		return Response.success();
	}

	@Override
	public Response changeEntry(ManualEntryRequest request) {
		log.info("start EmployeeController changeEntry request:{}",request);
		if(request.getChangeDate() == null) {
            throw new ApplicationException("入职日期不能为空！");
		}
		employeeService.changeEntry(request, 1);
		return Response.success();
	}

	@Override
	public Response changeEntryFor123(ManualEntryRequest request) {
		log.info("start EmployeeController changeEntryFor123 request:{}",request);
		if(request.getChangeDate() == null) {
			throw new ApplicationException("入职日期不能为空！");
		}
		employeeService.changeEntry(request, RequestUtils.getChannel());
		return Response.success();
	}


	@Override
	public Response employeeOrganizationFor123(@RequestParam(value = "employeeId") String employeeId){
		log.info("start employeeOrganization  employeeId:{}",employeeId);
		return Response.success(employeeService.getEmployeeOrganization(employeeId));

	}

	@Override
	public Response<IPage<TargetOverviewVO>> targetOverview(TargetOverviewRequest request) {
		log.info("start EmployeeController targetOverview request:{}",request);
		return Response.success(employeeService.targetOverview(request));
	}

	@Override
	public Response exportTargetOverview(TargetOverviewRequest request) {
		log.info("start EmployeeController exportTargetOverview request:{}",request);
		employeeService.exportTargetOverview(request);
		return Response.success();
	}

	@Override
	public Response<List<ZbManagerVo>> getZbManager(String empName) {
		List<ZbManagerVo> list = employeeService.getZbManager(empName);
		return Response.success(list);
	}

	@Override
	public Response<List<CustomerFeesVO>> getCustomerFees(CustomerFeesRequest request) {
		log.info("start EmployeeController examineClassify request:{}",request);
		return Response.success(employeeService.getCustomerFees(request));
	}

	@Override
	public void getCustomerFeesExport(CustomerFeesRequest request) {
		log.info("start EmployeeController examineClassify request:{}",request);
		employeeService.getCustomerFeesExport(request);
	}

	@Override
	public Response<List<CustomerFeesParamVO>> getCustomerFeesParam() {
		return Response.success(employeeService.getCustomerFeesParam());
	}


}
