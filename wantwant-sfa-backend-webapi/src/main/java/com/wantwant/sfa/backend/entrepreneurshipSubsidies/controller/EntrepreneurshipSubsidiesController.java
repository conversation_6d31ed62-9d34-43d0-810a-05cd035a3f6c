package com.wantwant.sfa.backend.entrepreneurshipSubsidies.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.api.EntrepreneurshipSubsidiesApi;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.Entrepreneurship;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.EntrepreneurshipSubsidiesAudit;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.EntrepreneurshipSubsidiesDetailRequest;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.request.EntrepreneurshipSubsidiesRequest;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo.EntrepreneurshipSubsidiesDetailVo;
import com.wantwant.sfa.backend.entrepreneurshipSubsidies.vo.EntrepreneurshipVo;
import com.wantwant.sfa.backend.service.EntrepreneurshipSubsidiesService;
import com.wantwant.sfa.backend.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@RestController
public class EntrepreneurshipSubsidiesController implements EntrepreneurshipSubsidiesApi {

    private static final String SUBSIDIES_APPLY_LOCK = "entrepreneurshipSubsid:apply";

    private static final String SUBSIDIES_AUDIT_LOCK = "entrepreneurshipSubsid:audit";

    private static final String SUBSIDIES_ISSUE_LOCK = "entrepreneurshipSubsid:issue";

    @Autowired
    private EntrepreneurshipSubsidiesService entrepreneurshipSubsidiesService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Response addEntrepreneurshipSubsidiesApply(Entrepreneurship request) {
        if(!redisUtil.setLockIfAbsent(SUBSIDIES_APPLY_LOCK,request.getPartnerMemberkey(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        Integer integer = entrepreneurshipSubsidiesService.addEntrepreneurshipSubsidiesApply(request);
        if(integer>0){
            return Response.success();
        }else{
            return Response.error();
        }
    }

    @Override
    public Response<EntrepreneurshipSubsidiesDetailVo> getEntrepreneurshipSubsidiesDetail(EntrepreneurshipSubsidiesDetailRequest request) {
        return Response.success(entrepreneurshipSubsidiesService.getEntrepreneurshipSubsidiesDetail(request));
    }

    @Override
    public Response<Integer> entrepreneurshipSubsidiesAudit(EntrepreneurshipSubsidiesAudit request) {
        if(!redisUtil.setLockIfAbsent(SUBSIDIES_AUDIT_LOCK,request.getPerson(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        return Response.success(entrepreneurshipSubsidiesService.auditEntrepreneurshipSubsidies(request));
    }

    @Override
    public Response<Integer> entrepreneurshipSubsidiesIssue(EntrepreneurshipSubsidiesAudit request) {
        if(!redisUtil.setLockIfAbsent(SUBSIDIES_ISSUE_LOCK,request.getPerson(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        return Response.success(entrepreneurshipSubsidiesService.issueEntrepreneurshipSubsidies(request));
    }
}
