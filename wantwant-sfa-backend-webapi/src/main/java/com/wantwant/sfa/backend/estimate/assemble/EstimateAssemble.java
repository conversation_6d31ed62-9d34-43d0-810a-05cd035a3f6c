package com.wantwant.sfa.backend.estimate.assemble;


import com.wantwant.sfa.backend.domain.estimate.DO.*;
import com.wantwant.sfa.backend.domain.estimate.DO.value.AdjustDetailValue;
import com.wantwant.sfa.backend.estimate.request.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午7:16
 */
@Mapper(componentModel = "spring")
public interface EstimateAssemble {

    @Mapping(target = "skuDOList", resultType = EstimateSkuDO.class,qualifiedByName = "skuDOListConvert")
    EstimateSkuGroupDO convertEstimateSkuGroupDO(EstimateSkuGroupRequest request);

    EstimateSkuOrgDO estimateSkuOrgRequestToEstimateSkuOrgDO(EstimateSkuOrgRequest request);


    @Mapping(target = "estimateOrganizationDOList", resultType = EstimateOrganizationDO.class)
    EstimateScheduleDO convertEstimateScheduleDO(EstimateScheduleRequest request);

    EstimateOrgSearchDO convertEstimateOrgSearchDO(EstimateOrgSearchRequest estimateOrgSearchRequest);


    @Mapping(target = "skuList", resultType = EstimateApprovalDetailDO.class)
    EstimateApprovalPassDO convertEstimateApprovalPassDO(EstimateApprovalPassRequest request);

    EstimateApprovalRejectDO convertEstimateApprovalRejectDO(EstimateApprovalRejectRequest estimateApprovalRejectRequest);

    @Mapping(target = "skuList", resultType = EstimateApprovalDetailDO.class)
    EstimateSubmitDO convertEstimateSubmit(EstimateSubmitRequest estimateSubmitRequest);

    EstimateShipPeriodDO covertShipPeriod(EstimateShipPeriodRequest estimateShipPeriodRequest);

    EstimateShipPeriodDO covertShipPeriod2(ShipPeriodOperateRequest shipPeriodOperateRequest);

    @Mapping(target = "orgList", resultType = EstimateAdjustOrgDO.class)
    EstimateAdjustDO convertMOQAuditRequest(EstimateAdjustRequest estimateAdjustRequest);

    @Mapping(target = "adjustDetailList", resultType = AdjustDetailValue.class)
    AdjustDO convertAdjust(AdjustRequest adjustRequest);

}
