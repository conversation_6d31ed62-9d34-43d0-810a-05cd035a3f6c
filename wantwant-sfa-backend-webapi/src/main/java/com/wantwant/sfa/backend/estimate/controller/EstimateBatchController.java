package com.wantwant.sfa.backend.estimate.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.estimate.service.IEstimateSkuCatchService;
import com.wantwant.sfa.backend.estimated.api.EstimateBatchApi;
import com.wantwant.sfa.backend.estimated.request.EstimateBatchProcessRequest;
import com.wantwant.sfa.backend.estimated.vo.EstimateBatchDetailVo;
import com.wantwant.sfa.backend.estimated.vo.EstimateBatchVo;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/27/上午9:15
 */
@RestController
@Slf4j
public class EstimateBatchController implements EstimateBatchApi {

    @Autowired
    private IEstimateSkuCatchService estimateSkuCatchService;

    @Override
    public Response<EstimateBatchVo> estimateSku(String month) {
        EstimateBatchVo vo = estimateSkuCatchService.estimateSku(month);
        return Response.success(vo);
    }

    @Override
    public void areaSkuSearchExport(String month, HttpServletResponse response) {
        EstimateBatchVo estimateBatchVo = estimateSkuCatchService.estimateSku(month);
        List<EstimateBatchDetailVo> skuList = estimateBatchVo.getSkuList();
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), EstimateBatchDetailVo.class,skuList);
        EasyPoiUtil.downLoadExcel("可提报物料.xls",response,workbook);
    }

    @Override
    public Response<EstimateBatchVo> skuCatch(String month) {
        EstimateBatchVo vo =  estimateSkuCatchService.skuCatch(month);
        return Response.success(vo);
    }



    @Override
    public Response saveSku(EstimateBatchProcessRequest request) {
        estimateSkuCatchService.saveSku(request);
        return Response.success();
    }
}
