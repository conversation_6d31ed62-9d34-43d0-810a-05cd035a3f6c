package com.wantwant.sfa.backend.estimate.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.EstimateApplication;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.ChannelEnum;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.estimate.service.*;
import com.wantwant.sfa.backend.estimated.api.EstimateSubmitVo;
import com.wantwant.sfa.backend.estimated.request.CacheEstimateRequest;
import com.wantwant.sfa.backend.estimated.api.EstimateApi;
import com.wantwant.sfa.backend.estimated.request.*;
import com.wantwant.sfa.backend.estimated.vo.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.realData.vo.CommitTimesVo;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 销售预估Controller。
 * @Auther: zhengxu
 * @Date: 2021/12/27/上午10:56
 */
@RestController
@Slf4j
public class EstimateController implements EstimateApi {
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private IEstimateService estimateService;
    @Autowired
    private IEstimateV2Service estimateV2Service;
    @Autowired
    private IEstimateAuditService estimateAuditService;
    @Autowired
    private IEstimateSearchService estimateSearchService;
    @Autowired
    private IEstimateCheckService estimateCheckService;
    @Autowired
    private IEstimateSearchV2Service estimateSearchV2Service;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private EstimateApplication estimateApplication;


    private String LOCK_ZB_SUBMIT = "estimate:zb:submit";

    private String LOCK_AREA_SUBMIT  = "estimate:area:submit";

    private String LOCK_COMPANY_SUBMIT = "estimate:company:submit";

    private String LOCK_PRODUCTION_SUBMIT = "estimate:production:submit";

    @Override
    public Response apply(EstimateApplyRequest request) {
        log.info("【销售预估申请】request:{}", JSONObject.toJSONString(request));

        estimateApplication.apply(request);

        return Response.success();
    }

    @Override
    public Response cancel(EstimateApplyRequest request) {
        log.info("【销售预估取消】request:{}",request);

        estimateApplication.cancel(request.getSaleEstimateNo());
        return Response.success();
    }

    @Override
    public Response<List<CeoEstimateVo>> ceoEstimateList(EstimateGoodsRequest request) {
        log.info("【销售预估合伙人列表】request:{}",request);
        List<CeoEstimateVo> list =  estimateSearchV2Service.ceoEstimateList(request);
        return Response.success(list);
    }

    @Override
    public Response<List<DepartmentEstimateVo>> departmentEstimateList(EstimateGoodsRequest request) {
        log.info("【营业所审核列表】request:{}",request);

        List<DepartmentEstimateVo> list = estimateSearchV2Service.departmentEstimateList(request);

        return Response.success(list);
    }

    @Override
    public Response<List<SkuEstimateVo>> goodsEstimateList(EstimateGoodsRequest request) {
        log.info("【销售预估sku列表】request:{}",request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<SkuEstimateVo> list = estimateSearchV2Service.goodsEstimateList(request);
        return Response.success(list);
    }



    @Override
    public Response<List<CompanyFinalConfirmVo>> companyFinalConfirmList(EstimateGoodsRequest request) {
        log.info("【分公司最终确认】request:{}",request);
        List<CompanyFinalConfirmVo> list = estimateSearchV2Service.companyFinalConfirmList(request);
        return Response.success(list);
    }

    @Override
    public Response<List<SkuEstimateVo>> companyGoodsEstimateList(EstimateGoodsRequest request) {
        log.info("【销售预估分公司sku列表】request:{}",request);
        List<SkuEstimateVo> list = estimateSearchV2Service.companyGoodsEstimateList(request);
        return Response.success(list);
    }

    @Override
    public Response companyCache(CompanyEstimateSubmitRequest request) {
        log.info("【销售预估分公司预留保存】request:{}",request);
        if(!redisUtil.setLockIfAbsent(LOCK_COMPANY_SUBMIT,request.getOrganizationId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try{
            estimateV2Service.companyCache(request);
        }finally{
            redisUtil.unLock(LOCK_COMPANY_SUBMIT,request.getOrganizationId());
        }

        return Response.success();
    }

    @Override
    public Response departmentApproval(CompanyAgreeRequest request) {
        log.info("【销售预估分公司审核同意】request:{}",request);
        estimateV2Service.departmentApproval(request);
        return Response.success();
    }

    @Override
    public Response areaCache(AreaSkuConfirmRequest request) {
        log.info("【销售预估大区保存】request:{}",request);
        estimateV2Service.areaCache(request);
        return Response.success();
    }

    @Override
    public Response departmentSubmit(CompanyEstimateSubmitRequest request) {
        log.info("【销售预估区域经理提报】request:{}",request);
        estimateV2Service.departmentSubmit(request);
        return Response.success();
    }

    @Override
    public Response companyApproval(CompanyAgreeRequest request) {
        log.info("【销售预估分公司审核同意】request:{}",request);

        estimateV2Service.companyApproval(request);

        return Response.success();
    }

    @Override
    public Response companySubmit(CompanyEstimateSubmitRequest request) {
        log.info("【销售预估分公司提报】request:{}",request);
        if(!redisUtil.setLockIfAbsent(LOCK_COMPANY_SUBMIT,request.getOrganizationId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try{
            estimateV2Service.companySubmit(request);
        }finally {
            redisUtil.unLock(LOCK_COMPANY_SUBMIT,request.getOrganizationId());
        }

        return Response.success();
    }

    @Override
    public Response areaSubmit(CompanyEstimateSubmitRequest request) {
        log.info("【销售预估大区提报】request:{}",request);
        if(!redisUtil.setLockIfAbsent(LOCK_AREA_SUBMIT,request.getOrganizationId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try{
            estimateV2Service.areaSubmit(request);
        }finally{
            redisUtil.unLock(LOCK_AREA_SUBMIT,request.getOrganizationId());
        }

        return Response.success();
    }

    @Override
    public Response zbSubmit(ProductionSubmitRequest request) {
        log.info("【销售预估总部提报】request:{}",request);

        if(!redisUtil.setLockIfAbsent(LOCK_ZB_SUBMIT,request.getOrganizationId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try{
            estimateV2Service.zbSubmit(request);
        }finally{
            redisUtil.unLock(LOCK_ZB_SUBMIT,request.getOrganizationId());
        }


        return Response.success();
    }

    @Override
    public Response<List<CompanyApprovalVo>> companyEstimateList(EstimateGoodsRequest request) {
        log.info("【销售预估分公司查询】request:{}",request);
        List<CompanyApprovalVo> list = estimateSearchV2Service.companyEstimateList(request);
        return Response.success(list);
    }

    @Override
    public Response productionSubmit(ProductionSubmitRequest request) {
        log.info("【产销提交】request:{}",request);
        if(!redisUtil.setLockIfAbsent(LOCK_PRODUCTION_SUBMIT,request.getOrganizationId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try {
            estimateV2Service.productionSubmit(request);
        }finally {
            redisUtil.unLock(LOCK_PRODUCTION_SUBMIT,request.getOrganizationId());
        }
        return Response.success();
    }

    @Override
    public Response<EstimateButton> checkButton(CheckButtonRequest request) {
        log.info("【产销按钮检查】request:{}",request);
        EstimateButton estimateButton = estimateCheckService.checkButton(request);
        return Response.success(estimateButton);
    }

    @Override
    public Response<CommitTimesVo> times(CheckButtonRequest request) {
        log.info("【产销按钮检查】request:{}",request);
        CommitTimesVo vo = estimateCheckService.times(request);
        return Response.success(vo);
    }

    @Override
    public Response finalSubmit(FinalConfirmRequest request) {
        log.info("【最终确认】request:{}",request);
        if(!redisUtil.setLockIfAbsent(LOCK_COMPANY_SUBMIT,request.getOrganizationId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try{
            estimateV2Service.finalSubmit(request);
        }finally{
            redisUtil.unLock(LOCK_COMPANY_SUBMIT,request.getOrganizationId());
        }

        return Response.success();
    }

    @Override
    public Response dismissedV2(DismissedRequest request) {
        log.info("【销售预估驳回】request:{}",request);
        estimateV2Service.dismissed(request);
        return Response.success();
    }

    @Override
    public Response<List<EstimateAggregationVo>> searchCompany(FinalConfirmRequest request) {
        log.info("【销售预估查询(分公司)】request:{}",request);
        List<EstimateAggregationVo> list = estimateSearchV2Service.searchCompany(request);
        return Response.success(list);
    }

    @Override
    public Response<Page<EstimateSkuAggregationVo>> searchSku(EstimateSkuSearchRequest request) {
        log.info("【销售预估查询(物料)】request:{}",request);
        Page<EstimateSkuAggregationVo> page = estimateSearchV2Service.searchSku(request);
        return Response.success(page);
    }


    @Override
    public Response<EstimateConfirmVo> estimateConfirmList(Integer estimateApplyId) {
        log.info("【销售预估确认】request:{}",estimateApplyId);
        EstimateConfirmVo vo = estimateSearchV2Service.estimateConfirmList(estimateApplyId);
        return Response.success(vo);
    }

    @Override
    public Response<EstimateSkuConfirmVo> estimateConfirmSkuList(SkuConfirmReuqest request) {
        log.info("【销售预估物料确认】request:{}",request);
        EstimateSkuConfirmVo vo = estimateSearchV2Service.estimateConfirmSkuList(request);
        return Response.success(vo);
    }

    @Override
    public Response departmentCache(CompanyEstimateSubmitRequest request) {
        log.info("【销售预估区域经理保存】request{}",request);
        estimateV2Service.departmentCache(request);
        return Response.success();
    }


    @Override
    public Response<List<EstimatePositionVo>> estimateEmpList(EstimateEmpListRequest request) {
        log.info("【销售预估按合伙人查询】request:{}",request);

        List<EstimatePositionVo> list = estimateSearchService.estimatePositionList(request);

        return Response.success(list);
    }

    @Override
    public Response<EstimateSubmitVo> estimateSubmitCheck(EstimateEmpListRequest request) {
        log.info("【销售预估最终提报检查】request:{}",request);
        EstimateSubmitVo vo = estimateCheckService.estimateSubmitCheck(request);
        return Response.success(vo);
    }

    @Override
    public Response<List<EstimateStatisticsVo>> estimateGoodsList(EstimateGoodsRequest request) {
        log.info("【销售预估按物料查询】request:{}",request);

        List<EstimateStatisticsVo> list = estimateSearchService.estimateGoodsList(request);
        return Response.success(list);
    }



    @Override
    public Response<List<SkuInfoVo>> skuList(SkuSearchRequest request) {
        List<SkuInfoVo> list = estimateSearchService.skuList(request);
        return  Response.success(list);
    }

    @Override
    public Response<List<CeoInfoVo>> ceoList(CeoSearchRequest request) {
        log.info("【销售预估合伙人信息获取】request:{}",request);

        List<CeoInfoVo> list = estimateSearchService.ceoList(request);
        return Response.success(list);
    }

    @Override
    public Response cache(CacheEstimateRequest request) {
        log.info("【销售预估修改信息】request:{}",request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);


        estimateService.cache(personInfo,request);
        return Response.success();
    }

    @Override
    public Response dismissed(ValidExcuteRequest request) {
        log.info("【销售预估审核驳回】request:{}",request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);


        estimateAuditService.dismissed(personInfo,request);
        return Response.success();
    }


    @Override
    public Response submit(ValidExcuteRequest request) {
        log.info("【销售预估审核提交】request:{}",request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);


        estimateAuditService.submit(personInfo,request);
        return Response.success();
    }

    @Override
    public Response finalConfirm(ValidExcuteRequest request) {
        log.info("【销售预估最终确认】:{}",request);

        estimateAuditService.finalConfirm(request);
        return Response.success();
    }

    @Override
    public Response<AreaConfirmVo> getConfirm(EstimateGoodsRequest request) {
        AreaConfirmVo areaConfirm = estimateSearchService.getConfirm(request);
        return Response.success(areaConfirm);
    }

    @Override
    public Response<List<StoreVo>> getStore(EstimateStoreRequest request) {
        List<StoreVo> list = estimateSearchService.getStore(request);
        return Response.success(list);
    }

}
