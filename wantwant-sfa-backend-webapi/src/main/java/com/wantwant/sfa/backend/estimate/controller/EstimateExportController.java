package com.wantwant.sfa.backend.estimate.controller;


import com.wantwant.sfa.backend.estimate.service.IEstimateExportService;
import com.wantwant.sfa.backend.estimated.api.EstimateExportApi;
import com.wantwant.sfa.backend.estimated.request.EstimateSkuSearchRequest;
import com.wantwant.sfa.backend.estimated.request.FinalConfirmRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/05/上午10:39
 */
@RestController
@Slf4j
public class EstimateExportController implements EstimateExportApi {
    @Autowired
    private IEstimateExportService estimateExportService;

    @Override
    public void company(FinalConfirmRequest request) {
        log.info("【销售预估导出】request:{}",request);
        estimateExportService.companyExport(request);
    }

    @Override
    public void sku(EstimateSkuSearchRequest request) {
        log.info("【销售预估导出】request:{}",request);
        estimateExportService.skuExport(request);
    }
}
