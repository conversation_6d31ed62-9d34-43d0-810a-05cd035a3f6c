package com.wantwant.sfa.backend.estimate.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.abnormal.vo.LineVO;
import com.wantwant.sfa.backend.estimate.service.IEstimateSummaryService;
import com.wantwant.sfa.backend.estimated.api.EstimateSearchApi;
import com.wantwant.sfa.backend.estimated.request.*;
import com.wantwant.sfa.backend.estimated.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/26/上午11:45
 */
@RestController
@Slf4j
public class EstimateSearchController implements EstimateSearchApi {
    @Autowired
    private IEstimateSummaryService estimateSummaryService;

    @Override
    public Response<List<EstimateSummaryVo>> summary(EstimateSearchRequest request) {
        log.info("【search estimate summary】request:{}",request);

        List<EstimateSummaryVo> list = estimateSummaryService.summary(request);

        return Response.success(list);
    }

    @Override
    public void summaryExport(EstimateSearchRequest request) {
        log.info("【search estimate export】request:{}",request);
        estimateSummaryService.summaryExport(request);
    }

    @Override
    public Response<Page<EstimateAreaSkuVo>> areaSkuSearch(EstimateSearchSkuRequest request) {
        log.info("【search estimate area】request:{}",request);
        Page<EstimateAreaSkuVo> page = estimateSummaryService.areaSkuSearch(request);
        return Response.success(page);
    }

    @Override
    public void areaSkuSearchExport(EstimateSearchSkuRequest request) {
        log.info("【search estimate area】request:{}",request);
        estimateSummaryService.areaSkuSearchExport(request);
    }

    @Override
    public Response<Page<EstimateCompanySkuVo>> companySkuSearch(EstimateSearchSkuRequest request) {
        log.info("【search estimate company】request:{}",request);

        Page<EstimateCompanySkuVo> page = estimateSummaryService.companySkuSearch(request);

        return Response.success(page);
    }

    @Override
    public void companySkuSearchExport(EstimateSearchSkuRequest request) {
        log.info("【search estimate company】request:{}",request);
        estimateSummaryService.companySkuSearchExport(request);
    }

    @Override
    public Response<List<SkuVo>> getSkuInfo(String month) {
        log.info("【search sku】month:{}",month);
        List<SkuVo> list = estimateSummaryService.getSkuInfo(month);
        return Response.success(list);
    }

    @Override
    public Response<List<String>> getTags() {
        List<String> list = estimateSummaryService.getTags();
        return Response.success(list);
    }

    @Override
    public Response<List<String>> getStore() {
        List<String> list = estimateSummaryService.getStore();
        return Response.success(list);
    }

    @Override
    public Response<EstimateSearchVo> searchList(EstimateSearchV2Request request) {
        log.info("【search list】request:{}",request);

        EstimateSearchVo vo = estimateSummaryService.searchList(request);
        return Response.success(vo);
    }

    @Override
    public void estimateSearchExport(EstimateSearchV2Request request) {
        log.info("【search list export】request:{}",request);

        estimateSummaryService.estimateSearchExport(request);
    }

    @Override
    public Response<List<String>> selectUnCommit(EstimateSearchRequest request) {
        log.info("【select UnCommit】request:{}",request);
        List<String> list = estimateSummaryService.selectUnCommit(request);
        return Response.success(list);
    }

    @Override
    public Response<List<EstimateAssessVo>> selectAssess(EstimateAssessRequest request) {
        log.info("【select Assess】request:{}",request);
        List<EstimateAssessVo> list =  estimateSummaryService.selectAssess(request);
        return Response.success(list);
    }

    @Override
    public Response estimatePresenting(List<EstimatePresentingRequest> request) {
        log.info("【estimate Presenting】request:{}",request);
        estimateSummaryService.estimatePresenting(request);
        return Response.success();
    }

    @Override
    public Response<List<LineVO>> selectAssessParameter(String theYearMon) {
        log.info("【estimate AssessParameter】theYearMon:{}",theYearMon);
        return estimateSummaryService.selectAssessParameter(theYearMon);
    }

    @Override
    public void selectAssessExport(EstimateAssessRequest request) {
        log.info("【estimate AssessExport】request:{}",request);
        estimateSummaryService.selectAssessExport(request);
    }
}
