package com.wantwant.sfa.backend.estimate.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.EstimateApplication;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.estimate.DO.*;
import com.wantwant.sfa.backend.estimate.api.EstimateV3Api;
import com.wantwant.sfa.backend.estimate.assemble.EstimateAssemble;
import com.wantwant.sfa.backend.estimate.request.*;
import com.wantwant.sfa.backend.estimate.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/下午6:56
 */
@RestController
@Slf4j
public class EstimateV3Controller implements EstimateV3Api {

    @Resource
    private EstimateApplication estimateApplication;
    @Resource
    private EstimateAssemble estimateAssemble;

    @Override
    public Response<List<EstimateSkuGroupVO>> selectSkuGroup() {
        List<EstimateSkuGroupVO> list = estimateApplication.selectSkuGroup();
        return Response.success(list);
    }

    @Override
    public Response saveSkuGroup(@Valid EstimateSkuGroupRequest estimateSkuGroupRequest) {
        log.info("【save sku group】request:{}",JSONObject.toJSONString(estimateSkuGroupRequest));


        EstimateSkuGroupDO estimateSkuGroupDO = estimateAssemble.convertEstimateSkuGroupDO(estimateSkuGroupRequest);
        estimateSkuGroupDO.setBusinessGroup(RequestUtils.getBusinessGroup());
        estimateApplication.saveSkuGroup(estimateSkuGroupDO,estimateSkuGroupRequest.getPerson());
        return Response.success();
    }

    @Override
    public Response updateStatus(@Valid EstimateGroupUpdateStatusRequest estimateGroupUpdateStatusRequest) {
        log.info("【update sku group status】request:{}",JSONObject.toJSONString(estimateGroupUpdateStatusRequest));

        estimateApplication.updateStatus(estimateGroupUpdateStatusRequest);

        return Response.success();
    }

    @Override
    public Response<EstimateSkuGroupDetailVO> getSkuGroupDetail(Long groupId) {
        log.info("【get sku group detail】groupId:{}",groupId);

        EstimateSkuGroupDetailVO estimateSkuGroupDetailVO =  estimateApplication.getSkuGroupDetail(groupId);

        return Response.success(estimateSkuGroupDetailVO);
    }

    @Override
    public Response<List<EstimateSkuVO>> catchSku(Long shipPeriodId) {
        List<EstimateSkuVO> list = estimateApplication.catchSku(RequestUtils.getBusinessGroup(),shipPeriodId);
        return Response.success(list);
    }

    @Override
    public Response<List<EstimateScheduleVO>> selectSchedule(String yearMonth) {
        log.info("【select schedule】yearMonth:{}",yearMonth);

        List<EstimateScheduleVO> list = estimateApplication.selectSchedule(yearMonth,RequestUtils.getBusinessGroup());
        return Response.success(list);
    }

    @Override
    public Response saveSchedule(EstimateScheduleRequest estimateScheduleRequest) {
        log.info("【save estimate schedule】request:{}", JSONObject.toJSONString(estimateScheduleRequest));

        estimateApplication.saveSchedule(estimateAssemble.convertEstimateScheduleDO(estimateScheduleRequest),estimateScheduleRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response<List<EstimateSkuGroupSelectVO>> selectEstimateGroup() {

        List<EstimateSkuGroupSelectVO> list = estimateApplication.selectEstimateGroup();
        return Response.success(list);
    }

    @Override
    public Response<EstimateScheduleDetailVO> getEstimateScheduleDetail(Long scheduleId) {
        log.info("【get estimate schedule detail】scheduleId:{}",scheduleId);

        EstimateScheduleDetailVO estimateScheduleDetailVO = estimateApplication.getEstimateScheduleDetail(scheduleId);
        return Response.success(estimateScheduleDetailVO);
    }

    @Override
    public Response<List<EstimateOrganizationVO>> selectEstimateOrg(EstimateOrgSearchRequest estimateOrgSearchRequest) {
        log.info("【select estimate org】request:{}",JSONObject.toJSONString(estimateOrgSearchRequest));
        EstimateOrgSearchDO estimateOrgSearchDO = estimateAssemble.convertEstimateOrgSearchDO(estimateOrgSearchRequest);
        estimateOrgSearchDO.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<EstimateOrganizationVO> list = estimateApplication.selectEstimateOrg(estimateOrgSearchDO);
        return Response.success(list);
    }

    @Override
    public void exportSkuList(@Valid EstimateSkuExportRequest estimateSkuExportRequest) {
        estimateApplication.exportSkuList(estimateSkuExportRequest);
    }

    @Override
    public Response<EstimateApprovalInfoVO> selectApprovalList(@Valid EstimateApprovalSearchRequest estimateApprovalSearchRequest) {
        log.info("【select approval list】request:{}",JSONObject.toJSONString(estimateApprovalSearchRequest));

        EstimateApprovalInfoVO estimateApprovalInfoVO =  estimateApplication.selectApprovalList(estimateApprovalSearchRequest);

        return Response.success(estimateApprovalInfoVO);
    }

    @Override
    public void estimateApprovalExport(@Valid EstimateApprovalSearchRequest estimateApprovalSearchRequest) {
        estimateApplication.estimateApprovalExport(estimateApprovalSearchRequest);

    }

    @Override
    public Response<EstimateApprovalDetailVO> getEstimateApprovalDetail(Long approvalId, Integer mode) {
        log.info("【get approval detail】approval:{},mode:{}",approvalId,mode);

        EstimateApprovalDetailVO estimateApprovalDetailVO = estimateApplication.getEstimateApprovalDetail(approvalId,mode);
        return Response.success(estimateApprovalDetailVO);
    }

    @Override
    public Response<EstimateApprovalDetailVO> selectSubmitSku(@Valid EstimateSubmitSkuRequest estimateSubmitSkuRequest) {
        log.info("【select " +
                " sku】request:{}",JSONObject.toJSONString(estimateSubmitSkuRequest));

        EstimateApprovalDetailVO estimateApprovalDetailVO =  estimateApplication.selectSubmitSku(estimateSubmitSkuRequest);
        return Response.success(estimateApprovalDetailVO);
    }

    @Override
    public Response pass(@Valid EstimateApprovalPassRequest estimateApprovalPassRequest) {
        log.info("【estimate approval pass】request:{}",JSONObject.toJSONString(estimateApprovalPassRequest));

        estimateApplication.pass(estimateAssemble.convertEstimateApprovalPassDO(estimateApprovalPassRequest),estimateApprovalPassRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response reject(@Valid EstimateApprovalRejectRequest estimateApprovalRejectRequest) {
        log.info("【estimate approval reject】request:{}",JSONObject.toJSONString(estimateApprovalRejectRequest));

        estimateApplication.reject(estimateAssemble.convertEstimateApprovalRejectDO(estimateApprovalRejectRequest),estimateApprovalRejectRequest.getPerson());


        return Response.success();
    }

    @Override
    public Response<List<EstimateSubmitVO>> selectEstimateSubmit(@Valid EstimateSubmitSearchRequest estimateSubmitSearchRequest) {
        log.info("【estimate select submit】request:{}", JSONObject.toJSONString(estimateSubmitSearchRequest));


        List<EstimateSubmitVO> list = estimateApplication.selectEstimateSubmit(estimateSubmitSearchRequest);

        return Response.success(list);
    }

    @Override
    public Response submit(@Valid EstimateSubmitRequest estimateSubmitRequest) {
        log.info("【estimate submit】request:{}",JSONObject.toJSONString(estimateSubmitRequest));

        estimateApplication.submit(estimateAssemble.convertEstimateSubmit(estimateSubmitRequest),estimateSubmitRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response batch(@Valid EstimateBatchApprovalRequest estimateBatchApprovalRequest) {
        log.info("【estimate batch approval】request:{}",JSONObject.toJSONString(estimateBatchApprovalRequest));

        estimateApplication.batch(estimateBatchApprovalRequest);

        return  Response.success();
    }

    @Override
    public Response<List<StoreVO>> getStore() {

        List<StoreVO> storeVOList = estimateApplication.getStore(RequestUtils.getBusinessGroup());
        return Response.success(storeVOList);
    }

    @Override
    public Response<List<SkuVO>> getSku( boolean ignoreBusinessGroup) {
        Integer businessGroup = null;
        if(!ignoreBusinessGroup){
            businessGroup = RequestUtils.getBusinessGroup();
        }
        List<SkuVO> skuVOList = estimateApplication.getSku(businessGroup);
        return Response.success(skuVOList);
    }

    @Override
    public Response<IPage<EstimateSummaryVO>> selectSummary(@Valid EstimateSearchRequest estimateSearchRequest) {
        estimateSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());


        log.info("【estimate select summary】request:{}",JSONObject.toJSONString(estimateSearchRequest));



        IPage<EstimateSummaryVO> page = estimateApplication.selectSummary(estimateSearchRequest);

        return Response.success(page);
    }

    @Override
    public void exportSummary(@Valid EstimateSearchRequest estimateSearchRequest) {
        estimateApplication.exportSummary(estimateSearchRequest);
    }

    @Override
    public Response<EstimateDetailInfoVO> selectDetail(@Valid EstimateSearchRequest estimateSearchRequest) {
        log.info("【estimate select detail】request:{}",JSONObject.toJSONString(estimateSearchRequest));

        EstimateDetailInfoVO estimateDetailInfoVO = estimateApplication.selectDetail(estimateSearchRequest);

        return Response.success(estimateDetailInfoVO);
    }

    @Override
    public void exportDetail(@Valid EstimateSearchRequest estimateSearchRequest) {
        log.info("【estimate export detail】request:{}",JSONObject.toJSONString(estimateSearchRequest));

        estimateApplication.exportDetail(estimateSearchRequest);
    }

    @Override
    public Response setShipPeriod(@Valid EstimateShipPeriodRequest estimateShipPeriodRequest) {
        log.info("【set ship period】request:{}",JSONObject.toJSONString(estimateShipPeriodRequest));

        EstimateShipPeriodDO estimateShipPeriodDO =  estimateAssemble.covertShipPeriod(estimateShipPeriodRequest);

        estimateApplication.setShipPeriod(estimateShipPeriodDO,estimateShipPeriodRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response setShipPeriodStatus(@Valid ShipPeriodOperateRequest shipPeriodOperateRequest) {
        log.info("【set ship period status】request:{}",JSONObject.toJSONString(shipPeriodOperateRequest));
        EstimateShipPeriodDO estimateShipPeriodDO =  estimateAssemble.covertShipPeriod2(shipPeriodOperateRequest);

        estimateApplication.setShipPeriod(estimateShipPeriodDO,shipPeriodOperateRequest.getPerson());
        return Response.success();
    }

    @Override
    public Response<List<ShipPeriodVO>> selectShipPeriod(Integer status) {
        List<ShipPeriodVO> list = estimateApplication.selectShipPeriod(status);
        return Response.success(list);
    }

    @Override
    public Response<IPage<EstimateMOQVO>> selectMOQ(@Valid MOQSearchRequest moqSearchRequest) {
        log.info("【select MOQ】request:{}",JSONObject.toJSONString(moqSearchRequest));
        IPage<EstimateMOQVO> page = estimateApplication.selectMOQ(moqSearchRequest);
        return Response.success(page);
    }

    @Override
    public Response<MOQDetailVO> getMOQDetail(String yearMonth, Long shipPeriodId, String sku) {
        log.info("【get MOQ detail】yearMonth:{},shipPeriodId:{},sku:{}",yearMonth,shipPeriodId,sku);

        MOQDetailVO moqDetailVO = estimateApplication.getMOQDetail(yearMonth,shipPeriodId,sku);
        return Response.success(moqDetailVO);
    }

    @Override
    public Response moqAudit(@Valid EstimateAdjustRequest estimateAdjustRequest) {
        log.info("【estimate moq audit】request:{}",JSONObject.toJSONString(estimateAdjustRequest));

        EstimateAdjustDO estimateAdjustDO =  estimateAssemble.convertMOQAuditRequest(estimateAdjustRequest);
        estimateApplication.moqAudit(estimateAdjustDO,estimateAdjustRequest.getPerson());
        return Response.success();
    }

    @Override
    public Response<IPage<EstimateAdjustVO>> selectAdjust(EstimateAdjustSearchRequest estimateAdjustSearchRequest) {
        log.info("【select adjust】request:{}",JSONObject.toJSONString(estimateAdjustSearchRequest));

        IPage<EstimateAdjustVO> page = estimateApplication.selectAdjust(estimateAdjustSearchRequest);
        return Response.success(page);
    }

    @Override
    public Response<EstimateAdjustDetailVO> getEstimateAdjustDetail(AdjustDetailSearchRequest adjustDetailSearchRequest) {
        log.info("【get adjust detail】request:{}",JSONObject.toJSONString(adjustDetailSearchRequest));

        EstimateAdjustDetailVO estimateAdjustDetailVO = estimateApplication.getEstimateAdjustDetail(adjustDetailSearchRequest);

        return Response.success(estimateAdjustDetailVO);
    }

    @Override
    public Response adjust(@Valid AdjustRequest adjustRequest) {
        log.info("【estimate adjust】request:{}",JSONObject.toJSONString(adjustRequest));
        AdjustDO adjustDO = estimateAssemble.convertAdjust(adjustRequest);
        estimateApplication.adjust(adjustDO,adjustRequest.getPerson());
        return Response.success();
    }

    @Override
    public Response<EstimatePermissionVO> getEstimatePermission(String person) {
        log.info("【get estimate permission】person:{}",person);
        EstimatePermissionVO estimatePermissionVO = estimateApplication.getEstimatePermission(person);
        return Response.success(estimatePermissionVO);
    }

    @Override
    public Response<EstimateControlVO> selectEstimateControl(EstimateControlSearchRequest estimateControlSearchRequest) {
        log.info("select estimate control request:{}", JSONObject.toJSONString(estimateControlSearchRequest));

        EstimateControlVO estimateControlVO = estimateApplication.selectEstimateControl(estimateControlSearchRequest);
        return Response.success(estimateControlVO);
    }
}
