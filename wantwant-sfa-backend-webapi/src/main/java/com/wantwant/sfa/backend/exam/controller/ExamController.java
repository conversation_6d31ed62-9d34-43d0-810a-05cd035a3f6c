package com.wantwant.sfa.backend.exam.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.exam.api.ExamApi;
import com.wantwant.sfa.backend.exam.request.ExamNotifyRequest;
import com.wantwant.sfa.backend.exam.service.IExamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/01/05/上午9:13
 */
@RestController
@Slf4j
public class ExamController implements ExamApi {
    @Autowired
    private IExamService examService;

    @Override
    public Response<List<String>> examNotify(ExamNotifyRequest request) {
        log.info("【exam notify】request:{}",request);
        List<String> list = examService.examNotify(request);
        return Response.success(list);
    }
}
