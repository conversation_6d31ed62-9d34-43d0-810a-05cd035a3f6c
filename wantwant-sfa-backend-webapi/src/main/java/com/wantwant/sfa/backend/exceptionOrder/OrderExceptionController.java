package com.wantwant.sfa.backend.exceptionOrder;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.exceptionOrder.api.OrderExceptionApi;
import com.wantwant.sfa.backend.exceptionOrder.request.OrderExceptionProcessRequest;
import com.wantwant.sfa.backend.exceptionOrder.request.OrderExceptionSearchRequest;
import com.wantwant.sfa.backend.exceptionOrder.vo.ExceptionOrderDetailVO;
import com.wantwant.sfa.backend.exceptionOrder.vo.ExceptionOrderVO;
import com.wantwant.sfa.backend.exceptionOrder.vo.OrderExceptionVO;
import com.wantwant.sfa.backend.service.OrderExceptionService;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/**
 * @Description: 异常订单controller。
 * @Auther: zhengxu
 * @Date: 2021/08/11/下午2:57
 */
@RestController
public class OrderExceptionController implements OrderExceptionApi {
    @Autowired
    private OrderExceptionService orderExceptionService;
    @Override
    public Response upload(MultipartFile file, String employId) throws IOException {
        try {
            orderExceptionService.upload(file,employId);
        } catch (Exception e) {
            Response.error("导入失败!");
        }
        return Response.success();
    }



    @Override
    public Response<OrderExceptionVO> alertInfo() {
        OrderExceptionVO orderExceptionVO = orderExceptionService.alertInfo();
        return Response.success(orderExceptionVO);
    }

    @Override
    public Response<Page<ExceptionOrderVO>> list(OrderExceptionSearchRequest request) {
        Page<ExceptionOrderVO> list = orderExceptionService.list(request);
        return Response.success(list);
    }

    @Override
    public Response<List<String>> getExceptionItem() {
        List<String> exceptionItem = orderExceptionService.getExceptionItem();
        return Response.success(exceptionItem);
    }


    @Override
    public Response<ExceptionOrderDetailVO> getOrderDetail(String orderKey) {
        ExceptionOrderDetailVO vo = orderExceptionService.getOrderDetail(orderKey);
        return Response.success(vo);
    }

    @Override
    public Response process(OrderExceptionProcessRequest request) {
        orderExceptionService.process(request);
        return  Response.success();
    }

    @Override
    public Response<ExceptionOrderDetailVO> getOrderDetailPage(OrderExceptionSearchRequest request) {
        ExceptionOrderDetailVO orderDetailPage = orderExceptionService.getOrderDetailPage(request);
        return Response.success(orderDetailPage);
    }
}
