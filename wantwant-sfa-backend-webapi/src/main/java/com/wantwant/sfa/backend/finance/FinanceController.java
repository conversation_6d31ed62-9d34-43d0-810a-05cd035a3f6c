package com.wantwant.sfa.backend.finance;


import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.finance.request.FinanceRecordRequest;
import com.wantwant.sfa.backend.model.finance.FinanceExpenseRecord;
import com.wantwant.sfa.backend.service.FinanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description 财务数据导入
 * <AUTHOR>
 * @Date 2023/2/24
 **/
@RestController
@Slf4j
public class FinanceController {

    @Autowired
    private FinanceService financeService;

    @ApiOperation(value = "财务出差记录" ,notes = "财务出差记录" ,httpMethod = "GET")
    @GetMapping("/finance/record")
    Response<List<FinanceExpenseRecord>> getFinanceRecord(FinanceRecordRequest request) {
        log.info("getFinanceRecord request:{}", request);
        List<FinanceExpenseRecord> list = financeService.queryFinanceRecord(request);
        return Response.success(list);
    }

    @ApiOperation(notes = "批量导入财务报销记录", value = "批量导入财务报销记录")
    @PostMapping("/finance/importFinanceExpenseRecord")
    public Response importExpenseRecord(@RequestParam(value = "file") MultipartFile file,
                                                      @ApiParam(value = "操作人ID", required = true) @RequestParam(value = "person") String person){
        log.info("importManagerSalary  person:{}", person);
        financeService.importFinanceExpenseRecord(file,person);
        return Response.success();
    }

}

