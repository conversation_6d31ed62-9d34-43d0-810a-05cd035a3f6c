package com.wantwant.sfa.backend.goal.controller;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.goal.request.ExcelQueryRequest;
import com.wantwant.sfa.backend.goal.vo.SalesGoalVo;
import com.wantwant.sfa.backend.util.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.goal.api.GoalApi;
import com.wantwant.sfa.backend.goal.request.ExcelRequest;
import com.wantwant.sfa.backend.goal.request.GoalExcelIdRequest;
import com.wantwant.sfa.backend.goal.request.GoalRequest;
import com.wantwant.sfa.backend.goal.vo.GoalExcelVo;
import com.wantwant.sfa.backend.goal.vo.GoalsVo;
import com.wantwant.sfa.backend.goal.vo.SfaGoalsVo;
import com.wantwant.sfa.backend.service.IGoalService;
import com.wantwant.sfa.backend.util.PageParam;
import com.wantwant.sfa.backend.util.RedisUtil;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import lombok.extern.slf4j.Slf4j;


/**
 * @Description 目标数据
 * <AUTHOR>
 * @Date 2020/7/13
 **/
@RestController
@Slf4j
public class GoalController implements GoalApi {
	
	@Autowired
	IGoalService goalService;

    @Autowired
    private RedisUtil redisUtil;
    
    //目标变更共用key
    public static final String LOCK_HEAD_CHANGE_GOAL = "changeGoalLock";

	/**
	 * 导入目标数据
	 */
	@Override
	public Response<Integer> goalUpload(MultipartHttpServletRequest request) {
   	log.info("start GoalController goalUpload request:{}",request);
		
		LocalDate startDate;
		LocalDate endDate;

		try{
			startDate = LocalDate.parse(request.getParameter("startDate")+"-01");
//			endDate = LocalDate.parse(request.getParameter("endDate")+"-01").with(TemporalAdjusters.lastDayOfMonth());

			endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());

		}catch(Exception e) {
			throw new ApplicationException("时间格式错误！");
		}

//		if(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).isAfter(startDate)) {
//			throw new ApplicationException("开始时间早于当前月份");
//		}else if(startDate.isAfter(endDate)) {
//			throw new ApplicationException("结束时间早于开始时间");
//		}

		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
	        return Response.error("请求正在处理中！～");
	    }

		try {
			String person = request.getParameter("person");
			int type = Integer.parseInt( request.getParameter("type"));
			
			ImportParams params = new ImportParams();
	
			 Map<String, MultipartFile> fileMap = request.getFileMap();
			 
			 try {
					 int count = 0;
					 
					 for(String key : fileMap.keySet()) {
						
						 MultipartFile multipartFile = fileMap.get(key);
						 
						 List<SfaGoalsVo> res =  ExcelImportUtil.importExcel(multipartFile.getInputStream(), SfaGoalsVo.class, params);
		
						GoalExcelVo goalExcel = new GoalExcelVo();
						goalExcel.setName(key);
						goalExcel.setPerson(person);
						goalExcel.setType(type);
						goalExcel.setStartDate(startDate);
						goalExcel.setEndDate(endDate);
		
						 count = goalService.insertByExcel(goalExcel,res);
					 }

					 return Response.success(count);
					 
				 }catch (Exception e) {
					 log.error("导入失败",e);
			         return Response.error("导入失败");
				}
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}
	}

	/**
	 * 导入目标数据
	 */
	@Override
	public Response<Integer> goalUploadFor123(MultipartHttpServletRequest request) {
		log.info("start GoalController goalUploadFor123 request:{}",request);

		LocalDate startDate;
		LocalDate endDate;

		try{
			startDate = LocalDate.parse(request.getParameter("startDate")+"-01");
//			endDate = LocalDate.parse(request.getParameter("endDate")+"-01").with(TemporalAdjusters.lastDayOfMonth());

			endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());

		}catch(Exception e) {
			throw new ApplicationException("时间格式错误！");
		}

//		if(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).isAfter(startDate)) {
//			throw new ApplicationException("开始时间早于当前月份");
//		}else if(startDate.isAfter(endDate)) {
//			throw new ApplicationException("结束时间早于开始时间");
//		}

		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
			return Response.error("请求正在处理中！～");
		}

		try {
			String person = request.getParameter("person");
			int type = Integer.parseInt( request.getParameter("type"));

			ImportParams params = new ImportParams();

			Map<String, MultipartFile> fileMap = request.getFileMap();

			try {
				int count = 0;

				for(String key : fileMap.keySet()) {

					MultipartFile multipartFile = fileMap.get(key);

					List<SfaGoalsVo> res =  ExcelImportUtil.importExcel(multipartFile.getInputStream(), SfaGoalsVo.class, params);
					res = res.stream().filter(e-> CommonUtil.StringUtils.isEmpty(e.getOrganizationId()) == false).collect(Collectors.toList());
					GoalExcelVo goalExcel = new GoalExcelVo();
					goalExcel.setName(key);
					goalExcel.setPerson(person);
					goalExcel.setType(type);
					goalExcel.setStartDate(startDate);
					goalExcel.setEndDate(endDate);

					count = goalService.insertByExcel(goalExcel,res);
				}

				return Response.success(count);

			}catch (Exception e) {
				log.error("导入失败",e);
				return Response.error("导入失败");
			}
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}
	}

	/**
	 * 业务目标信息列表
	 */
	@Override
	public Response<Page<GoalExcelVo>> excelList(ExcelQueryRequest request) {
		log.info("start GoalController excelList request:{}",request);
		
		Page<GoalExcelVo> list = goalService.excelList(request);
		
		return Response.success(list);
	}

	/**
	 * 123业务目标信息列表
	 */
	@Override
	public Response<Page<GoalExcelVo>> excelListFor123(ExcelQueryRequest request) {
		log.info("start GoalController excelListFor123 request:{}",request);

		Page<GoalExcelVo> list = goalService.excelList(request);

		return Response.success(list);
	}

	/**
	 * 删除目标信息
	 */
	@Override
	public Response deleteExcel(ExcelRequest request) {
		log.info("start GoalController deleteExcel request:{}",request);

		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

		try {
			goalService.deleteExcel(request, 1);
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}
		
		return Response.success();
	}

	/**
	 * 123删除目标信息
	 */
	@Override
	public Response deleteExcelFor123(ExcelRequest request) {
		log.info("start GoalController deleteExcelFor123 request:{}",request);

		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
			return Response.error("请求正在处理中！～");
		}

		try {
			goalService.deleteExcel(request, RequestUtils.getChannel());
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}

		return Response.success();
	}

	/**
	 * 修改目标信息
	 */
	@Override
	public Response updateExcel(ExcelRequest request) {
		log.info("start GoalController updateExcel request:{}",request);

		LocalDate startDate = LocalDate.parse(request.getStartDate()+"-01");
		LocalDate endDate = LocalDate.parse(request.getEndDate()+"-01").with(TemporalAdjusters.lastDayOfMonth());
				
		if(!request.getStartDate().equals(request.getEndDate())) {
			throw new ApplicationException("时间不能跨月");
		}
		
		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
		
		try {
			request.setStartDate(startDate.toString());
			request.setEndDate(endDate.toString());
	
			goalService.updateExcel(request);
			
			return Response.success();
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}
	}

	@Override
	public Response<Page<GoalsVo>> goalList(GoalExcelIdRequest request) {
		log.info("start GoalController goalList request:{}",request);
		
		Page<GoalsVo> page = goalService.goalList(request, 1);

		return Response.success(page);
	}

	@Override
	public Response<Page<GoalsVo>> goalListFor123(GoalExcelIdRequest request) {
		log.info("start GoalController goalListFor123 request:{}",request);

		Page<GoalsVo> page = goalService.goalList(request, RequestUtils.getChannel());

		return Response.success(page);
	}

	@Override
	public Response updateGoal(GoalRequest request) {
		log.info("start GoalController updateGoal request:{}",request);
       
		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
		
		try {
			goalService.updateGoal(request);
			
			return Response.success();
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}
	}

	@Override
	public Response updateGoalFor123(GoalRequest request) {
		log.info("start GoalController updateGoalFor123 request:{}",request);

		if(! redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_GOAL,"1",5, TimeUnit.SECONDS)){
			return Response.error("请求正在处理中！～");
		}

		try {
			goalService.updateGoal(request);

			return Response.success();
		}finally {
			redisUtil.unLock(LOCK_HEAD_CHANGE_GOAL,"1");
		}
	}

	@Override
	public Response<SalesGoalVo> selectSalesGoalByMemberKey(Long memberKey,String month) {
		log.info("startselectSalesGoalByMemberKey memberKey:{},month:{}",memberKey,month);


		SalesGoalVo salesGoalVo = goalService.selectSalesGoalByMemberKey(memberKey, month);

		return Response.success(salesGoalVo);
	}


}
