package com.wantwant.sfa.backend.gold.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.gold.api.GoldApi;
import com.wantwant.sfa.backend.gold.request.GoldApplyDetailUpdateRequest;
import com.wantwant.sfa.backend.gold.request.GoldApprovalRequest;
import com.wantwant.sfa.backend.gold.request.GoldCumulativeRequest;
import com.wantwant.sfa.backend.gold.service.IGoldService;
import com.wantwant.sfa.backend.gold.service.IGoldTypeService;
import com.wantwant.sfa.backend.gold.vo.ExpenseVo;
import com.wantwant.sfa.backend.gold.vo.GoldCumulativeVo;
import com.wantwant.sfa.backend.gold.vo.GoldImportResult;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/07/下午2:29
 */
@RestController
@Slf4j
public class GoldController implements GoldApi {
    private static final String APPROVAL_LOCAL = "gold:approval";
    @Autowired
    private IGoldService goldService;
    @Autowired
    private IGoldTypeService goldTypeService;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Response upload(MultipartFile file, int type,String person) {
        List<String> errList = goldService.upload(file,type, person);
        if(!CollectionUtils.isEmpty(errList)){
            return Response.success(errList);
        }
        return Response.success();
    }


    @Override
    public Response deleteDetailById(Long applyDetailId) {
        goldService.deleteDetailById(applyDetailId);
        return Response.success();
    }

    @Override
    public Response<GoldImportResult> approval(@Valid GoldApprovalRequest request) {
        if(!redisUtil.setLockIfAbsent(APPROVAL_LOCAL, request.getApplyId().toString(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            GoldImportResult approval = goldService.approval(request);
            return Response.success(approval);
        }finally {
            redisUtil.unLock(APPROVAL_LOCAL,request.getApplyId().toString());
        }

    }

    @Override
    public Response dismissed(@Valid GoldApprovalRequest request) {
        goldService.dismissed(request);
        return Response.success();
    }

    @Override
    public Response<List<ExpenseVo>> expensesType(Integer coinsType) {
        List<ExpenseVo> expenseVos = goldTypeService.selectExpenseTypeByCoins(coinsType);
        return Response.success(expenseVos);
    }

    @Override
    public Response<List<GoldCumulativeVo>> cumulative(GoldCumulativeRequest request) {
       List<GoldCumulativeVo> list = goldTypeService.selectGoldTypeService(request);
        return Response.success(list);
    }

    @Override
    public Response exportCumulative(GoldCumulativeRequest request) {
        goldTypeService.exportCumulative(request);
        return Response.success();
    }
}
