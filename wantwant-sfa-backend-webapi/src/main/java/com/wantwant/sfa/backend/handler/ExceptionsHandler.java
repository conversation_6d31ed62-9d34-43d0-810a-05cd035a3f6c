//package com.wantwant.sfa.backend.handler;
//
//import com.wantwant.commons.cons.IStatusCode;
//import com.wantwant.commons.cons.StatusCode;
//import com.wantwant.commons.ex.ApplicationException;
//import com.wantwant.commons.web.response.Response;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.http.converter.HttpMessageNotReadableException;
//import org.springframework.jdbc.UncategorizedSQLException;
//import org.springframework.validation.BindException;
//import org.springframework.web.HttpRequestMethodNotSupportedException;
//import org.springframework.web.bind.MissingServletRequestParameterException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import org.springframework.web.multipart.support.MissingServletRequestPartException;
//import org.springframework.web.servlet.NoHandlerFoundException;
//
///**
// * 全局异常接管
// */
//@RestControllerAdvice
//public class ExceptionsHandler {
//
//    /**
//     * 注意：
//     * 启用全局异常接管后，没有在此处定义拦截的异常都会默认返回500错误。
//     * 若需要自定义拦截的异常，请在此处定义拦截。
//     * 若需要输出异常的日志日志，请使用logger输出。
//     */
//    private final Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @ExceptionHandler(UncategorizedSQLException.class)
//    public Response SQLException(UncategorizedSQLException e){
//        logger.error(e.getMessage(), e);
//        if("HY000".equalsIgnoreCase(e.getSQLException().getSQLState()) && 1366 == e.getSQLException().getErrorCode()){
//            return Response.error("系统暂不支持特殊字符输入");
//        }else{
//            return Response.error("系统异常，请联系管理员");
//        }
//    }
//
//    /**
//     * 基本异常
//     */
//    @ExceptionHandler(Exception.class)
//    public Response exception(Exception e) {
//        logger.error(e.getMessage(), e);
//        return new Response().error( 1,"Error");
//    }
//
//
//    /**
//     * 请求路径无法找到异常
//     */
//    @ExceptionHandler(NoHandlerFoundException.class)
//    public Response notFoundException() {
//        return new Response().error(404,"Not found");
//    }
//
//    /**
//     * 请求方法不支持异常
//     */
//    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
//    public Response httpRequestMethodNotSupportedException() {
//        return new Response().error(405,"Method not allowed");
//    }
//
//    /**
//     * 请求参数异常
//     */
//    @ExceptionHandler({HttpMessageNotReadableException.class, MissingServletRequestParameterException.class, MissingServletRequestPartException.class, BindException.class})
//    public Response parameterException() {
//        return new Response().error(403,"Parameter error");
//    }
//
//    /**
//     * 服务异常
//     */
//    @ExceptionHandler(ApplicationException.class)
//    public Response serviceException(ApplicationException e) {
//    	if(null == e.getStatusCode()) {
//        return new Response().error(1,e.getMessage());
//        }else {
//        return new Response().error(e.getStatusCode().getCode(),e.getMessage());
//        }
//    }
//
//}