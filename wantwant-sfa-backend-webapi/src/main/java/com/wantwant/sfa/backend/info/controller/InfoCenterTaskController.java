package com.wantwant.sfa.backend.info.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.info.api.InfoCenterTaskApi;
import com.wantwant.sfa.backend.info.request.PublishRequest;
import com.wantwant.sfa.backend.service.InfoCenterTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/24/上午9:49
 */
@RestController
@Slf4j
public class InfoCenterTaskController implements InfoCenterTaskApi {
    @Autowired
    private InfoCenterTaskService infoCenterTaskService;


    @Override
    public Response publish(PublishRequest publishRequest) {
        log.info("message publish request:{}",publishRequest);

        infoCenterTaskService.publish(publishRequest);

        return Response.success();
    }
}
