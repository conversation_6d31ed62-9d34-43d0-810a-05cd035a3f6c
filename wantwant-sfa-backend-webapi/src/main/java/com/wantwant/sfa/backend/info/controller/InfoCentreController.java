package com.wantwant.sfa.backend.info.controller;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.info.request.*;
import com.wantwant.sfa.backend.info.vo.*;

import com.wantwant.sfa.backend.info.api.InfoCentreApi;

import com.wantwant.sfa.backend.policy.service.IPolicyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.google.common.collect.Lists;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;

import com.wantwant.sfa.backend.info.vo.FileVo;
import com.wantwant.sfa.backend.info.vo.InfoCountVo;
import com.wantwant.sfa.backend.info.vo.InfoDetailVo;
import com.wantwant.sfa.backend.info.vo.InfoEditVo;
import com.wantwant.sfa.backend.info.vo.MailBoxVo;
import com.wantwant.sfa.backend.info.vo.ReadNumInfoVo;
import com.wantwant.sfa.backend.info.vo.UnreadNumVo;
import com.wantwant.sfa.backend.info.vo.WithdrawVo;
import com.wantwant.sfa.backend.service.InfoCentreService;
import com.wantwant.sfa.backend.util.CommonUtil.ListUtils;
import com.wantwant.sfa.backend.util.RedisUtil;

import lombok.extern.slf4j.Slf4j;


@RestController
@Slf4j
public class InfoCentreController implements InfoCentreApi {


    @Autowired
    private InfoCentreService centreService;
    @Autowired
    private IPolicyService policyService;
    
    @Autowired
    private RedisUtil redisUtil;
    
    public static final String LOCK_HEAD_FEEDBACK = "feedbackLock";

    private static final String LOCK_MESSAGE_SUBMIT = "message:submit";


    //首页消息
    @Override
    public Response<InfoCountVo> getinfoHome(InfoHomeRequset homeRequset) {
        log.info("start InfoCentreController getEdit editRequest:{}", homeRequset);
        return Response.success(centreService.findHomeMessage(homeRequset.getEmployeeId()));
    }


    //消息列表
    @Override
    public Response<Page<MailBoxVo>> getInfoList(InfoListRequest InfoListRequest) {
        log.info("start InfoCentreController getInfoList InfoListRequest:{}", InfoListRequest);
        return centreService.findInfoList(InfoListRequest);
    }


    //消息撤回列表
    @Override
    public Response<Page<WithdrawVo>> getWithdrawList(WithdrawListRequest withdrawListRequest) {
        log.info("start InfoCentreController getWithdrawList withdrawListRequest:{}", withdrawListRequest);
        return centreService.findWithdrawList(withdrawListRequest);
    }


    //消息编辑
    @Override
    public Response<InfoEditVo> getEdit(InEditRequest editRequest) {
        log.info("start InfoCentreController getEdit editRequest:{}", editRequest);
        return Response.success(centreService.findDraft(editRequest));
    }


    //消息提交
    @Override
    public Response getSubmit(InfoSubmitReqest submitReqest) {
        log.info("start InfoCentreController getSubmit request:{}", submitReqest);
        centreService.savaSystemMessage(submitReqest);
        return Response.success();

    }

    @Override
    public Response infoSubmitV2(InfoSubmitReqest submitRequest) {
        log.info("start InfoCentreController info submit v2 request:{}", submitRequest);
        if(Objects.isNull(submitRequest.getBusinessGroup())) {
            submitRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        }
        if(!redisUtil.setLockIfAbsent(LOCK_MESSAGE_SUBMIT,submitRequest.getCreateEmployeeId(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }


        try{
            centreService.infoSubmitV2(submitRequest);
        }finally {
            redisUtil.unLock(LOCK_MESSAGE_SUBMIT,submitRequest.getCreateEmployeeId());
        }



        return Response.success();
    }

    /**
     * 消息详情
     *
     * @param detailRequest
     * @return
     */
    @Override
    public Response<InfoDetailVo> getInfoDetail(InfoDetailRequest detailRequest) {
        log.info("start InfoCentreController getInfoDetail detailRequest:{}", detailRequest);
        return Response.success(centreService.findInfoDetail(detailRequest));
    }


    /**
     * 系统反馈
     *
     * @param feedbackRequest
     * @return
     */
    @Override
    public Response getFeedback(InfoFeedbackRequest feedbackRequest) {
        log.info("start InfoCentreController getFeedback feedbackRequest:{}", feedbackRequest);
        String messageId = String.valueOf(feedbackRequest.getMessageId());
        String originalEmployeeId = String.valueOf(feedbackRequest.getOriginalEmployeeId());
        
        if(!redisUtil.setLockIfAbsent(LOCK_HEAD_FEEDBACK,messageId+":"+originalEmployeeId,5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
	        centreService.updateMessageFeedback(feedbackRequest);
	        return Response.success();
        } finally {
			redisUtil.unLock(LOCK_HEAD_FEEDBACK,messageId+":"+originalEmployeeId);
		}
    }

    @Override
    public Response ceoFeedback(CeoFeedbackRequest request) {
        log.info("start InfoCentreController ceoFeedback request:{}", request);

        if(!redisUtil.setLockIfAbsent(LOCK_HEAD_FEEDBACK,request.getMessageRecordId()+":"+request.getMemberKey(),5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            Integer messageCategoryType = request.getMessageCategoryType();
            if(messageCategoryType == 0){
                centreService.ceoFeedback(request);
            }else{
                policyService.feedback(request.getMessageRecordId().longValue(),Long.valueOf(request.getMemberKey()),null);
            }



            return Response.success();
        } finally {
            redisUtil.unLock(LOCK_HEAD_FEEDBACK,request.getMessageRecordId()+":"+request.getMemberKey());
        }

    }

    //图片上传
    @Override
    public Response<List<FileVo>> upload(MultipartHttpServletRequest request) {
        List<FileVo> lists = Lists.newArrayList();
        Map<String, MultipartFile> fileMap = request.getFileMap();
        for (String key : fileMap.keySet()) {
            fileMap.get(key);
            FileVo picVo = new FileVo();
            picVo.setName(key);
            picVo.setUrl(key);
            lists.add(picVo);

        }
        return Response.success(lists);
    }


    /**
     * 消息列表导出
     *
     * @param exportInfoRequest
     */
//    @Override
//    public void exportInfoList(MessageListExportRequest exportInfoRequest) {
//        log.info("start InfoCentreController exportInfoList exportInfoRequest:{}", exportInfoRequest);
//        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
//                .getRequestAttributes();
//
//        HttpServletResponse response = servletRequestAttributes.getResponse();
//
//        List<MessageListExport> record = centreService.exportExcel(exportInfoRequest);
//
//        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
//
//        String name = "消息列表" + sheetName;
//
//        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), MessageListExport.class, record);
//
//        response.setContentType("application/vnd.ms-excel");
//
//        try {
//            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
//                    .encode(name + ".xlsx"));
//            OutputStream outputStream = response.getOutputStream();
//            workbook.write(outputStream);
//            outputStream.flush();
//            outputStream.close();
//        } catch (IOException e) {
//            response.setStatus(500);
//        }
//    }


    /**
     * 批量撤回
     *
     * @param batchWithdrawsRequset
     * @return
     */
    @Override
    public Response batchWithdraw(BatchWithdrawsRequset batchWithdrawsRequset) {
        log.info("start InfoCentreController batchWithdraw batchWithdrawsRequset:{}", batchWithdrawsRequset);
        centreService.updateBatchWithdraw(batchWithdrawsRequset);
        return Response.success();
    }


    /**
     * 单个撤回
     *
     * @param personalWithdrawRequset
     * @return
     */
    @Override
    public Response personalWithdraw(PersonalWithdrawRequset personalWithdrawRequset) {
        log.info("start InfoCentreController personalWithdraw personalWithdrawRequset:{}", personalWithdrawRequset);
        centreService.updatePersonalWithdraw(personalWithdrawRequset);
        return Response.success();
    }

    /**
     * 取消置顶
     */

    public Response cancelTopping(CancelToppingRequset cancelToppingRequset) {
        log.info("start InfoCentreController cancelTopping cancelToppingRequset:{}", cancelToppingRequset);
        centreService.updateTopping(cancelToppingRequset);
        return Response.success();
    }

    /**
     * 消息已读
     */
	@Override
	public Response readMessage(ReadRequest request) {
        log.info("start InfoCentreController read request:{}", request);
        
        if(StringUtils.isBlank(request.getMessageId())) {
			throw new ApplicationException("请选择消息");
        }else if(ListUtils.isEmpty(request.getOriginalEmployeeList())) {
			throw new ApplicationException("请选择收件人");
        }
        
        centreService.readMessage(request);
        
		return Response.success();
	}


	/**
	 * 未读消息数量
	 */
	@Override
	public Response<UnreadNumVo> unreadNum(InfoHomeRequset requset) {

		return Response.success(centreService.unreadNum(requset.getEmployeeId()));
	}


	/**
	 * 消息读取数量详情
	 */
	@Override
	public Response<ReadNumInfoVo> readNumInfo(ReadNumInfoRequest requset) {
		 
		return Response.success(centreService.readNumInfo(requset));
	}

    @Override
    public Response<Page<SenderBoxV2Vo>> senderBoxV2List(SenderBoxV2Request request) {
	    log.info("senderBoxV2List: {}",request);
	    if(request.getEmployeeId() == null) {
	        throw new ApplicationException("请传入工号");
        }
        return Response.success(centreService.getSenderBoxV2List(request));
    }

    @Override
    public Response messagePushTop(MessageTopSubmitRequest request) {
	    if(Objects.isNull(request.getBusinessGroup())) {
	        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        }
	    log.info("messagePushTop request:{}", request);
        centreService.messagePushTop(request);
        return Response.success();
    }

    @Override
    public Response showMessage(InfoTemplateIRequest request) {
	    log.info("showMessage:{}",request);
        centreService.setupMessageTemplateShow(request);
        return Response.success();
    }

    @Override
    public Response deleteMessage(InfoTemplateIRequest request) {
        log.info("deleteMessage:{}",request);
        centreService.deleteMessageTemplate(request);
        return Response.success();
    }

    @Override
    public Response<MessageRelationNumberVo> getMessageRelationNumber(String messageTemplateId) {
	    log.info("getMessageRelationNumber: {}", messageTemplateId);
        return Response.success(centreService.queryMessageRelationNumber(messageTemplateId));
    }

    @Override
    public Response verifyMessage(InfoVerifyRequest request) {
	    log.info("verifyMessage request:{}", request);
	    if(request.getResult() != 1 && request.getResult() != 2) {
	        throw new ApplicationException("审核类型传参错误");
        }
        centreService.messageAudit(request);
        return Response.success();
    }

    @Override
    public Response<List<AnnualPolicyVo>> getAnnualPolicy() {
        return Response.success(centreService.getAnnualPolicy());
    }

    @Override
    public Response<List<YearPolicyListVo>> getYearPolicyList(YearPolicyListRequest request) {
        return Response.success(centreService.getYearPolicyList(request));
    }

}
