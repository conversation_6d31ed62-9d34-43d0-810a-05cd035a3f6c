package com.wantwant.sfa.backend.info.controller;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.info.api.RegulationsApi;
import com.wantwant.sfa.backend.info.request.RegulationsListRequest;
import com.wantwant.sfa.backend.info.request.RegulationsSubmitRequest;
import com.wantwant.sfa.backend.info.request.RegulationsSubscribeNumsRequest;
import com.wantwant.sfa.backend.info.vo.RegulationsDetailsVo;
import com.wantwant.sfa.backend.info.vo.RegulationsListVo;
import com.wantwant.sfa.backend.info.vo.RegulationsSubscribeNumsVo;
import com.wantwant.sfa.backend.service.RegulationsService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import com.wantwant.commons.pagination.Page;

import java.util.concurrent.TimeUnit;

@RestController
@Slf4j
public class RegulationsController implements RegulationsApi {

    @Autowired
    private RegulationsService regulationsService;


    @Autowired
    private RedisUtil redisUtil;

    private static final String LOCK_REGULATIONS_SUBMIT = "regulations:submit";

    @Override
    public Response regulationsSubmit(RegulationsSubmitRequest request) {
        log.info("start RegulationsController regulationsSubmit regulationsSubmitRequest:{}", request);

        if (!redisUtil.setLockIfAbsent(LOCK_REGULATIONS_SUBMIT, request.getCreateEmployeeId(), 5, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！～");
        }
        try {
            regulationsService.regulationsSubmit(request);
        } finally {
            redisUtil.unLock(LOCK_REGULATIONS_SUBMIT, request.getCreateEmployeeId());
        }

        return Response.success();
    }

    @Override
    public Response<Page<RegulationsListVo>> regulationsList(RegulationsListRequest request) {
        log.info("start RegulationsController regulationsList regulationsListRequest:{}", request);
        return Response.success(regulationsService.regulationsList(request));
    }

    @Override
    public Response<Page<RegulationsSubscribeNumsVo>> subscribeNums(RegulationsSubscribeNumsRequest request) {
        log.info("start RegulationsController subscribeNums subscribeNumsRequest:{}", request);
        return Response.success(regulationsService.subscribeNums(request));
    }

    @Override
    public void subscribeNumsExport(RegulationsSubscribeNumsRequest request) {
        log.info("start RegulationsController subscribeNumsExport subscribeNumsExportRequest:{}", request);
        regulationsService.subscribeNumsExport(request);
    }

    @Override
    public Response<RegulationsDetailsVo> details(RegulationsSubscribeNumsRequest request) {
        log.info("start RegulationsController details detailsRequest:{}", request);
        return Response.success(regulationsService.details(request));
    }
}
