package com.wantwant.sfa.backend.interview.controller;

import com.alibaba.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.CeoModifyApplication;
import com.wantwant.sfa.backend.interview.api.CeoInterviewApi;
import com.wantwant.sfa.backend.interview.controller.assemble.CeoModifyAssemble;
import com.wantwant.sfa.backend.interview.dto.InterviewProbationPassDTO;
import com.wantwant.sfa.backend.interview.request.*;
import com.wantwant.sfa.backend.interview.service.InterviewProbationService;
import com.wantwant.sfa.backend.interview.service.InterviewService;
import com.wantwant.sfa.backend.interview.service.impl.CeoRegisterService;
import com.wantwant.sfa.backend.interview.vo.CeoInterviewVo;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/20/下午5:40
 */
@RestController
@Slf4j
public class CeoInterviewController implements CeoInterviewApi {
    @Autowired
    private InterviewService interviewService;
    @Autowired
    private InterviewProbationService interviewProbationService;
    @Resource
    private CeoRegisterService ceoRegisterService;
    @Resource
    private CeoModifyApplication ceoModifyApplication;
    @Resource
    private CeoModifyAssemble ceoModifyAssemble;
    @Resource
    private RedisUtil redisUtil;

    private static final String INTERNATIONAL_CEO_MODIFY_LOCK_KEY = "international:ceo:modify";

    @Override
    public Response<CeoInterviewVo> ceoInterviewInfo(Long memberKey) {
        log.info("【get ceo interview info 】memberKey:{}",memberKey);

        CeoInterviewVo vo = interviewService.getCeoInterviewInfo(memberKey);

        return Response.success(vo);
    }

    @Override
    public Response PassProbation(PassProbationRequest request) {
        log.info("【ceo probation pass】request:{}",request);

        interviewProbationService.passProbation(convertDTO(request));

        return Response.success();
    }

    @Override
    public Response cityManagerPassProbation(CityManagerPassProbationRequest request) {
        log.info("【city manager pass】request:{}",request);

        interviewProbationService.cityManagerPassProbation(request);

        return Response.success();
    }

    @Override
    public Response ceoRegister(CeoRegisterRequest ceoRegisterRequest) {
        log.info("【ceo register】ceoRegisterRequest:{}",ceoRegisterRequest);


        ceoRegisterService.ceoRegister(ceoRegisterRequest);

        return Response.success();
    }


    @Override
    public Response ceoModify(CeoBaseInfoModifyRequest ceoBaseInfoModifyRequest) {
        log.info("【ceo modify】request:{}",ceoBaseInfoModifyRequest);

        ceoModifyApplication.modifyBaseInfo(ceoModifyAssemble.convertModifyDO(ceoBaseInfoModifyRequest));

        return Response.success();
    }

    @Override
    public Response ceoBusinessGroupModify(CeoBusinessGroupModifyRequest ceoBusinessGroupModifyRequest) {
        log.info("【ceo business group modify】request:{}",ceoBusinessGroupModifyRequest);

        ceoModifyApplication.changeBusinessGroup(ceoModifyAssemble.convertBusinessGroupDO(ceoBusinessGroupModifyRequest));
        return Response.success();
    }

    @Override
    public Response ceoReplacement(CeoReplacementRequest ceoReplacementRequest) {
        log.info("【ceo replacement】request:{}",ceoReplacementRequest);

        ceoModifyApplication.ceoReplacement(ceoModifyAssemble.convertReplacement(ceoReplacementRequest));
        return Response.success();
    }

    @Override
    public Response ceoMobileModify(CeoMobileModifyRequest ceoMobileModifyRequest) {
        log.info("【ceo mobile modify】request:{}",ceoMobileModifyRequest);

        ceoModifyApplication.ceoMobileModify(ceoModifyAssemble.convertCeoMobileModify(ceoMobileModifyRequest));
        return Response.success();
    }

    @Override
    public Response ceoChangeOrganization(ChangeOrganizationRequest changeOrganizationRequest) {
        log.info("【ceo change organization】request:{}", JSONObject.toJSONString(changeOrganizationRequest));

        ceoModifyApplication.ceoChangeOrganization(ceoModifyAssemble.convertChangeOrganization(changeOrganizationRequest));

        return Response.success();
    }

    @Override
    public Response internationalCeoRegister(InternationalCeoRequest internationalCeoRequest) {
        log.info("international ceo register request:{}", JacksonHelper.toJson(internationalCeoRequest,false));

        String memberKey = String.valueOf(internationalCeoRequest.getMemberKey());

        if(! redisUtil.setLockIfAbsent(INTERNATIONAL_CEO_MODIFY_LOCK_KEY,memberKey,5, TimeUnit.SECONDS)){
            return Response.error("The request is being processed, please try again later!");
        }

        try{
            ceoModifyApplication.internationalCeoRegister(ceoModifyAssemble.convertInternationalCeo(internationalCeoRequest));
        }finally {
            redisUtil.unLock(INTERNATIONAL_CEO_MODIFY_LOCK_KEY,memberKey);
        }

        return Response.success();
    }

    @Override
    public Response internationalCeoModify(InternationalCeoRequest internationalCeoRequest) {
        log.info("international ceo modify request:{}", JacksonHelper.toJson(internationalCeoRequest,false));

        String memberKey = String.valueOf(internationalCeoRequest.getMemberKey());

        if(! redisUtil.setLockIfAbsent(INTERNATIONAL_CEO_MODIFY_LOCK_KEY,memberKey,5, TimeUnit.SECONDS)){
            return Response.error("The request is being processed, please try again later!");
        }

        try{
            ceoModifyApplication.internationalCeoModify(ceoModifyAssemble.convertInternationalCeo(internationalCeoRequest));
        }finally {
            redisUtil.unLock(INTERNATIONAL_CEO_MODIFY_LOCK_KEY,memberKey);
        }

        return Response.success();
    }

    private InterviewProbationPassDTO convertDTO(PassProbationRequest request) {
        InterviewProbationPassDTO interviewProbationPassDTO = new InterviewProbationPassDTO();
        BeanUtils.copyProperties(request,interviewProbationPassDTO);
        return interviewProbationPassDTO;
    }
}
