package com.wantwant.sfa.backend.interview.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.api.EliminateApi;
import com.wantwant.sfa.backend.interview.request.BatchSpecialCaseApprovalRequest;
import com.wantwant.sfa.backend.interview.request.SpecialApprovalRequest;
import com.wantwant.sfa.backend.interview.request.SpecialCaseApprovalRequest;
import com.wantwant.sfa.backend.interview.service.IEliminateService;
import com.wantwant.sfa.backend.interview.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/19/下午4:06
 */
@RestController
@Slf4j
public class EliminateController implements EliminateApi {
    @Autowired
    private IEliminateService eliminateService;

    @Override
    public Response<List<EliminateAuditRecordVo>> getEliminateRecords(Long memberKey) {
        log.info("【get eliminate audit record】memberKey:{}",memberKey);
        List<EliminateAuditRecordVo> list = eliminateService.getEliminateRecords(memberKey);
        return Response.success(list);
    }

    @Override
    public Response<EliminateStatusVo> getEliminateStatus(Long memberKey,String person,String yearMonth) {
        log.info("【get eliminate status】memberKey:{},empId:{}",memberKey,person);
        EliminateStatusVo vo = eliminateService.getEliminateStatus(memberKey,person,yearMonth);
        return Response.success(vo);
    }

    @Override
    public Response<IPage<SpecialApprovalVo>> selectSpecialApproval(SpecialApprovalRequest specialApprovalRequest) {
        log.info("【select special approval】request:{}",specialApprovalRequest);
        IPage<SpecialApprovalVo> page = eliminateService.selectSpecialApproval(specialApprovalRequest);

        return Response.success(page);
    }

    @Override
    public Response<List<SpecialCaseApprovalVo>> selectSpecialCaseApproval(SpecialCaseApprovalRequest specialCaseApprovalRequest) {
        log.info("【select special case approval】request:{}",specialCaseApprovalRequest);

        List<SpecialCaseApprovalVo> list = eliminateService.selectSpecialCaseApproval(specialCaseApprovalRequest);

        return Response.success(list);
    }

    @Override
    public Response batchSpecialCaseProcess(BatchSpecialCaseApprovalRequest batchSpecialCaseApprovalRequest) {
        log.info("【select special case batch】request:{}",batchSpecialCaseApprovalRequest);

        eliminateService.batchSpecialCaseProcess(batchSpecialCaseApprovalRequest);

        return Response.success();
    }

    @Override
    public Response<SpecialApprovalDetailVo> getSpecialApprovalDetail(Integer applyId) {
        log.info("【get special approval detail】applyId:{}",applyId);

        SpecialApprovalDetailVo specialApprovalDetailVo = eliminateService.getSpecialApprovalDetail(applyId);
        return Response.success(specialApprovalDetailVo);
    }

    @Override
    public Response<Boolean> getSpecialApprovalDetail(String employeeId) {
        log.info("【getSpecialApprovalDetail】person:{}",employeeId);
        return Response.success(eliminateService.showSpecialApproval(employeeId));
    }


}
