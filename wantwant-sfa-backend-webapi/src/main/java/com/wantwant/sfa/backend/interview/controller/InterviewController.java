package com.wantwant.sfa.backend.interview.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.estimate.vo.CityManagerProbationVO;
import com.wantwant.sfa.backend.interview.api.InternationalEmpSearchRequest;
import com.wantwant.sfa.backend.interview.api.InterviewApi;
import com.wantwant.sfa.backend.interview.command.Impl.CacheInterviewOperationCommandImpl;
import com.wantwant.sfa.backend.interview.command.Impl.CloseInterviewOperationCommandImpl;
import com.wantwant.sfa.backend.interview.command.InterviewOperationInvoker;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.request.*;
import com.wantwant.sfa.backend.interview.service.InterviewProcessService;
import com.wantwant.sfa.backend.interview.service.InterviewSearchService;
import com.wantwant.sfa.backend.interview.service.InterviewService;
import com.wantwant.sfa.backend.interview.service.impl.InternationalInterviewService;
import com.wantwant.sfa.backend.interview.vo.*;

import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.zw.vo.PartnerInfoVo;
import com.wantwant.sfa.backend.zw.vo.RecruitmentNeedsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 面试用Controller。
 * @Auther: zhengxu
 * @Date: 2021/11/06/上午10:44
 */
@RestController
@Slf4j
public class InterviewController implements InterviewApi {
    private static final String INTERVIEW_SUBMIT_LOCK = "interview:submit";
    private static final String INTERVIEW_reemploy_LOCK = "interview:reemploy";

    @Autowired
    private InterviewService interviewService;
    @Autowired
    private CacheInterviewOperationCommandImpl cacheInterviewOperationCommandgetCEOList;
    @Autowired
    private CloseInterviewOperationCommandImpl closeInterviewOperationCommand;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private InterviewProcessService interviewProcessService;
    @Autowired
    private InterviewSearchService InterviewSearchService;
    @Autowired
    private ISalaryMiddlewareService iSalaryMiddlewareService;
    @Autowired
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Autowired
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Resource
    private InternationalInterviewService internationalInterviewService;

    @Override
    public Response<InterviewEmployeeInfoVo> applyInfo(InterviewApplyInfoRequest request) {

        InterviewEmployeeInfoVo vo = interviewService.applyInfo(request);
        
        return Response.success(vo);
    }

    @Override
    public Response interviewClose(InterviewOperateRequest request) {
        InterviewOperationInvoker invoker = new InterviewOperationInvoker(closeInterviewOperationCommand);
        invoker.call(request);
        return Response.success();
    }

    @Override
    public Response interviewReject(InterviewRejectRequest request) {
        log.info("【审核驳回】request:{}",request);
        interviewService.reject(request);
        return Response.success();
    }

    @Override
    public Response interviewCache(InterviewOperateRequest request) {
        InterviewOperationInvoker invoker = new InterviewOperationInvoker(cacheInterviewOperationCommandgetCEOList);
        invoker.call(request);
        return Response.success();
    }

    @Override
    public Response interviewSubmit(InterviewOperateRequest request) {
        if(!redisUtil.setLockIfAbsent(INTERVIEW_SUBMIT_LOCK,request.getApplicationId().toString(),5, TimeUnit.SECONDS)){
            return Response.error("当前正在处理中");
        }
        try{
            interviewProcessService.interviewSubmit(request);
        }finally {
            redisUtil.unLock(INTERVIEW_SUBMIT_LOCK,request.getApplicationId().toString());
        }

        return Response.success();
    }


    @Override
    public Response probationCancel(ProbationCancelRequest request) {
        interviewService.probationCancel(request);
        return Response.success();
    }

    @Override
    public Response edit(EmployeeInfoEditRequest request) {
        interviewService.edit(request);
        return Response.success();
    }


    @Override
    public Response ehrRecommendation(@RequestParam(value = "applicationId") Integer applicationId) {
        log.info("ehrRecommendation applicationId: {}", applicationId);
        return Response.success(interviewService.getRecommendation(applicationId));
    }


    @Override
    public Response syn(SynProcessRequest request) {
        log.info("同步入离职流程信息 request: {}", request);

        interviewService.syn(request);

        return Response.success();
    }

    @Override
    public Response synDelayReason(SynDelayReasonRequest request) {
        log.info("【syn delay reason】 request: {}", request);

        interviewService.synDelayReason(request);

        return Response.success();
    }

    @Override
    public Response<List<RecruitmentNeedsVO>> recruitmentList(@RequestParam(value = "companyCode") String companyCode,@RequestParam(value = "employeeId") String employeeId,@RequestParam(value = "position")int position) {
        log.info("recruitmentList companyCode: {}", companyCode);
        return Response.success(interviewService.getRecruitmentList(companyCode,employeeId,position));

    }

    @Override
    public Response<List<PartnerInfoVo>> getCEOList(@RequestParam(value = "key") String key, @RequestParam(value = "departMentCode") String departMentCode) {
        log.info("getCEOList key: {} companyCode: {}", key, departMentCode);
        return Response.success(interviewService.getCEOList(key, departMentCode));
    }

    @Override
    public Response<Page<InterviewZBVerifyVo>> getInterviewZBVerify(ZBVerifyRequest request) {
        log.info("【运营审核列表查询】request:{}",request);
        return Response.success(InterviewSearchService.getInterviewZBVerify(request));
    }

    @Override
    public Response<InterviewAuditInfoVo> getNextAuditInfo(Integer recordId) {
        log.info("【获取下级审核信息】recordId:{}",recordId);
        return Response.success(InterviewSearchService.getNextAuditInfo(recordId));
    }

    @Override
    public Response sendMsg(InterviewInfoSynRequest request) {
        log.info("【interview send msg】request:{}",request);

        interviewService.sendMsg(request);

        return Response.success();
    }

    @Override
    public Response interviewInfoSyn(InterviewInfoSynRequest request) {
        log.info("【面试信息同步】request:{}",request);
        interviewService.interviewInfoSyn(request);
        return Response.success();
    }

    @Override
    public Response tryEmployment(ReemployInfoRequest request) {
        log.info("【重新开通试岗】request:{}",request);
        throw new ApplicationException("不支持重新开通试岗");
//        if(!redisUtil.setLockIfAbsent(INTERVIEW_reemploy_LOCK,request.getEmployeeId().toString(),5, TimeUnit.SECONDS)){
//            return Response.error("当前正在处理中");
//        }
//        try{
//            interviewService.tryReemploy(request);
//        }finally {
//            redisUtil.unLock(INTERVIEW_reemploy_LOCK,request.getEmployeeId().toString());
//        }
//        return Response.success();
    }

    @Override
    public Response updateLaborContract(@RequestBody LaborContractRequest request) {
        interviewService.updateLaborContract(request);
        return Response.success();
    }

    @Override
    public Response<CertificationVo> checkCertification(Integer applyId) {
        log.info("【check certification】applyId:{}",applyId);

        CertificationVo certificationVo = interviewService.checkCertification(applyId);

        return Response.success(certificationVo);
    }

    @Override
    public Response<OrganizationSalaryControlVo> getSalaryControl(Integer applyId) {
        log.info("【get salary control】recordId:{}",applyId);
        OrganizationSalaryControlVo vo = iSalaryMiddlewareService.getSalaryControl(applyId);
        return Response.success(vo);
    }

    @Override
    public Response<CityManagerProbationVO> getCityManagerProbation(String departmentId) {
        log.info("【get salary control】departmentId:{}",departmentId);
        CityManagerProbationVO cityManagerProbationVO = interviewService.getCityManagerProbation(departmentId);

        return Response.success(cityManagerProbationVO);
    }

    @Override
    public Response<List<Integer>> getExternalBusinessGroup() {

        List<Integer> list = interviewService.getExternalBusinessGroup();
        return Response.success(list);
    }

    @Override
    public Response importApplyMember(MultipartFile file) {
        log.info("import apply member");

        interviewService.importApplyMember(file);

        return Response.success();
    }

    @Override
    public Response cancel(InterviewCancelRequest interviewCancelRequest) {
        log.info("【interview cancel】request:{}", JSONObject.toJSONString(interviewCancelRequest));

        interviewProcessService.cancel(interviewCancelRequest);

        return Response.success();
    }

    @Override
    public Response<List<String>> importInternationalPersonnelInfo(MultipartFile file, String employeeId) {
        List<String> errMsg = internationalInterviewService.importPersonnelInfo(file, employeeId);
        if(!CollectionUtils.isEmpty(errMsg)){
            return Response.error(errMsg, "There is error information in the imported content");
        }
        return Response.success(Collections.emptyList());
    }

    @Override
    public Response<IPage<InternationalEmpVo>> selectInternationalEmpList(InternationalEmpSearchRequest internationalEmpSearchRequest) {
        IPage<InternationalEmpVo> page = internationalInterviewService.selectInternationalEmpList(internationalEmpSearchRequest);
        return Response.success(page);
    }

    @Override
    public void exportInternationalEmpList(InternationalEmpSearchRequest internationalEmpSearchRequest, HttpServletRequest request, HttpServletResponse response) {
        internationalInterviewService.exportInternationalEmpList(internationalEmpSearchRequest,request,response);
    }
}
