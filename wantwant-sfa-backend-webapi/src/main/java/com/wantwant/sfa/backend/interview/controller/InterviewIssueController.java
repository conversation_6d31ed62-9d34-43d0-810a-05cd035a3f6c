package com.wantwant.sfa.backend.interview.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.api.InterviewIssueApi;
import com.wantwant.sfa.backend.interview.request.IssueModifyRequest;
import com.wantwant.sfa.backend.interview.service.InterviewIssueService;
import com.wantwant.sfa.backend.interview.vo.InterviewIssueVo;
import com.wantwant.sfa.backend.interview.vo.IssueReplyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/08/上午10:05
 */
@RestController
@Slf4j
public class InterviewIssueController implements InterviewIssueApi {

    @Autowired
    private InterviewIssueService interviewIssueService;

    @Override
    public Response<List<InterviewIssueVo>> selectIssue(Integer position, String companyCode) {
        log.info("【select interview issue】position:{},recruitmentId:{}",position,companyCode);

        List<InterviewIssueVo> list = interviewIssueService.selectIssue(position,companyCode);

        return Response.success(list);
    }

    @Override
    public Response<List<IssueReplyVo>> getIssueReply(Integer applyId) {
        log.info("【get issue reply】applyId:{}",applyId);

        List<IssueReplyVo> list = interviewIssueService.getIssueReply(applyId);

        return Response.success(list);
    }

    @Override
    public Response modify(IssueModifyRequest request) {
        log.info("【interview issue modify】request:{}",request);

        interviewIssueService.modify(request.getPosition(),request.getCompanyCode(),request.getIssueIds(),request.getPerson());

        return Response.success();
    }
}
