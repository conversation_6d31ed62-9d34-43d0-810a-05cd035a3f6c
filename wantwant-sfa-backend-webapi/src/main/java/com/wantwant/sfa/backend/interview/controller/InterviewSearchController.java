package com.wantwant.sfa.backend.interview.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.api.InterviewSearchApi;
import com.wantwant.sfa.backend.interview.dto.MemberExperienceDTO;
import com.wantwant.sfa.backend.interview.service.InterviewSearchService;
import com.wantwant.sfa.backend.interview.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/12/11/下午5:27
 */
@Slf4j
@RestController
public class InterviewSearchController implements InterviewSearchApi {
    @Autowired
    private InterviewSearchService interviewSearchService;

    @Override
    public Response<InterviewBaseVo> getInterviewBase(Integer recordId) {
        log.info("【interview base】recordId:{}",recordId);
        InterviewBaseVo interviewBaseVo = interviewSearchService.getInterviewBase(recordId);
        return Response.success(interviewBaseVo);
    }

    @Override
    public Response<List<MemberExperienceDTO>> getExperience(Integer recordId) {
        log.info("【interview experience】recordId:{}",recordId);
        List<MemberExperienceDTO> list = interviewSearchService.getExperience(recordId);
        return  Response.success(list);
    }

    @Override
    public Response<InterviewRecordVo> getInterviewRecord(Integer recordId) {
        log.info("【interview record】record:{}",recordId);
        InterviewRecordVo interviewRecordVo = interviewSearchService.getInterviewRecord(recordId);
        return Response.success(interviewRecordVo);
    }

    @Override
    public Response<List<ResignRecordVo>> getResignRecord(Integer recordId) {
        log.info("【interview resign】record:{}",recordId);

        List<ResignRecordVo> list = interviewSearchService.getResignRecord(recordId);
        return  Response.success(list);
    }

    @Override
    public Response<InterviewProgressVO> getProcess(String mobile) {
        log.info("【search interview process】mobile:{}",mobile);

        InterviewProgressVO interviewProgressVO = interviewSearchService.getProcess(mobile);

        return Response.success(interviewProgressVO);
    }
}
