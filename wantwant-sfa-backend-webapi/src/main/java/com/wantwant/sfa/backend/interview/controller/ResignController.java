package com.wantwant.sfa.backend.interview.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.api.ResignApi;
import com.wantwant.sfa.backend.interview.request.ResignApplyRequest;
import com.wantwant.sfa.backend.interview.request.ResignSubmitRequest;
import com.wantwant.sfa.backend.interview.request.ResignValidRequest;
import com.wantwant.sfa.backend.interview.service.ResignService;
import com.wantwant.sfa.backend.interview.vo.InterviewReasonVo;
import com.wantwant.sfa.backend.interview.vo.ResignAuditInfoVo;
import com.wantwant.sfa.backend.interview.vo.ResignVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 离职操作Controller。
 * @Auther: zhengxu
 * @Date: 2021/11/20/下午12:03
 */
@RestController
@Slf4j
public class ResignController implements ResignApi {

    @Autowired
    private ResignService resignService;
    @Autowired
    private RedisUtil redisUtil;

    private static final String RESIGN_APPLY_LOCK = "resign:apply";

    private static final String RESIGN_VALID_LOCK = "resign:valid";

    private static final String RESIGN_SUBMIT_LOCK = "resign:submit";

    @Override
    public Response<Long> resignApply(@Valid ResignApplyRequest request) {
        String employeeId = StringUtils.EMPTY;

        if(Objects.isNull(request.getEmployeeInfoId())){
            employeeId = request.getInterviewRecordId().toString();
        }else{
            employeeId = request.getEmployeeInfoId().toString();
        }

        if(!redisUtil.setLockIfAbsent(RESIGN_APPLY_LOCK, employeeId, 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            Long agentId = resignService.apply(request);
            return Response.success(agentId);
        }finally {
            redisUtil.unLock(RESIGN_APPLY_LOCK,employeeId);
        }

    }




    @Override
    public Response<List<InterviewReasonVo>> getResignReason() {
        List<InterviewReasonVo> list = resignService.getResignReason();
        return Response.success(list);
    }

    @Override
    public Response resignValid(ResignValidRequest request) {
        if(!redisUtil.setLockIfAbsent(RESIGN_VALID_LOCK, request.getProcessRecordId().toString(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            resignService.valid(request);
        }finally {
            redisUtil.unLock(RESIGN_VALID_LOCK,request.getProcessRecordId().toString());
        }
        return Response.success();
    }

    @Override
    public Response<ResignAuditInfoVo> resignAuditInfo(int recordId) {
        log.info("【resign audit info 】recordId:{}",recordId);
        ResignAuditInfoVo resignAuditInfoVo = resignService.resignAuditInfo(recordId);
        return Response.success(resignAuditInfoVo);
    }

    @Override
    public Response<ResignVo> getResignInfo(Integer employeeInfoId) {
        ResignVo vo = resignService.getResignInfo(employeeInfoId);
        return Response.success(vo);
    }

    @Override
    public Response submit(ResignSubmitRequest request) {
        log.info("【离职确认】request:{}",request);

        if(!redisUtil.setLockIfAbsent(RESIGN_SUBMIT_LOCK, request.getInterviewProcessRecordId().toString(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }


        try{
            Integer processResult = request.getProcessResult();
            if(processResult == 1){
                resignService.pass(request);
            }else if(processResult == 3){
                resignService.cache(request);
            }else{
                throw new ApplicationException("请选择正确的审核结果");
            }

        }finally {
            redisUtil.unLock(RESIGN_SUBMIT_LOCK,request.getInterviewProcessRecordId().toString());
        }

        return Response.success();
    }

}
