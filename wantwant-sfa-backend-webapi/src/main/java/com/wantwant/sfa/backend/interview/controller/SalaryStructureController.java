package com.wantwant.sfa.backend.interview.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.api.SalaryStructureApi;
import com.wantwant.sfa.backend.interview.request.SalaryStructureRequest;
import com.wantwant.sfa.backend.interview.vo.SalaryStructureVo;
import com.wantwant.sfa.backend.salary.vo.SalarySchemeHistoryVO;
import com.wantwant.sfa.backend.service.EmployeeSalaryStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/12/08/下午5:05
 */
@RestController
@Slf4j
public class SalaryStructureController implements SalaryStructureApi {

    @Autowired
    private EmployeeSalaryStructureService employeeSalaryStructureService;

    @Override
    public Response<List<SalaryStructureVo>> getSalaryStructure(SalaryStructureRequest request) {
        log.info("【salary structure】 request:{}",request);

        List<SalaryStructureVo> list = employeeSalaryStructureService.getSalaryStructure(request);

        return Response.success(list);
    }

    @Override
    public Response<List<SalarySchemeHistoryVO>> getSalaryHistory(Integer applyId) {
        log.info("【get salary history】applyId:{}",applyId);

        List<SalarySchemeHistoryVO> list =  employeeSalaryStructureService.getSalaryHistory(applyId);

        return Response.success(list);
    }
}
