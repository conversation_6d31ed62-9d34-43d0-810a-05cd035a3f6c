package com.wantwant.sfa.backend.interview.controller.assemble;

import com.wantwant.sfa.backend.domain.ceoModify.DO.*;
import com.wantwant.sfa.backend.domain.emp.DO.InternationalEmpDO;
import com.wantwant.sfa.backend.interview.request.*;
import com.wantwant.sfa.backend.policy.dto.PolicyRegularDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/05/22/上午7:54
 */
@Mapper(componentModel = "spring")
public interface CeoModifyAssemble {

    @Mapping(target = "warehouseRequests", resultType = WarehouseDO.class)
    CeoBaseInfoModifyDO convertModifyDO(CeoBaseInfoModifyRequest ceoBaseInfoModifyRequest);


    @Mapping(target = "addBusinessGroupCodes", resultType = OrgBusinessGroup.class)
    @Mapping(target = "subBusinessGroupCodes", resultType = OrgBusinessGroup.class)
    CeoBusinessGroupModifyDO convertBusinessGroupDO(CeoBusinessGroupModifyRequest ceoBusinessGroupModifyRequest);


    CeoReplacementDO convertReplacement(CeoReplacementRequest ceoReplacementRequest);

    CeoMobileModifyDO convertCeoMobileModify(CeoMobileModifyRequest ceoMobileModifyRequest);

    ChangeOrganizationDO convertChangeOrganization(ChangeOrganizationRequest changeOrganizationRequest);

    InternationalEmpDO convertInternationalCeo(InternationalCeoRequest internationalCeoRequest);

}
