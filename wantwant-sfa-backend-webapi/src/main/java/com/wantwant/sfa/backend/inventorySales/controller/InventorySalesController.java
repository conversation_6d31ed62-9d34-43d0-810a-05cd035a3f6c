package com.wantwant.sfa.backend.inventorySales.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.inventorySales.api.InventorySalesApi;
import com.wantwant.sfa.backend.inventorySales.request.InventorySalesDetailRequest;
import com.wantwant.sfa.backend.inventorySales.request.InventorySalesRateRequest;
import com.wantwant.sfa.backend.inventorySales.vo.InventorySalesDetailVo;
import com.wantwant.sfa.backend.inventorySales.vo.InventorySalesRateVo;
import com.wantwant.sfa.backend.service.InventorySalesService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description：建档客户盘点动销
 * @Author： chen
 * @Date 2022/8/3
 */
@RestController
@Api(tags = "建档客户盘点动销Api")
@Slf4j
public class InventorySalesController implements InventorySalesApi {

    @Autowired
    private InventorySalesService inventorySalesService;


    @Override
    public Response<Page<InventorySalesRateVo>> inventorySalesRateList(InventorySalesRateRequest request) {
        Page<InventorySalesRateVo> inventorySalesVo = inventorySalesService.selectinventorySalesRateList(request);

        return Response.success(inventorySalesVo);
    }

    @Override
    public Response<Page<InventorySalesDetailVo>> inventorySalesDetailList(InventorySalesDetailRequest request) {
        Page<InventorySalesDetailVo> inventorySalesDetailVoPage = inventorySalesService.selectinventorySalesDetailList(request);

        return Response.success(inventorySalesDetailVoPage);
    }
}
