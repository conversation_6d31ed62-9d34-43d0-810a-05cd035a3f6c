package com.wantwant.sfa.backend.jobTransfer.assemble;

import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrganizationDO;
import com.wantwant.sfa.backend.domain.flow.DO.JobTransferAuditDO;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.*;
import com.wantwant.sfa.backend.jobTransfer.request.*;
import io.swagger.annotations.ApiModelProperty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/03/上午10:14
 */
@Mapper(componentModel = "Spring")
public interface JobTransferAssemble {

    @Mapping(target = "changeBusinessGroupDO", resultType = ChangeBusinessGroupDO.class)
    @Mapping(target = "changePositionTypeDO", resultType = ChangePositionTypeDO.class)
    @Mapping(target = "changeMainOrg", resultType = ChangeOrganizationDO.class)
    @Mapping(target = "changeWorkPlace", resultType = ChangeOrganizationDO.class)
    @Mapping(target = "changePartTimeOrg", resultType = ChangeOrganizationDO.class)
    @Mapping(target = "changeSalaryDO", resultType = ChangeSalaryDO.class)
    @Mapping(target = "changeBusinessBDType", resultType = ChangeBusinessBDTypeDO.class)
    @Mapping(target = "changeServerObj",resultType = ChangeServerObjDO.class)
    JobTransferDO convertToJobTransferDO(JobTransferApplyRequest jobTransferApplyRequest);

    @Mapping(target = "changeSalaryDO", resultType = ChangeSalaryDO.class)
    @Mapping(target = "changePartTimeOrg", resultType = ChangeOrganizationDO.class)
    JobTransferAuditDO jobTransferAudit(AuditRequest auditRequest);



    @Mapping(target = "BModel", resultType = TransactBModelDO.class)
    @Mapping(target = "CModel", resultType = TransactCModelDO.class)
    @Mapping(target = "DModel", resultType = TransactDModelDO.class)
    @Mapping(target = "EModel", resultType = TransactEModelDO.class)
    JobTransferTransactDO transact(TransactRequest transactRequest);


}
