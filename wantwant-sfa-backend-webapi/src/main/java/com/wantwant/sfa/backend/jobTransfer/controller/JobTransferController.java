package com.wantwant.sfa.backend.jobTransfer.controller;

import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.JobTransferApplication;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.JobTransferDO;
import com.wantwant.sfa.backend.jobTransfer.api.JobTransferApi;
import com.wantwant.sfa.backend.jobTransfer.assemble.JobTransferAssemble;
import com.wantwant.sfa.backend.jobTransfer.request.*;
import com.wantwant.sfa.backend.jobTransfer.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;


import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/08/03/上午8:00
 */
@RestController
@Slf4j
public class JobTransferController implements JobTransferApi {
    @Resource
    private JobTransferApplication jobTransferApplication;
    @Resource
    private JobTransferAssemble jobTransferAssemble;

    @Override
    public Response<List<ChangeBusinessGroupVO>> selectChangeBusinessGroup(Integer employeeInfoId) {
        log.info("【select change business group】employeeInfoId:{}",employeeInfoId);
        List<ChangeBusinessGroupVO> list = jobTransferApplication.selectChangeBusinessGroup(employeeInfoId);
        return Response.success(list);
    }

    @Override
    public Response<List<ChangeBusinessBDTypeVO>> selectChangeBusinessBDType(Integer employeeInfoId) {
        log.info("【select change business bd type】employeeInfoId:{}",employeeInfoId);

        List<ChangeBusinessBDTypeVO> list = jobTransferApplication.selectChangeBusinessBDType(employeeInfoId);
        return Response.success(list);
    }

    @Override
    public Response<List<ChangeOrganizationVO>> selectChangeOrganization(Integer businessGroup,String person, Integer positionTypeId) {
        log.info("【select change organization】businessGroup:{},person:{},positionTypeId:{}",businessGroup,person,positionTypeId);
        List<ChangeOrganizationVO> list = jobTransferApplication.selectChangeOrganization(businessGroup,person,positionTypeId);
        return Response.success(list);
    }

    @Override
    public Response<List<ChangePositionTypeVO>> selectChangePositionType() {
        List<ChangePositionTypeVO> list = jobTransferApplication.selectChangePositionType();
        return Response.success(list);
    }

    @Override
    public Response<List<ChangeSalaryVO>> selectChangeSalary(@Valid SalarySearchRequest salarySearchRequest) {
        log.info("【select change salary】request:{}",JSONObject.toJSONString(salarySearchRequest));
        List<ChangeSalaryVO> list = jobTransferApplication.selectChangeSalary(salarySearchRequest);
        return Response.success(list);
    }

    @Override
    public Response<List<JobTransferTypeVO>> getJobTransferType(Integer employeeInfoId) {
        log.info("【get job transfer type】employeeInfoId:{}",employeeInfoId);
        List<JobTransferTypeVO> list = jobTransferApplication.getJobTransferType(employeeInfoId);
        return Response.success(list);
    }


    @Override
    public Response<Long> jobTransferApply(@Valid JobTransferApplyRequest jobTransferApplyRequest) {
        log.info("【job transfer apply】request:{}", JSONObject.toJSONString(jobTransferApplyRequest));

        JobTransferDO jobTransferDO =jobTransferAssemble.convertToJobTransferDO(jobTransferApplyRequest);
        Long transactionId = jobTransferApplication.jobTransferApply(jobTransferDO, jobTransferApplyRequest.getPerson());
        return Response.success(transactionId);
    }

    @Override
    public Response<SalaryControlVO> getSalaryControl(String orgCode) {
        log.info("【get salary control】orgCode:{}",orgCode);
        SalaryControlVO salaryControlVO = jobTransferApplication.getSalaryControl(orgCode);
        return Response.success(salaryControlVO);
    }

    @Override
    public Response<JobTransferApplyVO> getJobTransferApply(Long transactionId) {
        log.info("【get job  transfer apply】transactionId:{}",transactionId);
        JobTransferApplyVO jobTransferApplyVO = jobTransferApplication.getJobTransferApply(transactionId);
        return Response.success(jobTransferApplyVO);
    }

    @Override
    public Response audit(@Valid AuditRequest auditRequest) {
        log.info("【job transfer audit】request:{}",auditRequest);

        jobTransferApplication.audit(jobTransferAssemble.jobTransferAudit(auditRequest),auditRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response transact(@Valid TransactRequest transactRequest) {
        log.info("【job transfer transact】request:{}",JSONObject.toJSONString(transactRequest));

        jobTransferApplication.transact(jobTransferAssemble.transact(transactRequest),transactRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response revertTransaction(@Valid TransactionRevertRequest transactionRevertRequest) {
        log.info("【job transfer revert】request:{}",JSONObject.toJSONString(transactionRevertRequest));

        jobTransferApplication.revertTransaction(transactionRevertRequest.getTransactionId(),transactionRevertRequest.getPerson());

        return Response.success();
    }

    @Override
    public Response<List<JobTransferHistoryVO>> getJobTransferHistory(Integer applyId) {
        log.info("【job transfer history】applyId:{}",applyId);

        List<JobTransferHistoryVO> list = jobTransferApplication.getJobTransferHistory(applyId);

        return Response.success(list);
    }

    @Override
    public Response<List<TransferActionVO>> getTransferAction(Long transactionId) {
        log.info("【get transaction action】transactionId:{}",transactionId);

        List<TransferActionVO> list = jobTransferApplication.getTransferAction(transactionId);
        return Response.success(list);
    }

    @Override
    public Response empOperation(List<EmpOperationEventRequest> list) {
        log.info("【emp operation】list:{}",list);

        jobTransferApplication.empOperation(list);

        return Response.success();
    }
}
