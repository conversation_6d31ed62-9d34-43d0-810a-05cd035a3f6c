package com.wantwant.sfa.backend.leave.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.vo.CheckCustomerVO;
import com.wantwant.sfa.backend.leave.api.LeaveApi;
import com.wantwant.sfa.backend.leave.request.LeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCancelInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.LeaveListRequest;
import com.wantwant.sfa.backend.leave.service.ILeaveService;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditRecordVo;
import com.wantwant.sfa.backend.leave.vo.LeaveDetailVo;
import com.wantwant.sfa.backend.leave.vo.LeaveListVo;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;


@RestController
@Slf4j
public class LeaveController  implements LeaveApi {

    private static final String LEAVE_AUDIT_LOCK = "leave:audit:lock";
    private static final String LEAVE_COMMIT_LOCK = "leave:commit:lock";
    private static final String LEAVE_CANCEL_LOCK = "leave:cancel:lock";


    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ILeaveService iLeaveService;

    @Override
    public Response commitLeaveInfo(LeaveCommitInfoRequest request) {
        log.info("leaveAudit request:{}",request);

        if(!redisUtil.setLockIfAbsent(LEAVE_AUDIT_LOCK, request.getBusinessNum(),1, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！");
        }
        try {
            iLeaveService.employeeCommitLeaveInfo(request);
        } finally {
            redisUtil.unLock(LEAVE_AUDIT_LOCK, request.getBusinessNum());
        }
        return Response.success();
    }

    @Override
    public Response LeaveCancelInfo(List<LeaveCancelInfoRequest> list) {
        log.info("LeaveCancelInfo request:{}",list);
        if(CollectionUtils.isEmpty(list)) {
            throw new ApplicationException("数据传入为空");
        }
        LeaveCancelInfoRequest request = list.get(0);
        if(request == null || request.getBusinessNum() == null) {
            throw new ApplicationException("传入销假单号");
        }
        if(!redisUtil.setLockIfAbsent(LEAVE_CANCEL_LOCK, request.getBusinessNum(),1, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！");
        }
        try {
            iLeaveService.employeeCancelLeave(list);
        } finally {
            redisUtil.unLock(LEAVE_CANCEL_LOCK, request.getBusinessNum());
        }
        return Response.success();
    }

    @Override
    public Response<IPage<LeaveListVo>> leaveList(LeaveListRequest request) {
        log.info("leaveList request:{}", request);
        if(CommonUtil.StringUtils.isEmpty(request.getPerson())) {
            throw new ApplicationException("员工工号为空");
        }
        return Response.success(iLeaveService.getLeaveList(request));
    }

    @Override
    public Response<LeaveDetailVo> leaveDetail(String businessNum) {
        log.info("leaveDetail request:{}", businessNum);
        return Response.success(iLeaveService.leaveCancelDetailInfo(businessNum));
    }

    @Override
    public Response<List<LeaveAuditRecordVo>> leaveAuditList(String businessNum) {
        log.info("leaveAuditList :{}", businessNum);
        if(CommonUtil.StringUtils.isEmpty(businessNum)) {
            throw new ApplicationException("申请单号为空");
        }
        return Response.success(iLeaveService.getLeaveAuditRecord(businessNum));
    }

    @Override
    public Response leaveAudit(LeaveAuditRequest request) {

        log.info("leaveAudit request:{}",request);

        if(!redisUtil.setLockIfAbsent(LEAVE_AUDIT_LOCK, request.getBusinessNum(),3, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！");
        }
        try {
            iLeaveService.leaveAudit(request);
        } finally {
            redisUtil.unLock(LEAVE_AUDIT_LOCK, request.getBusinessNum());
        }
        return Response.success();
    }
}
