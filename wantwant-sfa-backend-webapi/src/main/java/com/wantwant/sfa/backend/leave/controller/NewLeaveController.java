package com.wantwant.sfa.backend.leave.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.leave.api.NewLeaveApi;
import com.wantwant.sfa.backend.leave.request.LeaveAuditPopupRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveAuditRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveCommitInfoRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveDetailRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveListRequest;
import com.wantwant.sfa.backend.leave.request.NewLeaveRevocationRequest;
import com.wantwant.sfa.backend.leave.service.INewLeaveService;
import com.wantwant.sfa.backend.leave.vo.LeaveAuditPopupVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveCheckVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveListVo;
import com.wantwant.sfa.backend.leave.vo.NewLeaveRecordVo;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Api(tags = "请假API")
@RestController
@Slf4j
public class NewLeaveController implements NewLeaveApi {

    private static final String LEAVE_AUDIT_LOCK = "leave:new:audit:lock";
    private static final String LEAVE_COMMIT_LOCK = "leave:new:commit:lock";
    private static final String LEAVE_CANCEL_LOCK = "leave:cancel:lock";


    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private INewLeaveService iLeaveService;

    @ApiOperation(value = "请假审批弹窗", notes = "请假审批弹窗")
    @Override
    public Response<LeaveAuditPopupVo> leaveAuditPopup(LeaveAuditPopupRequest request) {
        log.info("leaveAuditPopup request:{}", request);
        return Response.success(iLeaveService.leaveAuditPopup(request));
    }

    @Override
    public Response<NewLeaveCheckVo> leaveCheck(String person) {
        log.info("leaveCheck request:{}", person);
        return Response.success(iLeaveService.leaveCheck(person));
    }

    @Override
    public Response leaveCommit(NewLeaveCommitInfoRequest request) {
        log.info("leaveCommit request:{}", request);

        if (!redisUtil.setLockIfAbsent(LEAVE_COMMIT_LOCK, String.valueOf(request.getEmployeeInfoId()), 3, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            iLeaveService.leaveCommit(request);
        } finally {
            redisUtil.unLock(LEAVE_COMMIT_LOCK, String.valueOf(request.getEmployeeInfoId()));
        }
        return Response.success();
    }

    @Override
    public Response<IPage<NewLeaveListVo>> leaveList(NewLeaveListRequest request) {
        log.info("leaveList request:{}", request);
        return Response.success(iLeaveService.leaveList(request));
    }

    @Override
    public void leaveListExport(NewLeaveListRequest request) {
        log.info("leaveListExport request:{}", request);
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), NewLeaveListVo.class, iLeaveService.leaveList(request).getRecords());
        try {
            String fileName = "请假单列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @Override
    public Response<NewLeaveListVo> leaveDetail(NewLeaveDetailRequest request) {
        log.info("leaveDetail request:{}", request);
        return Response.success(iLeaveService.leaveDetail(request));
    }

    @Override
    public Response<List<NewLeaveRecordVo>> leaveRecordList(String businessNum) {
        log.info("leaveRecordList request:{}", businessNum);
        if (CommonUtil.StringUtils.isEmpty(businessNum)) {
            throw new ApplicationException("申请单号为空");
        }
        return Response.success(iLeaveService.leaveRecordList(businessNum));
    }

    @Override
    public Response leaveAudit(NewLeaveAuditRequest request) {

        log.info("leaveAudit request:{}", request);

        if (!redisUtil.setLockIfAbsent(LEAVE_AUDIT_LOCK, request.getBusinessNum(), 3, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            iLeaveService.leaveAudit(request);
        } finally {
            redisUtil.unLock(LEAVE_AUDIT_LOCK, request.getBusinessNum());
        }
        return Response.success();
    }

    @Override
    public Response leaveRevocation(NewLeaveRevocationRequest request) {
        log.info("leaveRevocation request:{}", request);

        if (!redisUtil.setLockIfAbsent(LEAVE_AUDIT_LOCK, request.getBusinessNum(), 1, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            iLeaveService.leaveRevocation(request);
        } finally {
            redisUtil.unLock(LEAVE_AUDIT_LOCK, request.getBusinessNum());
        }
        return Response.success();
    }
}
