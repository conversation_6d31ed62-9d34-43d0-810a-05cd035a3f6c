package com.wantwant.sfa.backend.map.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.map.api.MapApi;
import com.wantwant.sfa.backend.map.request.*;
import com.wantwant.sfa.backend.map.service.MapService;
import com.wantwant.sfa.backend.map.service.OrganizationMapService;
import com.wantwant.sfa.backend.map.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Api(tags = "地图API")
@RestController
@Slf4j
public class MapController implements MapApi {

    @Autowired
    private MapService mapService;

    @Resource
    private OrganizationMapService organizationMapService;

    @ApiOperation(value = "地图打卡列表", notes = "地图打卡列表")
    @Override
    public Response<List<MapListVo>> attendanceList(MapListRequest request) {
        log.info("attendanceList request:{}", request);
        return Response.success(mapService.attendanceList(request));
    }

    @ApiOperation(value = "地图打卡列表", notes = "地图打卡列表")
    @Override
    public Response<List<MapListVo>> attendanceList1(MapListRequest request) {
        log.info("attendanceList1 request:{}", request);
        return Response.success(mapService.attendanceList(request));
    }

    @Override
    public Response<List<MapListVo>> attendanceListForPeriod(PeriodMapListRequest request) {
        log.info("attendanceListForPeriod request:{}", request);
        return Response.success(mapService.attendanceListForPeriod(request));
    }

    @ApiOperation(value = "地图打卡列表（根据上级工号获取所有直属下级某一天的轨迹）", notes = "地图打卡列表（根据上级工号获取所有直属下级某一天的轨迹）")
    @Override
    public Response<List<MapListVo>> attendanceListForSome(MapListRequest request) {
        log.info("attendanceListForSome request:{}", request);
        return Response.success(mapService.attendanceListForSome(request));
    }

    @ApiOperation(value = "动态定位提交", notes = "动态定位提交")
    @Override
    public Response realtimePositioningCommit(RealtimePositioningCommitRequest request) {
        log.info("realtimePositioningCommit request:{}", request);
        mapService.realtimePositioningCommit(request);
        return Response.success();
    }

    @ApiOperation(value = "动态定位列表", notes = "动态定位列表")
    @Override
    public Response<List<RealtimePositioningVo>> realtimePositioningList(RealtimePositioningListRequest request) {
        log.info("realtimePositioningList request:{}", request);
        return Response.success(mapService.realtimePositioningList(request));
    }

    @Override
    public Response<OrganizationMapVo> getOrganizationMap(OrganizationMapRequest request) {
        return Response.success(organizationMapService.getOrganizationMap(request));
    }

    @Override
    public Response<List<OrganizationMapProductVo>> getOrganizationMapProductInfo(String queryType) {
        return Response.success(organizationMapService.getOrganizationMapProductInfo(queryType));
    }

    @ApiOperation(value = "组织地图-潜力业绩对比", notes = "组织地图-潜力业绩对比")
    @Override
    public Response<List<OrganizationMapAchievementCompareVo>> getOrganizationMapAchievementCompare(OrganizationMapAchievementRequest request) {
        return Response.success(organizationMapService.getOrganizationMapAchievementCompare(request));
    }

    @ApiOperation(value = "组织地图-地域分布业绩", notes = "组织地图-地域分布业绩")
    @Override
    public Response<OrganizationMapAchievementDistributionVo> getOrganizationMapAchievementDistribution(OrganizationMapAchievementRequest request) {
        return Response.success(organizationMapService.getOrganizationMapAchievementDistribution(request));
    }

}
