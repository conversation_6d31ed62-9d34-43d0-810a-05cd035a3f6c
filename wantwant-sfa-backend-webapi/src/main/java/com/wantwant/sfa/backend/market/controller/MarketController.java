package com.wantwant.sfa.backend.market.controller;

import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.market.request.*;
import com.wantwant.sfa.backend.market.api.MarketApi;
import com.wantwant.sfa.backend.market.service.MarketService;
import com.wantwant.sfa.backend.market.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/09/26/下午1:08
 */
@RestController
@Slf4j
public class MarketController implements MarketApi {
    @Autowired
    private MarketService marketService;

    @Override
    public Response<List<SignupMarketVo>> getSignupMarketTree(String branchId) {
        List<SignupMarketVo> list = marketService.getSignupMarketTree(branchId);
        return Response.success(list);
    }

    @Override
    public Response updatePositionMarketRelation(PositionMarketRelationRequest request) {

        marketService.updatePositionMarketRelation(request);

        return Response.success();
    }

    @Override
    public Response<Page<MarketAndCustomer>> marketAndCustomerList(MarketAndCustomerRequest request) {
        Page<MarketAndCustomer> page = marketService.marketAndCustomerList(request);
        return Response.success(page);
    }

    @Override
    public void marketAndCustomerExport(MarketAndCustomerExportRequest request) {
        log.info("start marketAndCustomerExport export request:{}",request);

        Long startTime = System.currentTimeMillis();

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        Assert.notNull(servletRequestAttributes,"系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        try {
            Workbook wb = marketService.marketAndCustomerExport(request);
            log.info("workbook size : {}",wb.getSheetAt(0).getPhysicalNumberOfRows());

            String fileName = "市场与客户列表";
            if(wb instanceof HSSFWorkbook){
                fileName=fileName+".xls";
            }else{
                fileName=fileName+".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident") ) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

        log.info("导出订单列表用时:{}ms",(System.currentTimeMillis()-startTime));
    }

    @Override
    public Response updateCustomerMarketRelation(CustomerMarketRelationRequest request) {
        marketService.updateCustomerMarketRelation(request);
        return Response.success();
    }

}
