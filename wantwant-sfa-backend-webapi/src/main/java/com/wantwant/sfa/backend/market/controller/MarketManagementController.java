package com.wantwant.sfa.backend.market.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.market.api.MarketManagementApi;
import com.wantwant.sfa.backend.market.request.*;
import com.wantwant.sfa.backend.market.service.IMarketManagementService;
import com.wantwant.sfa.backend.market.vo.*;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/19/下午2:49
 */
@RestController
@Slf4j
public class MarketManagementController implements MarketManagementApi {
    @Autowired
    private IMarketManagementService marketManagementService;
    @Autowired
    private ICheckCustomerService checkCustomerService;


    @Override
    public Response<List<MarketProfileVo>> getMarketProfile(MarketProfileRequest request) {
        List<MarketProfileVo> vo = marketManagementService.getMarketProfile(request);
        return Response.success(vo);
    }

    @Override
    public Response<Page<CompanyMarketVo>> getCompanyMarket(CompanyMarketRequest request) {
        Page<CompanyMarketVo> vo = marketManagementService.getCompanyMarket(request);
        return  Response.success(vo);
    }

    @Override
    public void companyMarketExport(CompanyMarketRequest request) {
        marketManagementService.companyMarketExport(request);
    }

    @Override
    public Response<CeoMarketRelationVo> getCeoMarketRelation(Integer employeeId) {
        log.info("【合伙人信息获取】employeeId:{}",employeeId);
        CeoMarketRelationVo vo = marketManagementService.getCeoMarketRelation(employeeId);
        return Response.success(vo);
    }

    @Override
    public Response<List<MarketTreeV2Vo>> getMarketTree(String organizationId,int interviewRecordId) {
        List<MarketTreeV2Vo> list  = marketManagementService.getMarketTree(organizationId,interviewRecordId);
        return Response.success(list);
    }

    @Override
    public Response updateCeoMarketRelation(CeoMarketRelationRequest request) {
        log.info("【合伙人信息修改】request:{}",request);
        marketManagementService.updateCeoMarketRelation(request);
        return Response.success();
    }

    @Override
    public Response<MarketDivisionPermissionVo> getMarketDivision(MarketDivisionRequest request) {
        log.info("【市场划分】request:{}",request);
        MarketDivisionPermissionVo vo = new MarketDivisionPermissionVo();
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);
        if(7 == personInfo.getPositionTypeId()){
            vo.setCanEdit(true);
        }
        Page<MarketDivisionVo> page = marketManagementService.getMarketDivision(request);
        vo.setPages(page);
        return Response.success(vo);
    }

    @Override
    public void marketDivisionExport(MarketDivisionRequest request) {
        marketManagementService.marketDivisionExport(request);
    }

    @Override
    public Response<MarketV2Vo> getMarketInfo(Integer marketId) {
        log.info("【查询大标市场信息】marketId:{}",marketId);
        MarketV2Vo vo = marketManagementService.getMarketInfo(marketId);
        return Response.success(vo);
    }

    @Override
    public Response updateMarket(UpdateMarketRequest request) {
        log.info("【创建修改市场】request:{}",request);
        marketManagementService.updateMarket(request);
        return Response.success();
    }

    @Override
    public Response updateSmallMarket(UpdateSmallMarketRequest request) {
        log.info("【创建小标市场】request:{}",request);
        marketManagementService.updateSmallMarket(request);
        return Response.success();
    }

    @Override
    public Response<SmallMarketInfoVo> getSmallMarketInfo(Integer marketId,Integer smallMarketId) {
        log.info("【获取小标市场信息】marketId:{},smallMarketId:{}",marketId,smallMarketId);
        SmallMarketInfoVo vo = marketManagementService.getSmallMarketInfo(marketId,smallMarketId);
        return Response.success(vo);
    }

    @Override
    public Response deleteSmallMarket(DeleteSmallMarketRequest request) {
        log.info("【删除小标市场】request:{}",request);
        marketManagementService.deleteSmallMarket(request);
        return  Response.success();
    }

    @Override
    public Response deleteMarket(DeleteMarketRequest request) {
        log.info("【删除大标市场】request:{}",request);
        marketManagementService.deleteMarket(request);
        return Response.success();
    }

    @Override
    public Response<MarketControlVo> getMarketControl(String companyCode) {
        log.info("【market control】companyCode:{}",companyCode);
        MarketControlVo vo = marketManagementService.getMarketControl(companyCode);
        return Response.success(vo);
    }

    @Override
    public Response<IPage<OrganizationRegiontVo>> getOrganizationRegion(OrganizationRegionRequest request) {
        return  Response.success(marketManagementService.getOrganizationRegion(request));
    }
}
