package com.wantwant.sfa.backend.market.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.market.api.MarketV2Api;
import com.wantwant.sfa.backend.market.request.MarketSearchRequest;
import com.wantwant.sfa.backend.market.request.MarketSearchWithOrderRequest;
import com.wantwant.sfa.backend.market.service.IMarketV2Service;
import com.wantwant.sfa.backend.market.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/19/下午8:13
 */
@RestController
public class MarketV2Controller implements MarketV2Api {
    @Autowired
    private IMarketV2Service marketV2Service;

    @Override
    public Response<IPage<EmpMarketVo>> empMarketList(MarketSearchRequest request) {
        IPage<EmpMarketVo> page = marketV2Service.empMarketList(request);
        return Response.success(page);
    }

    @Override
    public void exportEmpMarket(MarketSearchRequest request) {
        marketV2Service.exportEmpMarket(request);
    }
}
