package com.wantwant.sfa.backend.market.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.market.api.ProductionApi;
import com.wantwant.sfa.backend.market.request.SynCategoryRequest;
import com.wantwant.sfa.backend.market.service.ProductionService;
import com.wantwant.sfa.backend.market.vo.ProductionCategoryVo;
import com.wantwant.sfa.backend.market.vo.ProductionExperimentalVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/21/上午11:57
 */
@RestController
@Slf4j
public class ProductionController implements ProductionApi {
    @Autowired
    private ProductionService productionService;

    @Override
    public Response<List<ProductionCategoryVo>> getProductionCategory() {
        int businessGroup = RequestUtils.getBusinessGroup();
        List<ProductionCategoryVo> list = productionService.selectAllCategory(businessGroup);
        return Response.success(list);
    }

    @Override
    public Response synCategory(SynCategoryRequest request) {
        log.info("synCategory list :{}",request);
        productionService.synCategory(request);
        return Response.success();
    }
}
