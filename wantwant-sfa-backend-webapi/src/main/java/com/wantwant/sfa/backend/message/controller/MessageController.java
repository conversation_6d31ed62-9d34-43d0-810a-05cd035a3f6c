package com.wantwant.sfa.backend.message.controller;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.message.api.MessageApi;
import com.wantwant.sfa.backend.message.request.DeliveryAbnormalRequest;
import com.wantwant.sfa.backend.message.request.PushMessageRequest;
import com.wantwant.sfa.backend.message.request.PushOrderMessageRequest;
import com.wantwant.sfa.backend.message.request.UpgradeMessageRequest;
import com.wantwant.sfa.backend.service.IMessageService;
import com.wantwant.sfa.backend.util.RedisUtil;

import lombok.extern.slf4j.Slf4j;


@RestController
@Slf4j
public class MessageController implements MessageApi {
	
	@Autowired
	private IMessageService messageService;
    @Autowired
    private RedisUtil redisUtil;
	
    public static final String LOCK_HEAD_ORDER_PROCESSING_MESSAGE = "orderPaidMessageLock";

	
	@Override
	public Response PushOrderMessage(PushOrderMessageRequest request) {
		log.info("start MessageController PushOrderMessage request:{}",request);
		
		String orderCode = request.getOrderCode();
		
		if(request.getMessageType() != 201 || redisUtil.setIfAbsent(redisUtil.getLOCK_KEY_HEAD()+LOCK_HEAD_ORDER_PROCESSING_MESSAGE+":"+orderCode, "1", 1, TimeUnit.DAYS)) {

			messageService.pushOrderMessage(request);
			
			return Response.success();
			
		}else {
			  return Response.error("请求正在处理中！～");
		}
		

	}


	@Override
	public Response upgrade(UpgradeMessageRequest request) {
		log.info("start MessageController upgrade request:{}",request);

		messageService.upgrade(request);
		
		return Response.success();
	}


	@Override
	public Response pushMessage(PushMessageRequest request) {
		log.info("start MessageController pushMessage request:{}",request);
		
		messageService.pushMessage(request);
		
		return Response.success();
	}

	@Override
	public Response pushDeliveryAbnormalMessage(DeliveryAbnormalRequest request) {
		log.info("start MessageController deliveryAbnormal request:{}",request);
        messageService.pushDeliveryAbnormalMessage(request.getDate());
		return null;
	}

}
