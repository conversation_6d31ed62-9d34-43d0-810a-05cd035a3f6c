package com.wantwant.sfa.backend.metrics.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.metrics.api.AlterApi;
import com.wantwant.sfa.backend.metrics.request.*;
import com.wantwant.sfa.backend.metrics.service.IAlterService;
import com.wantwant.sfa.backend.metrics.service.ILogisticsService;
import com.wantwant.sfa.backend.metrics.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/29/下午4:39
 */
@RestController
@Slf4j
public class AlterController implements AlterApi {

    @Autowired
    private IAlterService alterService;
    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private ILogisticsService logisticsService;

    @Override
    public Response add(@Valid AlterRequest alterRequest) {
        log.info("【alter add】request:{}",alterRequest);

        alterService.add(alterRequest);

        return Response.success();
    }

    @Override
    public Response<Page<AlterVo>> selectList(AlterSearchRequest alterSearchRequest) {
        log.info("【alter search】request:{}",alterSearchRequest);
        alterSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
        if(zw_senior_hr_employee_id.equals(alterSearchRequest.getPerson())){
            alterSearchRequest.setAuditor(true);
        }

        Page<AlterVo> page =  alterService.selectList(alterSearchRequest);

        return Response.success(page);
    }

    @Override
    public Response batchDelete(MetricsBatchDeleteRequest metricsBatchDeleteRequest) {
        log.info("【alter batch delete】request:{}",metricsBatchDeleteRequest);

        alterService.batchDelete(metricsBatchDeleteRequest);

        return Response.success();
    }

    @Override
    public Response copy(@Valid MetricsBatchDeleteRequest metricsBatchDeleteRequest) {
        log.info("【alter copy】request:{}",metricsBatchDeleteRequest);
        alterService.copy(metricsBatchDeleteRequest);
        return Response.success();
    }

    @Override
    public Response<AlterInfoVo> getAlterInfo(Integer id) {
        log.info("【alter get info】id:{}",id);
        AlterInfoVo vo = alterService.getAlterInfo(id);
        return Response.success(vo);
    }

    @Override
    public Response alterStatusTrigger(@Valid AlterStatusTriggerRequest request) {
        log.info("【alter status trigger】request:{}",request);

        alterService.alterStatusTrigger(request);
        return Response.success();
    }

    @Override
    public Response update(@Valid AlterUpdateRequest request) {
        log.info("【alter update】request:{}",request);

        alterService.update(request);
        return Response.success();
    }

    @Override
    public Response submit(@Valid AlterSubmitRequest request) {
        log.info("【alter template submit】request:{}",request);

        alterService.submit(request);

        return Response.success();
    }

    @Override
    public Response audit(@Valid AlterAuditRequest request) {
        log.info("【alter template audit】request:{}",request);

        alterService.audit(request);

        return Response.success();
    }


    @Override
    public Response logisticsAdd(LogisticsAddRequest request) {
        log.info("【logistics add】request:{}",request);
        logisticsService.logisticsAdd(request);
        return Response.success();
    }


    @Override
    public Response logisticsUpdate(LogisticsUpdateRequest request) {
        log.info("【logistics update】request:{}",request);
        logisticsService.logisticsUpdate(request);
        return Response.success();
    }


    @Override
    public Response<LogisticsInfoVo> getLogisticsInfo(Integer id) {
        log.info("【logistics LogisticsInfo】id:{}",id);

        return Response.success(logisticsService.getLogisticsInfo(id));
    }

    @Override
    public Response<List<Map<String,Integer>>> getLogisticsParam(LogisticsParamRequest request) {
        log.info("【logistics Param】getLogisticsParam:{}",request);
        return Response.success(logisticsService.getLogisticsParam(request));
    }

    @Override
    public Response logisticsSubmit(AlterSubmitRequest request) {
        log.info("【logistics Param】getLogisticsParam:{}",request);
        logisticsService.logisticsSubmit(request);
        return Response.success();
    }

    @Override
    public Response<Integer> alertAuditCheck(String employeeId, Integer applicationType) {

        log.info("【alter Param】employeeId:{},applicationType:{}", employeeId, applicationType);
        Integer canAudit = alterService.canAuditCheck(employeeId, applicationType);
        return Response.success(canAudit);
    }

    @Override
    public Response<List<LogisticsDisplayDetailVo>> logisticsDisplay(LogisticsDisplayRequest request) {
        log.info("【logistics  Display】logisticsDisplay:{}",request);

        return Response.success(logisticsService.logisticsDisplay(request));

    }

    @Override
    public Response<LogisticsDetailVo> logisticsDisplayDetail(LogisticsDisplayRequest request) {
        log.info("【logistics  DisplayDetail】logisticsDisplay:{}",request);

        return Response.success(logisticsService.logisticsDisplayDetail(request));
    }

    @Override
    public Response<List<Map<String,String>>> logisticsDeptName() {
        return Response.success(logisticsService.logisticsDeptName());
    }

    @Override
    public void logisticsExportTemplate() {
        logisticsService.logisticsExportTemplate();
    }

    @Override
    public Response<List<String>> upload(MultipartFile file, String person, String theYear) {

        log.info("【logistics upload】person:{} theYear:{}",person,theYear);

        List<String> errMsg = logisticsService.upload(file, person,theYear);

        return Response.success(errMsg);
    }

    @Override
    public Response<Page<LogisticsMetricsVo>> logisticsMetricsList(LogisticsMetricsRequest request) {
        log.info("【logistics Metrics List 】request:{}",request);

        return Response.success(logisticsService.logisticsMetricsList(request));
    }

    @Override
    public Response logisticsMetricsDelete(Integer id) {
        log.info("【logistics Metrics Delete 】id:{}",id);
        logisticsService.logisticsMetricsDelete(id);
        return Response.success();


    }

    @Override
    public Response<List<LogisticsMetricsDetailVo>> logisticsMetricsDetail(Integer id) {
        log.info("【logistics Metrics detail 】id:{}",id);
        return Response.success(logisticsService.logisticsMetricsDetail(id));

    }

    @Override
    public Response<List<Map<String, Object>>> chooseBusinessGroup() {
        return Response.success(logisticsService.chooseBusinessGroup());

    }
}
