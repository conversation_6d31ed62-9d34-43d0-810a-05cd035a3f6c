package com.wantwant.sfa.backend.metrics.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.metrics.api.MetricsCategoryApi;
import com.wantwant.sfa.backend.metrics.request.*;
import com.wantwant.sfa.backend.metrics.service.IMetricsCategoryService;
import com.wantwant.sfa.backend.metrics.vo.MetricsCategoryInfoVo;
import com.wantwant.sfa.backend.metrics.vo.MetricsCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/28/下午5:53
 */
@RestController
@Slf4j
public class MetricsCategoryController implements MetricsCategoryApi {

    @Autowired
    private IMetricsCategoryService metricsCategoryService;

    @Override
    public Response add(@Valid MetricsCategoryRequest request) {
        log.info("【add metrics category】request:{}",request);
        metricsCategoryService.add(request);
        return Response.success();
    }

    @Override
    public Response<List<MetricsCategoryVo>> list(MetricsCategorySearchRequest request) {
        log.info("【metrics category search】request:{}",request);

        List<MetricsCategoryVo> list = metricsCategoryService.list(request);

        return Response.success(list);
    }

    @Override
    public Response<MetricsCategoryInfoVo> info(Integer id) {
        log.info("【metrics category info】id:{}",id);
        MetricsCategoryInfoVo vo = metricsCategoryService.info(id);
        return Response.success(vo);
    }

    @Override
    public Response update(@Valid MetricsCategoryUpdateRequest request) {
        log.info("【metrics category update】request:{}",request);

        metricsCategoryService.update(request);

        return Response.success();
    }

    @Override
    public Response delete(@Valid MetricsCategoryDeleteRequest request) {
        log.info("【metrics delete】request:{}",request);

        metricsCategoryService.delete(request);

        return Response.success();
    }

    @Override
    public Response triggerStatus(@Valid MetricsCategoryTriggerStatusRequest request) {
        log.info("【metrics trigger status】request:{}",request);

        metricsCategoryService.triggerStatus(request);

        return Response.success();
    }
}
