package com.wantwant.sfa.backend.metrics.controller;

import com.wantwant.commons.cons.StatusCode;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.ex.ValidParamException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.metrics.api.MetricsApi;
import com.wantwant.sfa.backend.metrics.request.*;
import com.wantwant.sfa.backend.metrics.service.IMetricsService;
import com.wantwant.sfa.backend.metrics.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/28/下午8:12
 */
@RestController
@Slf4j
public class MetricsController implements MetricsApi {
    @Autowired
    private IMetricsService metricsService;

    @Override
    public Response add(@Valid MetricsRequest request) {
        log.info("【metrics add】request:{}",request);

        metricsService.add(request);

        return Response.success();
    }

    @Override
    public Response<Page<MetricsVo>> selectList(MetricsSearchRequest request) {
        log.info("【metrics list】request:{}",request);

        Page<MetricsVo> page = metricsService.selectList(request);

        return Response.success(page);
    }

    @Override
    public void exportMetricsList(MetricsSearchRequest request) {
        log.info("【metrics export】request:{}",request);

        metricsService.export(request);
    }

    @Override
    public void exportMetricsListInfo(MetricsSearchRequest request, HttpServletRequest req, HttpServletResponse res) {
        metricsService.exportMetricsListInfo(request, req, res);
    }

    @Override
    public Response triggerStatus(@Valid MetricsTriggerStatusRequest request) {
        log.info("【metrics status trigger】 request:{}",request);
        metricsService.triggerStatus(request);
        return Response.success();
    }

    @Override
    public Response batchDelete(@Valid MetricsBatchDeleteRequest request) {
        log.info("【metrics batch delete】request:{}",request);
        metricsService.batchDelete(request);
        return Response.success();
    }

    @Override
    public Response<MetricsDetailsDto> getMetricsInfo(int id) {
        log.info("【metrics info】id:{}",id);
        return Response.success(metricsService.getMetricsInfo(id));
    }

    @Override
    public Response update(@Valid MetricsUpdateRequest request) {
        log.info("【metrics update】request:{}",request);
        metricsService.update(request);
        return Response.success();
    }

    @Override
    public Response<List<String>> upload(MultipartFile multipartFile, String person) {
        log.info("【metrics upload】person:{}",person);

        List<String> errMsg = metricsService.upload( multipartFile, person);

        return Response.success(errMsg);
    }

    @Override
    public Response<List<String>> uploadInfo(MultipartFile file, String person) {
        List<String> returnMessages = new ArrayList<>();
        try {
            metricsService.uploadInfo(file, person,returnMessages);
        } catch (ValidParamException e) {
            return Response.error(returnMessages, StatusCode.ERROR.getCode(),"解析文件异常");
        }catch (ApplicationException e){
            returnMessages.add(e.getMessage());
            return Response.error(returnMessages, StatusCode.ERROR.getCode(),"读取解析异常");
        }
        return Response.success();
    }

    @Override
    public Response<List<MetricsSelectVo>> getMetricsSelect(int domainId) {
        log.info("【metrics select】domain id : {}", domainId);

        List<MetricsSelectVo> list = metricsService.getMetricsSelect(domainId);

        return Response.success(list);
    }

    @Override
    public Response<List<MetricsTreeVo>> getMetricsTree(String all) {

        List<MetricsTreeVo> list = metricsService.getMetricsTree(all);

        return Response.success(list);
    }

    @Override
    public Response<QuerySourceTimeDimensionInfoDto> queryTimeDimensionInfo(QuerySourceTimeDimensionInfoReq req) {
        return Response.success(metricsService.queryTimeDimensionInfo(req));
    }

    @Override
    public Response<MetricsProcessVo> getPermission(String person) {
        log.info("【metrics permission】person:{}",person);

        MetricsProcessVo vo = metricsService.getMetricsPermission(person);

        return Response.success(vo);
    }

    @Override
    public Response<List<String>> getMetricsType() {
       List<String> list = metricsService.getMetricsType();
        return Response.success(list);
    }

}
