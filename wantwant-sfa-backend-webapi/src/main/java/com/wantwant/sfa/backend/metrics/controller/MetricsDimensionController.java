package com.wantwant.sfa.backend.metrics.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.metrics.api.MetricsDimensionApi;
import com.wantwant.sfa.backend.metrics.service.IMetricsDimensionService;
import com.wantwant.sfa.backend.metrics.vo.MetricsDimensionDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metrics.controller
 * @Description:
 * @Date: 2025/1/2 11:50
 */
@RestController
@Slf4j
public class MetricsDimensionController implements MetricsDimensionApi {
    @Resource
    private IMetricsDimensionService metricsDimensionService;

    @Override
    public Response<MetricsDimensionDto> getMetricsDimension() {
        return Response.success(metricsDimensionService.getMetricsDimension());
    }
}
