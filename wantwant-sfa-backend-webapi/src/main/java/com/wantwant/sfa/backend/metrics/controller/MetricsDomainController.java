package com.wantwant.sfa.backend.metrics.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.metrics.api.MetricsDomainApi;
import com.wantwant.sfa.backend.metrics.service.IMetricsDomainService;
import com.wantwant.sfa.backend.metrics.vo.MetricsDomainVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/28/下午4:54
 */
@RestController
public class MetricsDomainController implements MetricsDomainApi {
    @Autowired
    private IMetricsDomainService metricsDomainService;

    @Override
    public Response<List<MetricsDomainVo>> getList() {
        List<MetricsDomainVo> list = metricsDomainService.getList();
        return Response.success(list);
    }
}
