package com.wantwant.sfa.backend.metricsEarlyWarning.control;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.metricsEarlyWarning.api.MetricsEarlyWarningApi;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.MetricsEarlyWarningEnumDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.MetricsEarlyWarningRuleInfoDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.PageQueryMetricsEarlyWarningRuleInfoDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.dto.QueryMetricsInfoBeforeAddRuleDto;
import com.wantwant.sfa.backend.metricsEarlyWarning.request.*;
import com.wantwant.sfa.backend.metricsEarlyWarning.service.ISfaMetricsEarlyWarningService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.metricsEarlyWarning.control
 * @Description:
 * @Date: 2025/2/10 10:16
 */
@RestController
public class MetricsEarlyWarningController implements MetricsEarlyWarningApi {

    @Resource
    private ISfaMetricsEarlyWarningService metricsEarlyWarningService;

    @Override
    public Response add(AddMetricsEarlyWarningRuleReq request) {
        metricsEarlyWarningService.add(request);
        return Response.success();
    }

    @Override
    public Response update(UpdateMetricsEarlyWarningRuleReq request) {
        metricsEarlyWarningService.update(request);
        return Response.success();
    }

    @Override
    public Response copy(CopyMetricsEarlyWarningRuleReq request) {
        metricsEarlyWarningService.copy(request);
        return Response.success();
    }

    @Override
    public Response updateStatus(UpdateMetricsEarlyWarningRuleStatusReq request) {
        metricsEarlyWarningService.updateStatus(request);
        return Response.success();
    }

    @Override
    public Response batchDelete(BatchDeleteMetricsEarlyWarningRuleReq request) {
        metricsEarlyWarningService.batchDelete(request);
        return Response.success();
    }

    @Override
    public Response<MetricsEarlyWarningEnumDto> getEnumInfo() {
        return Response.success(metricsEarlyWarningService.getEnumInfo());
    }

    @Override
    public Response<MetricsEarlyWarningRuleInfoDto> getMetricsEarlyWarningRuleInfo(QueryMetricsEarlyWarningRuleDetailReq request) {
        return Response.success(metricsEarlyWarningService.getMetricsEarlyWarningRuleInfo(request));
    }

    @Override
    public Response<Page<PageQueryMetricsEarlyWarningRuleInfoDto>> pageQuery(PageQueryMetricsEarlyWarningRuleInfoReq request) {
        return Response.success(metricsEarlyWarningService.pageQuery(request));
    }

    @Override
    public Response<List<QueryMetricsInfoBeforeAddRuleDto>> queryMetricsInfoBeforeAddRule(QueryMetricsInfoBeforeAddRuleRequest request) {
        return Response.success(metricsEarlyWarningService.queryMetricsInfoBeforeAddRule(request));
    }
}
