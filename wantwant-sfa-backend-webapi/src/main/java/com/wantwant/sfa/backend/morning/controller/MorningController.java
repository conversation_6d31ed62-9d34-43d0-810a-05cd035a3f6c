package com.wantwant.sfa.backend.morning.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.morning.api.MorningApi;
import com.wantwant.sfa.backend.morning.request.MorningCommitRequest;
import com.wantwant.sfa.backend.morning.request.MorningDeleteRequest;
import com.wantwant.sfa.backend.morning.request.MorningDetailRequest;
import com.wantwant.sfa.backend.morning.request.MorningLikedRequest;
import com.wantwant.sfa.backend.morning.request.MorningListRequest;
import com.wantwant.sfa.backend.morning.request.MorningRankingListRequest;
import com.wantwant.sfa.backend.morning.request.MorningUpdateRequest;
import com.wantwant.sfa.backend.morning.request.MorningViewedListRequest;
import com.wantwant.sfa.backend.morning.request.MorningViewedRequest;
import com.wantwant.sfa.backend.morning.service.MorningService;
import com.wantwant.sfa.backend.morning.vo.MorningDetailVo;
import com.wantwant.sfa.backend.morning.vo.MorningListVo;
import com.wantwant.sfa.backend.morning.vo.MorningRankingListVo;
import com.wantwant.sfa.backend.morning.vo.MorningViewedListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Optional;


@Api(tags = "晨语API")
@RestController
@Slf4j
public class MorningController implements MorningApi {
    @Autowired
    private MorningService morningService;
    @Autowired
    private SettingsMapper settingsMapper;

    @ApiOperation(value = "晨语提交", notes = "晨语提交")
    @Override
    public Response morningCommit(MorningCommitRequest request) {
        log.info("morningCommit request:{}", request);
        morningService.morningCommit(request);
        return Response.success();
    }

    @ApiOperation(value = "晨语删除", notes = "晨语删除")
    @Override
    public Response morningDelete(MorningDeleteRequest request) {
        log.info("morningDelete request:{}", request);
        morningService.morningDelete(request);
        return Response.success();
    }

    @ApiOperation(value = "晨语更新", notes = "晨语更新")
    @Override
    public Response morningUpdate(MorningUpdateRequest request) {
        log.info("morningUpdate request:{}", request);
        morningService.morningUpdate(request);
        return Response.success();
    }

    @ApiOperation(value = "晨语列表", notes = "晨语列表")
    @Override
    public Response<IPage<MorningListVo>> morningList(MorningListRequest request) {
        log.info("morningList request:{}", request);
        return Response.success(morningService.morningList(request));
    }

    @ApiOperation(value = "晨语列表导出", notes = "晨语列表导出")
    @Override
    public void morningListExport(MorningListRequest request) {
        log.info("morningListExport request:{}", request);
        request.setRows(Optional.ofNullable(settingsMapper.getSfaSettingsByCode("export_max")).map(Integer::parseInt).orElse(1000));
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), MorningListVo.class, morningService.morningList(request).getRecords());
        try {
            String fileName = "晨语列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @ApiOperation(value = "晨语详情", notes = "晨语详情")
    @Override
    public Response<MorningDetailVo> morningDetail(MorningDetailRequest request) {
        log.info("morningDetail request:{}", request);
        return Response.success(morningService.morningDetail(request));
    }

    @ApiOperation(value = "晨语查看", notes = "晨语查看")
    @Override
    public Response morningViewed(MorningViewedRequest request) {
        log.info("morningViewed request:{}", request);
        morningService.morningViewed(request);
        return Response.success();
    }

    @ApiOperation(value = "晨语点赞", notes = "晨语点赞")
    @Override
    public Response morningLiked(MorningLikedRequest request) {
        log.info("morningLiked request:{}", request);
        morningService.morningLiked(request);
        return Response.success();
    }

    @ApiOperation(value = "晨语排行列表", notes = "晨语排行列表")
    @Override
    public Response<IPage<MorningRankingListVo>> morningRankingList(MorningRankingListRequest request) {
        log.info("morningRankingList request:{}", request);
        return Response.success(morningService.morningRankingList(request));
    }

    @ApiOperation(value = "晨语排行列表导出", notes = "晨语排行列表导出")
    @Override
    public void morningRankingListExport(MorningRankingListRequest request) {
        log.info("morningRankingListExport request:{}", request);
        request.setRows(Optional.ofNullable(settingsMapper.getSfaSettingsByCode("export_max")).map(Integer::parseInt).orElse(1000));
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), MorningRankingListVo.class, morningService.morningRankingList(request).getRecords());
        try {
            String fileName = "晨语排行列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @ApiOperation(value = "晨语查看数列表", notes = "晨语查看数列表")
    @Override
    public Response<IPage<MorningViewedListVo>> morningViewedList(MorningViewedListRequest request) {
        log.info("morningViewedList request:{}", request);
        return Response.success(morningService.morningViewedList(request));
    }

    @ApiOperation(value = "晨语查看数列表导出", notes = "晨语查看数列表导出")
    @Override
    public void morningViewedListExport(MorningViewedListRequest request) {
        log.info("morningViewedListExport request:{}", request);
        request.setRows(Optional.ofNullable(settingsMapper.getSfaSettingsByCode("export_max")).map(Integer::parseInt).orElse(1000));
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), MorningViewedListVo.class, morningService.morningViewedList(request).getRecords());
        try {
            String fileName = "晨语查看数列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

}
