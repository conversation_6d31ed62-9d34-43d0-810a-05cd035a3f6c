package com.wantwant.sfa.backend.notify;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.Task.LeaveAuditTask;
import com.wantwant.sfa.backend.Task.NotifyTask;
import com.wantwant.sfa.backend.application.NotifyApplication;
import com.wantwant.sfa.backend.notify.dto.*;
import com.wantwant.sfa.backend.notify.vo.*;
import com.wantwant.sfa.backend.service.NotifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 通知管理相关接口
 *
 * @date 3/7/22 5:03 PM
 * @version 1.0
 */
@Api(tags = "通知管理相关接口")
@RestController
@RequestMapping("/notify")
public class NotifyController {

	@Autowired
	private NotifyService notifyService;

	@Autowired
	private NotifyTask notifyTask;

	@Autowired
	private LeaveAuditTask leaveAuditTask;

	@Resource
	private NotifyApplication notifyApplication;

	/**
	 * 分页查询通知
	 *
	 * @param notifyQueryDto
	 * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.notify.vo.NotifyVO>
	 * @date: 3/7/22 5:20 PM
	 */
	@ApiOperation(notes = "分页查询通知", value = "分页查询通知")
	@GetMapping
	public Response<IPage<NotifyVO>> queryNotifyPage(NotifyQueryDTO notifyQueryDto) {
		return Response.success(notifyService.queryNotifyPage(notifyQueryDto));
	}

	@ApiOperation(notes = "合伙人通知", value = "合伙人通知")
	@PostMapping("/ceoNotify")
	public Response ceoNotify(@Valid @RequestBody NotifyPushRequest notifyPushRequest){

		notifyApplication.push(notifyPushRequest);

		return Response.success();
	}

	@ApiOperation(notes = "发送通知", value = "发送通知")
	@PostMapping
	public Response notify(@Valid @RequestBody NotifyPushRequest notifyPushRequest){
		notifyApplication.push(notifyPushRequest);

		return Response.success();
	}

	@ApiOperation(notes = "获取未读数", value = "获取未读数")
	@GetMapping("/unReadCount")
	public Response<Integer> unReadCount(NotifyQueryDTO notifyQueryDto){
		return Response.success(notifyService.queryUnReadCount(notifyQueryDto));
	}

	/**
	 * 获取消息条数
	 *
	 * @param notifyQueryDto
	 * @return:
	 * @date: 3/7/22 5:20 PM
	 */
	@ApiOperation(notes = "获取消息条数", value = "获取消息条数")
	@PostMapping("/count")
	public Response<Integer> NotifyCount(@RequestBody NotifyQueryDTO notifyQueryDto){
		return Response.success(notifyService.notifyCount(notifyQueryDto));
	}

	/**
	 * 通知详情
	 *
	 * @param templateId
	 * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.notify.vo.NotifyDetailVO>
	 * @date: 7/11/22 2:41 PM
	 */
	@ApiOperation(notes = "通知详情", value = "通知详情")
	@GetMapping("/detail/{templateId}")
	public Response<NotifyDetailVO> detail(@PathVariable("templateId") @NotNull(message = "templateId不能为空") Long templateId) {
		return Response.success(notifyService.detail(templateId));
	}

	@ApiOperation(notes = "获取最后条消息", value = "获取最后条消息")
	@GetMapping("/lastNotify/{person}")
	public Response<String> lastNotify(@PathVariable String person){
		return Response.success(notifyService.lastNotify(person));
	}

	/**
	 * 根据ID批量已读
	 *
	 * @param request
	 * @return: int
	 * @date: 3/7/22 6:54 PM
	 */
	@ApiOperation(value = "根据ID批量已读")
	@RequestMapping(value = "/batch",method = {RequestMethod.POST,RequestMethod.PUT})
	public Response<Integer> readByIds(@Valid @RequestBody NotifyBatchRequest request) {
		return Response.success(notifyService.readByIds(request));
	}

	/**
	 * 通知根据id收藏或取消
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 10/14/22 6:26 PM
	 */
	@ApiOperation(value = "通知根据id收藏或取消")
	@PostMapping("/notifyFavorite")
	public Response notifyFavorite(@RequestBody FavoriteRequest request){
		notifyService.notifyFavorite(request);
		return Response.success();
	}

	/**
	 * 帮助查看列表
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.notify.vo.HelpVO>>
	 * @date: 7/25/22 2:07 PM
	 */
	@ApiOperation(notes = "帮助查看列表", value = "帮助查看列表")
	@GetMapping(value = "/queryHelpPage")
	public Response<IPage<HelpVO>> queryHelpPage(HelpQueryRequest request) {
		return Response.success(notifyService.queryHelpPage(request));
	}

	@ApiOperation(notes = "帮助查看列表详情", value = "帮助查看列表详情")
	@GetMapping(value = "/message/detail/{templateId}")
	public Response<HelpVO> queryMessageDetail(@PathVariable("templateId") @NotNull(message = "templateId不能为空") Long templateId) {
		return Response.success(notifyService.queryMessageDetail(templateId));
	}


	/**
	 * 消息根据id收藏或取消
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response
	 * @date: 10/14/22 6:28 PM
	 */
	@ApiOperation(notes = "消息根据id收藏或取消", value = "消息根据id收藏或取消")
	@PostMapping(value = "/messageFavorite")
	public Response messageFavorite(@RequestBody FavoriteRequest request) {
		notifyService.messageFavorite(request);
		return Response.success();
	}

	/**
	 * 根据messageId获取消息接收人
	 *
	 * @param messageId
	 * @return: com.wantwant.commons.web.response.Response<java.util.List<java.lang.String>>
	 * @date: 10/14/22 1:49 PM
	 */
	@ApiOperation(notes = "根据messageId获取消息接收人", value = "根据messageId获取消息接收人")
	@GetMapping(value = "/queryRecipients")
	public Response<List<String>> queryRecipients(@ApiParam(value = "messageId", required = true) @RequestParam Integer messageId) {
		return Response.success(notifyService.queryRecipients(messageId));
	}

	/**
	 * 帮助已读
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 7/25/22 5:12 PM
	 */
	@ApiOperation(notes = "帮助已读", value = "帮助已读")
	@PostMapping(value = "/helpRead")
	public Response<Integer> helpRead(@Valid @RequestBody HelpUpdateRequest request) {
		return Response.success(notifyService.helpRead(request));
	}

	/**
	 * 帮助删除
	 *
	 * @param request
	 * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
	 * @date: 7/25/22 5:12 PM
	 */
	@ApiOperation(notes = "帮助删除", value = "帮助删除")
	@PostMapping(value = "/delRead")
	public Response<Integer> delRead(@Valid @RequestBody HelpDelRequest request) {
		return Response.success(notifyService.delRead(request));
	}

	@ApiOperation(notes = "分类列表", value = "分类列表")
	@GetMapping("/category/list")
	public Response<List<NotifyCategoryInfoVo>> categoryList(@RequestParam(required = false) String person) {
		return Response.success(notifyService.getCategoryList(person));
	}

	@ApiOperation(notes = "分类列表未读数量", value = "分类列表未读数量")
	@GetMapping("/category/list/unread")
	public Response<List<NotifyCategoryInfoVo>> categoryListUnread(@RequestParam String person,@RequestParam int type) {
		return Response.success(notifyService.getCategoryListUnread(person,type));
	}


	@GetMapping("/leaveAudit")
	@ApiOperation(value = "test")
	public void test(){
		leaveAuditTask.leaveAudit(null);
	}

	@ApiOperation(notes = "推送监控", value = "推送监控")
	@GetMapping("/queryPushMonitor")
	public Response<IPage<PushMonitorVO>> queryPushMonitor(PushMoinitorQueryDTO dto) {
		return Response.success(notifyService.queryPushMonitor(dto));
	}

	@ApiOperation(notes = "推送监控查看", value = "推送监控查看")
	@GetMapping("/queryPushMonitorDetail")
	public Response<IPage<PushMonitorDetailVO>> queryPushMonitorDetail(PushMoinitorQueryDetailDTO dto) {
		return Response.success(notifyService.queryPushMonitorDetail(dto));
	}

	@ApiOperation(notes = "核心指标预警周报", value = "核心指标预警周报")
	@GetMapping("/testSendWeekly")
	public void testSendWeekly(@RequestParam(required = false) String param){
		notifyTask.weeklyReportCompletion(param);
	}




}
