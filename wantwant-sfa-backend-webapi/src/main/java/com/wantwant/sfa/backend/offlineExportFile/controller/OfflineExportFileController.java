package com.wantwant.sfa.backend.offlineExportFile.controller;

import com.alibaba.fastjson.JSONObject;
import com.wantwant.sfa.backend.offlineExportFile.api.OfflineExportFileApi;
import com.wantwant.sfa.backend.offlineExportFile.request.AgainDownloadRequest;
import com.wantwant.sfa.backend.offlineExportFile.request.OfflineExportFileRequest;
import com.wantwant.sfa.backend.offlineExportFile.vo.OfflineExportFileResponse;
import com.wantwant.sfa.backend.service.OfflineExportFileService;
import com.wantwant.sfa.backend.service.OfflineExportService;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Api(tags = "离线导出API功能")
@RestController(value = "/FreeSampleApi")
public class OfflineExportFileController implements OfflineExportFileApi {

    @Autowired
    private OfflineExportFileService offlineExportFileService;
    @Autowired
    private OfflineExportService offlineExportService;



    @Override
    public Response<OfflineExportFileResponse> offlineExportFileList(OfflineExportFileRequest request) {
        return Response.success(offlineExportFileService.offlineExportFileList(request));
    }

   @Override
    public Response AgainDownload(AgainDownloadRequest request) {
        if(StringUtils.isBlank(request.getEmployeeId())){
            throw new ApplicationException("操作人ID不能为空");
        }
        if( null == request.getRecordId()){
        throw new ApplicationException("离线导出id不能为空");
    }
    Response<JSONObject> jsonObjectResponse = offlineExportService.REExport(request.getEmployeeId(),"", request.getRecordId());
        return jsonObjectResponse;
    }
}
