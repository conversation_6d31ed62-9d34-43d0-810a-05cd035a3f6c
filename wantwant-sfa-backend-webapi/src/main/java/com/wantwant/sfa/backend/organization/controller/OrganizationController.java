package com.wantwant.sfa.backend.organization.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.OrganizationApplication;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.info.vo.CompanyStoreMessageVo;
import com.wantwant.sfa.backend.organization.api.OrganizationApi;
import com.wantwant.sfa.backend.info.vo.OrganizationVo;
import com.wantwant.sfa.backend.mapper.market.SFAOrganizationMapper;
import com.wantwant.sfa.backend.organization.request.*;
import com.wantwant.sfa.backend.organization.vo.*;
import com.wantwant.sfa.backend.position.service.IPositionRelationService;
import com.wantwant.sfa.backend.service.IOrganizationService;
import com.wantwant.sfa.backend.service.IOrganizationUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * 
* <AUTHOR> Ning
* @description: //模块目的、功能描述
* @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
* @Time 2020-9-8 13:58:29
 */
@RestController
@Slf4j
public class OrganizationController implements OrganizationApi {
	
	@Autowired
	IOrganizationService organizationService;
	@Autowired
	IOrganizationUploadService organizationUploadService;
	@Autowired
	private SFAOrganizationMapper sfaOrganizationMapper;
	@Autowired
	private IPositionRelationService positionRelationService;
	@Resource
	private OrganizationApplication organizationApplication;

	@Override
	public Response savaRegion(SavaRegionlRequest request) {
		log.info("start OrganizationController savaRegion request:{}",request);
		if(StringUtils.isBlank(request.getEmployeeId())) {
			throw new ApplicationException("请输入业务员工号！");
		}else if(StringUtils.isBlank(request.getOrganizationId())) {
			throw new ApplicationException("请选择组织！");
		}else if(CollectionUtils.isEmpty(request.getAllCurrentSelectAdcodeList())) {
			throw new ApplicationException("请选择添加的区域！");
		}

		organizationService.savaRegion(request);
		
		return Response.success();
	}

	@Override
	public Response<StaffingResponse> staffing(StaffingReq request) {
		log.info("start OrganizationController staffing request:{}",request);
		if(StringUtils.isBlank(request.getEmployeeId())) {
			throw new ApplicationException("请输入业务员工号！");
		}
		StaffingResponse res = organizationService.staffing(request);
		return Response.success(res);
	}

	@Override
	public Response<List<OrganizationTreeVO>> organizationAreaFor123(OrganizationInfoReq request) {
		log.info("start OrganizationController organizationAreaFor123 request:{}",request);
		List<OrganizationTreeVO> list = organizationService.getOrganizationInfo(request);
		return Response.success(list);
	}



	@Override
	public Response upload(MultipartFile file) {
		organizationUploadService.upload(file);

		return Response.success();
	}

	@Override
	public Response<List<OrganizationTreeVO>> organizationArea(String channelId) {
		List<OrganizationTreeVO> list = organizationService.getOrganizationByChannelId(channelId);
		return Response.success(list);
	}

	@Override
	public Response<List<OrganizationVo>> getSfaOrganizationInfo(IwantwantOrganizationReq request) {
		request.setBusinessGroup(RequestUtils.getBusinessGroup());
		List<OrganizationVo> list = sfaOrganizationMapper.getSfaOrganizationInfo(request);

		return Response.success(list);
	}

	@Override
	public Response<List<CompanyStoreMessageVo>> getStoreCompanyRelation() {
		log.info("getStoreCompanyRelation employeeId");
		return Response.success(organizationService.getStoreCompanyRelation());
	}

	@Override
	public Response<List<LoginUserOrgVo>> getLoginOrg( String person) {
		return Response.success(positionRelationService.selectPositionByEmpId(person,RequestUtils.getBusinessGroup()));
	}

	@Override
	public Response<List<OrgVo>> selectAllOrg(boolean filterZB,boolean filterBranch,Integer businessGroup) {
		if(Objects.isNull(businessGroup)){
			businessGroup = RequestUtils.getBusinessGroup();
		}
		List<OrgVo> orgVos = organizationApplication.selectAllService(businessGroup, filterZB,filterBranch);
		return Response.success(orgVos);
	}

	@Override
	public Response<String> getZbOrgCode() {
		String orgCode = organizationApplication.getZbOrgCode(RequestUtils.getBusinessGroup());
		return Response.success(orgCode);
	}

	@Override
	public Response<String> selectOrgByArea(OrgAreaSearchRequest orgAreaSearchRequest) {
		String orgCode = organizationApplication.selectOrgByArea(orgAreaSearchRequest);
		return Response.success(orgCode);
	}

	@Override
	public Response<Boolean> fitBusinessGroup(VerifyOrganizationFitBusinessGroupRequest request) {
		return Response.success(organizationService.fitBusinessGroup(request));
	}
}
