package com.wantwant.sfa.backend.oss;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.util.AliOSSServiceUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 阿里云OSS
 *
 * @date 2024-04-23 14:32
 * @version 1.0
 */
@Api(tags = "阿里云接口相关")
@RestController
@RequestMapping("/oss")
@Slf4j
public class AliOSSController {


    @Resource
    private AliOSSServiceUtils aliOSSServiceUtils;


    @GetMapping("/sts/credential")
    @ApiOperation(value = "获取临时凭证", notes = "获取临时凭证")
    public Response<Map<String, String>> getCredential() {
        log.info("AliOSSController getCredential");
        return Response.success(aliOSSServiceUtils.getAccessCredentials());
    }

}
