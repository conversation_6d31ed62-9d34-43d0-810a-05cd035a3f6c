package com.wantwant.sfa.backend.permission.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.permission.api.PermissionApi;
import com.wantwant.sfa.backend.permission.request.PermissionEmpRequest;
import com.wantwant.sfa.backend.permission.request.PermissionGroupRequest;
import com.wantwant.sfa.backend.permission.service.IPermissionService;
import com.wantwant.sfa.backend.permission.vo.EmployeeVo;
import com.wantwant.sfa.backend.permission.vo.PermissionGroupInfoVo;
import com.wantwant.sfa.backend.permission.vo.PermissionGroupVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/29/上午11:28
 */
@RestController
@Slf4j
public class PermissionController implements PermissionApi {
    @Autowired
    private IPermissionService permissionService;

    @Override
    public Response<List<PermissionGroupVo>> getPermissionGroupByEmpId(String person,Integer type) {
        log.info("get permission group by empId:{}",person);
        List<PermissionGroupVo> list = permissionService.getPermissionGroupByEmpId(person,type);
        return Response.success(list);
    }

    @Override
    public Response<PermissionGroupInfoVo> getPermissionGroupInfo(int id) {
        log.info("get permission group info by id:{}",id);
        PermissionGroupInfoVo vo = permissionService.getPermissionGroupInfo(id);
        return Response.success(vo);
    }

    @Override
    public Response savePermissionGroup(@Valid PermissionGroupRequest request) {
        log.info("【modify permission group】request:{}",request);
        permissionService.savePermissionGroup(request);
        return Response.success();
    }

    @Override
    public Response deletePermissionGroup(int id) {
        log.info("【delete permission group】id:{}",id);
        permissionService.deletePermissionGroup(id);
        return Response.success();
    }

    @Override
    public Response<List<EmployeeVo>> selectEmployeeInfo(PermissionEmpRequest request) {
        log.info("【select permission emp 】,request:{}",request);
        List<EmployeeVo> list = permissionService.selectEmployeeInfo(request);
        return Response.success(list);
    }
}
