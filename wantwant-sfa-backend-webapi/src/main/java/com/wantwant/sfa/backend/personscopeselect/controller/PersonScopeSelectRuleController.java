package com.wantwant.sfa.backend.personscopeselect.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.personscopeselect.api.PersonScopeSelectRuleApi;
import com.wantwant.sfa.backend.personscopeselect.dto.*;
import com.wantwant.sfa.backend.personscopeselect.request.*;
import com.wantwant.sfa.backend.personscopeselect.service.IPersonScopeSelectRuleService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.controller
 * @Description:
 * @Date: 2025/2/19 14:32
 */
@RestController
public class PersonScopeSelectRuleController implements PersonScopeSelectRuleApi {
    @Resource
    private IPersonScopeSelectRuleService personScopeSelectRuleService;
    @Override
    public Response<Long> add(AddPersonScopeSelectRuleRequest request) {
        return Response.success(personScopeSelectRuleService.add(request));
    }

    @Override
    public Response<Long> copy(CopyPersonScopeSelectRuleRequest request) {
        return Response.success(personScopeSelectRuleService.copy(request));
    }

    @Override
    public Response update(UpdatePersonScopeSelectRuleRequest request) {
        personScopeSelectRuleService.update(request);
        return Response.success();
    }

    @Override
    public Response<PersonScopeSelectRuleInfoDto> queryDetail(QueryPersonScopeSelectRuleInfoRequest request) {
        return Response.success(personScopeSelectRuleService.queryDetail(request.getId()));
    }

    @Override
    public Response delete(DeletePersonScopeSelectRuleInfoRequest request) {
        personScopeSelectRuleService.delete(request);
        return Response.success();
    }

    @Override
    public Response<List<PersonScopeSelectRuleDetailDto>> queryRuleDetails(QueryPersonScopeSelectRuleDetailRequest request) {
        return Response.success(personScopeSelectRuleService.queryRuleDetails(request));
    }

    @Override
    public Response<PersonScopeSelectRuleCompareDto> compared(ComparePersonScopeSelectRuleDetailRequest request) {
        return Response.success(personScopeSelectRuleService.compared(request));
    }

    @Override
    public Response<PersonScopeSelectRuleCompareBeforeAddDto> comparedBeforeAdd(CompareBeforeAddPersonScopeSelectRuleDetailRequest request) {
        return Response.success(personScopeSelectRuleService.comparedBeforeAdd(request));
    }

    @Override
    public Response<QueryPersonScopeSelectRuleListDto> queryRuleListForPerson(QueryPersonScopeSelectRuleListRequest request) {
        return Response.success(personScopeSelectRuleService.queryRuleListForPerson(request));
    }
}
