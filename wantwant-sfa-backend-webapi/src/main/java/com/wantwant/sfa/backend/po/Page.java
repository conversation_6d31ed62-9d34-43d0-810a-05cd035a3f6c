package com.wantwant.sfa.backend.po;

import java.util.Collections;
import java.util.List;

/**
 * 
 * @文件名称: Page.java
 * @文件作用： 分页参数vo
 * @创建日期: 2018年6月25日
 * <AUTHOR>
 *         <p>
 *         Copyrights © 2016 华夏信财股权投资管理有限公司 All rights reserved.
 *         <p>
 *         注意：本内容仅限于华夏信财股权投资管理有限公司信息技术部内部传阅，禁止外泄以及用于其他的商业目的。
 */
public class Page<T> implements java.io.Serializable {
	private static final long serialVersionUID = 8450665768936866696L;
	private static final long DEFAULT_PAGE_SIZE = 10;
	/** 当前页 */
	private long currentPage = 1;
	/** 每页数查询数量，默认10条 */
	private long pageSize = DEFAULT_PAGE_SIZE;
	/** 记录总数 */
	private long totalSum = 0;
	/** 记录集 */
	private List<T> records = Collections.emptyList();
	/** 总页数 */
	private long totalPageNum;

	public Page() {

	}


	public Page(long totalSum) {
		this.totalSum = totalSum;
		this.pageSize = DEFAULT_PAGE_SIZE;
		this.currentPage = 1;
	}

	public Page(long pageSize, long currentPage) {
		if (pageSize < 1)
			this.pageSize = DEFAULT_PAGE_SIZE;
		else
			this.pageSize = pageSize;
		this.currentPage = currentPage;
	}

	public Page(long totalSum, long pageSize, long currentPage) {
		this.totalSum = totalSum;
		this.pageSize = pageSize;
		if (currentPage < 1)
			this.currentPage = 1;
		this.currentPage = currentPage;
		totalPageNum = getTotalPages();
	}

	public long getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(final Long currentPage) {
		if (currentPage == null || currentPage < 1)
			this.currentPage = 1;
		this.currentPage = currentPage;
	}

	/**
	 * 每页数显示数量，默认10
	 * 
	 * @return
	 */
	public long getPageSize() {
		return pageSize;
	}

	public void setPageSize(long pageSize) {
		this.pageSize = pageSize;
	}

	public long getTotalSum() {
		return totalSum;
	}

	public void setTotalSum(long totalSum) {
		this.totalSum = totalSum;
	}

	public List<T> getRecords() {
		return records;
	}

	public void setRecords(List<T> records) {
		this.records = records;
	}

	public long getStartRows() {
		return (long) (currentPage - 1) * pageSize + 1;
	}

	public long getTotalPages() {
		if (this.totalSum % this.pageSize > 0) {
			return (long) this.totalSum / this.pageSize + 1;
		} else {
			return (long) this.totalSum / this.pageSize;
		}
	}

	/**
	 * 设置PageConfig
	 * 
	 * @param totalSum
	 *            记录总数
	 * @param pageSize
	 *            每页数
	 * @param currentPage
	 *            当前页
	 * @return
	 */
	public static Page page(long totalSum, long pageSize,
			long currentPage) {
		return new Page(totalSum, pageSize, currentPage);
	}

	public static Page page(long pageSize, long currentPage) {
		return new Page(pageSize, currentPage);
	}

	public long getTotalPageNum() {
		return this.getTotalPages();
	}

	public void setTotalPageNum(long totalPageNum) {
		this.totalPageNum = totalPageNum;
	}

	/**
	 * 重置记录数量总数
	 * 
	 * @param
	 *
	 * @param
	 * @param
	 *
	 * @param totalSum
	 *            当前页
	 * @return
	 */
	public void resettotalSum(Page<T> pageConfig, long totalSum) {
		if (totalSum <= 0)
			return;
		pageConfig.setTotalSum(totalSum);
		long totoalNumCount = getTotalPages();
		pageConfig.setTotalPageNum(totoalNumCount);
	}

	/**
	 * 当前页的结束行
	 * 
	 * @return
	 */
	public long getEndRows() {
		long num = 0;
		if (this.currentPage == this.totalPageNum) {
			num = this.getTotalSum();
		} else {
			num = this.getStartRows() + this.getPageSize() - 1;
		}
		return num;
	}

	public void setPage(Long page) {
		this.currentPage = page;
	}

	public Long getPage() {
		return this.currentPage;
	}

	/**
	 * 是否存在下一页
	 * 
	 * @return
	 */
	public boolean hasNext() {
		long totalPage = getTotalPages();
		return (totalPage > currentPage);
	}

}
