package com.wantwant.sfa.backend.policy.controller;

import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.policy.api.PolicyApi;
import com.wantwant.sfa.backend.policy.controller.assemble.PolicyAssemble;
import com.wantwant.sfa.backend.policy.request.*;
import com.wantwant.sfa.backend.policy.service.IPolicySearchService;
import com.wantwant.sfa.backend.policy.service.IPolicyService;
import com.wantwant.sfa.backend.policy.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/21/上午10:35
 */
@RestController
@Slf4j
public class PolicyController implements PolicyApi {
    @Autowired
    private IPolicySearchService policySearchService;
    @Autowired
    private IPolicyService policyService;
    @Autowired
    private PolicyAssemble policyAssemble;

    @Override
    public Response<List<PolicyCategoryVo>> getCategory() {
        List<PolicyCategoryVo> list = policySearchService.getCategory();
        return Response.success(list);
    }

    @Override
    public Response savePolicy(PolicyRequest policyRequest) {
        log.info("【save policy】request:{}", JSONObject.toJSONString(policyRequest));
        policyService.savePolicy(policyAssemble.convertToVo(policyRequest));
        return Response.success();
    }

    @Override
    public Response modifyStatus(PolicyStatusModifyRequest policyStatusModifyRequest) {
        log.info("【modify policy status】request:{}", JSONObject.toJSONString(policyStatusModifyRequest));
        policyService.modifyStatus(policyAssemble.convertToVo(policyStatusModifyRequest));
        return  Response.success();
    }

    @Override
    public Response delay(PolicyDelayRequest policyDelayRequest) {
        log.info("【policy delay】request:{}",JSONObject.toJSONString(policyDelayRequest));
        policyService.delay(policyAssemble.convertToVo(policyDelayRequest));

        return  Response.success();
    }

    @Override
    public Response<List<PolicyDateVo>> getPolicyDate(Boolean filterNextMonth) {
        List<PolicyDateVo> list = policySearchService.getPolicyDate(filterNextMonth);
        return Response.success(list);
    }

    @Override
    public Response<List<PolicyVo>> selectPolicy(PolicySearchRequest policySearchRequest) {
        log.info("【policy select】request:{}",JSONObject.toJSONString(policySearchRequest));

        List<PolicyVo> list = policySearchService.selectPolicy(policySearchRequest);
        return Response.success(list);
    }

    @Override
    public Response<PolicyDetailVo> getDetail(Long policyId) {
        log.info("【policy detail 】id:{}",policyId);
        PolicyDetailVo policyDetailVo = policySearchService.getDetail(policyId);
        return Response.success(policyDetailVo);
    }

    @Override
    public Response policySort(@Valid List<PolicySortRequest> policySortRequest) {
        log.info("【policy sort】request:{}",policySortRequest);

        policyService.policySort(policySortRequest);

        return Response.success();
    }

    @Override
    public Response<PolicyViewAnalyseVo> selectPolicyView(PolicyViewSearchRequest policyViewSearchRequest) {
        log.info("【policy view 】request:{}",JSONObject.toJSONString(policyViewSearchRequest));
        PolicyViewAnalyseVo vo = policySearchService.selectPolicyView(policyViewSearchRequest);
        return Response.success(vo);
    }

    @Override
    public Response<List<PolicyCategoryItemVo>> selectPolicyItem(PolicyItemSearchRequest policyItemSearchRequest) {
        log.info("【policy item 】request:{}",JSONObject.toJSONString(policyItemSearchRequest));

        List<PolicyCategoryItemVo> list = policySearchService.selectPolicyItem(policyItemSearchRequest);
        return Response.success(list);
    }

    @Override
    public Response<List<PolicyCategoryItemVo>> selectMorePolicyItem(PolicyItemMoreSearchRequest policyItemMoreSearchRequest) {
        log.info("【policy item 】request:{}",JSONObject.toJSONString(policyItemMoreSearchRequest));

        List<PolicyCategoryItemVo> list = policySearchService.selectMorePolicyItem(policyItemMoreSearchRequest);
        return Response.success(list);
    }

    @Override
    public Response<PolicyCheckVo> getPolicyView(Long policyId) {
        log.info("【policy view】policyId:{}",policyId);

        PolicyCheckVo policyCheckVo = policySearchService.getPolicyView(policyId);

        return Response.success(policyCheckVo);
    }

    @Override
    public Response checkPolicy(PolicyCheckRequest policyCheckRequest) {
        log.info("【check policy】request:{}",policyCheckRequest);

        policyService.feedback(policyCheckRequest.getPolicyId(),null,policyCheckRequest.getPerson());

        return Response.success(policyCheckRequest);
    }

    @Override
    public Response<List<PolicyPromptVo>> policyPrompt(PolicyItemSearchRequest policyItemSearchRequest) {
        log.info("【policy prompt 】policyItemSearchRequest:{}",JSONObject.toJSONString(policyItemSearchRequest));

        List<PolicyPromptVo> list = policySearchService.policyPrompt(policyItemSearchRequest);
        return Response.success(list);
    }

    @Override
    public Response<Long> unreadCount(String person) {
        log.info("【policy unread count】empId:{}",person);

        long count = policySearchService.unreadCountForCurrentMonth(person);
        return Response.success(count);
    }

    @Override
    public Response<List<PolicyRelateMenuVo>> getPolicyMenu(Long policyId) {

        log.info("【get policy menu】policyId:{}",policyId);

        List<PolicyRelateMenuVo> list = policySearchService.getPolicyMenu(policyId);

        return Response.success(list);
    }

    @Override
    public Response setRelateMenu(PolicyRelateMenuRequest policyRelateMenuRequest) {
        log.info("【set relate menu】request:{}",policyRelateMenuRequest);

        policyService.setRelateMenu(policyRelateMenuRequest);

        return Response.success();
    }

    @Override
    public Response<PolicySalaryVo> selectSalaryPolicy(PolicySalarySearchRequest policySearchRequest) {
        log.info("【selectSalaryPolicy】request:{}",JSONObject.toJSONString(policySearchRequest));

        PolicySalaryVo policySalaryVo = policySearchService.selectSalaryPolicy(policySearchRequest);

        return Response.success(policySalaryVo);
    }

}
