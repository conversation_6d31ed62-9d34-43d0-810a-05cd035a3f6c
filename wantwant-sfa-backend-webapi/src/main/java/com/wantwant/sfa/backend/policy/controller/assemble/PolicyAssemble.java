package com.wantwant.sfa.backend.policy.controller.assemble;

import com.wantwant.sfa.backend.policy.dto.PolicyDTO;
import com.wantwant.sfa.backend.policy.dto.PolicyDelayDTO;
import com.wantwant.sfa.backend.policy.dto.PolicyModifyStatusDTO;
import com.wantwant.sfa.backend.policy.dto.PolicyRegularDTO;
import com.wantwant.sfa.backend.policy.request.PolicyDelayRequest;
import com.wantwant.sfa.backend.policy.request.PolicyRequest;
import com.wantwant.sfa.backend.policy.request.PolicyStatusModifyRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/21/上午10:50
 */

@Mapper(componentModel = "spring")
public interface PolicyAssemble {

    PolicyAssemble INSTANCE = Mappers.getMapper(PolicyAssemble.class);


    @Mapping(target = "policyRegular", resultType = PolicyRegularDTO.class)
    PolicyDTO convertToVo(PolicyRequest request);



    PolicyModifyStatusDTO convertToVo(PolicyStatusModifyRequest policyStatusModifyRequest);


    PolicyDelayDTO convertToVo(PolicyDelayRequest policyDelayRequest);
}
