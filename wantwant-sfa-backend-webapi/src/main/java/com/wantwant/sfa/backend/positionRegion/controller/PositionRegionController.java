package com.wantwant.sfa.backend.positionRegion.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.CustomerMapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketV2Mapper;
import com.wantwant.sfa.backend.market.service.ISmallMarketEmployeeService;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.Customer;
import com.wantwant.sfa.backend.positionRegion.api.PositionRegionApi;
import com.wantwant.sfa.backend.positionRegion.request.ReginInfoRequest;
import com.wantwant.sfa.backend.positionRegion.request.VillageRequest;
import com.wantwant.sfa.backend.positionRegion.service.IPositionRegionService;
import com.wantwant.sfa.backend.positionRegion.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/02/19/下午4:50
 */
@RestController
@Slf4j
public class PositionRegionController implements PositionRegionApi {

    @Autowired
    private IPositionRegionService positionRegionService;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private ISmallMarketEmployeeService smallMarketEmployeeService;

    @Override
    public Response<List<RegionVo>> regions(Long memberKey) {
        log.info("【根据memberKey获取区域信息】memberKey:{}",memberKey);
        List<RegionVo> regionModels = positionRegionService.selectRegionByMemberKey(memberKey);

        return Response.success(regionModels);
    }

    @Override
    public Response<List<DistrictVo>> districts(Long memberKey) {
        log.info("【根据memberKey获取区级信息】memberKey:{}",memberKey);
        List<DistrictVo> list = positionRegionService.selectDistrictsByMember(memberKey);

        return Response.success(list);
    }

    @Override
    public Response<List<VillageVo>> villageVos(VillageRequest villageRequest) {
        List<VillageVo> list = positionRegionService.selectVillage(villageRequest);
        return Response.success(list);
    }

    @Override
    public Response<List<RegionInfoVo>> regionInfo(ReginInfoRequest request) {
        List<RegionInfoVo> list = positionRegionService.regionInfo(request);
        return Response.success(list);
    }

    @Override
    public Response<List<String>> regionScope(Long memberKey) {
        log.info("【获取经销地区】memberKey:{}",memberKey);
        Customer customerByMemberKey = customerMapper.getCustomerByMemberKey(memberKey);
        if(Objects.isNull(customerByMemberKey)){
            throw new ApplicationException("客户信息不存在");
        }

        String positionId = customerByMemberKey.getPositionId();

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(
                new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                        .eq("position_id", positionId)
                        .eq("channel", RequestUtils.getChannel())
        );
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("组织信息获取失败");
        }

        List<String> result = positionRegionService.regionScope(memberKey,ceoBusinessOrganizationPositionRelation.getPositionTypeId());
        return Response.success(result);
    }

    /**
     * 20240921和张浩然确认旺铺已不调用
     * @param memberKey
     */
    @Override
    public Response<List<SmallMarketVo>> smallMarket(Long memberKey) {
        log.info("【根据memberKey获取小标信息】memberKey:{}",memberKey);
        throw new ApplicationException("当前接口已下线");
    }

    /**
     * 20240921和张浩然确认旺铺已不调用
     * @param smallMarketIds
     */
    @Override
    public Response<List<SmallMarketVo>> getSmallMarketById(List<String> smallMarketIds) {
        log.info("【根据小标市场ID获取小标信息】smallMarketId:{}",smallMarketIds);
        throw new ApplicationException("当前接口已下线");
    }
}
