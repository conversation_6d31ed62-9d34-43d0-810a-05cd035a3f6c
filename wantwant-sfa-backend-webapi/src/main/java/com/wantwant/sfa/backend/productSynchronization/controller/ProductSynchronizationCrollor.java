package com.wantwant.sfa.backend.productSynchronization.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.productSynchronization.api.ProductSynchronizationApi;
import com.wantwant.sfa.backend.productSynchronization.request.YearPerformanceTrendsRequest;
import com.wantwant.sfa.backend.productSynchronization.request.YearProductSyncCustomerRequest;
import com.wantwant.sfa.backend.productSynchronization.request.YearProductSynchronizationRequest;
import com.wantwant.sfa.backend.productSynchronization.service.IProductSynchronizationService;
import com.wantwant.sfa.backend.productSynchronization.vo.*;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@RestController
public class ProductSynchronizationCrollor implements ProductSynchronizationApi {

    @Autowired
    private IProductSynchronizationService productSynchronizationService;

    @Override
    public Response<Page<YearProductSynchronizationVo>> getYearProductAchievedList(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearProductAchievedList(request));
    }

    @Override
    public Response<List<YearProductSynchronizationVo>> getYearProductAchievedOrganizationList(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearProductAchievedOrganizationList(request));
    }

    @Override
    public Response<Page<YearTransactionsNumVo>> getYearProductTransactionsNumList(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearProductTransactionsNumList(request));
    }

    @Override
    public Response<List<YearTransactionsNumVo>> getYearProductOrganizationList(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearProductOrganizationist(request));
    }

    @Override
    public Response<Page<YearFilingClientVo>> getYearFilingClientList(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearFilingClientList(request));
    }

    @Override
    public Response<YearPerformanceAchievedVo> getYearPerformanceAchievedTrends(YearPerformanceTrendsRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearPerformanceAchievedTrends(request));
    }

    @Override
    public Response<TransactionsNumVo> getYearProductAchievedSpu(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearProductAchievedSpu(request));
    }

    @Override
    public Response<TransactionsNumVo> getYearTransactionsNumSpuSku(YearProductSynchronizationRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearTransactionsNumSpuSku(request));
    }

    @Override
    public Response<Page<YearProductSyncCustomerVo>> getYearProductCustomerList(YearProductSyncCustomerRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        return Response.success(productSynchronizationService.getYearProductCustomerList(request));
    }

    @Override
    public void getYearProductAchievedOrganizationListExport(YearProductSynchronizationRequest request, HttpServletResponse response) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        productSynchronizationService.getYearProductAchievedOrganizationListExport(request,response);
    }

    @Override
    public void getYearProductOrganizationNumListExport(YearProductSynchronizationRequest request, HttpServletResponse response) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        productSynchronizationService.getYearProductOrganizationNumListExport(request,response);
    }

    @Override
    public void getYearProductAchievedListExport(YearProductSynchronizationRequest request, HttpServletResponse response) {
        // HttpServletResponse response = servletRequestAttributes.getResponse();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        Page<YearProductSynchronizationVo> result = productSynchronizationService.getYearProductAchievedList(request);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSynchronizationVo.class, result.getList());
        EasyPoiUtil.downLoadExcel("合伙人达成分析导出.xls", response, workbook);
    }

    @Override
    public void getYearProductTransactionsNumListExport(YearProductSynchronizationRequest request, HttpServletResponse response){
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        Page<YearTransactionsNumVo> result = productSynchronizationService.getYearProductTransactionsNumList(request);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearTransactionsNumVo.class, result.getList());
        EasyPoiUtil.downLoadExcel("合伙人达成分析导出.xls", response, workbook);
    }

    @Override
    public void getYearFilingClientListExport(YearProductSynchronizationRequest request, HttpServletResponse response) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPage(null);
        request.setRows(null);
        Page<YearFilingClientVo> result = productSynchronizationService.getYearFilingClientList(request);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearFilingClientVo.class, result.getList());
        EasyPoiUtil.downLoadExcel("合伙人达成分析导出.xls", response, workbook);
    }

    @Override
    public void getYearProductCustomerListExport(YearProductSyncCustomerRequest request, HttpServletResponse response) {
        request.setPage(null);
        request.setRows(null);
        Integer positionTypeId = RequestUtils.getLoginInfo().getPositionTypeId();
        Integer businessGroup = RequestUtils.getBusinessGroup();

        List<YearProductSyncCustomerVo> list = productSynchronizationService.getYearProductCustomerAllList(request);


        Workbook workbook = null;
        if (positionTypeId == 7){
            workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSyncCustomerVo.class, list);
        }else {
            if (businessGroup == 1){

                ArrayList<YearProductSyncCustomerAzuVo> subList = new ArrayList<>();
                list.forEach(vo -> {
                    YearProductSyncCustomerAzuVo customerAzuVo = new YearProductSyncCustomerAzuVo();
                    BeanUtils.copyProperties(vo, customerAzuVo);
                    subList.add(customerAzuVo);
                });
                workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSyncCustomerAzuVo.class, subList);
            }else if (businessGroup == 2){

                ArrayList<YearProductSyncCustomerBzuVo> subList = new ArrayList<>();
                list.forEach(vo -> {
                    YearProductSyncCustomerBzuVo customerAzuVo = new YearProductSyncCustomerBzuVo();
                    BeanUtils.copyProperties(vo, customerAzuVo);
                    subList.add(customerAzuVo);
                });

                workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSyncCustomerBzuVo.class, subList);
            }else if (businessGroup == 3){

                ArrayList<YearProductSyncCustomerCzuVo> subList = new ArrayList<>();
                list.forEach(vo -> {
                    YearProductSyncCustomerCzuVo customerAzuVo = new YearProductSyncCustomerCzuVo();
                    BeanUtils.copyProperties(vo, customerAzuVo);
                    subList.add(customerAzuVo);
                });
                workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSyncCustomerCzuVo.class, subList);
            }else if (businessGroup == 4){
                ArrayList<YearProductSyncCustomerDzuVo> subList = new ArrayList<>();
                list.forEach(vo -> {
                    YearProductSyncCustomerDzuVo customerAzuVo = new YearProductSyncCustomerDzuVo();
                    BeanUtils.copyProperties(vo, customerAzuVo);
                    subList.add(customerAzuVo);
                });

                workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSyncCustomerDzuVo.class, subList);
            }else if (businessGroup == 5){

                ArrayList<YearProductSyncCustomerEzuVo> subList = new ArrayList<>();
                list.forEach(vo -> {
                    YearProductSyncCustomerEzuVo customerAzuVo = new YearProductSyncCustomerEzuVo();
                    BeanUtils.copyProperties(vo, customerAzuVo);
                    subList.add(customerAzuVo);
                });
                workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), YearProductSyncCustomerEzuVo.class, subList);
            }
        }

        EasyPoiUtil.downLoadExcel("年节产品全部客户列表导出.xls", response, workbook);
    }
}
