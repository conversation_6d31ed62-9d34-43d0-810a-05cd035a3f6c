package com.wantwant.sfa.backend.recruit.assemble;


import com.wantwant.sfa.backend.domain.recruit.DO.RecruitConfigDO;
import com.wantwant.sfa.backend.recruit.request.RecruitConfigRequest;
import org.mapstruct.Mapper;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午6:59
 */
@Mapper(componentModel = "spring")
public interface RecruitConfigAssemble {



    RecruitConfigDO convertRecruitConfigDO(RecruitConfigRequest request);

}
