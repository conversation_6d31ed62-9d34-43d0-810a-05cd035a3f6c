package com.wantwant.sfa.backend.recruit.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.RecruitConfigApplication;
import com.wantwant.sfa.backend.recruit.api.RecruitConfigApi;
import com.wantwant.sfa.backend.recruit.assemble.RecruitConfigAssemble;
import com.wantwant.sfa.backend.recruit.request.RecruitCommand;
import com.wantwant.sfa.backend.recruit.request.RecruitConfigRequest;
import com.wantwant.sfa.backend.recruit.request.RecruitSearchRequest;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigDetailVO;
import com.wantwant.sfa.backend.recruit.vo.RecruitConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/06/下午3:13
 */
@RestController
@Slf4j
public class RecruitConfigController implements RecruitConfigApi {

    @Resource
    private RecruitConfigApplication recruitConfigApplication;

    @Resource
    private RecruitConfigAssemble recruitConfigAssemble;

    @Override
    public Response save(@Valid RecruitConfigRequest recruitConfigRequest) {
        log.info("【recruit config save】request:{}", JSONObject.toJSONString(recruitConfigRequest));
        Long id = recruitConfigApplication.save(recruitConfigAssemble.convertRecruitConfigDO(recruitConfigRequest), recruitConfigRequest.getPerson());
        return Response.success(id);
    }

    @Override
    public Response<IPage<RecruitConfigVO>> selectList(RecruitSearchRequest recruitSearchRequest) {
        log.info("【recruit config search】request:{}",JSONObject.toJSONString(recruitSearchRequest));
        IPage<RecruitConfigVO> page = recruitConfigApplication.selectList(recruitSearchRequest);
        return Response.success(page);
    }

    @Override
    public void export(RecruitSearchRequest recruitSearchRequest) {
        log.info("【recruit config export】request:{}",JSONObject.toJSONString(recruitSearchRequest));
        recruitConfigApplication.export(recruitSearchRequest);
    }

    @Override
    public Response<RecruitConfigDetailVO> getRecruitConfig(Long id) {
        RecruitConfigDetailVO recruitConfigDetailVO = recruitConfigApplication.getRecruitConfig(id);
        return Response.success(recruitConfigDetailVO);
    }

    @Override
    public Response delete(@Valid RecruitCommand recruitCommand) {

        recruitConfigApplication.delete(recruitCommand);
        return Response.success();
    }


}
