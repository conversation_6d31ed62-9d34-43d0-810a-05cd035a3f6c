package com.wantwant.sfa.backend.reviewReport.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.review.api.ReviewReportApi;
import com.wantwant.sfa.backend.review.dto.*;
import com.wantwant.sfa.backend.review.request.*;
import com.wantwant.sfa.backend.review.service.IPerformancePromiseService;
import com.wantwant.sfa.backend.review.service.IProductAnalysisService;
import com.wantwant.sfa.backend.review.service.IReviewReportSearchService;
import com.wantwant.sfa.backend.review.service.IReviewReportService;
import com.wantwant.sfa.backend.review.vo.*;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.warehouse.vo.ImportMsgVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/23/下午1:19
 */
@RestController
@Slf4j
public class ReviewReportController implements ReviewReportApi {

    @Autowired
    private IReviewReportSearchService reviewReportSearchService;
    @Autowired
    private IReviewReportService reviewReportService;
    @Autowired
    private IProductAnalysisService productAnalysisService;
    @Autowired
    private IPerformancePromiseService performancePromiseService;

    @Override
    public Response<ReviewReportPointVo> getReviewReportPoint(String person,String theYearMon) {

        log.info("【get review report point】person:{} theYearMon:{}"  ,person,theYearMon);

        ReviewReportPointVo vo = reviewReportSearchService.getReviewReportPoint(person,theYearMon, RequestUtils.getBusinessGroup());

        return Response.success(vo);
    }

    @Override
    public Response<List<ReviewReportVo>> selectReviewReport(ReviewReportSearchRequest reviewReportSearchRequest) {

        log.info("【review report select】request:{}",reviewReportSearchRequest);

        List<ReviewReportVo> list = reviewReportSearchService.selectReviewReport(reviewReportSearchRequest);

        return Response.success(list);
    }

    @Override
    public Response editReviewReport(ReviewReportEditRequest reviewReportEditRequest) {

        log.info("【review report edit】request:{}",reviewReportEditRequest);
        ReportEditDto reportEditDto = new ReportEditDto();
        BeanUtils.copyProperties(reviewReportEditRequest,reportEditDto);
        reviewReportService.editReport(reportEditDto);

        return Response.success();
    }

    @Override
    public Response read(@Valid ReviewReportReadRequest request) {
        log.info("【review report read】request:{}",request);

        reviewReportService.read(request);

        return Response.success();
    }

    @Override
    public Response calculateReadDuration(@Valid CalculateReadDurationRequest request) {
        log.info("【calculate read duration】request:{}", JSONObject.toJSONString(request));

        reviewReportService.calculateReadDuration(request);

        return Response.success();
    }

    @Override
    public Response finish(ReviewReportSendRequest request) {
        log.info("【review report finish】request:{}",request);

        ReportSendDto reportSendDto = new ReportSendDto();
        BeanUtils.copyProperties(request,reportSendDto);
        reviewReportService.finish(reportSendDto);

        return Response.success();
    }

    @Override
    public Response sendReport(ReviewReportSendRequest request) {

        log.info("【send report】request:{}",request);

        ReportSendDto reportSendDto = new ReportSendDto();
        BeanUtils.copyProperties(request,reportSendDto);
        reviewReportService.sendReport(reportSendDto);

        return Response.success();
    }

    @Override
    public Response receive(ReviewReportReceiveRequest request) {
        log.info("【review report receive】request:{}",request);

        reviewReportService.receive(request);

        return Response.success();
    }

    @Override
    public Response evaluate(ReviewReportEvaluateRequest request) {

        log.info("【review report evaluate】request:{}",request);
        ReportEvaluateDto reportEvaluateDto = new ReportEvaluateDto();
        BeanUtils.copyProperties(request,reportEvaluateDto);
        reviewReportService.evaluate(reportEvaluateDto);
        return Response.success();
    }

    @Override
    public Response issueFeedback(ReviewIssueRequest request) {

        log.info("【review report issue feedback】request:{}",request);
        ReviewIssueDto reviewIssueDto = new ReviewIssueDto();
        BeanUtils.copyProperties(request,reviewIssueDto);

        List<ReviewIssueDetailRequest> reviewIssueDetails = request.getReviewIssueDetails();
        List<ReviewIssueDetailDto> detailDtoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(reviewIssueDetails)){
            reviewIssueDetails.forEach(e -> {
                ReviewIssueDetailDto reviewIssueDetailDto = new ReviewIssueDetailDto();
                BeanUtils.copyProperties(e,reviewIssueDetailDto);
                detailDtoList.add(reviewIssueDetailDto);
            });
            reviewIssueDto.setDetailDtoList(detailDtoList);
        }

        reviewReportService.issueFeedback(reviewIssueDto);

        return Response.success();
    }

    @Override
    public Response<List<ReviewIssueVo>> getIssueFeedback(Long reportId) {
        log.info("【get Issue feedback】reportId:{}",reportId);

        List<ReviewIssueVo> list = reviewReportService.getIssueFeedback(reportId);

        return Response.success(list);
    }

    @Override
    public Response<ReviewBaseInfoNewVo> getBaseInfo(Long reportId) {
        log.info("【get review base info】reportId:{}",reportId);
        ReviewBaseInfoNewVo baseInfoNew = reviewReportSearchService.getBaseInfoNew(reportId);
        return Response.success(baseInfoNew);
    }

    @Override
    public Response<List<ReviewBaseInfoReachVo>> getBaseReachInfo(Long reportId) {
        return Response.success(reviewReportSearchService.getBaseReachInfo(reportId));
    }

    // ReviewBaseInfoVo baseInfoVo = reviewReportSearchService.getBaseInfo(reportId);
    @Override
    public Response<List<SalesAnalysisVO>> getSalesAnalysis(Long reportId) {
        log.info("【get review Sales Analysis】reportId:{}",reportId);
        List<SalesAnalysisVO> salesAnalysisVOS = reviewReportSearchService.getSalesAnalysis(reportId);
        return Response.success(salesAnalysisVOS);
    }

    @Override
    public Response<ReviewReportDetailVo> getReviewDetail(Long reportId,String person) {
        ReviewReportDetailVo vo = reviewReportSearchService.getReviewDetail(reportId,person);
        return Response.success(vo);
    }

    @Override
    public Response saveOrUpdateReviewReportNote(ReviewReportNoteRequest request) {
        log.info("【saveOrUpdate report note】request:{}",request);

        reviewReportSearchService.saveOrUpdateReviewNote(request);

        return Response.success();
    }

    @Override
    public Response<List<ReviewProductionSaleVo>> getReviewProductionSale(Long reportId) {
        log.info("【review production sale】reportId:{}",reportId);
        List<ReviewProductionSaleVo> list = reviewReportSearchService.getReviewProductionSale(reportId);
        return Response.success(list);
    }

    @Override
    public Response<ReviewReportIDVo> selectReportId(ReviewReportIdSearchVo request) {
        log.info("【review report id search】request:{}",request);

        ReviewReportIDVo vo = reviewReportSearchService.selectReportId(request);

        return Response.success(vo);
    }

    @Override
    public Response<ReviewBasicDataVo> selectReviewBasicData(ReviewReportIdSearchVo request) {
        return Response.success(reviewReportSearchService.selectReviewBasicData(request));
    }

    @Override
    public Response<List<ReviewReplayReason>> getReviewReasonByReplayReasonId(Integer replayReasonId) {
        return Response.success(reviewReportSearchService.getReviewReasonByReplayReasonId(replayReasonId));
    }

    @Override
    public Response<List<ReviewReplayReason>> getReviewReasonByReporId(Integer reporId) {
        return Response.success(reviewReportSearchService.getReviewReasonByReporId(reporId));
    }

    @Override
    public Response<List<ReviewReplayReason>> getReviewReason(ReviewReasonRequest request) {
        return Response.success(reviewReportSearchService.getReviewReason(request));
    }

    @Override
    public Response<List<ReviewReplayReason>> selectReviewReasonUpdate(List<ReviewReplayReason> request) {
        List<ReviewReplayReason> reviewReplayReasons = reviewReportSearchService.selectReviewReasonUpdate(request);
        return Response.success(reviewReplayReasons);
    }

    @Override
    public Response<ProductSearchVo> queryGoodsProductDateSnapshot(ProductSearchRequest productSearchRequest) {
        return Response.success(productAnalysisService.queryGoodsProductDateSnapshot(productSearchRequest));
    }

    @Override
    public Response<PromiseVo> selectPerformancePromise(Long reportId,String employeeId) {
        log.info("【get performance promise】reportId:{},employeeId:{}",reportId,employeeId);

        PromiseVo promiseVo = performancePromiseService.selectPerformancePromise(reportId,employeeId);
        return Response.success(promiseVo);
    }

    @ApiOperation(value = "获取承诺合伙人业绩合计", notes = "获取承诺合伙人业绩合计")
    @Override
    public Response<PromisePartnerSumVo> promiseSum(ReviewReportPromisePartnerSumRequest request) {
        log.info("promiseSum request:{}", request);
        return Response.success(reviewReportService.promiseSum(request));
    }

    @ApiOperation(value = "获取承诺合伙人业绩列表", notes = "获取承诺合伙人业绩列表")
    @Override
    public Response<IPage<PromisePartnerListVo>> promiseList(ReviewReportPromisePartnerListRequest request) {
        log.info("promiseList request:{}", request);
        return Response.success(reviewReportService.promiseList(request));
    }

    @ApiOperation(value = "删除承诺合伙人业绩", notes = "删除承诺合伙人业绩")
    @Override
    public Response promiseDelete(ReviewReportPromisePartnerDeleteRequest request) {
        log.info("promiseDelete request:{}", request);
        reviewReportService.promiseDelete(request);
        return Response.success();
    }

    @ApiOperation(value = "导入承诺合伙人业绩", notes = "导入承诺合伙人业绩")
    @Override
    public Response<ImportMsgVO> promiseUpload(MultipartFile file, String theYearMonth, String person) {
        log.info("promiseUpload request:{}{}", theYearMonth,person);
        return reviewReportService.promiseUpload(file, theYearMonth, person);
    }

    @Override
    public Response reportConfirm(@Valid ReviewReportConfirmRequest request) {
        log.info("【report confirm】request:{}",request);
        reviewReportService.confirm(request);
        return Response.success();
    }

    @Override
    public Response promise(@Valid ModifyPromiseRequest modifyPromiseRequest) {
        log.info("【review report promise modify】request:{}",modifyPromiseRequest);

        performancePromiseService.modifyPromise(modifyPromiseRequest);

        return Response.success();
    }

    @Override
    public Response<List<LastMonthPromiseVo>> getLastMonthPromise(Long reportId) {
        log.info("【last month promise】reportId:{}",reportId);

        List<LastMonthPromiseVo> list = performancePromiseService.getLastMonthPromise(reportId);
        return Response.success(list);
    }

    @Override
    public Response<List<PromiseMetricsVo>> getPromiseMetrics(String metricsName,boolean processMetrics) {
        log.info("【get promise metrics】metricsName:{}",metricsName);

        List<PromiseMetricsVo> list = performancePromiseService.getPromiseMetrics(metricsName,processMetrics);
        return Response.success(list);
    }

    @Override
    public Response<List<ReviewIndexMultipleVo>> getReviewIndexMultiple(String reportId,String month,String organizationId, String year,String person,Integer fiscalYearSpecialModel) throws IllegalAccessException {
        log.info("【get ReviewReportController getReviewIndexMultiple】reportId:{} month:{} organizationId:{} year:{} person:{} fiscalYearSpecialModel:{}"  ,reportId,month,organizationId,year,person,fiscalYearSpecialModel);
        List<ReviewIndexMultipleVo> reviewIndexMultiple = performancePromiseService.getReviewIndexMultiple(reportId, month, organizationId, year, 2, person, fiscalYearSpecialModel);
        return Response.success(reviewIndexMultiple);
    }

    @Override
    public Response<Integer> updateReviewIndexMultiple(@Valid ReviewMultipleRequest reviewMultipleRequest) {
        log.info("【get ReviewReportController updateReviewIndexMultiple】reviewMultipleRequest:{} " ,reviewMultipleRequest);
        return Response.success(performancePromiseService.updateReviewIndexMultiple(reviewMultipleRequest));
    }

}
