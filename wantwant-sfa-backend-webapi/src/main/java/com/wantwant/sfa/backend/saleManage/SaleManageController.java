package com.wantwant.sfa.backend.saleManage;

import com.alibaba.fastjson.JSONObject;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.saleManage.model.SaleBehaviorModel;
import com.wantwant.sfa.backend.saleManage.model.SaleBehaviorUpdateModel;
import com.wantwant.sfa.backend.saleManage.service.ISaleManageService;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.common.base.dto.RpcResult;
import com.wantwant.sfa.sales.management.api.dto.common.TrackingRequestDto;
import com.wantwant.sfa.sales.management.api.service.CommonServiceApi;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2025/02/08/下午4:44
 */
@RestController
@Slf4j
@RequestMapping("saleManage")
public class SaleManageController {

    @Resource
    private CommonServiceApi commonServiceApi;
    @Resource
    private ICheckCustomerService checkCustomerService;

    @Resource
    private ISaleManageService saleManageService;

    @ApiOperation(value = "埋点", httpMethod = "POST")
    @PostMapping("/common/tracking")
    public Response<Long> tracking(@RequestBody @Validated TrackingRequestDto request) {
        log.info("tracking request:{}", request);
        RpcResult<Long> rpcResult = commonServiceApi.tracking(request);
        if (rpcResult.getCode() != 0L) {
            throw new ApplicationException(rpcResult.getMessage());
        }
        return Response.success(rpcResult.getData());
    }

    @PostMapping("/searchBehavior")
    @ApiOperation(notes = "查看销售行为", value = "查看销售行为")
    public Response<SaleBehaviorModel> getSaleBehaviorModel(@RequestBody @Valid SearchBehaviorRequest searchBehaviorRequest) {
        log.info("【get sale behavior】request:{}", JSONObject.toJSONString(searchBehaviorRequest));
        SaleBehaviorModel saleBehaviorModel = saleManageService.getSaleBehaviorModel(searchBehaviorRequest);
        return Response.success(saleBehaviorModel);
    }

    @PostMapping("/updateBehavior")
    @ApiOperation(notes = "更新销售行为", value = "更新销售行为")
    public Response updateBehavior(@RequestBody @Valid UpdateBehaviorRequest updateBehaviorRequest){
        log.info("【update sale behavior】request:{}", JSONObject.toJSONString(updateBehaviorRequest));

        // 根据工号获取positionTypeId的值
        Integer positionTypeId = checkCustomerService.getPositionTypeByEmpId(updateBehaviorRequest.getPerson());

        SaleBehaviorUpdateModel saleBehaviorUpdateModel = SaleBehaviorUpdateModel.builder()
                .workFlowId(updateBehaviorRequest.getWorkFlowId())
                .actionId(updateBehaviorRequest.getActionId())
                .empId(updateBehaviorRequest.getPerson())
                .positionTypeId(positionTypeId)
                .businessGroup(RequestUtils.getBusinessGroup())
                .build();

        saleManageService.updateSaleBehavior(saleBehaviorUpdateModel);

        return Response.success();
    }
}
