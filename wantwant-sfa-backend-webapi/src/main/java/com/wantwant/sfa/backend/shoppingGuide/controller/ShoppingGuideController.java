package com.wantwant.sfa.backend.shoppingGuide.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.shoppingGuide.api.ShoppingGuideApi;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideCommitRequest;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideDetailRequest;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideListRequest;
import com.wantwant.sfa.backend.shoppingGuide.request.ShoppingGuideUpdateRequest;
import com.wantwant.sfa.backend.shoppingGuide.service.ShoppingGuideService;
import com.wantwant.sfa.backend.shoppingGuide.vo.ShoppingGuideDetailVo;
import com.wantwant.sfa.backend.shoppingGuide.vo.ShoppingGuideListVo;
import com.wantwant.sfa.backend.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;


@Api(tags = "导购人员API")
@RestController
@Slf4j
public class ShoppingGuideController implements ShoppingGuideApi {

    private static final String SHOPPING_GUIDE_COMMIT_LOCK = "shopping:guide:commit";

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ShoppingGuideService shoppingGuideService;

    @ApiOperation(value = "导购人员提交", notes = "导购人员提交")
    @Override
    public Response shoppingGuideCommit(ShoppingGuideCommitRequest request) {
        log.info("shoppingGuideCommit request:{}", request);
        if (!redisUtil.setLockIfAbsent(SHOPPING_GUIDE_COMMIT_LOCK, String.valueOf(request.getMobile()), 3, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            shoppingGuideService.shoppingGuideCommit(request);
        } finally {
            redisUtil.unLock(SHOPPING_GUIDE_COMMIT_LOCK, String.valueOf(request.getMobile()));
        }
        return Response.success();
    }

    @ApiOperation(value = "导购人员更新", notes = "导购人员更新")
    @Override
    public Response shoppingGuideUpdate(ShoppingGuideUpdateRequest request) {
        log.info("shoppingGuideUpdate request:{}", request);
        shoppingGuideService.shoppingGuideUpdate(request);
        return Response.success();
    }

    @ApiOperation(value = "导购人员列表", notes = "导购人员列表")
    @Override
    public Response<IPage<ShoppingGuideListVo>> shoppingGuideList(ShoppingGuideListRequest request) {
        log.info("shoppingGuideList request:{}", request);
        return Response.success(shoppingGuideService.shoppingGuideList(request));
    }

    @ApiOperation(value = "导购人员详情", notes = "导购人员详情")
    @Override
    public Response<ShoppingGuideDetailVo> shoppingGuideDetail(ShoppingGuideDetailRequest request) {
        log.info("shoppingGuideDetail request:{}", request);
        return Response.success(shoppingGuideService.shoppingGuideDetail(request));
    }


}
