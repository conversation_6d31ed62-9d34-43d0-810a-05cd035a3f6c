package com.wantwant.sfa.backend.skuControl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.skuControl.EmployeeSkuMapper;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.skuControl.api.SkuControlApi;
import com.wantwant.sfa.backend.skuControl.entity.EmployeeSkuEntity;
import com.wantwant.sfa.backend.skuControl.request.*;
import com.wantwant.sfa.backend.skuControl.service.ISkuControlSearchService;
import com.wantwant.sfa.backend.skuControl.service.ISkuControlService;
import com.wantwant.sfa.backend.skuControl.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/12/05/下午12:06
 */
@RestController
@Slf4j
public class SkuControlController implements SkuControlApi {

    @Autowired
    private ISkuControlService skuControlService;
    @Autowired
    private ISkuControlSearchService skuControlSearchService;
    @Autowired
    private EmployeeSkuMapper employeeSkuMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Override
    public Response modify(@Valid SkuControlModifyRequest request) {
        log.info("【sku control modify】request:{}",request);

        skuControlService.modify(request);

        return Response.success();
    }

    @Override
    public Response modifySpecial(@Valid SkuSpecialRequest request) {
        log.info("【modify special sku】request:{}",request);

        skuControlService.modifySpecial(request);

        return Response.success();
    }

    @Override
    public Response<List<SkuControlLogVo>> selectControlLog(ControlLogSearchRequest controlLogSearchRequest) {
        log.info("【select sku control log】request:{}",controlLogSearchRequest);

        List<SkuControlLogVo> list = skuControlSearchService.selectControlLog(controlLogSearchRequest);

        return Response.success(list);
    }



    @Override
    public Response<List<SkuOrgEmpVo>> getSkuOrgTree(String sku, String person, String orgCode) {
        log.info("【get sku org tree】sku:{},person:{}",sku,person);

        List<SkuOrgEmpVo> list = skuControlSearchService.getSkuOrgTree(sku,person,orgCode);

        return Response.success(list);
    }

    @Override
    public Response modifySkuEmp(@Valid SkuEmpModifyRequest skuEmpModifyRequest) {
        log.info("【modify sku emp】request:{}",skuEmpModifyRequest);

        skuControlService.modifySkuEmp(skuEmpModifyRequest);

        return Response.success();
    }

    @Override
    public Response modifyEmpSku(@Valid EmpSkuModifyRequest request) {
        log.info("【modify emp sku】request:{}",request);

        skuControlService.modifyEmpSku(request);

        return Response.success();
    }


    @Override
    public Response<List<SkuVo>> skuList(String orgCode) {

        List<SkuVo> list = skuControlSearchService.skuList(new ArrayList<>(),orgCode,true);

        return Response.success(list);
    }


    @Override
    public Response<List<SkuMonitorVo>> skuMonitorSearch(SkuMonitorSearchRequest skuMonitorSearchRequest) {
        log.info("【sku monitor search】skuMonitorSearchRequest:{}",skuMonitorSearchRequest);


        List<SkuMonitorVo> list = skuControlSearchService.skuMonitorSearch(skuMonitorSearchRequest);

        if(!CollectionUtils.isEmpty(list)){
            list.forEach(e -> {
                List<DepartmentEmpVo> departmentEmpVoList = e.getDepartmentEmpVoList();
                if(!CollectionUtils.isEmpty(departmentEmpVoList)){
                    List<EmpSkuVo> collect = departmentEmpVoList.stream().map(DepartmentEmpVo::getEmpSkuVoList).flatMap(Collection::stream).collect(Collectors.toList());
                    int size = collect.stream().filter(f -> f.isChecked()).collect(Collectors.toList()).size();
                    e.setTotalEmp(size);
                }
            });
        }

        return Response.success(list);
    }

    @Override
    public Response<List<SkuVo>> getSkuEmpTree(Integer employeeInfoId) {
        log.info("【get sku emp tree】employeeInfoId:{}",employeeInfoId);

        List<EmployeeSkuEntity> employeeSkuEntities = Optional.ofNullable(employeeSkuMapper.selectList(new LambdaQueryWrapper<EmployeeSkuEntity>().eq(EmployeeSkuEntity::getEmployeeInfoId, employeeInfoId)
                .eq(EmployeeSkuEntity::getBusinessGroup, RequestUtils.getBusinessGroup()).eq(EmployeeSkuEntity::getStatus, 1)
                .eq(EmployeeSkuEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());
        List<String> selectedSkuList = Optional.ofNullable(employeeSkuEntities.stream().map(EmployeeSkuEntity::getSku).collect(Collectors.toList())).orElse(new ArrayList<>());

        SfaPositionRelationEntity positionRelationEntityList = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, employeeInfoId)
                .eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .last("limit 1")
        );


        List<SkuVo> list = skuControlSearchService.skuList(selectedSkuList,positionRelationEntityList.getDepartmentCode(),true);

        return Response.success(list);
    }

    @Override
    public Response<List<SkuVo>> getSkuTreeWithApplyType(Integer applyId) {
        log.info("【get sku tree】applyId:{}",applyId);

        List<SkuVo> list =  skuControlSearchService.getSkuTreeWithApplyType(applyId);

        return Response.success(list);
    }

    @Override
    public Response<SaleSkuListVo> searchSaleSkuList(SaleSkuSearchRequest saleSkuSearchRequest) {
        log.info("【search sale sku】request:{}",saleSkuSearchRequest);

        SaleSkuListVo saleSkuListVo = skuControlSearchService.searchSaleSkuList(saleSkuSearchRequest);

        return Response.success(saleSkuListVo);
    }
}
