package com.wantwant.sfa.backend.sms.assemble;

import com.wantwant.sfa.backend.domain.sms.DO.InitVerificationCodeDO;
import com.wantwant.sfa.backend.domain.sms.DO.VerificationCodeVerifyDO;
import com.wantwant.sfa.backend.sms.request.InitVerificationCodeRequest;
import com.wantwant.sfa.backend.sms.request.VerificationCodeVerifyRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:14
 */
@Mapper(componentModel = "spring")
public interface SmsVerificationAssemble {


    InitVerificationCodeDO convertToInitDO(InitVerificationCodeRequest request);

    VerificationCodeVerifyDO convertToVerifyDO(VerificationCodeVerifyRequest request);
}
