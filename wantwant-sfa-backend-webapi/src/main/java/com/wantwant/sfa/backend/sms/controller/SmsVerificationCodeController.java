package com.wantwant.sfa.backend.sms.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.SmsVerificationApplication;
import com.wantwant.sfa.backend.sms.api.SmsVerificationCodeApi;
import com.wantwant.sfa.backend.sms.assemble.SmsVerificationAssemble;
import com.wantwant.sfa.backend.sms.request.InitVerificationCodeRequest;
import com.wantwant.sfa.backend.sms.request.VerificationCodeVerifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午1:13
 */
@RestController
@Slf4j
public class SmsVerificationCodeController implements SmsVerificationCodeApi {

    @Resource
    private SmsVerificationApplication smsVerificationApplication;
    @Resource
    private SmsVerificationAssemble smsVerificationAssemble;


    @Override
    public Response initVerificationCode(@Valid InitVerificationCodeRequest request) {
        log.info("【init verification code】request:{}",request);

        smsVerificationApplication.initVerificationCode(smsVerificationAssemble.convertToInitDO(request));
        return Response.success();
    }

    @Override
    public Response verify(@Valid VerificationCodeVerifyRequest request) {

        smsVerificationApplication.verify(smsVerificationAssemble.convertToVerifyDO(request));
        return Response.success();
    }
}
