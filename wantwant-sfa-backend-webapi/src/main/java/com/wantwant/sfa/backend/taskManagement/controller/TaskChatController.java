package com.wantwant.sfa.backend.taskManagement.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.task.service.ITaskChatService;
import com.wantwant.sfa.backend.taskManagement.api.TaskChatApi;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatDelRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskChatEditRequest;
import com.wantwant.sfa.backend.taskManagement.request.TaskLogReplyRequest;
import com.wantwant.sfa.backend.taskManagement.vo.TaskChatReplyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/07/下午2:08
 */
@RestController
@Slf4j
public class TaskChatController implements TaskChatApi {
    @Autowired
    private ITaskChatService taskChatService;

    @Override
    public Response reply(TaskLogReplyRequest taskLogReplyRequest) {

        log.info("【task log reply】request:{}",taskLogReplyRequest);

        taskChatService.reply(taskLogReplyRequest);

        return Response.success();
    }

    @Override
    public Response<List<TaskChatReplyVo>> getReply(Long logId, String person) {
        log.info("【get task log reply】logId:{},person:{}",logId,person);

        List<TaskChatReplyVo> list = taskChatService.getReply(logId,person);

        return Response.success(list);
    }

    @Override
    public Response deleteReply(TaskChatDelRequest taskChatDelRequest) {
        log.info("【delete reply】request:{}",taskChatDelRequest);

        taskChatService.deleteReply(taskChatDelRequest);

        return Response.success();
    }

    @Override
    public Response edit(TaskChatEditRequest taskChatEditRequest) {
        log.info("【task reply edit】request:{}",taskChatEditRequest);

        taskChatService.edit(taskChatEditRequest);

        return Response.success();
    }
}
