package com.wantwant.sfa.backend.taskManagement.controller;

import com.gexin.fastjson.JSONObject;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.task.service.ITaskApplication;
import com.wantwant.sfa.backend.taskManagement.api.TaskManagementApi;
import com.wantwant.sfa.backend.taskManagement.request.*;
import com.wantwant.sfa.backend.taskManagement.vo.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/11/下午7:13
 */
@RestController
@Slf4j
public class TaskManagementController implements TaskManagementApi {
    @Autowired
    private ITaskApplication taskApplication;

    @Override
    public Response createTask(@Valid CreateTaskRequest createTaskRequest) {
        log.info("【create task】request:{}",createTaskRequest);
        taskApplication.createTask(createTaskRequest);

        return Response.success();
    }

    @Override
    public Response<List<AssignVo>> getAssign(String employeeName) {
        List<AssignVo> list = taskApplication.getAssign(employeeName);
        return Response.success(list);
    }

    @Override
    public Response follow(@Valid TaskFollowRequest taskFollowRequest) {
        log.info("【task follow】request:{}",taskFollowRequest);
        taskApplication.follow(taskFollowRequest);
        return Response.success();
    }

    @Override
    public Response deleteTask(@Valid TaskOperatorRequest request) {
        log.info("【delete task】request:{}",request);
        taskApplication.delete(request);
        return Response.success();
    }

    @Override
    public Response<Page<TaskVo>> selectList(TaskSelectRequest taskSelectRequest) {

        Page<TaskVo> page = taskApplication.selectList(taskSelectRequest);

        return Response.success(page);
    }

    @ApiOperation(value = "任务列表-列表下载", notes = "任务列表-列表下载")
    @Override
    public void downloadList(@RequestBody TaskSelectRequest request, HttpServletRequest req, HttpServletResponse res) {
        taskApplication.downloadList(request, req, res);
    }

    @Override
    public Response updateTask(@Valid UpdateTaskRequest updateTaskRequest) {
        log.info("【update task】request:{}",updateTaskRequest);
        taskApplication.updateTask(updateTaskRequest);
        return Response.success();
    }

    @Override
    public Response modifyTask(@Valid UpdateTaskRequest updateTaskRequest) {
        log.info("【modify task】request:{}", JSONObject.toJSONString(updateTaskRequest));
        taskApplication.modifyTask(updateTaskRequest);
        return Response.success();
    }

    @Override
    public Response modifyDeadline(@Valid ModifyDeadlineRequest modifyDeadlineRequest) {
        log.info("【modify deadline】request:{}",modifyDeadlineRequest);
        taskApplication.modifyDeadline(modifyDeadlineRequest);
        return Response.success();
    }

    @Override
    public Response<List<TaskSituationVo>> getSituation(Long taskId) {
        log.info("【get task situation】 taskId:{}",taskId);
        List<TaskSituationVo> list = taskApplication.getSituation(taskId);
        return Response.success(list);
    }

    @Override
    public Response<List<TaskLogVo>> getTaskLog(Long taskId) {
        log.info("【get task log】 taskId:{}",taskId);
        List<TaskLogVo> list = taskApplication.getTaskLog(taskId);
        return Response.success(list);
    }

    @Override
    public Response<TaskDetailVo> getDetail(Long taskId,String person) {
        log.info("【get task detail】 taskId:{}",taskId);
        TaskDetailVo taskDetailVo =  taskApplication.getDetail(taskId,person);
        return  Response.success(taskDetailVo);
    }

    @Override
    public Response publish(@Valid TaskOperatorRequest request) {
        log.info("【task publish】 request:{}",request);
        taskApplication.publish(request);
        return Response.success();
    }

    @Override
    public Response batchPublish(@Valid TaskBatchCommand command) {
        log.info("【task batch publish】 command:{}",command);
        taskApplication.batchPublish(command);
        return Response.success();
    }

    @Override
    public Response revert(@Valid TaskOperatorRequest request) {
        log.info("【task revert】 request:{}",request);
        taskApplication.revert(request);

        return Response.success();
    }

    @Override
    public Response sign(@Valid TaskOperatorRequest request) {
        log.info("【task sign】request:{}",request);
        taskApplication.sign(request);
        return Response.success();
    }

    @Override
    public Response modifyAssign(@Valid TaskAssignModifyRequest request) {
        log.info("【task modify assign】request:{}",request);
        taskApplication.modifyAssign(request);
        return Response.success();
    }

    @Override
    public Response submitSituation(@Valid TaskSituationSubmitRequest taskSituationSubmitRequest) {
        log.info("【task submit situation】request:{}",taskSituationSubmitRequest);
        taskApplication.submitSituation(taskSituationSubmitRequest);
        return Response.success();
    }

    @Override
    public Response complete(@Valid TaskCompleteRequest request) {
        log.info("【task complete】request:{}",request);
        taskApplication.complete(request);
        return Response.success();
    }

    @Override
    public Response finish(@Valid TaskOperatorRequest request) {
        log.info("【task finish】request:{}",request);
        taskApplication.finish(request);
        return Response.success();
    }

    @Override
    public Response batchFinish(@Valid TaskBatchCommand command) {
        log.info("【task finish】request:{}",command);
        taskApplication.batchFinish(command);
        return Response.success();
    }

    @Override
    public Response redo(@Valid TaskRedoRequest request) {
        log.info("【task redo】request:{}",request);
        taskApplication.redo(request);
        return Response.success();
    }

    @Override
    public Response suspend(@Valid TaskSuspendRequest request) {
        log.info("【task suspend】request:{}",request);
        taskApplication.suspend(request);
        return Response.success();
    }

    @Override
    public Response closed(@Valid TaskOperatorRequest request) {
        log.info("【task close】request:{}",request);
        taskApplication.close(request);
        return Response.success();
    }

    @Override
    public Response<List<TaskSampleVo>> searchTaskByName(String taskName) {
        log.info("【search task by name】taskName:{}",taskName);
        List<TaskSampleVo> list = taskApplication.searchTaskByName(taskName);
        return Response.success(list);
    }

    @Override
    public Response<TaskInfoVo> taskInfo(String person) {
        log.info("【search task info】person:{}",person);
        TaskInfoVo taskInfoVo = taskApplication.taskInfo(person);
        return Response.success(taskInfoVo);
    }

    @Override
    public Response<Page<TaskTraceVo>> selectTaskTraceAudit(TaskTraceAuditSearchRequest request) {
        log.info("【select task trace】request:{}",request);
        Page<TaskTraceVo> page = taskApplication.selectTaskTraceAudit(request);
        return Response.success(page);
    }

    @Override
    public void taskTraceExport(TaskTraceAuditSearchRequest request) {
        log.info("【task trace export】request:{}",request);
        taskApplication.taskTraceExport(request);
    }

    @Override
    public Response callback(@Valid TraceCallBackRequest traceCallBackRequest) {
        log.info("【trace callback】request:{}",traceCallBackRequest);
        taskApplication.callback(traceCallBackRequest);
        return Response.success();
    }

    @Override
    public Response audit(@Valid TraceAuditRequest traceAuditRequest) {
        log.info("【trace audit 】request:{}",traceAuditRequest);
        taskApplication.audit(traceAuditRequest);
        return Response.success();
    }

    @Override
    public Response<Boolean> auditPermission(String person) {
        log.info("【trace audit permission】person:{}",person);
        Boolean flag = taskApplication.auditPermission(person);
        return Response.success(flag);
    }

    /**
     * 任务驳回
     * LEO的待确认发布、待确认完结、项目管理（杜颖、孙帆）的待发布、待审核，新增驳回按钮
     *
     * @param request
     * @return
     */
    @Override
    public Response refuse(@Valid TaskRefuseRequest request) {
        log.info("【task refuse】request:{}",request);
        taskApplication.refuse(request);
        return Response.success();
    }

    @Override
    public Response launchMeeting(@Valid LaunchMeetingRequest launchMeetingRequest) {
        log.info("【task launch meeting】request:{}",launchMeetingRequest);
        taskApplication.launchMeeting(launchMeetingRequest);
        return Response.success();
    }

    @Override
    public Response meetingAudit(@Valid TaskAuditRequest taskAuditRequest) {
        log.info("【task meeting audit】request:{}",taskAuditRequest);
        taskApplication.meetingAudit(taskAuditRequest);
        return Response.success();
    }

    @Override
    public Response meetingAuditFinish(@Valid TaskAuditRequest taskAuditRequest) {
        log.info("【task meeting audit finish】request:{}",taskAuditRequest);
        taskApplication.meetingAuditFinish(taskAuditRequest);
        return Response.success();
    }

    @Override
    public Response taskContextModify(@Valid TaskContextModifyRequest taskContextModifyRequest) {
        log.info("【task context modify】request:{}",taskContextModifyRequest);
        taskApplication.taskContextModify(taskContextModifyRequest);
        return Response.success();
    }

    @Override
    public Response urge(@Valid TaskAuditRequest taskAuditRequest) {
        log.info("【task urge】request:{}",taskAuditRequest);
        taskApplication.urge(taskAuditRequest);
        return Response.success();
    }

    @Override
    public Response batchUrge(@Valid TaskBatchCommand command) {
        log.info("【batch urge】request:{}",command);
        taskApplication.batchUrge(command);
        return Response.success();
    }
}
