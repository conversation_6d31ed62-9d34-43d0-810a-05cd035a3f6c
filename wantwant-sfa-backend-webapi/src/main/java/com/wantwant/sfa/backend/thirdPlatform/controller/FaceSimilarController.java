package com.wantwant.sfa.backend.thirdPlatform.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.attendance.service.impl.AttendanceService;
import com.wantwant.sfa.backend.complete.service.CompleteService;
import com.wantwant.sfa.backend.service.meeting.MeetingInfoService;
import com.wantwant.sfa.backend.thirdPlatform.api.FaceSimilarAPI;
import com.wantwant.sfa.backend.thirdPlatform.request.FaceSimilarRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Slf4j
public class FaceSimilarController implements FaceSimilarAPI {

    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private CompleteService completeService;

    @Autowired
    private MeetingInfoService meetingInfoService;

    @Override
    public Response faceSimilar(FaceSimilarRequest similarRequest) {
        if(similarRequest.getType() == 0) {
            return Response.success(attendanceService.faceSimilar(similarRequest.getUrl()));
        }else if (similarRequest.getType() == 1) {
            return Response.success(completeService.faceSimilar(similarRequest.getUrl()));
        }else if (similarRequest.getType() == 2) {
            return Response.success(meetingInfoService.faceSimilar(similarRequest.getUrl()));
        } else {
            throw new ApplicationException("type 类型传入不正确");
        }

    }
}
