package com.wantwant.sfa.backend.track.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.track.api.TrackApi;
import com.wantwant.sfa.backend.track.request.TrackMenuRequest;
import com.wantwant.sfa.backend.track.service.ITrackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class TrackController implements TrackApi {

    @Autowired
    private ITrackService iTrackService;

    @Override
    public Response commitTrackMenuInfo(List<TrackMenuRequest> list) {
        log.info("commitTrackMenuInfo:{}",list);
        iTrackService.commitTrackMenuInfo(list);
        return Response.success();
    }
}
