package com.wantwant.sfa.backend.transaction.controller;


import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.transaction.api.PositionTransactionApi;
import com.wantwant.sfa.backend.transaction.request.*;
import com.wantwant.sfa.backend.transaction.service.ITransactionConvertService;
import com.wantwant.sfa.backend.transaction.service.ITransactionService;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/24/下午4:20
 */
@RestController
@Slf4j

public class PositionTransactionController implements PositionTransactionApi {

    @Autowired
    private ITransactionService transactionService;
    @Autowired
    private ITransactionConvertService transactionConvertService;
    @Autowired
    private RedisUtil redisUtil;

    private static final String TRANSACTION_APPLY_LOCK = "sfa:transaction:apply:";

    private static final String TRANSACTION_CEO_APPLY_LOCK = "sfa:transaction:ceo:apply:";

    private static final String TRANSACTION_PROCESS_LOCK = "sfa:transaction:process:";

    private static final String TRANSACTION_AUDIT_LOCK ="sfa:transaction:audit:";

    @Override
    public Response apply(PositionTransactionApplyRequest request) {
        log.info("【position transaction apply】request:{}",request);

        if(!redisUtil.setLockIfAbsent(TRANSACTION_APPLY_LOCK, String.valueOf(request.getEmployeeInfoId()), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            transactionService.transactionApply(transactionConvertService.convertToApplyDTO(request));
        }finally {
            redisUtil.unLock(TRANSACTION_APPLY_LOCK,String.valueOf(request.getEmployeeInfoId()));
        }

        return Response.success();
    }

    @Override
    public Response ceoApply(CeoBusinessGroupTransactionApplyRequest request) {
        log.info("【ceo transaction apply】 request:{}",request);

        if(!redisUtil.setLockIfAbsent(TRANSACTION_CEO_APPLY_LOCK, String.valueOf(request.getMemberKey()), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            transactionService.transactionCeoApply(request);
        }finally {
            redisUtil.unLock(TRANSACTION_APPLY_LOCK,String.valueOf(request.getMemberKey()));
        }

        return Response.success();
    }


    @Override
    public Response audit(@Valid PositionTransactionAuditRequest request) {
        log.info("【position transaction audit】request:{}",request);
        if(!redisUtil.setLockIfAbsent(TRANSACTION_AUDIT_LOCK, String.valueOf(request.getTransactionApplyId()), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try {
            transactionService.audit(request);
        }finally {
            redisUtil.unLock(TRANSACTION_AUDIT_LOCK,String.valueOf(request.getTransactionApplyId()));
        }
        return Response.success();
    }

    @Override
    public Response transactionProcess(PositionTransactionProcessRequest request) {
        log.info("【position transaction process】request:{}",request);
        if(!redisUtil.setLockIfAbsent(TRANSACTION_PROCESS_LOCK, String.valueOf(request.getTransactionApplyId()), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try{
            transactionService.transactionProcess(request);
        }finally {
            redisUtil.unLock(TRANSACTION_PROCESS_LOCK,String.valueOf(request.getTransactionApplyId()));
        }


        return Response.success();
    }

    @Override
    public Response changeCompany(ChangCompanyRequest changCompanyRequest) {
        log.info("【change ChangCompanyRequest】request:{}",changCompanyRequest);

        transactionService.changeCompany(changCompanyRequest);

        return Response.success();
    }
}
