package com.wantwant.sfa.backend.transaction.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.vo.OrganizationSalaryControlVo;
import com.wantwant.sfa.backend.transaction.api.PositionTransactionSearchApi;
import com.wantwant.sfa.backend.transaction.request.ChangeCompanySearchRequest;
import com.wantwant.sfa.backend.transaction.request.PositionTransactionSearchRequest;
import com.wantwant.sfa.backend.transaction.service.ITransactionSearchService;
import com.wantwant.sfa.backend.transaction.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/11/24/下午7:30
 */
@RestController
@Slf4j
public class PositionTransactionSearchController implements PositionTransactionSearchApi {

    @Autowired
    private ITransactionSearchService transactionSearchService;


    @Override
    public Response<IPage<PositionTransactionVo>> list(PositionTransactionSearchRequest request) {

        IPage<PositionTransactionVo> list = transactionSearchService.list(request);

        return Response.success(list);
    }

    @Override
    public void export(PositionTransactionSearchRequest request) {
        transactionSearchService.export(request);
    }

    @Override
    public Response<PositionTransactionEmployeeInfoVo> getPositionTransactionEmployeeInfo(Long transactionApplyId) {
        PositionTransactionEmployeeInfoVo vo = transactionSearchService.getPositionTransactionEmployeeInfo(transactionApplyId);
        return Response.success(vo);
    }

    @Override
    public Response<PositionTransactionApplyEmployeeInfoVo> getPositionTransactionApplyInfo(Integer employeeInfoId) {
        PositionTransactionApplyEmployeeInfoVo vo = transactionSearchService.getPositionTransactionApplyInfo(employeeInfoId);
        return Response.success(vo);
    }

    @Override
    public Response<List<PositionTransactionSelectVo>> getPositionTransactionSelectVo(String employeeId,Integer currentPositionId) {
        List<PositionTransactionSelectVo> list = transactionSearchService.getPositionTransactionSelectVo(employeeId,currentPositionId);
        return Response.success(list);
    }


    @Override
    public Response<List<TransactionBaseVo>> getTransactionRecord(Integer applyId) {
        log.info("【transaction record】applyId:{}",applyId);
        List<TransactionBaseVo> list = transactionSearchService.getTransactionRecord(applyId);
        return Response.success(list);
    }

    @Override
    public Response<List<TransactionRecordVo>> getCurrentTransactionRecord(Long transactionId) {
        log.info("【transaction record】transactionId:{}",transactionId);
        List<TransactionRecordVo> list = transactionSearchService.getCurrentTransactionRecord(transactionId);
        return Response.success(list);
    }

    @Override
    public Response<TransactionEmployeeBaseVo> transactionBaseInfo(Integer employeeInfoId) {
        log.info("【transaction base info】employeeInfoId:{}",employeeInfoId);
        TransactionEmployeeBaseVo transactionEmployeeBaseVo = transactionSearchService.transactionBaseInfo(employeeInfoId);
        return Response.success(transactionEmployeeBaseVo);
    }

    @Override
    public Response<List<TransactionActionVo>> getTransactionAction(Integer employeeInfoId) {
        log.info("【get transaction action】employeeInfoId:{}",employeeInfoId);
        List<TransactionActionVo> list = transactionSearchService.getTransactionAction(employeeInfoId);
        return Response.success(list);
    }

    @Override
    public Response<List<TransactionCeoTypeVo>> getTransactionCeoTypeAction(Integer employeeInfoId) {
        log.info("【get transaction ceoType】employeeInfoId:{}",employeeInfoId);
        List<TransactionCeoTypeVo> list = transactionSearchService.getTransactionCeoTypeAction(employeeInfoId);
        return Response.success(list);
    }

    @Override
    public Response<TransactionOrganizationPositionVo> getTransactionOrganizationPosition(Integer employeeInfoId) {
        log.info("【get transaction position organization】employeeInfoId:{}",employeeInfoId);
        TransactionOrganizationPositionVo vo = transactionSearchService.getTransactionOrganizationPosition(employeeInfoId);
        return Response.success(vo);
    }

    @Override
    public Response<MainPartTimeVo> getMainPartTimeOrgList(Integer employeeInfoId) {
        log.info("【get main part orgList】employeeInfoId:{}",employeeInfoId);

        MainPartTimeVo vo = transactionSearchService.getMainPartTimeOrgList(employeeInfoId);

        return Response.success(vo);
    }

    @Override
    public Response<TransactionInfoVo> getTransactionInfo(Long transactionApplyId) {
        TransactionInfoVo transactionInfoVo = transactionSearchService.getTransactionInfo(transactionApplyId);
        return Response.success(transactionInfoVo);
    }

    @Override
    public Response<TransactionBusinessVo> getTransferBusinessGroup(Long transactionApplyId) {
        log.info("【get transfer businessGroup】applyId:{}",transactionApplyId);
        TransactionBusinessVo vo = transactionSearchService.getTransferBusinessGroup(transactionApplyId);
        return Response.success(vo);
    }

    @Override
    public Response<List<BusinessBDSelectedVo>> getBusinessBDPersonnel(Integer employeeInfoId) {
        log.info("【get business bd personnel】employeeInfoId:{}",employeeInfoId);

        List<BusinessBDSelectedVo> list = transactionSearchService.getBusinessBDPersonnel(employeeInfoId);
        return Response.success(list);
    }

    @Override
    public Response<OrganizationSalaryControlVo> getSalaryControl(Integer transactionId) {
        log.info("【get salary control】transactionId:{}",transactionId);
        OrganizationSalaryControlVo vo = transactionSearchService.getSalaryControl(transactionId);
        return Response.success(vo);
    }

    @Override
    public Response<List<EmployeeVo>> changeCompanyList(@Valid ChangeCompanySearchRequest changeCompanySearchRequest) {
        log.info("【change company list】request:{}",changeCompanySearchRequest);

        List<EmployeeVo> list = transactionSearchService.changeCompanyList(changeCompanySearchRequest);

        return Response.success(list);
    }

    @Override
    public void changeCompanyExport(@Valid ChangeCompanySearchRequest changeCompanySearchRequest) {
        log.info("【change company export】changeCompanySearchRequest:{}",changeCompanySearchRequest);

        transactionSearchService.changeCompanyExport(changeCompanySearchRequest);
    }
}
