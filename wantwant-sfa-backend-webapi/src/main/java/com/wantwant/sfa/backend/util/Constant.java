package com.wantwant.sfa.backend.util;

import com.wantwant.sfa.backend.vo.ResultVo;

public class Constant {

    public static final String MESSAGE_SUCCESS = "操作成功";

    public static final String SUCCESS_CODE = "0000";

    public static final String SUCCESS_FLAG = "1";


    public static final String MESSAGE_FAIL = "操作失败";

    public static final String FAIL_CODE = "9999";

    public static final String FAIL_FLAG = "0";

    public static final Integer FILE_TYPE_EXCEL = 1;
    public static final String FILE_TYPE_EXCEL_SUFFIX = ".xlsx";
    public static final Integer FILE_TYPE_CSV = 2;
    public static final String FILE_TYPE_CSV_SUFFIX = ".csv";

    public static ResultVo SetResultVo(ResultVo resultVo, String code, String message) {
        if (resultVo == null) {
            resultVo = new ResultVo();
        }
        if (code.equals(Constant.SUCCESS_CODE)) {
            resultVo.setSuccess("1");
        } else {
            resultVo.setSuccess("0");
        }
        resultVo.setCode(code);
        resultVo.setMessage(message);
        return resultVo;
    }

}
