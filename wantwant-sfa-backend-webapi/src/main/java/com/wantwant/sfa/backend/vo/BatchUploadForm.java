package com.wantwant.sfa.backend.vo;

import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

public class BatchUploadForm{
	
	private static final long serialVersionUID = 3601881317869840351L;

	//模板id
	private String messageTemplateId;
	
	@NotNull(message="文件不能为空")
	private transient MultipartFile file;

	public MultipartFile getFile() {
		return file;
	}
	
	public void setFile(MultipartFile file) {
		this.file = file;
	}

	public String getMessageTemplateId() {
		return messageTemplateId;
	}

	public void setMessageTemplateId(String messageTemplateId) {
		this.messageTemplateId = messageTemplateId;
	}
}
