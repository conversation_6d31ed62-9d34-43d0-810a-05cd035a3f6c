package com.wantwant.sfa.backend.vo;

import com.wantwant.sfa.backend.util.Constant;

import java.io.Serializable;

/**
 * vo对象传输
 * 
 * <AUTHOR>
 *
 */
public class ResultVo implements Serializable{

	private String code;

	private String message;

	private String success;

	public ResultVo() {
	}

	public ResultVo(String success) {
		super();
		this.success = success;
		if (success.equals(Constant.SUCCESS_FLAG)) {
			this.message = Constant.MESSAGE_SUCCESS;
			this.code = Constant.SUCCESS_CODE;
		} else if (success.equals(Constant.FAIL_FLAG)) {
			this.message = Constant.MESSAGE_FAIL;
			this.code = Constant.FAIL_CODE;
		}
	}

	private Object data;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getSuccess() {
		return success;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

}
