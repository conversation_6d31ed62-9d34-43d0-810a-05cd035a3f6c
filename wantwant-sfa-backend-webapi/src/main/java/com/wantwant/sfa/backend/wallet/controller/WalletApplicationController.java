package com.wantwant.sfa.backend.wallet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.WalletQuotaApplication;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.wallet.api.WalletApplicationApi;
import com.wantwant.sfa.backend.wallet.request.ApplyInfoRequest;
import com.wantwant.sfa.backend.wallet.request.QuotaAuditRequest;
import com.wantwant.sfa.backend.wallet.request.WalletQuotaApplySearchRequest;
import com.wantwant.sfa.backend.wallet.vo.ApplyInfoVO;
import com.wantwant.sfa.backend.wallet.vo.WalletQuotaApplicationVo;
import com.wantwant.sfa.backend.wallet.vo.WalletQuotaApplyDetailVo;
import com.wantwant.sfa.backend.wallet.vo.WalletSurplusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/21/上午11:27
 */
@RestController
@Slf4j
public class WalletApplicationController implements WalletApplicationApi {

    @Resource
    private WalletQuotaApplication walletQuotaApplication;

    @Resource
    private RedisUtil redisUtil;

    private final static String WALLET_FLOW_LOCK = "sfa:wallet:flow";

    @Override
    public Response pass(@Valid QuotaAuditRequest quotaAuditRequest) {
        log.info("【wallet application pass】request:{}",quotaAuditRequest);

        if(!redisUtil.setLockIfAbsent(WALLET_FLOW_LOCK,quotaAuditRequest.getInstanceId().toString(),10, TimeUnit.SECONDS)){
            return Response.error("请勿重复操作");
        }

        try{
            walletQuotaApplication.pass(quotaAuditRequest);
        }finally {
            redisUtil.unLock(WALLET_FLOW_LOCK,quotaAuditRequest.getInstanceId().toString());
        }

        return Response.success();
    }

    @Override
    public Response reject(@Valid QuotaAuditRequest quotaAuditRequest) {
        log.info("【wallet application reject】request:{}",quotaAuditRequest);


        if(!redisUtil.setLockIfAbsent(WALLET_FLOW_LOCK,quotaAuditRequest.getInstanceId().toString(),10, TimeUnit.SECONDS)){
            return Response.error("请勿重复操作");
        }

        try{
            walletQuotaApplication.reject(quotaAuditRequest);
        }finally {
            redisUtil.unLock(WALLET_FLOW_LOCK,quotaAuditRequest.getInstanceId().toString());
        }


        return Response.success();
    }

    @Override
    public Response<IPage<WalletQuotaApplicationVo>> search(@Valid WalletQuotaApplySearchRequest walletQuotaApplySearchRequest) {
        log.info("【wallet quota apply search】request:{}",walletQuotaApplySearchRequest);
        IPage<WalletQuotaApplicationVo> result = walletQuotaApplication.search(walletQuotaApplySearchRequest);
        return Response.success(result);
    }

    @Override
    public void searchExport(@Valid WalletQuotaApplySearchRequest walletQuotaApplySearchRequest) {
        log.info("【wallet quota apply search export】request:{}",walletQuotaApplySearchRequest);
        walletQuotaApplication.export(walletQuotaApplySearchRequest);
    }


    @Override
    public Response<WalletQuotaApplyDetailVo> getApplyDetail(Long applyId,String person) {
        log.info("【get apply detail】applyId:{},person:{}",applyId,person);

        WalletQuotaApplyDetailVo walletQuotaApplyDetailVo = walletQuotaApplication.getApplyDetail(applyId,person);
        return Response.success(walletQuotaApplyDetailVo);
    }

    @Override
    public Response<ApplyInfoVO> getApplyInfo(@Valid ApplyInfoRequest applyInfoRequest) {
        log.info("【want wallet apply】request:{}",applyInfoRequest);
        ApplyInfoVO applyInfo = walletQuotaApplication.getApplyInfo(applyInfoRequest);
        return Response.success(applyInfo);
    }

    @Override
    public Response<List<WalletSurplusVO>> getWalletSurplus(String orgCode) {
        log.info("【get wallet surplus】orgCode:{}",orgCode);
        List<WalletSurplusVO> list = walletQuotaApplication.getWalletSurplus(orgCode);
        return Response.success(list);
    }


}
