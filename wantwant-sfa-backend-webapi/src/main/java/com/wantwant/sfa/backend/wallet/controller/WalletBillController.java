package com.wantwant.sfa.backend.wallet.controller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.wallet.api.WalletBillApi;
import com.wantwant.sfa.backend.wallet.request.CeoBillSearchRequest;
import com.wantwant.sfa.backend.wallet.request.OrgBillSearchRequest;
import com.wantwant.sfa.backend.wallet.service.IWalletBillService;
import com.wantwant.sfa.backend.wallet.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/10/下午3:38
 */
@RestController
@Slf4j
public class WalletBillController implements WalletBillApi {
    @Resource
    private IWalletBillService walletBillService;

    @Override
    public Response<List<MonthBillVo>> getYearBill(String year, String organizationId) {
        log.info("【wallet year bill 】year:{},organizationId:{}",year,organizationId);

        List<MonthBillVo> list = walletBillService.getYearBill(year,organizationId);
        return Response.success(list);
    }

    @Override
    public Response<List<WalletTypeRateVo>> getTypeRate(String year, String organizationId, String type) {
        log.info("【wallet year rate 】year:{},organizationId:{},type:{}",year,organizationId,type);

        List<WalletTypeRateVo> list = walletBillService.getTypeRate(year, organizationId, type);
        return Response.success(list);
    }

    @Override
    public Response<OrgBillPermissionVo> getBillPermission() {
        log.info("【get bill permission】");

        OrgBillPermissionVo orgBillPermissionVo = walletBillService.getBillPermission();
        return Response.success(orgBillPermissionVo);
    }

    @Override
    public Response<Page<OrgBillVo>> selectOrgBill(OrgBillSearchRequest orgBillSearchRequest) {
        log.info("【select org bill】request:{}",orgBillSearchRequest);
        orgBillSearchRequest.setNeedPage(true);
        Page<OrgBillVo> pages = walletBillService.selectOrgBill(orgBillSearchRequest);

        return Response.success(pages);
    }

    @Override
    public void exportOrgBill(OrgBillSearchRequest orgBillSearchRequest) {
        log.info("【export org bill】request:{}",orgBillSearchRequest);

        walletBillService.exportOrgBill(orgBillSearchRequest);
    }

    @Override
    public Response<CeoBillAggregationVo> selectCeoBill(@Valid CeoBillSearchRequest ceoBillSearchRequest) {
        log.info("【select ceo bill】request:{}",ceoBillSearchRequest);

        CeoBillAggregationVo vo = walletBillService.selectCeoBill(ceoBillSearchRequest);

        return Response.success(vo);
    }

    @Override
    public void exportCeoBill(@Valid CeoBillSearchRequest ceoBillSearchRequest) {
        log.info("【export ceo bill】request:{}",ceoBillSearchRequest);

        walletBillService.exportCeoBill(ceoBillSearchRequest);
    }
}
