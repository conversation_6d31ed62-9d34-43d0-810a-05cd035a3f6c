package com.wantwant.sfa.backend.wallet.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gexin.fastjson.JSONArray;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.activityQuota.model.ProcessUserModel;
import com.wantwant.sfa.backend.activityQuota.service.IQuotaSendService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.gold.enums.TransactionBusinessTypeEnum;
import com.wantwant.sfa.backend.interview.enums.PositionTypeEnum;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.wallet.api.WalletApi;
import com.wantwant.sfa.backend.wallet.dto.ExpensesAdditionalDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletSendDTO;
import com.wantwant.sfa.backend.wallet.request.*;
import com.wantwant.sfa.backend.wallet.service.IWalletAgentService;
import com.wantwant.sfa.backend.wallet.service.IWalletApplicationService;
import com.wantwant.sfa.backend.wallet.service.IWalletBlackListService;
import com.wantwant.sfa.backend.wallet.service.IWalletSearchService;
import com.wantwant.sfa.backend.wallet.vo.*;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/29/上午9:29
 */
@RestController
@Slf4j
public class WalletController implements WalletApi {
    @Autowired
    private IWalletSearchService walletSearchService;
    @Autowired
    private IWalletApplicationService walletApplicationService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private IWalletBlackListService walletBlackListService;
    @Resource
    private IWalletAgentService walletAgentService;
    @Resource
    private IQuotaSendService quotaSendService;

    private static final String REWARDS_SEND_LOCK = "sfa:rewards:send";

    private static final String QUICK_SEND_LOCK = "sfa:wallet:quickSend";

    private static final String QUICK_BATCH_LOCK = "sfa:wallet:batch";

    private static final String QUICK_REVERT_LOCK = "sfa:wallet:revert";

    private static final String MEMBER_KEY_REVERT_LOCK = "sfa:wallet:revert:memberKey";




    @Override
    public Response<List<WalletTypeQuotaVo>> getWalletTypeQuota(String organizationId) {
        log.info("【get wallet type quota】organizationId:{}",organizationId);
        List<WalletTypeQuotaVo> list = walletSearchService.getWalletTypeQuota(organizationId);
        return Response.success(list);
    }

    @Override
    public Response<List<WalletTypeVo>> getWalletType() {
        List<WalletTypeVo> list = walletSearchService.getWalletType();
        return Response.success(list);
    }

    @Override
    public Response<List<WalletOrgQuotaVo>> getLowerQuota(String organizationId, Integer walletType,String person) {
        log.info("【get Lower quota】organizationId:{},walletType:{}",organizationId,walletType);
        List<WalletOrgQuotaVo> nextOrgQuota = walletSearchService.getNextOrgQuota(organizationId, walletType, null,person);
        return Response.success(nextOrgQuota);
    }

    @Override
    public Response<WalletOrgSpuVo> getSpuOrgVo(String organizationId, String spuId) {
        WalletOrgSpuVo walletOrgSpuVo =  walletSearchService.getSpuOrgVo(organizationId,spuId);
        return Response.success(walletOrgSpuVo);
    }

    @Override
    public Response<List<WalletOrgSpuVo>> getSpuTotalVo(String organizationId) {
        log.info("【get spu total】orgCode:{}",organizationId);

        List<WalletOrgSpuVo> list = walletSearchService.getSpuTotalVo(organizationId);
        return Response.success(list);
    }

    @Override
    public Response<List<WalletSpuVo>> getSpu(String spuKey) {
        List<WalletSpuVo> list = walletSearchService.getSpuQuota(spuKey);
        return Response.success(list);
    }

    @Override
    public Response<Page<WalletBusinessQuotaVo>> selectBusinessQuota(WalletBusinessQuotaRequest request) {
        log.info("【wallet business search】request:{}",request);

        Page<WalletBusinessQuotaVo> page = walletSearchService.selectBusinessQuota(request);

        return Response.success(page);
    }

    @Override
    public Response<WalletQuotaVo> getWalletQuota(String organizationId,Integer walletTypeId,String spuId) {
        log.info("【get walletQuota】organizationId:{},walletTypeId:{},spuId:{}",organizationId,walletTypeId,spuId);

        WalletQuotaVo walletQuotaVo = walletSearchService.getWalletQuota(organizationId,walletTypeId,spuId);
        return Response.success(walletQuotaVo);
    }

    @Override
    public Response quickSend(@Valid QuotaSendRequest quotaSendRequest) {
        log.info("【wallet quick send】request:{}",quotaSendRequest);

        Integer count = Optional.ofNullable(walletBlackListService.blackListCount(quotaSendRequest.getPerson())).orElse(0);
        if(count > 0){
            throw new ApplicationException("发放旺金币权限已冻结");
        }


        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(quotaSendRequest.getPerson(), RequestUtils.getLoginInfo());
        if(!redisUtil.setLockIfAbsent(QUICK_SEND_LOCK, quotaSendRequest.getOrganizationId(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }


        try{
            WalletSendDTO walletSendDTO = new WalletSendDTO();
            walletSendDTO.setVerifyAuditFlowCheck(!quotaSendRequest.isSkipAudit());
            walletSendDTO.setCostPurpose(quotaSendRequest.getCostPurpose());
            walletSendDTO.setProcessUserId(personInfo.getEmployeeId());
            walletSendDTO.setBusinessGroup(sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup()).getBusinessGroupCode());
            walletSendDTO.setProcessUserName(personInfo.getEmployeeName());
            walletSendDTO.setProcessUserId(personInfo.getEmployeeId());
            walletSendDTO.setProcessUserName(personInfo.getEmployeeName());
            ProcessUserModel processUserModel = quotaSendService.buildProcessUserModel(personInfo.getEmployeeId(), RequestUtils.getBusinessGroup());
            walletSendDTO.setProcessUserOrgName(processUserModel.getProcessUserOrgName());
            walletSendDTO.setProcessUserRole(processUserModel.getProcessUserRole());


            walletSendDTO.setSenderOrgCode(quotaSendRequest.getOrganizationId());
            walletSendDTO.setSendWalletType(quotaSendRequest.getWalletTypeId());
            walletSendDTO.setReceiverWalletType(quotaSendRequest.getReceiverWalletTypeId());
            walletSendDTO.setReceiverType(quotaSendRequest.getReceiverType());
            walletSendDTO.setReceiverKey(quotaSendRequest.getReceiverKey());
            walletSendDTO.setPositionId(quotaSendRequest.getReceiverPositionId());
            walletSendDTO.setQuota(quotaSendRequest.getQuota());
            walletSendDTO.setSubTypeId(quotaSendRequest.getSubTypeId());
            walletSendDTO.setRemark(quotaSendRequest.getRemark());
            walletSendDTO.setAnnexRequestList(quotaSendRequest.getAnnexRequestList());
            walletSendDTO.setTransactionBusinessTypeEnum(TransactionBusinessTypeEnum.SFA_BUSINESS_MANAGER_GRANT);
            ExpensesAdditionalRequest expensesAdditionalRequest = quotaSendRequest.getExpensesAdditionalRequest();
            if(Objects.nonNull(expensesAdditionalRequest)){
                ExpensesAdditionalDTO expensesAdditionalDTO = new ExpensesAdditionalDTO();
                BeanUtils.copyProperties(expensesAdditionalRequest,expensesAdditionalDTO);
                walletSendDTO.setExpensesAdditionalDTO(expensesAdditionalDTO);
            }

            walletApplicationService.quickSend(walletSendDTO);
        }finally {
            redisUtil.unLock(QUICK_SEND_LOCK, quotaSendRequest.getOrganizationId());
        }
        return Response.success();
    }

    @Override
    public Response ceoQuickSend(@Valid QuotaCeoSendRequest quotaCeoSendRequest) {
        log.info("【ceo quick send】request:{}",quotaCeoSendRequest);
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getBusinessGroupCode, quotaCeoSendRequest.getBusinessGroupCode())
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0).eq(SfaBusinessGroupEntity::getStatus, 1)
        );
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("业务组信息获取失败");
        }
        String organizationId = organizationMapper.getOrganizationIdByName(quotaCeoSendRequest.getOrganizationName(), 3, sfaBusinessGroupEntity.getId());
        if(StringUtils.isBlank(organizationId)){
            throw new ApplicationException("组织信息获取失败");
        }

        if(!redisUtil.setLockIfAbsent(QUICK_SEND_LOCK, organizationId, 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try{
            walletApplicationService.ceoQuickSend(quotaCeoSendRequest,organizationId,sfaBusinessGroupEntity,null);
        }finally {
            redisUtil.unLock(QUICK_SEND_LOCK, organizationId);
        }

        return Response.success();
    }

    @Override
    public Response<String> send(String encryption) {
        log.info("【ceo send】encryption:{}",encryption);

        String code = walletApplicationService.send(encryption);

        return Response.success(code);
    }



    @Override
    public Response ceoBatchSend(@Valid CeoBatchSendRequest ceoBatchSendRequest) {
        log.info("【ceo batch send】request:{}",ceoBatchSendRequest);

        if(!redisUtil.setLockIfAbsent(QUICK_BATCH_LOCK, ceoBatchSendRequest.getBatchId(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try{
            List<QuotaCeoSendRequest> quotaSendRequestList = ceoBatchSendRequest.getQuotaSendRequestList();
            quotaSendRequestList.forEach(e -> {
                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                        .eq(SfaBusinessGroupEntity::getBusinessGroupCode, e.getBusinessGroupCode())
                        .eq(SfaBusinessGroupEntity::getDeleteFlag, 0).eq(SfaBusinessGroupEntity::getStatus, 1)
                );
                if(Objects.isNull(sfaBusinessGroupEntity)){
                    throw new ApplicationException("业务组信息获取失败");
                }
                String organizationId = organizationMapper.getOrganizationIdByName(e.getOrganizationName(), 3, sfaBusinessGroupEntity.getId());
                if(StringUtils.isBlank(organizationId)){
                    throw new ApplicationException("组织信息获取失败");
                }

                walletApplicationService.ceoQuickSend(e,organizationId,sfaBusinessGroupEntity,ceoBatchSendRequest.getBatchId());
            });
        }finally {
            redisUtil.unLock(QUICK_BATCH_LOCK, ceoBatchSendRequest.getBatchId());
        }


        return Response.success();
    }

    @Override
    public Response rewardsBatch(RewardsBatchRequest rewardsBatchRequest) {
        log.info("【reward batch】request:{}",JSONObject.toJSONString(rewardsBatchRequest));

        if(!redisUtil.setLockIfAbsent(REWARDS_SEND_LOCK, rewardsBatchRequest.getBatchId().toString(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        try{
            // 批量额度发放
            walletApplicationService.rewardsBatch(rewardsBatchRequest);
        }finally {
            redisUtil.unLock(REWARDS_SEND_LOCK, rewardsBatchRequest.getBatchId().toString());
        }
        return Response.success();
    }

    @Override
    public Response FreezeAccountRequest(@Valid FreezeAccountRequest freezeAccountRequest) {
        log.info("【freeze account】request:{}",freezeAccountRequest);



        walletApplicationService.freezeAccount(freezeAccountRequest);

        return Response.success();
    }

    @Override
    public Response FreezeAccountBatch(@Valid FreezeBatchRequest freezeBatchRequest) {
        log.info("【freeze account batch】req:{}",JSONObject.toJSONString(freezeBatchRequest));

        walletApplicationService.freezeAccountBatch(freezeBatchRequest);

        return Response.success();
    }

    @Override
    public Response<QuotaRetrieveVO> checkRetrieveInfo(String organizationId) {
        log.info("【check retrieve info】organizationId:{}",organizationId);

        QuotaRetrieveVO quotaRetrieveVO = walletSearchService.checkRetrieveInfo(organizationId);

        return Response.success(quotaRetrieveVO);
    }

    @Override
    public Response retrieve(@Valid RetrieveRequest retrieveRequest) {
        log.info("【wallet retrieve】request:{}",retrieveRequest);

        walletApplicationService.retrieve(retrieveRequest);

        return Response.success();
    }

    @Override
    public Response retrieveBatch(@Valid List<RetrieveRequest> retrieveRequests) {
        log.info("【wallet retrieve batch】request:{}",retrieveRequests);

        retrieveRequests.forEach(e -> {
            walletApplicationService.retrieve(e);
        });

        return Response.success();
    }

    @Override
    public Response ceoRevert(@Valid CeoQuotaRevertRequest ceoQuotaRevertRequest) {
        log.info("【ceo revert】request:{}", JSONObject.toJSONString(ceoQuotaRevertRequest));

        if(!redisUtil.setLockIfAbsent(QUICK_REVERT_LOCK, String.valueOf(ceoQuotaRevertRequest.getLogId()), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }
        String msg = null;
        try{
            msg = walletApplicationService.ceoRevert(ceoQuotaRevertRequest);
        }finally{
            redisUtil.unLock(QUICK_REVERT_LOCK,String.valueOf(ceoQuotaRevertRequest.getLogId()));
        }

        return Response.success(msg);
    }

    @Override
    public Response<WalletAgentVO> selectWalletAgent(@Valid WalletAgentSearchRequest walletAgentSearchRequest) {
        log.info("【select wallet agent】request:{}",JSONObject.toJSONString(walletAgentSearchRequest));
        WalletAgentVO walletAgentVO = walletAgentService.selectWalletAgent(walletAgentSearchRequest);
        return Response.success(walletAgentVO);
    }

    @Override
    public void walletAgentExport(@Valid WalletAgentSearchRequest walletAgentSearchRequest) {
        walletAgentService.walletAgentExport(walletAgentSearchRequest);
    }

    @Override
    public Response agentProcess(@Valid AgentProcessRequest agentProcessRequest) {

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(agentProcessRequest.getPerson(), RequestUtils.getLoginInfo());

        walletAgentService.updateAgentStatus(agentProcessRequest.getAgentId(),1,0,StringUtils.EMPTY,personInfo.getEmployeeName());

        return Response.success();
    }

    @Override
    public Response retrieveByMemberKey(@Valid PersonRetrieveRequest personRetrieveRequest) {
        log.info("【retrieve by memberKy】request:{}",JSONObject.toJSONString(personRetrieveRequest));

        if(!redisUtil.setLockIfAbsent(MEMBER_KEY_REVERT_LOCK, String.valueOf(personRetrieveRequest.getMemberKey()), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }


        try {
            walletApplicationService.retrieveByMemberKey(personRetrieveRequest);
        } finally {
            redisUtil.unLock(MEMBER_KEY_REVERT_LOCK,String.valueOf(personRetrieveRequest.getMemberKey()));
        }

        return Response.success();
    }

    @Override
    public Response retrieveByMemberKeyBatch(@Valid List<PersonRetrieveRequest> personRetrieveRequests) {
        log.info("【retrieve by member key batch】request:{}", JSONArray.toJSONString(personRetrieveRequests));

        personRetrieveRequests.forEach(e -> {
            if(!redisUtil.setLockIfAbsent(MEMBER_KEY_REVERT_LOCK, String.valueOf(e.getMemberKey()), 5, TimeUnit.SECONDS)){
                throw new ApplicationException("请求正在处理中！～");
            }

            try {
                walletApplicationService.retrieveByMemberKey(e);
            } finally {
                redisUtil.unLock(MEMBER_KEY_REVERT_LOCK,String.valueOf(e.getMemberKey()));
            }
        });

        return Response.success();
    }

    @Override
    public Response<List<QuotaVO>> searchCeoQuotaDetail(CeoWalletSearchRequest ceoWalletSearchRequest) {
        log.info("【search ceo quota detail】req:{}",JacksonHelper.toJson(ceoWalletSearchRequest,false));

        List<QuotaVO> ceoQuotaDetailVOS = walletSearchService.searchCeoQuotaDetail(ceoWalletSearchRequest);
        return Response.success(ceoQuotaDetailVOS);
    }
}
