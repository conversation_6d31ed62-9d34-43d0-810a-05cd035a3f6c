package com.wantwant.sfa.backend.wangGoldCoin.contriller;

import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.WangGoldCoinApi.api.WangGoldCoinApi;
import com.wantwant.sfa.backend.WangGoldCoinApi.request.WangGoldCoinDetailRequest;
import com.wantwant.sfa.backend.WangGoldCoinApi.request.WangGoldCoinHeaderRequest;
import com.wantwant.sfa.backend.WangGoldCoinApi.vo.WangGoldCoinDetaillVo;
import com.wantwant.sfa.backend.WangGoldCoinApi.vo.WangGoldCoinHeaderVo;
import com.wantwant.sfa.backend.service.WangGoldCoinService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class WangGoldCoinContriller implements WangGoldCoinApi {

  @Autowired private WangGoldCoinService wangGoldCoinService;

  @Override
  public Response<Page<WangGoldCoinHeaderVo>> getWangGoldCoinHeaderList(
      WangGoldCoinHeaderRequest request) {
    Page<WangGoldCoinHeaderVo> wangGoldCoinHeaderList =
        wangGoldCoinService.getWangGoldCoinHeaderList(request);
    return Response.success(wangGoldCoinHeaderList);
  }

  @Override
  public Response<WangGoldCoinDetaillVo> getWangGoldCoinDetailList(
      WangGoldCoinDetailRequest request) {
    WangGoldCoinDetaillVo wangGoldCoinDetailList =
        wangGoldCoinService.getWangGoldCoinDetailList(request);
    return Response.success(wangGoldCoinDetailList);
  }

  @Override
  public Response<Integer> getWangGoldCoinDetailDelete(WangGoldCoinHeaderRequest request) {

    return Response.success(wangGoldCoinService.deleteWangGoldCoin(request));
  }
}
