package com.wantwant.sfa.backend.workCalendar.assemble;

import com.wantwant.sfa.backend.domain.workCalendar.DO.HolidaySearchDO;
import com.wantwant.sfa.backend.workCalendar.request.HolidaySearchRequest;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/03/下午3:38
 */
@Mapper(componentModel = "spring")
public interface WorkCalendarAssemble {

    WorkCalendarAssemble INSTANCE = Mappers.getMapper(WorkCalendarAssemble.class);


    HolidaySearchDO covertToDO(HolidaySearchRequest holidaySearchRequest);
}
