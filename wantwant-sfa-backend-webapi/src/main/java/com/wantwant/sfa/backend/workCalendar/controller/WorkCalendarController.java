package com.wantwant.sfa.backend.workCalendar.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.application.WorkCalendarApplication;
import com.wantwant.sfa.backend.workCalendar.api.WorkCalendarApi;
import com.wantwant.sfa.backend.workCalendar.assemble.WorkCalendarAssemble;
import com.wantwant.sfa.backend.workCalendar.request.HolidaySearchRequest;
import com.wantwant.sfa.backend.workCalendar.vo.HolidayVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/03/下午3:30
 */
@RestController
@Slf4j
public class WorkCalendarController implements WorkCalendarApi {
    @Resource
    private WorkCalendarApplication workCalendarApplication;
    @Resource
    private WorkCalendarAssemble workCalendarAssemble;

    @Override
    public Response<List<HolidayVo>> selectHolidayWithRange(HolidaySearchRequest holidaySearchRequest) {
        log.info("【select holidy  with range】request:{}",holidaySearchRequest);

        List<HolidayVo> holidayVos = workCalendarApplication.selectHolidayWithRange(workCalendarAssemble.covertToDO(holidaySearchRequest));

        return Response.success(holidayVos);
    }
}
