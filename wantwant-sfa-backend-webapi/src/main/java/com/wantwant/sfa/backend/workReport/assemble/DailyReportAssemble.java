package com.wantwant.sfa.backend.workReport.assemble;

import com.wantwant.sfa.backend.policy.controller.assemble.PolicyAssemble;
import com.wantwant.sfa.backend.policy.dto.PolicyDTO;
import com.wantwant.sfa.backend.policy.dto.PolicyRegularDTO;
import com.wantwant.sfa.backend.policy.request.PolicyRequest;
import com.wantwant.sfa.backend.workReport.dto.*;
import com.wantwant.sfa.backend.workReport.request.DailyReportFeedbackRequest;
import com.wantwant.sfa.backend.workReport.request.DailyReportRequest;
import com.wantwant.sfa.backend.workReport.request.DailyReportViewRequest;
import com.wantwant.sfa.backend.workReport.request.DailyVisitCustomerRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/03/05/下午3:59
 */
@Mapper(componentModel = "spring")
public interface DailyReportAssemble {

    DailyReportAssemble INSTANCE = Mappers.getMapper(DailyReportAssemble.class);


    @Mapping(target = "customerList", resultType = DailyVisitCustomerDTO.class)
    @Mapping(target = "issueList", resultType = DailyIssueDTO.class)
    @Mapping(target = "annexList", resultType = DailyAnnexDTO.class)
    DailyReportDTO convertToDTO(DailyReportRequest request);



    DailyReportFeedbackDTO convertToDTO(DailyReportFeedbackRequest request);


    DailyReportViewDTO convertToDTO(DailyReportViewRequest request);
}
