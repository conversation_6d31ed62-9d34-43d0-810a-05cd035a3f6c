package com.wantwant.sfa.backend.workReport.assemble;

import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.workReport.dto.WorkReportBuildDTO;
import com.wantwant.sfa.backend.workReport.dto.WorkReportModifyDTO;
import com.wantwant.sfa.backend.workReport.request.WorkReportBuildRequest;
import com.wantwant.sfa.backend.workReport.request.WorkReportModifyRequest;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/06/下午5:02
 */
public class WorkReportAssemble {


    public static WorkReportBuildDTO buildAssemble(WorkReportBuildRequest workReportBuildRequest) {
        WorkReportBuildDTO workReportBuildDTO = new WorkReportBuildDTO();
        BeanUtils.copyProperties(workReportBuildRequest,workReportBuildDTO);
        return workReportBuildDTO;
    }

    public static WorkReportModifyDTO modifyAssemble(WorkReportModifyRequest workReportModifyRequest) {
        WorkReportModifyDTO workReportModifyDTO = new WorkReportModifyDTO();
        BeanUtils.copyProperties(workReportModifyRequest,workReportModifyDTO);
        return workReportModifyDTO;
    }
}
