package com.wantwant.sfa.backend.workReport.controller;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.workReport.api.DailyReportAPI;
import com.wantwant.sfa.backend.workReport.assemble.DailyReportAssemble;
import com.wantwant.sfa.backend.workReport.assemble.WorkReportAssemble;
import com.wantwant.sfa.backend.workReport.request.DailyReportFeedbackRequest;
import com.wantwant.sfa.backend.workReport.request.DailyReportRequest;
import com.wantwant.sfa.backend.workReport.request.DailyReportViewRequest;
import com.wantwant.sfa.backend.workReport.request.SubmitSearchRequest;
import com.wantwant.sfa.backend.workReport.service.IDailyReportSearchService;
import com.wantwant.sfa.backend.workReport.service.IDailyReportService;
import com.wantwant.sfa.backend.workReport.vo.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/03/05/下午3:58
 */
@RestController
@Slf4j
public class DailyReportController implements DailyReportAPI {

    @Autowired
    private IDailyReportService dailyReportService;
    @Autowired
    private DailyReportAssemble dailyReportAssemble;
    @Autowired
    private IDailyReportSearchService dailyReportSearchService;
    @Resource
    private RabbitMQSender rabbitMQSender;

    private String BUILD_WORK_REPORT_LOCK = "sfa:dailyReport:build";

    private String REVIEW_WORK_REPORT_LOCK = "sfa:dailyReport:review";


    @Resource
    private RedisUtil redisUtil;
    @Override
    public Response save(@Valid DailyReportRequest dailyReportRequest) {
        log.info("【save daily report】request:{}",dailyReportRequest);


        if(!redisUtil.setLockIfAbsent(BUILD_WORK_REPORT_LOCK, dailyReportRequest.getOrganizationId(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            dailyReportService.save(dailyReportAssemble.convertToDTO(dailyReportRequest));
        }finally {
            redisUtil.unLock(BUILD_WORK_REPORT_LOCK,dailyReportRequest.getOrganizationId());
        }


        if (Objects.nonNull(dailyReportRequest.getIsSubmit()) && dailyReportRequest.getIsSubmit() == 1) {
            try {
                JSONObject obj = new JSONObject();
                obj.put("nodeCode", "SUBMIT_DAILY_REPORT");
                obj.put("person", dailyReportRequest.getPerson());
                obj.put("organizationCode", dailyReportRequest.getOrganizationId());
                rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
            } catch (Exception ex) {
                log.error("dailyReportSave 发送MQ失败", ex);
            }
        }

        return Response.success();
    }

    @Override
    public Response<List<CustomerVo>> getCustomer(String organizationId, String customerName) {
        log.info("【get customer】organizationId:{}.customerName:{}",organizationId,customerName);

        List<CustomerVo> list = dailyReportSearchService.getCustomer(organizationId,customerName);

        return Response.success(list);
    }

    @Override
    public Response review(@Valid DailyReportViewRequest dailyReportViewRequest) {
        log.info("【daily report review】request:{}",dailyReportViewRequest);

        if(!redisUtil.setLockIfAbsent(REVIEW_WORK_REPORT_LOCK, dailyReportViewRequest.getPerson(), 5, TimeUnit.SECONDS)){
            return Response.error("请求正在处理中！～");
        }

        try {
            dailyReportService.review(dailyReportAssemble.convertToDTO(dailyReportViewRequest));
        }finally {
            redisUtil.unLock(REVIEW_WORK_REPORT_LOCK,dailyReportViewRequest.getPerson());
        }

        return Response.success();
    }

    @Override
    public Response feedback(@Valid DailyReportFeedbackRequest dailyReportFeedbackRequest) {
        log.info("【daily report feedback】request:{}",dailyReportFeedbackRequest);
        dailyReportService.feedback(dailyReportAssemble.convertToDTO(dailyReportFeedbackRequest));
        return Response.success();
    }

    @Override
    public Response<DailyReportOrderVo> getOrderInformation(String organizationId,String day) {
        log.info("【get daily report order information】organizationId:{},day:{}",organizationId,day);
        DailyReportOrderVo dailyReportOrderVo = dailyReportSearchService.getOrderInformation(organizationId,day);
        return Response.success(dailyReportOrderVo);
    }

    @Override
    public Response<DailyReportDetailVo> getDailyReportDetail(Long reportId, String person) {
        log.info("【get daily report detail】reportId:{}",reportId);
        DailyReportDetailVo vo = dailyReportSearchService.getDailyReportDetail(reportId,person);
        return Response.success(vo);
    }

    @ApiOperation(value = "获取客户情况", notes = "获取客户情况")
    @Override
    public Response<CustomerSituationVo> getCustomerSituation(String organizationId,String day) {
        log.info("【get daily report customer situation】organizationId:{},day:{}",organizationId,day);

        CustomerSituationVo customerSituationVo = dailyReportSearchService.getCustomerSituation(organizationId,day);

        return Response.success(customerSituationVo);
    }

    @ApiOperation(value = "获取拜访情况", notes = "获取拜访情况")
    @Override
    public Response<CustomerVisitSituationVo> getVisitInfo(String employeeId, String organizationId, String startDay,String endDay) {
        log.info("【get daily report customer visit info】organizationId:{},startDay:{},endDay:{}",organizationId,startDay,endDay);


        CustomerVisitSituationVo customerVisitSituationVo = dailyReportSearchService.getVisitInfo(employeeId,organizationId,startDay,endDay);

        return Response.success(customerVisitSituationVo);
    }

    @Override
    public Response<List<String>> getSubmitDate(SubmitSearchRequest submitSearchRequest) {
        log.info("get submit date request:{}", submitSearchRequest);

        List<String> submitDateList = dailyReportSearchService.getSubmitDate(submitSearchRequest);
        return Response.success(submitDateList);
    }
}
