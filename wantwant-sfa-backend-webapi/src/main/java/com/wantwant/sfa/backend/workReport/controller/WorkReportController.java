package com.wantwant.sfa.backend.workReport.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.workReport.api.WorkReportAPI;
import com.wantwant.sfa.backend.workReport.assemble.WorkReportAssemble;
import com.wantwant.sfa.backend.workReport.request.*;
import com.wantwant.sfa.backend.workReport.service.IDailyReportSearchService;
import com.wantwant.sfa.backend.workReport.service.IWorkReportProcessService;
import com.wantwant.sfa.backend.workReport.service.IWorkReportStaticsService;
import com.wantwant.sfa.backend.workReport.vo.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/06/下午2:47
 */
@RestController
@Slf4j
public class WorkReportController implements WorkReportAPI {
    @Autowired
    private IWorkReportProcessService workReportProcessService;
    @Autowired
    private IWorkReportStaticsService workReportStaticsService;
    @Autowired
    private IDailyReportSearchService dailyReportSearchService;
    @Resource
    private RabbitMQSender rabbitMQSender;

    private String BUILD_WORK_REPORT_LOCK = "sfa:workReport:build";
    @Resource
    private RedisUtil redisUtil;

    @Override
    public Response<List<WorkReportPeriodVO>> selectWorkPeriod(String person, int workType,String organizationId) {
        log.info("【select work period】empId:{},workType:{}",person,workType);

        List<WorkReportPeriodVO> list = workReportProcessService.selectWorkPeriod(person,workType,organizationId);
        return Response.success(list);
    }

    @Override
    public Response<WorkReportPeriodDetailVO> selectWorkPeriodDetail(PeriodRequest request) {
        log.info("【selectWorkPeriodDetail】person:{},workId:{},organizationId:{}",request.getPerson(),request.getWorkId(),request.getOrganizationId());
        WorkReportPeriodDetailVO detail = workReportProcessService.selectWorkPeriodDetail(request);
        return Response.success(detail);
    }

    @Override
    public Response build(WorkReportBuildRequest workReportBuildRequest) {
        log.info("【work report build】request:{}",workReportBuildRequest);


        workReportProcessService.build(WorkReportAssemble.buildAssemble(workReportBuildRequest));

        if (Objects.nonNull(workReportBuildRequest.getIsSubmit()) && workReportBuildRequest.getIsSubmit() == 1) {
            try {
                JSONObject obj = new JSONObject();
                obj.put("nodeCode", "SUBMIT_WEEKLY_REPORT");
                obj.put("person", workReportBuildRequest.getPerson());
                obj.put("organizationCode", workReportBuildRequest.getOrganizationId());
                obj.put("businessId", workReportBuildRequest.getWorkId());
                rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
            } catch (Exception ex) {
                log.error("workReportBuild 发送MQ失败", ex);
            }
        }
        return Response.success();
    }

    @Override
    public Response modify(WorkReportModifyRequest workReportModifyRequest) {
        log.info("【work report modify】request:{}",workReportModifyRequest);

        workReportProcessService.update(WorkReportAssemble.modifyAssemble(workReportModifyRequest));

        return Response.success();
    }

    @Override
    public Response<WorkReportPermissionVO> getPermission(String person) {
        log.info("【work report permission】person:{}",person);

        WorkReportPermissionVO workReportPermissionVO =  workReportProcessService.getPermission(person);

        return Response.success(workReportPermissionVO);
    }

    @Override
    public Response<Page<WorkReportVo>> selectWorkReport(WorkReportSearchRequest workReportSearchRequest) {
        log.info("【select work report】request:{}",workReportSearchRequest);
        Integer reportType = workReportSearchRequest.getReportType();

        Page<WorkReportVo> page = null;
        if(reportType == 1){
            page = workReportProcessService.selectWorkReport(workReportSearchRequest);
        }else{
            page = dailyReportSearchService.selectDailyReport(workReportSearchRequest);
        }


        return Response.success(page);
    }

    @Override
    public void selectWorkReportExport(WorkReportSearchRequest workReportSearchRequest) {
        log.info("【select work report export】request:{}",workReportSearchRequest);
        Integer reportType = workReportSearchRequest.getReportType();
        if(reportType == 1){
            workReportProcessService.export(workReportSearchRequest);
        }else if(reportType == 2){
            dailyReportSearchService.export(workReportSearchRequest);
        }

    }

    @Override
    public Response<WorkReportHeaderVo> getHeader(Long taskId,String person) {
        log.info("【get work report header】taskId:{}",taskId);

        WorkReportHeaderVo vo = workReportProcessService.getHeader(taskId,person);

        return Response.success(vo);
    }

    @Override
    public Response<List<WorkReportCalendarVo>> getCalendar(Long taskId,String person,String yearMonth) {
        log.info("【get work report calendar】taskId:{}",taskId);

        List<WorkReportCalendarVo> list = workReportProcessService.getCalendar(taskId,person,yearMonth);

        return Response.success(list);
    }

    @Override
    public Response<WorkReportDataVO> getData(Long taskId,String person) {
        log.info("【work report data】taskId:{}",taskId);

        WorkReportDataVO vo = workReportProcessService.getData(taskId,person);
        return Response.success(vo);
    }

    @Override
    public Response read(WorkReportInspectRequest request) {
        log.info("【work report data】inspect:{}",request);
        workReportProcessService.read(request);
        return Response.success();
    }

    @Override
    public Response revert(WorkReportRevertRequest workReportRevertRequest) {
        log.info("【work report revert】request:{}",workReportRevertRequest);

        workReportProcessService.revert(workReportRevertRequest);

        return Response.success();
    }

    @Override
    public Response delete(WorkReportRevertRequest workReportRevertRequest) {
        log.info("【work report delete】request:{}",workReportRevertRequest);

        workReportProcessService.delete(workReportRevertRequest);

        return Response.success();
    }

    @Override
    public Response<List<WorkReportStaticsVO>> statics(WorkReportStaticsRequest workReportStaticsRequest) {
        log.info("【work report statics】request:{}",workReportStaticsRequest);

        List<WorkReportStaticsVO> list = workReportStaticsService.statics(workReportStaticsRequest);
        return Response.success(list);
    }

    @Override
    public Response review(WorkReportRevertRequest workReportRevertRequest) {
        log.info("【work report review】request:{}",workReportRevertRequest);
        workReportProcessService.review(workReportRevertRequest);
        return Response.success();
    }

    @Override
    public Response<WorkReportReferDataVo> refer(Long workId, String organizationId) {
        log.info("【work report refer】workId:{},organizationId:{}",workId,organizationId);

        WorkReportReferDataVo workReportReferDataVo = workReportProcessService.refer(workId,organizationId);

        return Response.success(workReportReferDataVo);
    }

    @Override
    public void staticsExport(WorkReportStaticsRequest workReportRevertRequest) {
        log.info("【work report statics export】request:{}",workReportRevertRequest);
        workReportStaticsService.export(workReportRevertRequest);
    }

    @Override
    public Response<WorkReportMonitorVo> monitor(WorkReportMonitorRequest request) {
        log.info("【work report monitor】request:{}",request);
        return Response.success(workReportProcessService.monitor(request));
    }

    @Override
    public Response feedback(WorkReportFeedbackRequest workReportFeedbackRequest) {
        log.info("【work report feedback】request:{}",workReportFeedbackRequest);
        workReportProcessService.feedback(workReportFeedbackRequest);
        return Response.success();
    }

    @Override
    public Response<List<WeeklyCompletionDetailVO>> weeklyCompletion(PeriodRequest request) {
        return Response.success(workReportProcessService.weeklyCompletion(request));
    }

    @Override
    public Response<List<WeeklyReportInfoVO>> weeklyReportInfo(PeriodRequest request) {
        return Response.success(workReportProcessService.weeklyReportInfo(request));
    }

    @Override
    public Response<List<WeeklyReportInfoVO>> reviewReportInfo(ReviewPeriodRequest request) {
        return Response.success(workReportProcessService.reviewReportInfo(request));
    }

    @Override
    public Response<YearSameVO> reviewYearSame(ReviewYearSameRequest request) {
        log.info("【月报年节同期分析】request:{}",request);
        return Response.success(workReportProcessService.reviewYearSame(request));
    }

    @Override
    public Response<List<OrganizationEstimateVo>> nextEstimateInfo(String sku, String organizationId,Long workId) {
        log.info("【get next estimate info】sku:{},organizationId:{},workId:{}",sku,organizationId,workId);
        List<OrganizationEstimateVo> list = workReportProcessService.nextEstimateInfo(sku,organizationId,workId);
        return Response.success(list);
    }

    @Override
    public Response<List<PeriodVo>> getPeriodSelect(String yearMonth) {

        List<PeriodVo> list = workReportStaticsService.getPeriodSelect(yearMonth);

        return Response.success(list);
    }

    @Override
    public Response<Long> getNearestWeekReport(String organizationId) {
        return Response.success(workReportProcessService.getNearestWeekReport(organizationId));
    }

    @ApiOperation(value = "获取陈列照片列表", notes = "获取陈列照片列表")
    @Override
    public Response<List<WorkReportImageVO>> queryImageList(@RequestBody WorkReportImageRequest request) {
        return Response.success(workReportProcessService.queryImageList(request));
    }

    @ApiOperation(value = "线上照片列表", notes = "线上照片列表")
    @Override
    public Response<IPage<WorkReportDisplayImageVO>> queryDisplayImageList(@RequestBody WorkReportImageRequest request) {
        return Response.success(workReportProcessService.queryDisplayImageList(request));
    }
    @ApiOperation(value = "选择线上照片", notes = "选择线上照片")
    @Override
    public Response commitDisplayImage(WorkReportDisplayImageCommitRequest request) {
        workReportProcessService.commitDisplayImage(request);
        return Response.success();
    }
    @ApiOperation(value = "上传本地照片", notes = "上传本地照片")
    @Override
    public Response commitLocalImage(WorkReportLocalImageCommitRequest request) {
        workReportProcessService.commitLocalImage(request);
        return Response.success();
    }

    @ApiOperation(value = "删除陈列照片", notes = "删除陈列照片")
    @Override
    public Response deleteImage(WorkReportLocalImageDeleteRequest request) {
        workReportProcessService.deleteImage(request);
        return Response.success();
    }

}
