package com.wantwant.sfa.backend.yearFestival.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.yearFestival.api.YearFestivalApi;
import com.wantwant.sfa.backend.yearFestival.request.YearFestivalRequest;
import com.wantwant.sfa.backend.yearFestival.service.YearFestivalService;
import com.wantwant.sfa.backend.yearFestival.vo.YearCustomerVo;
import com.wantwant.sfa.backend.yearFestival.vo.YearFestivalDetailVo;
import com.wantwant.sfa.backend.yearFestival.vo.YearProductOptions;
import com.wantwant.sfa.backend.yearFestival.vo.YearProductVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@Slf4j
public class YearFestivalController implements YearFestivalApi {

    @Autowired
    private YearFestivalService yearFestivalService;


    @ApiOperation(value = "业绩总览", notes = "个人信息、业绩数据、每日&累计业绩趋势")
    @Override
    public Response<YearFestivalDetailVo> yearFestivalDetail(YearFestivalRequest request) {
        return Response.success(yearFestivalService.yearFestivalDetail(request));
    }

    @ApiOperation(value = "业务数据明细-下拉模式", notes = "业务数据明细-下拉模式")
    @Override
    public Response<List<YearFestivalDetailVo>> queryPerformanceList(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryPerformanceList(request));
    }

    @ApiOperation(value = "业务数据明细-下拉模式-列表下载", notes = "业务数据明细-下拉模式-列表下载")
    @Override
    public void downLoadPerformanceList(@RequestBody YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        yearFestivalService.downLoadPerformanceList(request, req, res);
    }

    @ApiOperation(value = "业务数据明细-平铺模式（分页）", notes = "业务数据明细-平铺模式（分页）")
    @Override
    public Response<IPage<YearFestivalDetailVo>> queryPerformancePage(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryPerformancePage(request));
    }

    @ApiOperation(value = "业务数据明细-平铺模式（分页）-列表下载", notes = "业务数据明细-平铺模式（分页）-列表下载")
    @Override
    public void downLoadPerformancePage(@RequestBody YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        yearFestivalService.downLoadPerformancePage(request, req, res);
    }

    @ApiOperation(value = "产品列表", notes = "sku、sku、产品线")
    @Override
    public Response<IPage<YearProductVo>> queryProductPage(YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryProductPage(request));
    }

    @ApiOperation(value = "产品列表-列表下载", notes = "sku、sku、产品线-列表下载")
    @Override
    public void downLoadProductPage(@RequestBody YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        yearFestivalService.downLoadProductPage(request, req, res);
    }

    @ApiOperation(value = "组织交易客户明细-下拉模式", notes = "组织交易客户明细-下拉模式")
    @Override
    public Response<List<YearCustomerVo>> queryCustomerList(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryCustomerList(request));
    }

    @ApiOperation(value = "组织交易客户明细-下拉模式（分页）", notes = "组织交易客户明细-下拉模式（分页）")
    @Override
    public Response<IPage<YearCustomerVo>> queryCustomerListPage(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryCustomerListPage(request));
    }

    @ApiOperation(value = "组织交易客户明细-下拉模式-列表下载", notes = "组织交易客户明细-下拉模式-列表下载")
    @Override
    public void downLoadCustomerList(@RequestBody YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        yearFestivalService.downLoadCustomerList(request, req, res);
    }

    @ApiOperation(value = "组织交易客户明细-平铺模式（分页）", notes = "组织交易客户明细-平铺模式（分页）")
    @Override
    public Response<IPage<YearCustomerVo>> queryCustomerPage(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryCustomerPage(request));
    }

    @ApiOperation(value = "组织交易客户明细-平铺模式（分页）-列表下载", notes = "组织交易客户明细-平铺模式（分页）-列表下载")
    @Override
    public void downLoadCustomerPage(@RequestBody YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        yearFestivalService.downLoadCustomerPage(request, req, res);
    }

    @ApiOperation(value = "产品交易客户明细列表", notes = "sku、sku、产品线")
    @Override
    public Response<IPage<YearCustomerVo>> queryProductCustomerPage(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.queryProductCustomerPage(request));
    }

    @ApiOperation(value = "产品交易客户明细列表-列表下载", notes = "产品交易客户明细列表-列表下载")
    @Override
    public void downLoadProductCustomerPage(@RequestBody YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        yearFestivalService.downLoadProductCustomerPage(request, req, res);
    }

    @ApiOperation(value = "产品下拉选项", notes = "产品下拉选项")
    @Override
    public Response<List<YearProductOptions>> productOptions(@RequestBody YearFestivalRequest request) {
        return Response.success(yearFestivalService.productOptions(request));
    }

}
