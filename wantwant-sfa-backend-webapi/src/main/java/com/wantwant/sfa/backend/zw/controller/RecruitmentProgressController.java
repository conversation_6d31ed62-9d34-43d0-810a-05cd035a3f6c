package com.wantwant.sfa.backend.zw.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.service.IRealtimeDataService;
import com.wantwant.sfa.backend.service.RecruitmentNeedsService;
import com.wantwant.sfa.backend.service.RecruitmentProgressService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.zw.request.*;
import com.wantwant.sfa.backend.zw.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 招聘进度
 *
 * <AUTHOR>
 * @date 3/9/22 3:41 PM
 * @version 1.0
 */
@Slf4j
@Api(tags = "招聘进度")
@RestController
@RequestMapping("/recruitment")
public class RecruitmentProgressController {

    @Autowired
    private RecruitmentProgressService service;

    @Autowired
    private RecruitmentNeedsService needsService;

    @Autowired
    private IRealtimeDataService realtimeDataService;

    /**
     * 招聘渠道(下拉框)
     *
     * @date 3/10/22 10:46 AM
     * @version 1.0
     */
    @ApiOperation(value = "招聘渠道(下拉框)", notes = "招聘渠道(下拉框)")
    @GetMapping(value = "/getChannel")
    public Response<List<ChannelVO>> getChannel(){
        return Response.success(service.getChannel());
    }

    /**
     * 根据组织获取三级地(下拉框)
     *
     * @param companyOrganizationId
     * @param departmentCode
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.zw.vo.AreaVO>>
     * @date: 3/14/22 10:46 PM
     */
    @ApiOperation(value = "根据组织获取三级地(下拉框)", notes = "根据组织获取三级地(下拉框)")
    @GetMapping(value = "/getAreaByOrganization")
    public Response<List<AreaTreeVO>> getAreaByOrganization(String companyOrganizationId,
                                                              String departmentId){
        return Response.success(service.getAreaByOrganization(companyOrganizationId,departmentId));
    }

    /**
     * 招聘渠道列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.customer.vo.CustomerApproveVO>>
     * @date: 2022-02-19 13:14
     */
    @ApiOperation(value = "招聘渠道列表", notes = "招聘渠道列表")
    @GetMapping(value = "/queryChannelByList")
    public Response<List<RecruitmentChannelVO>> queryChannelByList(RecruitmentChannelRequest request) {
        return Response.success(service.queryChannelByList(request));
    }

    /**
     * 招聘需求列表
     * 分公司市场范围，招聘需求列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.zw.vo.RecruitmentNeedsInfoVO>
     * @date: 3/11/22 11:08 AM
     */
    @ApiOperation(value = "招聘需求列表", notes = "招聘需求列表")
    @GetMapping(value = "/queryNeeds")
    public Response<RecruitmentNeedsInfoVO> queryNeeds(CompanyScopeRequest request) {
        return Response.success(needsService.queryNeeds(request));
    }

    /**
     * 根据ID获取需求
     *
     * @param id
     * @return: com.wantwant.sfa.backend.zw.vo.RecruitmentNeedsInfoVO
     * @date: 3/15/22 9:57 AM
     */
    @ApiOperation(value = "根据ID获取需求")
    @GetMapping(value = "/getNeedById/{id}")
    public Response<RecruitmentNeedsVO> getNeedById(@ApiParam(value = "id", required = true) @PathVariable("id") @NotNull(message = "id不能为空") Integer id) {
        return Response.success(needsService.getNeedById(id));
    }

    /**
     * 新增招聘需求
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 3/10/22 7:46 PM
     */
    @ApiOperation(value = "新增招聘需求", notes = "新增招聘需求")
    @PostMapping(value = "/addNeeds")
    public Response<Integer> addNeeds(@Valid @RequestBody RecruitmentNeedsRequest request) {
        if(CommonUtil.StringUtils.isBlank(request.getCompanyOrganizationId())){
            throw new ApplicationException("请选分公司");
        }
        return Response.success(needsService.addNeeds(request));
    }

    /**
     * 修改招聘需求
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.lang.Integer>
     * @date: 3/10/22 8:25 PM
     */
    @ApiOperation(value = "修改招聘需求", notes = "修改招聘需求")
    @PostMapping(value = "/updateNeeds")
    public Response<Integer> updateNeeds(@Valid @RequestBody RecruitmentNeedsRequest request) {
        return Response.success(needsService.updateNeeds(request.getId(),request));
    }

    /**
     * 招聘进度
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<com.wantwant.sfa.backend.zw.vo.RecruitmentProgressVO>
     * @date: 3/22/22 10:02 AM
     */
    @ApiOperation(value = "招聘进度", notes = "招聘进度")
    @PostMapping (value = "/queryProgress")
    public Response<RecruitmentProgressVO> queryProgress(@RequestBody RecruitmentProgressRequest request){
        return Response.success(realtimeDataService.queryProgress(request));
    }

    /**
     * 招聘进度导出
     *
     * @date 5/5/22 2:47 PM
     * @version 1.0
     */
    @ApiOperation(value = "招聘进度导出", notes = "招聘进度导出")
    @PostMapping(value = "/exportProgress")
    public void exportProgress(@RequestBody RecruitmentProgress1Request request){
        realtimeDataService.exportProgress(request);
    }

    /**
     * 离职进度列表
     *
     * @param request
     * @return: com.wantwant.commons.web.response.Response<java.util.List<com.wantwant.sfa.backend.zw.vo.ResignVO>>
     * @date: 3/11/22 4:34 PM
     */
    @ApiOperation(value = "离职进度列表", notes = "离职进度列表")
    @PostMapping(value = "/queryResign")
    public Response<List<ResignVO>> queryResign(@RequestBody ResignRequest request) {
        return Response.success(service.queryResign(request));
    }

    /**
     * 离职进度导出
     *
     * @param request
     * @return: void
     * @date: 5/5/22 8:13 PM
     */
    @ApiOperation(value = "离职进度导出", notes = "离职进度导出")
    @PostMapping(value = "/exportResign")
    public void exportResign(@RequestBody ResignRequest request){
        service.exportResign(request);
    }

}
