package com.wantwant.sfa.backend.zw.controller;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.interview.service.AuditService;
import com.wantwant.sfa.backend.zw.api.ZWAuditApi;
import com.wantwant.sfa.backend.zw.request.ZWAuditRequest;
import com.wantwant.sfa.backend.zw.vo.ZWAuditVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/08/09/上午10:39
 */
@RestController
public class ZWAuditController implements ZWAuditApi {
    @Autowired
    private AuditService auditService;


    @Override
    public Response<List<ZWAuditVo>> selectAudit(@Valid ZWAuditRequest request) {

        List<ZWAuditVo> list = auditService.selectAudit(request);

        return Response.success(list);
    }
}
