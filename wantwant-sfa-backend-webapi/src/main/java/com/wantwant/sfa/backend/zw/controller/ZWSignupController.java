package com.wantwant.sfa.backend.zw.controller;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.applyMember.dto.ResignApprovalQueryDTO;
import com.wantwant.sfa.backend.applyMember.vo.ApplyApprovalVO;
import com.wantwant.sfa.backend.interview.service.AuditService;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.service.ApplyMemberService;
import com.wantwant.sfa.backend.service.IZWSignupService;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.zw.api.ZWSignApi;
import com.wantwant.sfa.backend.zw.request.*;
import com.wantwant.sfa.backend.zw.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @Description: @Auther: zhouming @Date: 2021/10/23/下午1:08
 */
@RestController
@Slf4j
public class ZWSignupController implements ZWSignApi {
    @Autowired
    IZWSignupService izwSignupService;

    @Autowired
    SettingServiceImpl settingServiceImpl;

    @Autowired
    private ApplyMemberService applyMemberService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private AuditService auditService;

    @Override
    public Response signup(ZWSignRequest request) {
        try {
            izwSignupService.singup(request);
        } catch (ApplicationException e) {
            return Response.error(e.getMessage());
        }
        return Response.success();
    }

//    @Override
//    public Response<ZWSignupInfoVo> getSignupInfo(int id) {
//        return Response.success(izwSignupService.getSignupInfo(id));
//    }

    @Override
    public Response repairSmallMarketPositionRelation(MultipartFile file, String employeeId) {
        List<String> upload = izwSignupService.repair(file, employeeId);
        return Response.success(upload);
    }

    @Override
    public Response<List<ZWOverallInfoVo>> zwOverallList(ZWEmployeeInfoRequest request) {
        List<ZWOverallInfoVo> zwOverallInfoVoPage = izwSignupService.zwOverallList(request);
        return Response.success(zwOverallInfoVoPage);
    }

    @Override
    public Response<Page<ZWDetailedInfoVo>> zwDetailedList(ZWDetailedInfoRequest request) {
        Page<ZWDetailedInfoVo> zwDetailednfoVoPage = izwSignupService.zwDetailedList(request);
        return Response.success(zwDetailednfoVoPage);
    }


    @Override
    public Response<Page<ZWEnrollmentApplicationAndReviewInfoVo>>
    zwEnrollmentApplicationAndReviewList(ZWDetailedInfoRequest request) {
        Page<ZWEnrollmentApplicationAndReviewInfoVo> zwEnrollmentApplicationAndReviewInfoPage =
                izwSignupService.zwEnrollmentApplicationAndReviewList(request);



        return Response.success(zwEnrollmentApplicationAndReviewInfoPage);
    }
    
    @Override
    public Response<List<ZWEmployeeInfoVo>> zwInterviewEmployeeList(
            @RequestBody ZWEmployeeInfoRequest request) {
        log.info("ZWSignupController zwInterviewEmployeeList: {}", request);
        if (request.getType() != 1 && request.getType() != 2) {
            throw new ApplicationException("面试类型传入错误");
        }
        return Response.success(izwSignupService.getZWInterviewList(request));
    }

    @Override
    public Response<List<Map<String, Object>>> zwSourceList() {
        log.info("ZWSignupController zwSourceList");
        String sourceStr = settingServiceImpl.getValue("zw_source");
        if (sourceStr == null || sourceStr.length() == 0) {
            sourceStr = "员工推荐,第三方外包,自主报名,合伙人推荐";
        }
        String[] sourceList = sourceStr.split(",");
        List<Map<String, Object>> retList = new LinkedList<>();
        Map tempMap = null;
        for (int i = 0; i < sourceList.length; i++) {
            Map map = new HashMap();
            map.put("value", i + 1);
            map.put("label", sourceList[i]);
            retList.add(map);
            if ("第三方外包".equals(sourceList[i])) {
                tempMap = map;
            }
        }

        sourceStr = settingServiceImpl.getValue("zw_ectocyst_company");
        if (sourceStr == null || sourceStr.length() == 0) {
            sourceStr = "睿秉,英格瑪,卡思,旺旺,魔方,易才,華服,大瀚,安可人力,人瑞,万宝盛华,斗米,杰博,CDP";
        }
        List<Map<String, String>> children = new LinkedList<>();
        String[] companyList = sourceStr.split(",");
        for (String company : companyList) {
            Map map = new HashMap();
            map.put("value", company);
            map.put("label", company);
            children.add(map);
        }

        if (tempMap != null) {
            tempMap.put("children", children);
        }
        return Response.success(retList);
    }

    @Override
    public Response<Map<String, Integer>> pendingRedPointCount(@RequestParam("empId") String empId) {
        log.info("start pendingRedPointCount ,empId:{}", empId);

        Map<String,Integer> map = auditService.pendingRedPointCount(empId);


        return Response.success(map);
    }

    @Override
    public Response<ZWHumanResources> getSignupInfo(@RequestBody ZWHumanResources request) {
        ZWHumanResources zwHumanResources = new ZWHumanResources();
        zwHumanResources.setType(1);
        return Response.success(zwHumanResources);
    }

    @Override
    public Response<List<MarketVisitVo>> marketVisit(MarketVisitRequest marketVisitRequest) {
        List<MarketVisitVo> list = izwSignupService.marketVisit(marketVisitRequest);
        return Response.success(list);
    }
}
