package com.wantwant.sfa.backend.businessBd;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.domain.emp.DO.BusinessBDRuleDO;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.repository.persistence.BusinessBDRepository;
import com.wantwant.sfa.backend.domain.emp.service.impl.BusinessBDRuleService;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 业务BD规则导入测试类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RunWith(MockitoJUnitRunner.class)
public class BusinessBDRuleImportTest {

    @Mock
    private OrganizationMapper organizationMapper;

    @Mock
    private BusinessBDRepository businessBDRepository;

    @InjectMocks
    private BusinessBDRuleService businessBDRuleService;

    private ProcessUserDO processUserDO;
    private BusinessBDRuleDO businessBDRuleDO;

    @Before
    public void setUp() {
        // 初始化处理人信息
        processUserDO = new ProcessUserDO();
        processUserDO.setEmployeeId("EMP001");
        processUserDO.setEmployeeName("测试员工");

        // 初始化业务BD规则数据
        businessBDRuleDO = new BusinessBDRuleDO();
        businessBDRuleDO.setCompanyName("测试公司");
        businessBDRuleDO.setTheYearMonth("2024-12");
        businessBDRuleDO.setStartYearMonth("2024-01");
        businessBDRuleDO.setFullTimePerformanceRequire(new BigDecimal("100"));
        businessBDRuleDO.setContractPerformanceRequire(new BigDecimal("80"));

        // 通过反射设置静态字段，模拟业务组
        mockStaticBusinessGroup();
    }

    /**
     * 测试正常导入流程 - 验证删除和插入操作的调用
     */
    @Test
    public void testImportRule_NormalFlow_VerifyDeleteAndInsertCalls() {
        // Given
        List<BusinessBDRuleDO> ruleList = Collections.singletonList(businessBDRuleDO);
        String expectedOrgCode = "ORG001";

        // Mock organization查询
        when(organizationMapper.getOrganizationIdByName("测试公司", 3, 1))
            .thenReturn(expectedOrgCode);
        when(organizationMapper.getOrganizationType(expectedOrgCode))
            .thenReturn("company");

        // 使用ArgumentCaptor捕获调用参数
        ArgumentCaptor<String> deleteYearMonthCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> deleteOrgCodeCaptor = ArgumentCaptor.forClass(String.class);

        // When
        businessBDRuleService.importRule(ruleList, processUserDO);

        // Then - 验证删除操作被调用且参数正确
        verify(businessBDRepository).deleteLastRule(deleteYearMonthCaptor.capture(), deleteOrgCodeCaptor.capture());
        assertEquals("删除操作的月份参数应该正确", "2024-12", deleteYearMonthCaptor.getValue());
        assertEquals("删除操作的组织代码参数应该正确", expectedOrgCode, deleteOrgCodeCaptor.getValue());

        // Then - 验证插入操作被调用
        verify(businessBDRepository).insert(any());

        // 验证调用顺序：先删除后插入
        org.mockito.InOrder inOrder = inOrder(businessBDRepository);
        inOrder.verify(businessBDRepository).deleteLastRule(anyString(), anyString());
        inOrder.verify(businessBDRepository).insert(any());
    }

    /**
     * 测试多个组织的重复数据处理 - 验证每个组织都会删除历史数据
     */
    @Test
    public void testImportRule_MultipleOrganizations_VerifyEachDeleteAndInsert() {
        // Given - 两个不同组织的数据
        BusinessBDRuleDO rule1 = createBusinessBDRuleDO("公司A", "2024-12");
        BusinessBDRuleDO rule2 = createBusinessBDRuleDO("公司B", "2024-12");
        List<BusinessBDRuleDO> ruleList = Arrays.asList(rule1, rule2);

        // Mock 组织查询
        when(organizationMapper.getOrganizationIdByName("公司A", 3, 1)).thenReturn("ORG_A");
        when(organizationMapper.getOrganizationIdByName("公司B", 3, 1)).thenReturn("ORG_B");
        when(organizationMapper.getOrganizationType("ORG_A")).thenReturn("company");
        when(organizationMapper.getOrganizationType("ORG_B")).thenReturn("company");

        // When
        businessBDRuleService.importRule(ruleList, processUserDO);

        // Then - 验证每个组织都执行了删除操作
        verify(businessBDRepository).deleteLastRule("2024-12", "ORG_A");
        verify(businessBDRepository).deleteLastRule("2024-12", "ORG_B");
        
        // 验证总共调用了2次删除和2次插入
        verify(businessBDRepository, times(2)).deleteLastRule(anyString(), anyString());
        verify(businessBDRepository, times(2)).insert(any());
    }

    /**
     * 测试相同组织相同月份的数据覆盖场景
     */
    @Test
    public void testImportRule_SameOrgSameMonth_VerifyOverwrite() {
        // Given - 相同组织，相同月份的数据
        List<BusinessBDRuleDO> ruleList = Collections.singletonList(businessBDRuleDO);
        String orgCode = "ORG001";

        when(organizationMapper.getOrganizationIdByName("测试公司", 3, 1)).thenReturn(orgCode);
        when(organizationMapper.getOrganizationType(orgCode)).thenReturn("company");

        // When
        businessBDRuleService.importRule(ruleList, processUserDO);

        // Then - 验证先删除后插入的顺序，确保数据被覆盖
        org.mockito.InOrder inOrder = inOrder(businessBDRepository);
        inOrder.verify(businessBDRepository).deleteLastRule("2024-12", orgCode);
        inOrder.verify(businessBDRepository).insert(any());
        
        // 验证该组织该月份的删除和插入各执行一次，确保只有一条新记录
        verify(businessBDRepository, times(1)).deleteLastRule("2024-12", orgCode);
        verify(businessBDRepository, times(1)).insert(any());
    }

    /**
     * 测试相同组织不同月份在同一文件中的异常处理
     */
    @Test(expected = ApplicationException.class)
    public void testImportRule_SameOrgDifferentMonth_ThrowException() {
        // Given - 相同组织，不同月份的数据（这在同一导入文件中是不允许的）
        BusinessBDRuleDO rule1 = createBusinessBDRuleDO("测试公司", "2024-11");
        BusinessBDRuleDO rule2 = createBusinessBDRuleDO("测试公司", "2024-12");
        List<BusinessBDRuleDO> ruleList = Arrays.asList(rule1, rule2);

        // When & Then - 应该抛出异常，因为同一文件中不允许相同组织
        try {
            businessBDRuleService.importRule(ruleList, processUserDO);
        } finally {
            // 验证由于异常，没有执行任何数据库操作
            verify(businessBDRepository, never()).deleteLastRule(anyString(), anyString());
            verify(businessBDRepository, never()).insert(any());
        }
    }

    /**
     * 测试文件中重复数据的异常处理
     */
    @Test(expected = ApplicationException.class)
    public void testImportRule_DuplicateInFile_ThrowException() {
        // Given - 文件中包含重复的公司名
        BusinessBDRuleDO rule1 = createBusinessBDRuleDO("重复公司", "2024-12");
        BusinessBDRuleDO rule2 = createBusinessBDRuleDO("重复公司", "2024-11");
        List<BusinessBDRuleDO> ruleList = Arrays.asList(rule1, rule2);

        // When & Then - 应该抛出异常，不执行任何数据库操作
        try {
            businessBDRuleService.importRule(ruleList, processUserDO);
        } finally {
            // 验证由于异常，没有执行任何数据库操作
            verify(businessBDRepository, never()).deleteLastRule(anyString(), anyString());
            verify(businessBDRepository, never()).insert(any());
        }
    }

    /**
     * 测试组织不存在的异常处理
     */
    @Test
    public void testImportRule_OrganizationNotFound_ThrowException() {
        // Given
        List<BusinessBDRuleDO> ruleList = Collections.singletonList(businessBDRuleDO);

        // Mock 组织不存在
        when(organizationMapper.getOrganizationIdByName("测试公司", 3, 1)).thenReturn(null);

        // When & Then
        try {
            businessBDRuleService.importRule(ruleList, processUserDO);
            fail("应该抛出ApplicationException");
        } catch (ApplicationException e) {
            assertTrue("异常信息应该包含组织名", e.getMessage().contains("测试公司"));
            assertTrue("异常信息应该提示不存在", e.getMessage().contains("不存在"));
            
            // 验证不应该调用删除和插入操作
            verify(businessBDRepository, never()).deleteLastRule(anyString(), anyString());
            verify(businessBDRepository, never()).insert(any());
        }
    }

    /**
     * 测试空数据列表的异常处理
     */
    @Test(expected = ApplicationException.class)
    public void testImportRule_EmptyList_ThrowException() {
        // Given
        List<BusinessBDRuleDO> emptyList = Collections.emptyList();

        // When & Then - 应该抛出异常
        try {
            businessBDRuleService.importRule(emptyList, processUserDO);
        } finally {
            // 验证不执行任何数据库操作
            verify(businessBDRepository, never()).deleteLastRule(anyString(), anyString());
            verify(businessBDRepository, never()).insert(any());
        }
    }

    /**
     * 测试全职BD配置小于承揽BD配置的异常处理
     */
    @Test
    public void testImportRule_FullTimeLessThanContract_ThrowException() {
        // Given - 全职配置小于承揽配置
        businessBDRuleDO.setFullTimePerformanceRequire(new BigDecimal("50"));
        businessBDRuleDO.setContractPerformanceRequire(new BigDecimal("80"));
        List<BusinessBDRuleDO> ruleList = Collections.singletonList(businessBDRuleDO);

        when(organizationMapper.getOrganizationIdByName("测试公司", 3, 1)).thenReturn("ORG001");
        when(organizationMapper.getOrganizationType("ORG001")).thenReturn("company");

        // When & Then
        try {
            businessBDRuleService.importRule(ruleList, processUserDO);
            fail("应该抛出ApplicationException");
        } catch (ApplicationException e) {
            assertTrue("异常信息应该包含配置错误提示", 
                e.getMessage().contains("全职业务BD配置必须大于等于承揽业务BD配置"));
            
            // 验证不执行插入操作（但可能已经执行了删除操作）
            verify(businessBDRepository, never()).insert(any());
        }
    }

    /**
     * 创建测试用的BusinessBDRuleDO
     */
    private BusinessBDRuleDO createBusinessBDRuleDO(String companyName, String yearMonth) {
        BusinessBDRuleDO rule = new BusinessBDRuleDO();
        rule.setCompanyName(companyName);
        rule.setTheYearMonth(yearMonth);
        rule.setStartYearMonth("2024-01");
        rule.setFullTimePerformanceRequire(new BigDecimal("100"));
        rule.setContractPerformanceRequire(new BigDecimal("80"));
        return rule;
    }

    /**
     * 模拟静态业务组设置
     */
    private void mockStaticBusinessGroup() {
        // 通过反射或其他方式模拟 RequestUtils.getBusinessGroup() 返回1
        // 这里简化处理，假设测试环境已配置
    }
} 