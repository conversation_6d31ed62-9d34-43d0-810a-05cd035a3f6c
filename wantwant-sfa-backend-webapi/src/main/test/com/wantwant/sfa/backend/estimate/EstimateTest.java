package com.wantwant.sfa.backend.estimate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gexin.fastjson.JSON;
import com.wantwant.sfa.backend.application.EstimateApplication;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateOrganizationDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateScheduleDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateSkuDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateSkuGroupDO;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateSkuService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.estimated.request.EstimateApplyRequest;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/16/上午8:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EstimateTest {
    @Resource
    private IEstimateSkuService estimateSkuService;
    @Resource
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Resource
    private EstimateApplication estimateApplication;

    @Test
    public void testSaveGroup(){
        ProcessUserDO processUser = getProcessUser();

        EstimateSkuGroupDO estimateSkuGroupDO = new EstimateSkuGroupDO();
        estimateSkuGroupDO.setBusinessGroup(1);
        estimateSkuGroupDO.setGroupName("物料组002");
        List<EstimateSkuDO> skuDOList = getListFromJson();
        estimateSkuGroupDO.setSkuDOList(skuDOList);
        estimateSkuService.saveSkuGroup(estimateSkuGroupDO,processUser);
    }

    @Test
    public void testSaveSchedule(){
        ProcessUserDO processUser = getProcessUser();

        EstimateScheduleDO estimateScheduleDO = new EstimateScheduleDO();
        estimateScheduleDO.setShipPeriodId(4L);
        estimateScheduleDO.setStartDate(LocalDate.parse("2024-07-01"));
        estimateScheduleDO.setEndDate(LocalDate.parse("2024-07-31"));
        estimateScheduleDO.setTheYearMonth("2024-09");
        estimateScheduleDO.setType(1);

        List<EstimateOrganizationDO> estimateOrganizationDOList = new ArrayList<>();
        List<CeoBusinessOrganizationEntity> companyList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>().eq(CeoBusinessOrganizationEntity::getChannel, 3).eq(CeoBusinessOrganizationEntity::getBusinessGroup, 1).eq(CeoBusinessOrganizationEntity::getOrganizationType, "company"));
        companyList.forEach(e -> {
            EstimateOrganizationDO estimateOrganizationDO = new EstimateOrganizationDO();
            estimateOrganizationDO.setCompanyCode(e.getOrganizationId());
            estimateOrganizationDO.setCompanyName(e.getOrganizationName());
            estimateOrganizationDOList.add(estimateOrganizationDO);
        });

        estimateScheduleDO.setEstimateOrganizationDOList(estimateOrganizationDOList);

        estimateSkuService.saveSchedule(estimateScheduleDO,processUser);
    }

    @Test
    public void testApply(){
        EstimateApplyRequest estimateApplyRequest = new EstimateApplyRequest();
        estimateApplyRequest.setSaleEstimateNo("GD24013100001");
        estimateApplyRequest.setProductGroupId("G06051542001");
        estimateApplyRequest.setMemberKey(177223195L);
        estimateApplication.apply(estimateApplyRequest);
    }

    private List<EstimateSkuDO> getListFromJson() {
        try (BufferedReader reader = new BufferedReader(new FileReader("src/main/test/com/wantwant/sfa/backend/estimate/data/sku2.json"))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            String jsonString = sb.toString();
            List<EstimateSkuDO> skuDOList = JSON.parseArray(jsonString, EstimateSkuDO.class);
            return skuDOList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private ProcessUserDO getProcessUser() {
        ProcessUserDO processUserDO = new ProcessUserDO();
        processUserDO.setEmployeeId("00441211");
        processUserDO.setEmployeeName("张远");
        processUserDO.setOrganizationId("ZB_Z");
        processUserDO.setOrganizationName("总部");
        processUserDO.setPositionName("总部-营运");
        return processUserDO;
    }
}
