package com.wantwant.sfa.backend.estimate;

import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateAdjustDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateAdjustOrgDO;
import com.wantwant.sfa.backend.domain.estimate.enums.AuditResultEnum;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateHistoryDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateSkuRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.persistence.EstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateAdjustPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailHistoryPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateSkuPO;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateAdjustService;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateControlService;
import com.wantwant.sfa.backend.domain.estimate.service.impl.EstimateDomainService;
import com.wantwant.sfa.backend.estimate.model.SaveSkuSaleModel;
import com.wantwant.sfa.backend.estimate.model.SkuEstimateBoxNumberDTO;
import com.wantwant.sfa.backend.estimate.model.SkuModel;
import com.wantwant.sfa.backend.estimate.model.SkuOverSaleControlSaveWithPhaseRequest;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.util.EstimateConnectorUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * MOQ审核方法测试类 - 验证调用estimateConnectorUtil的传参
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RunWith(MockitoJUnitRunner.class)
public class MoqAuditTest {

    @Mock
    private IEstimateRepository estimateRepository;

    @Mock
    private OrganizationMapper organizationMapper;

    @Mock
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @Mock
    private EstimateConnectorUtil estimateConnectorUtil;

    @Mock
    private IEstimateAdjustService estimateAdjustService;

    @Mock
    private IEstimateSkuRepository estimateSkuRepository;

    @Mock
    private RedisUtil redisUtil;

    @InjectMocks
    private EstimateDomainService estimateDomainService;

    private EstimateAdjustDO estimateAdjustDO;
    private ProcessUserDO processUserDO;
    private EstimateAdjustPO estimateAdjustPO;
    private List<EstimateHistoryDTO> mockHistoryData;

    @Before
    public void setUp() {
        // 初始化审核调整DO
        estimateAdjustDO = new EstimateAdjustDO();
        estimateAdjustDO.setResult(AuditResultEnum.APPROVED.getCode()); // 审核通过
        estimateAdjustDO.setSku("SKU001");
        estimateAdjustDO.setYearMonth("2024-12");
        estimateAdjustDO.setShipPeriodId(1L);
        
        EstimateAdjustOrgDO orgDO = new EstimateAdjustOrgDO();
        orgDO.setOrganizationId("ORG001");
        estimateAdjustDO.setOrgList(Arrays.asList(orgDO));

        // 初始化处理人信息
        processUserDO = new ProcessUserDO();
        processUserDO.setEmployeeId("EMP001");
        processUserDO.setEmployeeName("测试员工");

        // 初始化调整单PO
        estimateAdjustPO = new EstimateAdjustPO();
        estimateAdjustPO.setAdjustId(1L);
        estimateAdjustPO.setSku("SKU001");
        estimateAdjustPO.setMonth("2024-12");
        estimateAdjustPO.setShipPeriodId(1L);

        // 模拟历史数据 - 为两个SKU提供数据以测试聚合逻辑
        mockHistoryData = Arrays.asList(
            createHistoryDTO("SKU001", 100, 1L),
            createHistoryDTO("SKU002", 200, 1L),
            createHistoryDTO("SKU003", 150, 2L), // 不同货需期别
            createHistoryDTO("SKU001", 50, 1L)   // 相同SKU，测试聚合
        );
    }

    @Test
    public void testMoqAudit_ValidateSaveSkuSaleParameters() {
        // Given - 设置审核通过的条件
        setupMockDependenciesForApproval();

        // 使用ArgumentCaptor捕获SaveSkuSaleModel参数
        ArgumentCaptor<SaveSkuSaleModel> saveSkuSaleCaptor = ArgumentCaptor.forClass(SaveSkuSaleModel.class);

        // When
        estimateDomainService.moqAudit(estimateAdjustDO, processUserDO);

        // Then
        verify(estimateConnectorUtil).saveSkuSale(saveSkuSaleCaptor.capture());
        SaveSkuSaleModel capturedModel = saveSkuSaleCaptor.getValue();

        // 验证基础参数
        assertEquals("公司名称应该正确", "测试组织", capturedModel.getCompanyName());
        assertEquals("员工ID应该正确", "EMP001", capturedModel.getEmployeeId());
        assertEquals("预估月份应该正确", "2024-12", capturedModel.getEstimateMonth());
        assertEquals("产品组编码应该正确", "PG001", capturedModel.getProductGroupId());

        // 验证SKU列表聚合正确
        List<SkuModel> skuList = capturedModel.getSkuList();
        assertNotNull("SKU列表不能为空", skuList);
        assertEquals("应该有3个不同的SKU", 3, skuList.size());

        // 验证SKU001聚合正确 (100 + 50 = 150)
        SkuModel sku001 = findSkuByCode(skuList, "SKU001");
        assertNotNull("应该包含SKU001", sku001);
        assertEquals("SKU001数量应该聚合正确", new BigDecimal("150"), sku001.getEstimateBoxNumber());

        // 验证SKU002
        SkuModel sku002 = findSkuByCode(skuList, "SKU002");
        assertNotNull("应该包含SKU002", sku002);
        assertEquals("SKU002数量应该正确", new BigDecimal("200"), sku002.getEstimateBoxNumber());

        // 验证SKU003
        SkuModel sku003 = findSkuByCode(skuList, "SKU003");
        assertNotNull("应该包含SKU003", sku003);
        assertEquals("SKU003数量应该正确", new BigDecimal("150"), sku003.getEstimateBoxNumber());
    }

    @Test
    public void testMoqAudit_ValidateOverSaleControlParameters() {
        // Given - 设置审核通过的条件
        setupMockDependenciesForApproval();

        // 使用ArgumentCaptor捕获SkuOverSaleControlSaveWithPhaseRequest参数
        ArgumentCaptor<SkuOverSaleControlSaveWithPhaseRequest> overSaleCaptor =
            ArgumentCaptor.forClass(SkuOverSaleControlSaveWithPhaseRequest.class);

        // When
        estimateDomainService.moqAudit(estimateAdjustDO, processUserDO);

        // Then
        verify(estimateConnectorUtil).saveSkuOverSaleControlInfoWithPhase(overSaleCaptor.capture());
        SkuOverSaleControlSaveWithPhaseRequest capturedRequest = overSaleCaptor.getValue();

        // 验证基础参数
        assertEquals("预估月份应该正确", "2024-12", capturedRequest.getEstimateMonth());
        assertEquals("预估期别应该正确", Integer.valueOf(1), capturedRequest.getEstimatePhase());
        assertEquals("公司名称应该正确", "测试组织", capturedRequest.getCompanyName());
        assertEquals("员工ID应该正确", "EMP001", capturedRequest.getEmployeeId());
        assertEquals("产品组编码应该正确", "PG001", capturedRequest.getProductGroupId());

        // 验证SKU列表按货需期别过滤正确
        List<SkuEstimateBoxNumberDTO> skuList = capturedRequest.getSkuList();
        assertNotNull("SKU列表不能为空", skuList);
        assertEquals("应该只有shipPeriod=1的SKU", 2, skuList.size());

        // 验证SKU001数据（聚合后的结果）
        SkuEstimateBoxNumberDTO sku001 = findSkuEstimateByCode(skuList, "SKU001");
        assertNotNull("应该包含SKU001", sku001);
        assertEquals("SKU001数量应该聚合正确", Integer.valueOf(150), sku001.getEstimateBoxNumber());

        // 验证SKU002数据
        SkuEstimateBoxNumberDTO sku002 = findSkuEstimateByCode(skuList, "SKU002");
        assertNotNull("应该包含SKU002", sku002);
        assertEquals("SKU002数量应该正确", Integer.valueOf(200), sku002.getEstimateBoxNumber());

        // 验证不包含shipPeriod=2的SKU003
        boolean hasSku003 = skuList.stream().anyMatch(sku -> "SKU003".equals(sku.getSku()));
        assertFalse("不应该包含shipPeriod=2的SKU003", hasSku003);
    }

    /**
     * 设置Mock依赖用于审核通过测试
     */
    private void setupMockDependenciesForApproval() {
        // Mock Redis分布式锁
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any(TimeUnit.class)))
            .thenReturn(true);

        // Mock 调整单信息
        when(estimateAdjustService.findEstimateAdjust(anyString(), anyLong(), anyString()))
            .thenReturn(estimateAdjustPO);

        when(estimateSkuRepository.selectSkuByCode(
                anyString(), anyInt())).thenReturn(new EstimateSkuPO());

        // Mock 历史记录查询
        when(estimateRepository.selectHistoryByCondition(anyString(), anyLong(), anyList()))
            .thenReturn(new ArrayList<EstimateApprovalDetailHistoryPO>());

        // Mock 调整明细查询
        when(estimateRepository.selectEstimateAdjustDetailByAdjustId(anyLong()))
            .thenReturn(new ArrayList<EstimateAdjustDetailPO>());

        // Mock 历史数据查询
        when(estimateRepository.selectHistoryByOrgCode(anyString(), anyString()))
            .thenReturn(mockHistoryData);

        // Mock 组织相关信息
        when(organizationMapper.getOrganizationName(anyString()))
            .thenReturn("测试组织");
        when(organizationMapper.getBusinessGroupById(anyString()))
            .thenReturn(1);

        // Mock 产品组信息
        SfaBusinessGroupEntity businessGroup = new SfaBusinessGroupEntity();
        businessGroup.setBusinessGroupCode("PG001");
        when(sfaBusinessGroupMapper.selectById(anyInt()))
            .thenReturn(businessGroup);
    }

    /**
     * 创建测试用的历史数据DTO
     */
    private EstimateHistoryDTO createHistoryDTO(String sku, Integer estimateCount, Long shipPeriodId) {
        EstimateHistoryDTO dto = new EstimateHistoryDTO();
        dto.setSku(sku);
        dto.setEstimateCount(estimateCount);
        dto.setShipPeriodId(shipPeriodId);
        return dto;
    }

    /**
     * 根据SKU编码查找SKU模型
     */
    private SkuModel findSkuByCode(List<SkuModel> skuList, String skuCode) {
        return skuList.stream()
            .filter(sku -> skuCode.equals(sku.getSku()))
            .findFirst()
            .orElse(null);
    }

    /**
     * 根据SKU编码查找SKU预估模型
     */
    private SkuEstimateBoxNumberDTO findSkuEstimateByCode(List<SkuEstimateBoxNumberDTO> skuList, String skuCode) {
        return skuList.stream()
            .filter(sku -> skuCode.equals(sku.getSku()))
            .findFirst()
            .orElse(null);
    }
} 