package com.wantwant.sfa.backend.estimate;

import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalPassDO;
import com.wantwant.sfa.backend.domain.estimate.mapper.EstimateApprovalDetailMapper;
import com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateHistoryDTO;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.facade.IEstimateSkuRepository;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailPO;
import com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalPO;
import com.wantwant.sfa.backend.domain.estimate.service.IEstimateControlService;
import com.wantwant.sfa.backend.domain.estimate.service.impl.EstimateDomainService;
import com.wantwant.sfa.backend.estimate.model.SaveSkuSaleModel;
import com.wantwant.sfa.backend.estimate.model.SkuEstimateBoxNumberDTO;
import com.wantwant.sfa.backend.estimate.model.SkuModel;
import com.wantwant.sfa.backend.estimate.model.SkuOverSaleControlSaveWithPhaseRequest;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.util.EstimateConnectorUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * SyncSkuSaleData方法测试类 - 验证调用estimateConnectorUtil前的参数
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RunWith(MockitoJUnitRunner.class)
public class SyncSkuSaleDataTest {

    @Mock
    private IEstimateRepository estimateRepository;

    @Mock
    private IEstimateSkuRepository estimateSkuRepository;

    @Mock
    private OrganizationMapper organizationMapper;

    @Mock
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @Mock
    private EstimateConnectorUtil estimateConnectorUtil;

    @Mock
    private IEstimateControlService estimateControlService;

    @Mock
    private EstimateApprovalDetailMapper estimateApprovalDetailMapper;

    @Mock
    private RedisUtil redisUtil;

    @InjectMocks
    private EstimateDomainService estimateDomainService;

    private EstimateApprovalPO estimateApprovalPO;
    private ProcessUserDO processUserDO;
    private List<EstimateHistoryDTO> mockHistoryData;

    @Before
    public void setUp() {
        // 初始化测试数据
        estimateApprovalPO = new EstimateApprovalPO();
        estimateApprovalPO.setApprovalId(1L);
        estimateApprovalPO.setMonth("2024-12");
        estimateApprovalPO.setOrganizationId("ORG001");
        estimateApprovalPO.setType(1);
        estimateApprovalPO.setScheduleId(100L);

        processUserDO = new ProcessUserDO();
        processUserDO.setEmployeeId("EMP001");
        processUserDO.setEmployeeName("测试员工");

        // 模拟历史数据
        mockHistoryData = Arrays.asList(
            createHistoryDTO("SKU001", 100, 1L),
            createHistoryDTO("SKU002", 200, 1L),
            createHistoryDTO("SKU003", 150, 2L), // 不同货需期别
            createHistoryDTO("SKU001", 50, 1L)   // 重复SKU，测试聚合
        );
    }

    @Test
    public void testPass_ValidateSkuSaleModelParameters() {
        // Given - 设置产销审核且为分公司的条件
        EstimateApprovalPassDO passRequest = createPassRequest(50); // 产销审核
        String organizationId = "ORG001";
        String organizationName = "测试组织";
        String productGroupCode = "PG001";

        setupMockDependenciesForPass(organizationId, organizationName, productGroupCode, "company");

        // 使用ArgumentCaptor捕获SaveSkuSaleModel参数
        ArgumentCaptor<SaveSkuSaleModel> saveSkuSaleCaptor = ArgumentCaptor.forClass(SaveSkuSaleModel.class);

        // When
        estimateDomainService.pass(passRequest, processUserDO);

        // Then
        verify(estimateConnectorUtil).saveSkuSale(saveSkuSaleCaptor.capture());
        SaveSkuSaleModel capturedModel = saveSkuSaleCaptor.getValue();

        // 验证基础参数
        assertEquals("公司名称应该正确", "测试组织", capturedModel.getCompanyName());
        assertEquals("员工ID应该正确", "EMP001", capturedModel.getEmployeeId());
        assertEquals("预估月份应该正确", "2024-12", capturedModel.getEstimateMonth());
        assertEquals("产品组编码应该正确", "PG001", capturedModel.getProductGroupId());

        // 验证SKU列表聚合正确（只有有效的预估数量）
        List<SkuModel> skuList = capturedModel.getSkuList();
        assertNotNull("SKU列表不能为空", skuList);
        assertEquals("应该有3个不同的SKU", 3, skuList.size());

        // 验证SKU001聚合正确 (100 + 50 = 150)
        SkuModel sku001 = findSkuByCode(skuList, "SKU001");
        assertNotNull("应该包含SKU001", sku001);
        assertEquals("SKU001数量应该聚合正确", new BigDecimal("150"), sku001.getEstimateBoxNumber());

        // 验证SKU002
        SkuModel sku002 = findSkuByCode(skuList, "SKU002");
        assertNotNull("应该包含SKU002", sku002);
        assertEquals("SKU002数量应该正确", new BigDecimal("200"), sku002.getEstimateBoxNumber());
    }

    @Test
    public void testPass_ValidateOverSaleControlParameters() {
        // Given - 设置产销审核且为分公司的条件
        EstimateApprovalPassDO passRequest = createPassRequest(50); // 产销审核
        String organizationId = "ORG001";
        String organizationName = "测试组织";
        String productGroupCode = "PG001";

        setupMockDependenciesForPass(organizationId, organizationName, productGroupCode, "company");

        // 使用ArgumentCaptor捕获SkuOverSaleControlSaveWithPhaseRequest参数
        ArgumentCaptor<SkuOverSaleControlSaveWithPhaseRequest> overSaleCaptor =
            ArgumentCaptor.forClass(SkuOverSaleControlSaveWithPhaseRequest.class);

        // When
        estimateDomainService.pass(passRequest, processUserDO);

        // Then
        verify(estimateConnectorUtil).saveSkuOverSaleControlInfoWithPhase(overSaleCaptor.capture());
        SkuOverSaleControlSaveWithPhaseRequest capturedRequest = overSaleCaptor.getValue();

        // 验证基础参数
        assertEquals("预估月份应该正确", "2024-12", capturedRequest.getEstimateMonth());
        assertEquals("预估期别应该正确", Integer.valueOf(1), capturedRequest.getEstimatePhase());
        assertEquals("公司名称应该正确", "测试组织", capturedRequest.getCompanyName());
        assertEquals("员工ID应该正确", "EMP001", capturedRequest.getEmployeeId());
        assertEquals("产品组编码应该正确", "PG001", capturedRequest.getProductGroupId());

        // 验证SKU列表按货需期别过滤正确
        List<SkuEstimateBoxNumberDTO> skuList = capturedRequest.getSkuList();
        assertNotNull("SKU列表不能为空", skuList);
        assertEquals("应该只有shipPeriod=1的SKU", 2, skuList.size()); // SKU001(2个) + SKU002(1个)

        // 验证SKU001数据（应该有两条记录）
        long sku001Count = skuList.stream().filter(sku -> "SKU001".equals(sku.getSku())).count();
        assertEquals("SKU001应该有1条记录", 1, sku001Count);

        // 验证SKU002数据
        SkuEstimateBoxNumberDTO sku002 = skuList.stream()
            .filter(sku -> "SKU002".equals(sku.getSku()))
            .findFirst()
            .orElse(null);
        assertNotNull("应该包含SKU002", sku002);
        assertEquals("SKU002数量应该正确", Integer.valueOf(200), sku002.getEstimateBoxNumber());

        // 验证不包含shipPeriod=2的SKU003
        boolean hasSku003 = skuList.stream().anyMatch(sku -> "SKU003".equals(sku.getSku()));
        assertFalse("不应该包含shipPeriod=2的SKU003", hasSku003);
    }

    /**
     * 创建测试用的历史数据DTO
     */
    private EstimateHistoryDTO createHistoryDTO(String sku, Integer estimateCount, Long shipPeriodId) {
        EstimateHistoryDTO dto = new EstimateHistoryDTO();
        dto.setSku(sku);
        dto.setEstimateCount(estimateCount);
        dto.setShipPeriodId(shipPeriodId);
        return dto;
    }

    /**
     * 根据SKU编码查找SKU模型
     */
    private SkuModel findSkuByCode(List<SkuModel> skuList, String skuCode) {
        return skuList.stream()
            .filter(sku -> skuCode.equals(sku.getSku()))
            .findFirst()
            .orElse(null);
    }

    /**
     * 创建审核通过请求
     */
    private EstimateApprovalPassDO createPassRequest(Integer processStep) {
        EstimateApprovalPassDO passRequest = new EstimateApprovalPassDO();
        passRequest.setApprovalId(1L);
        passRequest.setProcessStep(processStep);
        passRequest.setSkuList(new ArrayList<>());
        return passRequest;
    }

    /**
     * 设置Mock依赖用于pass方法测试
     */
    private void setupMockDependenciesForPass(String organizationId, String organizationName,
                                             String productGroupCode, String organizationType) {
        // Mock Redis分布式锁
        when(redisUtil.setLockIfAbsent(anyString(), anyString(), anyLong(), any()))
            .thenReturn(true);

        // Mock 审核申请信息
        when(estimateRepository.selectApprovalById(anyLong()))
            .thenReturn(estimateApprovalPO);

        // Mock 货需期别信息
        when(estimateSkuRepository.selectShipPeriodBySchedule(anyLong()))
            .thenReturn(1L);

        // Mock 审核详情列表
        List<EstimateApprovalDetailPO> detailPOList = Arrays.asList(
            createDetailPO("SKU001", organizationId),
            createDetailPO("SKU002", organizationId)
        );
        when(estimateRepository.selectApprovalDetailByApprovalId(anyLong()))
            .thenReturn(detailPOList);

        // Mock estimateApprovalDetailMapper查询
        when(estimateApprovalDetailMapper.selectList(any()))
            .thenReturn(new ArrayList<>());

        // Mock 历史数据
        when(estimateRepository.selectHistoryByOrgCode(anyString(), anyString()))
            .thenReturn(mockHistoryData);

        // Mock 组织相关信息
        when(organizationMapper.getBusinessGroupById(organizationId))
            .thenReturn(1);
        when(estimateRepository.getStoreName(organizationId))
            .thenReturn("测试门店");
        when(organizationMapper.getOrganizationType(organizationId))
            .thenReturn(organizationType);
        when(organizationMapper.getOrganizationName(organizationId))
            .thenReturn(organizationName);

        // Mock 产品组信息
        SfaBusinessGroupEntity businessGroup = new SfaBusinessGroupEntity();
        businessGroup.setBusinessGroupCode(productGroupCode);
        when(sfaBusinessGroupMapper.selectById(anyInt()))
            .thenReturn(businessGroup);
    }

    /**
     * 创建审核详情PO
     */
    private EstimateApprovalDetailPO createDetailPO(String sku, String organizationId) {
        EstimateApprovalDetailPO detailPO = new EstimateApprovalDetailPO();
        detailPO.setSku(sku);
        detailPO.setOrganizationId(organizationId);
        detailPO.setEstimateQuantity(100);
        detailPO.setAuditQuantity(100);
        detailPO.setSalePrice(new BigDecimal("10.00"));
        return detailPO;
    }
} 