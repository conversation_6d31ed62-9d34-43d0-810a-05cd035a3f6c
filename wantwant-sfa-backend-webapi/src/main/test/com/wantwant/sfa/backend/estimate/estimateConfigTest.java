package com.wantwant.sfa.backend.estimate;

import com.alibaba.excel.util.CollectionUtils;
import com.gexin.fastjson.JSON;
import com.wantwant.sfa.backend.estimate.model.EstimateApplyConfigModel;
import com.wantwant.sfa.backend.estimate.model.EstimateButtonModel;
import com.wantwant.sfa.backend.estimate.service.IEstimateApplyConfigCheckService;
import com.wantwant.sfa.backend.meeting.vo.MeetingEmpVo;
import com.wantwant.sfa.backend.meeting.vo.MeetingOrgEmpVo;
import com.wantwant.sfa.backend.meeting.vo.MeetingSelectEmpVo;
import lombok.ToString;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/03/15/下午2:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class estimateConfigTest {
    @Autowired
    private IEstimateApplyConfigCheckService estimateApplyConfigCheckService;

    @Test
    public void testConfig(){
        List<EstimateApplyConfigModel> applyConfig = estimateApplyConfigCheckService.getApplyConfig("2023-04");

        String result = JSON.toJSONString(applyConfig);
        System.out.println(result);
    }





    @Test
    public void test01(){

        MeetingSelectEmpVo meetingSelectEmpVo = new MeetingSelectEmpVo();


        List<MeetingOrgEmpVo> searchResult = meetingSelectEmpVo.getSearchResult();

        dp(searchResult);
    }

    private void dp(List<MeetingOrgEmpVo> searchResult) {
        if(!CollectionUtils.isEmpty(searchResult)){
            return;
        }

        searchResult.forEach(e -> {
            List<MeetingOrgEmpVo> children = Optional.ofNullable(e.getChildren()).orElse(new ArrayList<>());

            List<MeetingEmpVo> meetingEmpVoList = e.getMeetingEmpVoList();


        });
    }
}
