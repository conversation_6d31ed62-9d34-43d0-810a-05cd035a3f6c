package com.wantwant.sfa.backend.flow;

import com.wantwant.sfa.backend.domain.flow.DO.FlowDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowPassDO;
import com.wantwant.sfa.backend.domain.flow.DO.FlowProcessUserDO;
import com.wantwant.sfa.backend.domain.flow.enums.AuditStrategyEnum;
import com.wantwant.sfa.backend.domain.flow.service.IFlowService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/下午8:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FlowTest {

    @Resource
    private IFlowService flowService;

    @Test
    public void testInitFlow(){

        FlowDO flowDO = new FlowDO();
        flowDO.setFlowCode("TS00000001");
        flowDO.setCreateUserId("00550004");
        flowDO.setCreateUserName("杨敏");
        flowDO.setAuditStrategyEnum(AuditStrategyEnum.POSITION_TYPE_STRATEGY);
        flowDO.setOrganizationType("area");
        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();
        flowProcessUserDO.setEmployeeId("00550004");
        flowProcessUserDO.setEmployeeName("杨敏");
        flowProcessUserDO.setOrganizationId("DN_Z");
        flowDO.setFlowProcessUserDO(flowProcessUserDO);
        flowService.initFlow(flowDO);
    }


    @Test
    public void testPass(){
        FlowPassDO flowPassDO = new FlowPassDO();
        flowPassDO.setInstanceId(1L);
        flowPassDO.setNextProcessStep(3);

        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();
        flowProcessUserDO.setEmployeeId("00441211");
        flowProcessUserDO.setEmployeeName("张远");
        flowProcessUserDO.setRoleId(69);
        flowProcessUserDO.setOrganizationId("ZB_Z");
        flowPassDO.setCurrentProcessUserDO(flowProcessUserDO);

        FlowProcessUserDO nextUserPO = new FlowProcessUserDO();
        nextUserPO.setEmployeeId("00272473");
        nextUserPO.setEmployeeName("蔡旺祖");
        nextUserPO.setOrganizationId("ZB_Z");
        flowPassDO.setNextProcessUserDO(nextUserPO);


        flowService.pass(flowPassDO);
    }


    @Test
    public void testPass_step3(){
        FlowPassDO flowPassDO = new FlowPassDO();
        flowPassDO.setInstanceId(1L);

        FlowProcessUserDO flowProcessUserDO = new FlowProcessUserDO();
        flowProcessUserDO.setEmployeeId("00272473");
        flowProcessUserDO.setEmployeeName("蔡旺祖");
        flowProcessUserDO.setOrganizationId("ZB_Z");
        flowPassDO.setCurrentProcessUserDO(flowProcessUserDO);


        flowService.pass(flowPassDO);
    }
}
