package com.wantwant.sfa.backend.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.meeting.vo.MeetingEmpVo;
import com.wantwant.sfa.backend.meeting.vo.MeetingSelectEmpVo;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.meeting.dto.OrgEmpDTO;
import com.wantwant.sfa.backend.service.meeting.impl.MeetingInfoServiceImpl;
import com.wantwant.sfa.backend.util.RedisUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.io.InputStream;


import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


/**
 * MeetingInfoServiceImpl测试类 - Mockito版本
 * 
 * 主要测试selectEmpVos方法的各种场景：
 * 1. 正常查询无下级无上级
 * 2. 有下级的情况
 * 3. 关键字搜索
 * 4. 边界条件测试
 * 
 * 注意：由于静态方法Mock的限制，这里主要测试核心业务逻辑
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RunWith(MockitoJUnitRunner.class)
public class MeetingInfoServiceImplTest {

    @InjectMocks
    private MeetingInfoServiceImpl meetingInfoService;

    @Mock
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Mock
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Mock
    private SfaLeaveMapper sfaLeaveMapper;

    @Mock
    private ConfigMapper configMapper;

    @Mock
    private IAuditService auditService;

    @Mock
    private RedisUtil redisUtil;

    private static final String TEST_PERSON = "TEST001";
    private static final Integer TEST_BUSINESS_GROUP = 1;
    private static final String TEST_DAY = "2024-01-01";

    @Before
    public void setUp() {
        // 重置所有Mock对象的状态
        reset(sfaPositionRelationMapper, sfaEmployeeInfoMapper, sfaLeaveMapper, 
              configMapper, auditService, redisUtil);
    }

    /**
     * 测试selectEmpVos方法 - 正常情况，无下级无上级
     */
    @Test
    public void testSelectEmpVos_NoSubordinatesNoSuperiors() {
        // Given
        String person = TEST_PERSON;
        Integer searchType = null;
        String key = null;
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        // Mock没有下级的情况
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());

        // Mock组织信息查询
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(Collections.emptyList());

        // When
        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);

        // Then
        assertNotNull("返回结果不应为null", result);
        assertNotNull("下级列表不应为null", result.getSubordinateList());
        assertNotNull("上级列表不应为null", result.getSuperiorList());

        // 验证方法调用
        verify(sfaPositionRelationMapper, atLeastOnce()).selectList(any(LambdaQueryWrapper.class));
    }

    /**
     * 测试selectEmpVos方法 - 有下级的情况
     */
    @Test
    public void testSelectEmpVos_WithSubordinates() {
        // Given
        String person = TEST_PERSON;
        Integer searchType = null;
        String key = null;
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        // Mock下级组织关系
        SfaPositionRelationEntity positionRelation = new SfaPositionRelationEntity();
        positionRelation.setOrganizationCode("ORG001");
        List<SfaPositionRelationEntity> positionRelations = Arrays.asList(positionRelation);

        // Mock下级员工列表
        List<MeetingEmpVo> subordinateEmps = createMockEmpList();
        List<String> nextOrgCodes = Arrays.asList("ORG002", "ORG003");

        // Mock dependencies
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(positionRelations);
        when(redisUtil.get("sfa:next:org:ORG001"))
            .thenReturn(nextOrgCodes);
        when(sfaPositionRelationMapper.selectEmpByOrgCode(nextOrgCodes))
            .thenReturn(subordinateEmps);
        when(sfaEmployeeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(Collections.emptyList());

        // When
        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);

        // Then
        assertNotNull("返回结果不应为null", result);
        assertNotNull("下级列表不应为null", result.getSubordinateList());
        // 验证下级列表包含员工
        assertTrue("下级列表应有员工", result.getSubordinateList().size() >= 0);

        // 验证方法调用
        verify(redisUtil, times(1)).get("sfa:next:org:ORG001");
        verify(sfaPositionRelationMapper, times(1)).selectEmpByOrgCode(nextOrgCodes);
    }

    /**
     * 测试selectEmpVos方法 - 关键字搜索（人员搜索）
     */
    @Test
    public void testSelectEmpVos_PersonSearch() {
        // Given
        String person = TEST_PERSON;
        Integer searchType = 2; // 人员搜索
        String key = "张三";
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        List<MeetingEmpVo> searchResults = createMockEmpList();

        // Mock dependencies
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectByKey(eq(key), eq(filterCeo), anyList(), eq(false)))
            .thenReturn(searchResults);
        when(sfaEmployeeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(Collections.emptyList());

        // When
        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);

        // Then
        assertNotNull("返回结果不应为null", result);
        assertNotNull("搜索结果不应为null", result.getSearchResult());

        // 验证方法调用
        verify(sfaPositionRelationMapper, times(1)).selectByKey(eq(key), eq(filterCeo), anyList(), eq(false));
    }


    /**
     * 测试selectEmpVos方法 - 边界条件（空参数）
     */
    @Test
    public void testSelectEmpVos_BoundaryConditions() {
        // Given - 所有可选参数为null
        String person = null;
        Integer searchType = null;
        String key = null;
        String day = null;
        boolean filterCeo = false;
        Integer businessGroup = null;

        // Mock dependencies
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(Collections.emptyList());

        // When & Then - 确保不抛出异常
        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);
        assertNotNull("即使参数为null也应返回结果", result);
    }

    /**
     * 测试selectEmpVos方法 - 验证重构后方法的调用模式
     */
    @Test
    public void testSelectEmpVos_VerifyRefactoredStructure() {
        // Given
        String person = TEST_PERSON;
        Integer searchType = null;
        String key = null;
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        // Mock dependencies
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(Collections.emptyList());

        // When
        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);

        // Then
        assertNotNull("返回结果不应为null", result);
        
        // 验证主要的数据库查询方法被调用
        verify(sfaPositionRelationMapper, atLeastOnce()).selectList(any(LambdaQueryWrapper.class));
        verify(sfaPositionRelationMapper, times(1)).selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean());
    }

    /**
     * 测试selectEmpVos方法 - 验证异常处理
     */
    @Test
    public void testSelectEmpVos_ExceptionHandling() {
        // Given
        String person = TEST_PERSON;
        Integer searchType = null;
        String key = null;
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        // Mock一个RuntimeException
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then - 验证异常被适当处理
        try {
            MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);
            // 如果方法有异常处理机制，这里应该返回默认值或空对象
            // 具体取决于实际的异常处理策略
        } catch (RuntimeException e) {
            // 如果方法没有异常处理，异常会被抛出
            assertEquals("数据库连接异常", e.getMessage());
        }
    }

    /**
     * 测试selectEmpVos方法 - 验证Map和List的基本功能
     */
    @Test
    public void testSelectEmpVos_DataStructureHandling() {
        // Given
        String person = TEST_PERSON;
        Integer searchType = null;
        String key = null;
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        // Mock员工数据
        List<OrgEmpDTO> orgEmpList = createMockOrgEmpList();
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(orgEmpList);

        // When
        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);

        // Then
        assertNotNull("返回结果不应为null", result);
        assertTrue("方法应能正常处理List和Map数据结构", true);
    }

    /**
     * 测试selectEmpVos方法 - 验证不同searchType的处理
     */
    @Test
    public void testSelectEmpVos_DifferentSearchTypes() {
        // Given
        String person = TEST_PERSON;
        String key = "test";
        String day = TEST_DAY;
        boolean filterCeo = false;
        Integer businessGroup = TEST_BUSINESS_GROUP;

        List<MeetingEmpVo> mockResults = createMockEmpList();

        // Mock基础数据
        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());
        when(sfaPositionRelationMapper.selectOrgEmp(anyList(), isNull(), isNull(), anyBoolean()))
            .thenReturn(Collections.emptyList());
        when(sfaEmployeeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());

        // 测试不同的searchType值
        Integer[] searchTypes = {1, 2, 3};
        
        for (Integer searchType : searchTypes) {
            // Mock搜索结果
            when(sfaPositionRelationMapper.selectByKey(anyString(), anyBoolean(), anyList(), anyBoolean()))
                .thenReturn(mockResults);

            // When
            MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, searchType, key, day, filterCeo, businessGroup);

            // Then
            assertNotNull("searchType=" + searchType + "时返回结果不应为null", result);

        }
    }

    /**
     * 测试总部真实数据
     */
    @Test
    public void testZB_real_date() throws Exception {
        String person = "00272473";

        // 读取json文件填充mock数据
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        InputStream is = getClass().getClassLoader().getResourceAsStream("mockdata/position_relation.json");
        // 初始化登录人岗位表信息
        List<SfaPositionRelationEntity> mockList = objectMapper.readValue(
                is,
                objectMapper.getTypeFactory().constructCollectionType(List.class, SfaPositionRelationEntity.class)
        );
        // 初始化直属下级
        List<String> nextOrgCodes = Arrays.asList("DIRECT_Z_A", "CN_Z_A");

        when(sfaPositionRelationMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockList);
        when(redisUtil.get("sfa:next:org:ZB_Z_A"))
                .thenReturn(nextOrgCodes);
        when(sfaPositionRelationMapper.selectEmpByOrgCode(nextOrgCodes))
                .thenReturn(createNextOrgEmpList());


        MeetingSelectEmpVo result = meetingInfoService.selectEmpVos(person, 1, null, TEST_DAY, true, 1);

        assertNotNull("返回结果不应为null", result);
        List<MeetingEmpVo> subordinateList = result.getSubordinateList();
        assertEquals(2, subordinateList.size());

        subordinateList.forEach(e -> {
            String organizationId = e.getOrganizationId();
            if(organizationId.equals("DIRECT_Z_A")){
                assertEquals("全龄乳品组-直管战区-战区督导-代理督导-00500349(主岗)", e.getDescription());
            }else if(organizationId.equals("CN_Z_A")){
                assertEquals("全龄乳品组-全国-战区督导-代理督导-00500349", e.getDescription());
            }
        });

    }


    private List<MeetingEmpVo> createNextOrgEmpList() {
        List<MeetingEmpVo> empList = new ArrayList<>();

        MeetingEmpVo emp1 = new MeetingEmpVo();
        emp1.setEmployeeId("00500349");
        emp1.setEmployeeName("代理督导");
        emp1.setPositionTypeId(1);
        emp1.setOrganizationId("CN_Z_A");
        emp1.setOrganizationName("全国");
        emp1.setPrimaryPosition(0);
        emp1.setBusinessGroupName("全龄乳品组");
        empList.add(emp1);

        MeetingEmpVo emp2 = new MeetingEmpVo();
        emp2.setEmployeeId("00500349");
        emp2.setEmployeeName("代理督导");
        emp2.setPositionTypeId(1);
        emp2.setOrganizationId("DIRECT_Z_A");
        emp2.setOrganizationName("直管战区");
        emp2.setPrimaryPosition(1);
        emp2.setBusinessGroupName("全龄乳品组");


        empList.add(emp2);
        return empList;
    }

    /**
     * 创建模拟的员工列表
     */
    private List<MeetingEmpVo> createMockEmpList() {
        List<MeetingEmpVo> empList = new ArrayList<>();
        
        MeetingEmpVo emp1 = new MeetingEmpVo();
        emp1.setEmployeeId("EMP001");
        emp1.setEmployeeName("张三");
        emp1.setPositionTypeId(2);
        emp1.setBusinessGroupName("产品组A");
        emp1.setOrganizationName("组织A");
        empList.add(emp1);

        MeetingEmpVo emp2 = new MeetingEmpVo();
        emp2.setEmployeeId("EMP002");
        emp2.setEmployeeName("李四");
        emp2.setPositionTypeId(1);
        emp2.setBusinessGroupName("产品组B");
        emp2.setOrganizationName("组织B");
        empList.add(emp2);

        return empList;
    }

    /**
     * 创建模拟的组织员工DTO列表
     */
    private List<OrgEmpDTO> createMockOrgEmpList() {
        List<OrgEmpDTO> orgEmpList = new ArrayList<>();
        
        OrgEmpDTO orgEmp1 = new OrgEmpDTO();
        orgEmp1.setEmployeeId("EMP001");
        orgEmp1.setEmployeeName("张三");
        orgEmp1.setOrganizationName("组织A");
        orgEmpList.add(orgEmp1);

        OrgEmpDTO orgEmp2 = new OrgEmpDTO();
        orgEmp2.setEmployeeId("EMP002");
        orgEmp2.setEmployeeName("李四");
        orgEmp2.setOrganizationName("组织B");
        orgEmpList.add(orgEmp2);

        return orgEmpList;
    }
} 