package com.wantwant.sfa.backend.interview.test;

import com.wantwant.sfa.backend.Task.BusinessBDDevelopAutomationTask;
import com.wantwant.sfa.backend.application.businessBd.BusinessBdOrgQuotaAppService;
import com.wantwant.sfa.backend.domain.businessBd.DO.BusinessBdEmployeeQuotaOperationValue;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class BusinessBDTest {
    @Resource
    private BusinessBdOrgQuotaAppService businessBdOrgQuotaAppService;
    @Resource
    private BusinessBDDevelopAutomationTask businessBDDevelopAutomationTask;

    @Test
    public void test01(){
//        BusinessBdEmployeeQuotaOperationValue build = BusinessBdEmployeeQuotaOperationValue.builder()
//                .employeeInfoId(11064)
//                .organizationId("M5934_B")
//                .curPositionId(PositionEnum.BUSINESS_BD.getId())
//                .type(1).userId("ROOT").userName("系统自动").build();
//        businessBdOrgQuotaAppService.adjustQuotaByEmployeeOperation(build);
    }


    @Test
    public void test02(){
        businessBDDevelopAutomationTask.checkStaffingOccupancy("2025-06-01");
    }


    @Test
    public void test03(){
        businessBDDevelopAutomationTask.checkStaffingOccupancy("2025-03-22");
    }


    @Test
    public void test04(){
        businessBDDevelopAutomationTask.businessBDDevelopAutomatic("2025-04-20");
    }
}
