package com.wantwant.sfa.backend.interview.test;

import com.wantwant.sfa.backend.Task.EliminateTask;
import com.wantwant.sfa.backend.interview.service.impl.EliminateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/18/下午2:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EliminateTest {


    @Autowired
    private EliminateTask eliminateTask;
    @Autowired
    private EliminateService eliminateService;

    @Test
    public void test01(){
        eliminateTask.eliminateListExecute("2025-05");
    }


    @Test
    public void test02(){
        eliminateTask.eliminateAutoProcess(null);
    }


    @Test
    public void test03(){
        eliminateService.eliminate(33642546L);
    }
}
