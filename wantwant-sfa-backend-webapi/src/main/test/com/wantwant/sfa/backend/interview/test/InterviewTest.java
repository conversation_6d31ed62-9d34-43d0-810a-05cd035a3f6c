package com.wantwant.sfa.backend.interview.test;


import com.wantwant.sfa.backend.Task.JobPositionTask;

import com.wantwant.sfa.backend.Task.MobilizationTask;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.interview.dto.InterviewIssueReplyDto;
import com.wantwant.sfa.backend.interview.service.InterviewResumeService;

import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/12/13/下午1:20
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class InterviewTest {

    @Autowired
    private InterviewResumeService interviewResumeService;
    @Autowired
    private JobPositionTask jobPositionTask;
    @Autowired
    private IPenaltyService penaltyService;
    @Autowired
    private MobilizationTask mobilizationTask;
    @Autowired
    private ROOTConnectorUtil rootConnectorUtil;

    @Test
    public void testSaveReply(){
        List<InterviewIssueReplyDto> list = new ArrayList<>();

        InterviewIssueReplyDto dto = new InterviewIssueReplyDto();
        dto.setIssueId(1);
        dto.setReply("1231231");
        list.add(dto);

        InterviewIssueReplyDto dto2 = new InterviewIssueReplyDto();
        dto2.setIssueId(3);
        dto2.setReply("dddd");
        list.add(dto2);

        interviewResumeService.saveIssueReply(1,list);
    }

    @Test
    public void testJobsTransactionTask(){
        jobPositionTask.jobPosition("2025-04-15");
    }


    @Test
    public void testPenalty(){
//        penaltyService.eliminatePenalty(6412);
    }

    @Test
    public void testSendOnBoardMessage(){
        mobilizationTask.onboardMessage(null);
    }

    @Test
    public void testSendMSG(){
        rootConnectorUtil.updateSigningCompany(177222817L,"旺旺");
    }
}
