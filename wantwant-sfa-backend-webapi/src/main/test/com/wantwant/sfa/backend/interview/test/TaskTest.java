package com.wantwant.sfa.backend.interview.test;

import com.wantwant.sfa.backend.Task.TransactionPositionTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/06/30/上午11:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TaskTest {
    @Autowired
    private TransactionPositionTask transactionPositionTask;

    @Test
    public void test01(){
        transactionPositionTask.transactionTask("2024-11-03");
    }
}
