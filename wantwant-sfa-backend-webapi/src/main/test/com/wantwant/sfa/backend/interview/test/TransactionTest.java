package com.wantwant.sfa.backend.interview.test;

import com.wantwant.sfa.backend.Task.TransferCompanyTask;
import com.wantwant.sfa.backend.transaction.service.ITransactionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/21/下午7:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TransactionTest {

    @Autowired
    private ITransactionService transactionService;
    @Autowired
    private TransferCompanyTask transferCompanyTask;

    @Test
    public void testInit(){
        List<Long> memberKeyList = Arrays.asList(177222859L);
        transactionService.initJoiningCompanyTransaction(memberKeyList);
    }

    @Test
    public void testTask(){
        transferCompanyTask.execute("2023-08");
    }
}
