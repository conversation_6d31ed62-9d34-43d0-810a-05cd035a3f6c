package com.wantwant.sfa.backend.map;

import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.map.request.PeriodMapListRequest;
import com.wantwant.sfa.backend.map.vo.MapListVo;
import com.wantwant.sfa.backend.map.controller.MapController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MapTest {

    @Autowired
    private MapController mapController;

    /**
     * 测试 /attendance/listForPeriod 接口 - 日维度查询
     */
    @Test
    public void testAttendanceListForPeriod_DayPeriod() {
        // 准备测试数据
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001"); // 操作人工号，必填
        request.setEmployeeId("test001"); // 员工号
        request.setPeriod(1); // 日维度
        request.setAttendanceStartDate(LocalDate.now().minusDays(7)); // 开始日期
        request.setAttendanceEndDate(LocalDate.now()); // 结束日期

        // 执行测试
        Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);

        // 验证结果
        assertNotNull("响应不能为空", response);
        assertNotNull("数据不能为空", response.getData());
        // 日维度查询应该返回数据（即使是空列表）
        assertTrue("数据应该是列表类型", response.getData() instanceof List);
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 月维度查询
     */
    @Test
    public void testAttendanceListForPeriod_MonthPeriod() {
        // 准备测试数据
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001"); // 操作人工号，必填
        request.setEmployeeId("test001"); // 员工号
        request.setPeriod(2); // 月维度
        request.setYearMonth("2024-01"); // 月份格式

        // 执行测试
        Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);

        // 验证结果
        assertNotNull("响应不能为空", response);
        assertNotNull("数据不能为空", response.getData());
        assertTrue("数据应该是列表类型", response.getData() instanceof List);
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 季度维度查询
     */
    @Test
    public void testAttendanceListForPeriod_QuarterPeriod() {
        // 准备测试数据
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001"); // 操作人工号，必填
        request.setEmployeeId("test001"); // 员工号
        request.setPeriod(3); // 季度维度
        request.setYearMonth("2024-Q1"); // 季度格式

        // 执行测试
        Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);

        // 验证结果
        assertNotNull("响应不能为空", response);
        assertNotNull("数据不能为空", response.getData());
        assertTrue("数据应该是列表类型", response.getData() instanceof List);
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 参数验证：缺少操作人工号
     */
    @Test
    public void testAttendanceListForPeriod_MissingPerson() {
        // 准备测试数据 - 缺少必填的 person 字段
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPeriod(1); // 日维度
        request.setAttendanceStartDate(LocalDate.now().minusDays(7));
        request.setAttendanceEndDate(LocalDate.now());

        try {
            // 执行测试
            Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);
            fail("应该抛出参数验证异常");
        } catch (Exception e) {
            // 预期会抛出异常，因为 person 是必填字段
            assertNotNull("异常信息不能为空", e.getMessage());
            assertTrue("异常信息应该包含参数验证错误",
                    e.getMessage().contains("工号") || e.getMessage().contains("不能为空"));
        }
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 参数验证：错误的月份格式
     */
    @Test
    public void testAttendanceListForPeriod_InvalidMonthFormat() {
        // 准备测试数据 - 错误的月份格式
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001");
        request.setEmployeeId("test001");
        request.setPeriod(2); // 月维度
        request.setYearMonth("2024-13"); // 错误的月份格式（13月不存在）

        try {
            // 执行测试
            Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);
            fail("应该抛出格式验证异常");
        } catch (Exception e) {
            // 预期会抛出异常，因为月份格式错误
            assertNotNull("异常信息不能为空", e.getMessage());
            assertTrue("异常信息应该包含格式错误提示",
                    e.getMessage().contains("正确的月份") || e.getMessage().contains("格式"));
        }
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 参数验证：错误的季度格式
     */
    @Test
    public void testAttendanceListForPeriod_InvalidQuarterFormat() {
        // 准备测试数据 - 错误的季度格式
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001");
        request.setEmployeeId("test001");
        request.setPeriod(3); // 季度维度
        request.setYearMonth("2024-Q5"); // 错误的季度格式（Q5不存在）

        try {
            // 执行测试
            Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);
            fail("应该抛出格式验证异常");
        } catch (Exception e) {
            // 预期会抛出异常，因为季度格式错误
            assertNotNull("异常信息不能为空", e.getMessage());
            assertTrue("异常信息应该包含格式错误提示",
                    e.getMessage().contains("正确的季度") || e.getMessage().contains("格式"));
        }
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 默认处理（无period参数）
     */
    @Test
    public void testAttendanceListForPeriod_NoPeriod() {
        // 准备测试数据 - 不设置period参数，应该默认使用日维度
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001");
        request.setEmployeeId("test001");
        request.setAttendanceStartDate(LocalDate.now().minusDays(1));
        request.setAttendanceEndDate(LocalDate.now());

        // 执行测试
        Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);

        // 验证结果
        assertNotNull("响应不能为空", response);
        assertNotNull("数据不能为空", response.getData());
        assertTrue("数据应该是列表类型", response.getData() instanceof List);
    }

    /**
     * 测试 /attendance/listForPeriod 接口 - 验证返回数据结构
     */
    @Test
    public void testAttendanceListForPeriod_ResponseStructure() {
        // 准备测试数据
        PeriodMapListRequest request = new PeriodMapListRequest();
        request.setPerson("test001");
        request.setEmployeeId("test001");
        request.setPeriod(1);
        request.setAttendanceStartDate(LocalDate.now().minusDays(3));
        request.setAttendanceEndDate(LocalDate.now());

        // 执行测试
        Response<List<MapListVo>> response = mapController.attendanceListForPeriod(request);

        // 验证响应结构
        assertNotNull("响应不能为空", response);
        assertNotNull("响应数据不能为空", response.getData());

        // 如果有数据，验证数据结构
        List<MapListVo> dataList = response.getData();
        if (!dataList.isEmpty()) {
            MapListVo firstItem = dataList.get(0);
            // 验证关键字段是否存在
            assertNotNull("员工ID不能为空", firstItem.getEmployeeId());
            assertNotNull("打卡日期不能为空", firstItem.getAttendanceDate());
            // 可以根据需要添加更多字段验证
        }
    }
}
