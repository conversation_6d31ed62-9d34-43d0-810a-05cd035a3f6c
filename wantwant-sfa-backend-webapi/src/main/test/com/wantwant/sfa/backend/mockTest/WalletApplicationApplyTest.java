package com.wantwant.sfa.backend.mockTest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.application.WalletQuotaApplication;
import com.wantwant.sfa.backend.audit.service.AuditService;
import com.wantwant.sfa.backend.domain.wallet.DO.WalletQuotaApplicationDO;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletAccountMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.wallet.dto.WalletSendDTO;
import com.wantwant.sfa.backend.wallet.entity.WantWalletAccountEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletEntity;
import com.wantwant.sfa.backend.wallet.service.IWalletSearchService;
import com.wantwant.sfa.backend.wallet.service.impl.WalletApplicationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class WalletApplicationApplyTest {

    @Mock
    private OrganizationMapper organizationMapper;

    @Mock
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Mock
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Mock
    private WalletQuotaApplication walletQuotaApplication;

    @Mock
    private IWalletSearchService walletSearchService;

    @Mock
    private ConfigMapper configMapper;

    @Mock
    private AuditService auditService;

    @Spy
    @InjectMocks
    private WalletApplicationService walletApplicationService;

    @Mock
    private WantWalletAccountMapper wantWalletAccountMapper;

    @Mock
    private WantWalletMapper wantWalletMapper;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(walletApplicationService, "auditService", auditService);
        // 设置通用mock返回值
        when(configMapper.getValueByCode("zw_hr_employee_id")).thenReturn("100000");
        when(organizationMapper.getOrganizationName(anyString())).thenReturn("测试组织");
        when(wantWalletAccountMapper.selectOne(any())).thenReturn(createWantWalletAccountEntity());
        when(wantWalletMapper.selectOne(any())).thenReturn(createWantWalletEntity());
        
        // Mock doSend方法，避免实际执行
        doNothing().when(walletApplicationService).doSend(any(WalletSendDTO.class), anyMap());
    }


    @Test
    void testQuickSend_skipAudit_true() {
        // 准备测试数据
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(false); // 跳过审核
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证没有启动审核流程
        verify(walletQuotaApplication, never()).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
    }

    @Test
    void testQuickSend_skipAudit_false_partnerReceiver_regionManagerOnDuty_companyManagerCrossLevel() {
        // 测试场景：区域经理在岗，区域总监跨级发放合伙人
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(2); // 合伙人
        walletSendDTO.setReceiverKey("100001"); // 合伙人memberKey
        walletSendDTO.setProcessUserId("100003"); // 区域总监

        // Mock员工信息
        SfaEmployeeInfoModel employeeInfo = createEmployeeInfo();
        when(sfaEmployeeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(employeeInfo);
        
        // Mock岗位关系 - 合伙人
        SfaPositionRelationEntity positionRelation = createPositionRelation();
        positionRelation.setPositionTypeId(3); // 合伙人
        positionRelation.setOrganizationCode("B00001"); // 营业所
        when(sfaPositionRelationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(positionRelation);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("B00001")).thenReturn("M00001"); // 父级是营业所
        when(organizationMapper.getOrganizationParentId("M00001")).thenReturn("C00001"); // 父级是分公司

        // Mock审核人员 - 模拟区域经理在职
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(10); // 区域经理
        auditPerson.setEmployeeId("100002"); // 区域总监工号
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // Mock walletQuotaApplication.startQuotaApplyFlow
        doNothing().when(walletQuotaApplication).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证启动了审核流程
        ArgumentCaptor<WalletQuotaApplicationDO> captor = ArgumentCaptor.forClass(WalletQuotaApplicationDO.class);
        verify(walletQuotaApplication, times(1)).startQuotaApplyFlow(captor.capture(), anyString());
        
        // 验证bypassHierarchy为true（跨级）
        WalletQuotaApplicationDO capturedDO = captor.getValue();
        assertTrue(capturedDO.isBypassHierarchy());
    }

    @Test
    void testQuickSend_skipAudit_false_partnerReceiver_regionManagerOnDuty_provinceManagerCrossLevel() {
        // 测试场景：区域经理在岗，省区总监跨级发放合伙人
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(2); // 合伙人
        walletSendDTO.setReceiverKey("100001"); // 合伙人memberKey
        walletSendDTO.setProcessUserId("100004"); // 省区总监

        // Mock员工信息
        SfaEmployeeInfoModel employeeInfo = createEmployeeInfo();
        when(sfaEmployeeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(employeeInfo);
        
        // Mock岗位关系 - 合伙人
        SfaPositionRelationEntity positionRelation = createPositionRelation();
        positionRelation.setPositionTypeId(3); // 合伙人
        positionRelation.setOrganizationCode("M00001"); // 营业所
        when(sfaPositionRelationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(positionRelation);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("M00001")).thenReturn("C00001"); // 父级是分公司
        
        // Mock审核人员 - 省区总监
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(10); // 区域经理
        auditPerson.setEmployeeId("100002"); // 区域总监工号
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // Mock walletQuotaApplication.startQuotaApplyFlow
        doNothing().when(walletQuotaApplication).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证启动了审核流程
        ArgumentCaptor<WalletQuotaApplicationDO> captor = ArgumentCaptor.forClass(WalletQuotaApplicationDO.class);
        verify(walletQuotaApplication, times(1)).startQuotaApplyFlow(captor.capture(), anyString());
        
        // 验证bypassHierarchy为true（跨级）
        WalletQuotaApplicationDO capturedDO = captor.getValue();
        assertTrue(capturedDO.isBypassHierarchy());
    }

    @Test
    void testQuickSend_skipAudit_false_partnerReceiver_cityManagerDistribution() {
        // 测试场景：城市经理发放合伙人
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(2); // 合伙人
        walletSendDTO.setReceiverKey("100001"); // 合伙人memberKey
        walletSendDTO.setProcessUserId("100006"); // 城市经理

        // Mock员工信息
        SfaEmployeeInfoModel employeeInfo = createEmployeeInfo();
        when(sfaEmployeeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(employeeInfo);
        
        // Mock岗位关系 - 合伙人
        SfaPositionRelationEntity positionRelation = createPositionRelation();
        positionRelation.setPositionTypeId(3); // 合伙人
        positionRelation.setOrganizationCode("B00001"); // 营业所
        when(sfaPositionRelationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(positionRelation);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("B00001")).thenReturn("M00001"); // 父级是分公司
        
        // Mock审核人员 - 城市经理
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(10); // 城市经理
        auditPerson.setEmployeeId("100006"); // 城市经理工号
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // Mock walletQuotaApplication.startQuotaApplyFlow
        doNothing().when(walletQuotaApplication).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证启动了审核流程
        ArgumentCaptor<WalletQuotaApplicationDO> captor = ArgumentCaptor.forClass(WalletQuotaApplicationDO.class);
        verify(walletQuotaApplication, times(1)).startQuotaApplyFlow(captor.capture(), anyString());
        
        // 验证bypassHierarchy为true（跨级）
        WalletQuotaApplicationDO capturedDO = captor.getValue();
        assertFalse(capturedDO.isBypassHierarchy());
    }

    @Test
    void testQuickSend_skipAudit_false_organizationReceiver_companyManagerDistribution() {
        // 测试场景：区域总监发放到营业所
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(1); // 组织
        walletSendDTO.setReceiverKey("M00001"); // 营业所ID
        walletSendDTO.setProcessUserId("100002"); // 区域总监
        walletSendDTO.setQuota(new BigDecimal("1000"));

        // Mock费用率检查 - 不超标
        when(walletSearchService.checkRatioOver(eq(1), any(BigDecimal.class), eq("M00001"), isNull())).thenReturn(false);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("M00001")).thenReturn("C00001"); // 父级是分公司
        
        // Mock审核人员 - 区域总监
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(2); // 区域总监
        auditPerson.setEmployeeId("100002"); // 区域总监工号
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证没有启动审核流程（费用率不超标且不跨级）
        verify(walletQuotaApplication, never()).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
    }

    @Test
    void testQuickSend_skipAudit_false_organizationReceiver_provinceManagerCrossLevel() {
        // 测试场景：省区总监发放到营业所
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(1); // 组织
        walletSendDTO.setReceiverKey("M00001"); // 营业所ID
        walletSendDTO.setProcessUserId("100003"); // 省区总监
        walletSendDTO.setQuota(new BigDecimal("1000"));

        // Mock费用率检查 - 超标
        when(walletSearchService.checkRatioOver(eq(1), any(BigDecimal.class), eq("M00001"), isNull())).thenReturn(true);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("M00001")).thenReturn("C00001"); // 父级是分公司
        
        // Mock审核人员 - 省区总监
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(11); // 省区总监
        auditPerson.setEmployeeId("100006"); // 省区总监工号
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // Mock walletQuotaApplication.startQuotaApplyFlow
        doNothing().when(walletQuotaApplication).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证启动了审核流程
        ArgumentCaptor<WalletQuotaApplicationDO> captor = ArgumentCaptor.forClass(WalletQuotaApplicationDO.class);
        verify(walletQuotaApplication, times(1)).startQuotaApplyFlow(captor.capture(), anyString());
        
        // 验证bypassHierarchy为true（跨级）
        WalletQuotaApplicationDO capturedDO = captor.getValue();
        assertTrue(capturedDO.isBypassHierarchy());
    }

    @Test
    void testQuickSend_skipAudit_false_organizationReceiver_headquartersAuditPerson() {
        // 测试场景：总部人员审核（不跨级）
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(1); // 组织
        walletSendDTO.setReceiverKey("M00001"); // 营业所ID
        walletSendDTO.setProcessUserId("100007"); // 总部人员
        walletSendDTO.setQuota(new BigDecimal("1000"));

        // Mock费用率检查 - 超标
        when(walletSearchService.checkRatioOver(eq(1), any(BigDecimal.class), eq("M00001"), isNull())).thenReturn(true);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("M00001")).thenReturn("C00001"); // 父级是分公司
        
        // Mock审核人员 - 总部人员
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(7); // 总部
        auditPerson.setEmployeeId("100007"); // 总部人员工号
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // Mock walletQuotaApplication.startQuotaApplyFlow
        doNothing().when(walletQuotaApplication).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证启动了审核流程
        ArgumentCaptor<WalletQuotaApplicationDO> captor = ArgumentCaptor.forClass(WalletQuotaApplicationDO.class);
        verify(walletQuotaApplication, times(1)).startQuotaApplyFlow(captor.capture(), anyString());
        
        // 验证bypassHierarchy为false（不跨级）
        WalletQuotaApplicationDO capturedDO = captor.getValue();
        assertFalse(capturedDO.isBypassHierarchy());
    }

    @Test
    void testQuickSend_skipAudit_false_organizationReceiver_sameUserAuditPerson() {
        // 测试场景：同一用户审核（不跨级）
        WalletSendDTO walletSendDTO = createWalletSendDTO();
        walletSendDTO.setVerifyAuditFlowCheck(true);
        walletSendDTO.setReceiverType(1); // 组织
        walletSendDTO.setReceiverKey("M00001"); // 营业所ID
        walletSendDTO.setProcessUserId("100002"); // 区域总监
        walletSendDTO.setQuota(new BigDecimal("1000"));

        // Mock费用率检查 - 超标
        when(walletSearchService.checkRatioOver(eq(1), any(BigDecimal.class), eq("M00001"), isNull())).thenReturn(true);

        // Mock组织层级关系
        when(organizationMapper.getOrganizationParentId("M00001")).thenReturn("C00001"); // 父级是分公司
        
        // Mock审核人员 - 同一用户
        CeoBusinessOrganizationPositionRelation auditPerson = createAuditPerson();
        auditPerson.setPositionTypeId(2); // 区域总监
        auditPerson.setEmployeeId("100002"); // 同一用户
        when(auditService.chooseAuditPerson(any())).thenReturn(auditPerson);

        // Mock walletQuotaApplication.startQuotaApplyFlow
        doNothing().when(walletQuotaApplication).startQuotaApplyFlow(any(WalletQuotaApplicationDO.class), anyString());
        
        // 执行测试
        walletApplicationService.quickSend(walletSendDTO);
        
        // 验证启动了审核流程
        ArgumentCaptor<WalletQuotaApplicationDO> captor = ArgumentCaptor.forClass(WalletQuotaApplicationDO.class);
        verify(walletQuotaApplication, times(1)).startQuotaApplyFlow(captor.capture(), anyString());
        
        // 验证bypassHierarchy为false（不跨级）
        WalletQuotaApplicationDO capturedDO = captor.getValue();
        assertFalse(capturedDO.isBypassHierarchy());
    }

    private WantWalletAccountEntity createWantWalletAccountEntity(){
        WantWalletAccountEntity wantWalletAccountEntity = new WantWalletAccountEntity();
        wantWalletAccountEntity.setAccountId(1L);
        wantWalletAccountEntity.setOrganizationId("M00001");
        wantWalletAccountEntity.setOrganizationType("M");
        return wantWalletAccountEntity;
    }

    private WantWalletEntity createWantWalletEntity() {
        WantWalletEntity wantWalletEntity = new WantWalletEntity();
        wantWalletEntity.setId(1L);
        wantWalletEntity.setWalletTypeId(1);
        wantWalletEntity.setQuota(BigDecimal.ZERO);
        wantWalletEntity.setIsFrozen(0);
        return wantWalletEntity;
    }


    // 辅助方法
    private WalletSendDTO createWalletSendDTO() {
        WalletSendDTO dto = new WalletSendDTO();
        dto.setReceiverType(1);
        dto.setReceiverKey("M00001");
        dto.setQuota(new BigDecimal("1000"));
        dto.setProcessUserId("100001");
        dto.setProcessUserName("测试用户");
        dto.setSendWalletType(1);
        dto.setReceiverWalletType(1);
        dto.setRemark("测试备注");
        dto.setVerifyAuditFlowCheck(true);
        return dto;
    }

    private SfaEmployeeInfoModel createEmployeeInfo() {
        SfaEmployeeInfoModel model = new SfaEmployeeInfoModel();
        model.setId(1);
        model.setMemberKey(100001L);
        model.setEmployeeName("测试员工");
        return model;
    }

    private SfaPositionRelationEntity createPositionRelation() {
        SfaPositionRelationEntity entity = new SfaPositionRelationEntity();
        entity.setEmployeeInfoId(1);
        entity.setPositionTypeId(3);
        entity.setOrganizationCode("M00001");
        entity.setStatus(1);
        entity.setDeleteFlag(0);
        return entity;
    }

    private CeoBusinessOrganizationPositionRelation createAuditPerson() {
        CeoBusinessOrganizationPositionRelation relation = new CeoBusinessOrganizationPositionRelation();
        relation.setEmployeeId("100002");
        relation.setEmployeeName("测试审核人");
        relation.setPositionTypeId(2);
        relation.setOrganizationId("C00001");
        return relation;
    }
}
