package com.wantwant.sfa.backend.notify;

import com.wantwant.sfa.backend.Task.NotifyTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/16/下午3:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class NotifyTest {

    @Resource
    private NotifyTask notifyTask;

    @Test
    public void test01(){
        notifyTask.sendWeekly(null);
    }
}
