package com.wantwant.sfa.backend.organization;

import com.wantwant.sfa.backend.Task.OrganizationTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/26/下午3:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrganizationTest {
    @Autowired
    private OrganizationTask organizationTask;

    @Test
    public void test01(){
        organizationTask.execute(null);
    }
}
