package com.wantwant.sfa.backend.policy;

import com.wantwant.sfa.backend.Task.PolicyTask;
import com.wantwant.sfa.backend.policy.service.IPolicyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/22/下午2:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PolicyTest {
    @Autowired
    private IPolicyService policyService;
    @Autowired
    private PolicyTask policyTask;

    @Test
    public void sendPolicy(){
        policyService.onBoardCeoSendPolicy(177222804L,3);
    }

    @Test
    public void testCopy(){
        policyTask.copyPolicy("2025-06");
    }
}
