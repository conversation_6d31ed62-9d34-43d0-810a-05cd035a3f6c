package com.wantwant.sfa.backend.queueTask;

import com.alibaba.fastjson.JSONObject;
import com.wantwant.sfa.backend.rabbitMQ.OnboardBenefitReceiver;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/06/26/下午8:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OnBoardBenefitReceiverTest {
    @Autowired
    private OnboardBenefitReceiver onboardBenefitReceiver;

    @Test
    public void send() throws InterruptedException {
        JSONObject object = new JSONObject();
        object.put("memberKey","177222938");
        object.put("position",2);
        onboardBenefitReceiver.benefit(object);
    }
}
