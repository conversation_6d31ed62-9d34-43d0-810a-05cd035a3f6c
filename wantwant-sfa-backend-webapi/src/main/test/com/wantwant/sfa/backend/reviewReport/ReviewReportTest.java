package com.wantwant.sfa.backend.reviewReport;

import com.wantwant.sfa.backend.Task.ReviewReportTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/02/23/上午10:00
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ReviewReportTest {
    @Autowired
    private ReviewReportTask reviewReportTask;

    @Test
    public void testGenerate(){
        reviewReportTask.execute("2024-11");
    }
}
