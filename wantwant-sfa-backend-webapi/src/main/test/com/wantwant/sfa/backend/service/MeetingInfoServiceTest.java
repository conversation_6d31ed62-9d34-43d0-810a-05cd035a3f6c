package com.wantwant.sfa.backend.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wantwant.sfa.backend.Task.MeetingInfoTask;
import com.wantwant.sfa.backend.Task.NotifyTask;
import com.wantwant.sfa.backend.meeting.request.AttendMeetingRequest;
import com.wantwant.sfa.backend.meeting.request.MeetingFileVO;
import com.wantwant.sfa.backend.meeting.request.MeetingSaveRequest;
import com.wantwant.sfa.backend.meeting.request.MeetingSummarySaveRequest;
import com.wantwant.sfa.backend.meeting.vo.MeetingDetailVO;
import com.wantwant.sfa.backend.meeting.vo.MeetingInfoVO;
import com.wantwant.sfa.backend.meeting.vo.RecordEmpVO;
import com.wantwant.sfa.backend.service.meeting.MeetingInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
* 线下会议信息 测试
*
* @since 2024-02-21
*/
@RunWith(SpringRunner.class)
@SpringBootTest
public class MeetingInfoServiceTest {

	@Autowired
	public MeetingInfoService meetingInfoService;

	@Autowired
	private NotifyTask notifyTask;

	@Autowired
	private MeetingInfoTask meetingInfoTask;

	/**
	 * 根据查询条件返回结果集
	 *
	 * @return java.util.List<generator.com.wantwant.service.entity.dto.MeetingInfoDto>
	 * <AUTHOR>
	 * @since 2018/7/22 0022
	 */
	@Test
	public void selectListBySql() {

	}

	@Test
	public void pushNotice(){
		meetingInfoTask.meetingNotice("2024-09-14");
	}

	@Test
	public void pushDailyNotice(){

		meetingInfoTask.meetingDailyNotice("2024-06-13");
		meetingInfoTask.meetingDailyNotice("2024-06-12");
		meetingInfoTask.meetingDailyNotice("2024-06-11");
		meetingInfoTask.meetingDailyNotice("2024-06-10");
		meetingInfoTask.meetingDailyNotice("2024-06-09");
		meetingInfoTask.meetingDailyNotice("2024-06-08");
		meetingInfoTask.meetingDailyNotice("2024-06-07");
		meetingInfoTask.meetingDailyNotice("2024-06-06");
		meetingInfoTask.meetingDailyNotice("2024-06-05");
		meetingInfoTask.meetingDailyNotice("2024-06-04");
		meetingInfoTask.meetingDailyNotice("2024-06-03");
	}

	@Test
	public void saveInfoTest() {
		MeetingSaveRequest meetingReq = new MeetingSaveRequest();
		meetingReq.setCategory("月会");
		meetingReq.setSubclass("分公司月会");
		meetingReq.setOrganizationId("C782_Z_B");
		meetingReq.setStartTime(LocalDateTime.now());
		meetingReq.setEndTime(LocalDateTime.now().plusDays(1));
		meetingReq.setTopic("2024年01月 月度会议总结");
		meetingReq.setMode("线下");
		meetingReq.setLink("www.huiyi.com");
		meetingReq.setProvince("上海市");
		meetingReq.setCity("市辖区");
		meetingReq.setDistrict("闵行区");
		meetingReq.setStreet("古北");
		meetingReq.setAddress("虹武路旺旺");
		meetingReq.setLeaderPositionId("POS_ZB002_Z");
		meetingReq.setPapers("月报");
		meetingReq.setRemark("备注");
//		meetingReq.setBusinessGroups(Arrays.asList(1,2));
//		meetingReq.setAreaCodes(Arrays.asList("DN_Z","HD_Z"));
//		meetingReq.setRoles(Arrays.asList(5,6,7,9,10));
		meetingReq.setCreateBy("00477312");
		MeetingFileVO fileVO = new MeetingFileVO();
		fileVO.setUrl("https://prd-wzwp-std-oss.oss-cn-shanghai.aliyuncs.com/e21a94443e35c6c9b2dcb666d62bc814");
		fileVO.setFormat("xls");
		fileVO.setName("表格附件");
		fileVO.setSize(2048);
		meetingReq.setFiles(Arrays.asList(fileVO));
		Integer integer = meetingInfoService.saveInfo(meetingReq);
		System.out.println("integer = " + integer);
	}

	@Test
	public void getDetailByInfoIdTest(){
		MeetingDetailVO detail = meetingInfoService.getDetailByInfoId(1, "00477312");
		System.out.println("detail = " + JSON.toJSONString(detail));
	}

	@Test
	public void saveSummaryTest(){
		MeetingSummarySaveRequest request = new MeetingSummarySaveRequest();
		request.setInfoId(1);
		request.setTitle("会议纪要标题");
		request.setContent("会议纪要正文");
		request.setUniformRequirements("统一要求");
		request.setProblemFeedback("问题反馈");
		request.setCreateBy("00441211");
		request.setCommitStatus(0);
		List<MeetingFileVO> picFiles = Lists.newArrayList();
		MeetingFileVO pic = new MeetingFileVO();
		pic.setUrl("https://prd-wzwp-std-oss.oss-cn-shanghai.aliyuncs.com/ceoT/commodity/prd/2023-01-16/227093b0-21d2-b04e-81e4-0185bad119d4.jpeg");
		pic.setSize(512);
		pic.setName("会议图片");
		pic.setFormat("jpeg");
		picFiles.add(pic);
		request.setPicFiles(picFiles);
		List<MeetingFileVO> summaryFiles = Lists.newArrayList();
		MeetingFileVO summary = new MeetingFileVO();
		summary.setUrl("https://prd-wzwp-std-oss.oss-cn-shanghai.aliyuncs.com/ceoT/commodity/prd/2023-01-16/4ae328fa-7c92-b064-22eb-0185bb268a82.png");
		summary.setSize(256);
		summary.setName("附件");
		summary.setFormat("png");
		summaryFiles.add(summary);
		request.setSummaryFiles(summaryFiles);
		List<RecordEmpVO> recordList = Lists.newArrayList();
		RecordEmpVO recordEmpVO = new RecordEmpVO();
		recordEmpVO.setRecordId(1);
		recordEmpVO.setProblem("测试问题1");
		recordEmpVO.setSuggestions("改善意见1");
		recordEmpVO.setRequirements("主管要求1");
		recordEmpVO.setCommitment("本人承诺1");
		recordList.add(recordEmpVO);
		request.setRecordList(recordList);
		Integer integer = meetingInfoService.saveSummary(request);
		System.out.println("integer = " + integer);
	}

	@Test
	public void listByEmpIdTest() {
		List<MeetingInfoVO> list = meetingInfoService.listByEmpId("15140339153");
		System.out.println("meetingInfoVOS = " + JSON.toJSONString(list));
	}

	@Test
	public void attendTest() {
		AttendMeetingRequest request = new AttendMeetingRequest();
		request.setEmployeeId("15140339153");
		request.setInfoId(1);
		request.setReceiveStatus(2);
		request.setRejectReasons("拒绝");
		meetingInfoService.attend(request);
	}

	@Test
	public void notifyTaskTest(){
		notifyTask.meetingGeTui("");
	}


	@Test
	public void autoComplete(){
		meetingInfoTask.autoComplete("2024-06-04");
	}

	@Test
	public void meetingMonthNoticeTest(){
		meetingInfoTask.meetingMonthNotice(null);
	}
}
