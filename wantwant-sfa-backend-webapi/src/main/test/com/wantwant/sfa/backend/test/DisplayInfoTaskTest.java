package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.Task.DisplayTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/24/上午7:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DisplayInfoTaskTest {
    @Resource
    private DisplayTask displayTask;

    @Test
    public void test01(){
        displayTask.operationAutoProcess(null);
    }

    @Test
    public void test02(){
        displayTask.displayBusinessRejectProcessTask(null);
    }
}
