package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.Task.DisplayTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/30/下午1:44
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DisplayTaskTest {
    @Resource
    private DisplayTask displayTask;

    @Test
    public void testSend(){
        displayTask.displayGrantTask(null);
    }

    @Test
    public void testTag(){
        displayTask.displayUnGrantTag(null);
    }
}
