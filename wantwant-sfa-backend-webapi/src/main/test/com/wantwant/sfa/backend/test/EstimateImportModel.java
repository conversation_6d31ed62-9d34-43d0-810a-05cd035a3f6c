package com.wantwant.sfa.backend.test;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/上午9:54
 */
@Data
public class EstimateImportModel {
    @Excel(name="month")
    private String month;
    @Excel(name="area")
    private String area;
    @Excel(name="company")
    private String company;
    @Excel(name="sku")
    private String sku;
    @Excel(name="estimateQuantity")
    private String estimateQuantity;
    @Excel(name="estimatePrice")
    private String estimatePrice;
    @Excel(name="auditQuantity")
    private String auditQuantity;
    @Excel(name="auditPrice")
    private String auditPrice;
}
