package com.wantwant.sfa.backend.test;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.estimate.enums.EstimateResultEnum;
import com.wantwant.sfa.backend.estimate.model.EstimateProcessModel;
import com.wantwant.sfa.backend.estimate.model.SfaEstimateApprovalModel;
import com.wantwant.sfa.backend.estimate.model.SfaEstimateApprovalRecordModel;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.estimate.EstimateProcessMapper;
import com.wantwant.sfa.backend.mapper.estimate.SfaEstimateApprovalMapper;
import com.wantwant.sfa.backend.mapper.estimate.SfaEstimateApprovalRecordMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/05/06/上午9:50
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class EstimateImportTest {
    @Autowired
    private SfaEstimateApprovalMapper sfaEstimateApprovalMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private SfaEstimateApprovalRecordMapper sfaEstimateApprovalRecordMapper;
    @Autowired
    private EstimateProcessMapper estimateProcessMapper;

    @Test
    public void export(){
        List<com.wantwant.sfa.backend.test.EstimateImportModel> list = new ArrayList<>();
        ImportParams params = new ImportParams();
        File file = new File("/Users/<USER>/Desktop/销售预估历史数据.xlsx");

        list = ExcelImportUtil.importExcel(file, com.wantwant.sfa.backend.test.EstimateImportModel.class, params);

        HashMap<String,SfaEstimateApprovalModel> approvalModelHashMap= new HashMap<>();

        list.stream().forEach(e -> {
            // 获取大区，分公司的信息
            CeoBusinessOrganizationPositionRelation company = selectByName(e.getCompany());
            String organizationParentId = company.getOrganizationParentId();
            CeoBusinessOrganizationPositionRelation area = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",organizationParentId).eq("channel",3));

            SfaEstimateApprovalModel companyApproval = null;
            if(!approvalModelHashMap.containsKey(e.getMonth()+"_"+e.getCompany()+"_"+e.getArea())){
                // 创建分公司申请记录
                companyApproval = createApproval(e.getMonth(), company, area, 2, 2);
                approvalModelHashMap.put(e.getMonth()+"_"+e.getCompany()+"_"+e.getArea(),companyApproval);
            }else{
                companyApproval = approvalModelHashMap.get(e.getMonth()+"_"+e.getCompany()+"_"+e.getArea());
            }

            createApprovalRecord(companyApproval,e,company.getOrganizationId());
        });

        approvalModelHashMap.forEach((k,v) -> {
            CeoBusinessOrganizationPositionRelation company = selectByName(k.split("_")[1]);
            // 创建分公司申请记录
            SfaEstimateApprovalModel areaApprovalModel = createApproval(k.split("_")[0], company,  null, 3, EstimateResultEnum.PASS.getCode());
            // 获取分公司记录
            List<SfaEstimateApprovalRecordModel> recordModelList = sfaEstimateApprovalRecordMapper.selectList(new QueryWrapper<SfaEstimateApprovalRecordModel>().eq("estimate_approval_id", v.getId()));

            // 修改链接
            v.setNextId(areaApprovalModel.getId());
            sfaEstimateApprovalMapper.updateById(v);

            recordModelList.forEach(e -> {
                SfaEstimateApprovalRecordModel recordModel = new SfaEstimateApprovalRecordModel();
                BeanUtils.copyProperties(e,recordModel,"id","estimateApprovalId");
                recordModel.setEstimateApprovalId(areaApprovalModel.getId());
                recordModel.setEstimateQuantity(e.getAuditQuantity());
                recordModel.setEstimatePrice(e.getAuditPrice());
                recordModel.setAuditQuantity(e.getAuditQuantity());
                recordModel.setAuditPrice(e.getAuditPrice());
                sfaEstimateApprovalRecordMapper.insert(recordModel);
            });

            // 流程创建
            saveProcess(k.split("_")[0],company.getOrganizationId());
        });

    }

    private void saveProcess(String month, String company) {
        EstimateProcessModel processModel = new EstimateProcessModel();
        processModel.setIsFinish(1);
        processModel.setProcessStep(5);
        processModel.setOrganizationId(company);
        processModel.setStatus(1);
        processModel.setMonth(month);
        estimateProcessMapper.insert(processModel);
    }

    private void createApprovalRecord(SfaEstimateApprovalModel companyApproval, com.wantwant.sfa.backend.test.EstimateImportModel e, String organizationId) {
        SfaEstimateApprovalRecordModel sfaEstimateApprovalRecordModel = new SfaEstimateApprovalRecordModel();
        sfaEstimateApprovalRecordModel.setAuditPrice(new BigDecimal(e.getAuditPrice()));
        sfaEstimateApprovalRecordModel.setAuditQuantity(new BigDecimal(e.getAuditQuantity()));
        sfaEstimateApprovalRecordModel.setSaleEstimateNo("0");
        sfaEstimateApprovalRecordModel.setStatus(1);
        sfaEstimateApprovalRecordModel.setEstimateApprovalId(companyApproval.getId());
        sfaEstimateApprovalRecordModel.setSku(e.getSku());
        sfaEstimateApprovalRecordModel.setEstimatePrice(new BigDecimal(e.getEstimatePrice()));
        sfaEstimateApprovalRecordModel.setEstimateQuantity(new BigDecimal(e.getEstimateQuantity()));
        sfaEstimateApprovalRecordModel.setOrganizationId(organizationId);
        sfaEstimateApprovalRecordModel.setIsReserved(0);
        sfaEstimateApprovalRecordMapper.insert(sfaEstimateApprovalRecordModel);
    }

    private SfaEstimateApprovalModel createApproval(String month, CeoBusinessOrganizationPositionRelation company,
                                CeoBusinessOrganizationPositionRelation area, int processStep, int result) {
        SfaEstimateApprovalModel approvalModel = new SfaEstimateApprovalModel();
        approvalModel.setTimes(1);
        approvalModel.setIsCurrent(1);
        approvalModel.setIsSubmit(1);
        approvalModel.setEstimateType(0);
        approvalModel.setUpdateTime(LocalDateTime.now());
        approvalModel.setIsCurrent(1);
        approvalModel.setResult(result);
        approvalModel.setStatus(1);
        approvalModel.setCreateTime(LocalDateTime.now());
        approvalModel.setProcessStep(processStep);
        if(Objects.nonNull(area)){
            approvalModel.setValidPositionId(area.getPositionId());
        }

        approvalModel.setSaleEstimateNo("0");
        approvalModel.setMonth(month);
        approvalModel.setProcessStep(processStep);
        approvalModel.setApplyPositionId(company.getPositionId());
        sfaEstimateApprovalMapper.insert(approvalModel);
        return approvalModel;
    }


    public CeoBusinessOrganizationPositionRelation selectByName(String name){

        CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationEntity>()
                .eq("organization_name", name)
                .eq("channel", 3)
        );

        if(Objects.isNull(ceoBusinessOrganizationEntity)){
            log.error("查询失败，name：{}",name);
            return null;
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", ceoBusinessOrganizationEntity.getOrganizationId())
        );

        return ceoBusinessOrganizationPositionRelation;
    }
}
