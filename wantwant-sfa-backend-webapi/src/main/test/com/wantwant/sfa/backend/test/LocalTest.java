package com.wantwant.sfa.backend.test;

import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/02/26/下午3:59
 */
public class LocalTest {

    @Test
    public void test01(){
        LocalDate currentDate = LocalDate.of(2023, 12, 26);
        int currentQuarter = (currentDate.getMonthValue() + 2) / 3;
        LocalDate currentQuarterFirstMonth = LocalDate.of(currentDate.getYear(), currentQuarter * 3 - 2, 1);

        LocalDate prevQuarterFirstMonth = currentQuarterFirstMonth.minusMonths(3L);
        int quarter = (prevQuarterFirstMonth.getMonthValue() + 2) / 3;


        System.out.println(prevQuarterFirstMonth.getYear() +"-Q"+quarter);
    }


    @Test
    public void test02(){
        String season  = "2023-Q4";

        String quater = season.split("Q")[1];
        System.out.println(quater);

        String year = season.split("-")[0];

        LocalDate currentDate = LocalDate.of( Integer.parseInt(year), Integer.parseInt(quater) * 3, 1);

        System.out.println(currentDate.withDayOfMonth(currentDate.getMonth().length(currentDate.isLeapYear())));
    }
}
