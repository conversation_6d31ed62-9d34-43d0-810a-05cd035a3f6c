package com.wantwant.sfa.backend.test;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.controller.FreeSampleController;
import com.wantwant.sfa.backend.freeSample.request.FreeSampleListRequest;
import com.wantwant.sfa.backend.freeSample.vo.FreeSamplePage;
import com.wantwant.sfa.backend.labels.mapStruct.OrderDailyMapping;
import com.wantwant.sfa.backend.labels.model.CustomerDailyOrderStats;
import com.wantwant.sfa.backend.labels.service.CustomerLabelJobService;
import com.wantwant.sfa.backend.labels.service.CustomerLabelsService;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.model.OrderDailyStats;
import com.wantwant.sfa.backend.model.attendanceTask.Attendance;
import com.wantwant.sfa.backend.service.impl.OfflineExportServiceImpl;
import com.wantwant.sfa.backend.util.QcloudExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@SpringBootTest
@Slf4j
class MainTest {

//  @Autowired private StockMapper stockMapper;
  @Autowired private FreeSampleRecordMapper freeSampleRecordMapper;

  @Test
  public void test2() {

    String random = RandomStringUtils.random(1009, 5, 129, true, true);
    System.out.println(random);
  }

  @Test
  public void testMapper() {
    //    VisitIdRequest visitIdRequest = new VisitIdRequest();
    //    visitIdRequest.setVisitId(122548);
    //    visitIdRequest.setPage(10);
    //    visitIdRequest.setRows(10);
    //
    //    stockMapper.stockAndLastStock(visitIdRequest);

    List<FreeSamplePage> list = freeSampleRecordMapper.freeSampleList(new FreeSampleListRequest());
    log.info("result = {}", JSONObject.toJSONString(list));
  }

  @Autowired private AttendanceMapper attendanceMapper;
  @Autowired private CeoOrderHeaderMapper ceoOrderHeaderMapper;

  @Test
  public void test4() {}

  @Test
  public void testMapper2222() {

    List<Attendance> list =
        attendanceMapper.selectList(
            new QueryWrapper<Attendance>()
                .gt("create_time", LocalDateTime.of(2020, 05, 05, 00, 00, 01)));
    for (Attendance attendance : list) {
      int count = 0;
      BigDecimal amount = BigDecimal.ZERO;
      LocalDateTime startTime = LocalDateTimeUtils.getDayStart(attendance.getCreateTime());
      LocalDateTime endTime = LocalDateTimeUtils.getDayStart(attendance.getCreateTime());

//      List<String> memberKeys =
//          attendanceExcelTaskMapper.findEmployeeMemberKey(
//              startTime, endTime, attendance.getMemberId());
//
//      for (String memberKey : memberKeys) {
//        // List<CeoOrderHeader> list1 =  ceoOrderHeaderMapper.get(startTime, endTime, memberKey);
////        ceoOrderHeaderMapper.get(startTime, endTime, memberKey);
//        // 如果list 不为空 count++ amount+orderAmount
//      }
//      log.info("result = {}", memberKeys);
    }
  }

//  @Test
//  public void setStockMapper() {
//    ceoOrderHeaderMapper.get(LocalDateTime.now(), LocalDateTime.now(), "1");
//  }

  @Test
  public void DateTime() throws ParseException {

    // 设置转换的日期格式
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    String a = "2018-05-10";
    String b = "2018-11-10";

    // 开始时间
    Date startDate = sdf.parse(a);
    // 结束时间
    Date endDate = sdf.parse(b);

    // 得到相差的天数 betweenDate
    long betweenDate = (endDate.getTime() - startDate.getTime()) / (60 * 60 * 24 * 1000);

    // 打印控制台相差的天数
    System.out.println(betweenDate);
  }

  @Autowired private OfflineExportServiceImpl offlineExportServiceImpl;
  @Autowired private FreeSampleController



          freeSampleController;
  @Autowired private QcloudExcelUtils qcloudExcelUtils;

  //    @Test
  //    public void testOffline(){
  //
  //        FreeSampleListRequest request =  new FreeSampleListRequest();
  //        request.setStartTime("2020-06-01");
  //        request.setEndTime("2020-06-30");
  //        Workbook workbook =  freeSampleController.exportToWb(request);
  //        log.info(qcloudExcelUtils.uploadExcel(workbook,"testExcel"+System.currentTimeMillis()));
  //
  //    }

  //    @Autowired
  //    private VisitMapper visitMapper;
  //    @Autowired
  //    private VisitService visitService;
  //    @Test
  //    public void testOOO(){
  //        VisitListParam visitListParam = new VisitListParam();
  //        visitListParam.setBeginTime("2020-05-20");
  //        visitListParam.setEndTime("2020-05-20");
  ////        List<VisitListDTO> sqlResult = visitMapper.queryVisitList(visitListParam);
  //        List<VisitListExport> sqlResult = visitService.visitListExport(visitListParam);
  //        log.info(JSONObject.toJSONString(sqlResult));
  //
  //    }

  @Autowired private CustomerLabelsService customerLabelsService;

  @Autowired private OrderDailyMapping orderDailyMapping;

  @Test
  public void testLabel() {
    //        customerLabelsService.collectDate(null,30);
    OrderDailyStats statsOrder = new OrderDailyStats();
    statsOrder.setDay(1);
    statsOrder.setMonth(1);
    statsOrder.setYear(1);
    statsOrder.setAmount(BigDecimal.ONE);
    statsOrder.setSaleAmount(BigDecimal.ONE);
    statsOrder.setProAt(LocalDateTime.now());
    CustomerDailyOrderStats cu = orderDailyMapping.mapping(statsOrder);
    LocalDate a = LocalDateTime.now().toLocalDate();
    System.out.println(JSONObject.toJSONString(cu));
  }

  @Autowired private CustomerLabelJobService customerLabelJobService;

  @Test
  public void testlabels() {
    customerLabelJobService.customerLabelsJob();
  }
}
