package com.wantwant.sfa.backend.test;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.entity.SaleEstimateEntity;
import com.wantwant.sfa.backend.entity.SaleEstimateItemEntity;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.RegionMapper;
import com.wantwant.sfa.backend.mapper.estimate.EstimateOrderMapper;
import com.wantwant.sfa.backend.market.model.MarketImportModel;
import com.wantwant.sfa.backend.market.model.RegionModel;
import com.wantwant.sfa.backend.model.ApplyMemberPo;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/12/28/下午2:44
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MapperTest {
    @Autowired
    private EstimateOrderMapper estimateOrderMapper;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private RegionMapper regionMapper;

    @Test
    public void testSelect(){
        SaleEstimateEntity entity = estimateOrderMapper.getSaleEstimateBySaleEstimateNo("GD21122800010");
        List<SaleEstimateItemEntity> itemEntityList = entity.getItemEntityList();
        itemEntityList.forEach(e ->{
            System.out.println(e.getPtKey());
        });
    }

    @Test
    public void testMapper(){
        ApplyMemberPo applyMemberPo = applyMemberMapper.selectOne(new QueryWrapper<ApplyMemberPo>().eq("user_mobile", "19909900801"));

        System.out.println(applyMemberPo.getPicUrl());
    }

//
//     @Test
//     @Transactional
//     public void testImport(){
//         List<RegionModel> list = new ArrayList<>();
//         ImportParams params = new ImportParams();
//         File file = new File("/Users/<USER>/Desktop/全国行政区4级划分表-省市县乡街道大全表.xlsx");
//
//         list = ExcelImportUtil.importExcel(file, RegionModel.class, params);
//
//
//         list.forEach(e -> {
//             String parentCode = regionMapper.selectOrgCode(e.getParentName().trim(),e.getCityName().trim());
//
//             regionMapper.insert(e.getCode(),e.getName(),4,parentCode);
//         });
//
//     }

     @Test
     @Transactional
     public void testInsert(){

         ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(6726);
         applyMemberPo.setCeoType(1);
         applyMemberMapper.updateById(applyMemberPo);
     }

}
