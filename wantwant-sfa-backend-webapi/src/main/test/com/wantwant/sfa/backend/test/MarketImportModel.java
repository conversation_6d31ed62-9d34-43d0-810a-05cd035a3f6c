package com.wantwant.sfa.backend.test;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/02/下午2:10
 */
@Data
public class MarketImportModel {
    @Excel(name="marketName")
    private String marketName;
    @Excel(name="smallMarketName")
    private String smallMarketName;
    @Excel(name="area")
    private String area;
    @Excel(name="company")
    private String company;
    @Excel(name="province")
    private String province;
    @Excel(name="city")
    private String city;
    @Excel(name="district")
    private String district;
    @Excel(name="village")
    private String village;
}
