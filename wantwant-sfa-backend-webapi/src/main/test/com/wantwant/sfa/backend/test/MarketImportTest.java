package com.wantwant.sfa.backend.test;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.market.MarketV2Mapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketRegionRelationMapper;
import com.wantwant.sfa.backend.mapper.market.SmallMarketV2Mapper;
import com.wantwant.sfa.backend.market.entity.MarketV2Entity;
import com.wantwant.sfa.backend.market.entity.SmallMarketRegionRelationEntity;
import com.wantwant.sfa.backend.market.entity.SmallMarketV2Entity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/02/下午2:07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class MarketImportTest {
    @Autowired
    private MarketV2Mapper marketV2Mapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SmallMarketV2Mapper smallMarketV2Mapper;
    @Autowired
    private SmallMarketRegionRelationMapper smallMarketRegionRelationMapper;

    @Test
    public void test01(){
        ImportParams params = new ImportParams();
        File file = new File("/Users/<USER>/Desktop/南京分行政区划-大小标划分.xlsx");

        List<MarketImportModel> list = ExcelImportUtil.importExcel(file, MarketImportModel.class, params);

        list.forEach(e -> {
            // 检查大标市场是否创建
            MarketV2Entity marketV2Entity = marketV2Mapper.selectOne(new QueryWrapper<MarketV2Entity>().eq("market_name", e.getMarketName().trim()));
            if(Objects.isNull(marketV2Entity)){
                String company = e.getCompany();
                String organizationIdByName = organizationMapper.getOrganizationIdByName(company, 3,3);
                marketV2Entity = new MarketV2Entity();
                marketV2Entity.setCompanyCode(organizationIdByName);
                marketV2Entity.setDeleteFlag(0);
                marketV2Entity.setUpdateTime(LocalDateTime.now());
                marketV2Entity.setUpdateUserId("ROOT");
                marketV2Entity.setMarketName(e.getMarketName().trim());
                marketV2Entity.setCreateUserId("ROOT");
                marketV2Entity.setProvinceCode(e.getProvince().trim());
                marketV2Entity.setCityCode(e.getCity().trim());
                marketV2Entity.setCreateTime(LocalDateTime.now());
                marketV2Mapper.insert(marketV2Entity);
            }

            // 检查小标
            SmallMarketV2Entity smallMarketV2Entity = smallMarketV2Mapper.selectOne(new QueryWrapper<SmallMarketV2Entity>().eq("small_market_name", e.getSmallMarketName().trim()));
            if(Objects.isNull(smallMarketV2Entity)){
                smallMarketV2Entity = new SmallMarketV2Entity();
                smallMarketV2Entity.setSmallMarketName(e.getSmallMarketName().trim());
                smallMarketV2Entity.setMarketId(marketV2Entity.getId());
                smallMarketV2Entity.setDeleteFlag(0);
                smallMarketV2Entity.setCreateUserId("ROOT");
                smallMarketV2Entity.setCreateTime(LocalDateTime.now());
                smallMarketV2Entity.setUpdateUserId("ROOT");
                smallMarketV2Entity.setUpdateTime(LocalDateTime.now());
                smallMarketV2Mapper.insert(smallMarketV2Entity);
            }

            // 小标四级地
            SmallMarketRegionRelationEntity smallMarketRegionRelationEntity = new SmallMarketRegionRelationEntity();
            smallMarketRegionRelationEntity.setDeleteFlag(0);
            smallMarketRegionRelationEntity.setDistrictCode(e.getDistrict());
            smallMarketRegionRelationEntity.setVillageCode(e.getVillage());
            smallMarketRegionRelationEntity.setSmallMarketId(smallMarketV2Entity.getId());
            smallMarketRegionRelationMapper.insert(smallMarketRegionRelationEntity);
        });
    }
}
