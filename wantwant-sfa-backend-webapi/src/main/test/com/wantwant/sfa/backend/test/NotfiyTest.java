package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.display.dto.DisplayProcessInfoDTO;
import com.wantwant.sfa.backend.display.dto.ParentOrganizationDTO;
import com.wantwant.sfa.backend.display.enums.ProcessResult;
import com.wantwant.sfa.backend.display.enums.ProcessType;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.NotifyContentMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.afterSales.AfterSalesInfoMapper;
import com.wantwant.sfa.backend.mapper.afterSales.AfterSalesProcessDetailMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayInfoMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayProcessDetailMapper;
import com.wantwant.sfa.backend.mapper.display.DisplayRuleMapper;
import com.wantwant.sfa.backend.model.afterSales.AfterSalesProcessDetailPO;
import com.wantwant.sfa.backend.model.display.DisplayProcessDetailPO;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.util.CommonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/07/07/上午11:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class NotfiyTest {
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyContentMapper notifyContentMapper;

    @Autowired
    private DisplayRuleMapper displayRuleMapper;

    @Autowired
    private DisplayInfoMapper displayInfoMapper;

    @Resource
    private CeoBusinessOrganizationPositionRelationMapper relationMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private DisplayProcessDetailMapper displayProcessDetailMapper;

    @Autowired
    private AfterSalesInfoMapper afterSalesInfoMapper;
    @Autowired
    private AfterSalesProcessDetailMapper afterSalesProcessDetailMapper;

    @Test
    public void test01(){
        List<DisplayProcessInfoDTO> list = displayInfoMapper.selectErrProcess();
        list.forEach(p -> {
            ParentOrganizationDTO pod = relationMapper.getParentOrganizationByMemberkey(String.valueOf(p.getPartnerMemberKey()), p.getProductGroupId1());
            DisplayProcessDetailPO detailPO = new DisplayProcessDetailPO();
            detailPO.setId(p.getDetailId());
            if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getDepartmentEmployeeId()) && p.getProcessType() <2) {
                detailPO.setReviewerId(pod.getDepartmentEmployeeId());
                detailPO.setReviewerName(pod.getAreaOrganizationName() + "-" + pod.getCompanyOrganizationName() + "-" + pod.getDepartmentOrganizationName() + "-区域经理-" + pod.getDepartmentEmployeeName());
                detailPO.setOrganizationId(pod.getDepartmentOrganizationId());
            } else {
                //指定分公司审批人
                chooseCompany(detailPO, pod,p.getProductGroupId1(),p.getProcessType());
            }
            detailPO.setComment("正确数据");
            displayProcessDetailMapper.updateById(detailPO);
        });

    }

    /**
     * 选择分公司审批人
     *
     * @param po
     * @param pod 合伙人组织信息
     * @return: void
     * @date: 10/31/22 2:41 PM
     */
    private void chooseCompany(DisplayProcessDetailPO po, ParentOrganizationDTO pod,Integer businessGroupId,Integer processType) {
        if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getCompanyEmployeeId()) && processType <2) {
            po.setReviewerId(pod.getCompanyEmployeeId());
            po.setReviewerName(pod.getAreaOrganizationName() + "-" + pod.getCompanyOrganizationName() + "-区域总监-" + pod.getCompanyEmployeeName());
            po.setOrganizationId(pod.getCompanyOrganizationId());
        } else {
            //指定督导审批人
            chooseArea(po, pod,businessGroupId);
        }
    }

    private void chooseArea(DisplayProcessDetailPO po, ParentOrganizationDTO pod,Integer productGroupId) {
        po.setProcessType(ProcessType.AREA_AUDIT.getProcessCode());
        po.setProcessResult(ProcessResult.WAIT_AUDIT.getResultCode());
        if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getAreaEmployeeId())) {
            po.setReviewerId(pod.getAreaEmployeeId());
            po.setReviewerName(pod.getAreaOrganizationName() + "-督导-" + pod.getAreaEmployeeName());
            po.setOrganizationId(pod.getAreaOrganizationId());
        } else {
            String zb = organizationMapper.selectOrganizationByProductGroupAndType(productGroupId, "zb");
            //大区没有督导审核,张远审核
            po.setReviewerId("00441211");
            po.setReviewerName("总部-运营-张远");
            po.setOrganizationId(zb);
        }
    }


    @Test
    public void test02() {
        List<DisplayProcessInfoDTO> list = afterSalesInfoMapper.selectErrProcess();
        list.forEach(p -> {
            ParentOrganizationDTO pod = relationMapper.getParentOrganizationByMemberkey(String.valueOf(p.getPartnerMemberKey()), p.getProductGroupId1());
            AfterSalesProcessDetailPO detailPO = new AfterSalesProcessDetailPO();
            detailPO.setId(p.getDetailId());
            detailPO.setComment("正确数据");
            if (p.getProcessType() == 1){
                chooseCompanyAfter(detailPO, pod,p.getProductGroupId1(),1);
            }else if (p.getProcessType() == 2){
                chooseCompanyAfter(detailPO, pod,p.getProductGroupId1(),2);
            }
            afterSalesProcessDetailMapper.updateById(detailPO);
        });


    }

    private void chooseCompanyAfter(AfterSalesProcessDetailPO detailPO, ParentOrganizationDTO pod, Integer productGroupId,Integer processType) {
        if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getCompanyEmployeeId()) && processType<2) {
            detailPO.setReviewerId(pod.getCompanyEmployeeId());
            detailPO.setReviewerName(pod.getAreaOrganizationName()+"-"+pod.getCompanyOrganizationName()+"-区域总监-"+pod.getCompanyEmployeeName());
            detailPO.setOrganizationId(pod.getCompanyOrganizationId());
        }else if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getAreaEmployeeId())){
            detailPO.setReviewerId(pod.getAreaEmployeeId());
            detailPO.setReviewerName(pod.getAreaOrganizationName() +"-督导-"+pod.getAreaEmployeeName());
            detailPO.setOrganizationId(pod.getAreaOrganizationId());
        }else {
            String zb = organizationMapper.selectOrganizationByProductGroupAndType(productGroupId, "zb");
            //大区没有督导审核,张远审核
            detailPO.setReviewerId("00441211");
            detailPO.setReviewerName("总部-运营-张远");
            detailPO.setOrganizationId(zb);
        }
    }

}
