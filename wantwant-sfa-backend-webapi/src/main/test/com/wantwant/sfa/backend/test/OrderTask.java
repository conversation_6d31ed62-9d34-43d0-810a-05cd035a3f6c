package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.Task.SynExceptionOrderTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/08/26/上午11:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderTask {
    @Autowired
    private SynExceptionOrderTask synExceptionOrderTask;

    @Test
    public void testSyn(){
        synExceptionOrderTask.syn();
    }
}
