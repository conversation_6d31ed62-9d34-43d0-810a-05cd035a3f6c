package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.interview.util.ProductionCategoryBitMapUtil;
import org.junit.Test;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/12/07/下午4:11
 */
public class ProductionCategoryBitMapTest {

    @Test
    public void test01(){
        List<Integer> integers = Arrays.asList(1);
        int i = ProductionCategoryBitMapUtil.calculateBin(integers);
        System.out.println(i);
    }

    @Test
    public void test02(){
        List<Integer> categoryId = ProductionCategoryBitMapUtil.getCategoryId(8);
    }

    @Test
    public void test03(){

        System.out.println(LocalDate.now().getYear());


    }
}
