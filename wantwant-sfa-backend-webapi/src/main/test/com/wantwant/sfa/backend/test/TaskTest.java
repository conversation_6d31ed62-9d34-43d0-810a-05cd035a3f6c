package com.wantwant.sfa.backend.test;

import com.alibaba.fastjson.JSONObject;
import com.wantwant.sfa.backend.Task.*;
import com.wantwant.sfa.backend.gold.model.GoldImportErrModel;
import com.wantwant.sfa.backend.interview.strategy.impl.ZWResignStrategyImpl;
import com.wantwant.sfa.backend.rabbitMQ.OnboardBenefitReceiver;
import com.wantwant.sfa.backend.rabbitMQ.config.GoldImportErrMsgTopicRabbitConfig;
import com.wantwant.sfa.backend.rabbitMQ.config.OnboardBenefitTopicRabbitConfig;
import com.wantwant.sfa.backend.service.InfoCenterTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/03/28/上午11:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TaskTest {
    @Autowired
    private ActivityQuotaTask activityQuotaTask;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private OnboardBenefitReceiver onboardBenefitReceiver;
    @Autowired
    private ThirdpartyOnboardProcessTask thirdpartyOnboardProcessTask;
    @Autowired
    private MobilizationTask mobilizationTask;
    @Autowired
    private ProbationCheckTask probationCheckTask;
    @Autowired
    private CategoryTask categoryTask;
    @Autowired
    private ZWResignStrategyImpl zwResignStrategy;
    @Autowired
    private MarketVisitCheckTask marketVisitCheckTask;
    @Autowired
    private InfoCenterTaskService infoCenterTaskService;
    @Autowired
    private EmployeeEliminateTask employeeEliminateTask;
    @Autowired
    private LeaveAuditTask leaveAuditTask;
    @Autowired
    private MessageTask messageTask;
    @Autowired
    private JobPositionTask jobPositionTask;
    @Autowired
    private NotificationTask notificationTask;
    @Autowired
    private WorkReportInitTask workReportInitTask;
    private EliminateAutoProcessTask eliminateAutoProcessTask;

    @Test
    public void test01(){
        activityQuotaTask.transfer("2023-11-01");
    }

    @Test
    public void test08(){
//        zwResignStrategy.closeActivity("POS_7356_Z","C18149_Z");
    }

    @Test
    public void test02(){

        JSONObject obj = new JSONObject();
        obj.put("memberKey", "177221710");

        for(int i=0; i< 3; i++){
            rabbitTemplate.convertAndSend(OnboardBenefitTopicRabbitConfig.onboardBenefitExchange,
                    OnboardBenefitTopicRabbitConfig.onboardBenefit , obj);
        }


         System.out.println("111");
    }

    @Test
    public void test03(){
        JSONObject obj = new JSONObject();
        obj.put("memberKey", "177222523");
        obj.put("position",4);

//        onboardBenefitReceiver.benefit(obj);

    }

    @Test
    public void test04(){
        thirdpartyOnboardProcessTask.excute("");
    }

    @Test
    public void test05(){
        mobilizationTask.offBoardMessage("2022-07-10");
    }

    @Test
    public void test06(){
        probationCheckTask.check(null);
    }

    @Test
    public void test07(){
        categoryTask.excute("");
    }

    @Test
    public void testRabbit() {

        List<GoldImportErrModel> errList = new ArrayList<>();
        GoldImportErrModel model = new GoldImportErrModel();
        model.setErrMsg("1111");
        model.setMobile("1111");
        model.setEmployeeName("test");
        errList.add(model);

        String jsonStrong = "[{\"amount\":10.00,\"applyType\":\"文宣品补贴\",\"employeeInfoId\":1632,\"errMsg\":\"客户信息获取失败\",\"goldDetailId\":80,\"month\":\"2022-09\"}]";

        rabbitTemplate.convertAndSend(
                GoldImportErrMsgTopicRabbitConfig.goldImportErrMsgExchange,
                GoldImportErrMsgTopicRabbitConfig.goldImportErrMsg,
                jsonStrong);


        System.out.println("111");

    }
    
    public void test09(){
        marketVisitCheckTask.check(null);
    }




    @Test
    public void test10(){
        infoCenterTaskService.doPublish(471);
    }

    @Test
    public void test11(){
        employeeEliminateTask.eliminate("2023-02");
    }

    @Test
    public void test12(){
        leaveAuditTask.leaveAudit(null);
    }


    @Test
    public void test13(){
        messageTask.excute("2022-11-23");
    }


    @Test
    public void testJobHandler(){
        ReturnT<String> stringReturnT = jobPositionTask.jobPosition("2025-06-10");
    }

    @Test
    public void testSendMessage(){
        notificationTask.agentNotify(null);
    }

    @Test
    public void testInitWorkReport() {
        workReportInitTask.workReportInit("2024-04-29");
    }
}
