package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.service.filterChain.TargetFilterChainComponent;
import com.wantwant.sfa.backend.service.filterChain.TargetRule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/03/29/下午4:10
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestComponent {

    @Autowired
    private TargetFilterChainComponent targetFilterChainComponent;


    @Test
    public void test01(){
        TargetRule target = targetFilterChainComponent.getTarget(new BigDecimal(-24), targetFilterChainComponent.getItemSupplyTotalYearOnYearChain());


        System.out.println(target.getTargetName());
    }
}
