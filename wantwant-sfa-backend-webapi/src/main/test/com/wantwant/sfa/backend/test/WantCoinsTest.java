package com.wantwant.sfa.backend.test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.Task.PenaltyAttendanceTask;
import com.wantwant.sfa.backend.activityQuota.model.CeoQuotaModel;
import com.wantwant.sfa.backend.activityQuota.model.CeoSmallFreeAccountModel;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.activityQuota.service.impl.PenaltyService;
import com.wantwant.sfa.backend.interview.strategy.impl.ZWResignStrategyImpl;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/14/上午10:31
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WantCoinsTest {
    @Autowired
    private PenaltyService penaltyService;
    @Autowired
    private ZWResignStrategyImpl zwResignStrategy;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private PenaltyAttendanceTask penaltyAttendanceTask;

    // 特批汰换扣款
    @Test
    public void testEliminatePenalty(){
//        penaltyService.eliminatePenalty(356);
    }

    // 测试追缴
    @Test
    public void testPressPenalty(){
        penaltyService.pressPenalty("C18881_Z",11,false);
    }

    // 旺铺接口联通性测试
    @Test
    public void testQueryDeptQuota(){
        List<CeoQuotaModel> ceoQuotaModels = activityQuotaConnectorUtil.queryDeptQuota(177221570);
        System.out.println(ceoQuotaModels);
    }





    // 离职回收造旺币测试
    @Test
    public void testRetire(){

        CeoBusinessOrganizationPositionRelation oldPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", "C18881_Z")
                .eq("channel", 3)
        );


        zwResignStrategy.closeActivity(oldPosition,"C18881_Z");
    }

    // 测试考勤扣款
    @Test
    public void testAttendancePenalty(){
//        penaltyAttendanceTask.excute("2023-01-01");
    }


    @Test
    public void testAccountList(){
        CeoSmallFreeAccountModel ceoSmallFreeAccountModel = activityQuotaConnectorUtil.accountList("***********");
        System.out.println(ceoSmallFreeAccountModel.getSurplusAmount());
    }
}
