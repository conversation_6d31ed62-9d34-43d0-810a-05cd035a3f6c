package com.wantwant.sfa.backend.test;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.Task.ActivityQuotaTask;
import com.wantwant.sfa.backend.Task.EmployeeEliminateTask;
import com.wantwant.sfa.backend.Task.ProbationCheckTask;
import com.wantwant.sfa.backend.customerMaintain.request.VerifieRequest;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.interview.model.OffPositionModel;
import com.wantwant.sfa.backend.interview.service.InterviewOnboardModel;
import com.wantwant.sfa.backend.interview.service.InterviewService;
import com.wantwant.sfa.backend.interview.service.ResignService;
import com.wantwant.sfa.backend.interview.strategy.impl.ZWOnboardStrategyImpl;
import com.wantwant.sfa.backend.interview.strategy.impl.ZWResignStrategyImpl;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationBindRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.organization.bind.OrganizationRelationModel;
import com.wantwant.sfa.backend.rabbitMQ.OnboardBenefitReceiver;
import com.wantwant.sfa.backend.rabbitMQ.config.OnboardBenefitTopicRabbitConfig;
import com.wantwant.sfa.backend.service.impl.CustomerServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2021/09/16/上午10:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WebServiceTest {
    @Autowired
    private CustomerServiceImpl customerService;

    public static final String ZW_HR_EMPLOYEE_ID_CODE = "zw_hr_employee_id";

    @Autowired
    private ProbationCheckTask probationCheckTask;
    @Autowired
    private InterviewService interviewService;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper
            ceoBusinessOrganizationPositionRelationMapper;
    @Autowired private ConfigMapper configMapper;
    @Autowired private OrganizationBindRelationMapper organizationBindRelationMapper;
    @Autowired
    private ResignService resignService;
    @Autowired
    private ActivityQuotaTask activityQuotaTask;
    @Autowired
    private EmployeeEliminateTask employeeEliminateTask;

    @Autowired private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private ZWResignStrategyImpl zwResignStrategy;
    @Autowired
    private ZWOnboardStrategyImpl zwOnboardStrategy;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private OnboardBenefitReceiver onboardBenefitReceiver;

    @Test
    public void test01(){
        probationCheckTask.check(null);
    }

    @Test
    public void test02(){
        interviewService.createInterviewProcess(17,3);
    }

    @Test
    public void test03(){
//        InterviewOnboardModel model = new InterviewOnboardModel("***********","2021-11-10","00559871","报名流程测试1-2");
//        interviewService.onboard(model,"00390190","test");
    }

    @Test
    public void test04(){
        CeoBusinessOrganizationPositionRelation positionRelation = chooseProcessUser(getSfaOrganizationCode("Y07",3), getSfaOrganizationCode("C39",3), 1, 3);
        System.out.println(positionRelation.getEmployeeName());
    }

    private CeoBusinessOrganizationPositionRelation chooseProcessUser(
            String areaCode, String companyCode, Integer position, int channel) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = null;
        if (position == 1) {
            // 获取区域经理信息
            ceoBusinessOrganizationPositionRelation =
                    ceoBusinessOrganizationPositionRelationMapper.selectOne(
                            new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                    .eq("organization_id", companyCode)
                                    .eq("channel", channel));
            // 如果没有对应的审核人，则向上传递
            if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                // 大区经理信息
                ceoBusinessOrganizationPositionRelation =
                        ceoBusinessOrganizationPositionRelationMapper.selectOne(
                                new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                        .eq("organization_id", areaCode)
                                        .eq("channel", channel));
            }
        } else {
            // 大区经理信息
            ceoBusinessOrganizationPositionRelation =
                    ceoBusinessOrganizationPositionRelationMapper.selectOne(
                            new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                    .eq("organization_id", areaCode)
                                    .eq("channel", channel));
        }
        // 无法获取审核人，试用配置项目
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)
                || StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeName())) {
            String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
            if (StringUtils.isBlank(employeeId)) {
                throw new ApplicationException("获取审核人信息失败");
            }
            ceoBusinessOrganizationPositionRelation =
                    ceoBusinessOrganizationPositionRelationMapper.selectOne(
                            new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                                    .eq("employee_id", employeeId)
                                    .eq("channel", channel));
        }

        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException("获取审核人信息失败");
        }
        return ceoBusinessOrganizationPositionRelation;
    }


    private String getSfaOrganizationCode(String companyOrganizationId, int channel) {
        OrganizationRelationModel organizationRelationModel =
                organizationBindRelationMapper.selectOne(
                        new QueryWrapper<OrganizationRelationModel>()
                                .eq("org_code", companyOrganizationId)
                                .eq("channel", channel));
        if (Objects.isNull(organizationRelationModel)
                || StringUtils.isBlank(organizationRelationModel.getSfaOrgCode())) {
            throw new ApplicationException("获取组织对应关系失败");
        }
        return organizationRelationModel.getSfaOrgCode();
    }

    @Test
    public void test05(){
        OffPositionModel model = new OffPositionModel();
        model.setMobile("***********");
        model.setEmployeeId("");
        model.setEmployeeName("王四九");
        model.setOffPositionTime("2021-12-30");
        model.setProcessRecordId(3006);
        resignService.offPosition(model,"00390190");
    }

    @Test
    public void test06(){
        VerifieRequest request = new VerifieRequest();
        request.setVerifieId("20463");
        request.setIsVerified(null);
        request.setSuggestions("系统自动审核");
        request.setCustomerId("108139");

        customerService.toApproved(request,"系统");
    }

    @Test
    public void test07(){
        Integer branchCount =
                ceoBusinessOrganizationMapper.selectCount(
                        new QueryWrapper<CeoBusinessOrganizationEntity>()
                                .eq("channel", 3)
                                .eq("organization_type", "branch")
                                .likeRight("organization_name", "兰州"));
        System.out.println(branchCount);
    }


    @Test
    public void test08(){
        activityQuotaTask.excute("");
    }

    @Test
    public void test09(){
        employeeEliminateTask.eliminate("2021-11");
    }


    @Test
    public void test10(){
        zwResignStrategy.changeValidPerson("00314649","POS_XB_001_Z");
    }


    @Test
    public void test11(){
        zwOnboardStrategy.changeValidPerson("00314649","POS_XB_001_Z","111");
    }


    @Test
    public void test12() throws InterruptedException {
        JSONObject obj = new JSONObject();
        obj.put("memberKey",123123);

        rabbitTemplate.convertAndSend(OnboardBenefitTopicRabbitConfig.onboardBenefitDlExchange, OnboardBenefitTopicRabbitConfig.onboardBenefit, obj);

        System.out.println("11111");
    }

    @Test
    public void test13(){

        JSONObject obj = new JSONObject();
        obj.put("memberKey", "177221631");

        rabbitTemplate.convertAndSend("test","subject","111");

    }
}

