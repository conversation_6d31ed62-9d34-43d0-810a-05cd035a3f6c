package com.wantwant.sfa.backend.test;

import com.wantwant.sfa.backend.Task.BusinessBDDevelopAutomationTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/10/12/上午9:30
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class businessBDDevelopAutomaticTest {
    @Resource
    private BusinessBDDevelopAutomationTask businessBDDevelopAutomationTask;

    @Test
    public void test01(){
        businessBDDevelopAutomationTask.businessBDDevelopAutomatic("2025-04-15");
    }


    @Test
    public void test02(){
        businessBDDevelopAutomationTask.businessBDDevelopAutomatic("2025-04-15");
    }

    @Test
    public void test03(){
        businessBDDevelopAutomationTask.addExternalQuota("2025-05-22");
    }


    @Test
    public void test04(){
        businessBDDevelopAutomationTask.saveBdSalarySnapshot("2025-05-31");
    }
}
