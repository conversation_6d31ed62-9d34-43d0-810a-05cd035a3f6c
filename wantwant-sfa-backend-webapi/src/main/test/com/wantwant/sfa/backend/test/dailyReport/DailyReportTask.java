package com.wantwant.sfa.backend.test.dailyReport;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/03/07/下午5:56
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DailyReportTask {
    @Autowired
    private com.wantwant.sfa.backend.Task.DailyReportTask dailyReportTask;

    @Test
    public void testTask01(){
        dailyReportTask.dailyReportAutoSubmit("2025-01-10");
    }
}
