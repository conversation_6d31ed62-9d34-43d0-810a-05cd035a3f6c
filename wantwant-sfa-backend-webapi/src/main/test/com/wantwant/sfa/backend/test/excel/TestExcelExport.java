package com.wantwant.sfa.backend.test.excel;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/05/09/下午3:41
 */
public class TestExcelExport {

    @Test
    public void test01(){

        List<ExcelExportEntity> exportList = new ArrayList<>();
        ExcelExportEntity basic = new ExcelExportEntity("基础信息","basic");
        ExcelExportEntity period1 = new ExcelExportEntity("2023-05-01~2023-05-07","basic1");
        ExcelExportEntity period2 = new ExcelExportEntity("2023-05-08~2023-05-14","basic2");

        ExcelExportEntity area = new ExcelExportEntity("大区","area");
        ExcelExportEntity company = new ExcelExportEntity("分公司","company");
        ExcelExportEntity department = new ExcelExportEntity("营业所","department");
        ExcelExportEntity avatar = new ExcelExportEntity("头像","avatar");
        ExcelExportEntity employeeName = new ExcelExportEntity("用户名称","employeeName");
        ExcelExportEntity mobile = new ExcelExportEntity("手机号","mobile");


        basic.setList(Arrays.asList(area,company,department,avatar,employeeName,mobile));


        ExcelExportEntity status = new ExcelExportEntity("状态","status");
        ExcelExportEntity saleGoal = new ExcelExportEntity("盘价业绩","saleGoal");
        ExcelExportEntity saleGoalAchievementRate = new ExcelExportEntity("目标达成率","saleGoalAchievementRate");
        ExcelExportEntity estimatePrice = new ExcelExportEntity("本月预估金额","estimatePrice");
        ExcelExportEntity estimateAchievementRate = new ExcelExportEntity("本月预估达成率","estimateAchievementRate");
        ExcelExportEntity nextEstimatePrice = new ExcelExportEntity("下属预估金额合计","nextEstimatePrice");
        ExcelExportEntity nextFinishRate = new ExcelExportEntity("下属周报完成率","nextFinishRate");

        period1.setList(Arrays.asList(status,saleGoal,saleGoalAchievementRate,estimatePrice,estimateAchievementRate,nextEstimatePrice,nextFinishRate));
        period2.setList(Arrays.asList(status,saleGoal,saleGoalAchievementRate,estimatePrice,estimateAchievementRate,nextEstimatePrice,nextFinishRate));


        exportList.add(basic);
        exportList.add(period1);
        exportList.add(period2);



        ExportParams params = new ExportParams("表格标题__布拉布拉布拉", "随便取个sheet名", ExcelType.XSSF);

    }
}
