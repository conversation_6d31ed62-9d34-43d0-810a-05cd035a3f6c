package com.wantwant.sfa.backend.test.interview;

import com.wantwant.sfa.backend.interview.service.impl.InterviewBigTableServiceImpl;
import com.wantwant.sfa.backend.interview.service.impl.InterviewServiceImpl;
import com.wantwant.sfa.backend.estimate.vo.CityManagerProbationVO;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 城市经理试用期测试类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RunWith(MockitoJUnitRunner.class)
public class CityManagerProbationTest {

    @Mock
    private ConfigMapper configMapper;

    @Mock
    private InterviewBigTableServiceImpl interviewBigTableService;

    @InjectMocks
    private InterviewServiceImpl interviewService;

    private String testDepartmentId;
    private String currentYearMonth;

    @Before
    public void setUp() {
        testDepartmentId = "DEPT001";
        currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 测试configMapper有值的情况
     */
    @Test
    public void testGetCityManagerProbation_ConfigMapperHasValue() {
        // Given - configMapper返回具体值，performance有值
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("100");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("150"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("150"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("100"), result.getRequirePerformance());
        assertTrue("应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试configMapper无值的情况（返回null）
     */
    @Test
    public void testGetCityManagerProbation_ConfigMapperReturnsNull() {
        // Given - configMapper返回null，performance有值
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn(null);
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("150"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("150"), result.getPerformance());
        assertEquals("要求业绩应该默认为0", new BigDecimal("0"), result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试interviewBigTableService有值的情况
     */
    @Test
    public void testGetCityManagerProbation_PerformanceHasValue() {
        // Given - performance有值
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("50");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("80"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("80"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("50"), result.getRequirePerformance());
        assertTrue("应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试interviewBigTableService无值的情况（返回null）
     */
    @Test
    public void testGetCityManagerProbation_PerformanceReturnsNull() {
        // Given - performance返回null
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("100");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(null);

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该默认为0", BigDecimal.ZERO, result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("100"), result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试if条件：require <= 0的情况
     */
    @Test
    public void testGetCityManagerProbation_RequirePerformanceIsZero() {
        // Given - 要求业绩为0
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("0");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("100"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("100"), result.getPerformance());
        assertEquals("要求业绩应该为0", BigDecimal.ZERO, result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试if条件：require > 0 但 require >= performance的情况
     */
    @Test
    public void testGetCityManagerProbation_RequireGreaterThanOrEqualPerformance() {
        // Given - 要求业绩大于等于实际业绩
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("100");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("100")); // 相等情况

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("100"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("100"), result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试if条件：require > 0 且 require < performance的情况（应该跳过试用期）
     */
    @Test
    public void testGetCityManagerProbation_RequireLessThanPerformance() {
        // Given - 要求业绩小于实际业绩
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("80");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("120"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("120"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("80"), result.getRequirePerformance());
        assertTrue("应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试边界情况：require > performance（差一点点的情况）
     */
    @Test
    public void testGetCityManagerProbation_RequireSlightlyGreaterThanPerformance() {
        // Given - 要求业绩略大于实际业绩
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("100.01");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("100.00"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("100.00"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("100.01"), result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试负数要求业绩的情况
     */
    @Test
    public void testGetCityManagerProbation_NegativeRequirePerformance() {
        // Given - 要求业绩为负数
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("-10");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("50"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("50"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("-10"), result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试综合情况：都为null的情况
     */
    @Test
    public void testGetCityManagerProbation_BothNull() {
        // Given - 两个依赖都返回null
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn(null);
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(null);

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该默认为0", BigDecimal.ZERO, result.getPerformance());
        assertEquals("要求业绩应该默认为0", BigDecimal.ZERO, result.getRequirePerformance());
        assertFalse("不应该跳过试用期", result.isSkipProbation());
    }

    /**
     * 测试极限情况：非常大的数值
     */
    @Test
    public void testGetCityManagerProbation_LargeNumbers() {
        // Given - 大数值情况
        when(configMapper.getValueByCode("city_manager_require_performance")).thenReturn("999999");
        when(interviewBigTableService.selectDepartmentPerformance(testDepartmentId, currentYearMonth))
            .thenReturn(new BigDecimal("1000000"));

        // When
        CityManagerProbationVO result = interviewService.getCityManagerProbation(testDepartmentId);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertEquals("业绩应该正确", new BigDecimal("1000000"), result.getPerformance());
        assertEquals("要求业绩应该正确", new BigDecimal("999999"), result.getRequirePerformance());
        assertTrue("应该跳过试用期", result.isSkipProbation());
    }
} 