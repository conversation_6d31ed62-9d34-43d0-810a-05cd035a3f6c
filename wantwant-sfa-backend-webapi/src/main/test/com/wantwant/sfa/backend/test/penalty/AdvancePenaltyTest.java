package com.wantwant.sfa.backend.test.penalty;

import com.wantwant.sfa.backend.Task.AdvancePenaltyTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/18/下午2:07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AdvancePenaltyTest {
    @Resource
    private AdvancePenaltyTask advancePenaltyTask;

    @Test
    public void testAdvance(){
        advancePenaltyTask.execute("2025-10-02");
    }
}
