package com.wantwant.sfa.backend.test.penalty;

import com.wantwant.sfa.backend.Task.PenaltyTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/12/05/下午7:00
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PenaltyTaskTest {
    @Resource
    private PenaltyTask penaltyTask;

    @Test
    public void testWeek(){
        penaltyTask.weeklyPenaltyProcess("2024-12-08");
    }

    @Test
    public void testDaily(){
        penaltyTask.dailyPenaltyProcess(null);
    }


    @Test
    public void testMonth(){
        penaltyTask.monthPenaltyProcess("2024-11");
    }
}
