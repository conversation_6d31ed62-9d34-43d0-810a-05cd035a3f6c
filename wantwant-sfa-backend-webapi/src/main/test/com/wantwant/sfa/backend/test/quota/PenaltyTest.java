package com.wantwant.sfa.backend.test.quota;

import com.wantwant.sfa.backend.Task.PenaltyNotifyTask;
import com.wantwant.sfa.backend.activityQuota.dto.QuotaSendDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/09/下午6:31
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PenaltyTest {
    @Autowired
    private PenaltyNotifyTask penaltyNotifyTask;


    @Test
    public void sendNotify(){

        penaltyNotifyTask.penaltyNotify("2024-03-18");
    }
}
