package com.wantwant.sfa.backend.test.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskInstanceMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskInstanceRecordMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.task.dto.*;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskInstanceEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskInstanceRecordEntity;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.enums.TaskProcessStepEnum;
import com.wantwant.sfa.backend.task.enums.TaskStatusEnum;
import com.wantwant.sfa.backend.task.service.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午9:20
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TaskTest {

    @Autowired
    private ITaskLogService taskLongService;
    @Autowired
    private ITaskService taskService;
    @Autowired
    private ITaskPublishService taskPublishService;
    @Autowired
    private SfaTaskMapper sfaTaskMapper;
    @Autowired
    private SfaTaskInstanceMapper sfaTaskInstanceMapper;
    @Autowired
    private SfaTaskInstanceRecordMapper sfaTaskInstanceRecordMapper;
    @Autowired
    private ITaskCustomerService taskCustomerService;
    @Autowired
    private ITaskProcessService taskProcessService;
    @Autowired
    private ITaskAuditService taskAuditService;

    @Test
    public void testSaveLog(){
        TaskLogDTO taskLogDTO = new TaskLogDTO();
        taskLogDTO.setProcessObj(Arrays.asList("张远","张近"));
        taskLogDTO.setTaskId(1L);
        taskLogDTO.setProcessUserId("00272473");
        taskLogDTO.setProcessUserName("蔡老师");
        taskLogDTO.setType(TaskLogTypeEnum.PUSH_TASK.getType());
        taskLongService.saveLog(taskLogDTO);


        TaskLogDTO taskLogDTO2 = new TaskLogDTO();
        taskLogDTO2.setTaskId(1L);
        taskLogDTO2.setProcessUserId("00272473");
        taskLogDTO2.setProcessUserName("蔡老师");
        taskLogDTO2.setType(TaskLogTypeEnum.CONFIRM_PUSH.getType());
        taskLongService.saveLog(taskLogDTO2);
    }


    @Test
    public void testSaveTask(){
        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setTaskName("测试任务01");
        taskDTO.setCategory(1);
//        taskDTO.setContextTask(1000007L);
        taskDTO.setTaskType(1);
        taskDTO.setContent("123123123");
        taskDTO.setPurpose("asdasdasdasds");
        taskDTO.setPriority(1);
        taskDTO.setWorth("133334444444");
        TaskAssignDTO mainProcessUser = new TaskAssignDTO();
        mainProcessUser.setDeptCode("*********");
        mainProcessUser.setDeptName("营运管理部");
        mainProcessUser.setEmpId("00441211");
        mainProcessUser.setEmpName("张元");
        taskDTO.setMainProcessUser(mainProcessUser);

        TaskAssignDTO assignDTO = new TaskAssignDTO();
        assignDTO.setDeptCode("*********");
        assignDTO.setDeptName("数据分析部");
        assignDTO.setEmpId("00390190");
        assignDTO.setEmpName("沈克勤（开发验证）");

        TaskAssignDTO assignDTO2 = new TaskAssignDTO();
        assignDTO2.setDeptCode("*********");
        assignDTO2.setDeptName("大数据开发部");
        assignDTO2.setEmpId("00660001");
        assignDTO2.setEmpName("张近");

        taskDTO.setAssistedProcessUsers(Arrays.asList(assignDTO,assignDTO2));

        TaskAssignDTO ccAssign = new TaskAssignDTO();
        ccAssign.setDeptCode("*********");
        ccAssign.setDeptName("旺铺财务管理课");
        ccAssign.setEmpId("00272473");
        ccAssign.setEmpName("蔡老师");

        taskDTO.setCcProcessUsers(Arrays.asList(ccAssign));
        taskDTO.setDeadline(LocalDateTime.now());
        taskDTO.setSubmit(1);
        taskDTO.setCreateUserId("00272473");
        taskDTO.setCreateUserName("蔡旺祖");
        taskDTO.setAnnex("https://prd-wzwp-std-oss.oss-cn-shanghai.aliyuncs.com/567f31f3-686c-4e91-b269-86a14e1e1aac13822334578");
        taskService.createTask(taskDTO);
    }


    @Test
    public void testUpdateTask(){
        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setTaskId(1000009L);
        taskDTO.setTaskName("测试任务01-子任务01");
        taskDTO.setCategory(1);
        taskDTO.setContextTask(1000007L);
        taskDTO.setTaskType(1);
        taskDTO.setContent("123123123");
        taskDTO.setPurpose("asdasdasdasds11111");
        taskDTO.setPriority(2);
        taskDTO.setWorth("aaaaa");
        TaskAssignDTO mainProcessUser = new TaskAssignDTO();
        mainProcessUser.setDeptCode("*********");
        mainProcessUser.setDeptName("营运管理部");
        mainProcessUser.setEmpId("00441211");
        mainProcessUser.setEmpName("张元");
        taskDTO.setMainProcessUser(mainProcessUser);

        TaskAssignDTO assignDTO = new TaskAssignDTO();
        assignDTO.setDeptCode("*********");
        assignDTO.setDeptName("数据分析部");
        assignDTO.setEmpId("00390190");
        assignDTO.setEmpName("沈克勤（开发验证）");

        TaskAssignDTO assignDTO2 = new TaskAssignDTO();
        assignDTO2.setDeptCode("*********");
        assignDTO2.setDeptName("大数据开发部");
        assignDTO2.setEmpId("00660001");
        assignDTO2.setEmpName("张近");

        taskDTO.setAssistedProcessUsers(Arrays.asList(assignDTO,assignDTO2));

        TaskAssignDTO ccAssign = new TaskAssignDTO();
        ccAssign.setDeptCode("*********");
        ccAssign.setDeptName("旺铺财务管理课");
        ccAssign.setEmpId("00272473");
        ccAssign.setEmpName("蔡老师");

        taskDTO.setCcProcessUsers(Arrays.asList(ccAssign));
        taskDTO.setDeadline(LocalDateTime.now());
        taskDTO.setSubmit(1);
        taskDTO.setCreateUserId("00272473");
        taskDTO.setCreateUserName("蔡旺祖");
        taskDTO.setAnnex("https://prd-wzwp-std-oss.oss-cn-shanghai.aliyuncs.com/567f31f3-686c-4e91-b269-86a14e1e1aac13822334578");
        taskService.updateTask(taskDTO);
    }

    @Test
    public void testModifyAssign(){
        TaskAssignModifyDTO taskAssignModifyDTO = new TaskAssignModifyDTO();
        taskAssignModifyDTO.setTaskId(1000014L);
        taskAssignModifyDTO.setDeadline(LocalDateTime.now().plusDays(30L));



        TaskAssignDTO mainProcessUser = new TaskAssignDTO();
        mainProcessUser.setDeptCode("*********");
        mainProcessUser.setDeptName("营运管理部");
        mainProcessUser.setEmpId("00441211");
        mainProcessUser.setEmpName("张元");
        taskAssignModifyDTO.setMainProcessUser(mainProcessUser);

        TaskAssignDTO assignDTO = new TaskAssignDTO();
        assignDTO.setDeptCode("*********");
        assignDTO.setDeptName("数据分析部");
        assignDTO.setEmpId("00390190");
        assignDTO.setEmpName("沈克勤（开发验证）");

        TaskAssignDTO assignDTO2 = new TaskAssignDTO();
        assignDTO2.setDeptCode("*********");
        assignDTO2.setDeptName("大数据开发部");
        assignDTO2.setEmpId("00660001");
        assignDTO2.setEmpName("张近");

        taskAssignModifyDTO.setAssistedProcessUsers(Arrays.asList(assignDTO,assignDTO2));

        TaskAssignDTO ccAssign = new TaskAssignDTO();
        ccAssign.setDeptCode("*********");
        ccAssign.setDeptName("旺铺财务管理课");
        ccAssign.setEmpId("00272473");
        ccAssign.setEmpName("蔡老师");
        taskAssignModifyDTO.setCcProcessUsers(Arrays.asList(ccAssign));

        taskAssignModifyDTO.setProcessUserId("00272473");
        taskAssignModifyDTO.setProcessUserName("蔡旺祖");
        taskService.modifyAssign(taskAssignModifyDTO);
    }

    @Test
    public void testPublish(){
        Long taskId = 1000014L;
        TaskPublishDTO taskPublishDTO = new TaskPublishDTO();
        taskPublishDTO.setTaskId(taskId);
        taskPublishDTO.setProcessUserId("00272473");
        taskPublishDTO.setProcessUserName("蔡旺祖");
        taskPublishService.publish(taskPublishDTO);

        SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(taskId);
        Assert.assertEquals(TaskStatusEnum.READY_SIGN.getStatus(),sfaTaskEntity.getStatus());

        SfaTaskInstanceEntity sfaTaskInstanceEntity = sfaTaskInstanceMapper.selectOne(new QueryWrapper<SfaTaskInstanceEntity>().eq("task_id", taskId).eq("delete_flag",0));
        Integer processStep = sfaTaskInstanceEntity.getProcessStep();
        Integer processResult = sfaTaskInstanceEntity.getProcessResult();
        Assert.assertEquals(TaskProcessStepEnum.SIGN.getProcessStep(),processStep);
        Assert.assertEquals(Integer.valueOf("0"),processResult);

        SfaTaskInstanceRecordEntity sfaTaskInstanceRecordEntity = sfaTaskInstanceRecordMapper.selectById(sfaTaskInstanceEntity.getRecordId());
        Integer rResult = sfaTaskInstanceRecordEntity.getProcessResult();
        Integer rStep = sfaTaskInstanceRecordEntity.getProcessStep();
        Assert.assertEquals(TaskProcessStepEnum.SIGN.getProcessStep(),rStep);
        Assert.assertEquals(Integer.valueOf("0"),rResult);

        Long prevRecord = sfaTaskInstanceRecordEntity.getPrevRecord();
        SfaTaskInstanceRecordEntity prevEntity = sfaTaskInstanceRecordMapper.selectById(prevRecord);
        Integer pResult = prevEntity.getProcessResult();
        Integer pStep = prevEntity.getProcessStep();
        Assert.assertEquals(TaskProcessStepEnum.PUSH.getProcessStep(),pStep);
        Assert.assertEquals(Integer.valueOf("1"),pResult);

    }

    @Test
    public void testRevert(){
        TaskPublishDTO taskPublishDTO = new TaskPublishDTO();
        taskPublishDTO.setTaskId(1000009L);
        taskPublishDTO.setProcessUserId("00272473");
        taskPublishDTO.setProcessUserName("蔡旺祖");
        taskPublishService.revert(taskPublishDTO);
    }

    @Test
    public void testSign(){
        TaskCustomerDTO taskCustomerDTO = new TaskCustomerDTO();
        taskCustomerDTO.setTaskId(1000014L);
        taskCustomerDTO.setProcessUserId("00441211");
        taskCustomerDTO.setProcessUserName("张远");
        taskCustomerService.sign(taskCustomerDTO);
    }


    @Test
    public void testSuspend(){
        taskProcessService.suspend(1000012L,0,"123","00272473","蔡旺祖");
    }

    @Test
    public void closed(){
        taskProcessService.closed(1000012L,"","00272473","蔡旺祖");
    }


    @Test
    public void situationSubmit(){
        TaskSituationDTO taskSituationDTO = new TaskSituationDTO();
        taskSituationDTO.setProcessUserId("00390190");
        taskSituationDTO.setProcessUserName("沈克勤（开发验证）");
        taskSituationDTO.setExpectedFinishDate(LocalDateTime.now().plusDays(11L));
        taskSituationDTO.setSituation("1111222223333");
        taskSituationDTO.setTaskId(1000014L);
        taskCustomerService.situationSubmit(taskSituationDTO);
    }

    @Test
    public void completeTest(){
        TaskCompleteDTO taskCompleteDTO = new TaskCompleteDTO();
        taskCompleteDTO.setTaskId(1000014L);
        taskCompleteDTO.setProcessUserId("00441211");
        taskCompleteDTO.setProcessUserName("张远");
//        taskCompleteDTO.setAuditUserId("00272473");
//        taskCompleteDTO.setAuditUserName("蔡旺祖");
        taskCompleteDTO.setRemark("123123123123");
        taskCustomerService.complete(taskCompleteDTO);
    }


    @Test
    public void redone(){

        taskAuditService.redone(1000014L,"111111", LocalDate.now(),"1111","00272473","蔡旺祖", 1);
    }

    @Test
    public void finish(){

        taskAuditService.finish(1000013L,"111111","00272473",null,null);
    }



    @Test
    public void follow(){
        TaskFollowDTO taskFollowDTO = new TaskFollowDTO();
        taskFollowDTO.setEmployeeId("00272473");
        taskFollowDTO.setTaskId(1000062L);
        taskFollowDTO.setFollow(1);
        taskCustomerService.follow(taskFollowDTO);
    }
}
