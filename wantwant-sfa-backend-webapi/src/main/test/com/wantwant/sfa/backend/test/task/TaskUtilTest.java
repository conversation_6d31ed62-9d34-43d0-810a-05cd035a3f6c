package com.wantwant.sfa.backend.test.task;

import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.util.ObjectComparator;
import com.wantwant.sfa.backend.util.ObjectDiffModel;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/09/04/上午7:43
 */
public class TaskUtilTest {

    @Test
    public void test01(){
        SfaTaskEntity s1 = new SfaTaskEntity();
        s1.setTaskName("1111");
        s1.setPriority(1);
        s1.setDeadline(LocalDateTime.now());
        SfaTaskEntity s2 = new SfaTaskEntity();
        s2.setTaskName("2222");
        s2.setPriority(2);
        s2.setDeadline(LocalDateTime.now());
        List<ObjectDiffModel> objectDiffModels = ObjectComparator.objectCompare(s1, s2);
        System.out.println(objectDiffModels);
    }
}
