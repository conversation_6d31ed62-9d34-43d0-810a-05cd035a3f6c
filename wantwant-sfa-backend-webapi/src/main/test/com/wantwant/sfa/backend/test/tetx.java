package com.wantwant.sfa.backend.test;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

public class tetx {
    public static void main(String[] args) {

//        LocalDate of = LocalDate.of(Integer.parseInt("2024"), Integer.parseInt("1") * 3, 1);
//        System.out.println(of.toString());

        int dayOfMonth = LocalDate.now().getDayOfMonth();
        int with = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
        System.out.println(dayOfMonth+"_"+with);

        BigDecimal multiply = new BigDecimal(dayOfMonth).divide(new BigDecimal(with),4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        System.out.println(multiply);
    }


//    public static List<AttendanceTaskEmployee> getList(){
//        AttendanceTaskEmployee employee = new AttendanceTaskEmployee();
//        AttendanceTaskCustomer customer1 = new AttendanceTaskCustomer();
//        AttendanceTaskCustomer customer2 = new AttendanceTaskCustomer();
//        List<AttendanceTaskCustomer> customerList = new ArrayList();
//        customerList.add(customer1);
//        customerList.add(customer2);
//        AttendanceTaskOrder Order1 = new AttendanceTaskOrder();
//        AttendanceTaskOrder Order2 = new AttendanceTaskOrder();
//        AttendanceTaskOrder Order3 = new AttendanceTaskOrder();
//        List<AttendanceTaskOrder> orderList1 = new ArrayList<>();
//        List<AttendanceTaskOrder> orderList2 = new ArrayList<>();
//        orderList1.add(Order1);
//        orderList2.add(Order2);
//        orderList2.add(Order3);
//        customer1.setOrderList(orderList1);
//        customer2.setOrderList(orderList2);
//        employee.setCustomerList(customerList);
//
//        List<AttendanceTaskEmployee> list = new ArrayList<>();
//        list.add(employee);
//        return list;
//
//    }

}
