package com.wantwant.sfa.backend.wallet;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyCostDetailEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyEntity;
import com.wantwant.sfa.backend.activityQuota.request.UpdateStatusRequest;
import com.wantwant.sfa.backend.activityQuota.service.impl.PenaltyService;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyCostDetailMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.PenaltyMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletAccountMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.wallet.data.WalletTestData;
import com.wantwant.sfa.backend.wallet.dto.CreateWalletAccountDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletAddDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletAddDetailDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletUsedDTO;
import com.wantwant.sfa.backend.wallet.entity.WantWalletAccountEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity;
import com.wantwant.sfa.backend.wallet.enums.WalletLogTypeEnum;
import com.wantwant.sfa.backend.wallet.service.IWalletService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/24/下午4:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WalletPenaltyTest {
    @Autowired
    private IWalletService walletService;

    private String processUserId = "********";

    private String processUserName = "张远";
    @Autowired
    private WantWalletAccountMapper wantWalletAccountMapper;
    @Autowired
    private WantWalletLogMapper wantWalletLogMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private PenaltyService penaltyService;
    @Autowired
    private PenaltyMapper penaltyMapper;
    @Autowired
    private PenaltyCostDetailMapper penaltyCostDetailMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private WantWalletMapper wantWalletMapper;

    private Integer applyType = 11;


    String path = "com/wantwant/sfa/backend/wallet/data/walletAdd.json";

    /**
     * 数据初始化
     *
     * @param organizationId
     * @param walletType
     */
    private void initData(String organizationId, Integer walletType) {
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(organizationId);
        createWalletAccountDTO.setWalletType(walletType);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);
    }

    private void setTestData(String organizationId, Integer walletType) {
        initData(organizationId, walletType);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletAddDTO walletAddDTO = new WalletAddDTO();
        walletAddDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletAddDTO.setWalletTypeId(walletType);
        walletAddDTO.setProcessUserId(processUserId);
        walletAddDTO.setProcessUserName(processUserName);

        String json = ResourceUtil.readUtf8Str(path);
        List<WalletTestData> walletTestData = JSONArray.parseArray(json, WalletTestData.class);
        List<WalletAddDetailDTO> detailDTOList = new ArrayList<>();
        walletTestData.forEach(e -> {
            WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
            walletAddDetailDTO.setDeptCode(e.getDeptCode());
            walletAddDetailDTO.setDeptName(e.getDeptName());
            walletAddDetailDTO.setQuota(e.getQuota());
            walletAddDetailDTO.setApplyType(applyType);
            walletAddDetailDTO.setBoundary(e.getBoundary());
            walletAddDetailDTO.setSubTypeId(e.getSubTypeId());
            detailDTOList.add(walletAddDetailDTO);
        });
        walletAddDTO.setDetailDTOList(detailDTOList);
        walletService.add(walletAddDTO);
    }

    private void initPosition(String organizationId) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        ceoBusinessOrganizationPositionRelation.setEmployeeId(processUserId);
        ceoBusinessOrganizationPositionRelation.setEmployeeName(processUserName);
        ceoBusinessOrganizationPositionRelationMapper.updateById(ceoBusinessOrganizationPositionRelation);
    }

    @Transactional
    @Test
    public void testPenaltyLessThan100() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        setTestData(organizationId, walletType);
        initPosition(organizationId);
        BigDecimal amount = new BigDecimal(100);

        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, amount, 26L, "测试扣罚", processUser);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).last("limit 1"));
        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(wantWalletEntity.getQuota().subtract(amount)) == 0, "扣罚额度有误");


        BigDecimal quota = wantWalletLogEntity.getQuota();
        BigDecimal surplus = wantWalletLogEntity.getSurplus();
        Assert.isTrue(quota.subtract(amount).compareTo(surplus) == 0, "扣罚额度有误");

        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        Assert.isTrue(Objects.nonNull(penaltyEntity), "扣罚记录错误");

        Integer status = penaltyEntity.getStatus();
        Assert.isTrue(status == 3, "扣罚状态错误");

        Assert.isTrue(penaltyEntity.getActualPenaltyAmount().compareTo(amount) == 0, "扣罚记录实际扣罚错误");
        Assert.isTrue(penaltyEntity.getPenaltyAmount().compareTo(amount) == 0, "扣罚记录应扣罚错误");

        PenaltyCostDetailEntity penaltyCostDetailEntity = penaltyCostDetailMapper.selectOne(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));
        Assert.isTrue(penaltyCostDetailEntity.getApplyType().equals(wantWalletLogEntity.getApplyType()), "扣罚记录扣罚类型错误");
        Assert.isTrue(penaltyCostDetailEntity.getWalletLogId().equals(wantWalletLogEntity.getLogId()), "扣罚记录中logId错误");
    }

    @Transactional
    @Test
    public void testPenaltyMoreThan100() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        setTestData(organizationId, walletType);
        initPosition(organizationId);
        AtomicReference<BigDecimal> amount = new AtomicReference<>(new BigDecimal(150));

        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, amount.get(), 26L, "测试扣罚", processUser);

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<WantWalletLogEntity> logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).last("limit 3"));

        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        Assert.isTrue(Objects.nonNull(penaltyEntity), "扣罚记录错误");


        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).last("limit 1"));
        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(wantWalletEntity.getQuota().subtract(amount.get())) == 0, "扣罚额度有误");


        Integer status = penaltyEntity.getStatus();
        Assert.isTrue(status == 3, "扣罚状态错误");

        Assert.isTrue(penaltyEntity.getActualPenaltyAmount().compareTo(amount.get()) == 0, "扣罚记录实际扣罚错误");
        Assert.isTrue(penaltyEntity.getPenaltyAmount().compareTo(amount.get()) == 0, "扣罚记录应扣罚错误");
        Assert.isTrue(penaltyEntity.getPenaltyAmount().compareTo(penaltyEntity.getActualPenaltyAmount()) == 0, "实际扣罚与应扣错误");

        List<PenaltyCostDetailEntity> penaltyCostDetailEntity = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));

        BigDecimal reduce = penaltyCostDetailEntity.stream().map(PenaltyCostDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(reduce.compareTo(amount.get()) == 0, "扣罚记录应扣罚错误");


        for (int i = 0; i < logList.size(); i++) {
            BigDecimal currentSurplus = logList.get(i).getSurplus();
            if (currentSurplus.compareTo(amount.get()) <= 0) {

                amount.set(amount.get().subtract(currentSurplus));
            } else {
                currentSurplus = BigDecimal.ZERO;
                amount.set(BigDecimal.ZERO);
            }
            Assert.isTrue(logList.get(i).getSurplus().compareTo(currentSurplus) == 0, "扣罚额度有误");
            Assert.isTrue(penaltyCostDetailEntity.get(i).getWalletLogId().equals(logList.get(i).getLogId()), "扣罚额度id有误");
            Assert.isTrue(penaltyCostDetailEntity.get(i).getApplyType().equals(logList.get(i).getApplyType()), "扣罚额度id有误");

            BigDecimal quota = logList.get(i).getQuota();
            BigDecimal surplus = logList.get(i).getSurplus();
            BigDecimal usedQuota = quota.subtract(surplus);
            Assert.isTrue(penaltyCostDetailEntity.get(i).getAmount().equals(usedQuota), "扣罚额度id有误");
        }
    }

    @Transactional
    @Test
    public void testPenaltyOver() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        setTestData(organizationId, walletType);
        initPosition(organizationId);
        BigDecimal penaltyAmount = new BigDecimal(9999);

        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, penaltyAmount, 26L, "测试扣罚", processUser);

        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        Assert.isTrue(Objects.nonNull(penaltyEntity), "扣罚记录错误");

        Integer status = penaltyEntity.getStatus();
        Assert.isTrue(status == 1, "扣罚状态错误");


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<WantWalletLogEntity> logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()));

        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).last("limit 1"));
        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(BigDecimal.ZERO) == 0, "扣罚额度有误");


        List<PenaltyCostDetailEntity> penaltyCostDetailEntity = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));

        Assert.isTrue(logList.size() == penaltyCostDetailEntity.size(), "扣罚明细错误");
        // 总额度
        BigDecimal totalQuota = logList.stream().map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);

        Assert.isTrue(totalQuota.compareTo(penaltyEntity.getActualPenaltyAmount()) == 0, "扣罚金额错误");

        Assert.isTrue(penaltyEntity.getPenaltyAmount().compareTo(penaltyAmount) == 0, "扣罚金额错误");

        for (int i = 0; i < logList.size(); i++) {
            BigDecimal currentSurplus = logList.get(i).getSurplus();
            if (currentSurplus.compareTo(penaltyAmount) <= 0) {
                penaltyAmount = penaltyAmount.subtract(currentSurplus);
            } else {
                currentSurplus = BigDecimal.ZERO;
                penaltyAmount = BigDecimal.ZERO;
            }
            Assert.isTrue(logList.get(i).getSurplus().compareTo(currentSurplus) == 0, "扣罚额度有误");
            Assert.isTrue(penaltyCostDetailEntity.get(i).getWalletLogId().equals(logList.get(i).getLogId()), "扣罚额度id有误");
            Assert.isTrue(penaltyCostDetailEntity.get(i).getApplyType().equals(logList.get(i).getApplyType()), "扣罚额度id有误");

            BigDecimal quota = logList.get(i).getQuota();
            BigDecimal surplus = logList.get(i).getSurplus();
            BigDecimal usedQuota = quota.subtract(surplus);
            Assert.isTrue(penaltyCostDetailEntity.get(i).getAmount().equals(usedQuota), "扣罚额度id有误");
        }
    }

    @Transactional
    @Test
    public void testPenaltyLessThan100AfterUsed() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(100);
        setTestData(organizationId, walletType);
        initPosition(organizationId);

        // 使用100
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        walletService.used(walletUsedDTO);

        // 扣罚30
        BigDecimal penaltyAmount = new BigDecimal(30);
        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, penaltyAmount, 26L, "测试扣罚", processUser);

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(wantWalletEntity.getQuota().subtract(usedQuota).subtract(penaltyAmount)) == 0, "扣罚额度有误");


        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        Assert.isTrue(Objects.nonNull(penaltyEntity), "扣罚记录错误");

        Integer status = penaltyEntity.getStatus();
        Assert.isTrue(status == 3, "扣罚状态错误");

        // 取前2条
        List<WantWalletLogEntity> logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).last("limit 2"));

        List<PenaltyCostDetailEntity> penaltyCostDetailEntity = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));

        Assert.isTrue(logList.size() == penaltyCostDetailEntity.size(), "扣罚明细错误");

        for (int i = 0; i < logList.size(); i++) {
            BigDecimal currentSurplus = logList.get(i).getSurplus();
            BigDecimal quota = logList.get(i).getQuota();
            PenaltyCostDetailEntity penaltyTemp = penaltyCostDetailEntity.get(i);
            BigDecimal amount = penaltyTemp.getAmount();
            Integer applyType = penaltyTemp.getApplyType();
            Integer applyType1 = logList.get(i).getApplyType();
            BigDecimal used = BigDecimal.ZERO;
            if (i == 0) {
                used = usedQuota;
            }
            Assert.isTrue(currentSurplus.add(amount).add(used).compareTo(quota) == 0, "额度扣减错误");
            Assert.isTrue(applyType.equals(applyType1), "额度扣减类型错误");
            Assert.isTrue(logList.get(i).getLogId().equals(penaltyCostDetailEntity.get(i).getWalletLogId()), "扣罚额度id有误");
        }
    }

    @Transactional
    @Test
    public void testPenaltyOverAfterUsed() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(100);
        setTestData(organizationId, walletType);
        initPosition(organizationId);

        // 使用100
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        walletService.used(walletUsedDTO);

        BigDecimal penaltyAmount = new BigDecimal(9999);
        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, penaltyAmount, 26L, "测试扣罚", processUser);


        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        Assert.isTrue(Objects.nonNull(penaltyEntity), "扣罚记录错误");
        Assert.isTrue(penaltyEntity.getPenaltyAmount().compareTo(penaltyAmount) == 0, "扣罚记录错误");
        Assert.isTrue(penaltyEntity.getActualPenaltyAmount().compareTo(penaltyAmount) < 0, "扣罚记录错误");

        Integer status = penaltyEntity.getStatus();
        Assert.isTrue(status == 1, "扣罚状态错误");

        // 取前2条
        List<WantWalletLogEntity> logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()));

        List<PenaltyCostDetailEntity> penaltyCostDetailEntity = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));

        Assert.isTrue(logList.size() == penaltyCostDetailEntity.size(), "扣罚明细错误");

        for (int i = 0; i < logList.size(); i++) {
            BigDecimal currentSurplus = logList.get(i).getSurplus();
            BigDecimal quota = logList.get(i).getQuota();
            PenaltyCostDetailEntity penaltyTemp = penaltyCostDetailEntity.get(i);
            BigDecimal amount = penaltyTemp.getAmount();
            Integer applyType = penaltyTemp.getApplyType();
            Integer applyType1 = logList.get(i).getApplyType();
            BigDecimal used = BigDecimal.ZERO;
            if (i == 0) {
                used = usedQuota;
            }
            Assert.isTrue(currentSurplus.add(amount).add(used).compareTo(quota) == 0, "额度扣减错误");
            Assert.isTrue(applyType.equals(applyType1), "额度扣减类型错误");
            Assert.isTrue(logList.get(i).getLogId().equals(penaltyCostDetailEntity.get(i).getWalletLogId()), "扣罚额度id有误");
        }
    }

    @Transactional
    @Test
    public void testUpdateStatus() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(100);
        setTestData(organizationId, walletType);
        initPosition(organizationId);

        // 使用100
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        walletService.used(walletUsedDTO);

        BigDecimal penaltyAmount = new BigDecimal(9999);
        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, penaltyAmount, 26L, "测试扣罚", processUser);


        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));

        UpdateStatusRequest updateStatusRequest = new UpdateStatusRequest();
        updateStatusRequest.setPerson("********");
        updateStatusRequest.setStatus(2);
        updateStatusRequest.setId(penaltyEntity.getId().intValue());
        penaltyService.updateStatus(updateStatusRequest);


        penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        Assert.isTrue(penaltyEntity.getStatus() == 2, "扣罚状态错误");

        List<PenaltyCostDetailEntity> penaltyCostDetailEntity = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));

        penaltyCostDetailEntity.forEach(e -> {
            Assert.isTrue(e.getAmount().compareTo(BigDecimal.ZERO) == 0, "扣罚额度回收错误");
        });

        List<WantWalletLogEntity> logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()));
        BigDecimal reduce = logList.stream().map(WantWalletLogEntity::getSurplus).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalQuota = logList.stream().map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);


        Assert.isTrue(totalQuota.compareTo(reduce.add(usedQuota)) == 0, "扣罚额度回收错误");
        Assert.isTrue(logList.get(0).getQuota().compareTo(logList.get(0).getSurplus().add(usedQuota)) == 0, "扣罚额度回收错误");
    }


    @Transactional
    @Test
    public void testUpdateStatusHistory() {
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(100);
        setTestData(organizationId, walletType);
        initPosition(organizationId);

        // 使用100
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        walletService.used(walletUsedDTO);

        BigDecimal penaltyAmount = new BigDecimal(9999);
        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, penaltyAmount, 26L, "测试扣罚", processUser);


        // 修改数据模拟历史
        PenaltyEntity penaltyEntity = penaltyMapper.selectOne(new LambdaQueryWrapper<PenaltyEntity>().eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId));
        List<PenaltyCostDetailEntity> detailEntityList = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));
        detailEntityList.forEach(e -> {
            e.setWalletLogId(0L);
            penaltyCostDetailMapper.updateById(e);
        });

        List<WantWalletLogEntity> logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()));
        for (int i = 0; i < logList.size(); i++) {
            WantWalletLogEntity wantWalletLogEntity = logList.get(i);
            wantWalletLogEntity.setSurplus(wantWalletLogEntity.getQuota());
            if (i == 0) {
                wantWalletLogEntity.setSurplus(wantWalletLogEntity.getQuota().subtract(usedQuota));
            }
            wantWalletLogMapper.updateById(wantWalletLogEntity);
        }


        UpdateStatusRequest updateStatusRequest = new UpdateStatusRequest();
        updateStatusRequest.setPerson("********");
        updateStatusRequest.setStatus(2);
        updateStatusRequest.setId(penaltyEntity.getId().intValue());
        penaltyService.updateStatus(updateStatusRequest);

        List<PenaltyCostDetailEntity> penaltyCostDetailEntity = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyEntity.getId()));

        penaltyCostDetailEntity.forEach(e -> {
            Assert.isTrue(e.getAmount().compareTo(BigDecimal.ZERO) == 0, "扣罚额度回收错误");
        });

        logList = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getDeleteFlag, 0).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()));
        BigDecimal reduce = logList.stream().map(WantWalletLogEntity::getSurplus).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalQuota = logList.stream().map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);


        Assert.isTrue(totalQuota.compareTo(reduce.add(usedQuota)) == 0, "扣罚额度回收错误");
        Assert.isTrue(logList.get(0).getQuota().compareTo(logList.get(0).getSurplus().add(usedQuota)) == 0, "扣罚额度回收错误");
    }


    @Test
    public void testPenalty() {
        String organizationId = "p31";
        Integer walletType = 1;
        setTestData(organizationId, walletType);
        initPosition(organizationId);
        BigDecimal amount = new BigDecimal(19665.00);

        CeoBusinessOrganizationPositionRelation processUser = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, "********").last("limit 1"));
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        CeoBusinessOrganizationPositionRelation areaPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, companyPosition.getOrganizationParentId()));
        penaltyService.savePenaltyLogMapper(companyPosition, amount, 26L, "测试扣罚", processUser);

    }
}
