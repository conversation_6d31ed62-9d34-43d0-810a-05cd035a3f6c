package com.wantwant.sfa.backend.wallet;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletAccountMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletMapper;
import com.wantwant.sfa.backend.wallet.data.WalletTestData;
import com.wantwant.sfa.backend.wallet.dto.CreateWalletAccountDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletAddDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletAddDetailDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletSendDTO;
import com.wantwant.sfa.backend.wallet.entity.WantWalletAccountEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity;
import com.wantwant.sfa.backend.wallet.enums.WalletLogTypeEnum;
import com.wantwant.sfa.backend.wallet.service.IWalletApplicationService;
import com.wantwant.sfa.backend.wallet.service.IWalletService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/27/下午9:07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WalletQuickStartTest {

    private String organizationId = "C18359_Z_03";

    private Integer boundary = 1;

    private String businessGroup = "G06051542003";

    private String deptCode = "D000000039";

    private String deptName = "营运管理部";

    private Integer walletType = 2;

    private String processUserId = "********";

    private String processUserName = "张远";

    private Integer applyType = 11;
    @Autowired
    private IWalletApplicationService walletApplicationService;
    @Autowired
    private WantWalletAccountMapper wantWalletAccountMapper;
    @Autowired
    private WantWalletMapper wantWalletMapper;
    @Autowired
    private WantWalletLogMapper wantWalletLogMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private IWalletService walletService;


    String path = "com/wantwant/sfa/backend/wallet/data/walletAdd.json";

    private void initData(String organizationId, Integer walletType) {
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(organizationId);
        createWalletAccountDTO.setWalletType(walletType);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);
    }


    private void setTestData(String organizationId, Integer walletType) {
        initData(organizationId,walletType);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletAddDTO walletAddDTO = new WalletAddDTO();
        walletAddDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletAddDTO.setWalletTypeId(walletType);
        walletAddDTO.setProcessUserId(processUserId);
        walletAddDTO.setProcessUserName(processUserName);

        String json = ResourceUtil.readUtf8Str(path);
        List<WalletTestData> walletTestData = JSONArray.parseArray(json, WalletTestData.class);
        List<WalletAddDetailDTO> detailDTOList = new ArrayList<>();
        walletTestData.forEach(e -> {
            WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
            walletAddDetailDTO.setDeptCode(e.getDeptCode());
            walletAddDetailDTO.setDeptName(e.getDeptName());
            walletAddDetailDTO.setQuota(e.getQuota());
            walletAddDetailDTO.setBoundary(e.getBoundary());
            walletAddDetailDTO.setApplyType(e.getApplyType());
            detailDTOList.add(walletAddDetailDTO);
        });
        walletAddDTO.setDetailDTOList(detailDTOList);
        walletService.add(walletAddDTO);
    }

    @Test
    @Transactional
    public void companyWithoutAccountAdd(){

        BigDecimal quota = new BigDecimal("23.11");

        WalletSendDTO walletSendDTO = new WalletSendDTO();
        walletSendDTO.setBoundary(boundary);
        walletSendDTO.setBusinessGroup(businessGroup);
        walletSendDTO.setDeptCode(deptCode);
        walletSendDTO.setDeptName(deptName);
        walletSendDTO.setQuota(quota);
        walletSendDTO.setApplyType(applyType);
        walletSendDTO.setReceiverKey(organizationId);
        walletSendDTO.setReceiverType(1);
        walletSendDTO.setReceiverWalletType(walletType);
        walletSendDTO.setProcessUserId(processUserId);
        walletSendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(walletSendDTO);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletAccountEntity),"账号创建失败");

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletEntity),"账号创建失败");

        Assert.isTrue(wantWalletEntity.getQuota().compareTo(quota) == 0,"账号创建额度错误");
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(quota) == 0,"账号创建额度错误");

        WantWalletEntity notExistRecord = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, 9).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.isNull(notExistRecord),"账号创建异常");

        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletLogEntity),"账号创建失败,记录表错误");


        Assert.isTrue(wantWalletLogEntity.getQuota().compareTo(quota) == 0, "账号创建失败,额度错误");
        Assert.isTrue(wantWalletLogEntity.getSurplus().compareTo(quota) == 0, "账号创建失败,剩余额度错误");
        Assert.isTrue(wantWalletLogEntity.getApplyType() == applyType, "账号创建失败,费用类型错误");
        Assert.isTrue(wantWalletLogEntity.getBoundary() == boundary, "账号创建失败,边界错误");
        Assert.isTrue(wantWalletLogEntity.getDeptCode().equals(deptCode), "账号创建失败,部门code错误");
        Assert.isTrue(wantWalletLogEntity.getDeptName().equals(deptName), "账号创建失败,部门名称错误");
        Assert.isTrue(wantWalletLogEntity.getExpenditure().equals("总部"), "账号创建失败,支出方错误");
        Assert.isTrue(wantWalletLogEntity.getRevenue().equals(organizationMapper.getOrganizationName(organizationId)), "账号创建失败,收入方错误");
    }

    @Test
    @Transactional
    public void companyWithoutWalletTypeAdd(){
        int walletTypeTemp = 1;
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(organizationId);
        createWalletAccountDTO.setWalletType(walletTypeTemp);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);

        BigDecimal quota = new BigDecimal("23.11");
        WalletSendDTO walletSendDTO = new WalletSendDTO();
        walletSendDTO.setBoundary(boundary);
        walletSendDTO.setBusinessGroup(businessGroup);
        walletSendDTO.setDeptCode(deptCode);
        walletSendDTO.setDeptName(deptName);
        walletSendDTO.setQuota(quota);
        walletSendDTO.setApplyType(applyType);
        walletSendDTO.setReceiverKey(organizationId);
        walletSendDTO.setReceiverType(1);
        walletSendDTO.setReceiverWalletType(walletType);
        walletSendDTO.setProcessUserId(processUserId);
        walletSendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(walletSendDTO);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletAccountEntity),"账号创建失败");


        WantWalletEntity walletTemp = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletTypeTemp).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(walletTemp),"账号创建失败");
        Assert.isTrue(walletTemp.getQuota().compareTo(BigDecimal.ZERO) == 0,"账号创建额度错误");
        Assert.isTrue(walletTemp.getSurplus().compareTo(BigDecimal.ZERO) == 0,"账号创建额度错误");

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletEntity),"账号创建失败");


        Assert.isTrue(wantWalletEntity.getQuota().compareTo(quota) == 0,"账号创建额度错误");
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(quota) == 0,"账号创建额度错误");


        WantWalletEntity notExistRecord = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, 9).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.isNull(notExistRecord),"账号创建异常");

        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletLogEntity),"账号创建失败,记录表错误");


        Assert.isTrue(wantWalletLogEntity.getQuota().compareTo(quota) == 0, "账号创建失败,额度错误");
        Assert.isTrue(wantWalletLogEntity.getSurplus().compareTo(quota) == 0, "账号创建失败,剩余额度错误");
        Assert.isTrue(wantWalletLogEntity.getApplyType() == applyType, "账号创建失败,费用类型错误");
        Assert.isTrue(wantWalletLogEntity.getBoundary() == boundary, "账号创建失败,边界错误");
        Assert.isTrue(wantWalletLogEntity.getDeptCode().equals(deptCode), "账号创建失败,部门code错误");
        Assert.isTrue(wantWalletLogEntity.getDeptName().equals(deptName), "账号创建失败,部门名称错误");
        Assert.isTrue(wantWalletLogEntity.getExpenditure().equals("总部"), "账号创建失败,支出方错误");
        Assert.isTrue(wantWalletLogEntity.getRevenue().equals(organizationMapper.getOrganizationName(organizationId)), "账号创建失败,收入方错误");
    }

    @Test
    @Transactional
    public void companyAdd(){
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(organizationId);
        createWalletAccountDTO.setWalletType(walletType);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);

        BigDecimal quota = new BigDecimal("23.11");
        WalletSendDTO walletSendDTO = new WalletSendDTO();
        walletSendDTO.setBoundary(boundary);
        walletSendDTO.setBusinessGroup(businessGroup);
        walletSendDTO.setDeptCode(deptCode);
        walletSendDTO.setDeptName(deptName);
        walletSendDTO.setQuota(quota);
        walletSendDTO.setApplyType(applyType);
        walletSendDTO.setReceiverKey(organizationId);
        walletSendDTO.setReceiverType(1);
        walletSendDTO.setReceiverWalletType(walletType);
        walletSendDTO.setProcessUserId(processUserId);
        walletSendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(walletSendDTO);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletAccountEntity),"账号创建失败");


        WantWalletEntity walletTemp = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, 3).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.isNull(walletTemp),"账号创建失败");

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletEntity),"账号创建失败");


        Assert.isTrue(wantWalletEntity.getQuota().compareTo(quota) == 0,"账号创建额度错误");
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(quota) == 0,"账号创建额度错误");


        WantWalletEntity notExistRecord = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, 9).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.isNull(notExistRecord),"账号创建异常");

        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(Objects.nonNull(wantWalletLogEntity),"账号创建失败,记录表错误");


        Assert.isTrue(wantWalletLogEntity.getQuota().compareTo(quota) == 0, "账号创建失败,额度错误");
        Assert.isTrue(wantWalletLogEntity.getSurplus().compareTo(quota) == 0, "账号创建失败,剩余额度错误");
        Assert.isTrue(wantWalletLogEntity.getApplyType() == applyType, "账号创建失败,费用类型错误");
        Assert.isTrue(wantWalletLogEntity.getBoundary() == boundary, "账号创建失败,边界错误");
        Assert.isTrue(wantWalletLogEntity.getDeptCode().equals(deptCode), "账号创建失败,部门code错误");
        Assert.isTrue(wantWalletLogEntity.getDeptName().equals(deptName), "账号创建失败,部门名称错误");
        Assert.isTrue(wantWalletLogEntity.getExpenditure().equals("总部"), "账号创建失败,支出方错误");
        Assert.isTrue(wantWalletLogEntity.getRevenue().equals(organizationMapper.getOrganizationName(organizationId)), "账号创建失败,收入方错误");

    }


    @Test
    @Transactional
    public void sendAccountNotExist(){
        String receiverOrgCode = "M20701_Z_03";
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(receiverOrgCode);
        createWalletAccountDTO.setWalletType(walletType);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);

        BigDecimal quota = new BigDecimal("23.11");
        WalletSendDTO walletSendDTO = new WalletSendDTO();
        walletSendDTO.setSenderOrgCode(organizationId);
        walletSendDTO.setBoundary(boundary);
        walletSendDTO.setBusinessGroup(businessGroup);
        walletSendDTO.setDeptCode(deptCode);
        walletSendDTO.setDeptName(deptName);
        walletSendDTO.setQuota(quota);
        walletSendDTO.setApplyType(applyType);
        walletSendDTO.setReceiverKey(receiverOrgCode);
        walletSendDTO.setReceiverType(1);
        walletSendDTO.setReceiverWalletType(walletType);
        walletSendDTO.setProcessUserId(processUserId);
        walletSendDTO.setProcessUserName(processUserName);
        try {
            walletApplicationService.quickSend(walletSendDTO);
        } catch (Exception e) {
            Assert.isTrue(e.getMessage().equals("组织账号未创建"),"错误信息不正确");
        }
    }

    @Test
    @Transactional
    public void sendAccountOverSurplus(){
        int walletTypeTemp = 1;
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(organizationId);
        createWalletAccountDTO.setWalletType(walletTypeTemp);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);

        BigDecimal quota = new BigDecimal("23.11");
        WalletSendDTO walletSendDTO = new WalletSendDTO();
        walletSendDTO.setBoundary(boundary);
        walletSendDTO.setBusinessGroup(businessGroup);
        walletSendDTO.setDeptCode(deptCode);
        walletSendDTO.setDeptName(deptName);
        walletSendDTO.setQuota(quota);
        walletSendDTO.setApplyType(applyType);
        walletSendDTO.setReceiverKey(organizationId);
        walletSendDTO.setReceiverType(1);
        walletSendDTO.setReceiverWalletType(walletType);
        walletSendDTO.setProcessUserId(processUserId);
        walletSendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(walletSendDTO);

        String receiverOrgCode = "M20701_Z_03";
        BigDecimal sendQuota = new BigDecimal("24.11");
        WalletSendDTO sendDTO = new WalletSendDTO();
        sendDTO.setSenderOrgCode(organizationId);
        sendDTO.setSendWalletType(walletType);
        sendDTO.setQuota(quota);
        sendDTO.setReceiverKey(receiverOrgCode);
        sendDTO.setReceiverType(1);
        sendDTO.setReceiverWalletType(walletType);
        sendDTO.setProcessUserId(processUserId);
        sendDTO.setProcessUserName(processUserName);
        try {
            walletApplicationService.quickSend(walletSendDTO);
        } catch (Exception e) {
            Assert.isTrue(e.getMessage().equals("额度不足"),"错误信息不正确");
        }
    }

    @Test
    @Transactional
    public void sendQuotaFromAccount(){
        setTestData(organizationId,walletType);

        String receiverOrgCode = "M20701_Z_03";
        BigDecimal sendQuota = new BigDecimal(150);
        WalletSendDTO sendDTO = new WalletSendDTO();
        sendDTO.setSenderOrgCode(organizationId);
        sendDTO.setSendWalletType(walletType);
        sendDTO.setQuota(sendQuota);
        sendDTO.setReceiverKey(receiverOrgCode);
        sendDTO.setReceiverType(1);
        sendDTO.setReceiverWalletType(walletType);
        sendDTO.setProcessUserId(processUserId);
        sendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(sendDTO);

        WantWalletAccountEntity fromAccount = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<WantWalletLogEntity> fromAccountLogs = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, fromAccount.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.USED.getCode()).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(!CollectionUtils.isEmpty(fromAccountLogs),"账号创建错误");
        WantWalletAccountEntity toAccount = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, receiverOrgCode).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<WantWalletLogEntity> toAccountLogs = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, toAccount.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(!CollectionUtils.isEmpty(toAccountLogs),"账号创建错误");
        Assert.isTrue(toAccountLogs.size() == fromAccountLogs.size(),"账号创建错误");

        BigDecimal reduce = fromAccountLogs.stream().map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(reduce.compareTo(sendQuota) == 0,"发放额度错误");

        WantWalletEntity fromWallet = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, fromAccount.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(fromWallet.getSurplus().compareTo(fromWallet.getQuota().subtract(sendQuota)) == 0,"发放额度错误");

        WantWalletEntity toWallet = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, toAccount.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(toWallet.getSurplus().compareTo(sendQuota) == 0,"发放额度错误");
        Assert.isTrue(toWallet.getQuota().compareTo(sendQuota) == 0,"发放额度错误");

        for(int i=0 ; i < toAccountLogs.size(); i++){
            WantWalletLogEntity toLog = toAccountLogs.get(i);
            WantWalletLogEntity fromLog = fromAccountLogs.get(i);

            Assert.isTrue(toLog.getApplyType() == fromLog.getApplyType(),"发放类型错误");
            Assert.isTrue(toLog.getQuota().compareTo(fromLog.getQuota()) == 0,"发放额度错误");
            Assert.isTrue(toLog.getBoundary() == fromLog.getBoundary(),"发放边界错误");
            Assert.isTrue(toLog.getDeptCode().equals(fromLog.getDeptCode()),"发放部门错误");
            Assert.isTrue(toLog.getDeptName().equals(fromLog.getDeptName()),"发放部门错误");
        }
    }


    @Test
    @Transactional
    public void sendQuotaFromAccountAnotherWalletType(){
        setTestData(organizationId,walletType);

        String receiverOrgCode = "M20701_Z_03";
        Integer sendWalletType = 3;
        BigDecimal sendQuota = new BigDecimal(150);
        WalletSendDTO sendDTO = new WalletSendDTO();
        sendDTO.setSenderOrgCode(organizationId);
        sendDTO.setSendWalletType(walletType);
        sendDTO.setQuota(sendQuota);
        sendDTO.setReceiverKey(receiverOrgCode);
        sendDTO.setReceiverType(1);
        sendDTO.setReceiverWalletType(sendWalletType);
        sendDTO.setProcessUserId(processUserId);
        sendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(sendDTO);

        WantWalletAccountEntity fromAccount = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<WantWalletLogEntity> fromAccountLogs = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, fromAccount.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.USED.getCode()).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(!CollectionUtils.isEmpty(fromAccountLogs),"账号创建错误");
        WantWalletAccountEntity toAccount = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, receiverOrgCode).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<WantWalletLogEntity> toAccountLogs = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, toAccount.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, sendWalletType).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode()).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(!CollectionUtils.isEmpty(toAccountLogs),"账号创建错误");
        Assert.isTrue(toAccountLogs.size() == fromAccountLogs.size(),"账号创建错误");

        BigDecimal reduce = fromAccountLogs.stream().map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(reduce.compareTo(sendQuota) == 0,"发放额度错误");

        WantWalletEntity fromWallet = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, fromAccount.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(fromWallet.getSurplus().compareTo(fromWallet.getQuota().subtract(sendQuota)) == 0,"发放额度错误");

        WantWalletEntity toWallet = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, toAccount.getAccountId()).eq(WantWalletEntity::getWalletTypeId, sendWalletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(toWallet.getSurplus().compareTo(sendQuota) == 0,"发放额度错误");
        Assert.isTrue(toWallet.getQuota().compareTo(sendQuota) == 0,"发放额度错误");

        for(int i=0 ; i < toAccountLogs.size(); i++){
            WantWalletLogEntity toLog = toAccountLogs.get(i);
            WantWalletLogEntity fromLog = fromAccountLogs.get(i);

            Assert.isTrue(toLog.getApplyType() == fromLog.getApplyType(),"发放类型错误");
            Assert.isTrue(toLog.getQuota().compareTo(fromLog.getQuota()) == 0,"发放额度错误");
            Assert.isTrue(toLog.getBoundary() == fromLog.getBoundary(),"发放边界错误");
            Assert.isTrue(toLog.getDeptCode().equals(fromLog.getDeptCode()),"发放部门错误");
            Assert.isTrue(toLog.getDeptName().equals(fromLog.getDeptName()),"发放部门错误");
        }
    }

    @Test
    @Transactional
    public void sendQuotaSpecialApplyType(){
        setTestData(organizationId,walletType);

        String receiverOrgCode = "M20701_Z_03";
        Integer sendWalletType = 3;
        BigDecimal sendQuota = new BigDecimal(150);
        WalletSendDTO sendDTO = new WalletSendDTO();
        sendDTO.setSenderOrgCode(organizationId);
        sendDTO.setSendWalletType(walletType);
        sendDTO.setApplyType(1);
        sendDTO.setQuota(sendQuota);
        sendDTO.setReceiverKey(receiverOrgCode);
        sendDTO.setReceiverType(1);
        sendDTO.setReceiverWalletType(sendWalletType);
        sendDTO.setProcessUserId(processUserId);
        sendDTO.setProcessUserName(processUserName);
        try {
            walletApplicationService.quickSend(sendDTO);
        } catch (Exception e) {
            Assert.isTrue(e.getMessage().equals("额度不足"),"错误信息不正确");
        }
    }

    @Test
    @Transactional
    public void sendToCeo(){

        String receiverMemberKey = "177222735";
        BigDecimal sendQuota = new BigDecimal(1);
        WalletSendDTO sendDTO = new WalletSendDTO();

        sendDTO.setQuota(sendQuota);
        sendDTO.setBusinessGroup("G06051542001");
        sendDTO.setReceiverKey(receiverMemberKey);
        sendDTO.setPositionId("POS_M20843_Z");
        sendDTO.setReceiverType(2);
        sendDTO.setReceiverWalletType(1);
        sendDTO.setApplyType(14);
        sendDTO.setDeptCode("D00000039");
        sendDTO.setProcessUserId(processUserId);
        sendDTO.setProcessUserName(processUserName);
        walletApplicationService.quickSend(sendDTO);


    }



    @Test
    public void quickSend(){

        WalletSendDTO sendDTO = new WalletSendDTO();
        sendDTO.setSenderOrgCode("C18348_Z");
        sendDTO.setSendWalletType(1);
        sendDTO.setQuota(new BigDecimal(40));
        sendDTO.setBusinessGroup("G06051542001");
        sendDTO.setReceiverType(2);
        sendDTO.setReceiverWalletType(2);
        sendDTO.setReceiverKey("177221618");
        sendDTO.setProcessUserId("00448211");
        sendDTO.setProcessUserName("00448211");
        sendDTO.setPositionId("POS_1000000_Z");
        sendDTO.setCallWp(true);
        sendDTO.setRemark("1560,陈列形式编号:137");
        sendDTO.setSubTypeId("spu06190904009");
        sendDTO.setBoundary(0);
        walletApplicationService.quickSend(sendDTO);

    }
}
