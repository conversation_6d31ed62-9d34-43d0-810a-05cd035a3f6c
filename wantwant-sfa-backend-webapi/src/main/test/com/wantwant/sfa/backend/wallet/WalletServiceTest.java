package com.wantwant.sfa.backend.wallet;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletAccountMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletMapper;
import com.wantwant.sfa.backend.wallet.data.WalletTestData;
import com.wantwant.sfa.backend.wallet.dto.CreateWalletAccountDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletAddDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletAddDetailDTO;
import com.wantwant.sfa.backend.wallet.dto.WalletUsedDTO;
import com.wantwant.sfa.backend.wallet.entity.WantWalletAccountEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity;
import com.wantwant.sfa.backend.wallet.enums.WalletLogTypeEnum;
import com.wantwant.sfa.backend.wallet.service.IWalletService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/22/下午5:16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WalletServiceTest {
    @Autowired
    private IWalletService walletService;

    private String processUserId = "********";

    private String processUserName = "张远";
    @Autowired
    private WantWalletAccountMapper wantWalletAccountMapper;
    @Autowired
    private WantWalletMapper wantWalletMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private WantWalletLogMapper wantWalletLogMapper;


    String path = "com/wantwant/sfa/backend/wallet/data/walletAdd.json";

    /**
     * 数据初始化
     *
     * @param organizationId
     * @param walletType
     */
    private void initData(String organizationId, Integer walletType) {
        CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
        createWalletAccountDTO.setOrganizationId(organizationId);
        createWalletAccountDTO.setWalletType(walletType);
        createWalletAccountDTO.setProcessUserId(processUserId);
        createWalletAccountDTO.setProcessUserName(processUserName);
        walletService.createAccount(createWalletAccountDTO);
    }

    private void setTestData(String organizationId, Integer walletType,Integer applyType) {
        initData(organizationId,walletType);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletAddDTO walletAddDTO = new WalletAddDTO();
        walletAddDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletAddDTO.setWalletTypeId(walletType);
        walletAddDTO.setProcessUserId(processUserId);
        walletAddDTO.setProcessUserName(processUserName);

        String json = ResourceUtil.readUtf8Str(path);
        List<WalletTestData> walletTestData = JSONArray.parseArray(json, WalletTestData.class);
        List<WalletAddDetailDTO> detailDTOList = new ArrayList<>();
        walletTestData.forEach(e -> {
            WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
            walletAddDetailDTO.setDeptCode(e.getDeptCode());
            walletAddDetailDTO.setDeptName(e.getDeptName());
            walletAddDetailDTO.setQuota(e.getQuota());
            walletAddDetailDTO.setBoundary(e.getBoundary());
            walletAddDetailDTO.setApplyType(applyType);
            detailDTOList.add(walletAddDetailDTO);
        });
        walletAddDTO.setDetailDTOList(detailDTOList);
        walletService.add(walletAddDTO);
    }


    @Test
    @Transactional
    public void initAccount(){
        String organizationId = "DN_Z";
        Integer walletType = 1;

        initData(organizationId, walletType);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        Assert.notNull(wantWalletAccountEntity,"创建账号失败");
        Assert.isTrue(wantWalletAccountEntity.getOrganizationId().equals(organizationId),"账号组织错误");
        Assert.isTrue(wantWalletAccountEntity.getOrganizationType().equals("area"),"账号组织类型错误");
        Assert.isTrue(wantWalletAccountEntity.getIsFrozen() == 0,"账号冻结状态错误");
        Assert.isTrue(wantWalletAccountEntity.getCreateUserId().equals(processUserId),"账号创建人错误");
        Assert.isTrue(wantWalletAccountEntity.getUpdateUserId().equals(processUserId),"账号修改人错误");


        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.notNull(wantWalletEntity,"创建账号账单失败");
        Assert.isTrue(wantWalletEntity.getQuota().compareTo(BigDecimal.ZERO) == 0,"账号账单额度错误");
        Assert.isTrue(wantWalletEntity.getIsFrozen() == 0,"账号账单冻结状态错误");
    }

    @Test
    @Transactional
    public void initAccountWithMultiAccountType(){
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;

        initData(organizationId, walletType);

        Integer walletType2 = 2;
        initData(organizationId, walletType2);

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        Assert.notNull(wantWalletAccountEntity,"创建账号失败");
        Assert.isTrue(wantWalletAccountEntity.getOrganizationId().equals(organizationId),"账号组织错误");
        Assert.isTrue(wantWalletAccountEntity.getOrganizationType().equals("company"),"账号组织类型错误");
        Assert.isTrue(wantWalletAccountEntity.getIsFrozen() == 0,"账号冻结状态错误");
        Assert.isTrue(wantWalletAccountEntity.getCreateUserId().equals(processUserId),"账号创建人错误");
        Assert.isTrue(wantWalletAccountEntity.getUpdateUserId().equals(processUserId),"账号修改人错误");


        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.notNull(wantWalletEntity,"创建账号账单失败");
        Assert.isTrue(wantWalletEntity.getQuota().compareTo(BigDecimal.ZERO) == 0,"账号账单额度错误");
        Assert.isTrue(wantWalletEntity.getWalletTypeId() == walletType,"账号账单类型错误");
        Assert.isTrue(wantWalletEntity.getIsFrozen() == 0,"账号账单冻结状态错误");

        WantWalletEntity wantWalletEntity2 = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType2).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.notNull(wantWalletEntity2,"创建账号账单失败");
        Assert.isTrue(wantWalletEntity2.getQuota().compareTo(BigDecimal.ZERO) == 0,"账号账单额度错误");
        Assert.isTrue(wantWalletEntity2.getWalletTypeId() == walletType2,"账号账单类型错误");
        Assert.isTrue(wantWalletEntity2.getIsFrozen() == 0,"账号账单冻结状态错误");
    }

    @Test
    @Transactional
    public void add(){
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        // 支出方
        String expenditure = "系统自动";
        // 收入方
        String revenue = organizationMapper.getOrganizationName(organizationId);

        String deptCode = "D00000021";

        String deptName = "旺铺发展事业部";

        Integer applyType = 11;

        int boundary = 1;


        String remark = "这是一个测试";

        BigDecimal quota = new BigDecimal(10);

        initData(organizationId, walletType);

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletAddDTO walletAddDTO = new WalletAddDTO();
        walletAddDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletAddDTO.setWalletTypeId(walletType);
        walletAddDTO.setExpenditure(expenditure);
        walletAddDTO.setProcessUserId(processUserId);
        walletAddDTO.setProcessUserName(processUserName);
        walletAddDTO.setRevenue(revenue);
        walletAddDTO.setRemark("这是一个测试");
        List<WalletAddDetailDTO> list = new ArrayList<>();
        WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
        walletAddDetailDTO.setDeptCode(deptCode);
        walletAddDetailDTO.setDeptName(deptName);
        walletAddDetailDTO.setQuota(quota);
        walletAddDetailDTO.setApplyType(applyType);
        walletAddDetailDTO.setBoundary(boundary);
        list.add(walletAddDetailDTO);
        walletAddDTO.setDetailDTOList(list);
        walletService.add(walletAddDTO);

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletEntity.getQuota().compareTo(quota) == 0,"账号账单额度错误");
        Assert.isTrue(wantWalletEntity.getSurplus().compareTo(quota) == 0,"账号账单余额错误");
        Assert.isTrue(wantWalletEntity.getIsFrozen() == 0,"账号账单冻结状态错误");

        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectOne(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId())
                .eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.notNull(wantWalletLogEntity,"账单操作记录获取失败");
        Assert.isTrue(wantWalletLogEntity.getSurplus().compareTo(quota) == 0,"账单记录剩余额度错误");
        Assert.isTrue(wantWalletLogEntity.getType() == WalletLogTypeEnum.INCOME.getCode(),"账单记录操作类型错误");
        Assert.isTrue(wantWalletLogEntity.getQuota().compareTo(quota) == 0,"账单记录额度错误");
        Assert.isTrue(wantWalletLogEntity.getApplyType().equals(applyType),"账单记录类型错误");
        Assert.isTrue(wantWalletLogEntity.getBoundary() == 1,"账单记录边界错误");
        Assert.isTrue(wantWalletLogEntity.getDeptCode().equals(deptCode),"账单记录部门CODE错误");
        Assert.isTrue(wantWalletLogEntity.getDeptName().equals(deptName),"账单记录部门名称错误");
        Assert.isTrue(wantWalletLogEntity.getExpenditure().equals(expenditure),"账单记录支出方错误");
        Assert.isTrue(wantWalletLogEntity.getRevenue().equals(revenue),"账单记录收入方错误");
        Assert.isTrue(wantWalletLogEntity.getRemark().equals(remark),"账单记录备注错误");
    }

    @Test
    @Transactional
    public void used_less_than_100(){
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(100);
        Integer applyType = 11;
        setTestData(organizationId, walletType,applyType);

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        walletService.used(walletUsedDTO);

        List<WantWalletLogEntity> wantWalletLogEntities = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletLogEntities.size() == 5,"账单记录条数错误");
        WantWalletLogEntity wantWalletLogEntity = wantWalletLogEntities.stream().findFirst().get();
        BigDecimal quota = wantWalletLogEntity.getQuota();
        BigDecimal surplus = wantWalletLogEntity.getSurplus();
        BigDecimal expectedSurplus = quota.subtract(usedQuota);
        Assert.isTrue(surplus.compareTo(expectedSurplus) == 0,"账单记录余额错误");

        Collections.reverse(wantWalletLogEntities);
        WantWalletLogEntity usedLog = wantWalletLogEntities.stream().findFirst().get();

        Assert.isTrue(usedLog.getType() == WalletLogTypeEnum.USED.getCode(),"账单记录类型错误");
        Assert.isTrue(usedLog.getQuota().compareTo(usedQuota) == 0,"账单记录使用额度错误");
        Assert.isTrue(usedLog.getSurplus().compareTo(BigDecimal.ZERO) == 0,"账单记录使用额度错误");
        Assert.isTrue(usedLog.getApplyType().equals(wantWalletLogEntity.getApplyType()),"账单记录使用的类型错误");
        Assert.isTrue(usedLog.getBoundary().equals(wantWalletLogEntity.getBoundary()),"账单记录使用的边界错误");
        Assert.isTrue(usedLog.getDeptCode().equals(wantWalletLogEntity.getDeptCode()),"账单记录使用的部门错误");
        Assert.isTrue(usedLog.getDeptName().equals(wantWalletLogEntity.getDeptName()),"账单记录使用的部门错误");
        Assert.isTrue(usedLog.getExpenditure().equals("测试使用"),"账单记录收入方错误");
        Assert.isTrue(usedLog.getRevenue().equals(organizationName),"账单记录支出方错误");

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        BigDecimal curSurplus = wantWalletEntity.getSurplus();
        Assert.isTrue(curSurplus.add(usedQuota).compareTo(wantWalletEntity.getQuota()) == 0,"账号额度不匹配");

        BigDecimal usedTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.USED.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal addTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.INCOME.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(addTotal.subtract(usedTotal).compareTo(wantWalletEntity.getSurplus()) == 0,"账单记录支出方错误");
    }


    @Test
    @Transactional
    public void used_more_than_100(){
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(140.5);
        Integer applyType = 11;
        setTestData(organizationId, walletType,applyType);

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        walletService.used(walletUsedDTO);

        List<WantWalletLogEntity> wantWalletLogEntities = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletLogEntities.size() == 7,"账单记录条数错误");


        // 获取前3个消费记录
        List<WantWalletLogEntity> collect = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.USED.getCode()).limit(3).collect(Collectors.toList());
        WantWalletLogEntity log1 = collect.get(0);
        Assert.isTrue(log1.getType() == WalletLogTypeEnum.USED.getCode(),"账单记录类型错误");
        Assert.isTrue(log1.getQuota().compareTo(wantWalletLogEntities.get(0).getQuota()) == 0,"账单记录使用额度错误");
        Assert.isTrue(log1.getSurplus().compareTo(BigDecimal.ZERO) == 0,"账单记录使用额度错误");
        Assert.isTrue(log1.getApplyType().equals(wantWalletLogEntities.get(0).getApplyType()),"账单记录使用的类型错误");
        Assert.isTrue(log1.getBoundary().equals(wantWalletLogEntities.get(0).getBoundary()),"账单记录使用的边界错误");
        Assert.isTrue(log1.getDeptCode().equals(wantWalletLogEntities.get(0).getDeptCode()),"账单记录使用的部门错误");
        Assert.isTrue(log1.getDeptName().equals(wantWalletLogEntities.get(0).getDeptName()),"账单记录使用的部门错误");
        Assert.isTrue(log1.getExpenditure().equals("测试使用"),"账单记录收入方错误");
        Assert.isTrue(log1.getRevenue().equals(organizationName),"账单记录支出方错误");

        WantWalletLogEntity log2 = collect.get(1);
        Assert.isTrue(log2.getType() == WalletLogTypeEnum.USED.getCode(),"账单记录类型错误");
        Assert.isTrue(log2.getQuota().compareTo(wantWalletLogEntities.get(1).getQuota()) == 0,"账单记录使用额度错误");
        Assert.isTrue(log2.getSurplus().compareTo(BigDecimal.ZERO) == 0,"账单记录使用额度错误");
        Assert.isTrue(log2.getApplyType().equals(wantWalletLogEntities.get(1).getApplyType()),"账单记录使用的类型错误");
        Assert.isTrue(log2.getBoundary().equals(wantWalletLogEntities.get(1).getBoundary()),"账单记录使用的边界错误");
        Assert.isTrue(log2.getDeptCode().equals(wantWalletLogEntities.get(1).getDeptCode()),"账单记录使用的部门错误");
        Assert.isTrue(log2.getDeptName().equals(wantWalletLogEntities.get(1).getDeptName()),"账单记录使用的部门错误");
        Assert.isTrue(log2.getExpenditure().equals("测试使用"),"账单记录收入方错误");
        Assert.isTrue(log2.getRevenue().equals(organizationName),"账单记录支出方错误");




        WantWalletLogEntity log3 = collect.get(2);
        BigDecimal subtract = log3.getQuota().subtract(usedQuota.subtract(log1.getQuota()).subtract(log2.getQuota()));
        Assert.isTrue(log3.getType() == WalletLogTypeEnum.USED.getCode(),"账单记录类型错误");
        Assert.isTrue(log3.getQuota().compareTo(usedQuota.subtract(log1.getQuota()).subtract(log2.getQuota())) == 0,"账单记录使用额度错误");
        Assert.isTrue(log3.getSurplus().compareTo(BigDecimal.ZERO) == 0,"账单记录使用额度错误");
        Assert.isTrue(log3.getApplyType().equals(wantWalletLogEntities.get(2).getApplyType()),"账单记录使用的类型错误");
        Assert.isTrue(log3.getBoundary().equals(wantWalletLogEntities.get(2).getBoundary()),"账单记录使用的边界错误");
        Assert.isTrue(log3.getDeptCode().equals(wantWalletLogEntities.get(2).getDeptCode()),"账单记录使用的部门错误");
        Assert.isTrue(log3.getDeptName().equals(wantWalletLogEntities.get(2).getDeptName()),"账单记录使用的部门错误");
        Assert.isTrue(log3.getExpenditure().equals("测试使用"),"账单记录收入方错误");
        Assert.isTrue(log3.getRevenue().equals(organizationName),"账单记录支出方错误");


        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        BigDecimal curSurplus = wantWalletEntity.getSurplus();
        Assert.isTrue(curSurplus.add(usedQuota).compareTo(wantWalletEntity.getQuota()) == 0,"账号额度不匹配");

        BigDecimal usedTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.USED.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal addTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.INCOME.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(addTotal.subtract(usedTotal).compareTo(wantWalletEntity.getSurplus()) == 0,"账单记录支出方错误");
    }

    @Test
    @Transactional
    public void used_exceed(){
        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(199999);
        Integer applyType = 11;
        setTestData(organizationId, walletType,applyType);

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        walletUsedDTO.setRemark("测试使用。。。。。");
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setWalletTypeId(walletType);
        walletUsedDTO.setQuota(usedQuota);
        String organizationName = organizationMapper.getOrganizationName(organizationId);
        walletUsedDTO.setRevenue(organizationName);
        walletUsedDTO.setExpenditure("测试使用");
        walletUsedDTO.setProcessUserId(processUserId);
        walletUsedDTO.setProcessUserName(processUserName);
        // 使用额度
        try {
            walletService.used(walletUsedDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<WantWalletLogEntity> wantWalletLogEntities = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        Assert.isTrue(wantWalletLogEntities.size() == 4,"账单记录条数错误");

        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        BigDecimal quota = wantWalletEntity.getQuota();
        BigDecimal surplus = wantWalletEntity.getSurplus();
        Assert.isTrue(quota.compareTo(surplus) == 0,"账号额度不正确");

        BigDecimal usedTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.USED.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal addTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.INCOME.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(addTotal.subtract(usedTotal).compareTo(wantWalletEntity.getSurplus()) == 0,"账单记录支出方错误");
    }

    @Test
    @Transactional
    public void used_parallel(){

        String organizationId = "C18359_Z_03";
        Integer walletType = 1;
        BigDecimal usedQuota = new BigDecimal(30);
        Integer applyType = 11;
        setTestData(organizationId, walletType,applyType);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        IntStream.range(0,10).forEach(e -> {
            CompletableFuture<Void> cf = CompletableFuture.runAsync(() -> {
                WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
                walletUsedDTO.setRemark("测试使用。。。。。");
                walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
                walletUsedDTO.setWalletTypeId(walletType);
                walletUsedDTO.setQuota(usedQuota);
                String organizationName = organizationMapper.getOrganizationName(organizationId);
                walletUsedDTO.setRevenue(organizationName);
                walletUsedDTO.setExpenditure("测试使用");
                walletUsedDTO.setProcessUserId(processUserId);
                walletUsedDTO.setProcessUserName(processUserName);
                // 使用额度
                try {
                    walletService.used(walletUsedDTO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            });
            completableFutures.add(cf);
        });

        CompletableFuture.allOf(completableFutures.stream().toArray(CompletableFuture[]::new)).join();



        // 检查额度
        List<WantWalletLogEntity> wantWalletLogEntities = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));


        WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletType).eq(WantWalletEntity::getDeleteFlag, 0));
        BigDecimal quota = wantWalletEntity.getQuota();
        BigDecimal surplus = wantWalletEntity.getSurplus();
        Assert.isTrue(quota.compareTo(surplus) >= 0,"额度超发");

        BigDecimal usedTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.USED.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal addTotal = wantWalletLogEntities.stream().filter(f -> f.getType() == WalletLogTypeEnum.INCOME.getCode()).map(WantWalletLogEntity::getQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(addTotal.subtract(usedTotal).compareTo(wantWalletEntity.getSurplus()) == 0,"账单记录支出方错误");

        // 删除数据
        wantWalletAccountMapper.deleteById(wantWalletAccountEntity.getAccountId());
        wantWalletLogMapper.delete(new LambdaQueryWrapper<WantWalletLogEntity>().eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getWalletTypeId, walletType).eq(WantWalletLogEntity::getDeleteFlag, 0));
        wantWalletMapper.delete(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId,wantWalletAccountEntity.getAccountId()));
    }
}
