package com.wantwant.sfa.backend.workReport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.Task.WorkReportInitTask;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportMapper;
import com.wantwant.sfa.backend.workReport.entity.SfaWorkReportEntity;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/30/上午10:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WorkReportTask {

    @Resource
    private WorkReportInitTask workReportInitTask;
    @Resource
    private WorkReportMapper workReportMapper;

    @Test
    @Transactional
    public void testInit_0501_0502(){
        workReportInitTask.workReportInit("2024-05-02");

        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new LambdaQueryWrapper<SfaWorkReportEntity>().orderByDesc(SfaWorkReportEntity::getWorkId).last("limit 1"));

        Assert.notNull(sfaWorkReportEntity,"生成周报失败");


        LocalDate startDate = sfaWorkReportEntity.getStartDate();
        LocalDate endDate = sfaWorkReportEntity.getEndDate();

        Assert.isTrue(startDate.getMonthValue() == 5 ,"生成周报开始月份错误");
        Assert.isTrue(startDate.getDayOfMonth() == 1 ,"生成周报开始日期错误");

        Assert.isTrue(endDate.getMonthValue() == 5 ,"生成周报结束月份错误");
        Assert.isTrue(endDate.getDayOfMonth() == 2 ,"生成周报结束日期错误");

        Assert.isTrue(sfaWorkReportEntity.getWeeks() == 1 ,"生成周报结束日期错误");
    }


    @Test
    @Transactional
    public void testInit_0503_0509(){
        workReportInitTask.workReportInit("2024-05-09");

        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new LambdaQueryWrapper<SfaWorkReportEntity>().orderByDesc(SfaWorkReportEntity::getWorkId).last("limit 1"));

        Assert.notNull(sfaWorkReportEntity,"生成周报失败");


        LocalDate startDate = sfaWorkReportEntity.getStartDate();
        LocalDate endDate = sfaWorkReportEntity.getEndDate();

        Assert.isTrue(startDate.getMonthValue() == 5 ,"生成周报开始月份错误");
        Assert.isTrue(startDate.getDayOfMonth() == 3 ,"生成周报开始日期错误");

        Assert.isTrue(endDate.getMonthValue() == 5 ,"生成周报结束月份错误");
        Assert.isTrue(endDate.getDayOfMonth() == 9 ,"生成周报结束日期错误");

        Assert.isTrue(sfaWorkReportEntity.getWeeks() == 2 ,"生成周报结束日期错误");
    }

    @Test
    @Transactional
    public void testInit_0531_0531(){
        workReportInitTask.workReportInit("2024-05-31");

        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new LambdaQueryWrapper<SfaWorkReportEntity>().orderByDesc(SfaWorkReportEntity::getWorkId).last("limit 1"));

        Assert.notNull(sfaWorkReportEntity,"生成周报失败");


        LocalDate startDate = sfaWorkReportEntity.getStartDate();
        LocalDate endDate = sfaWorkReportEntity.getEndDate();

        Assert.isTrue(startDate.getMonthValue() != 5 ,"生成周报开始月份错误");
        Assert.isTrue(startDate.getDayOfMonth() != 31 ,"生成周报开始日期错误");

        Assert.isTrue(endDate.getMonthValue() != 5 ,"生成周报结束月份错误");
        Assert.isTrue(endDate.getDayOfMonth() != 31 ,"生成周报结束日期错误");


        Assert.isTrue(sfaWorkReportEntity.getWeeks() != 6 ,"生成周报结束日期错误");
    }
}
